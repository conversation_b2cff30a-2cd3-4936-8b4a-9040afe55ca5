// Header Styling
.download-toolbar {
  --background: #B88A42;
  --color: #ffffff;
}

.back-icon {
  font-size: 24px;
  color: #ffffff;
}

.download-title {
  font-size: 18px;
  font-weight: 600;
  color: #ffffff;
}

// Content Styling
.download-content {
  --background: #f8f9fa;
}

.download-container {
  padding: 20px;
  min-height: 100vh;
}

// Filter Section
.filter-section {
  margin-bottom: 24px;
}

.filter-title {
  font-size: 16px;
  font-weight: 600;
  color: #000000;
  margin: 0 0 12px 0;
}

.category-chips {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.category-chip {
  padding: 8px 16px;
  border-radius: 20px;
  background: #ffffff;
  border: 2px solid #e5e5e5;
  font-size: 14px;
  font-weight: 500;
  color: #666666;
  cursor: pointer;
  transition: all 0.2s ease;
  
  &:hover {
    border-color: #B88A42;
  }
  
  &.active {
    background: #B88A42;
    border-color: #B88A42;
    color: #ffffff;
  }
}

// Documents Section
.documents-section {
  margin-bottom: 32px;
}

.section-title {
  font-size: 18px;
  font-weight: 600;
  color: #000000;
  margin: 0 0 16px 0;
}

.documents-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.document-card {
  background: #ffffff;
  border-radius: 12px;
  padding: 16px;
  display: flex;
  align-items: center;
  gap: 16px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);
  border: 1px solid #e5e5e5;
  transition: all 0.2s ease;
  
  &:hover {
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  }
}

.document-icon {
  width: 48px;
  height: 48px;
  background: rgba(10, 73, 255, 0.1);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  
  ion-icon {
    font-size: 24px;
    color: #0A49FF;
  }
}

.document-info {
  flex: 1;
  min-width: 0;
}

.document-name {
  font-size: 16px;
  font-weight: 600;
  color: #000000;
  margin: 0 0 4px 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.document-meta {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
}

.document-size,
.document-date {
  font-size: 12px;
  color: #666666;
}

.document-size::after {
  content: '•';
  margin-left: 8px;
  color: #dee2e6;
}

.document-type {
  margin-top: 4px;
}

.type-badge {
  padding: 2px 8px;
  border-radius: 4px;
  background: #f8f9fa;
  color: #495057;
  font-size: 10px;
  font-weight: 600;
  text-transform: uppercase;
}

.document-actions {
  flex-shrink: 0;
}

.download-btn {
  background: #0A49FF;
  color: #ffffff;
  border: none;
  border-radius: 8px;
  padding: 8px 16px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 6px;
  
  &:hover {
    background: #0841e6;
  }
  
  &:active {
    transform: scale(0.95);
  }
}

.download-icon {
  font-size: 16px;
}

// Loading State
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
  
  ion-spinner {
    margin-bottom: 16px;
  }
  
  p {
    font-size: 16px;
    color: #666666;
    margin: 0;
  }
}

// Empty State
.empty-state {
  text-align: center;
  padding: 60px 20px;
}

.empty-icon {
  font-size: 64px;
  color: #dee2e6;
  margin-bottom: 16px;
}

.empty-title {
  font-size: 18px;
  font-weight: 600;
  color: #000000;
  margin: 0 0 8px 0;
}

.empty-description {
  font-size: 14px;
  color: #666666;
  margin: 0;
  line-height: 1.4;
}

// Responsive Design
@media (max-width: 375px) {
  .download-container {
    padding: 16px;
  }
  
  .category-chips {
    gap: 6px;
  }
  
  .category-chip {
    padding: 6px 12px;
    font-size: 12px;
  }
  
  .document-card {
    padding: 12px;
    gap: 12px;
  }
  
  .document-icon {
    width: 40px;
    height: 40px;
    
    ion-icon {
      font-size: 20px;
    }
  }
  
  .document-name {
    font-size: 14px;
  }
  
  .download-btn {
    padding: 6px 12px;
    font-size: 12px;
    
    span {
      display: none;
    }
  }
}
