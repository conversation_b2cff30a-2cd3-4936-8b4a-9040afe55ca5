"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.validateCode = exports.testFunction = void 0;
const functions = require("firebase-functions");
const admin = require("firebase-admin");
// Initialize Firebase Admin
admin.initializeApp();
/**
 * Simple test function to verify Cloud Functions are working
 */
exports.testFunction = functions.https.onCall(async (data, context) => {
    var _a;
    return {
        success: true,
        message: 'Cloud Functions are working!',
        timestamp: new Date().toISOString(),
        user: ((_a = context.auth) === null || _a === void 0 ? void 0 : _a.uid) || 'anonymous'
    };
});
/**
 * Simple function to validate assistant codes
 */
exports.validateCode = functions.https.onCall(async (data, context) => {
    if (!context.auth) {
        throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
    }
    const { code } = data;
    if (!code || typeof code !== 'string' || code.length !== 8) {
        throw new functions.https.HttpsError('invalid-argument', 'Valid 8-character code required');
    }
    // Simple validation - in production this would check Firestore
    return {
        success: true,
        valid: code.length === 8,
        message: 'Code format is valid'
    };
});
//# sourceMappingURL=index.js.map