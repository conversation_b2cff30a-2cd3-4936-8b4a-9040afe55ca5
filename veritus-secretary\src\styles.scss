/* Enhanced Global Styles for Veritus Secretary Portal */

@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');

:root {
  --primary-gradient: linear-gradient(135deg, #C49A56 0%, #D4AF6A 100%);
  --secondary-gradient: linear-gradient(135deg, #1e293b 0%, #334155 100%);
  --success-gradient: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
  --danger-gradient: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  --info-gradient: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
  --warning-gradient: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);

  --shadow-sm: 0 2px 8px rgba(0, 0, 0, 0.05);
  --shadow-md: 0 8px 24px rgba(0, 0, 0, 0.08);
  --shadow-lg: 0 20px 60px rgba(0, 0, 0, 0.1);
  --shadow-xl: 0 30px 80px rgba(0, 0, 0, 0.15);

  --border-radius-sm: 8px;
  --border-radius-md: 12px;
  --border-radius-lg: 20px;
  --border-radius-xl: 24px;

  --transition-fast: 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-normal: 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-slow: 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

html, body {
  height: 100%;
  margin: 0;
  font-family: 'Inter', 'Roboto', "Helvetica Neue", sans-serif;
  font-weight: 400;
  line-height: 1.6;
  color: #1e293b;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  overflow-x: hidden;
  overflow-y: auto;
}

/* Ensure proper scrolling for Angular components */
app-root {
  display: block;
  min-height: 100vh;
  width: 100%;
}

* {
  box-sizing: border-box;
}

/* Enhanced Material Design Overrides */
.mat-mdc-raised-button {
  background: var(--primary-gradient) !important;
  color: white !important;
  border-radius: var(--border-radius-md) !important;
  font-weight: 600 !important;
  text-transform: none !important;
  box-shadow: var(--shadow-md) !important;
  transition: var(--transition-normal) !important;
  border: none !important;
}

.mat-mdc-raised-button:hover {
  transform: translateY(-2px) !important;
  box-shadow: var(--shadow-lg) !important;
}

.mat-mdc-raised-button:active {
  transform: translateY(0) !important;
}

.mat-mdc-outlined-button {
  border: 2px solid #C49A56 !important;
  color: #C49A56 !important;
  border-radius: var(--border-radius-md) !important;
  font-weight: 600 !important;
  text-transform: none !important;
  transition: var(--transition-normal) !important;
}

.mat-mdc-outlined-button:hover {
  background: rgba(196, 154, 86, 0.1) !important;
  transform: translateY(-1px) !important;
}

.mat-mdc-form-field {
  border-radius: var(--border-radius-md) !important;
}

.mat-mdc-form-field-outline {
  border-radius: var(--border-radius-md) !important;
}

.mat-mdc-card {
  border-radius: var(--border-radius-lg) !important;
  box-shadow: var(--shadow-md) !important;
  transition: var(--transition-normal) !important;
}

.mat-mdc-card:hover {
  box-shadow: var(--shadow-lg) !important;
  transform: translateY(-2px) !important;
}

/* Custom Utility Classes */
.gradient-text {
  background: var(--primary-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.glass-effect {
  background: rgba(255, 255, 255, 0.25);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.hover-lift {
  transition: var(--transition-normal);
}

.hover-lift:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
}

/* Enhanced Scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #C49A56 0%, #D4AF6A 100%);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #B8935A 0%, #C49A56 100%);
}

/* Loading Animation */
@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

.loading-shimmer {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200px 100%;
  animation: shimmer 1.5s infinite;
}

/* Pulse Animation */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Global Material Design customizations */
.mat-toolbar.mat-primary {
  background: #1976d2;
  color: white;
}

.mat-raised-button.mat-primary {
  background-color: #1976d2;
}

.mat-card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border-radius: 12px;
}

/* Utility classes */
.full-width {
  width: 100%;
}

.text-center {
  text-align: center;
}

.spacer {
  flex: 1 1 auto;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
