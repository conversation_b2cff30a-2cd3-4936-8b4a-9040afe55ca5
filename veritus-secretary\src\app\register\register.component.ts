import { Component } from '@angular/core';
import { Router } from '@angular/router';
import { FirebaseService } from '../services/firebase.service';

@Component({
  selector: 'app-register',
  templateUrl: './register.component.html',
  styleUrls: ['./register.component.scss']
})
export class RegisterComponent {
  // Form fields
  fullName = '';
  email = '';
  password = '';
  confirmPassword = '';
  lawyerCode = '';
  showPassword = false;
  showConfirmPassword = false;
  isLoading = false;
  errorMessage = '';

  constructor(
    private router: Router,
    private firebaseService: FirebaseService
  ) { }

  togglePasswordVisibility() {
    this.showPassword = !this.showPassword;
  }

  toggleConfirmPasswordVisibility() {
    this.showConfirmPassword = !this.showConfirmPassword;
  }

  isFormValid(): boolean {
    return !!(
      this.fullName.trim() &&
      this.email.trim() &&
      this.password.trim() &&
      this.confirmPassword.trim() &&
      this.lawyerCode.trim() &&
      this.password === this.confirmPassword &&
      this.email.includes('@')
    );
  }

  async onRegister() {
    if (!this.isFormValid()) {
      this.errorMessage = 'Please fill in all fields correctly';
      return;
    }

    if (this.password !== this.confirmPassword) {
      this.errorMessage = 'Passwords do not match';
      return;
    }

    this.isLoading = true;
    this.errorMessage = '';

    try {
      // TODO: Implement secretary registration with lawyer code validation
      console.log('Secretary registration:', {
        fullName: this.fullName,
        email: this.email,
        lawyerCode: this.lawyerCode
      });

      // For now, show success message
      alert('Secretary registration will be implemented soon. Your details have been logged.');

      // Redirect to login
      this.router.navigate(['/login']);
    } catch (error: any) {
      this.errorMessage = error.message || 'Registration failed. Please try again.';
    } finally {
      this.isLoading = false;
    }
  }

  goToSignIn() {
    this.router.navigate(['/login']);
  }
}
