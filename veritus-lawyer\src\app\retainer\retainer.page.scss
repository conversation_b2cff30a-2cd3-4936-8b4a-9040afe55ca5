.retainer-content {
  --background: #FFFFFF;
  background: #FFFFFF;
}

.retainer-container {
  padding: 16px;
  background: #FFFFFF;
  min-height: calc(100vh - 44px);
}

.status-bar-spacer {
  height: 44px;
  background: #FFFFFF;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32px;
  padding: 8px 4px 0;
  background: #FFFFFF;
}

.page-title {
  font-size: 32px;
  font-weight: 700;
  color: #000000;
  margin: 0;
  letter-spacing: -0.02em;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 16px;
}

.search-button {
  background: none;
  border: none;
  padding: 8px;
  cursor: pointer;
  border-radius: 8px;
  transition: background-color 0.2s ease;

  &:hover {
    background: rgba(196, 154, 86, 0.1);
  }
}

.search-icon {
  font-size: 20px;
  color: #C49A56;
}

.add-button {
  background: none;
  border: none;
  font-size: 16px;
  font-weight: 500;
  color: #C49A56;
  cursor: pointer;
  padding: 8px 0;
  transition: color 0.2s ease;

  &:hover {
    color: #B8894A;
  }
}

.add-text {
  font-size: 16px;
  font-weight: 500;
}

// Search Container
.search-container {
  margin-bottom: 24px;
  padding: 0 16px;
  animation: slideDown 0.3s ease-out;
}

.custom-searchbar {
  --background: #F8F9FA;
  --border-radius: 12px;
  --box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  --color: #000000;
  --placeholder-color: #9E9E9E;
  --icon-color: #C49A56;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// Tab Navigation
.tab-navigation {
  display: flex;
  justify-content: center;
  gap: 8px;
  margin-bottom: 24px;
  padding: 0 16px;
}

.tab-button {
  background: #F5F5F5;
  border: 1px solid #E0E0E0;
  border-radius: 8px;
  padding: 12px 16px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  align-items: center;
  justify-content: center;
  flex: 1;
  max-width: 140px;
  min-width: 120px;

  &.tab-active {
    background: #C49A56;
    border-color: #C49A56;

    .tab-text {
      color: #FFFFFF;
      font-weight: 600;
    }
  }

  &:hover:not(.tab-active) {
    background: #EEEEEE;
    border-color: #D0D0D0;

    .tab-text {
      color: #757575;
    }
  }
}

.tab-text {
  font-size: 14px;
  font-weight: 500;
  color: #666666;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  letter-spacing: -0.01em;
  text-align: center;
}

// Client List
.client-list {
  margin-bottom: 32px;
  padding: 0 16px;
}

.client-item {
  background: #FFFFFF;
  border-radius: 12px;
  margin-bottom: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border: 1px solid rgba(0, 0, 0, 0.04);
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  }
}

.client-content {
  display: flex;
  align-items: center;
  padding: 16px;
  gap: 16px;
}

.client-avatar {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  overflow: hidden;
  flex-shrink: 0;
  position: relative;
}

.avatar-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.status-indicator {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: 2px solid #FFFFFF;

  &.status-success {
    background: #67EF77;
  }

  &.status-warning {
    background: #FFA726;
  }

  &.status-danger {
    background: #EF5350;
  }

  &.status-medium {
    background: #9E9E9E;
  }
}

.client-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.client-name {
  font-size: 16px;
  font-weight: 600;
  color: #000000;
  margin: 0;
  line-height: 1.2;
}

.client-status {
  font-size: 14px;
  margin: 0;
  line-height: 1.2;
  font-weight: 500;

  &.status-text-success {
    color: #2E7D32;
  }

  &.status-text-warning {
    color: #F57C00;
  }

  &.status-text-danger {
    color: #C62828;
  }

  &.status-text-medium {
    color: #616161;
  }
}

.client-case-type {
  font-size: 12px;
  color: #9E9E9E;
  margin: 0;
  line-height: 1.2;
}

.client-retainer {
  font-size: 14px;
  color: #C49A56;
  margin: 0;
  line-height: 1.2;
  font-weight: 600;
}

.client-meta {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 4px;
  flex-shrink: 0;
}

.last-activity {
  font-size: 12px;
  color: #9E9E9E;
  margin: 0;
  line-height: 1.2;
}

// Empty States
.empty-state {
  text-align: center;
  padding: 48px 24px;
  color: #9E9E9E;
}

.empty-icon {
  font-size: 64px;
  color: #E0E0E0;
  margin-bottom: 16px;
}

.empty-title {
  font-size: 18px;
  font-weight: 600;
  color: #616161;
  margin: 0 0 8px 0;
}

.empty-message {
  font-size: 14px;
  color: #9E9E9E;
  margin: 0 0 24px 0;
  line-height: 1.4;
}

.empty-action-button {
  background: #C49A56;
  color: #FFFFFF;
  border: none;
  border-radius: 8px;
  padding: 12px 24px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;

  &:hover {
    background: #B8894A;
  }
}

.chevron-icon {
  font-size: 20px;
  color: #9E9E9E;
}

// Contracts Section
.contracts-section {
  margin-bottom: 32px;
  padding: 0 16px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.section-title {
  font-size: 18px;
  font-weight: 600;
  color: #000000;
  margin: 0;
  letter-spacing: -0.01em;
}

.upload-button {
  background: none;
  border: none;
  font-size: 16px;
  font-weight: 500;
  color: #C49A56;
  cursor: pointer;
  padding: 8px 0;
  transition: color 0.2s ease;
  display: flex;
  align-items: center;
  gap: 6px;

  &:hover {
    color: #B8894A;
  }
}

.upload-icon {
  font-size: 18px;
}

.upload-text {
  font-size: 16px;
  font-weight: 500;
}

.view-all-button {
  background: none;
  border: none;
  font-size: 14px;
  font-weight: 500;
  color: #C49A56;
  cursor: pointer;
  padding: 4px 0;
  transition: color 0.2s ease;

  &:hover {
    color: #B8894A;
  }
}

.view-all-text {
  font-size: 14px;
  font-weight: 500;
}

// Subsection Titles
.subsection-title {
  font-size: 16px;
  font-weight: 600;
  color: #424242;
  margin: 0 0 12px 0;
  letter-spacing: -0.01em;
}

// Folders
.folders-list {
  margin-bottom: 24px;
}

.folder-item {
  background: #FFFFFF;
  border-radius: 12px;
  margin-bottom: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border: 1px solid rgba(0, 0, 0, 0.04);
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  }
}

.folder-content {
  display: flex;
  align-items: center;
  padding: 14px 16px;
  gap: 14px;
}

.folder-icon-container {
  width: 36px;
  height: 36px;
  background: linear-gradient(135deg, rgba(196, 154, 86, 0.15) 0%, rgba(196, 154, 86, 0.08) 100%);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.folder-icon {
  font-size: 18px;
  color: #C49A56;
}

.folder-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.folder-name {
  font-size: 15px;
  font-weight: 600;
  color: #000000;
  margin: 0;
  line-height: 1.2;
}

.folder-meta {
  font-size: 12px;
  color: #9E9E9E;
  margin: 0;
  line-height: 1.2;
}

.folder-action {
  flex-shrink: 0;
}

// Files List
.files-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.file-item {
  background: #FFFFFF;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border: 1px solid rgba(0, 0, 0, 0.04);
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  }
}

.file-content {
  display: flex;
  align-items: center;
  padding: 16px;
  gap: 16px;
}

.file-icon-container {
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, rgba(196, 154, 86, 0.1) 0%, rgba(196, 154, 86, 0.05) 100%);
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.file-icon {
  font-size: 20px;
  color: #C49A56;
}

.file-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.file-name {
  font-size: 16px;
  font-weight: 600;
  color: #000000;
  margin: 0;
  line-height: 1.2;
}

.file-type {
  font-size: 14px;
  color: #616161;
  margin: 0;
  line-height: 1.2;
}

.file-meta-info {
  font-size: 12px;
  color: #9E9E9E;
  margin: 0;
  line-height: 1.2;
}

.file-meta {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-shrink: 0;
}

.file-count {
  font-size: 14px;
  font-weight: 500;
  color: #000000;
}

// Activity Section
.activity-section {
  margin-bottom: 32px;
  padding: 0 16px;
}

.activity-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.activity-item {
  background: #FFFFFF;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border: 1px solid rgba(0, 0, 0, 0.04);
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  }
}

.activity-content {
  display: flex;
  align-items: center;
  padding: 16px;
  gap: 16px;
}

.activity-icon-container {
  width: 32px;
  height: 32px;
  background: linear-gradient(135deg, rgba(103, 239, 119, 0.1) 0%, rgba(103, 239, 119, 0.05) 100%);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.activity-icon {
  font-size: 18px;
  color: #67EF77;

  &.activity-contract {
    color: #2196F3;
  }

  &.activity-payment {
    color: #4CAF50;
  }

  &.activity-meeting {
    color: #FF9800;
  }

  &.activity-document {
    color: #9C27B0;
  }

  &.activity-communication {
    color: #00BCD4;
  }
}

.activity-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.activity-title {
  font-size: 16px;
  font-weight: 600;
  color: #000000;
  margin: 0;
  line-height: 1.2;
}

.activity-description {
  font-size: 14px;
  color: #616161;
  margin: 0;
  line-height: 1.3;
}

.activity-client {
  font-size: 12px;
  color: #C49A56;
  margin: 0;
  line-height: 1.2;
  font-weight: 500;
}

.activity-meta {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 2px;
  flex-shrink: 0;
}

.activity-time {
  font-size: 12px;
  color: #424242;
  margin: 0;
  line-height: 1.2;
  font-weight: 500;
}

.activity-date {
  font-size: 11px;
  color: #9E9E9E;
  margin: 0;
  line-height: 1.2;
}

// Empty Activity State
.empty-activity {
  text-align: center;
  padding: 32px 16px;
  color: #9E9E9E;
}

.empty-activity-icon {
  font-size: 48px;
  color: #E0E0E0;
  margin-bottom: 12px;
}

.empty-activity-text {
  font-size: 14px;
  color: #9E9E9E;
  margin: 0;
}

// Responsive Design
@media (max-width: 480px) {
  .retainer-container {
    padding: 12px;
  }

  .page-header {
    margin-bottom: 20px;
  }

  .page-title {
    font-size: 22px;
  }

  .tab-navigation {
    gap: 6px;
    padding: 0 12px;
  }

  .tab-button {
    padding: 10px 14px;
    max-width: 115px;
    min-width: 95px;
  }

  .tab-text {
    font-size: 13px;
  }

  .client-list,
  .contracts-section,
  .activity-section {
    padding: 0 12px;
  }

  .client-content,
  .file-content,
  .activity-content {
    padding: 12px;
    gap: 12px;
  }

  .client-avatar {
    width: 44px;
    height: 44px;
  }

  .section-title {
    font-size: 16px;
  }
}
