import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

// Services
import { ReminderService } from './services/reminder.service';
import { NotificationService } from './services/notification.service';
import { SchedulerService } from './services/scheduler.service';

// Components
import { ReminderListComponent } from './components/reminder-list/reminder-list.component';
import { ReminderFormComponent } from './components/reminder-form/reminder-form.component';
import { NotificationSettingsComponent } from './components/notification-settings/notification-settings.component';

@NgModule({
  declarations: [
    ReminderListComponent,
    ReminderFormComponent,
    NotificationSettingsComponent
  ],
  imports: [
    CommonModule
  ],
  providers: [
    ReminderService,
    NotificationService,
    SchedulerService
  ],
  exports: [
    ReminderListComponent,
    ReminderFormComponent,
    NotificationSettingsComponent
  ]
})
export class ReminderModule { }
