import { Component, OnInit, Output, EventEmitter } from '@angular/core';

@Component({
  selector: 'app-role-selection',
  templateUrl: './role-selection.component.html',
  styleUrls: ['./role-selection.component.scss'],
  standalone: false,
})
export class RoleSelectionComponent implements OnInit {
  @Output() roleSelected = new EventEmitter<'client' | 'lawyer' | 'secretary'>();

  selectedRole: 'client' | 'lawyer' | 'secretary' | null = null;

  roles = [
    {
      id: 'client' as const,
      title: 'Client',
      description: 'I need legal services',
      icon: 'person'
    },
    {
      id: 'lawyer' as const,
      title: 'Lawyer',
      description: 'I provide legal services',
      icon: 'briefcase'
    },
    {
      id: 'secretary' as const,
      title: 'Secretary',
      description: 'I assist lawyers',
      icon: 'people'
    }
  ];

  constructor() { }

  ngOnInit() { }

  selectRole(role: 'client' | 'lawyer' | 'secretary') {
    this.selectedRole = role;
    this.roleSelected.emit(role);
  }
}
