import { Component, OnInit, ViewChild, ElementRef } from '@angular/core';
import { Router } from '@angular/router';
import { AlertController, LoadingController } from '@ionic/angular';
import { FirebaseService, LawyerProfile } from '../services/firebase.service';

@Component({
  selector: 'app-profile',
  templateUrl: './profile.page.html',
  styleUrls: ['./profile.page.scss'],
  standalone: false,
})
export class ProfilePage implements OnInit {
  @ViewChild('fileInput', { static: false }) fileInput!: ElementRef<HTMLInputElement>;

  lawyer: LawyerProfile | null = null;
  profileImageUrl: string | null = null;
  isUploading = false;
  isHovering = false;

  constructor(
    private router: Router,
    private firebaseService: FirebaseService,
    private alertController: AlertController,
    private loadingController: LoadingController
  ) { }

  ngOnInit() {
    this.loadLawyerProfile();
    this.loadProfileImage();
  }

  async loadLawyerProfile() {
    const currentUser = this.firebaseService.getCurrentUser();
    if (currentUser) {
      this.lawyer = await this.firebaseService.getLawyerProfile(currentUser.uid);
    }
  }

  async loadProfileImage() {
    try {
      // Try to get from Firebase first
      const currentUser = this.firebaseService.getCurrentUser();
      if (currentUser) {
        const imageUrl = await this.firebaseService.getUserProfileImage(currentUser.uid);
        if (imageUrl) {
          this.profileImageUrl = imageUrl;
          return;
        }
      }

      // Fallback to localStorage for mock users
      const savedImage = localStorage.getItem('profileImage');
      if (savedImage) {
        this.profileImageUrl = savedImage;
      }
    } catch (error) {
      console.error('Error loading profile image:', error);
      // Fallback to localStorage
      const savedImage = localStorage.getItem('profileImage');
      if (savedImage) {
        this.profileImageUrl = savedImage;
      }
    }
  }

  onUploadImage() {
    if (this.isUploading) return;
    this.fileInput.nativeElement.click();
  }

  async onFileSelected(event: any) {
    const file = event.target.files[0];
    if (!file) return;

    // Validate file type
    if (!file.type.startsWith('image/')) {
      await this.showAlert('Invalid File', 'Please select an image file.');
      return;
    }

    // Validate file size (max 2MB for faster upload)
    if (file.size > 2 * 1024 * 1024) {
      await this.showAlert('File Too Large', 'Please select an image smaller than 2MB for faster upload.');
      return;
    }

    await this.uploadProfileImage(file);
  }

  async uploadProfileImage(file: File) {
    const loading = await this.loadingController.create({
      message: 'Compressing image...',
      spinner: 'crescent'
    });
    await loading.present();

    let compressedFile: File = file; // Initialize with original file

    try {
      this.isUploading = true;

      // Compress image first for faster upload
      console.log('Compressing image...');
      compressedFile = await this.compressImage(file);

      loading.message = 'Uploading to Firebase...';

      // Get current user
      const currentUser = this.firebaseService.getCurrentUser();

      if (currentUser) {
        // Try progress upload first, fallback to regular upload
        console.log('Uploading compressed image to Firebase Storage...');
        let downloadURL: string;

        try {
          // Upload profile image
          downloadURL = await this.firebaseService.uploadProfileImageWithProgress(compressedFile, currentUser.uid);
        } catch (uploadError) {
          console.log('Upload failed:', uploadError);
          loading.message = 'Upload failed, please try again';
          throw uploadError;
        }

        // Update local display
        this.profileImageUrl = downloadURL;

        await loading.dismiss();
        await this.showAlert('Success', 'Profile image uploaded successfully!');

      } else {
        // Fallback to localStorage for mock users (also use compressed image)
        console.log('No Firebase user, using localStorage...');
        const reader = new FileReader();

        reader.onload = async (e: any) => {
          const base64Image = e.target.result;

          // Save compressed image to localStorage
          localStorage.setItem('profileImage', base64Image);
          this.profileImageUrl = base64Image;

          await loading.dismiss();
          await this.showAlert('Success', 'Profile image updated successfully!');
        };

        reader.onerror = async () => {
          await loading.dismiss();
          await this.showAlert('Error', 'Failed to process the image. Please try again.');
        };

        reader.readAsDataURL(compressedFile);
      }

    } catch (error) {
      console.error('Upload error:', error);

      // Fallback to localStorage if Firebase fails
      try {
        console.log('Firebase upload failed, using localStorage fallback...');
        loading.message = 'Saving locally...';

        const reader = new FileReader();
        reader.onload = async (e: any) => {
          const base64Image = e.target.result;
          localStorage.setItem('profileImage', base64Image);
          this.profileImageUrl = base64Image;

          await loading.dismiss();
          await this.showAlert('Saved Locally', 'Image saved locally. Will sync to cloud when connection improves.');
        };

        reader.onerror = async () => {
          await loading.dismiss();
          await this.showAlert('Upload Error', 'Failed to save image. Please try again.');
        };

        reader.readAsDataURL(compressedFile || file);

      } catch (fallbackError) {
        await loading.dismiss();

        // Show specific error message
        let errorMessage = 'Failed to upload image. Please try again.';
        if (error instanceof Error) {
          errorMessage = error.message;
        }

        await this.showAlert('Upload Error', errorMessage);
      }
    } finally {
      this.isUploading = false;
    }
  }

  private async compressImage(file: File): Promise<File> {
    return new Promise((resolve, reject) => {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      const img = new Image();

      img.onload = () => {
        // Calculate new dimensions (max 300x300 for faster upload)
        const maxSize = 300;
        let { width, height } = img;

        if (width > height) {
          if (width > maxSize) {
            height = (height * maxSize) / width;
            width = maxSize;
          }
        } else {
          if (height > maxSize) {
            width = (width * maxSize) / height;
            height = maxSize;
          }
        }

        // Set canvas size
        canvas.width = width;
        canvas.height = height;

        // Draw and compress with better quality settings
        if (ctx) {
          // Improve image quality during resize
          ctx.imageSmoothingEnabled = true;
          ctx.imageSmoothingQuality = 'high';
          ctx.drawImage(img, 0, 0, width, height);
        }

        canvas.toBlob(
          (blob) => {
            if (blob) {
              const compressedFile = new File([blob], file.name, {
                type: 'image/jpeg',
                lastModified: Date.now()
              });
              console.log(`Compressed from ${(file.size / 1024).toFixed(1)}KB to ${(compressedFile.size / 1024).toFixed(1)}KB`);
              resolve(compressedFile);
            } else {
              reject(new Error('Failed to compress image'));
            }
          },
          'image/jpeg',
          0.7 // 70% quality for smaller files
        );
      };

      img.onerror = () => reject(new Error('Failed to load image'));
      img.src = URL.createObjectURL(file);
    });
  }

  onViewProfile() {
    console.log('View profile');
    // Navigate to detailed profile view
  }

  onProfileInformation() {
    console.log('Profile Information clicked - navigating to /profile-information');
    try {
      this.router.navigate(['/profile-information']).then((success) => {
        console.log('Navigation success:', success);
      }).catch((error) => {
        console.error('Navigation error:', error);
      });
    } catch (error) {
      console.error('Router navigate error:', error);
    }
  }

  onProfileIntroduction() {
    console.log('Profile Introduction & description');
    // Navigate to profile introduction page
  }

  onPermissions() {
    console.log('Permissions');
    // Navigate to permissions page
  }

  async onLogout() {
    console.log('Logout clicked'); // Debug log

    try {
      // Show confirmation dialog
      const alert = await this.alertController.create({
        header: 'Logout',
        message: 'Are you sure you want to logout?',
        buttons: [
          {
            text: 'Cancel',
            role: 'cancel'
          },
          {
            text: 'Logout',
            handler: () => {
              this.performLogout();
            }
          }
        ]
      });

      await alert.present();
    } catch (error) {
      console.error('Error showing logout dialog:', error);
      // If dialog fails, just logout directly
      this.performLogout();
    }
  }

  private async performLogout() {
    try {
      console.log('Starting logout process...'); // Debug log

      // Show loading
      const loading = await this.loadingController.create({
        message: 'Logging out...',
        spinner: 'crescent'
      });
      await loading.present();

      // Clear localStorage for mock authentication (keep profile image for demo)
      localStorage.removeItem('userRole');
      localStorage.removeItem('isAuthenticated');
      localStorage.removeItem('userName');
      localStorage.removeItem('userEmail');

      console.log('localStorage cleared'); // Debug log

      // Try Firebase logout (may fail if not properly authenticated)
      try {
        await this.firebaseService.signOut();
        console.log('Firebase signOut successful'); // Debug log
      } catch (firebaseError) {
        console.log('Firebase signOut failed (expected for mock auth):', firebaseError);
      }

      await loading.dismiss();

      console.log('Navigating to signin...'); // Debug log

      // Navigate to signin page
      this.router.navigate(['/auth/signin']).then(() => {
        console.log('Navigation to signin completed');
      }).catch((navError) => {
        console.error('Navigation error:', navError);
      });

    } catch (error) {
      console.error('Error during logout:', error);

      // Force clear and redirect even if there's an error
      localStorage.clear();
      window.location.href = '/auth/signin';
    }
  }

  private async showAlert(header: string, message: string) {
    const alert = await this.alertController.create({
      header,
      message,
      buttons: ['OK']
    });
    await alert.present();
  }
}
