<ion-content class="calendar-content">
  <div class="calendar-container">

    <!-- Tab Navigation -->
    <div class="tab-navigation">
      <ion-segment [(ngModel)]="selectedTab" (ionChange)="onTabChange($event)">
        <ion-segment-button value="calendar">
          <ion-icon name="calendar-outline"></ion-icon>
          <ion-label>Calendar</ion-label>
        </ion-segment-button>
        <ion-segment-button value="appointments">
          <ion-icon name="list-outline"></ion-icon>
          <ion-label>Appointments</ion-label>
        </ion-segment-button>
        <ion-segment-button value="availability">
          <ion-icon name="time-outline"></ion-icon>
          <ion-label>Availability</ion-label>
        </ion-segment-button>
      </ion-segment>
    </div>

    <!-- Calendar Tab Content -->
    <div *ngIf="selectedTab === 'calendar'" class="tab-content">
      <div class="placeholder-content">
        <ion-card>
          <ion-card-header>
            <ion-card-title>📅 Calendar View</ion-card-title>
            <ion-card-subtitle>Multi-lawyer calendar management</ion-card-subtitle>
          </ion-card-header>
          <ion-card-content>
            <p>Calendar functionality will be available here.</p>
            <p><strong>Linked Lawyers:</strong> {{ linkedLawyers.length }}</p>
            <ion-button routerLink="/calendar" fill="solid">
              <ion-icon name="calendar-outline" slot="start"></ion-icon>
              Go to Calendar
            </ion-button>
          </ion-card-content>
        </ion-card>
      </div>
    </div>

    <!-- Appointments Tab Content -->
    <div *ngIf="selectedTab === 'appointments'" class="tab-content">
      <div class="placeholder-content">
        <ion-card>
          <ion-card-header>
            <ion-card-title>📋 Appointment Management</ion-card-title>
            <ion-card-subtitle>Manage appointments for linked lawyers</ion-card-subtitle>
          </ion-card-header>
          <ion-card-content>
            <p>Appointment management functionality will be available here.</p>
            <p><strong>Total Appointments:</strong> {{ appointments.length }}</p>
            <ion-button routerLink="/calendar" fill="solid">
              <ion-icon name="calendar-outline" slot="start"></ion-icon>
              Manage Appointments
            </ion-button>
          </ion-card-content>
        </ion-card>
      </div>
    </div>

    <!-- Availability Tab Content -->
    <div *ngIf="selectedTab === 'availability'" class="tab-content">
      <div class="placeholder-content">
        <ion-card>
          <ion-card-header>
            <ion-card-title>⏰ Lawyer Availability</ion-card-title>
            <ion-card-subtitle>Manage lawyer schedules and availability</ion-card-subtitle>
          </ion-card-header>
          <ion-card-content>
            <p>Availability management functionality will be available here.</p>
            <p><strong>Selected Date:</strong> {{ selectedDate | date:'mediumDate' }}</p>
            <ion-button fill="solid" (click)="refreshData()">
              <ion-icon name="refresh-outline" slot="start"></ion-icon>
              Refresh Data
            </ion-button>
          </ion-card-content>
        </ion-card>
      </div>
    </div>
    <!-- Quick Stats -->
    <div class="stats-section">
      <div class="stat-card stat-total">
        <div class="stat-number">
          {{ getTotalAppointments() }}
        </div>
        <div class="stat-label">Total Appointments</div>
      </div>

      <div class="stat-card stat-confirmed">
        <div class="stat-number">
          {{ getConfirmedCount() }}
        </div>
        <div class="stat-label">Confirmed</div>
      </div>

      <div class="stat-card stat-pending">
        <div class="stat-number">
          {{ getPendingCount() }}
        </div>
        <div class="stat-label">Pending</div>
      </div>
    </div>

  </div>
</ion-content>
