(self["webpackChunkveritus_secretary"] = self["webpackChunkveritus_secretary"] || []).push([["main"],{

/***/ 6070:
/*!********************************************************!*\
  !*** ./src/app/activity-log/activity-log.component.ts ***!
  \********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ActivityLogComponent: () => (/* binding */ ActivityLogComponent)
/* harmony export */ });
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @angular/core */ 7580);
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @angular/common */ 316);
/* harmony import */ var _angular_forms__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @angular/forms */ 4456);
/* harmony import */ var _angular_material_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @angular/material/button */ 4175);
/* harmony import */ var _angular_material_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @angular/material/input */ 5541);
/* harmony import */ var _angular_material_form_field__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @angular/material/form-field */ 4950);
/* harmony import */ var _angular_material_icon__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @angular/material/icon */ 3840);
/* harmony import */ var _angular_material_menu__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @angular/material/menu */ 1034);
/* harmony import */ var _angular_material_paginator__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @angular/material/paginator */ 4624);
/* harmony import */ var _angular_material_select__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @angular/material/select */ 5175);
/* harmony import */ var _angular_material_core__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @angular/material/core */ 4646);











const _c0 = () => [10, 25, 50, 100];
function ActivityLogComponent_div_136_span_26_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](0, "span", 71)(1, "mat-icon");
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](2, "computer");
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const activity_r2 = _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵnextContext"]().$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵadvance"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtextInterpolate1"](" ", activity_r2.ipAddress, " ");
  }
}
function ActivityLogComponent_div_136_Template(rf, ctx) {
  if (rf & 1) {
    const _r1 = _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](0, "div", 55)(1, "div", 56)(2, "mat-icon");
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](4, "div", 57)(5, "div", 58)(6, "div", 59);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](7);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](8, "div", 60)(9, "span", 61);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](10);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵpipe"](11, "date");
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](12, "span", 62);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](13);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵpipe"](14, "titlecase");
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](15, "span", 63);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](16);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵpipe"](17, "titlecase");
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]()()();
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](18, "div", 64)(19, "p");
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](20);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](21, "div", 65)(22, "span", 66)(23, "mat-icon");
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](24, "person");
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](25);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtemplate"](26, ActivityLogComponent_div_136_span_26_Template, 4, 1, "span", 67);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]()()();
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](27, "div", 68)(28, "button", 5);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵlistener"]("click", function ActivityLogComponent_div_136_Template_button_click_28_listener() {
      const activity_r2 = _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵrestoreView"](_r1).$implicit;
      const ctx_r2 = _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵresetView"](ctx_r2.viewActivityDetails(activity_r2));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](29, "mat-icon");
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](30, "visibility");
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](31, "button", 69)(32, "mat-icon");
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](33, "more_vert");
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](34, "mat-menu", null, 0)(36, "button", 70);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵlistener"]("click", function ActivityLogComponent_div_136_Template_button_click_36_listener() {
      const activity_r2 = _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵrestoreView"](_r1).$implicit;
      const ctx_r2 = _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵresetView"](ctx_r2.exportSingleActivity(activity_r2));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](37, "mat-icon");
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](38, "download");
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](39, "span");
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](40, "Export Entry");
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](41, "button", 70);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵlistener"]("click", function ActivityLogComponent_div_136_Template_button_click_41_listener() {
      const activity_r2 = _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵrestoreView"](_r1).$implicit;
      const ctx_r2 = _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵresetView"](ctx_r2.flagActivity(activity_r2));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](42, "mat-icon");
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](43, "flag");
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](44, "span");
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](45, "Flag for Review");
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]()()()()();
  }
  if (rf & 2) {
    const activity_r2 = ctx.$implicit;
    const activityMenu_r4 = _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵreference"](35);
    const ctx_r2 = _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵclassMap"](activity_r2.severity);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵadvance"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵclassMap"](activity_r2.status);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtextInterpolate"](ctx_r2.getActivityIcon(activity_r2.action));
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵadvance"](4);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtextInterpolate"](activity_r2.description);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵadvance"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtextInterpolate"](_angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵpipeBind2"](11, 17, activity_r2.timestamp, "MMM dd, yyyy HH:mm:ss"));
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵclassMap"](activity_r2.category);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵadvance"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtextInterpolate"](_angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵpipeBind1"](14, 20, activity_r2.category));
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵclassMap"](activity_r2.severity);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵadvance"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtextInterpolate"](_angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵpipeBind1"](17, 22, activity_r2.severity));
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵadvance"](4);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtextInterpolate"](activity_r2.details);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵadvance"](5);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtextInterpolate1"](" ", activity_r2.user, " ");
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵadvance"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵproperty"]("ngIf", activity_r2.ipAddress);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵadvance"](5);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵproperty"]("matMenuTriggerFor", activityMenu_r4);
  }
}
function ActivityLogComponent_div_137_Template(rf, ctx) {
  if (rf & 1) {
    const _r5 = _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](0, "div", 72)(1, "mat-paginator", 73);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵlistener"]("page", function ActivityLogComponent_div_137_Template_mat_paginator_page_1_listener($event) {
      _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵrestoreView"](_r5);
      const ctx_r2 = _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵresetView"](ctx_r2.onPageChange($event));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]()();
  }
  if (rf & 2) {
    const ctx_r2 = _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵadvance"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵproperty"]("length", ctx_r2.filteredActivities.length)("pageSize", ctx_r2.pageSize)("pageSizeOptions", _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵpureFunction0"](3, _c0));
  }
}
class ActivityLogComponent {
  constructor() {
    this.searchTerm = '';
    this.selectedDateRange = 'month';
    this.selectedCategory = '';
    this.selectedAction = '';
    this.selectedSeverity = '';
    this.pageSize = 25;
    this.currentPage = 0;
    this.activities = [{
      id: '1',
      timestamp: new Date(2025, 6, 11, 14, 30, 0),
      action: 'create',
      category: 'client',
      description: 'Created new client profile',
      details: 'Added new retainer client "Maria Santos" with ₱60,000 retainer amount. All required documentation verified.',
      user: 'Manny P. (Secretary)',
      ipAddress: '*************',
      userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
      severity: 'medium',
      status: 'success'
    }, {
      id: '2',
      timestamp: new Date(2025, 6, 11, 13, 15, 0),
      action: 'upload',
      category: 'file',
      description: 'Uploaded case documents',
      details: 'Uploaded 3 files to Evidence folder: witness-statement.pdf (2.1MB), photo-evidence.zip (15.2MB), expert-report.docx (890KB)',
      user: 'Manny P. (Secretary)',
      ipAddress: '*************',
      severity: 'low',
      status: 'success'
    }, {
      id: '3',
      timestamp: new Date(2025, 6, 11, 12, 45, 0),
      action: 'update',
      category: 'appointment',
      description: 'Modified appointment schedule',
      details: 'Rescheduled consultation for Kobe Bryant from July 10, 2025 3:00 PM to July 12, 2025 10:00 AM. Client notified via email.',
      user: 'Manny P. (Secretary)',
      ipAddress: '*************',
      severity: 'medium',
      status: 'success'
    }, {
      id: '4',
      timestamp: new Date(2025, 6, 11, 11, 20, 0),
      action: 'create',
      category: 'transaction',
      description: 'Recorded consultation payment',
      details: 'Added consultation fee payment of ₱15,000 from John Doe. Payment method: Bank Transfer. Reference: TXN-2025-0711-001',
      user: 'Manny P. (Secretary)',
      ipAddress: '*************',
      severity: 'high',
      status: 'success'
    }, {
      id: '5',
      timestamp: new Date(2025, 6, 11, 10, 30, 0),
      action: 'download',
      category: 'file',
      description: 'Downloaded client contract',
      details: 'Downloaded Lopez-Contract.pdf (2.4MB) for review and client meeting preparation.',
      user: 'Manny P. (Secretary)',
      ipAddress: '*************',
      severity: 'low',
      status: 'success'
    }, {
      id: '6',
      timestamp: new Date(2025, 6, 11, 9, 15, 0),
      action: 'login',
      category: 'security',
      description: 'User login successful',
      details: 'Secretary Manny P. logged in successfully from IP *************. Session ID: sess_2025071109150001',
      user: 'Manny P. (Secretary)',
      ipAddress: '*************',
      severity: 'low',
      status: 'success'
    }, {
      id: '7',
      timestamp: new Date(2025, 6, 10, 17, 45, 0),
      action: 'logout',
      category: 'security',
      description: 'User logout',
      details: 'Secretary Manny P. logged out. Session duration: 8 hours 30 minutes. No security incidents detected.',
      user: 'Manny P. (Secretary)',
      ipAddress: '*************',
      severity: 'low',
      status: 'success'
    }, {
      id: '8',
      timestamp: new Date(2025, 6, 10, 16, 20, 0),
      action: 'update',
      category: 'client',
      description: 'Updated client contact information',
      details: 'Updated phone number for Clara Mendoza from +63 ************ to +63 ************. Email verification sent.',
      user: 'Manny P. (Secretary)',
      ipAddress: '*************',
      severity: 'medium',
      status: 'success'
    }, {
      id: '9',
      timestamp: new Date(2025, 6, 10, 15, 10, 0),
      action: 'view',
      category: 'file',
      description: 'Accessed confidential documents',
      details: 'Viewed financial disclosure documents for case #2025-0710-001. Access logged for compliance audit.',
      user: 'Manny P. (Secretary)',
      ipAddress: '*************',
      severity: 'high',
      status: 'success'
    }, {
      id: '10',
      timestamp: new Date(2025, 6, 10, 14, 30, 0),
      action: 'delete',
      category: 'file',
      description: 'Deleted duplicate file',
      details: 'Removed duplicate file "contract-copy-2.pdf" from Contracts folder. Original file retained with proper naming convention.',
      user: 'Manny P. (Secretary)',
      ipAddress: '*************',
      severity: 'medium',
      status: 'success'
    }, {
      id: '11',
      timestamp: new Date(2025, 6, 9, 16, 45, 0),
      action: 'create',
      category: 'appointment',
      description: 'Scheduled new appointment',
      details: 'Created appointment for Michael Jordan - Case Review on July 12, 2025 at 2:00 PM. Calendar invite sent to all parties.',
      user: 'Manny P. (Secretary)',
      ipAddress: '*************',
      severity: 'medium',
      status: 'success'
    }, {
      id: '12',
      timestamp: new Date(2025, 6, 9, 11, 20, 0),
      action: 'update',
      category: 'system',
      description: 'System backup completed',
      details: 'Automated daily backup completed successfully. 1.2GB of data backed up to secure cloud storage. Backup verification passed.',
      user: 'System',
      ipAddress: '********',
      severity: 'low',
      status: 'success'
    }, {
      id: '13',
      timestamp: new Date(2025, 6, 8, 13, 30, 0),
      action: 'view',
      category: 'security',
      description: 'Failed login attempt detected',
      details: 'Multiple failed login attempts detected from IP ************. Account temporarily locked for security. User notified via email.',
      user: 'Security System',
      ipAddress: '************',
      severity: 'critical',
      status: 'warning'
    }];
    this.filteredActivities = [];
  }
  ngOnInit() {
    this.filteredActivities = [...this.activities];
  }
  filterActivities() {
    let filtered = [...this.activities];
    // Filter by search term
    if (this.searchTerm) {
      const term = this.searchTerm.toLowerCase();
      filtered = filtered.filter(activity => activity.description.toLowerCase().includes(term) || activity.details.toLowerCase().includes(term) || activity.user.toLowerCase().includes(term));
    }
    // Filter by category
    if (this.selectedCategory) {
      filtered = filtered.filter(activity => activity.category === this.selectedCategory);
    }
    // Filter by action
    if (this.selectedAction) {
      filtered = filtered.filter(activity => activity.action === this.selectedAction);
    }
    // Filter by severity
    if (this.selectedSeverity) {
      filtered = filtered.filter(activity => activity.severity === this.selectedSeverity);
    }
    // Filter by date range
    if (this.selectedDateRange !== 'all') {
      const now = new Date();
      let startDate = new Date();
      switch (this.selectedDateRange) {
        case 'today':
          startDate.setHours(0, 0, 0, 0);
          break;
        case 'week':
          startDate.setDate(now.getDate() - 7);
          break;
        case 'month':
          startDate.setMonth(now.getMonth() - 1);
          break;
        case 'quarter':
          startDate.setMonth(now.getMonth() - 3);
          break;
        case 'year':
          startDate.setFullYear(now.getFullYear() - 1);
          break;
      }
      filtered = filtered.filter(activity => activity.timestamp >= startDate);
    }
    this.filteredActivities = filtered;
  }
  getActivityCount(status) {
    return this.filteredActivities.filter(activity => activity.status === status).length;
  }
  getTotalActivities() {
    return this.filteredActivities.length;
  }
  getActivityIcon(action) {
    switch (action) {
      case 'create':
        return 'add_circle';
      case 'update':
        return 'edit';
      case 'delete':
        return 'delete';
      case 'view':
        return 'visibility';
      case 'download':
        return 'download';
      case 'upload':
        return 'upload';
      case 'login':
        return 'login';
      case 'logout':
        return 'logout';
      default:
        return 'info';
    }
  }
  exportLog() {
    // Implement log export functionality
    console.log('Exporting activity log...');
  }
  refreshLog() {
    // Implement log refresh functionality
    this.filterActivities();
    console.log('Activity log refreshed');
  }
  viewActivityDetails(activity) {
    // Implement activity details view
    console.log('Viewing activity details:', activity);
  }
  exportSingleActivity(activity) {
    // Implement single activity export
    console.log('Exporting activity:', activity);
  }
  flagActivity(activity) {
    // Implement activity flagging
    console.log('Flagging activity for review:', activity);
  }
  onPageChange(event) {
    this.currentPage = event.pageIndex;
    this.pageSize = event.pageSize;
  }
  static {
    this.ɵfac = function ActivityLogComponent_Factory(t) {
      return new (t || ActivityLogComponent)();
    };
  }
  static {
    this.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵdefineComponent"]({
      type: ActivityLogComponent,
      selectors: [["app-activity-log"]],
      decls: 138,
      vars: 12,
      consts: [["activityMenu", "matMenu"], [1, "activity-log-container"], [1, "activity-log-header"], [1, "header-actions"], ["mat-raised-button", "", "color", "primary", 3, "click"], ["mat-icon-button", "", 3, "click"], [1, "filters-section"], [1, "filter-row"], ["appearance", "outline", 1, "filter-field"], [3, "valueChange", "selectionChange", "value"], ["value", "today"], ["value", "week"], ["value", "month"], ["value", "quarter"], ["value", "year"], ["value", "all"], ["value", ""], ["value", "client"], ["value", "file"], ["value", "appointment"], ["value", "transaction"], ["value", "system"], ["value", "security"], ["value", "create"], ["value", "update"], ["value", "delete"], ["value", "view"], ["value", "download"], ["value", "upload"], ["value", "login"], ["value", "logout"], ["value", "low"], ["value", "medium"], ["value", "high"], ["value", "critical"], [1, "search-row"], ["appearance", "outline", 1, "search-field"], ["matPrefix", ""], ["matInput", "", "placeholder", "Search by description, user, or details", 3, "ngModelChange", "input", "ngModel"], [1, "stats-section"], [1, "stat-card", "success"], [1, "stat-icon"], [1, "stat-content"], [1, "stat-value"], [1, "stat-label"], [1, "stat-card", "warning"], [1, "stat-card", "error"], [1, "stat-card", "info"], [1, "log-table-section"], [1, "table-header"], [1, "table-controls"], [1, "results-count"], [1, "activity-timeline"], ["class", "activity-entry", 3, "class", 4, "ngFor", "ngForOf"], ["class", "pagination-section", 4, "ngIf"], [1, "activity-entry"], [1, "activity-indicator"], [1, "activity-content"], [1, "activity-header"], [1, "activity-title"], [1, "activity-meta"], [1, "activity-time"], [1, "activity-category"], [1, "activity-severity"], [1, "activity-details"], [1, "activity-user-info"], [1, "user-name"], ["class", "ip-address", 4, "ngIf"], [1, "activity-actions"], ["mat-icon-button", "", 3, "matMenuTriggerFor"], ["mat-menu-item", "", 3, "click"], [1, "ip-address"], [1, "pagination-section"], [3, "page", "length", "pageSize", "pageSizeOptions"]],
      template: function ActivityLogComponent_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](0, "div", 1)(1, "div", 2)(2, "h1");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](3, "Activity Log");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](4, "div", 3)(5, "button", 4);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵlistener"]("click", function ActivityLogComponent_Template_button_click_5_listener() {
            return ctx.exportLog();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](6, "mat-icon");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](7, "download");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](8, " Export Log ");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](9, "button", 5);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵlistener"]("click", function ActivityLogComponent_Template_button_click_9_listener() {
            return ctx.refreshLog();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](10, "mat-icon");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](11, "refresh");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]()()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](12, "div", 6)(13, "div", 7)(14, "mat-form-field", 8)(15, "mat-label");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](16, "Date Range");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](17, "mat-select", 9);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtwoWayListener"]("valueChange", function ActivityLogComponent_Template_mat_select_valueChange_17_listener($event) {
            _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtwoWayBindingSet"](ctx.selectedDateRange, $event) || (ctx.selectedDateRange = $event);
            return $event;
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵlistener"]("selectionChange", function ActivityLogComponent_Template_mat_select_selectionChange_17_listener() {
            return ctx.filterActivities();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](18, "mat-option", 10);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](19, "Today");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](20, "mat-option", 11);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](21, "This Week");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](22, "mat-option", 12);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](23, "This Month");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](24, "mat-option", 13);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](25, "This Quarter");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](26, "mat-option", 14);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](27, "This Year");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](28, "mat-option", 15);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](29, "All Time");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](30, "mat-form-field", 8)(31, "mat-label");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](32, "Category");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](33, "mat-select", 9);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtwoWayListener"]("valueChange", function ActivityLogComponent_Template_mat_select_valueChange_33_listener($event) {
            _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtwoWayBindingSet"](ctx.selectedCategory, $event) || (ctx.selectedCategory = $event);
            return $event;
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵlistener"]("selectionChange", function ActivityLogComponent_Template_mat_select_selectionChange_33_listener() {
            return ctx.filterActivities();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](34, "mat-option", 16);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](35, "All Categories");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](36, "mat-option", 17);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](37, "Client Management");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](38, "mat-option", 18);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](39, "File Operations");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](40, "mat-option", 19);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](41, "Appointments");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](42, "mat-option", 20);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](43, "Transactions");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](44, "mat-option", 21);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](45, "System");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](46, "mat-option", 22);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](47, "Security");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](48, "mat-form-field", 8)(49, "mat-label");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](50, "Action Type");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](51, "mat-select", 9);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtwoWayListener"]("valueChange", function ActivityLogComponent_Template_mat_select_valueChange_51_listener($event) {
            _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtwoWayBindingSet"](ctx.selectedAction, $event) || (ctx.selectedAction = $event);
            return $event;
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵlistener"]("selectionChange", function ActivityLogComponent_Template_mat_select_selectionChange_51_listener() {
            return ctx.filterActivities();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](52, "mat-option", 16);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](53, "All Actions");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](54, "mat-option", 23);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](55, "Create");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](56, "mat-option", 24);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](57, "Update");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](58, "mat-option", 25);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](59, "Delete");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](60, "mat-option", 26);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](61, "View");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](62, "mat-option", 27);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](63, "Download");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](64, "mat-option", 28);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](65, "Upload");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](66, "mat-option", 29);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](67, "Login");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](68, "mat-option", 30);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](69, "Logout");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](70, "mat-form-field", 8)(71, "mat-label");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](72, "Severity");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](73, "mat-select", 9);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtwoWayListener"]("valueChange", function ActivityLogComponent_Template_mat_select_valueChange_73_listener($event) {
            _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtwoWayBindingSet"](ctx.selectedSeverity, $event) || (ctx.selectedSeverity = $event);
            return $event;
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵlistener"]("selectionChange", function ActivityLogComponent_Template_mat_select_selectionChange_73_listener() {
            return ctx.filterActivities();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](74, "mat-option", 16);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](75, "All Levels");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](76, "mat-option", 31);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](77, "Low");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](78, "mat-option", 32);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](79, "Medium");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](80, "mat-option", 33);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](81, "High");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](82, "mat-option", 34);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](83, "Critical");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]()()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](84, "div", 35)(85, "mat-form-field", 36)(86, "mat-icon", 37);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](87, "search");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](88, "mat-label");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](89, "Search activities...");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](90, "input", 38);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtwoWayListener"]("ngModelChange", function ActivityLogComponent_Template_input_ngModelChange_90_listener($event) {
            _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtwoWayBindingSet"](ctx.searchTerm, $event) || (ctx.searchTerm = $event);
            return $event;
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵlistener"]("input", function ActivityLogComponent_Template_input_input_90_listener() {
            return ctx.filterActivities();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]()()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](91, "div", 39)(92, "div", 40)(93, "div", 41)(94, "mat-icon");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](95, "check_circle");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](96, "div", 42)(97, "div", 43);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](98);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](99, "div", 44);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](100, "Successful Actions");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](101, "div", 45)(102, "div", 41)(103, "mat-icon");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](104, "warning");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](105, "div", 42)(106, "div", 43);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](107);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](108, "div", 44);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](109, "Warnings");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](110, "div", 46)(111, "div", 41)(112, "mat-icon");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](113, "error");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](114, "div", 42)(115, "div", 43);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](116);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](117, "div", 44);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](118, "Failed Actions");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](119, "div", 47)(120, "div", 41)(121, "mat-icon");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](122, "info");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](123, "div", 42)(124, "div", 43);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](125);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](126, "div", 44);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](127, "Total Activities");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]()()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](128, "div", 48)(129, "div", 49)(130, "h3");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](131, "Activity Timeline");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](132, "div", 50)(133, "span", 51);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](134);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](135, "div", 52);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtemplate"](136, ActivityLogComponent_div_136_Template, 46, 24, "div", 53);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtemplate"](137, ActivityLogComponent_div_137_Template, 2, 4, "div", 54);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]()();
        }
        if (rf & 2) {
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵadvance"](17);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtwoWayProperty"]("value", ctx.selectedDateRange);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵadvance"](16);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtwoWayProperty"]("value", ctx.selectedCategory);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵadvance"](18);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtwoWayProperty"]("value", ctx.selectedAction);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵadvance"](22);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtwoWayProperty"]("value", ctx.selectedSeverity);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵadvance"](17);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtwoWayProperty"]("ngModel", ctx.searchTerm);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵadvance"](8);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtextInterpolate"](ctx.getActivityCount("success"));
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵadvance"](9);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtextInterpolate"](ctx.getActivityCount("warning"));
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵadvance"](9);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtextInterpolate"](ctx.getActivityCount("failed"));
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵadvance"](9);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtextInterpolate"](ctx.getTotalActivities());
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵadvance"](9);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtextInterpolate1"]("", ctx.filteredActivities.length, " activities found");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵadvance"](2);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵproperty"]("ngForOf", ctx.filteredActivities);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵadvance"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵproperty"]("ngIf", ctx.filteredActivities.length > ctx.pageSize);
        }
      },
      dependencies: [_angular_common__WEBPACK_IMPORTED_MODULE_1__.NgForOf, _angular_common__WEBPACK_IMPORTED_MODULE_1__.NgIf, _angular_forms__WEBPACK_IMPORTED_MODULE_2__.DefaultValueAccessor, _angular_forms__WEBPACK_IMPORTED_MODULE_2__.NgControlStatus, _angular_forms__WEBPACK_IMPORTED_MODULE_2__.NgModel, _angular_material_button__WEBPACK_IMPORTED_MODULE_3__.MatButton, _angular_material_button__WEBPACK_IMPORTED_MODULE_3__.MatIconButton, _angular_material_input__WEBPACK_IMPORTED_MODULE_4__.MatInput, _angular_material_form_field__WEBPACK_IMPORTED_MODULE_5__.MatFormField, _angular_material_form_field__WEBPACK_IMPORTED_MODULE_5__.MatLabel, _angular_material_form_field__WEBPACK_IMPORTED_MODULE_5__.MatPrefix, _angular_material_icon__WEBPACK_IMPORTED_MODULE_6__.MatIcon, _angular_material_menu__WEBPACK_IMPORTED_MODULE_7__.MatMenu, _angular_material_menu__WEBPACK_IMPORTED_MODULE_7__.MatMenuItem, _angular_material_menu__WEBPACK_IMPORTED_MODULE_7__.MatMenuTrigger, _angular_material_paginator__WEBPACK_IMPORTED_MODULE_8__.MatPaginator, _angular_material_select__WEBPACK_IMPORTED_MODULE_9__.MatSelect, _angular_material_core__WEBPACK_IMPORTED_MODULE_10__.MatOption, _angular_common__WEBPACK_IMPORTED_MODULE_1__.TitleCasePipe, _angular_common__WEBPACK_IMPORTED_MODULE_1__.DatePipe],
      styles: [".activity-log-container[_ngcontent-%COMP%] {\n  padding: 32px;\n  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);\n  min-height: calc(100vh - 64px);\n  position: relative;\n}\n\n.activity-log-container[_ngcontent-%COMP%]::before {\n  content: \"\";\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: radial-gradient(circle at 25% 25%, rgba(251, 146, 60, 0.05) 0%, transparent 50%), radial-gradient(circle at 75% 75%, rgba(168, 85, 247, 0.03) 0%, transparent 50%);\n  pointer-events: none;\n}\n\n.activity-log-header[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 40px;\n  position: relative;\n  z-index: 1;\n}\n\n.activity-log-header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\n  margin: 0;\n  font-size: 36px;\n  font-weight: 700;\n  color: #1e293b;\n  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n  position: relative;\n}\n\n.activity-log-header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]::after {\n  content: \"\";\n  position: absolute;\n  bottom: -8px;\n  left: 0;\n  width: 80px;\n  height: 4px;\n  background: linear-gradient(90deg, #fb923c 0%, #f97316 100%);\n  border-radius: 2px;\n}\n\n.header-actions[_ngcontent-%COMP%] {\n  display: flex;\n  gap: 12px;\n  align-items: center;\n}\n\n.filters-section[_ngcontent-%COMP%] {\n  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);\n  border-radius: 20px;\n  padding: 32px;\n  margin-bottom: 32px;\n  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1), 0 8px 24px rgba(0, 0, 0, 0.05);\n  border: 1px solid rgba(226, 232, 240, 0.8);\n  position: relative;\n  overflow: hidden;\n}\n\n.filters-section[_ngcontent-%COMP%]::before {\n  content: \"\";\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  height: 6px;\n  background: linear-gradient(90deg, #fb923c 0%, #f97316 50%, #fb923c 100%);\n}\n\n.filter-row[_ngcontent-%COMP%] {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n  gap: 20px;\n  margin-bottom: 20px;\n}\n\n.search-row[_ngcontent-%COMP%] {\n  display: flex;\n  gap: 20px;\n}\n\n.filter-field[_ngcontent-%COMP%], .search-field[_ngcontent-%COMP%] {\n  width: 100%;\n}\n\n.stats-section[_ngcontent-%COMP%] {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\n  gap: 24px;\n  margin-bottom: 32px;\n  position: relative;\n  z-index: 1;\n}\n\n.stat-card[_ngcontent-%COMP%] {\n  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);\n  border-radius: 20px;\n  padding: 28px;\n  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1), 0 8px 24px rgba(0, 0, 0, 0.05);\n  border: 1px solid rgba(226, 232, 240, 0.8);\n  display: flex;\n  align-items: center;\n  gap: 20px;\n  transition: all 0.3s ease;\n  position: relative;\n  overflow: hidden;\n}\n\n.stat-card[_ngcontent-%COMP%]::before {\n  content: \"\";\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  height: 4px;\n  transition: transform 0.3s ease;\n  transform: scaleX(0);\n}\n\n.stat-card.success[_ngcontent-%COMP%]::before {\n  background: linear-gradient(90deg, #22c55e 0%, #16a34a 100%);\n}\n\n.stat-card.warning[_ngcontent-%COMP%]::before {\n  background: linear-gradient(90deg, #f59e0b 0%, #d97706 100%);\n}\n\n.stat-card.error[_ngcontent-%COMP%]::before {\n  background: linear-gradient(90deg, #ef4444 0%, #dc2626 100%);\n}\n\n.stat-card.info[_ngcontent-%COMP%]::before {\n  background: linear-gradient(90deg, #3b82f6 0%, #2563eb 100%);\n}\n\n.stat-card[_ngcontent-%COMP%]:hover::before {\n  transform: scaleX(1);\n}\n\n.stat-card[_ngcontent-%COMP%]:hover {\n  transform: translateY(-4px);\n  box-shadow: 0 30px 80px rgba(0, 0, 0, 0.15), 0 12px 32px rgba(0, 0, 0, 0.08);\n}\n\n.stat-icon[_ngcontent-%COMP%] {\n  width: 60px;\n  height: 60px;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 28px;\n}\n\n.stat-card.success[_ngcontent-%COMP%]   .stat-icon[_ngcontent-%COMP%] {\n  background: linear-gradient(135deg, #dcfce7 0%, #bbf7d0 100%);\n  color: #16a34a;\n}\n\n.stat-card.warning[_ngcontent-%COMP%]   .stat-icon[_ngcontent-%COMP%] {\n  background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);\n  color: #d97706;\n}\n\n.stat-card.error[_ngcontent-%COMP%]   .stat-icon[_ngcontent-%COMP%] {\n  background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%);\n  color: #dc2626;\n}\n\n.stat-card.info[_ngcontent-%COMP%]   .stat-icon[_ngcontent-%COMP%] {\n  background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);\n  color: #2563eb;\n}\n\n.stat-content[_ngcontent-%COMP%] {\n  flex: 1;\n}\n\n.stat-value[_ngcontent-%COMP%] {\n  font-size: 32px;\n  font-weight: 700;\n  color: #1e293b;\n  margin-bottom: 4px;\n}\n\n.stat-label[_ngcontent-%COMP%] {\n  font-size: 14px;\n  color: #64748b;\n  font-weight: 500;\n}\n\n.log-table-section[_ngcontent-%COMP%] {\n  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);\n  border-radius: 20px;\n  padding: 32px;\n  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1), 0 8px 24px rgba(0, 0, 0, 0.05);\n  border: 1px solid rgba(226, 232, 240, 0.8);\n  position: relative;\n  overflow: hidden;\n}\n\n.log-table-section[_ngcontent-%COMP%]::before {\n  content: \"\";\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  height: 6px;\n  background: linear-gradient(90deg, #a855f7 0%, #9333ea 50%, #a855f7 100%);\n}\n\n.table-header[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 24px;\n}\n\n.table-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\n  margin: 0;\n  font-size: 20px;\n  font-weight: 600;\n  color: #1e293b;\n}\n\n.results-count[_ngcontent-%COMP%] {\n  font-size: 14px;\n  color: #64748b;\n  font-weight: 500;\n}\n\n.activity-timeline[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  gap: 16px;\n  margin-bottom: 24px;\n}\n\n.activity-entry[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: flex-start;\n  gap: 20px;\n  padding: 20px;\n  border: 1px solid rgba(226, 232, 240, 0.8);\n  border-radius: 16px;\n  transition: all 0.3s ease;\n  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);\n  position: relative;\n}\n\n.activity-entry[_ngcontent-%COMP%]:hover {\n  border-color: #fb923c;\n  box-shadow: 0 8px 24px rgba(251, 146, 60, 0.15);\n  transform: translateY(-2px);\n}\n\n.activity-entry.critical[_ngcontent-%COMP%] {\n  border-left: 4px solid #ef4444;\n}\n\n.activity-entry.high[_ngcontent-%COMP%] {\n  border-left: 4px solid #f59e0b;\n}\n\n.activity-entry.medium[_ngcontent-%COMP%] {\n  border-left: 4px solid #3b82f6;\n}\n\n.activity-entry.low[_ngcontent-%COMP%] {\n  border-left: 4px solid #22c55e;\n}\n\n.activity-indicator[_ngcontent-%COMP%] {\n  width: 48px;\n  height: 48px;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  flex-shrink: 0;\n}\n\n.activity-indicator.success[_ngcontent-%COMP%] {\n  background: linear-gradient(135deg, #dcfce7 0%, #bbf7d0 100%);\n  color: #16a34a;\n}\n\n.activity-indicator.warning[_ngcontent-%COMP%] {\n  background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);\n  color: #d97706;\n}\n\n.activity-indicator.failed[_ngcontent-%COMP%] {\n  background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%);\n  color: #dc2626;\n}\n\n.activity-content[_ngcontent-%COMP%] {\n  flex: 1;\n}\n\n.activity-header[_ngcontent-%COMP%] {\n  margin-bottom: 12px;\n}\n\n.activity-title[_ngcontent-%COMP%] {\n  font-size: 16px;\n  font-weight: 600;\n  color: #1e293b;\n  margin-bottom: 8px;\n}\n\n.activity-meta[_ngcontent-%COMP%] {\n  display: flex;\n  gap: 16px;\n  flex-wrap: wrap;\n}\n\n.activity-time[_ngcontent-%COMP%] {\n  font-size: 12px;\n  color: #64748b;\n  font-weight: 500;\n}\n\n.activity-category[_ngcontent-%COMP%], .activity-severity[_ngcontent-%COMP%] {\n  font-size: 11px;\n  padding: 4px 8px;\n  border-radius: 12px;\n  font-weight: 600;\n  text-transform: uppercase;\n}\n\n.activity-category.client[_ngcontent-%COMP%] {\n  background: #dbeafe;\n  color: #2563eb;\n}\n\n.activity-category.file[_ngcontent-%COMP%] {\n  background: #f3e8ff;\n  color: #9333ea;\n}\n\n.activity-category.appointment[_ngcontent-%COMP%] {\n  background: #ecfdf5;\n  color: #16a34a;\n}\n\n.activity-category.transaction[_ngcontent-%COMP%] {\n  background: #fef3c7;\n  color: #d97706;\n}\n\n.activity-category.system[_ngcontent-%COMP%] {\n  background: #f1f5f9;\n  color: #475569;\n}\n\n.activity-category.security[_ngcontent-%COMP%] {\n  background: #fee2e2;\n  color: #dc2626;\n}\n\n.activity-severity.low[_ngcontent-%COMP%] {\n  background: #dcfce7;\n  color: #16a34a;\n}\n\n.activity-severity.medium[_ngcontent-%COMP%] {\n  background: #dbeafe;\n  color: #2563eb;\n}\n\n.activity-severity.high[_ngcontent-%COMP%] {\n  background: #fef3c7;\n  color: #d97706;\n}\n\n.activity-severity.critical[_ngcontent-%COMP%] {\n  background: #fee2e2;\n  color: #dc2626;\n}\n\n.activity-details[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\n  margin: 0 0 12px 0;\n  font-size: 14px;\n  color: #475569;\n  line-height: 1.5;\n}\n\n.activity-user-info[_ngcontent-%COMP%] {\n  display: flex;\n  gap: 20px;\n  align-items: center;\n}\n\n.user-name[_ngcontent-%COMP%], .ip-address[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  gap: 6px;\n  font-size: 12px;\n  color: #64748b;\n}\n\n.user-name[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%], .ip-address[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\n  font-size: 16px;\n  width: 16px;\n  height: 16px;\n}\n\n.activity-actions[_ngcontent-%COMP%] {\n  display: flex;\n  gap: 4px;\n  flex-shrink: 0;\n}\n\n.activity-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\n  width: 36px;\n  height: 36px;\n}\n\n.activity-actions[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\n  font-size: 18px;\n  width: 18px;\n  height: 18px;\n}\n\n.pagination-section[_ngcontent-%COMP%] {\n  margin-top: 24px;\n  display: flex;\n  justify-content: center;\n}\n\n@media (max-width: 768px) {\n  .activity-log-header[_ngcontent-%COMP%] {\n    flex-direction: column;\n    gap: 16px;\n    align-items: stretch;\n  }\n  .filter-row[_ngcontent-%COMP%] {\n    grid-template-columns: 1fr;\n  }\n  .stats-section[_ngcontent-%COMP%] {\n    grid-template-columns: 1fr;\n  }\n  .activity-entry[_ngcontent-%COMP%] {\n    flex-direction: column;\n    gap: 16px;\n  }\n  .activity-meta[_ngcontent-%COMP%] {\n    flex-direction: column;\n    gap: 8px;\n  }\n}\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */"]
    });
  }
}

/***/ }),

/***/ 4114:
/*!***************************************!*\
  !*** ./src/app/app-routing.module.ts ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AppRoutingModule: () => (/* binding */ AppRoutingModule)
/* harmony export */ });
/* harmony import */ var _angular_router__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @angular/router */ 5072);
/* harmony import */ var _dashboard_dashboard_component__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./dashboard/dashboard.component */ 2320);
/* harmony import */ var _login_login_component__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./login/login.component */ 3644);
/* harmony import */ var _link_lawyer_link_lawyer_component__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./link-lawyer/link-lawyer.component */ 3736);
/* harmony import */ var _calendar_calendar_component__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./calendar/calendar.component */ 6158);
/* harmony import */ var _files_files_component__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./files/files.component */ 4776);
/* harmony import */ var _finance_finance_component__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./finance/finance.component */ 2280);
/* harmony import */ var _clients_clients_component__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./clients/clients.component */ 5564);
/* harmony import */ var _activity_log_activity_log_component__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./activity-log/activity-log.component */ 6070);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @angular/core */ 7580);

// Existing components










const routes = [{
  path: '',
  redirectTo: '/secretary-tabs',
  pathMatch: 'full'
}, {
  path: 'login',
  component: _login_login_component__WEBPACK_IMPORTED_MODULE_1__.LoginComponent
},
// Secretary Tab Navigation
{
  path: 'secretary-tabs',
  loadChildren: () => __webpack_require__.e(/*! import() */ "src_app_secretary-tabs_secretary-tabs_module_ts").then(__webpack_require__.bind(__webpack_require__, /*! ./secretary-tabs/secretary-tabs.module */ 6519)).then(m => m.SecretaryTabsPageModule)
},
// Secretary Components (Lazy Loaded)
{
  path: 'secretary-dashboard',
  loadChildren: () => __webpack_require__.e(/*! import() */ "src_app_secretary-dashboard_secretary-dashboard_module_ts").then(__webpack_require__.bind(__webpack_require__, /*! ./secretary-dashboard/secretary-dashboard.module */ 5503)).then(m => m.SecretaryDashboardPageModule)
}, {
  path: 'secretary-calendar',
  loadChildren: () => __webpack_require__.e(/*! import() */ "src_app_secretary-calendar_secretary-calendar_module_ts").then(__webpack_require__.bind(__webpack_require__, /*! ./secretary-calendar/secretary-calendar.module */ 3487)).then(m => m.SecretaryCalendarPageModule)
}, {
  path: 'secretary-cases',
  loadChildren: () => __webpack_require__.e(/*! import() */ "src_app_secretary-cases_secretary-cases_module_ts").then(__webpack_require__.bind(__webpack_require__, /*! ./secretary-cases/secretary-cases.module */ 4963)).then(m => m.SecretaryCasesPageModule)
}, {
  path: 'secretary-files',
  loadChildren: () => __webpack_require__.e(/*! import() */ "src_app_secretary-files_secretary-files_module_ts").then(__webpack_require__.bind(__webpack_require__, /*! ./secretary-files/secretary-files.module */ 6615)).then(m => m.SecretaryFilesPageModule)
}, {
  path: 'secretary-profile',
  loadChildren: () => __webpack_require__.e(/*! import() */ "src_app_secretary-profile_secretary-profile_module_ts").then(__webpack_require__.bind(__webpack_require__, /*! ./secretary-profile/secretary-profile.module */ 3523)).then(m => m.SecretaryProfilePageModule)
},
// Legacy routes (keeping for backward compatibility)
{
  path: 'dashboard',
  component: _dashboard_dashboard_component__WEBPACK_IMPORTED_MODULE_0__.DashboardComponent
}, {
  path: 'lawyer-list',
  component: _link_lawyer_link_lawyer_component__WEBPACK_IMPORTED_MODULE_2__.LinkLawyerComponent
}, {
  path: 'calendar',
  component: _calendar_calendar_component__WEBPACK_IMPORTED_MODULE_3__.CalendarComponent
}, {
  path: 'clients',
  component: _clients_clients_component__WEBPACK_IMPORTED_MODULE_6__.ClientsComponent
}, {
  path: 'finance',
  component: _finance_finance_component__WEBPACK_IMPORTED_MODULE_5__.FinanceComponent
}, {
  path: 'activity-log',
  component: _activity_log_activity_log_component__WEBPACK_IMPORTED_MODULE_7__.ActivityLogComponent
}, {
  path: 'link-lawyer',
  component: _link_lawyer_link_lawyer_component__WEBPACK_IMPORTED_MODULE_2__.LinkLawyerComponent
}, {
  path: 'files',
  component: _files_files_component__WEBPACK_IMPORTED_MODULE_4__.FilesComponent
},
// Fallback
{
  path: '**',
  redirectTo: '/secretary-tabs'
}];
class AppRoutingModule {
  static {
    this.ɵfac = function AppRoutingModule_Factory(t) {
      return new (t || AppRoutingModule)();
    };
  }
  static {
    this.ɵmod = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵdefineNgModule"]({
      type: AppRoutingModule
    });
  }
  static {
    this.ɵinj = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵdefineInjector"]({
      imports: [_angular_router__WEBPACK_IMPORTED_MODULE_9__.RouterModule.forRoot(routes), _angular_router__WEBPACK_IMPORTED_MODULE_9__.RouterModule]
    });
  }
}
(function () {
  (typeof ngJitMode === "undefined" || ngJitMode) && _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵsetNgModuleScope"](AppRoutingModule, {
    imports: [_angular_router__WEBPACK_IMPORTED_MODULE_9__.RouterModule],
    exports: [_angular_router__WEBPACK_IMPORTED_MODULE_9__.RouterModule]
  });
})();

/***/ }),

/***/ 92:
/*!**********************************!*\
  !*** ./src/app/app.component.ts ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AppComponent: () => (/* binding */ AppComponent)
/* harmony export */ });
/* harmony import */ var C_Users_acer_Desktop_angularprojects_veritus_Veritus_veritus_secretary_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js */ 9204);
/* harmony import */ var _angular_cdk_layout__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @angular/cdk/layout */ 7912);
/* harmony import */ var rxjs_operators__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! rxjs/operators */ 271);
/* harmony import */ var rxjs_operators__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! rxjs/operators */ 6301);
/* harmony import */ var _angular_router__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @angular/router */ 5072);
/* harmony import */ var rxjs_operators__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! rxjs/operators */ 1567);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @angular/core */ 7580);
/* harmony import */ var _services_firebase_service__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./services/firebase.service */ 8287);
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @angular/common */ 316);
/* harmony import */ var _ionic_angular__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @ionic/angular */ 7401);
/* harmony import */ var _angular_material_toolbar__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @angular/material/toolbar */ 9552);
/* harmony import */ var _angular_material_button__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @angular/material/button */ 4175);
/* harmony import */ var _angular_material_icon__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @angular/material/icon */ 3840);
/* harmony import */ var _angular_material_sidenav__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @angular/material/sidenav */ 7049);
/* harmony import */ var _angular_material_list__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @angular/material/list */ 943);
/* harmony import */ var _angular_material_menu__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @angular/material/menu */ 1034);

















function AppComponent_div_0_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](0, "div", 4);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelement"](1, "router-outlet");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
  }
}
function AppComponent_mat_sidenav_container_1_button_66_Template(rf, ctx) {
  if (rf & 1) {
    const _r3 = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](0, "button", 34);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵlistener"]("click", function AppComponent_mat_sidenav_container_1_button_66_Template_button_click_0_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵrestoreView"](_r3);
      _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵnextContext"]();
      const drawer_r4 = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵreference"](2);
      return _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵresetView"](drawer_r4.toggle());
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](1, "mat-icon", 35);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](2, "menu");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()();
  }
}
function AppComponent_mat_sidenav_container_1_Template(rf, ctx) {
  if (rf & 1) {
    const _r1 = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](0, "mat-sidenav-container", 5)(1, "mat-sidenav", 6, 0);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵpipe"](3, "async");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵpipe"](4, "async");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵpipe"](5, "async");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](6, "div", 7)(7, "div", 8)(8, "mat-icon", 9);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](9, "balance");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](10, "span", 10);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](11, "Veritus");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()()();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](12, "mat-nav-list", 11)(13, "a", 12)(14, "mat-icon", 13);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](15, "dashboard");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](16, "span", 14);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](17, "Dashboard");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](18, "a", 15)(19, "mat-icon", 13);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](20, "people");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](21, "span", 14);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](22, "Lawyer List");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](23, "a", 16)(24, "mat-icon", 13);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](25, "event");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](26, "span", 14);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](27, "Calendar");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](28, "a", 17)(29, "mat-icon", 13);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](30, "folder");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](31, "span", 14);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](32, "Cases");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](33, "a", 18)(34, "mat-icon", 13);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](35, "person");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](36, "span", 14);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](37, "Clients");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](38, "a", 19)(39, "mat-icon", 13);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](40, "account_balance");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](41, "span", 14);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](42, "Finance");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](43, "a", 20)(44, "mat-icon", 13);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](45, "history");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](46, "span", 14);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](47, "Activity Log");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](48, "a", 21)(49, "mat-icon", 13);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](50, "receipt");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](51, "span", 14);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](52, "Retainers");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](53, "a", 22)(54, "mat-icon", 13);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](55, "settings");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](56, "span", 14);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](57, "Settings");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()()();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](58, "div", 23)(59, "button", 24);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵlistener"]("click", function AppComponent_mat_sidenav_container_1_Template_button_click_59_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵrestoreView"](_r1);
      const ctx_r1 = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵresetView"](ctx_r1.logout());
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](60, "mat-icon", 13);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](61, "logout");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](62, "span", 14);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](63, "Log Out");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()()()();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](64, "mat-sidenav-content")(65, "mat-toolbar", 25);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtemplate"](66, AppComponent_mat_sidenav_container_1_button_66_Template, 3, 0, "button", 26);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵpipe"](67, "async");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelement"](68, "span", 27);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](69, "div", 28)(70, "span", 29);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](71);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](72, "button", 30);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelement"](73, "img", 31);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](74, "mat-menu", null, 1)(76, "button", 32)(77, "mat-icon");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](78, "person");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](79, "span");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](80, "Profile");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](81, "button", 33);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵlistener"]("click", function AppComponent_mat_sidenav_container_1_Template_button_click_81_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵrestoreView"](_r1);
      const ctx_r1 = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵresetView"](ctx_r1.logout());
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](82, "mat-icon");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](83, "logout");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](84, "span");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](85, "Logout");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()()()()();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelement"](86, "router-outlet");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()();
  }
  if (rf & 2) {
    const userMenu_r5 = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵreference"](75);
    const ctx_r1 = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵproperty"]("mode", _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵpipeBind1"](3, 7, ctx_r1.isHandset$) ? "over" : "side")("opened", _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵpipeBind1"](4, 9, ctx_r1.isHandset$) === false);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵattribute"]("role", _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵpipeBind1"](5, 11, ctx_r1.isHandset$) ? "dialog" : "navigation");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](65);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵproperty"]("ngIf", _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵpipeBind1"](67, 13, ctx_r1.isHandset$));
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](5);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtextInterpolate1"]("Hi, ", (ctx_r1.currentUser == null ? null : ctx_r1.currentUser.name) || "Manny P.", "");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵproperty"]("matMenuTriggerFor", userMenu_r5);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵproperty"]("src", (ctx_r1.currentUser == null ? null : ctx_r1.currentUser.photoURL) || "assets/default-avatar.png", _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵsanitizeUrl"]);
  }
}
class AppComponent {
  constructor(breakpointObserver, router, firebaseService) {
    this.breakpointObserver = breakpointObserver;
    this.router = router;
    this.firebaseService = firebaseService;
    this.title = 'veritus-secretary';
    this.currentUser = {
      name: 'Manny P.',
      photoURL: null
    };
    this.isLoginPage = false;
    this.isHandset$ = this.breakpointObserver.observe(_angular_cdk_layout__WEBPACK_IMPORTED_MODULE_3__.Breakpoints.Handset).pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_4__.map)(result => result.matches), (0,rxjs_operators__WEBPACK_IMPORTED_MODULE_5__.shareReplay)());
    // Listen to route changes to determine if we're on login page
    this.router.events.pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_6__.filter)(event => event instanceof _angular_router__WEBPACK_IMPORTED_MODULE_7__.NavigationEnd)).subscribe(event => {
      const navigationEvent = event;
      this.isLoginPage = navigationEvent.url === '/login' || navigationEvent.url.startsWith('/login');
      console.log('Route changed:', navigationEvent.url, 'isLoginPage:', this.isLoginPage);
    });
    // Set initial state
    this.isLoginPage = this.router.url === '/login' || this.router.url.startsWith('/login');
  }
  logout() {
    var _this = this;
    return (0,C_Users_acer_Desktop_angularprojects_veritus_Veritus_veritus_secretary_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__["default"])(function* () {
      try {
        console.log('Logout clicked');
        // Show confirmation
        const confirmed = confirm('Are you sure you want to logout?');
        if (!confirmed) {
          return;
        }
        console.log('Starting logout process...');
        // Sign out from Firebase
        yield _this.firebaseService.signOut();
        console.log('Firebase signOut successful');
        // Clear any local storage
        localStorage.clear();
        console.log('Local storage cleared');
        // Navigate to login page
        yield _this.router.navigate(['/login']);
        console.log('Successfully logged out and redirected to login');
      } catch (error) {
        console.error('Error during logout:', error);
        // Force logout even if Firebase fails
        localStorage.clear();
        console.log('Forced logout - clearing storage and redirecting');
        // Use window.location as fallback
        window.location.href = '/login';
      }
    })();
  }
  static {
    this.ɵfac = function AppComponent_Factory(t) {
      return new (t || AppComponent)(_angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵdirectiveInject"](_angular_cdk_layout__WEBPACK_IMPORTED_MODULE_3__.BreakpointObserver), _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵdirectiveInject"](_angular_router__WEBPACK_IMPORTED_MODULE_7__.Router), _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵdirectiveInject"](_services_firebase_service__WEBPACK_IMPORTED_MODULE_1__.FirebaseService));
    };
  }
  static {
    this.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵdefineComponent"]({
      type: AppComponent,
      selectors: [["app-root"]],
      decls: 2,
      vars: 2,
      consts: [["drawer", ""], ["userMenu", "matMenu"], ["class", "login-layout", 4, "ngIf"], ["class", "sidenav-container", 4, "ngIf"], [1, "login-layout"], [1, "sidenav-container"], ["fixedInViewport", "", 1, "sidenav", 3, "mode", "opened"], [1, "sidebar-header"], [1, "logo-container"], [1, "logo-icon"], [1, "logo-text"], [1, "nav-menu"], ["mat-list-item", "", "routerLink", "/dashboard", "routerLinkActive", "active-nav-item"], ["matListItemIcon", ""], ["matListItemTitle", ""], ["mat-list-item", "", "routerLink", "/lawyer-list", "routerLinkActive", "active-nav-item"], ["mat-list-item", "", "routerLink", "/calendar", "routerLinkActive", "active-nav-item"], ["mat-list-item", "", "routerLink", "/cases", "routerLinkActive", "active-nav-item"], ["mat-list-item", "", "routerLink", "/clients", "routerLinkActive", "active-nav-item"], ["mat-list-item", "", "routerLink", "/finance", "routerLinkActive", "active-nav-item"], ["mat-list-item", "", "routerLink", "/activity-log", "routerLinkActive", "active-nav-item"], ["mat-list-item", "", "routerLink", "/retainers", "routerLinkActive", "active-nav-item"], ["mat-list-item", "", "routerLink", "/settings", "routerLinkActive", "active-nav-item"], [1, "sidebar-footer"], ["mat-list-item", "", 1, "logout-btn", 3, "click"], [1, "top-header"], ["type", "button", "aria-label", "Toggle sidenav", "mat-icon-button", "", 3, "click", 4, "ngIf"], [1, "spacer"], [1, "user-profile"], [1, "user-greeting"], ["mat-icon-button", "", 1, "user-avatar", 3, "matMenuTriggerFor"], ["alt", "User Avatar", 1, "avatar-img", 3, "src"], ["mat-menu-item", "", "routerLink", "/profile"], ["mat-menu-item", "", 3, "click"], ["type", "button", "aria-label", "Toggle sidenav", "mat-icon-button", "", 3, "click"], ["aria-label", "Side nav toggle icon"]],
      template: function AppComponent_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtemplate"](0, AppComponent_div_0_Template, 2, 0, "div", 2)(1, AppComponent_mat_sidenav_container_1_Template, 87, 15, "mat-sidenav-container", 3);
        }
        if (rf & 2) {
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵproperty"]("ngIf", ctx.isLoginPage);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵproperty"]("ngIf", !ctx.isLoginPage);
        }
      },
      dependencies: [_angular_common__WEBPACK_IMPORTED_MODULE_8__.NgIf, _angular_router__WEBPACK_IMPORTED_MODULE_7__.RouterOutlet, _angular_router__WEBPACK_IMPORTED_MODULE_7__.RouterLink, _angular_router__WEBPACK_IMPORTED_MODULE_7__.RouterLinkActive, _ionic_angular__WEBPACK_IMPORTED_MODULE_9__.RouterLinkDelegate, _ionic_angular__WEBPACK_IMPORTED_MODULE_9__.RouterLinkWithHrefDelegate, _angular_material_toolbar__WEBPACK_IMPORTED_MODULE_10__.MatToolbar, _angular_material_button__WEBPACK_IMPORTED_MODULE_11__.MatIconButton, _angular_material_icon__WEBPACK_IMPORTED_MODULE_12__.MatIcon, _angular_material_sidenav__WEBPACK_IMPORTED_MODULE_13__.MatSidenav, _angular_material_sidenav__WEBPACK_IMPORTED_MODULE_13__.MatSidenavContainer, _angular_material_sidenav__WEBPACK_IMPORTED_MODULE_13__.MatSidenavContent, _angular_material_list__WEBPACK_IMPORTED_MODULE_14__.MatNavList, _angular_material_list__WEBPACK_IMPORTED_MODULE_14__.MatListItem, _angular_material_list__WEBPACK_IMPORTED_MODULE_14__.MatListItemIcon, _angular_material_list__WEBPACK_IMPORTED_MODULE_14__.MatListItemTitle, _angular_material_menu__WEBPACK_IMPORTED_MODULE_15__.MatMenu, _angular_material_menu__WEBPACK_IMPORTED_MODULE_15__.MatMenuItem, _angular_material_menu__WEBPACK_IMPORTED_MODULE_15__.MatMenuTrigger, _angular_common__WEBPACK_IMPORTED_MODULE_8__.AsyncPipe],
      styles: [".login-layout[_ngcontent-%COMP%] {\n  height: 100vh;\n  width: 100vw;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.sidenav-container[_ngcontent-%COMP%] {\n  height: 100vh;\n  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);\n}\n\n.sidenav[_ngcontent-%COMP%] {\n  width: 280px;\n  background: #C49A56;\n  color: white;\n  box-shadow: 4px 0 20px rgba(0, 0, 0, 0.1);\n  border-right: none;\n}\n\n.sidebar-header[_ngcontent-%COMP%] {\n  padding: 24px 20px;\n  border-bottom: 1px solid rgba(255, 255, 255, 0.2);\n  background: transparent;\n}\n\n.logo-container[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  gap: 16px;\n  transition: all 0.3s ease;\n}\n\n.logo-container[_ngcontent-%COMP%]:hover {\n  transform: translateY(-2px);\n}\n\n.logo-icon[_ngcontent-%COMP%] {\n  font-size: 36px;\n  width: 36px;\n  height: 36px;\n  color: #C49A56;\n  filter: drop-shadow(0 2px 4px rgba(196, 154, 86, 0.3));\n  transition: all 0.3s ease;\n}\n\n.logo-icon[_ngcontent-%COMP%]:hover {\n  transform: rotate(5deg) scale(1.1);\n  color: #D4AF6A;\n}\n\n.logo-text[_ngcontent-%COMP%] {\n  font-size: 24px;\n  font-weight: 600;\n  color: white;\n  letter-spacing: 0.5px;\n}\n\n.nav-menu[_ngcontent-%COMP%] {\n  padding: 24px 0;\n}\n\n.nav-menu[_ngcontent-%COMP%]   .mat-mdc-list-item[_ngcontent-%COMP%] {\n  color: white;\n  margin: 4px 16px;\n  border-radius: 8px;\n  transition: all 0.3s ease;\n  position: relative;\n  overflow: hidden;\n}\n\n.nav-menu[_ngcontent-%COMP%]   .mat-mdc-list-item[_ngcontent-%COMP%]::before {\n  content: \"\";\n  position: absolute;\n  top: 0;\n  left: -100%;\n  width: 100%;\n  height: 100%;\n  background: linear-gradient(90deg, transparent, rgba(196, 154, 86, 0.1), transparent);\n  transition: left 0.5s ease;\n}\n\n.nav-menu[_ngcontent-%COMP%]   .mat-mdc-list-item[_ngcontent-%COMP%]:hover::before {\n  left: 100%;\n}\n\n.nav-menu[_ngcontent-%COMP%]   .mat-mdc-list-item[_ngcontent-%COMP%]:hover {\n  background: rgba(255, 255, 255, 0.1);\n  color: white;\n  transform: translateX(4px);\n}\n\n.nav-menu[_ngcontent-%COMP%]   .active-nav-item[_ngcontent-%COMP%] {\n  background: rgba(255, 255, 255, 0.2);\n  color: white;\n  transform: translateX(4px);\n}\n\n.nav-menu[_ngcontent-%COMP%]   .active-nav-item[_ngcontent-%COMP%]:hover {\n  background: rgba(255, 255, 255, 0.25);\n  transform: translateX(4px);\n  color: white;\n}\n\n.sidebar-footer[_ngcontent-%COMP%] {\n  position: absolute;\n  bottom: 20px;\n  left: 0;\n  right: 0;\n  padding: 0 16px;\n}\n\n.logout-btn[_ngcontent-%COMP%] {\n  color: rgba(255, 255, 255, 0.8);\n  border-radius: 8px;\n  transition: all 0.3s ease;\n}\n\n.logout-btn[_ngcontent-%COMP%]:hover {\n  background: rgba(255, 255, 255, 0.1);\n  color: white;\n}\n\n.top-header[_ngcontent-%COMP%] {\n  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);\n  color: #1e293b;\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\n  z-index: 100;\n  -webkit-backdrop-filter: blur(20px);\n          backdrop-filter: blur(20px);\n  border-bottom: 1px solid rgba(226, 232, 240, 0.5);\n}\n\n.spacer[_ngcontent-%COMP%] {\n  flex: 1 1 auto;\n}\n\n.user-profile[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  gap: 16px;\n  padding: 8px 16px;\n  border-radius: 12px;\n  transition: all 0.3s ease;\n}\n\n.user-profile[_ngcontent-%COMP%]:hover {\n  background: rgba(226, 232, 240, 0.5);\n  transform: translateY(-1px);\n}\n\n.user-greeting[_ngcontent-%COMP%] {\n  font-size: 15px;\n  color: #475569;\n  font-weight: 500;\n}\n\n.user-avatar[_ngcontent-%COMP%] {\n  width: 44px;\n  height: 44px;\n  border-radius: 50%;\n  overflow: hidden;\n  border: 3px solid #C49A56;\n  transition: all 0.3s ease;\n  box-shadow: 0 2px 8px rgba(196, 154, 86, 0.3);\n}\n\n.user-avatar[_ngcontent-%COMP%]:hover {\n  transform: scale(1.1);\n  box-shadow: 0 4px 16px rgba(196, 154, 86, 0.4);\n}\n\n.avatar-img[_ngcontent-%COMP%] {\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n  border-radius: 50%;\n}\n\nmat-sidenav-content[_ngcontent-%COMP%] {\n  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);\n  min-height: 100vh;\n}\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */"]
    });
  }
}

/***/ }),

/***/ 635:
/*!*******************************!*\
  !*** ./src/app/app.module.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AppModule: () => (/* binding */ AppModule)
/* harmony export */ });
/* harmony import */ var _angular_platform_browser__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @angular/platform-browser */ 436);
/* harmony import */ var _angular_platform_browser_animations__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @angular/platform-browser/animations */ 3835);
/* harmony import */ var _angular_forms__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @angular/forms */ 4456);
/* harmony import */ var _angular_common_http__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @angular/common/http */ 6443);
/* harmony import */ var _ionic_angular__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @ionic/angular */ 7401);
/* harmony import */ var _angular_material_toolbar__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @angular/material/toolbar */ 9552);
/* harmony import */ var _angular_material_button__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @angular/material/button */ 4175);
/* harmony import */ var _angular_material_card__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! @angular/material/card */ 3777);
/* harmony import */ var _angular_material_input__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! @angular/material/input */ 5541);
/* harmony import */ var _angular_material_form_field__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! @angular/material/form-field */ 4950);
/* harmony import */ var _angular_material_icon__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! @angular/material/icon */ 3840);
/* harmony import */ var _angular_material_sidenav__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! @angular/material/sidenav */ 7049);
/* harmony import */ var _angular_material_list__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! @angular/material/list */ 943);
/* harmony import */ var _angular_material_grid_list__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! @angular/material/grid-list */ 6488);
/* harmony import */ var _angular_material_menu__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! @angular/material/menu */ 1034);
/* harmony import */ var _angular_material_table__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! @angular/material/table */ 7697);
/* harmony import */ var _angular_material_paginator__WEBPACK_IMPORTED_MODULE_33__ = __webpack_require__(/*! @angular/material/paginator */ 4624);
/* harmony import */ var _angular_material_sort__WEBPACK_IMPORTED_MODULE_34__ = __webpack_require__(/*! @angular/material/sort */ 2047);
/* harmony import */ var _angular_material_dialog__WEBPACK_IMPORTED_MODULE_35__ = __webpack_require__(/*! @angular/material/dialog */ 2587);
/* harmony import */ var _angular_material_snack_bar__WEBPACK_IMPORTED_MODULE_36__ = __webpack_require__(/*! @angular/material/snack-bar */ 3347);
/* harmony import */ var _angular_material_progress_spinner__WEBPACK_IMPORTED_MODULE_37__ = __webpack_require__(/*! @angular/material/progress-spinner */ 1134);
/* harmony import */ var _angular_material_chips__WEBPACK_IMPORTED_MODULE_38__ = __webpack_require__(/*! @angular/material/chips */ 2772);
/* harmony import */ var _angular_material_select__WEBPACK_IMPORTED_MODULE_39__ = __webpack_require__(/*! @angular/material/select */ 5175);
/* harmony import */ var _angular_material_datepicker__WEBPACK_IMPORTED_MODULE_40__ = __webpack_require__(/*! @angular/material/datepicker */ 1977);
/* harmony import */ var _angular_material_core__WEBPACK_IMPORTED_MODULE_41__ = __webpack_require__(/*! @angular/material/core */ 4646);
/* harmony import */ var _angular_cdk_layout__WEBPACK_IMPORTED_MODULE_42__ = __webpack_require__(/*! @angular/cdk/layout */ 7912);
/* harmony import */ var _angular_fire_app__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @angular/fire/app */ 2945);
/* harmony import */ var _angular_fire_auth__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @angular/fire/auth */ 9082);
/* harmony import */ var _angular_fire_firestore__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @angular/fire/firestore */ 1159);
/* harmony import */ var _angular_fire_functions__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @angular/fire/functions */ 5559);
/* harmony import */ var _angular_fire_storage__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @angular/fire/storage */ 8335);
/* harmony import */ var _app_routing_module__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./app-routing.module */ 4114);
/* harmony import */ var _app_component__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./app.component */ 92);
/* harmony import */ var _environments_environment__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../environments/environment */ 5312);
/* harmony import */ var _dashboard_dashboard_component__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./dashboard/dashboard.component */ 2320);
/* harmony import */ var _login_login_component__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./login/login.component */ 3644);
/* harmony import */ var _link_lawyer_link_lawyer_component__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./link-lawyer/link-lawyer.component */ 3736);
/* harmony import */ var _calendar_calendar_component__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./calendar/calendar.component */ 6158);
/* harmony import */ var _files_files_component__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./files/files.component */ 4776);
/* harmony import */ var _finance_finance_component__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./finance/finance.component */ 2280);
/* harmony import */ var _clients_clients_component__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./clients/clients.component */ 5564);
/* harmony import */ var _activity_log_activity_log_component__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./activity-log/activity-log.component */ 6070);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @angular/core */ 7580);




// Ionic

// Angular Material





















// Firebase








// Components










class AppModule {
  static {
    this.ɵfac = function AppModule_Factory(t) {
      return new (t || AppModule)();
    };
  }
  static {
    this.ɵmod = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵdefineNgModule"]({
      type: AppModule,
      bootstrap: [_app_component__WEBPACK_IMPORTED_MODULE_1__.AppComponent]
    });
  }
  static {
    this.ɵinj = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵdefineInjector"]({
      providers: [(0,_angular_fire_app__WEBPACK_IMPORTED_MODULE_12__.provideFirebaseApp)(() => (0,_angular_fire_app__WEBPACK_IMPORTED_MODULE_12__.initializeApp)(_environments_environment__WEBPACK_IMPORTED_MODULE_2__.environment.firebase)), (0,_angular_fire_auth__WEBPACK_IMPORTED_MODULE_13__.provideAuth)(() => (0,_angular_fire_auth__WEBPACK_IMPORTED_MODULE_13__.getAuth)()), (0,_angular_fire_firestore__WEBPACK_IMPORTED_MODULE_14__.provideFirestore)(() => (0,_angular_fire_firestore__WEBPACK_IMPORTED_MODULE_14__.getFirestore)()), (0,_angular_fire_functions__WEBPACK_IMPORTED_MODULE_15__.provideFunctions)(() => (0,_angular_fire_functions__WEBPACK_IMPORTED_MODULE_15__.getFunctions)()), (0,_angular_fire_storage__WEBPACK_IMPORTED_MODULE_16__.provideStorage)(() => (0,_angular_fire_storage__WEBPACK_IMPORTED_MODULE_16__.getStorage)())],
      imports: [_angular_platform_browser__WEBPACK_IMPORTED_MODULE_17__.BrowserModule, _app_routing_module__WEBPACK_IMPORTED_MODULE_0__.AppRoutingModule, _angular_platform_browser_animations__WEBPACK_IMPORTED_MODULE_18__.BrowserAnimationsModule, _angular_forms__WEBPACK_IMPORTED_MODULE_19__.FormsModule, _angular_forms__WEBPACK_IMPORTED_MODULE_19__.ReactiveFormsModule, _angular_common_http__WEBPACK_IMPORTED_MODULE_20__.HttpClientModule,
      // Ionic
      _ionic_angular__WEBPACK_IMPORTED_MODULE_21__.IonicModule.forRoot(),
      // Angular Material
      _angular_material_toolbar__WEBPACK_IMPORTED_MODULE_22__.MatToolbarModule, _angular_material_button__WEBPACK_IMPORTED_MODULE_23__.MatButtonModule, _angular_material_card__WEBPACK_IMPORTED_MODULE_24__.MatCardModule, _angular_material_input__WEBPACK_IMPORTED_MODULE_25__.MatInputModule, _angular_material_form_field__WEBPACK_IMPORTED_MODULE_26__.MatFormFieldModule, _angular_material_icon__WEBPACK_IMPORTED_MODULE_27__.MatIconModule, _angular_material_sidenav__WEBPACK_IMPORTED_MODULE_28__.MatSidenavModule, _angular_material_list__WEBPACK_IMPORTED_MODULE_29__.MatListModule, _angular_material_grid_list__WEBPACK_IMPORTED_MODULE_30__.MatGridListModule, _angular_material_menu__WEBPACK_IMPORTED_MODULE_31__.MatMenuModule, _angular_material_table__WEBPACK_IMPORTED_MODULE_32__.MatTableModule, _angular_material_paginator__WEBPACK_IMPORTED_MODULE_33__.MatPaginatorModule, _angular_material_sort__WEBPACK_IMPORTED_MODULE_34__.MatSortModule, _angular_material_dialog__WEBPACK_IMPORTED_MODULE_35__.MatDialogModule, _angular_material_snack_bar__WEBPACK_IMPORTED_MODULE_36__.MatSnackBarModule, _angular_material_progress_spinner__WEBPACK_IMPORTED_MODULE_37__.MatProgressSpinnerModule, _angular_material_chips__WEBPACK_IMPORTED_MODULE_38__.MatChipsModule, _angular_material_select__WEBPACK_IMPORTED_MODULE_39__.MatSelectModule, _angular_material_datepicker__WEBPACK_IMPORTED_MODULE_40__.MatDatepickerModule, _angular_material_core__WEBPACK_IMPORTED_MODULE_41__.MatNativeDateModule, _angular_cdk_layout__WEBPACK_IMPORTED_MODULE_42__.LayoutModule]
    });
  }
}
(function () {
  (typeof ngJitMode === "undefined" || ngJitMode) && _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵsetNgModuleScope"](AppModule, {
    declarations: [_app_component__WEBPACK_IMPORTED_MODULE_1__.AppComponent, _dashboard_dashboard_component__WEBPACK_IMPORTED_MODULE_3__.DashboardComponent, _login_login_component__WEBPACK_IMPORTED_MODULE_4__.LoginComponent, _link_lawyer_link_lawyer_component__WEBPACK_IMPORTED_MODULE_5__.LinkLawyerComponent, _calendar_calendar_component__WEBPACK_IMPORTED_MODULE_6__.CalendarComponent, _files_files_component__WEBPACK_IMPORTED_MODULE_7__.FilesComponent, _finance_finance_component__WEBPACK_IMPORTED_MODULE_8__.FinanceComponent, _clients_clients_component__WEBPACK_IMPORTED_MODULE_9__.ClientsComponent, _activity_log_activity_log_component__WEBPACK_IMPORTED_MODULE_10__.ActivityLogComponent],
    imports: [_angular_platform_browser__WEBPACK_IMPORTED_MODULE_17__.BrowserModule, _app_routing_module__WEBPACK_IMPORTED_MODULE_0__.AppRoutingModule, _angular_platform_browser_animations__WEBPACK_IMPORTED_MODULE_18__.BrowserAnimationsModule, _angular_forms__WEBPACK_IMPORTED_MODULE_19__.FormsModule, _angular_forms__WEBPACK_IMPORTED_MODULE_19__.ReactiveFormsModule, _angular_common_http__WEBPACK_IMPORTED_MODULE_20__.HttpClientModule, _ionic_angular__WEBPACK_IMPORTED_MODULE_21__.IonicModule,
    // Angular Material
    _angular_material_toolbar__WEBPACK_IMPORTED_MODULE_22__.MatToolbarModule, _angular_material_button__WEBPACK_IMPORTED_MODULE_23__.MatButtonModule, _angular_material_card__WEBPACK_IMPORTED_MODULE_24__.MatCardModule, _angular_material_input__WEBPACK_IMPORTED_MODULE_25__.MatInputModule, _angular_material_form_field__WEBPACK_IMPORTED_MODULE_26__.MatFormFieldModule, _angular_material_icon__WEBPACK_IMPORTED_MODULE_27__.MatIconModule, _angular_material_sidenav__WEBPACK_IMPORTED_MODULE_28__.MatSidenavModule, _angular_material_list__WEBPACK_IMPORTED_MODULE_29__.MatListModule, _angular_material_grid_list__WEBPACK_IMPORTED_MODULE_30__.MatGridListModule, _angular_material_menu__WEBPACK_IMPORTED_MODULE_31__.MatMenuModule, _angular_material_table__WEBPACK_IMPORTED_MODULE_32__.MatTableModule, _angular_material_paginator__WEBPACK_IMPORTED_MODULE_33__.MatPaginatorModule, _angular_material_sort__WEBPACK_IMPORTED_MODULE_34__.MatSortModule, _angular_material_dialog__WEBPACK_IMPORTED_MODULE_35__.MatDialogModule, _angular_material_snack_bar__WEBPACK_IMPORTED_MODULE_36__.MatSnackBarModule, _angular_material_progress_spinner__WEBPACK_IMPORTED_MODULE_37__.MatProgressSpinnerModule, _angular_material_chips__WEBPACK_IMPORTED_MODULE_38__.MatChipsModule, _angular_material_select__WEBPACK_IMPORTED_MODULE_39__.MatSelectModule, _angular_material_datepicker__WEBPACK_IMPORTED_MODULE_40__.MatDatepickerModule, _angular_material_core__WEBPACK_IMPORTED_MODULE_41__.MatNativeDateModule, _angular_cdk_layout__WEBPACK_IMPORTED_MODULE_42__.LayoutModule]
  });
})();

/***/ }),

/***/ 6158:
/*!************************************************!*\
  !*** ./src/app/calendar/calendar.component.ts ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   CalendarComponent: () => (/* binding */ CalendarComponent)
/* harmony export */ });
/* harmony import */ var C_Users_acer_Desktop_angularprojects_veritus_Veritus_veritus_secretary_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js */ 9204);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @angular/core */ 7580);
/* harmony import */ var _ionic_angular__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @ionic/angular */ 7401);
/* harmony import */ var _services_firebase_service__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../services/firebase.service */ 8287);
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @angular/common */ 316);
/* harmony import */ var _angular_forms__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @angular/forms */ 4456);
/* harmony import */ var _angular_material_button__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @angular/material/button */ 4175);
/* harmony import */ var _angular_material_icon__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @angular/material/icon */ 3840);








function CalendarComponent_div_5_option_4_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](0, "option", 22);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const lawyer_r3 = ctx.$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵproperty"]("ngValue", lawyer_r3);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtextInterpolate2"](" ", lawyer_r3.name, " (", lawyer_r3.rollNumber, ") ");
  }
}
function CalendarComponent_div_5_Template(rf, ctx) {
  if (rf & 1) {
    const _r1 = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](0, "div", 19)(1, "label");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](2, "Managing appointments for:");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](3, "select", 20);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtwoWayListener"]("ngModelChange", function CalendarComponent_div_5_Template_select_ngModelChange_3_listener($event) {
      _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵrestoreView"](_r1);
      const ctx_r1 = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵnextContext"]();
      _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtwoWayBindingSet"](ctx_r1.selectedLawyer, $event) || (ctx_r1.selectedLawyer = $event);
      return _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵresetView"]($event);
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵlistener"]("change", function CalendarComponent_div_5_Template_select_change_3_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵrestoreView"](_r1);
      const ctx_r1 = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵresetView"](ctx_r1.onLawyerChange());
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtemplate"](4, CalendarComponent_div_5_option_4_Template, 2, 3, "option", 21);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()();
  }
  if (rf & 2) {
    const ctx_r1 = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtwoWayProperty"]("ngModel", ctx_r1.selectedLawyer);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵproperty"]("ngForOf", ctx_r1.linkedLawyers);
  }
}
function CalendarComponent_div_27_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](0, "div", 23);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const day_r4 = ctx.$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtextInterpolate"](day_r4);
  }
}
function CalendarComponent_div_29_div_1_Template(rf, ctx) {
  if (rf & 1) {
    const _r5 = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](0, "div", 26);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵlistener"]("click", function CalendarComponent_div_29_div_1_Template_div_click_0_listener() {
      const day_r6 = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵrestoreView"](_r5).$implicit;
      const ctx_r1 = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵnextContext"](2);
      return _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵresetView"](ctx_r1.selectDate(day_r6));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const day_r6 = ctx.$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵclassProp"]("other-month", !day_r6.isCurrentMonth)("today", day_r6.isToday)("selected", day_r6.isSelected);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtextInterpolate1"](" ", day_r6.date, " ");
  }
}
function CalendarComponent_div_29_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](0, "div", 24);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtemplate"](1, CalendarComponent_div_29_div_1_Template, 2, 7, "div", 25);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const week_r7 = ctx.$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵproperty"]("ngForOf", week_r7);
  }
}
function CalendarComponent_div_30_div_5_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](0, "div", 31)(1, "div", 32);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](3, "div", 33)(4, "div", 34);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](5);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](6, "div", 35);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](7);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](8, "div", 36);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](9);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()();
  }
  if (rf & 2) {
    const appointment_r9 = ctx.$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtextInterpolate"](appointment_r9.time);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtextInterpolate"](appointment_r9.type);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtextInterpolate"](appointment_r9.clientName);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵclassMap"](appointment_r9.status);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtextInterpolate1"](" ", appointment_r9.status, " ");
  }
}
function CalendarComponent_div_30_Template(rf, ctx) {
  if (rf & 1) {
    const _r8 = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](0, "div", 27)(1, "h4");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵpipe"](3, "date");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](4, "div", 28);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtemplate"](5, CalendarComponent_div_30_div_5_Template, 10, 6, "div", 29);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](6, "button", 30);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵlistener"]("click", function CalendarComponent_div_30_Template_button_click_6_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵrestoreView"](_r8);
      const ctx_r1 = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵresetView"](ctx_r1.openNewBookingDialog());
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](7, " + New Booking ");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()();
  }
  if (rf & 2) {
    const ctx_r1 = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtextInterpolate1"]("Booking on ", _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵpipeBind2"](3, 2, ctx_r1.selectedDate, "fullDate"), "");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵproperty"]("ngForOf", ctx_r1.getAppointmentsForDate(ctx_r1.selectedDate));
  }
}
function CalendarComponent_div_34_Template(rf, ctx) {
  if (rf & 1) {
    const _r10 = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](0, "div", 37)(1, "div", 38)(2, "div", 39);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵpipe"](4, "date");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](5, "div", 40);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](6);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵpipe"](7, "date");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](8, "div", 41)(9, "div", 42);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](10);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](11, "div", 35);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](12);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](13, "div", 32);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](14);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](15, "div", 43)(16, "button", 10);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵlistener"]("click", function CalendarComponent_div_34_Template_button_click_16_listener() {
      const appointment_r11 = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵrestoreView"](_r10).$implicit;
      const ctx_r1 = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵresetView"](ctx_r1.editAppointment(appointment_r11));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](17, "mat-icon");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](18, "edit");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](19, "button", 44);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵlistener"]("click", function CalendarComponent_div_34_Template_button_click_19_listener() {
      const appointment_r11 = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵrestoreView"](_r10).$implicit;
      const ctx_r1 = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵresetView"](ctx_r1.deleteAppointment(appointment_r11));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](20, "mat-icon");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](21, "delete");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()()()();
  }
  if (rf & 2) {
    const appointment_r11 = ctx.$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtextInterpolate"](_angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵpipeBind2"](4, 5, appointment_r11.date, "dd"));
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtextInterpolate"](_angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵpipeBind2"](7, 8, appointment_r11.date, "MMM"));
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](4);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtextInterpolate"](appointment_r11.type);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtextInterpolate"](appointment_r11.clientName);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtextInterpolate"](appointment_r11.time);
  }
}
class CalendarComponent {
  constructor(modalController, toastController, alertController, firebaseService) {
    this.modalController = modalController;
    this.toastController = toastController;
    this.alertController = alertController;
    this.firebaseService = firebaseService;
    this.currentMonth = 'July 2025';
    this.selectedDate = null;
    this.weekDays = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
    this.calendarWeeks = [];
    // Firebase integration
    this.linkedLawyers = [];
    this.selectedLawyer = null;
    this.isLoading = false;
    this.appointments = [{
      id: '1',
      clientName: 'Kobe Bryant',
      type: 'Consultation',
      time: '10:00',
      date: new Date(2025, 6, 1),
      status: 'confirmed'
    }, {
      id: '2',
      clientName: 'LeBron James',
      type: 'Consultation',
      time: '11:00',
      date: new Date(2025, 6, 1),
      status: 'confirmed'
    }, {
      id: '3',
      clientName: 'Michael Jordan',
      type: 'Case Review',
      time: '14:00',
      date: new Date(2025, 6, 2),
      status: 'pending'
    }];
    this.upcomingAppointments = [];
  }
  ngOnInit() {
    var _this = this;
    return (0,C_Users_acer_Desktop_angularprojects_veritus_Veritus_veritus_secretary_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__["default"])(function* () {
      console.log('Calendar component initialized');
      _this.generateCalendar();
      yield _this.loadLinkedLawyers();
      yield _this.loadAppointments();
      _this.upcomingAppointments = _this.appointments.slice(0, 5);
      console.log('Initial appointments:', _this.appointments);
      console.log('Upcoming appointments:', _this.upcomingAppointments);
    })();
  }
  loadLinkedLawyers() {
    var _this2 = this;
    return (0,C_Users_acer_Desktop_angularprojects_veritus_Veritus_veritus_secretary_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__["default"])(function* () {
      const currentUser = _this2.firebaseService.getCurrentUser();
      if (currentUser) {
        try {
          _this2.linkedLawyers = yield _this2.firebaseService.getSecretaryLinkedLawyers(currentUser.uid);
          if (_this2.linkedLawyers.length > 0) {
            _this2.selectedLawyer = _this2.linkedLawyers[0]; // Select first lawyer by default
          }
          console.log('Linked lawyers loaded:', _this2.linkedLawyers);
        } catch (error) {
          console.error('Error loading linked lawyers:', error);
          _this2.showToast('Error loading linked lawyers', 'danger');
        }
      }
    })();
  }
  loadAppointments() {
    var _this3 = this;
    return (0,C_Users_acer_Desktop_angularprojects_veritus_Veritus_veritus_secretary_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__["default"])(function* () {
      const currentUser = _this3.firebaseService.getCurrentUser();
      if (!currentUser) return;
      _this3.isLoading = true;
      try {
        const firebaseAppointments = yield _this3.firebaseService.getAppointmentsForSecretary(currentUser.uid);
        // Convert Firebase appointments to local format
        _this3.appointments = firebaseAppointments.map(apt => ({
          id: apt.id || '',
          clientName: apt.clientName,
          type: apt.type,
          time: apt.time,
          date: new Date(apt.date),
          status: apt.status
        }));
        console.log('Firebase appointments loaded:', _this3.appointments);
      } catch (error) {
        console.error('Error loading appointments from Firebase:', error);
        _this3.showToast('Error loading appointments', 'danger');
      } finally {
        _this3.isLoading = false;
      }
    })();
  }
  onLawyerChange() {
    console.log('Selected lawyer changed to:', this.selectedLawyer);
    if (this.selectedLawyer) {
      this.showToast(`Now managing appointments for ${this.selectedLawyer.name}`, 'primary');
      // Optionally reload appointments for the selected lawyer
      this.loadAppointments();
    }
  }
  generateCalendar() {
    // Generate calendar grid for July 2025
    const year = 2025;
    const month = 6; // July (0-indexed)
    const firstDay = new Date(year, month, 1);
    const lastDay = new Date(year, month + 1, 0);
    const startDate = new Date(firstDay);
    startDate.setDate(startDate.getDate() - firstDay.getDay() + 1); // Start from Monday
    this.calendarWeeks = [];
    let currentDate = new Date(startDate);
    for (let week = 0; week < 6; week++) {
      const weekDays = [];
      for (let day = 0; day < 7; day++) {
        weekDays.push({
          date: currentDate.getDate(),
          isCurrentMonth: currentDate.getMonth() === month,
          isToday: this.isToday(currentDate),
          isSelected: false,
          fullDate: new Date(currentDate)
        });
        currentDate.setDate(currentDate.getDate() + 1);
      }
      this.calendarWeeks.push(weekDays);
    }
  }
  isToday(date) {
    const today = new Date();
    return date.toDateString() === today.toDateString();
  }
  selectDate(day) {
    console.log('Date selected:', day);
    // Clear previous selection
    this.calendarWeeks.forEach(week => {
      week.forEach(d => d.isSelected = false);
    });
    // Set new selection
    day.isSelected = true;
    this.selectedDate = day.fullDate;
    // Show feedback
    this.showToast(`Selected ${this.selectedDate?.toDateString()}`, 'primary');
    console.log('Selected date:', this.selectedDate);
  }
  getAppointmentsForDate(date) {
    return this.appointments.filter(apt => apt.date.toDateString() === date.toDateString());
  }
  previousMonth() {
    console.log('Previous month clicked');
    this.showToast('Previous month navigation - Coming soon!', 'primary');
  }
  nextMonth() {
    console.log('Next month clicked');
    this.showToast('Next month navigation - Coming soon!', 'primary');
  }
  openNewBookingDialog() {
    var _this4 = this;
    return (0,C_Users_acer_Desktop_angularprojects_veritus_Veritus_veritus_secretary_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__["default"])(function* () {
      console.log('New booking dialog opened');
      if (_this4.linkedLawyers.length === 0) {
        _this4.showToast('No linked lawyers found. Please link a lawyer first.', 'warning');
        return;
      }
      const alert = yield _this4.alertController.create({
        header: 'New Booking Request',
        message: 'Create a new appointment booking (requires lawyer approval)',
        inputs: [{
          name: 'lawyer',
          type: 'radio',
          label: _this4.selectedLawyer?.name || 'Select Lawyer',
          value: _this4.selectedLawyer?.uid || '',
          checked: true
        }, ..._this4.linkedLawyers.slice(1).map(lawyer => ({
          name: 'lawyer',
          type: 'radio',
          label: lawyer.name,
          value: lawyer.uid,
          checked: false
        })), {
          name: 'clientName',
          type: 'text',
          placeholder: 'Client Name'
        }, {
          name: 'appointmentType',
          type: 'text',
          placeholder: 'Appointment Type (e.g., Consultation)'
        }, {
          name: 'time',
          type: 'time',
          placeholder: 'Time'
        }, {
          name: 'remarks',
          type: 'textarea',
          placeholder: 'Additional remarks (optional)'
        }],
        buttons: [{
          text: 'Cancel',
          role: 'cancel'
        }, {
          text: 'Create Booking Request',
          handler: data => {
            _this4.createNewBooking(data);
          }
        }]
      });
      yield alert.present();
    })();
  }
  createNewBooking(data) {
    var _this5 = this;
    return (0,C_Users_acer_Desktop_angularprojects_veritus_Veritus_veritus_secretary_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__["default"])(function* () {
      if (!_this5.selectedDate) {
        _this5.showToast('Please select a date first', 'warning');
        return;
      }
      if (!data.clientName || !data.appointmentType || !data.time || !data.lawyer) {
        _this5.showToast('Please fill in all required fields', 'warning');
        return;
      }
      const currentUser = _this5.firebaseService.getCurrentUser();
      if (!currentUser) {
        _this5.showToast('Please log in to create appointments', 'danger');
        return;
      }
      const selectedLawyer = _this5.linkedLawyers.find(l => l.uid === data.lawyer);
      if (!selectedLawyer) {
        _this5.showToast('Selected lawyer not found', 'danger');
        return;
      }
      _this5.isLoading = true;
      try {
        const appointmentData = {
          lawyerId: selectedLawyer.uid,
          lawyerName: selectedLawyer.name,
          clientName: data.clientName,
          date: _this5.selectedDate.toISOString().split('T')[0],
          time: data.time,
          status: 'pending',
          type: data.appointmentType,
          createdBy: 'secretary',
          managedBy: currentUser.uid,
          lastModifiedBy: currentUser.uid,
          lastModifiedByRole: 'secretary',
          remarks: data.remarks || '',
          isUrgent: false,
          createdAt: new Date(),
          updatedAt: new Date()
        };
        const appointmentId = yield _this5.firebaseService.createAppointment(appointmentData);
        // Add to local array for immediate UI update
        const newAppointment = {
          id: appointmentId,
          clientName: data.clientName,
          type: data.appointmentType,
          time: data.time,
          date: new Date(_this5.selectedDate),
          status: 'pending'
        };
        _this5.appointments.push(newAppointment);
        _this5.upcomingAppointments = _this5.appointments.slice(0, 5);
        _this5.showToast(`Appointment request created for ${data.clientName} with ${selectedLawyer.name}. Awaiting lawyer approval.`, 'success');
        console.log('New appointment created in Firebase:', appointmentData);
      } catch (error) {
        console.error('Error creating appointment:', error);
        _this5.showToast('Error creating appointment. Please try again.', 'danger');
      } finally {
        _this5.isLoading = false;
      }
    })();
  }
  editAppointment(appointment) {
    var _this6 = this;
    return (0,C_Users_acer_Desktop_angularprojects_veritus_Veritus_veritus_secretary_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__["default"])(function* () {
      console.log('Edit appointment:', appointment);
      const alert = yield _this6.alertController.create({
        header: 'Edit Appointment',
        message: 'Update appointment details',
        inputs: [{
          name: 'clientName',
          type: 'text',
          value: appointment.clientName,
          placeholder: 'Client Name'
        }, {
          name: 'appointmentType',
          type: 'text',
          value: appointment.type,
          placeholder: 'Appointment Type'
        }, {
          name: 'time',
          type: 'time',
          value: appointment.time,
          placeholder: 'Time'
        }],
        buttons: [{
          text: 'Cancel',
          role: 'cancel'
        }, {
          text: 'Update',
          handler: data => {
            _this6.updateAppointment(appointment.id, data);
          }
        }]
      });
      yield alert.present();
    })();
  }
  updateAppointment(appointmentId, data) {
    var _this7 = this;
    return (0,C_Users_acer_Desktop_angularprojects_veritus_Veritus_veritus_secretary_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__["default"])(function* () {
      const currentUser = _this7.firebaseService.getCurrentUser();
      if (!currentUser) {
        _this7.showToast('Please log in to update appointments', 'danger');
        return;
      }
      _this7.isLoading = true;
      try {
        const updates = {
          clientName: data.clientName,
          type: data.appointmentType,
          time: data.time,
          lastModifiedBy: currentUser.uid,
          lastModifiedByRole: 'secretary',
          updatedAt: new Date()
        };
        yield _this7.firebaseService.updateAppointment(appointmentId, updates);
        // Update local array
        const index = _this7.appointments.findIndex(apt => apt.id === appointmentId);
        if (index !== -1) {
          _this7.appointments[index] = {
            ..._this7.appointments[index],
            clientName: data.clientName,
            type: data.appointmentType,
            time: data.time
          };
          _this7.upcomingAppointments = _this7.appointments.slice(0, 5);
        }
        _this7.showToast('Appointment updated successfully', 'success');
      } catch (error) {
        console.error('Error updating appointment:', error);
        _this7.showToast('Error updating appointment. Please try again.', 'danger');
      } finally {
        _this7.isLoading = false;
      }
    })();
  }
  deleteAppointment(appointment) {
    var _this8 = this;
    return (0,C_Users_acer_Desktop_angularprojects_veritus_Veritus_veritus_secretary_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__["default"])(function* () {
      console.log('Delete appointment:', appointment);
      const alert = yield _this8.alertController.create({
        header: 'Delete Appointment',
        message: `Are you sure you want to delete the appointment with ${appointment.clientName}?`,
        buttons: [{
          text: 'Cancel',
          role: 'cancel'
        }, {
          text: 'Delete',
          role: 'destructive',
          handler: () => {
            _this8.confirmDeleteAppointment(appointment.id);
          }
        }]
      });
      yield alert.present();
    })();
  }
  confirmDeleteAppointment(appointmentId) {
    var _this9 = this;
    return (0,C_Users_acer_Desktop_angularprojects_veritus_Veritus_veritus_secretary_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__["default"])(function* () {
      const currentUser = _this9.firebaseService.getCurrentUser();
      if (!currentUser) {
        _this9.showToast('Please log in to delete appointments', 'danger');
        return;
      }
      _this9.isLoading = true;
      try {
        yield _this9.firebaseService.deleteAppointment(appointmentId, currentUser.uid, 'secretary');
        // Remove from local array
        const index = _this9.appointments.findIndex(apt => apt.id === appointmentId);
        if (index !== -1) {
          const deletedAppointment = _this9.appointments.splice(index, 1)[0];
          _this9.upcomingAppointments = _this9.appointments.slice(0, 5);
          _this9.showToast(`Appointment with ${deletedAppointment.clientName} deleted`, 'success');
        }
      } catch (error) {
        console.error('Error deleting appointment:', error);
        _this9.showToast('Error deleting appointment. Please try again.', 'danger');
      } finally {
        _this9.isLoading = false;
      }
    })();
  }
  showToast(_x) {
    var _this0 = this;
    return (0,C_Users_acer_Desktop_angularprojects_veritus_Veritus_veritus_secretary_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__["default"])(function* (message, color = 'primary') {
      const toast = yield _this0.toastController.create({
        message: message,
        duration: 3000,
        color: color,
        position: 'bottom'
      });
      toast.present();
    }).apply(this, arguments);
  }
  static {
    this.ɵfac = function CalendarComponent_Factory(t) {
      return new (t || CalendarComponent)(_angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵdirectiveInject"](_ionic_angular__WEBPACK_IMPORTED_MODULE_3__.ModalController), _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵdirectiveInject"](_ionic_angular__WEBPACK_IMPORTED_MODULE_3__.ToastController), _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵdirectiveInject"](_ionic_angular__WEBPACK_IMPORTED_MODULE_3__.AlertController), _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵdirectiveInject"](_services_firebase_service__WEBPACK_IMPORTED_MODULE_1__.FirebaseService));
    };
  }
  static {
    this.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵdefineComponent"]({
      type: CalendarComponent,
      selectors: [["app-calendar"]],
      decls: 35,
      vars: 8,
      consts: [[1, "calendar-container"], [1, "calendar-header"], [1, "header-left"], ["class", "lawyer-selector", 4, "ngIf"], [1, "header-right"], [1, "workflow-info"], ["mat-raised-button", "", "color", "primary", 1, "header-new-booking-btn", 3, "click", "disabled"], [1, "calendar-content"], [1, "calendar-widget"], [1, "calendar-nav"], ["mat-icon-button", "", 3, "click"], [1, "calendar-grid"], [1, "calendar-header-row"], ["class", "day-header", 4, "ngFor", "ngForOf"], [1, "calendar-body"], ["class", "calendar-row", 4, "ngFor", "ngForOf"], ["class", "selected-date-section", 4, "ngIf"], [1, "upcoming-appointments"], ["class", "appointment-card", 4, "ngFor", "ngForOf"], [1, "lawyer-selector"], [1, "lawyer-select", 3, "ngModelChange", "change", "ngModel"], [3, "ngValue", 4, "ngFor", "ngForOf"], [3, "ngValue"], [1, "day-header"], [1, "calendar-row"], ["class", "calendar-day", 3, "other-month", "today", "selected", "click", 4, "ngFor", "ngForOf"], [1, "calendar-day", 3, "click"], [1, "selected-date-section"], [1, "appointments-list"], ["class", "appointment-item", 4, "ngFor", "ngForOf"], ["mat-raised-button", "", "color", "primary", 1, "new-booking-btn", 3, "click"], [1, "appointment-item"], [1, "appointment-time"], [1, "appointment-details"], [1, "appointment-type"], [1, "appointment-client"], [1, "appointment-status"], [1, "appointment-card"], [1, "appointment-date"], [1, "date-day"], [1, "date-month"], [1, "appointment-info"], [1, "appointment-title"], [1, "appointment-actions"], ["mat-icon-button", "", "color", "warn", 3, "click"]],
      template: function CalendarComponent_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](0, "div", 0)(1, "div", 1)(2, "div", 2)(3, "h1");
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](4, "Scheduling");
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtemplate"](5, CalendarComponent_div_5_Template, 5, 2, "div", 3);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](6, "div", 4)(7, "div", 5)(8, "small");
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](9, "\uD83D\uDCCB Secretary creates \u2192 \uD83D\uDD04 Lawyer approves \u2192 \u2705 Confirmed");
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](10, "button", 6);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵlistener"]("click", function CalendarComponent_Template_button_click_10_listener() {
            return ctx.openNewBookingDialog();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](11, "mat-icon");
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](12, "add");
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](13);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](14, "div", 7)(15, "div", 8)(16, "div", 9)(17, "button", 10);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵlistener"]("click", function CalendarComponent_Template_button_click_17_listener() {
            return ctx.previousMonth();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](18, "mat-icon");
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](19, "chevron_left");
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](20, "h3");
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](21);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](22, "button", 10);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵlistener"]("click", function CalendarComponent_Template_button_click_22_listener() {
            return ctx.nextMonth();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](23, "mat-icon");
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](24, "chevron_right");
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](25, "div", 11)(26, "div", 12);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtemplate"](27, CalendarComponent_div_27_Template, 2, 1, "div", 13);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](28, "div", 14);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtemplate"](29, CalendarComponent_div_29_Template, 2, 1, "div", 15);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtemplate"](30, CalendarComponent_div_30_Template, 8, 5, "div", 16);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](31, "div", 17)(32, "h3");
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](33, "Upcoming Appointments");
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtemplate"](34, CalendarComponent_div_34_Template, 22, 11, "div", 18);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()()();
        }
        if (rf & 2) {
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](5);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵproperty"]("ngIf", ctx.linkedLawyers.length > 0);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](5);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵproperty"]("disabled", ctx.isLoading);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](3);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtextInterpolate1"](" ", ctx.isLoading ? "Creating..." : "New Booking Request", " ");
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](8);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtextInterpolate"](ctx.currentMonth);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](6);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵproperty"]("ngForOf", ctx.weekDays);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](2);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵproperty"]("ngForOf", ctx.calendarWeeks);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵproperty"]("ngIf", ctx.selectedDate);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](4);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵproperty"]("ngForOf", ctx.upcomingAppointments);
        }
      },
      dependencies: [_angular_common__WEBPACK_IMPORTED_MODULE_4__.NgForOf, _angular_common__WEBPACK_IMPORTED_MODULE_4__.NgIf, _angular_forms__WEBPACK_IMPORTED_MODULE_5__.NgSelectOption, _angular_forms__WEBPACK_IMPORTED_MODULE_5__["ɵNgSelectMultipleOption"], _angular_forms__WEBPACK_IMPORTED_MODULE_5__.SelectControlValueAccessor, _angular_forms__WEBPACK_IMPORTED_MODULE_5__.NgControlStatus, _angular_forms__WEBPACK_IMPORTED_MODULE_5__.NgModel, _angular_material_button__WEBPACK_IMPORTED_MODULE_6__.MatButton, _angular_material_button__WEBPACK_IMPORTED_MODULE_6__.MatIconButton, _angular_material_icon__WEBPACK_IMPORTED_MODULE_7__.MatIcon, _angular_common__WEBPACK_IMPORTED_MODULE_4__.DatePipe],
      styles: [".calendar-container[_ngcontent-%COMP%] {\n  padding: 32px;\n  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);\n  min-height: calc(100vh - 64px);\n  position: relative;\n}\n\n.calendar-container[_ngcontent-%COMP%]::before {\n  content: \"\";\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: radial-gradient(circle at 30% 30%, rgba(196, 154, 86, 0.08) 0%, transparent 50%), radial-gradient(circle at 70% 70%, rgba(30, 41, 59, 0.03) 0%, transparent 50%);\n  pointer-events: none;\n}\n\n.calendar-header[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: space-between;\n  align-items: flex-start;\n  margin-bottom: 32px;\n  position: relative;\n  z-index: 1;\n  gap: 20px;\n}\n\n.header-left[_ngcontent-%COMP%] {\n  flex: 1;\n}\n\n.header-right[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  align-items: flex-end;\n  gap: 8px;\n}\n\n.lawyer-selector[_ngcontent-%COMP%] {\n  margin-top: 12px;\n  display: flex;\n  flex-direction: column;\n  gap: 8px;\n}\n\n.lawyer-selector[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\n  font-size: 14px;\n  color: #64748b;\n  font-weight: 500;\n}\n\n.lawyer-select[_ngcontent-%COMP%] {\n  padding: 8px 12px;\n  border: 2px solid #e2e8f0;\n  border-radius: 8px;\n  background: white;\n  font-size: 14px;\n  color: #1e293b;\n  min-width: 250px;\n  transition: all 0.3s ease;\n}\n\n.lawyer-select[_ngcontent-%COMP%]:focus {\n  outline: none;\n  border-color: #C49A56;\n  box-shadow: 0 0 0 3px rgba(196, 154, 86, 0.1);\n}\n\n.workflow-info[_ngcontent-%COMP%] {\n  font-size: 12px;\n  color: #64748b;\n  background: rgba(255, 255, 255, 0.8);\n  padding: 6px 12px;\n  border-radius: 6px;\n  border: 1px solid #e2e8f0;\n  white-space: nowrap;\n}\n\n.calendar-header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\n  margin: 0;\n  font-size: 36px;\n  font-weight: 700;\n  color: #1e293b;\n  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n  position: relative;\n}\n\n.calendar-header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]::after {\n  content: \"\";\n  position: absolute;\n  bottom: -8px;\n  left: 0;\n  width: 80px;\n  height: 4px;\n  background: linear-gradient(90deg, #C49A56 0%, #D4AF6A 100%);\n  border-radius: 2px;\n}\n\n.calendar-content[_ngcontent-%COMP%] {\n  display: grid;\n  grid-template-columns: 1fr 300px;\n  gap: 24px;\n}\n\n.calendar-widget[_ngcontent-%COMP%] {\n  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);\n  border-radius: 24px;\n  padding: 32px;\n  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1), 0 8px 24px rgba(0, 0, 0, 0.05);\n  border: 1px solid rgba(226, 232, 240, 0.8);\n  position: relative;\n  overflow: hidden;\n  transition: all 0.3s ease;\n}\n\n.calendar-widget[_ngcontent-%COMP%]::before {\n  content: \"\";\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  height: 6px;\n  background: linear-gradient(90deg, #C49A56 0%, #D4AF6A 50%, #C49A56 100%);\n}\n\n.calendar-widget[_ngcontent-%COMP%]:hover {\n  transform: translateY(-4px);\n  box-shadow: 0 30px 80px rgba(0, 0, 0, 0.15), 0 12px 32px rgba(0, 0, 0, 0.08);\n}\n\n.calendar-nav[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 20px;\n}\n\n.calendar-nav[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\n  margin: 0;\n  font-size: 18px;\n  font-weight: 600;\n}\n\n.calendar-grid[_ngcontent-%COMP%] {\n  border: 1px solid #e0e0e0;\n  border-radius: 8px;\n  overflow: hidden;\n}\n\n.calendar-header-row[_ngcontent-%COMP%] {\n  display: grid;\n  grid-template-columns: repeat(7, 1fr);\n  background: #f8f9fa;\n}\n\n.day-header[_ngcontent-%COMP%] {\n  padding: 12px;\n  text-align: center;\n  font-weight: 600;\n  font-size: 12px;\n  color: #666;\n  border-right: 1px solid #e0e0e0;\n}\n\n.calendar-body[_ngcontent-%COMP%] {\n  background: white;\n}\n\n.calendar-row[_ngcontent-%COMP%] {\n  display: grid;\n  grid-template-columns: repeat(7, 1fr);\n  border-bottom: 1px solid #e0e0e0;\n}\n\n.calendar-day[_ngcontent-%COMP%] {\n  padding: 12px;\n  text-align: center;\n  cursor: pointer;\n  border-right: 1px solid #e0e0e0;\n  transition: all 0.2s ease;\n  min-height: 40px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.calendar-day[_ngcontent-%COMP%]:hover {\n  background: #f0f0f0;\n}\n\n.calendar-day.other-month[_ngcontent-%COMP%] {\n  color: #ccc;\n}\n\n.calendar-day.today[_ngcontent-%COMP%] {\n  background: #e3f2fd;\n  color: #1976d2;\n  font-weight: 600;\n}\n\n.calendar-day.selected[_ngcontent-%COMP%] {\n  background: #1976d2;\n  color: white;\n}\n\n.selected-date-section[_ngcontent-%COMP%] {\n  margin-top: 24px;\n  padding-top: 24px;\n  border-top: 1px solid #e0e0e0;\n}\n\n.selected-date-section[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\n  margin: 0 0 16px 0;\n  font-size: 16px;\n  font-weight: 600;\n}\n\n.appointment-item[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  gap: 12px;\n  padding: 12px;\n  background: #f8f9fa;\n  border-radius: 8px;\n  margin-bottom: 8px;\n}\n\n.appointment-time[_ngcontent-%COMP%] {\n  font-weight: 600;\n  color: #1976d2;\n  min-width: 60px;\n}\n\n.appointment-details[_ngcontent-%COMP%] {\n  flex: 1;\n}\n\n.appointment-type[_ngcontent-%COMP%] {\n  font-weight: 500;\n  margin-bottom: 2px;\n}\n\n.appointment-client[_ngcontent-%COMP%] {\n  font-size: 12px;\n  color: #666;\n}\n\n.appointment-status[_ngcontent-%COMP%] {\n  padding: 4px 8px;\n  border-radius: 4px;\n  font-size: 11px;\n  font-weight: 500;\n  text-transform: uppercase;\n}\n\n.appointment-status.confirmed[_ngcontent-%COMP%] {\n  background: #e8f5e8;\n  color: #2e7d32;\n}\n\n.appointment-status.pending[_ngcontent-%COMP%] {\n  background: #fff3e0;\n  color: #f57c00;\n}\n\n.new-booking-btn[_ngcontent-%COMP%] {\n  width: 100%;\n  margin-top: 16px;\n  background: linear-gradient(45deg, #C49A56, #D4AF6A) !important;\n  color: white !important;\n  font-weight: 600;\n  transition: all 0.3s ease;\n}\n\n.new-booking-btn[_ngcontent-%COMP%]:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 4px 12px rgba(196, 154, 86, 0.3);\n}\n\n.header-new-booking-btn[_ngcontent-%COMP%] {\n  background: linear-gradient(45deg, #C49A56, #D4AF6A) !important;\n  color: white !important;\n  font-weight: 600;\n  padding: 12px 24px !important;\n  border-radius: 8px !important;\n  transition: all 0.3s ease;\n  box-shadow: 0 4px 12px rgba(196, 154, 86, 0.2);\n}\n\n.header-new-booking-btn[_ngcontent-%COMP%]:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 8px 20px rgba(196, 154, 86, 0.3);\n}\n\n.upcoming-appointments[_ngcontent-%COMP%] {\n  background: white;\n  border-radius: 12px;\n  padding: 24px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n  height: -moz-fit-content;\n  height: fit-content;\n}\n\n.upcoming-appointments[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\n  margin: 0 0 20px 0;\n  font-size: 18px;\n  font-weight: 600;\n}\n\n.appointment-card[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  gap: 16px;\n  padding: 16px;\n  border: 1px solid #e0e0e0;\n  border-radius: 8px;\n  margin-bottom: 12px;\n  transition: all 0.2s ease;\n}\n\n.appointment-card[_ngcontent-%COMP%]:hover {\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n}\n\n.appointment-date[_ngcontent-%COMP%] {\n  text-align: center;\n  min-width: 50px;\n}\n\n.date-day[_ngcontent-%COMP%] {\n  font-size: 20px;\n  font-weight: 600;\n  color: #1976d2;\n}\n\n.date-month[_ngcontent-%COMP%] {\n  font-size: 12px;\n  color: #666;\n  text-transform: uppercase;\n}\n\n.appointment-info[_ngcontent-%COMP%] {\n  flex: 1;\n}\n\n.appointment-title[_ngcontent-%COMP%] {\n  font-weight: 500;\n  margin-bottom: 4px;\n}\n\n.appointment-actions[_ngcontent-%COMP%] {\n  display: flex;\n  gap: 4px;\n}\n\n@media (max-width: 768px) {\n  .calendar-content[_ngcontent-%COMP%] {\n    grid-template-columns: 1fr;\n  }\n}\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */"]
    });
  }
}

/***/ }),

/***/ 5564:
/*!**********************************************!*\
  !*** ./src/app/clients/clients.component.ts ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ClientsComponent: () => (/* binding */ ClientsComponent)
/* harmony export */ });
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @angular/core */ 7580);
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @angular/common */ 316);
/* harmony import */ var _angular_forms__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @angular/forms */ 4456);
/* harmony import */ var _angular_material_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @angular/material/button */ 4175);
/* harmony import */ var _angular_material_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @angular/material/input */ 5541);
/* harmony import */ var _angular_material_form_field__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @angular/material/form-field */ 4950);
/* harmony import */ var _angular_material_icon__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @angular/material/icon */ 3840);
/* harmony import */ var _angular_material_menu__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @angular/material/menu */ 1034);
/* harmony import */ var _angular_material_select__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @angular/material/select */ 5175);
/* harmony import */ var _angular_material_core__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @angular/material/core */ 4646);










function ClientsComponent_div_58_Template(rf, ctx) {
  if (rf & 1) {
    const _r1 = _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](0, "div", 27)(1, "div", 28)(2, "div", 29)(3, "mat-icon");
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](4, "person");
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](5, "div", 30);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelement"](6, "div", 31);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](7, "span");
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](8);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵpipe"](9, "titlecase");
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]()()();
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](10, "div", 32)(11, "h4", 33);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](12);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](13, "div", 34)(14, "div", 35)(15, "mat-icon");
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](16, "email");
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](17, "span");
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](18);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](19, "div", 35)(20, "mat-icon");
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](21, "phone");
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](22, "span");
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](23);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]()()()();
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](24, "div", 36)(25, "div", 37)(26, "span", 38);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](27, "Retainer:");
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](28, "span", 39);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](29);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵpipe"](30, "number");
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](31, "div", 37)(32, "span", 38);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](33, "Cases:");
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](34, "span", 39);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](35);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](36, "div", 37)(37, "span", 38);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](38, "Joined:");
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](39, "span", 39);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](40);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵpipe"](41, "date");
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](42, "div", 37)(43, "span", 38);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](44, "Last Contact:");
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](45, "span", 39);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](46);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]()()();
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](47, "div", 40)(48, "button", 41);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵlistener"]("click", function ClientsComponent_div_58_Template_button_click_48_listener() {
      const client_r2 = _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵrestoreView"](_r1).$implicit;
      const ctx_r2 = _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵresetView"](ctx_r2.viewClient(client_r2));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](49, "mat-icon");
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](50, "visibility");
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](51, "button", 41);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵlistener"]("click", function ClientsComponent_div_58_Template_button_click_51_listener() {
      const client_r2 = _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵrestoreView"](_r1).$implicit;
      const ctx_r2 = _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵresetView"](ctx_r2.editClient(client_r2));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](52, "mat-icon");
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](53, "edit");
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](54, "button", 41);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵlistener"]("click", function ClientsComponent_div_58_Template_button_click_54_listener() {
      const client_r2 = _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵrestoreView"](_r1).$implicit;
      const ctx_r2 = _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵresetView"](ctx_r2.contactClient(client_r2));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](55, "mat-icon");
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](56, "message");
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](57, "button", 42)(58, "mat-icon");
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](59, "more_vert");
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](60, "mat-menu", null, 0)(62, "button", 43);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵlistener"]("click", function ClientsComponent_div_58_Template_button_click_62_listener() {
      const client_r2 = _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵrestoreView"](_r1).$implicit;
      const ctx_r2 = _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵresetView"](ctx_r2.viewCases(client_r2));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](63, "mat-icon");
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](64, "folder");
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](65, "span");
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](66, "View Cases");
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](67, "button", 43);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵlistener"]("click", function ClientsComponent_div_58_Template_button_click_67_listener() {
      const client_r2 = _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵrestoreView"](_r1).$implicit;
      const ctx_r2 = _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵresetView"](ctx_r2.viewTransactions(client_r2));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](68, "mat-icon");
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](69, "receipt");
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](70, "span");
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](71, "View Transactions");
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](72, "button", 43);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵlistener"]("click", function ClientsComponent_div_58_Template_button_click_72_listener() {
      const client_r2 = _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵrestoreView"](_r1).$implicit;
      const ctx_r2 = _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵresetView"](ctx_r2.deactivateClient(client_r2));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](73, "mat-icon");
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](74, "block");
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](75, "span");
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](76, "Deactivate");
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]()()()()();
  }
  if (rf & 2) {
    const client_r2 = ctx.$implicit;
    const clientMenu_r4 = _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵreference"](61);
    const ctx_r2 = _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵadvance"](5);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵclassMap"](client_r2.status);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵadvance"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtextInterpolate"](_angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵpipeBind1"](9, 11, client_r2.status));
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵadvance"](4);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtextInterpolate"](client_r2.name);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵadvance"](6);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtextInterpolate"](client_r2.email);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵadvance"](5);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtextInterpolate"](client_r2.phone);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵadvance"](6);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtextInterpolate1"]("\u20B1", _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵpipeBind1"](30, 13, client_r2.retainerAmount), "");
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵadvance"](6);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtextInterpolate"](client_r2.caseCount);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵadvance"](5);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtextInterpolate"](_angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵpipeBind2"](41, 15, client_r2.joinDate, "MMM yyyy"));
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵadvance"](6);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtextInterpolate"](ctx_r2.getTimeAgo(client_r2.lastContact));
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵadvance"](11);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵproperty"]("matMenuTriggerFor", clientMenu_r4);
  }
}
class ClientsComponent {
  constructor() {
    this.searchTerm = '';
    this.statusFilter = '';
    this.clients = [{
      id: '1',
      name: 'Amado Cruz',
      email: '<EMAIL>',
      phone: '+63 ************',
      status: 'active',
      retainerAmount: 50000,
      joinDate: new Date(2024, 8, 15),
      lastContact: new Date(2025, 6, 10),
      caseCount: 3
    }, {
      id: '2',
      name: 'Clara Mendoza',
      email: '<EMAIL>',
      phone: '+63 ************',
      status: 'active',
      retainerAmount: 75000,
      joinDate: new Date(2024, 10, 20),
      lastContact: new Date(2025, 6, 8),
      caseCount: 2
    }, {
      id: '3',
      name: 'Kobe Bryant',
      email: '<EMAIL>',
      phone: '+63 ************',
      status: 'active',
      retainerAmount: 100000,
      joinDate: new Date(2024, 11, 5),
      lastContact: new Date(2025, 6, 12),
      caseCount: 1
    }, {
      id: '4',
      name: 'Maria Santos',
      email: '<EMAIL>',
      phone: '+63 ************',
      status: 'pending',
      retainerAmount: 60000,
      joinDate: new Date(2025, 6, 1),
      lastContact: new Date(2025, 6, 1),
      caseCount: 0
    }];
    this.filteredClients = [];
  }
  ngOnInit() {
    this.filteredClients = [...this.clients];
  }
  getClientCount(status) {
    return this.clients.filter(client => client.status === status).length;
  }
  getTotalRetainer() {
    return this.clients.filter(client => client.status === 'active').reduce((total, client) => total + client.retainerAmount, 0);
  }
  filterClients() {
    let filtered = [...this.clients];
    // Filter by search term
    if (this.searchTerm) {
      filtered = filtered.filter(client => client.name.toLowerCase().includes(this.searchTerm.toLowerCase()) || client.email.toLowerCase().includes(this.searchTerm.toLowerCase()));
    }
    // Filter by status
    if (this.statusFilter) {
      filtered = filtered.filter(client => client.status === this.statusFilter);
    }
    this.filteredClients = filtered;
  }
  getTimeAgo(date) {
    const now = new Date();
    const diffTime = Math.abs(now.getTime() - date.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    if (diffDays === 1) {
      return 'Today';
    } else if (diffDays <= 7) {
      return `${diffDays} days ago`;
    } else {
      return date.toLocaleDateString();
    }
  }
  openAddClientDialog() {
    // Implement add client dialog
  }
  viewClient(client) {
    // Implement view client details
  }
  editClient(client) {
    // Implement edit client
  }
  contactClient(client) {
    // Implement contact client
  }
  viewCases(client) {
    // Implement view client cases
  }
  viewTransactions(client) {
    // Implement view client transactions
  }
  deactivateClient(client) {
    // Implement deactivate client
  }
  static {
    this.ɵfac = function ClientsComponent_Factory(t) {
      return new (t || ClientsComponent)();
    };
  }
  static {
    this.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵdefineComponent"]({
      type: ClientsComponent,
      selectors: [["app-clients"]],
      decls: 59,
      vars: 8,
      consts: [["clientMenu", "matMenu"], [1, "clients-container"], [1, "clients-header"], ["mat-raised-button", "", "color", "primary", 3, "click"], [1, "clients-content"], [1, "stats-section"], [1, "stat-card"], [1, "stat-icon", "active"], [1, "stat-content"], [1, "stat-value"], [1, "stat-label"], [1, "stat-icon", "pending"], [1, "stat-icon", "revenue"], [1, "clients-list-section"], [1, "list-header"], [1, "list-controls"], ["appearance", "outline", 1, "search-field"], ["matPrefix", ""], ["matInput", "", "placeholder", "Search clients", 3, "ngModelChange", "input", "ngModel"], ["appearance", "outline", 1, "filter-field"], [3, "valueChange", "selectionChange", "value"], ["value", ""], ["value", "active"], ["value", "pending"], ["value", "inactive"], [1, "clients-grid"], ["class", "client-card", 4, "ngFor", "ngForOf"], [1, "client-card"], [1, "client-header"], [1, "client-avatar"], [1, "client-status"], [1, "status-dot"], [1, "client-info"], [1, "client-name"], [1, "client-contact"], [1, "contact-item"], [1, "client-details"], [1, "detail-row"], [1, "detail-label"], [1, "detail-value"], [1, "client-actions"], ["mat-icon-button", "", 3, "click"], ["mat-icon-button", "", 3, "matMenuTriggerFor"], ["mat-menu-item", "", 3, "click"]],
      template: function ClientsComponent_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](0, "div", 1)(1, "div", 2)(2, "h1");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](3, "Retainer Clients");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](4, "button", 3);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵlistener"]("click", function ClientsComponent_Template_button_click_4_listener() {
            return ctx.openAddClientDialog();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](5, "mat-icon");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](6, "add");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](7, " Add Client ");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](8, "div", 4)(9, "div", 5)(10, "div", 6)(11, "div", 7)(12, "mat-icon");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](13, "person");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](14, "div", 8)(15, "div", 9);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](16);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](17, "div", 10);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](18, "Active Clients");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](19, "div", 6)(20, "div", 11)(21, "mat-icon");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](22, "schedule");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](23, "div", 8)(24, "div", 9);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](25);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](26, "div", 10);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](27, "Pending Clients");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](28, "div", 6)(29, "div", 12)(30, "mat-icon");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](31, "account_balance_wallet");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](32, "div", 8)(33, "div", 9);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](34);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵpipe"](35, "number");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](36, "div", 10);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](37, "Total Retainer");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]()()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](38, "div", 13)(39, "div", 14)(40, "h3");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](41, "Client List");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](42, "div", 15)(43, "mat-form-field", 16)(44, "mat-icon", 17);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](45, "search");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](46, "input", 18);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtwoWayListener"]("ngModelChange", function ClientsComponent_Template_input_ngModelChange_46_listener($event) {
            _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtwoWayBindingSet"](ctx.searchTerm, $event) || (ctx.searchTerm = $event);
            return $event;
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵlistener"]("input", function ClientsComponent_Template_input_input_46_listener() {
            return ctx.filterClients();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](47, "mat-form-field", 19)(48, "mat-select", 20);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtwoWayListener"]("valueChange", function ClientsComponent_Template_mat_select_valueChange_48_listener($event) {
            _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtwoWayBindingSet"](ctx.statusFilter, $event) || (ctx.statusFilter = $event);
            return $event;
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵlistener"]("selectionChange", function ClientsComponent_Template_mat_select_selectionChange_48_listener() {
            return ctx.filterClients();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](49, "mat-option", 21);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](50, "All Status");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](51, "mat-option", 22);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](52, "Active");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](53, "mat-option", 23);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](54, "Pending");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](55, "mat-option", 24);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](56, "Inactive");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]()()()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](57, "div", 25);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtemplate"](58, ClientsComponent_div_58_Template, 77, 18, "div", 26);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]()()()();
        }
        if (rf & 2) {
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵadvance"](16);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtextInterpolate"](ctx.getClientCount("active"));
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵadvance"](9);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtextInterpolate"](ctx.getClientCount("pending"));
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵadvance"](9);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtextInterpolate1"]("\u20B1", _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵpipeBind1"](35, 6, ctx.getTotalRetainer()), "");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵadvance"](12);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtwoWayProperty"]("ngModel", ctx.searchTerm);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵadvance"](2);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtwoWayProperty"]("value", ctx.statusFilter);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵadvance"](10);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵproperty"]("ngForOf", ctx.filteredClients);
        }
      },
      dependencies: [_angular_common__WEBPACK_IMPORTED_MODULE_1__.NgForOf, _angular_forms__WEBPACK_IMPORTED_MODULE_2__.DefaultValueAccessor, _angular_forms__WEBPACK_IMPORTED_MODULE_2__.NgControlStatus, _angular_forms__WEBPACK_IMPORTED_MODULE_2__.NgModel, _angular_material_button__WEBPACK_IMPORTED_MODULE_3__.MatButton, _angular_material_button__WEBPACK_IMPORTED_MODULE_3__.MatIconButton, _angular_material_input__WEBPACK_IMPORTED_MODULE_4__.MatInput, _angular_material_form_field__WEBPACK_IMPORTED_MODULE_5__.MatFormField, _angular_material_form_field__WEBPACK_IMPORTED_MODULE_5__.MatPrefix, _angular_material_icon__WEBPACK_IMPORTED_MODULE_6__.MatIcon, _angular_material_menu__WEBPACK_IMPORTED_MODULE_7__.MatMenu, _angular_material_menu__WEBPACK_IMPORTED_MODULE_7__.MatMenuItem, _angular_material_menu__WEBPACK_IMPORTED_MODULE_7__.MatMenuTrigger, _angular_material_select__WEBPACK_IMPORTED_MODULE_8__.MatSelect, _angular_material_core__WEBPACK_IMPORTED_MODULE_9__.MatOption, _angular_common__WEBPACK_IMPORTED_MODULE_1__.DecimalPipe, _angular_common__WEBPACK_IMPORTED_MODULE_1__.TitleCasePipe, _angular_common__WEBPACK_IMPORTED_MODULE_1__.DatePipe],
      styles: [".clients-container[_ngcontent-%COMP%] {\n  padding: 32px;\n  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);\n  min-height: calc(100vh - 64px);\n  position: relative;\n}\n\n.clients-container[_ngcontent-%COMP%]::before {\n  content: \"\";\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: radial-gradient(circle at 20% 20%, rgba(59, 130, 246, 0.05) 0%, transparent 50%), radial-gradient(circle at 80% 80%, rgba(196, 154, 86, 0.03) 0%, transparent 50%);\n  pointer-events: none;\n}\n\n.clients-header[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 40px;\n  position: relative;\n  z-index: 1;\n}\n\n.clients-header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\n  margin: 0;\n  font-size: 36px;\n  font-weight: 700;\n  color: #1e293b;\n  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n  position: relative;\n}\n\n.clients-header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]::after {\n  content: \"\";\n  position: absolute;\n  bottom: -8px;\n  left: 0;\n  width: 80px;\n  height: 4px;\n  background: linear-gradient(90deg, #3b82f6 0%, #2563eb 100%);\n  border-radius: 2px;\n}\n\n.clients-content[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  gap: 24px;\n}\n\n.stats-section[_ngcontent-%COMP%] {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\n  gap: 20px;\n}\n\n.stat-card[_ngcontent-%COMP%] {\n  background: white;\n  border-radius: 12px;\n  padding: 24px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n  display: flex;\n  align-items: center;\n  gap: 16px;\n}\n\n.stat-icon[_ngcontent-%COMP%] {\n  width: 60px;\n  height: 60px;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.stat-icon.active[_ngcontent-%COMP%] {\n  background: #e8f5e8;\n  color: #2e7d32;\n}\n\n.stat-icon.pending[_ngcontent-%COMP%] {\n  background: #fff3e0;\n  color: #f57c00;\n}\n\n.stat-icon.revenue[_ngcontent-%COMP%] {\n  background: #e3f2fd;\n  color: #1976d2;\n}\n\n.stat-icon[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\n  font-size: 28px;\n  width: 28px;\n  height: 28px;\n}\n\n.stat-content[_ngcontent-%COMP%] {\n  flex: 1;\n}\n\n.stat-value[_ngcontent-%COMP%] {\n  font-size: 24px;\n  font-weight: 600;\n  color: #333;\n  margin-bottom: 4px;\n}\n\n.stat-label[_ngcontent-%COMP%] {\n  font-size: 14px;\n  color: #666;\n}\n\n.clients-list-section[_ngcontent-%COMP%] {\n  background: white;\n  border-radius: 12px;\n  padding: 24px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n}\n\n.list-header[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 24px;\n}\n\n.list-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\n  margin: 0;\n  font-size: 18px;\n  font-weight: 600;\n}\n\n.list-controls[_ngcontent-%COMP%] {\n  display: flex;\n  gap: 16px;\n}\n\n.search-field[_ngcontent-%COMP%], .filter-field[_ngcontent-%COMP%] {\n  width: 200px;\n}\n\n.clients-grid[_ngcontent-%COMP%] {\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));\n  gap: 20px;\n}\n\n.client-card[_ngcontent-%COMP%] {\n  border: 1px solid rgba(226, 232, 240, 0.8);\n  border-radius: 20px;\n  padding: 28px;\n  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);\n  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);\n  position: relative;\n  overflow: hidden;\n}\n\n.client-card[_ngcontent-%COMP%]::before {\n  content: \"\";\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  height: 4px;\n  background: linear-gradient(90deg, #3b82f6 0%, #2563eb 100%);\n  transform: scaleX(0);\n  transition: transform 0.3s ease;\n}\n\n.client-card[_ngcontent-%COMP%]:hover::before {\n  transform: scaleX(1);\n}\n\n.client-card[_ngcontent-%COMP%]:hover {\n  border-color: #3b82f6;\n  box-shadow: 0 20px 60px rgba(59, 130, 246, 0.15), 0 8px 24px rgba(59, 130, 246, 0.08);\n  background: linear-gradient(135deg, #ffffff 0%, #f0f9ff 100%);\n  transform: translateY(-8px);\n}\n\n.client-header[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 16px;\n}\n\n.client-avatar[_ngcontent-%COMP%] {\n  width: 50px;\n  height: 50px;\n  border-radius: 50%;\n  background: linear-gradient(135deg, #C49A56 0%, #B8935A 100%);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: white;\n}\n\n.client-status[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  gap: 6px;\n  padding: 4px 8px;\n  border-radius: 12px;\n  font-size: 11px;\n  font-weight: 500;\n  text-transform: uppercase;\n}\n\n.client-status.active[_ngcontent-%COMP%] {\n  background: #e8f5e8;\n  color: #2e7d32;\n}\n\n.client-status.pending[_ngcontent-%COMP%] {\n  background: #fff3e0;\n  color: #f57c00;\n}\n\n.client-status.inactive[_ngcontent-%COMP%] {\n  background: #ffebee;\n  color: #d32f2f;\n}\n\n.status-dot[_ngcontent-%COMP%] {\n  width: 6px;\n  height: 6px;\n  border-radius: 50%;\n  background: currentColor;\n}\n\n.client-info[_ngcontent-%COMP%] {\n  margin-bottom: 16px;\n}\n\n.client-name[_ngcontent-%COMP%] {\n  margin: 0 0 8px 0;\n  font-size: 16px;\n  font-weight: 600;\n  color: #333;\n}\n\n.client-contact[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  gap: 4px;\n}\n\n.contact-item[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  font-size: 12px;\n  color: #666;\n}\n\n.contact-item[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\n  font-size: 14px;\n  width: 14px;\n  height: 14px;\n}\n\n.client-details[_ngcontent-%COMP%] {\n  margin-bottom: 16px;\n  padding-top: 16px;\n  border-top: 1px solid #e0e0e0;\n}\n\n.detail-row[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: space-between;\n  margin-bottom: 8px;\n  font-size: 12px;\n}\n\n.detail-label[_ngcontent-%COMP%] {\n  color: #666;\n}\n\n.detail-value[_ngcontent-%COMP%] {\n  font-weight: 500;\n  color: #333;\n}\n\n.client-actions[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: flex-end;\n  gap: 4px;\n  padding-top: 16px;\n  border-top: 1px solid #e0e0e0;\n}\n\n.client-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\n  width: 32px;\n  height: 32px;\n}\n\n.client-actions[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\n  font-size: 16px;\n  width: 16px;\n  height: 16px;\n}\n\n@media (max-width: 768px) {\n  .clients-header[_ngcontent-%COMP%] {\n    flex-direction: column;\n    gap: 16px;\n    align-items: stretch;\n  }\n  .list-header[_ngcontent-%COMP%] {\n    flex-direction: column;\n    gap: 16px;\n    align-items: stretch;\n  }\n  .list-controls[_ngcontent-%COMP%] {\n    flex-direction: column;\n  }\n  .search-field[_ngcontent-%COMP%], .filter-field[_ngcontent-%COMP%] {\n    width: 100%;\n  }\n  .clients-grid[_ngcontent-%COMP%] {\n    grid-template-columns: 1fr;\n  }\n}\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */"]
    });
  }
}

/***/ }),

/***/ 2320:
/*!**************************************************!*\
  !*** ./src/app/dashboard/dashboard.component.ts ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   DashboardComponent: () => (/* binding */ DashboardComponent)
/* harmony export */ });
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @angular/core */ 7580);
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @angular/common */ 316);
/* harmony import */ var _angular_forms__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @angular/forms */ 4456);



function DashboardComponent_div_14_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](0, "div", 38);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const day_r1 = ctx.$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵadvance"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtextInterpolate"](day_r1);
  }
}
function DashboardComponent_div_15_Template(rf, ctx) {
  if (rf & 1) {
    const _r2 = _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](0, "div", 39);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵlistener"]("click", function DashboardComponent_div_15_Template_div_click_0_listener() {
      const day_r3 = _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵrestoreView"](_r2).$implicit;
      const ctx_r3 = _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵresetView"](ctx_r3.selectDate(day_r3));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const day_r3 = ctx.$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵclassProp"]("other-month", day_r3.otherMonth)("today", day_r3.isToday)("has-appointment", day_r3.hasAppointment);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵadvance"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtextInterpolate1"](" ", day_r3.date, " ");
  }
}
function DashboardComponent_div_20_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](0, "div", 40)(1, "div", 41)(2, "div", 42);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](4, "div", 43);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](5);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](6, "div", 44);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](7);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]()();
  }
  if (rf & 2) {
    const appointment_r5 = ctx.$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵadvance"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtextInterpolate"](appointment_r5.type);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtextInterpolate"](appointment_r5.client);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtextInterpolate"](appointment_r5.time);
  }
}
function DashboardComponent_div_30_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](0, "div", 45);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelement"](1, "div", 46);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](2, "span");
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]()();
  }
  if (rf & 2) {
    const client_r6 = ctx.$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵadvance"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵclassMap"](client_r6.status);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtextInterpolate"](client_r6.name);
  }
}
function DashboardComponent_div_64_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](0, "div", 47)(1, "div", 48);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](2, "\uD83D\uDCC4");
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](3, "div", 49)(4, "div", 50);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](5);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](6, "div", 51);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](7);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](8, "button", 52);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](9, "\u203A");
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]()();
  }
  if (rf & 2) {
    const doc_r7 = ctx.$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵadvance"](5);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtextInterpolate"](doc_r7.name);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtextInterpolate"](doc_r7.date);
  }
}
function DashboardComponent_div_73_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelement"](0, "div", 53);
  }
  if (rf & 2) {
    const bar_r8 = ctx.$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵstyleProp"]("height", bar_r8.height, "%")("background", bar_r8.color);
  }
}
function DashboardComponent_span_75_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](0, "span");
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const label_r9 = ctx.$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵadvance"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtextInterpolate"](label_r9);
  }
}
function DashboardComponent_div_81_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](0, "div", 54)(1, "div", 55);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](3, "div", 56)(4, "div", 57);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](5);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](6, "div", 58);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](7);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]()()();
  }
  if (rf & 2) {
    const transaction_r10 = ctx.$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtextInterpolate"](transaction_r10.amount);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵadvance"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtextInterpolate"](transaction_r10.type);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtextInterpolate"](transaction_r10.date);
  }
}
class DashboardComponent {
  constructor() {
    this.currentMonth = 'July';
    this.currentYear = 2025;
    this.selectedDateString = 'July 1, 2025';
    this.searchTerm = '';
    this.dayHeaders = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
    this.calendarDays = [{
      date: 30,
      otherMonth: true,
      isToday: false,
      hasAppointment: false
    }, {
      date: 1,
      otherMonth: false,
      isToday: true,
      hasAppointment: true
    }, {
      date: 2,
      otherMonth: false,
      isToday: false,
      hasAppointment: false
    }, {
      date: 3,
      otherMonth: false,
      isToday: false,
      hasAppointment: false
    }, {
      date: 4,
      otherMonth: false,
      isToday: false,
      hasAppointment: false
    }, {
      date: 5,
      otherMonth: false,
      isToday: false,
      hasAppointment: false
    }, {
      date: 6,
      otherMonth: false,
      isToday: false,
      hasAppointment: false
    }, {
      date: 7,
      otherMonth: false,
      isToday: false,
      hasAppointment: false
    }, {
      date: 8,
      otherMonth: false,
      isToday: false,
      hasAppointment: false
    }, {
      date: 9,
      otherMonth: false,
      isToday: false,
      hasAppointment: false
    }, {
      date: 10,
      otherMonth: false,
      isToday: false,
      hasAppointment: false
    }, {
      date: 11,
      otherMonth: false,
      isToday: false,
      hasAppointment: false
    }, {
      date: 12,
      otherMonth: false,
      isToday: false,
      hasAppointment: true
    }, {
      date: 13,
      otherMonth: false,
      isToday: false,
      hasAppointment: false
    }, {
      date: 14,
      otherMonth: false,
      isToday: false,
      hasAppointment: false
    }, {
      date: 15,
      otherMonth: false,
      isToday: false,
      hasAppointment: false
    }, {
      date: 16,
      otherMonth: false,
      isToday: false,
      hasAppointment: false
    }, {
      date: 17,
      otherMonth: false,
      isToday: false,
      hasAppointment: false
    }, {
      date: 18,
      otherMonth: false,
      isToday: false,
      hasAppointment: false
    }, {
      date: 19,
      otherMonth: false,
      isToday: false,
      hasAppointment: false
    }, {
      date: 20,
      otherMonth: false,
      isToday: false,
      hasAppointment: false
    }, {
      date: 21,
      otherMonth: false,
      isToday: false,
      hasAppointment: false
    }, {
      date: 22,
      otherMonth: false,
      isToday: false,
      hasAppointment: false
    }, {
      date: 23,
      otherMonth: false,
      isToday: false,
      hasAppointment: false
    }, {
      date: 24,
      otherMonth: false,
      isToday: false,
      hasAppointment: false
    }, {
      date: 25,
      otherMonth: false,
      isToday: false,
      hasAppointment: false
    }, {
      date: 26,
      otherMonth: false,
      isToday: false,
      hasAppointment: false
    }, {
      date: 27,
      otherMonth: false,
      isToday: false,
      hasAppointment: false
    }, {
      date: 28,
      otherMonth: false,
      isToday: false,
      hasAppointment: false
    }, {
      date: 29,
      otherMonth: false,
      isToday: false,
      hasAppointment: false
    }, {
      date: 30,
      otherMonth: false,
      isToday: false,
      hasAppointment: false
    }, {
      date: 31,
      otherMonth: false,
      isToday: false,
      hasAppointment: false
    }, {
      date: 1,
      otherMonth: true,
      isToday: false,
      hasAppointment: false
    }, {
      date: 2,
      otherMonth: true,
      isToday: false,
      hasAppointment: false
    }, {
      date: 3,
      otherMonth: true,
      isToday: false,
      hasAppointment: false
    }];
    this.todayAppointments = [{
      type: 'Consultation',
      client: 'Kobe Bryant',
      time: '10:00'
    }, {
      type: 'Consultation',
      client: 'LeBron James',
      time: '11:00'
    }];
    this.retainerClients = [{
      name: 'Amado Cruz',
      status: 'active'
    }, {
      name: 'Clara Mendoza',
      status: 'inactive'
    }, {
      name: 'Kobe Bryant',
      status: 'active'
    }];
    this.recentDocuments = [{
      name: 'Lopez-Contract.pdf',
      date: 'Updated today'
    }, {
      name: 'Client-Agreement.pdf',
      date: '4 days ago'
    }];
    this.chartData = [{
      height: 60,
      color: '#e91e63'
    }, {
      height: 40,
      color: '#e91e63'
    }, {
      height: 80,
      color: '#e91e63'
    }, {
      height: 30,
      color: '#e91e63'
    }, {
      height: 70,
      color: '#e91e63'
    }, {
      height: 50,
      color: '#e91e63'
    }, {
      height: 90,
      color: '#e91e63'
    }, {
      height: 45,
      color: '#e91e63'
    }, {
      height: 85,
      color: '#e91e63'
    }];
    this.chartLabels = ['1', '2', '3', '4', '5', '6', '7', '8', '9'];
    this.recentTransactions = [{
      amount: '₱15,000',
      type: 'Consultation',
      date: 'June 15, 2025'
    }, {
      amount: '₱5,000',
      type: 'Retainer',
      date: 'June 16, 2025'
    }];
  }
  ngOnInit() {
    // Initialize component
  }
  previousMonth() {
    // Implementation for previous month navigation
  }
  nextMonth() {
    // Implementation for next month navigation
  }
  selectDate(day) {
    // Implementation for date selection
  }
  static {
    this.ɵfac = function DashboardComponent_Factory(t) {
      return new (t || DashboardComponent)();
    };
  }
  static {
    this.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵdefineComponent"]({
      type: DashboardComponent,
      selectors: [["app-dashboard"]],
      decls: 82,
      vars: 11,
      consts: [[1, "dashboard-container"], [1, "left-column"], [1, "scheduling-section"], [1, "calendar-widget"], [1, "calendar-header"], [1, "nav-btn", 3, "click"], [1, "calendar-grid"], ["class", "day-header", 4, "ngFor", "ngForOf"], ["class", "calendar-day", 3, "other-month", "today", "has-appointment", "click", 4, "ngFor", "ngForOf"], [1, "booking-section"], [1, "appointment-list"], ["class", "appointment-item", 4, "ngFor", "ngForOf"], [1, "new-booking-btn"], [1, "retainer-section"], [1, "section-header"], [1, "add-client-btn"], [1, "client-list"], ["class", "client-item", 4, "ngFor", "ngForOf"], [1, "right-column"], [1, "files-section"], [1, "search-box"], ["type", "text", "placeholder", "Search a document", 3, "ngModelChange", "ngModel"], [1, "search-icon"], [1, "file-categories"], [1, "file-category"], [1, "folder-icon"], [1, "recent-documents"], ["class", "document-item", 4, "ngFor", "ngForOf"], [1, "finance-section"], [1, "new-transaction-btn"], [1, "chart-container"], [1, "chart-bars"], ["class", "bar", 3, "height", "background", 4, "ngFor", "ngForOf"], [1, "chart-labels"], [4, "ngFor", "ngForOf"], [1, "chart-period"], [1, "recent-transactions"], ["class", "transaction-item", 4, "ngFor", "ngForOf"], [1, "day-header"], [1, "calendar-day", 3, "click"], [1, "appointment-item"], [1, "appointment-info"], [1, "appointment-type"], [1, "appointment-client"], [1, "appointment-time"], [1, "client-item"], [1, "client-status"], [1, "document-item"], [1, "doc-icon"], [1, "doc-info"], [1, "doc-name"], [1, "doc-date"], [1, "doc-action"], [1, "bar"], [1, "transaction-item"], [1, "transaction-amount"], [1, "transaction-info"], [1, "transaction-type"], [1, "transaction-date"]],
      template: function DashboardComponent_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](0, "div", 0)(1, "div", 1)(2, "div", 2)(3, "h2");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](4, "Scheduling");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](5, "div", 3)(6, "div", 4)(7, "button", 5);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵlistener"]("click", function DashboardComponent_Template_button_click_7_listener() {
            return ctx.previousMonth();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](8, "\u2039");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](9, "h3");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](10);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](11, "button", 5);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵlistener"]("click", function DashboardComponent_Template_button_click_11_listener() {
            return ctx.nextMonth();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](12, "\u203A");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](13, "div", 6);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtemplate"](14, DashboardComponent_div_14_Template, 2, 1, "div", 7)(15, DashboardComponent_div_15_Template, 2, 7, "div", 8);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](16, "div", 9)(17, "h4");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](18);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](19, "div", 10);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtemplate"](20, DashboardComponent_div_20_Template, 8, 3, "div", 11);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](21, "button", 12);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](22, "+ New Booking");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](23, "div", 13)(24, "div", 14)(25, "h4");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](26, "Retainer Clients");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](27, "button", 15);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](28, "+ Add Client");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](29, "div", 16);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtemplate"](30, DashboardComponent_div_30_Template, 4, 3, "div", 17);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]()()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](31, "div", 18)(32, "div", 19)(33, "div", 14)(34, "h2");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](35, "Files");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](36, "div", 20)(37, "input", 21);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtwoWayListener"]("ngModelChange", function DashboardComponent_Template_input_ngModelChange_37_listener($event) {
            _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtwoWayBindingSet"](ctx.searchTerm, $event) || (ctx.searchTerm = $event);
            return $event;
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](38, "span", 22);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](39, "\uD83D\uDD0D");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](40, "div", 23)(41, "div", 24)(42, "div", 25);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](43, "\uD83D\uDCC1");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](44, "span");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](45, "Disclosures");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](46, "div", 24)(47, "div", 25);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](48, "\uD83D\uDCC1");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](49, "span");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](50, "Evidence");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](51, "div", 24)(52, "div", 25);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](53, "\uD83D\uDCC1");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](54, "span");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](55, "Receipts");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](56, "div", 24)(57, "div", 25);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](58, "\uD83D\uDCC1");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](59, "span");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](60, "Contracts");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](61, "div", 26)(62, "h4");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](63, "Recent documents");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtemplate"](64, DashboardComponent_div_64_Template, 10, 2, "div", 27);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](65, "div", 28)(66, "div", 14)(67, "h2");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](68, "Finance");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](69, "button", 29);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](70, "+ New Transaction");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](71, "div", 30)(72, "div", 31);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtemplate"](73, DashboardComponent_div_73_Template, 1, 4, "div", 32);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](74, "div", 33);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtemplate"](75, DashboardComponent_span_75_Template, 2, 1, "span", 34);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](76, "div", 35);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](77, "This year");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](78, "div", 36)(79, "h4");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](80, "Recent Transactions");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtemplate"](81, DashboardComponent_div_81_Template, 8, 3, "div", 37);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]()()()();
        }
        if (rf & 2) {
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵadvance"](10);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtextInterpolate"](ctx.currentMonth);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵadvance"](4);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵproperty"]("ngForOf", ctx.dayHeaders);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵadvance"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵproperty"]("ngForOf", ctx.calendarDays);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵadvance"](3);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtextInterpolate1"]("Booking on ", ctx.selectedDateString, "");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵadvance"](2);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵproperty"]("ngForOf", ctx.todayAppointments);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵadvance"](10);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵproperty"]("ngForOf", ctx.retainerClients);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵadvance"](7);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtwoWayProperty"]("ngModel", ctx.searchTerm);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵadvance"](27);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵproperty"]("ngForOf", ctx.recentDocuments);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵadvance"](9);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵproperty"]("ngForOf", ctx.chartData);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵadvance"](2);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵproperty"]("ngForOf", ctx.chartLabels);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵadvance"](6);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵproperty"]("ngForOf", ctx.recentTransactions);
        }
      },
      dependencies: [_angular_common__WEBPACK_IMPORTED_MODULE_1__.NgForOf, _angular_forms__WEBPACK_IMPORTED_MODULE_2__.DefaultValueAccessor, _angular_forms__WEBPACK_IMPORTED_MODULE_2__.NgControlStatus, _angular_forms__WEBPACK_IMPORTED_MODULE_2__.NgModel],
      styles: [".dashboard-container[_ngcontent-%COMP%] {\n  display: flex;\n  gap: 24px;\n  padding: 24px;\n  background: #f8f9fa;\n  min-height: 100vh;\n}\n\n.left-column[_ngcontent-%COMP%] {\n  flex: 1;\n  max-width: 400px;\n}\n\n.right-column[_ngcontent-%COMP%] {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  gap: 24px;\n}\n\n.scheduling-section[_ngcontent-%COMP%], .files-section[_ngcontent-%COMP%], .finance-section[_ngcontent-%COMP%] {\n  background: white;\n  border-radius: 12px;\n  padding: 24px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n}\n\nh2[_ngcontent-%COMP%] {\n  font-size: 24px;\n  font-weight: 600;\n  margin: 0 0 20px 0;\n  color: #333;\n}\n\nh3[_ngcontent-%COMP%], h4[_ngcontent-%COMP%] {\n  font-size: 16px;\n  font-weight: 600;\n  margin: 0 0 12px 0;\n  color: #333;\n}\n\n.calendar-widget[_ngcontent-%COMP%] {\n  margin-bottom: 24px;\n}\n\n.calendar-header[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 16px;\n}\n\n.nav-btn[_ngcontent-%COMP%] {\n  background: none;\n  border: none;\n  font-size: 20px;\n  cursor: pointer;\n  padding: 8px;\n  border-radius: 4px;\n}\n\n.nav-btn[_ngcontent-%COMP%]:hover {\n  background: #f0f0f0;\n}\n\n.calendar-grid[_ngcontent-%COMP%] {\n  display: grid;\n  grid-template-columns: repeat(7, 1fr);\n  gap: 1px;\n  background: #e0e0e0;\n  border-radius: 8px;\n  overflow: hidden;\n}\n\n.day-header[_ngcontent-%COMP%] {\n  background: #f5f5f5;\n  padding: 8px;\n  text-align: center;\n  font-size: 12px;\n  font-weight: 600;\n  color: #666;\n}\n\n.calendar-day[_ngcontent-%COMP%] {\n  background: white;\n  padding: 12px 8px;\n  text-align: center;\n  cursor: pointer;\n  font-size: 14px;\n  min-height: 40px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.calendar-day[_ngcontent-%COMP%]:hover {\n  background: #f0f0f0;\n}\n\n.calendar-day.today[_ngcontent-%COMP%] {\n  background: #1976d2;\n  color: white;\n}\n\n.calendar-day.other-month[_ngcontent-%COMP%] {\n  color: #ccc;\n}\n\n.calendar-day.has-appointment[_ngcontent-%COMP%] {\n  background: #e3f2fd;\n  color: #1976d2;\n}\n\n.booking-section[_ngcontent-%COMP%] {\n  margin-bottom: 24px;\n}\n\n.appointment-item[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 12px 0;\n  border-bottom: 1px solid #f0f0f0;\n}\n\n.appointment-type[_ngcontent-%COMP%] {\n  font-weight: 600;\n  color: #333;\n}\n\n.appointment-client[_ngcontent-%COMP%] {\n  color: #666;\n  font-size: 14px;\n}\n\n.appointment-time[_ngcontent-%COMP%] {\n  font-weight: 600;\n  color: #1976d2;\n}\n\n.new-booking-btn[_ngcontent-%COMP%] {\n  width: 100%;\n  background: #1976d2;\n  color: white;\n  border: none;\n  padding: 12px;\n  border-radius: 8px;\n  font-weight: 600;\n  cursor: pointer;\n  margin-top: 16px;\n}\n\n.new-booking-btn[_ngcontent-%COMP%]:hover {\n  background: #1565c0;\n}\n\n.section-header[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 20px;\n}\n\n.add-client-btn[_ngcontent-%COMP%] {\n  background: none;\n  border: 1px solid #ddd;\n  padding: 6px 12px;\n  border-radius: 6px;\n  font-size: 12px;\n  cursor: pointer;\n  color: #666;\n}\n\n.client-list[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  gap: 8px;\n}\n\n.client-item[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  padding: 8px 0;\n}\n\n.client-status[_ngcontent-%COMP%] {\n  width: 8px;\n  height: 8px;\n  border-radius: 50%;\n  background: #4caf50;\n}\n\n.client-status.inactive[_ngcontent-%COMP%] {\n  background: #f44336;\n}\n\n.search-box[_ngcontent-%COMP%] {\n  position: relative;\n  width: 250px;\n}\n\n.search-box[_ngcontent-%COMP%]   input[_ngcontent-%COMP%] {\n  width: 100%;\n  padding: 8px 12px 8px 36px;\n  border: 1px solid #ddd;\n  border-radius: 6px;\n  font-size: 14px;\n}\n\n.search-icon[_ngcontent-%COMP%] {\n  position: absolute;\n  left: 12px;\n  top: 50%;\n  transform: translateY(-50%);\n  color: #666;\n}\n\n.file-categories[_ngcontent-%COMP%] {\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: 16px;\n  margin-bottom: 24px;\n}\n\n.file-category[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  gap: 12px;\n  padding: 16px;\n  border: 1px solid #e0e0e0;\n  border-radius: 8px;\n  cursor: pointer;\n}\n\n.file-category[_ngcontent-%COMP%]:hover {\n  background: #f5f5f5;\n}\n\n.folder-icon[_ngcontent-%COMP%] {\n  font-size: 24px;\n}\n\n.document-item[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  gap: 12px;\n  padding: 12px 0;\n  border-bottom: 1px solid #f0f0f0;\n}\n\n.doc-icon[_ngcontent-%COMP%] {\n  font-size: 20px;\n}\n\n.doc-info[_ngcontent-%COMP%] {\n  flex: 1;\n}\n\n.doc-name[_ngcontent-%COMP%] {\n  font-weight: 500;\n  color: #333;\n}\n\n.doc-date[_ngcontent-%COMP%] {\n  font-size: 12px;\n  color: #666;\n}\n\n.doc-action[_ngcontent-%COMP%] {\n  background: none;\n  border: none;\n  font-size: 16px;\n  color: #666;\n  cursor: pointer;\n}\n\n.new-transaction-btn[_ngcontent-%COMP%] {\n  background: #1976d2;\n  color: white;\n  border: none;\n  padding: 8px 16px;\n  border-radius: 6px;\n  font-size: 14px;\n  cursor: pointer;\n}\n\n.chart-container[_ngcontent-%COMP%] {\n  margin-bottom: 24px;\n}\n\n.chart-bars[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: flex-end;\n  gap: 8px;\n  height: 120px;\n  margin-bottom: 8px;\n}\n\n.bar[_ngcontent-%COMP%] {\n  flex: 1;\n  min-height: 20px;\n  border-radius: 4px 4px 0 0;\n}\n\n.chart-labels[_ngcontent-%COMP%] {\n  display: flex;\n  gap: 8px;\n  font-size: 12px;\n  color: #666;\n}\n\n.chart-labels[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\n  flex: 1;\n  text-align: center;\n}\n\n.chart-period[_ngcontent-%COMP%] {\n  text-align: center;\n  font-size: 12px;\n  color: #666;\n  margin-top: 8px;\n}\n\n.transaction-item[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 12px 0;\n  border-bottom: 1px solid #f0f0f0;\n}\n\n.transaction-amount[_ngcontent-%COMP%] {\n  font-weight: 600;\n  color: #333;\n}\n\n.transaction-info[_ngcontent-%COMP%] {\n  text-align: right;\n}\n\n.transaction-type[_ngcontent-%COMP%] {\n  font-weight: 500;\n  color: #333;\n}\n\n.transaction-date[_ngcontent-%COMP%] {\n  font-size: 12px;\n  color: #666;\n}\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */"]
    });
  }
}

/***/ }),

/***/ 4776:
/*!******************************************!*\
  !*** ./src/app/files/files.component.ts ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   FilesComponent: () => (/* binding */ FilesComponent)
/* harmony export */ });
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @angular/core */ 7580);
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @angular/common */ 316);
/* harmony import */ var _angular_forms__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @angular/forms */ 4456);
/* harmony import */ var _angular_material_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @angular/material/button */ 4175);
/* harmony import */ var _angular_material_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @angular/material/input */ 5541);
/* harmony import */ var _angular_material_form_field__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @angular/material/form-field */ 4950);
/* harmony import */ var _angular_material_icon__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @angular/material/icon */ 3840);







function FilesComponent_div_53_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](0, "div", 18)(1, "div", 19)(2, "mat-icon");
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](4, "div", 20)(5, "div", 21);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](6);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](7, "div", 22)(8, "span", 23);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](9);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](10, "span", 24);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](11);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]()()();
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](12, "div", 25)(13, "button", 26)(14, "mat-icon");
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](15, "download");
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](16, "button", 26)(17, "mat-icon");
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](18, "more_vert");
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]()()()();
  }
  if (rf & 2) {
    const doc_r1 = ctx.$implicit;
    const ctx_r1 = _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵadvance"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtextInterpolate"](ctx_r1.getDocumentIcon(doc_r1.type));
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵadvance"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtextInterpolate"](doc_r1.name);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵadvance"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtextInterpolate"](doc_r1.size);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtextInterpolate"](ctx_r1.getTimeAgo(doc_r1.uploadDate));
  }
}
class FilesComponent {
  constructor() {
    this.searchTerm = '';
    this.selectedCategory = null;
    this.documents = [{
      id: '1',
      name: 'Lopez-Contract.pdf',
      category: 'contracts',
      uploadDate: new Date(),
      size: '2.4 MB',
      type: 'pdf'
    }, {
      id: '2',
      name: 'Client-Agreement.pdf',
      category: 'contracts',
      uploadDate: new Date(Date.now() - 4 * 24 * 60 * 60 * 1000),
      size: '1.8 MB',
      type: 'pdf'
    }, {
      id: '3',
      name: 'Evidence-Photos.zip',
      category: 'evidence',
      uploadDate: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),
      size: '15.2 MB',
      type: 'zip'
    }, {
      id: '4',
      name: 'Financial-Disclosure.docx',
      category: 'disclosures',
      uploadDate: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000),
      size: '890 KB',
      type: 'docx'
    }, {
      id: '5',
      name: 'Receipt-001.jpg',
      category: 'receipts',
      uploadDate: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000),
      size: '245 KB',
      type: 'jpg'
    }];
    this.filteredDocuments = [];
  }
  ngOnInit() {
    this.filteredDocuments = [...this.documents];
  }
  getCategoryCount(category) {
    return this.documents.filter(doc => doc.category === category).length;
  }
  selectCategory(category) {
    this.selectedCategory = category;
    this.filterDocuments();
  }
  filterDocuments() {
    let filtered = [...this.documents];
    // Filter by category
    if (this.selectedCategory) {
      filtered = filtered.filter(doc => doc.category === this.selectedCategory);
    }
    // Filter by search term
    if (this.searchTerm) {
      filtered = filtered.filter(doc => doc.name.toLowerCase().includes(this.searchTerm.toLowerCase()));
    }
    this.filteredDocuments = filtered;
  }
  getDocumentIcon(type) {
    switch (type) {
      case 'pdf':
        return 'picture_as_pdf';
      case 'docx':
        return 'description';
      case 'zip':
        return 'archive';
      case 'jpg':
      case 'png':
        return 'image';
      default:
        return 'insert_drive_file';
    }
  }
  getTimeAgo(date) {
    const now = new Date();
    const diffTime = Math.abs(now.getTime() - date.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    if (diffDays === 1) {
      return 'Updated today';
    } else {
      return `${diffDays} days ago`;
    }
  }
  static {
    this.ɵfac = function FilesComponent_Factory(t) {
      return new (t || FilesComponent)();
    };
  }
  static {
    this.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵdefineComponent"]({
      type: FilesComponent,
      selectors: [["app-files"]],
      decls: 54,
      vars: 6,
      consts: [[1, "files-container"], [1, "files-header"], [1, "search-container"], ["appearance", "outline", 1, "search-field"], ["matPrefix", ""], ["matInput", "", "placeholder", "Search a document", 3, "ngModelChange", "input", "ngModel"], [1, "files-content"], [1, "document-categories"], [1, "category-grid"], [1, "category-card", 3, "click"], [1, "category-icon"], [1, "category-name"], [1, "category-count"], [1, "recent-documents"], [1, "section-header"], ["mat-raised-button", "", "color", "primary"], [1, "documents-list"], ["class", "document-item", 4, "ngFor", "ngForOf"], [1, "document-item"], [1, "document-icon"], [1, "document-info"], [1, "document-name"], [1, "document-meta"], [1, "document-size"], [1, "document-date"], [1, "document-actions"], ["mat-icon-button", ""]],
      template: function FilesComponent_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](0, "div", 0)(1, "div", 1)(2, "h1");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](3, "Files");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](4, "div", 2)(5, "mat-form-field", 3)(6, "mat-icon", 4);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](7, "search");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](8, "input", 5);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtwoWayListener"]("ngModelChange", function FilesComponent_Template_input_ngModelChange_8_listener($event) {
            _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtwoWayBindingSet"](ctx.searchTerm, $event) || (ctx.searchTerm = $event);
            return $event;
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵlistener"]("input", function FilesComponent_Template_input_input_8_listener() {
            return ctx.filterDocuments();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]()()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](9, "div", 6)(10, "div", 7)(11, "div", 8)(12, "div", 9);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵlistener"]("click", function FilesComponent_Template_div_click_12_listener() {
            return ctx.selectCategory("disclosures");
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](13, "div", 10)(14, "mat-icon");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](15, "folder");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](16, "div", 11);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](17, "Disclosures");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](18, "div", 12);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](19);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](20, "div", 9);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵlistener"]("click", function FilesComponent_Template_div_click_20_listener() {
            return ctx.selectCategory("evidence");
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](21, "div", 10)(22, "mat-icon");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](23, "folder");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](24, "div", 11);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](25, "Evidence");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](26, "div", 12);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](27);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](28, "div", 9);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵlistener"]("click", function FilesComponent_Template_div_click_28_listener() {
            return ctx.selectCategory("receipts");
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](29, "div", 10)(30, "mat-icon");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](31, "folder");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](32, "div", 11);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](33, "Receipts");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](34, "div", 12);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](35);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](36, "div", 9);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵlistener"]("click", function FilesComponent_Template_div_click_36_listener() {
            return ctx.selectCategory("contracts");
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](37, "div", 10)(38, "mat-icon");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](39, "folder");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](40, "div", 11);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](41, "Contracts");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](42, "div", 12);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](43);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]()()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](44, "div", 13)(45, "div", 14)(46, "h3");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](47, "Recent documents");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](48, "button", 15)(49, "mat-icon");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](50, "upload");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](51, " Upload Document ");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](52, "div", 16);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtemplate"](53, FilesComponent_div_53_Template, 19, 4, "div", 17);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]()()()();
        }
        if (rf & 2) {
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵadvance"](8);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtwoWayProperty"]("ngModel", ctx.searchTerm);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵadvance"](11);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtextInterpolate1"]("", ctx.getCategoryCount("disclosures"), " files");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵadvance"](8);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtextInterpolate1"]("", ctx.getCategoryCount("evidence"), " files");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵadvance"](8);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtextInterpolate1"]("", ctx.getCategoryCount("receipts"), " files");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵadvance"](8);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtextInterpolate1"]("", ctx.getCategoryCount("contracts"), " files");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵadvance"](10);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵproperty"]("ngForOf", ctx.filteredDocuments);
        }
      },
      dependencies: [_angular_common__WEBPACK_IMPORTED_MODULE_1__.NgForOf, _angular_forms__WEBPACK_IMPORTED_MODULE_2__.DefaultValueAccessor, _angular_forms__WEBPACK_IMPORTED_MODULE_2__.NgControlStatus, _angular_forms__WEBPACK_IMPORTED_MODULE_2__.NgModel, _angular_material_button__WEBPACK_IMPORTED_MODULE_3__.MatButton, _angular_material_button__WEBPACK_IMPORTED_MODULE_3__.MatIconButton, _angular_material_input__WEBPACK_IMPORTED_MODULE_4__.MatInput, _angular_material_form_field__WEBPACK_IMPORTED_MODULE_5__.MatFormField, _angular_material_form_field__WEBPACK_IMPORTED_MODULE_5__.MatPrefix, _angular_material_icon__WEBPACK_IMPORTED_MODULE_6__.MatIcon],
      styles: [".files-container[_ngcontent-%COMP%] {\n  padding: 32px;\n  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);\n  min-height: calc(100vh - 64px);\n  position: relative;\n}\n\n.files-container[_ngcontent-%COMP%]::before {\n  content: \"\";\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: radial-gradient(circle at 30% 30%, rgba(168, 85, 247, 0.05) 0%, transparent 50%), radial-gradient(circle at 70% 70%, rgba(34, 197, 94, 0.03) 0%, transparent 50%);\n  pointer-events: none;\n}\n\n.files-header[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 40px;\n  position: relative;\n  z-index: 1;\n}\n\n.files-header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\n  margin: 0;\n  font-size: 36px;\n  font-weight: 700;\n  color: #1e293b;\n  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n  position: relative;\n}\n\n.files-header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]::after {\n  content: \"\";\n  position: absolute;\n  bottom: -8px;\n  left: 0;\n  width: 80px;\n  height: 4px;\n  background: linear-gradient(90deg, #a855f7 0%, #9333ea 100%);\n  border-radius: 2px;\n}\n\n.search-container[_ngcontent-%COMP%] {\n  width: 300px;\n}\n\n.search-field[_ngcontent-%COMP%] {\n  width: 100%;\n}\n\n.files-content[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  gap: 32px;\n}\n\n.document-categories[_ngcontent-%COMP%] {\n  background: white;\n  border-radius: 12px;\n  padding: 24px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n}\n\n.category-grid[_ngcontent-%COMP%] {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n  gap: 16px;\n}\n\n.category-card[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  padding: 24px;\n  border: 2px solid #e0e0e0;\n  border-radius: 12px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  background: #fafafa;\n}\n\n.category-card[_ngcontent-%COMP%]:hover {\n  border-color: #C49A56;\n  background: #fff;\n  transform: translateY(-2px);\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\n}\n\n.category-icon[_ngcontent-%COMP%] {\n  width: 60px;\n  height: 60px;\n  border-radius: 50%;\n  background: linear-gradient(135deg, #C49A56 0%, #B8935A 100%);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-bottom: 16px;\n}\n\n.category-icon[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\n  color: white;\n  font-size: 28px;\n  width: 28px;\n  height: 28px;\n}\n\n.category-name[_ngcontent-%COMP%] {\n  font-size: 16px;\n  font-weight: 600;\n  color: #333;\n  margin-bottom: 4px;\n}\n\n.category-count[_ngcontent-%COMP%] {\n  font-size: 12px;\n  color: #666;\n}\n\n.recent-documents[_ngcontent-%COMP%] {\n  background: white;\n  border-radius: 12px;\n  padding: 24px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n}\n\n.section-header[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 24px;\n}\n\n.section-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\n  margin: 0;\n  font-size: 18px;\n  font-weight: 600;\n  color: #333;\n}\n\n.documents-list[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  gap: 12px;\n}\n\n.document-item[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  gap: 16px;\n  padding: 16px;\n  border: 1px solid #e0e0e0;\n  border-radius: 8px;\n  transition: all 0.2s ease;\n}\n\n.document-item[_ngcontent-%COMP%]:hover {\n  background: #f8f9fa;\n  border-color: #C49A56;\n}\n\n.document-icon[_ngcontent-%COMP%] {\n  width: 40px;\n  height: 40px;\n  border-radius: 8px;\n  background: #f0f0f0;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.document-icon[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\n  color: #666;\n  font-size: 20px;\n  width: 20px;\n  height: 20px;\n}\n\n.document-info[_ngcontent-%COMP%] {\n  flex: 1;\n}\n\n.document-name[_ngcontent-%COMP%] {\n  font-size: 14px;\n  font-weight: 500;\n  color: #333;\n  margin-bottom: 4px;\n}\n\n.document-meta[_ngcontent-%COMP%] {\n  display: flex;\n  gap: 16px;\n  font-size: 12px;\n  color: #666;\n}\n\n.document-actions[_ngcontent-%COMP%] {\n  display: flex;\n  gap: 4px;\n}\n\n.document-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\n  width: 32px;\n  height: 32px;\n}\n\n.document-actions[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\n  font-size: 16px;\n  width: 16px;\n  height: 16px;\n}\n\n@media (max-width: 768px) {\n  .files-header[_ngcontent-%COMP%] {\n    flex-direction: column;\n    gap: 16px;\n    align-items: stretch;\n  }\n  .search-container[_ngcontent-%COMP%] {\n    width: 100%;\n  }\n  .category-grid[_ngcontent-%COMP%] {\n    grid-template-columns: repeat(2, 1fr);\n  }\n  .section-header[_ngcontent-%COMP%] {\n    flex-direction: column;\n    gap: 16px;\n    align-items: stretch;\n  }\n}\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */"]
    });
  }
}

/***/ }),

/***/ 2280:
/*!**********************************************!*\
  !*** ./src/app/finance/finance.component.ts ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   FinanceComponent: () => (/* binding */ FinanceComponent)
/* harmony export */ });
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @angular/core */ 7580);
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @angular/common */ 316);
/* harmony import */ var _angular_material_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @angular/material/button */ 4175);
/* harmony import */ var _angular_material_icon__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @angular/material/icon */ 3840);




function FinanceComponent_div_22_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](0, "div", 35);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const label_r1 = ctx.$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵadvance"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtextInterpolate"](label_r1);
  }
}
function FinanceComponent_div_24_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](0, "div", 36);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelement"](1, "div", 37)(2, "div", 38);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](3, "div", 39);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](4);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]()();
  }
  if (rf & 2) {
    const month_r2 = ctx.$implicit;
    const ctx_r2 = _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵadvance"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵstyleProp"]("height", ctx_r2.getBarHeight(month_r2.consultation), "%");
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵadvance"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵstyleProp"]("height", ctx_r2.getBarHeight(month_r2.retainer), "%");
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtextInterpolate"](month_r2.month);
  }
}
function FinanceComponent_div_42_span_11_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](0, "span", 48);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const transaction_r4 = _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵnextContext"]().$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵadvance"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtextInterpolate1"]("\u2022 ", transaction_r4.client, "");
  }
}
function FinanceComponent_div_42_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](0, "div", 40)(1, "div", 41)(2, "mat-icon");
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](4, "div", 42)(5, "div", 43);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](6);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](7, "div", 44)(8, "span", 45);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](9);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵpipe"](10, "date");
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtemplate"](11, FinanceComponent_div_42_span_11_Template, 2, 1, "span", 46);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](12, "div", 47);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](13);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]()();
  }
  if (rf & 2) {
    const transaction_r4 = ctx.$implicit;
    const ctx_r2 = _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵadvance"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵclassMap"](transaction_r4.type);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtextInterpolate"](ctx_r2.getTransactionIcon(transaction_r4.type));
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵadvance"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtextInterpolate"](transaction_r4.description);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵadvance"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtextInterpolate"](_angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵpipeBind2"](10, 9, transaction_r4.date, "MMM dd, yyyy"));
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵproperty"]("ngIf", transaction_r4.client);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵadvance"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵclassMap"](transaction_r4.type);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵadvance"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtextInterpolate1"](" ", ctx_r2.formatAmount(transaction_r4.amount), " ");
  }
}
class FinanceComponent {
  constructor() {
    this.selectedPeriod = 'month';
    this.totalRevenue = 125000;
    this.totalExpenses = 45000;
    this.netProfit = 80000;
    this.yAxisLabels = ['100', '75', '50', '25', '0'];
    this.chartData = [{
      month: '1',
      consultation: 45,
      retainer: 30
    }, {
      month: '2',
      consultation: 65,
      retainer: 40
    }, {
      month: '3',
      consultation: 35,
      retainer: 25
    }, {
      month: '4',
      consultation: 80,
      retainer: 60
    }, {
      month: '5',
      consultation: 55,
      retainer: 45
    }, {
      month: '6',
      consultation: 70,
      retainer: 50
    }, {
      month: '7',
      consultation: 90,
      retainer: 65
    }, {
      month: '8',
      consultation: 60,
      retainer: 40
    }, {
      month: '9',
      consultation: 75,
      retainer: 55
    }];
    this.recentTransactions = [{
      id: '1',
      amount: 15000,
      type: 'consultation',
      description: 'Consultation',
      date: new Date(2025, 5, 15),
      client: 'John Doe'
    }, {
      id: '2',
      amount: 5000,
      type: 'retainer',
      description: 'Retainer',
      date: new Date(2025, 5, 16),
      client: 'Jane Smith'
    }, {
      id: '3',
      amount: 2500,
      type: 'expense',
      description: 'Office Supplies',
      date: new Date(2025, 5, 14) // June 14, 2025
    }];
  }
  ngOnInit() {
    // Component initialization
  }
  selectPeriod(period) {
    this.selectedPeriod = period;
    // Update chart data based on selected period
  }
  getBarHeight(value) {
    return value / 100 * 100; // Convert to percentage
  }
  getTransactionIcon(type) {
    switch (type) {
      case 'consultation':
        return 'person';
      case 'retainer':
        return 'account_balance_wallet';
      case 'expense':
        return 'shopping_cart';
      case 'payment':
        return 'payment';
      default:
        return 'attach_money';
    }
  }
  formatAmount(amount) {
    const prefix = amount >= 0 ? '+' : '';
    return `${prefix}₱${amount.toLocaleString()}`;
  }
  formatNumber(num) {
    return num.toLocaleString();
  }
  openNewTransactionDialog() {
    // Implement new transaction dialog
  }
  static {
    this.ɵfac = function FinanceComponent_Factory(t) {
      return new (t || FinanceComponent)();
    };
  }
  static {
    this.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵdefineComponent"]({
      type: FinanceComponent,
      selectors: [["app-finance"]],
      decls: 79,
      vars: 10,
      consts: [[1, "finance-container"], [1, "finance-header"], ["mat-raised-button", "", "color", "primary", 3, "click"], [1, "finance-content"], [1, "chart-section"], [1, "chart-header"], [1, "chart-controls"], ["mat-button", "", 3, "click"], [1, "chart-container"], [1, "chart-canvas"], [1, "chart-bars"], [1, "chart-y-axis"], ["class", "y-label", 4, "ngFor", "ngForOf"], [1, "bars-container"], ["class", "bar-group", 4, "ngFor", "ngForOf"], [1, "chart-legend"], [1, "legend-item"], [1, "legend-color", "consultation"], [1, "legend-color", "retainer"], [1, "transactions-section"], [1, "section-header"], ["mat-icon-button", ""], [1, "transactions-list"], ["class", "transaction-item", 4, "ngFor", "ngForOf"], ["mat-button", "", 1, "view-all-btn"], [1, "summary-cards"], [1, "summary-card"], [1, "card-icon", "revenue"], [1, "card-content"], [1, "card-title"], [1, "card-value"], [1, "card-change", "positive"], [1, "card-icon", "expenses"], [1, "card-change", "negative"], [1, "card-icon", "profit"], [1, "y-label"], [1, "bar-group"], [1, "bar", "consultation"], [1, "bar", "retainer"], [1, "month-label"], [1, "transaction-item"], [1, "transaction-icon"], [1, "transaction-info"], [1, "transaction-description"], [1, "transaction-meta"], [1, "transaction-date"], ["class", "transaction-client", 4, "ngIf"], [1, "transaction-amount"], [1, "transaction-client"]],
      template: function FinanceComponent_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](0, "div", 0)(1, "div", 1)(2, "h1");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](3, "Finance");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](4, "button", 2);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵlistener"]("click", function FinanceComponent_Template_button_click_4_listener() {
            return ctx.openNewTransactionDialog();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](5, "mat-icon");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](6, "add");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](7, " New Transaction ");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](8, "div", 3)(9, "div", 4)(10, "div", 5)(11, "h3");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](12, "Monthly Revenue");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](13, "div", 6)(14, "button", 7);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵlistener"]("click", function FinanceComponent_Template_button_click_14_listener() {
            return ctx.selectPeriod("month");
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](15, "This month");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](16, "button", 7);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵlistener"]("click", function FinanceComponent_Template_button_click_16_listener() {
            return ctx.selectPeriod("year");
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](17, "This year");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](18, "div", 8)(19, "div", 9)(20, "div", 10)(21, "div", 11);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtemplate"](22, FinanceComponent_div_22_Template, 2, 1, "div", 12);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](23, "div", 13);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtemplate"](24, FinanceComponent_div_24_Template, 5, 5, "div", 14);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](25, "div", 15)(26, "div", 16);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelement"](27, "div", 17);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](28, "span");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](29, "Consultation");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](30, "div", 16);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelement"](31, "div", 18);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](32, "span");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](33, "Retainer");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]()()()()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](34, "div", 19)(35, "div", 20)(36, "h3");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](37, "Recent Transactions");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](38, "button", 21)(39, "mat-icon");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](40, "more_vert");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](41, "div", 22);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtemplate"](42, FinanceComponent_div_42_Template, 14, 12, "div", 23);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](43, "button", 24);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](44, "View All Transactions");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](45, "div", 25)(46, "div", 26)(47, "div", 27)(48, "mat-icon");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](49, "trending_up");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](50, "div", 28)(51, "div", 29);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](52, "Total Revenue");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](53, "div", 30);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](54);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](55, "div", 31);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](56, "+12.5% from last month");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](57, "div", 26)(58, "div", 32)(59, "mat-icon");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](60, "trending_down");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](61, "div", 28)(62, "div", 29);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](63, "Total Expenses");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](64, "div", 30);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](65);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](66, "div", 33);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](67, "+5.2% from last month");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](68, "div", 26)(69, "div", 34)(70, "mat-icon");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](71, "account_balance");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](72, "div", 28)(73, "div", 29);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](74, "Net Profit");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](75, "div", 30);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](76);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](77, "div", 31);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](78, "+18.3% from last month");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]()()()()()();
        }
        if (rf & 2) {
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵadvance"](14);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵclassProp"]("active", ctx.selectedPeriod === "month");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵadvance"](2);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵclassProp"]("active", ctx.selectedPeriod === "year");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵadvance"](6);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵproperty"]("ngForOf", ctx.yAxisLabels);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵadvance"](2);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵproperty"]("ngForOf", ctx.chartData);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵadvance"](18);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵproperty"]("ngForOf", ctx.recentTransactions);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵadvance"](12);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtextInterpolate1"]("\u20B1", ctx.formatNumber(ctx.totalRevenue), "");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵadvance"](11);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtextInterpolate1"]("\u20B1", ctx.formatNumber(ctx.totalExpenses), "");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵadvance"](11);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtextInterpolate1"]("\u20B1", ctx.formatNumber(ctx.netProfit), "");
        }
      },
      dependencies: [_angular_common__WEBPACK_IMPORTED_MODULE_1__.NgForOf, _angular_common__WEBPACK_IMPORTED_MODULE_1__.NgIf, _angular_material_button__WEBPACK_IMPORTED_MODULE_2__.MatButton, _angular_material_button__WEBPACK_IMPORTED_MODULE_2__.MatIconButton, _angular_material_icon__WEBPACK_IMPORTED_MODULE_3__.MatIcon, _angular_common__WEBPACK_IMPORTED_MODULE_1__.DatePipe],
      styles: [".finance-container[_ngcontent-%COMP%] {\n  padding: 32px;\n  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);\n  min-height: calc(100vh - 64px);\n  position: relative;\n}\n\n.finance-container[_ngcontent-%COMP%]::before {\n  content: \"\";\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: radial-gradient(circle at 25% 25%, rgba(34, 197, 94, 0.05) 0%, transparent 50%), radial-gradient(circle at 75% 75%, rgba(239, 68, 68, 0.03) 0%, transparent 50%);\n  pointer-events: none;\n}\n\n.finance-header[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 40px;\n  position: relative;\n  z-index: 1;\n}\n\n.finance-header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\n  margin: 0;\n  font-size: 36px;\n  font-weight: 700;\n  color: #1e293b;\n  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n  position: relative;\n}\n\n.finance-header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]::after {\n  content: \"\";\n  position: absolute;\n  bottom: -8px;\n  left: 0;\n  width: 80px;\n  height: 4px;\n  background: linear-gradient(90deg, #22c55e 0%, #16a34a 100%);\n  border-radius: 2px;\n}\n\n.finance-content[_ngcontent-%COMP%] {\n  display: grid;\n  grid-template-columns: 2fr 1fr;\n  grid-template-rows: auto auto;\n  gap: 24px;\n}\n\n.chart-section[_ngcontent-%COMP%] {\n  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);\n  border-radius: 24px;\n  padding: 32px;\n  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1), 0 8px 24px rgba(0, 0, 0, 0.05);\n  grid-row: span 2;\n  border: 1px solid rgba(226, 232, 240, 0.8);\n  position: relative;\n  overflow: hidden;\n  transition: all 0.3s ease;\n}\n\n.chart-section[_ngcontent-%COMP%]::before {\n  content: \"\";\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  height: 6px;\n  background: linear-gradient(90deg, #22c55e 0%, #16a34a 50%, #22c55e 100%);\n}\n\n.chart-section[_ngcontent-%COMP%]:hover {\n  transform: translateY(-4px);\n  box-shadow: 0 30px 80px rgba(0, 0, 0, 0.15), 0 12px 32px rgba(0, 0, 0, 0.08);\n}\n\n.chart-header[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 24px;\n}\n\n.chart-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\n  margin: 0;\n  font-size: 18px;\n  font-weight: 600;\n}\n\n.chart-controls[_ngcontent-%COMP%] {\n  display: flex;\n  gap: 8px;\n}\n\n.chart-controls[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\n  padding: 8px 16px;\n  border-radius: 20px;\n  font-size: 12px;\n}\n\n.chart-controls[_ngcontent-%COMP%]   button.active[_ngcontent-%COMP%] {\n  background: #1976d2;\n  color: white;\n}\n\n.chart-container[_ngcontent-%COMP%] {\n  height: 300px;\n  position: relative;\n}\n\n.chart-canvas[_ngcontent-%COMP%] {\n  height: 100%;\n  display: flex;\n  flex-direction: column;\n}\n\n.chart-bars[_ngcontent-%COMP%] {\n  flex: 1;\n  display: flex;\n  align-items: flex-end;\n  gap: 16px;\n}\n\n.chart-y-axis[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  justify-content: space-between;\n  height: 250px;\n  margin-right: 16px;\n}\n\n.y-label[_ngcontent-%COMP%] {\n  font-size: 12px;\n  color: #666;\n  text-align: right;\n}\n\n.bars-container[_ngcontent-%COMP%] {\n  flex: 1;\n  display: flex;\n  justify-content: space-between;\n  align-items: flex-end;\n  height: 250px;\n}\n\n.bar-group[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: 4px;\n  flex: 1;\n  max-width: 60px;\n}\n\n.bar[_ngcontent-%COMP%] {\n  width: 20px;\n  border-radius: 4px 4px 0 0;\n  transition: all 0.3s ease;\n}\n\n.bar.consultation[_ngcontent-%COMP%] {\n  background: #e91e63;\n  margin-right: 2px;\n}\n\n.bar.retainer[_ngcontent-%COMP%] {\n  background: #9c27b0;\n  margin-left: 2px;\n}\n\n.month-label[_ngcontent-%COMP%] {\n  font-size: 11px;\n  color: #666;\n  margin-top: 8px;\n  writing-mode: vertical-rl;\n  text-orientation: mixed;\n}\n\n.chart-legend[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: center;\n  gap: 24px;\n  margin-top: 16px;\n}\n\n.legend-item[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  font-size: 12px;\n}\n\n.legend-color[_ngcontent-%COMP%] {\n  width: 12px;\n  height: 12px;\n  border-radius: 2px;\n}\n\n.legend-color.consultation[_ngcontent-%COMP%] {\n  background: #e91e63;\n}\n\n.legend-color.retainer[_ngcontent-%COMP%] {\n  background: #9c27b0;\n}\n\n.transactions-section[_ngcontent-%COMP%] {\n  background: white;\n  border-radius: 12px;\n  padding: 24px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n}\n\n.section-header[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 20px;\n}\n\n.section-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\n  margin: 0;\n  font-size: 18px;\n  font-weight: 600;\n}\n\n.transactions-list[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  gap: 16px;\n  margin-bottom: 20px;\n}\n\n.transaction-item[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  gap: 16px;\n}\n\n.transaction-icon[_ngcontent-%COMP%] {\n  width: 40px;\n  height: 40px;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.transaction-icon.consultation[_ngcontent-%COMP%] {\n  background: #e3f2fd;\n  color: #1976d2;\n}\n\n.transaction-icon.retainer[_ngcontent-%COMP%] {\n  background: #f3e5f5;\n  color: #7b1fa2;\n}\n\n.transaction-icon.expense[_ngcontent-%COMP%] {\n  background: #ffebee;\n  color: #d32f2f;\n}\n\n.transaction-info[_ngcontent-%COMP%] {\n  flex: 1;\n}\n\n.transaction-description[_ngcontent-%COMP%] {\n  font-weight: 500;\n  margin-bottom: 4px;\n}\n\n.transaction-meta[_ngcontent-%COMP%] {\n  font-size: 12px;\n  color: #666;\n}\n\n.transaction-amount[_ngcontent-%COMP%] {\n  font-weight: 600;\n  font-size: 14px;\n}\n\n.transaction-amount.consultation[_ngcontent-%COMP%], .transaction-amount.retainer[_ngcontent-%COMP%] {\n  color: #2e7d32;\n}\n\n.transaction-amount.expense[_ngcontent-%COMP%] {\n  color: #d32f2f;\n}\n\n.view-all-btn[_ngcontent-%COMP%] {\n  width: 100%;\n  color: #1976d2;\n}\n\n.summary-cards[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  gap: 16px;\n}\n\n.summary-card[_ngcontent-%COMP%] {\n  background: white;\n  border-radius: 12px;\n  padding: 20px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n  display: flex;\n  align-items: center;\n  gap: 16px;\n}\n\n.card-icon[_ngcontent-%COMP%] {\n  width: 50px;\n  height: 50px;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.card-icon.revenue[_ngcontent-%COMP%] {\n  background: #e8f5e8;\n  color: #2e7d32;\n}\n\n.card-icon.expenses[_ngcontent-%COMP%] {\n  background: #ffebee;\n  color: #d32f2f;\n}\n\n.card-icon.profit[_ngcontent-%COMP%] {\n  background: #e3f2fd;\n  color: #1976d2;\n}\n\n.card-content[_ngcontent-%COMP%] {\n  flex: 1;\n}\n\n.card-title[_ngcontent-%COMP%] {\n  font-size: 12px;\n  color: #666;\n  margin-bottom: 4px;\n}\n\n.card-value[_ngcontent-%COMP%] {\n  font-size: 18px;\n  font-weight: 600;\n  margin-bottom: 4px;\n}\n\n.card-change[_ngcontent-%COMP%] {\n  font-size: 11px;\n}\n\n.card-change.positive[_ngcontent-%COMP%] {\n  color: #2e7d32;\n}\n\n.card-change.negative[_ngcontent-%COMP%] {\n  color: #d32f2f;\n}\n\n@media (max-width: 1024px) {\n  .finance-content[_ngcontent-%COMP%] {\n    grid-template-columns: 1fr;\n    grid-template-rows: auto auto auto;\n  }\n  .chart-section[_ngcontent-%COMP%] {\n    grid-row: span 1;\n  }\n  .summary-cards[_ngcontent-%COMP%] {\n    flex-direction: row;\n  }\n}\n@media (max-width: 768px) {\n  .finance-header[_ngcontent-%COMP%] {\n    flex-direction: column;\n    gap: 16px;\n    align-items: stretch;\n  }\n  .summary-cards[_ngcontent-%COMP%] {\n    flex-direction: column;\n  }\n  .chart-header[_ngcontent-%COMP%] {\n    flex-direction: column;\n    gap: 16px;\n    align-items: stretch;\n  }\n}\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvZmluYW5jZS9maW5hbmNlLmNvbXBvbmVudC50cyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFDSTtFQUNFLGFBQUE7RUFDQSw2REFBQTtFQUNBLDhCQUFBO0VBQ0Esa0JBQUE7QUFBTjs7QUFHSTtFQUNFLFdBQUE7RUFDQSxrQkFBQTtFQUNBLE1BQUE7RUFDQSxPQUFBO0VBQ0EsUUFBQTtFQUNBLFNBQUE7RUFDQSw0S0FDRTtFQUVGLG9CQUFBO0FBRk47O0FBS0k7RUFDRSxhQUFBO0VBQ0EsOEJBQUE7RUFDQSxtQkFBQTtFQUNBLG1CQUFBO0VBQ0Esa0JBQUE7RUFDQSxVQUFBO0FBRk47O0FBS0k7RUFDRSxTQUFBO0VBQ0EsZUFBQTtFQUNBLGdCQUFBO0VBQ0EsY0FBQTtFQUNBLHlDQUFBO0VBQ0Esa0JBQUE7QUFGTjs7QUFLSTtFQUNFLFdBQUE7RUFDQSxrQkFBQTtFQUNBLFlBQUE7RUFDQSxPQUFBO0VBQ0EsV0FBQTtFQUNBLFdBQUE7RUFDQSw0REFBQTtFQUNBLGtCQUFBO0FBRk47O0FBS0k7RUFDRSxhQUFBO0VBQ0EsOEJBQUE7RUFDQSw2QkFBQTtFQUNBLFNBQUE7QUFGTjs7QUFLSTtFQUNFLDZEQUFBO0VBQ0EsbUJBQUE7RUFDQSxhQUFBO0VBQ0EsMEVBQ0U7RUFFRixnQkFBQTtFQUNBLDBDQUFBO0VBQ0Esa0JBQUE7RUFDQSxnQkFBQTtFQUNBLHlCQUFBO0FBSk47O0FBT0k7RUFDRSxXQUFBO0VBQ0Esa0JBQUE7RUFDQSxNQUFBO0VBQ0EsT0FBQTtFQUNBLFFBQUE7RUFDQSxXQUFBO0VBQ0EseUVBQUE7QUFKTjs7QUFPSTtFQUNFLDJCQUFBO0VBQ0EsNEVBQ0U7QUFMUjs7QUFTSTtFQUNFLGFBQUE7RUFDQSw4QkFBQTtFQUNBLG1CQUFBO0VBQ0EsbUJBQUE7QUFOTjs7QUFTSTtFQUNFLFNBQUE7RUFDQSxlQUFBO0VBQ0EsZ0JBQUE7QUFOTjs7QUFTSTtFQUNFLGFBQUE7RUFDQSxRQUFBO0FBTk47O0FBU0k7RUFDRSxpQkFBQTtFQUNBLG1CQUFBO0VBQ0EsZUFBQTtBQU5OOztBQVNJO0VBQ0UsbUJBQUE7RUFDQSxZQUFBO0FBTk47O0FBU0k7RUFDRSxhQUFBO0VBQ0Esa0JBQUE7QUFOTjs7QUFTSTtFQUNFLFlBQUE7RUFDQSxhQUFBO0VBQ0Esc0JBQUE7QUFOTjs7QUFTSTtFQUNFLE9BQUE7RUFDQSxhQUFBO0VBQ0EscUJBQUE7RUFDQSxTQUFBO0FBTk47O0FBU0k7RUFDRSxhQUFBO0VBQ0Esc0JBQUE7RUFDQSw4QkFBQTtFQUNBLGFBQUE7RUFDQSxrQkFBQTtBQU5OOztBQVNJO0VBQ0UsZUFBQTtFQUNBLFdBQUE7RUFDQSxpQkFBQTtBQU5OOztBQVNJO0VBQ0UsT0FBQTtFQUNBLGFBQUE7RUFDQSw4QkFBQTtFQUNBLHFCQUFBO0VBQ0EsYUFBQTtBQU5OOztBQVNJO0VBQ0UsYUFBQTtFQUNBLHNCQUFBO0VBQ0EsbUJBQUE7RUFDQSxRQUFBO0VBQ0EsT0FBQTtFQUNBLGVBQUE7QUFOTjs7QUFTSTtFQUNFLFdBQUE7RUFDQSwwQkFBQTtFQUNBLHlCQUFBO0FBTk47O0FBU0k7RUFDRSxtQkFBQTtFQUNBLGlCQUFBO0FBTk47O0FBU0k7RUFDRSxtQkFBQTtFQUNBLGdCQUFBO0FBTk47O0FBU0k7RUFDRSxlQUFBO0VBQ0EsV0FBQTtFQUNBLGVBQUE7RUFDQSx5QkFBQTtFQUNBLHVCQUFBO0FBTk47O0FBU0k7RUFDRSxhQUFBO0VBQ0EsdUJBQUE7RUFDQSxTQUFBO0VBQ0EsZ0JBQUE7QUFOTjs7QUFTSTtFQUNFLGFBQUE7RUFDQSxtQkFBQTtFQUNBLFFBQUE7RUFDQSxlQUFBO0FBTk47O0FBU0k7RUFDRSxXQUFBO0VBQ0EsWUFBQTtFQUNBLGtCQUFBO0FBTk47O0FBU0k7RUFDRSxtQkFBQTtBQU5OOztBQVNJO0VBQ0UsbUJBQUE7QUFOTjs7QUFTSTtFQUNFLGlCQUFBO0VBQ0EsbUJBQUE7RUFDQSxhQUFBO0VBQ0Esd0NBQUE7QUFOTjs7QUFTSTtFQUNFLGFBQUE7RUFDQSw4QkFBQTtFQUNBLG1CQUFBO0VBQ0EsbUJBQUE7QUFOTjs7QUFTSTtFQUNFLFNBQUE7RUFDQSxlQUFBO0VBQ0EsZ0JBQUE7QUFOTjs7QUFTSTtFQUNFLGFBQUE7RUFDQSxzQkFBQTtFQUNBLFNBQUE7RUFDQSxtQkFBQTtBQU5OOztBQVNJO0VBQ0UsYUFBQTtFQUNBLG1CQUFBO0VBQ0EsU0FBQTtBQU5OOztBQVNJO0VBQ0UsV0FBQTtFQUNBLFlBQUE7RUFDQSxrQkFBQTtFQUNBLGFBQUE7RUFDQSxtQkFBQTtFQUNBLHVCQUFBO0FBTk47O0FBU0k7RUFDRSxtQkFBQTtFQUNBLGNBQUE7QUFOTjs7QUFTSTtFQUNFLG1CQUFBO0VBQ0EsY0FBQTtBQU5OOztBQVNJO0VBQ0UsbUJBQUE7RUFDQSxjQUFBO0FBTk47O0FBU0k7RUFDRSxPQUFBO0FBTk47O0FBU0k7RUFDRSxnQkFBQTtFQUNBLGtCQUFBO0FBTk47O0FBU0k7RUFDRSxlQUFBO0VBQ0EsV0FBQTtBQU5OOztBQVNJO0VBQ0UsZ0JBQUE7RUFDQSxlQUFBO0FBTk47O0FBU0k7O0VBRUUsY0FBQTtBQU5OOztBQVNJO0VBQ0UsY0FBQTtBQU5OOztBQVNJO0VBQ0UsV0FBQTtFQUNBLGNBQUE7QUFOTjs7QUFTSTtFQUNFLGFBQUE7RUFDQSxzQkFBQTtFQUNBLFNBQUE7QUFOTjs7QUFTSTtFQUNFLGlCQUFBO0VBQ0EsbUJBQUE7RUFDQSxhQUFBO0VBQ0Esd0NBQUE7RUFDQSxhQUFBO0VBQ0EsbUJBQUE7RUFDQSxTQUFBO0FBTk47O0FBU0k7RUFDRSxXQUFBO0VBQ0EsWUFBQTtFQUNBLGtCQUFBO0VBQ0EsYUFBQTtFQUNBLG1CQUFBO0VBQ0EsdUJBQUE7QUFOTjs7QUFTSTtFQUNFLG1CQUFBO0VBQ0EsY0FBQTtBQU5OOztBQVNJO0VBQ0UsbUJBQUE7RUFDQSxjQUFBO0FBTk47O0FBU0k7RUFDRSxtQkFBQTtFQUNBLGNBQUE7QUFOTjs7QUFTSTtFQUNFLE9BQUE7QUFOTjs7QUFTSTtFQUNFLGVBQUE7RUFDQSxXQUFBO0VBQ0Esa0JBQUE7QUFOTjs7QUFTSTtFQUNFLGVBQUE7RUFDQSxnQkFBQTtFQUNBLGtCQUFBO0FBTk47O0FBU0k7RUFDRSxlQUFBO0FBTk47O0FBU0k7RUFDRSxjQUFBO0FBTk47O0FBU0k7RUFDRSxjQUFBO0FBTk47O0FBU0k7RUFDRTtJQUNFLDBCQUFBO0lBQ0Esa0NBQUE7RUFOTjtFQVNJO0lBQ0UsZ0JBQUE7RUFQTjtFQVVJO0lBQ0UsbUJBQUE7RUFSTjtBQUNGO0FBV0k7RUFDRTtJQUNFLHNCQUFBO0lBQ0EsU0FBQTtJQUNBLG9CQUFBO0VBVE47RUFZSTtJQUNFLHNCQUFBO0VBVk47RUFhSTtJQUNFLHNCQUFBO0lBQ0EsU0FBQTtJQUNBLG9CQUFBO0VBWE47QUFDRiIsInNvdXJjZXNDb250ZW50IjpbIlxuICAgIC5maW5hbmNlLWNvbnRhaW5lciB7XG4gICAgICBwYWRkaW5nOiAzMnB4O1xuICAgICAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgI2Y4ZmFmYyAwJSwgI2UyZThmMCAxMDAlKTtcbiAgICAgIG1pbi1oZWlnaHQ6IGNhbGMoMTAwdmggLSA2NHB4KTtcbiAgICAgIHBvc2l0aW9uOiByZWxhdGl2ZTtcbiAgICB9XG5cbiAgICAuZmluYW5jZS1jb250YWluZXI6OmJlZm9yZSB7XG4gICAgICBjb250ZW50OiAnJztcbiAgICAgIHBvc2l0aW9uOiBhYnNvbHV0ZTtcbiAgICAgIHRvcDogMDtcbiAgICAgIGxlZnQ6IDA7XG4gICAgICByaWdodDogMDtcbiAgICAgIGJvdHRvbTogMDtcbiAgICAgIGJhY2tncm91bmQ6XG4gICAgICAgIHJhZGlhbC1ncmFkaWVudChjaXJjbGUgYXQgMjUlIDI1JSwgcmdiYSgzNCwgMTk3LCA5NCwgMC4wNSkgMCUsIHRyYW5zcGFyZW50IDUwJSksXG4gICAgICAgIHJhZGlhbC1ncmFkaWVudChjaXJjbGUgYXQgNzUlIDc1JSwgcmdiYSgyMzksIDY4LCA2OCwgMC4wMykgMCUsIHRyYW5zcGFyZW50IDUwJSk7XG4gICAgICBwb2ludGVyLWV2ZW50czogbm9uZTtcbiAgICB9XG5cbiAgICAuZmluYW5jZS1oZWFkZXIge1xuICAgICAgZGlzcGxheTogZmxleDtcbiAgICAgIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjtcbiAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gICAgICBtYXJnaW4tYm90dG9tOiA0MHB4O1xuICAgICAgcG9zaXRpb246IHJlbGF0aXZlO1xuICAgICAgei1pbmRleDogMTtcbiAgICB9XG5cbiAgICAuZmluYW5jZS1oZWFkZXIgaDEge1xuICAgICAgbWFyZ2luOiAwO1xuICAgICAgZm9udC1zaXplOiAzNnB4O1xuICAgICAgZm9udC13ZWlnaHQ6IDcwMDtcbiAgICAgIGNvbG9yOiAjMWUyOTNiO1xuICAgICAgdGV4dC1zaGFkb3c6IDAgMnB4IDRweCByZ2JhKDAsIDAsIDAsIDAuMSk7XG4gICAgICBwb3NpdGlvbjogcmVsYXRpdmU7XG4gICAgfVxuXG4gICAgLmZpbmFuY2UtaGVhZGVyIGgxOjphZnRlciB7XG4gICAgICBjb250ZW50OiAnJztcbiAgICAgIHBvc2l0aW9uOiBhYnNvbHV0ZTtcbiAgICAgIGJvdHRvbTogLThweDtcbiAgICAgIGxlZnQ6IDA7XG4gICAgICB3aWR0aDogODBweDtcbiAgICAgIGhlaWdodDogNHB4O1xuICAgICAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDkwZGVnLCAjMjJjNTVlIDAlLCAjMTZhMzRhIDEwMCUpO1xuICAgICAgYm9yZGVyLXJhZGl1czogMnB4O1xuICAgIH1cblxuICAgIC5maW5hbmNlLWNvbnRlbnQge1xuICAgICAgZGlzcGxheTogZ3JpZDtcbiAgICAgIGdyaWQtdGVtcGxhdGUtY29sdW1uczogMmZyIDFmcjtcbiAgICAgIGdyaWQtdGVtcGxhdGUtcm93czogYXV0byBhdXRvO1xuICAgICAgZ2FwOiAyNHB4O1xuICAgIH1cblxuICAgIC5jaGFydC1zZWN0aW9uIHtcbiAgICAgIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICNmZmZmZmYgMCUsICNmOGZhZmMgMTAwJSk7XG4gICAgICBib3JkZXItcmFkaXVzOiAyNHB4O1xuICAgICAgcGFkZGluZzogMzJweDtcbiAgICAgIGJveC1zaGFkb3c6XG4gICAgICAgIDAgMjBweCA2MHB4IHJnYmEoMCwgMCwgMCwgMC4xKSxcbiAgICAgICAgMCA4cHggMjRweCByZ2JhKDAsIDAsIDAsIDAuMDUpO1xuICAgICAgZ3JpZC1yb3c6IHNwYW4gMjtcbiAgICAgIGJvcmRlcjogMXB4IHNvbGlkIHJnYmEoMjI2LCAyMzIsIDI0MCwgMC44KTtcbiAgICAgIHBvc2l0aW9uOiByZWxhdGl2ZTtcbiAgICAgIG92ZXJmbG93OiBoaWRkZW47XG4gICAgICB0cmFuc2l0aW9uOiBhbGwgMC4zcyBlYXNlO1xuICAgIH1cblxuICAgIC5jaGFydC1zZWN0aW9uOjpiZWZvcmUge1xuICAgICAgY29udGVudDogJyc7XG4gICAgICBwb3NpdGlvbjogYWJzb2x1dGU7XG4gICAgICB0b3A6IDA7XG4gICAgICBsZWZ0OiAwO1xuICAgICAgcmlnaHQ6IDA7XG4gICAgICBoZWlnaHQ6IDZweDtcbiAgICAgIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCg5MGRlZywgIzIyYzU1ZSAwJSwgIzE2YTM0YSA1MCUsICMyMmM1NWUgMTAwJSk7XG4gICAgfVxuXG4gICAgLmNoYXJ0LXNlY3Rpb246aG92ZXIge1xuICAgICAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKC00cHgpO1xuICAgICAgYm94LXNoYWRvdzpcbiAgICAgICAgMCAzMHB4IDgwcHggcmdiYSgwLCAwLCAwLCAwLjE1KSxcbiAgICAgICAgMCAxMnB4IDMycHggcmdiYSgwLCAwLCAwLCAwLjA4KTtcbiAgICB9XG5cbiAgICAuY2hhcnQtaGVhZGVyIHtcbiAgICAgIGRpc3BsYXk6IGZsZXg7XG4gICAgICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47XG4gICAgICBhbGlnbi1pdGVtczogY2VudGVyO1xuICAgICAgbWFyZ2luLWJvdHRvbTogMjRweDtcbiAgICB9XG5cbiAgICAuY2hhcnQtaGVhZGVyIGgzIHtcbiAgICAgIG1hcmdpbjogMDtcbiAgICAgIGZvbnQtc2l6ZTogMThweDtcbiAgICAgIGZvbnQtd2VpZ2h0OiA2MDA7XG4gICAgfVxuXG4gICAgLmNoYXJ0LWNvbnRyb2xzIHtcbiAgICAgIGRpc3BsYXk6IGZsZXg7XG4gICAgICBnYXA6IDhweDtcbiAgICB9XG5cbiAgICAuY2hhcnQtY29udHJvbHMgYnV0dG9uIHtcbiAgICAgIHBhZGRpbmc6IDhweCAxNnB4O1xuICAgICAgYm9yZGVyLXJhZGl1czogMjBweDtcbiAgICAgIGZvbnQtc2l6ZTogMTJweDtcbiAgICB9XG5cbiAgICAuY2hhcnQtY29udHJvbHMgYnV0dG9uLmFjdGl2ZSB7XG4gICAgICBiYWNrZ3JvdW5kOiAjMTk3NmQyO1xuICAgICAgY29sb3I6IHdoaXRlO1xuICAgIH1cblxuICAgIC5jaGFydC1jb250YWluZXIge1xuICAgICAgaGVpZ2h0OiAzMDBweDtcbiAgICAgIHBvc2l0aW9uOiByZWxhdGl2ZTtcbiAgICB9XG5cbiAgICAuY2hhcnQtY2FudmFzIHtcbiAgICAgIGhlaWdodDogMTAwJTtcbiAgICAgIGRpc3BsYXk6IGZsZXg7XG4gICAgICBmbGV4LWRpcmVjdGlvbjogY29sdW1uO1xuICAgIH1cblxuICAgIC5jaGFydC1iYXJzIHtcbiAgICAgIGZsZXg6IDE7XG4gICAgICBkaXNwbGF5OiBmbGV4O1xuICAgICAgYWxpZ24taXRlbXM6IGZsZXgtZW5kO1xuICAgICAgZ2FwOiAxNnB4O1xuICAgIH1cblxuICAgIC5jaGFydC15LWF4aXMge1xuICAgICAgZGlzcGxheTogZmxleDtcbiAgICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47XG4gICAgICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47XG4gICAgICBoZWlnaHQ6IDI1MHB4O1xuICAgICAgbWFyZ2luLXJpZ2h0OiAxNnB4O1xuICAgIH1cblxuICAgIC55LWxhYmVsIHtcbiAgICAgIGZvbnQtc2l6ZTogMTJweDtcbiAgICAgIGNvbG9yOiAjNjY2O1xuICAgICAgdGV4dC1hbGlnbjogcmlnaHQ7XG4gICAgfVxuXG4gICAgLmJhcnMtY29udGFpbmVyIHtcbiAgICAgIGZsZXg6IDE7XG4gICAgICBkaXNwbGF5OiBmbGV4O1xuICAgICAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuO1xuICAgICAgYWxpZ24taXRlbXM6IGZsZXgtZW5kO1xuICAgICAgaGVpZ2h0OiAyNTBweDtcbiAgICB9XG5cbiAgICAuYmFyLWdyb3VwIHtcbiAgICAgIGRpc3BsYXk6IGZsZXg7XG4gICAgICBmbGV4LWRpcmVjdGlvbjogY29sdW1uO1xuICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAgICAgIGdhcDogNHB4O1xuICAgICAgZmxleDogMTtcbiAgICAgIG1heC13aWR0aDogNjBweDtcbiAgICB9XG5cbiAgICAuYmFyIHtcbiAgICAgIHdpZHRoOiAyMHB4O1xuICAgICAgYm9yZGVyLXJhZGl1czogNHB4IDRweCAwIDA7XG4gICAgICB0cmFuc2l0aW9uOiBhbGwgMC4zcyBlYXNlO1xuICAgIH1cblxuICAgIC5iYXIuY29uc3VsdGF0aW9uIHtcbiAgICAgIGJhY2tncm91bmQ6ICNlOTFlNjM7XG4gICAgICBtYXJnaW4tcmlnaHQ6IDJweDtcbiAgICB9XG5cbiAgICAuYmFyLnJldGFpbmVyIHtcbiAgICAgIGJhY2tncm91bmQ6ICM5YzI3YjA7XG4gICAgICBtYXJnaW4tbGVmdDogMnB4O1xuICAgIH1cblxuICAgIC5tb250aC1sYWJlbCB7XG4gICAgICBmb250LXNpemU6IDExcHg7XG4gICAgICBjb2xvcjogIzY2NjtcbiAgICAgIG1hcmdpbi10b3A6IDhweDtcbiAgICAgIHdyaXRpbmctbW9kZTogdmVydGljYWwtcmw7XG4gICAgICB0ZXh0LW9yaWVudGF0aW9uOiBtaXhlZDtcbiAgICB9XG5cbiAgICAuY2hhcnQtbGVnZW5kIHtcbiAgICAgIGRpc3BsYXk6IGZsZXg7XG4gICAgICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjtcbiAgICAgIGdhcDogMjRweDtcbiAgICAgIG1hcmdpbi10b3A6IDE2cHg7XG4gICAgfVxuXG4gICAgLmxlZ2VuZC1pdGVtIHtcbiAgICAgIGRpc3BsYXk6IGZsZXg7XG4gICAgICBhbGlnbi1pdGVtczogY2VudGVyO1xuICAgICAgZ2FwOiA4cHg7XG4gICAgICBmb250LXNpemU6IDEycHg7XG4gICAgfVxuXG4gICAgLmxlZ2VuZC1jb2xvciB7XG4gICAgICB3aWR0aDogMTJweDtcbiAgICAgIGhlaWdodDogMTJweDtcbiAgICAgIGJvcmRlci1yYWRpdXM6IDJweDtcbiAgICB9XG5cbiAgICAubGVnZW5kLWNvbG9yLmNvbnN1bHRhdGlvbiB7XG4gICAgICBiYWNrZ3JvdW5kOiAjZTkxZTYzO1xuICAgIH1cblxuICAgIC5sZWdlbmQtY29sb3IucmV0YWluZXIge1xuICAgICAgYmFja2dyb3VuZDogIzljMjdiMDtcbiAgICB9XG5cbiAgICAudHJhbnNhY3Rpb25zLXNlY3Rpb24ge1xuICAgICAgYmFja2dyb3VuZDogd2hpdGU7XG4gICAgICBib3JkZXItcmFkaXVzOiAxMnB4O1xuICAgICAgcGFkZGluZzogMjRweDtcbiAgICAgIGJveC1zaGFkb3c6IDAgMnB4IDhweCByZ2JhKDAsMCwwLDAuMSk7XG4gICAgfVxuXG4gICAgLnNlY3Rpb24taGVhZGVyIHtcbiAgICAgIGRpc3BsYXk6IGZsZXg7XG4gICAgICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47XG4gICAgICBhbGlnbi1pdGVtczogY2VudGVyO1xuICAgICAgbWFyZ2luLWJvdHRvbTogMjBweDtcbiAgICB9XG5cbiAgICAuc2VjdGlvbi1oZWFkZXIgaDMge1xuICAgICAgbWFyZ2luOiAwO1xuICAgICAgZm9udC1zaXplOiAxOHB4O1xuICAgICAgZm9udC13ZWlnaHQ6IDYwMDtcbiAgICB9XG5cbiAgICAudHJhbnNhY3Rpb25zLWxpc3Qge1xuICAgICAgZGlzcGxheTogZmxleDtcbiAgICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47XG4gICAgICBnYXA6IDE2cHg7XG4gICAgICBtYXJnaW4tYm90dG9tOiAyMHB4O1xuICAgIH1cblxuICAgIC50cmFuc2FjdGlvbi1pdGVtIHtcbiAgICAgIGRpc3BsYXk6IGZsZXg7XG4gICAgICBhbGlnbi1pdGVtczogY2VudGVyO1xuICAgICAgZ2FwOiAxNnB4O1xuICAgIH1cblxuICAgIC50cmFuc2FjdGlvbi1pY29uIHtcbiAgICAgIHdpZHRoOiA0MHB4O1xuICAgICAgaGVpZ2h0OiA0MHB4O1xuICAgICAgYm9yZGVyLXJhZGl1czogNTAlO1xuICAgICAgZGlzcGxheTogZmxleDtcbiAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gICAgICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjtcbiAgICB9XG5cbiAgICAudHJhbnNhY3Rpb24taWNvbi5jb25zdWx0YXRpb24ge1xuICAgICAgYmFja2dyb3VuZDogI2UzZjJmZDtcbiAgICAgIGNvbG9yOiAjMTk3NmQyO1xuICAgIH1cblxuICAgIC50cmFuc2FjdGlvbi1pY29uLnJldGFpbmVyIHtcbiAgICAgIGJhY2tncm91bmQ6ICNmM2U1ZjU7XG4gICAgICBjb2xvcjogIzdiMWZhMjtcbiAgICB9XG5cbiAgICAudHJhbnNhY3Rpb24taWNvbi5leHBlbnNlIHtcbiAgICAgIGJhY2tncm91bmQ6ICNmZmViZWU7XG4gICAgICBjb2xvcjogI2QzMmYyZjtcbiAgICB9XG5cbiAgICAudHJhbnNhY3Rpb24taW5mbyB7XG4gICAgICBmbGV4OiAxO1xuICAgIH1cblxuICAgIC50cmFuc2FjdGlvbi1kZXNjcmlwdGlvbiB7XG4gICAgICBmb250LXdlaWdodDogNTAwO1xuICAgICAgbWFyZ2luLWJvdHRvbTogNHB4O1xuICAgIH1cblxuICAgIC50cmFuc2FjdGlvbi1tZXRhIHtcbiAgICAgIGZvbnQtc2l6ZTogMTJweDtcbiAgICAgIGNvbG9yOiAjNjY2O1xuICAgIH1cblxuICAgIC50cmFuc2FjdGlvbi1hbW91bnQge1xuICAgICAgZm9udC13ZWlnaHQ6IDYwMDtcbiAgICAgIGZvbnQtc2l6ZTogMTRweDtcbiAgICB9XG5cbiAgICAudHJhbnNhY3Rpb24tYW1vdW50LmNvbnN1bHRhdGlvbixcbiAgICAudHJhbnNhY3Rpb24tYW1vdW50LnJldGFpbmVyIHtcbiAgICAgIGNvbG9yOiAjMmU3ZDMyO1xuICAgIH1cblxuICAgIC50cmFuc2FjdGlvbi1hbW91bnQuZXhwZW5zZSB7XG4gICAgICBjb2xvcjogI2QzMmYyZjtcbiAgICB9XG5cbiAgICAudmlldy1hbGwtYnRuIHtcbiAgICAgIHdpZHRoOiAxMDAlO1xuICAgICAgY29sb3I6ICMxOTc2ZDI7XG4gICAgfVxuXG4gICAgLnN1bW1hcnktY2FyZHMge1xuICAgICAgZGlzcGxheTogZmxleDtcbiAgICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47XG4gICAgICBnYXA6IDE2cHg7XG4gICAgfVxuXG4gICAgLnN1bW1hcnktY2FyZCB7XG4gICAgICBiYWNrZ3JvdW5kOiB3aGl0ZTtcbiAgICAgIGJvcmRlci1yYWRpdXM6IDEycHg7XG4gICAgICBwYWRkaW5nOiAyMHB4O1xuICAgICAgYm94LXNoYWRvdzogMCAycHggOHB4IHJnYmEoMCwwLDAsMC4xKTtcbiAgICAgIGRpc3BsYXk6IGZsZXg7XG4gICAgICBhbGlnbi1pdGVtczogY2VudGVyO1xuICAgICAgZ2FwOiAxNnB4O1xuICAgIH1cblxuICAgIC5jYXJkLWljb24ge1xuICAgICAgd2lkdGg6IDUwcHg7XG4gICAgICBoZWlnaHQ6IDUwcHg7XG4gICAgICBib3JkZXItcmFkaXVzOiA1MCU7XG4gICAgICBkaXNwbGF5OiBmbGV4O1xuICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAgICAgIGp1c3RpZnktY29udGVudDogY2VudGVyO1xuICAgIH1cblxuICAgIC5jYXJkLWljb24ucmV2ZW51ZSB7XG4gICAgICBiYWNrZ3JvdW5kOiAjZThmNWU4O1xuICAgICAgY29sb3I6ICMyZTdkMzI7XG4gICAgfVxuXG4gICAgLmNhcmQtaWNvbi5leHBlbnNlcyB7XG4gICAgICBiYWNrZ3JvdW5kOiAjZmZlYmVlO1xuICAgICAgY29sb3I6ICNkMzJmMmY7XG4gICAgfVxuXG4gICAgLmNhcmQtaWNvbi5wcm9maXQge1xuICAgICAgYmFja2dyb3VuZDogI2UzZjJmZDtcbiAgICAgIGNvbG9yOiAjMTk3NmQyO1xuICAgIH1cblxuICAgIC5jYXJkLWNvbnRlbnQge1xuICAgICAgZmxleDogMTtcbiAgICB9XG5cbiAgICAuY2FyZC10aXRsZSB7XG4gICAgICBmb250LXNpemU6IDEycHg7XG4gICAgICBjb2xvcjogIzY2NjtcbiAgICAgIG1hcmdpbi1ib3R0b206IDRweDtcbiAgICB9XG5cbiAgICAuY2FyZC12YWx1ZSB7XG4gICAgICBmb250LXNpemU6IDE4cHg7XG4gICAgICBmb250LXdlaWdodDogNjAwO1xuICAgICAgbWFyZ2luLWJvdHRvbTogNHB4O1xuICAgIH1cblxuICAgIC5jYXJkLWNoYW5nZSB7XG4gICAgICBmb250LXNpemU6IDExcHg7XG4gICAgfVxuXG4gICAgLmNhcmQtY2hhbmdlLnBvc2l0aXZlIHtcbiAgICAgIGNvbG9yOiAjMmU3ZDMyO1xuICAgIH1cblxuICAgIC5jYXJkLWNoYW5nZS5uZWdhdGl2ZSB7XG4gICAgICBjb2xvcjogI2QzMmYyZjtcbiAgICB9XG5cbiAgICBAbWVkaWEgKG1heC13aWR0aDogMTAyNHB4KSB7XG4gICAgICAuZmluYW5jZS1jb250ZW50IHtcbiAgICAgICAgZ3JpZC10ZW1wbGF0ZS1jb2x1bW5zOiAxZnI7XG4gICAgICAgIGdyaWQtdGVtcGxhdGUtcm93czogYXV0byBhdXRvIGF1dG87XG4gICAgICB9XG5cbiAgICAgIC5jaGFydC1zZWN0aW9uIHtcbiAgICAgICAgZ3JpZC1yb3c6IHNwYW4gMTtcbiAgICAgIH1cblxuICAgICAgLnN1bW1hcnktY2FyZHMge1xuICAgICAgICBmbGV4LWRpcmVjdGlvbjogcm93O1xuICAgICAgfVxuICAgIH1cblxuICAgIEBtZWRpYSAobWF4LXdpZHRoOiA3NjhweCkge1xuICAgICAgLmZpbmFuY2UtaGVhZGVyIHtcbiAgICAgICAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjtcbiAgICAgICAgZ2FwOiAxNnB4O1xuICAgICAgICBhbGlnbi1pdGVtczogc3RyZXRjaDtcbiAgICAgIH1cblxuICAgICAgLnN1bW1hcnktY2FyZHMge1xuICAgICAgICBmbGV4LWRpcmVjdGlvbjogY29sdW1uO1xuICAgICAgfVxuXG4gICAgICAuY2hhcnQtaGVhZGVyIHtcbiAgICAgICAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjtcbiAgICAgICAgZ2FwOiAxNnB4O1xuICAgICAgICBhbGlnbi1pdGVtczogc3RyZXRjaDtcbiAgICAgIH1cbiAgICB9XG4gICJdLCJzb3VyY2VSb290IjoiIn0= */"]
    });
  }
}

/***/ }),

/***/ 3736:
/*!******************************************************!*\
  !*** ./src/app/link-lawyer/link-lawyer.component.ts ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   LinkLawyerComponent: () => (/* binding */ LinkLawyerComponent)
/* harmony export */ });
/* harmony import */ var C_Users_acer_Desktop_angularprojects_veritus_Veritus_veritus_secretary_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js */ 9204);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @angular/core */ 7580);
/* harmony import */ var _angular_router__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @angular/router */ 5072);
/* harmony import */ var _services_firebase_service__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../services/firebase.service */ 8287);
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @angular/common */ 316);
/* harmony import */ var _angular_forms__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @angular/forms */ 4456);
/* harmony import */ var _angular_material_button__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @angular/material/button */ 4175);
/* harmony import */ var _angular_material_card__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @angular/material/card */ 3777);
/* harmony import */ var _angular_material_input__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @angular/material/input */ 5541);
/* harmony import */ var _angular_material_form_field__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @angular/material/form-field */ 4950);
/* harmony import */ var _angular_material_icon__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @angular/material/icon */ 3840);
/* harmony import */ var _angular_material_chips__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @angular/material/chips */ 2772);












function LinkLawyerComponent_div_28_mat_card_4_mat_chip_11_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](0, "mat-chip", 18);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](1, " Calendar ");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
  }
}
function LinkLawyerComponent_div_28_mat_card_4_mat_chip_12_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](0, "mat-chip", 18);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](1, " Files ");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
  }
}
function LinkLawyerComponent_div_28_mat_card_4_mat_chip_13_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](0, "mat-chip", 18);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](1, " Cases ");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
  }
}
function LinkLawyerComponent_div_28_mat_card_4_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](0, "mat-card", 12)(1, "mat-card-header")(2, "mat-card-title");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](4, "mat-card-subtitle");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](5);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](6, "mat-card-content")(7, "div", 13)(8, "h4");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](9, "Your Permissions:");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](10, "div", 14);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtemplate"](11, LinkLawyerComponent_div_28_mat_card_4_mat_chip_11_Template, 2, 0, "mat-chip", 15)(12, LinkLawyerComponent_div_28_mat_card_4_mat_chip_12_Template, 2, 0, "mat-chip", 15)(13, LinkLawyerComponent_div_28_mat_card_4_mat_chip_13_Template, 2, 0, "mat-chip", 15);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()()();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](14, "mat-card-actions")(15, "button", 16);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](16, "Access Dashboard");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](17, "button", 17);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](18, "View Details");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()()();
  }
  if (rf & 2) {
    const lawyer_r1 = ctx.$implicit;
    const ctx_r1 = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵnextContext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtextInterpolate"](lawyer_r1.lawyerName);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtextInterpolate1"]("Linked on ", ctx_r1.formatDate(lawyer_r1.createdAt), "");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](6);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵproperty"]("ngIf", lawyer_r1.permissions.canManageCalendar);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵproperty"]("ngIf", lawyer_r1.permissions.canManageFiles);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵproperty"]("ngIf", lawyer_r1.permissions.canManageCases);
  }
}
function LinkLawyerComponent_div_28_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](0, "div", 9)(1, "h2");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](2, "Linked Lawyers");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](3, "div", 10);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtemplate"](4, LinkLawyerComponent_div_28_mat_card_4_Template, 19, 5, "mat-card", 11);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()();
  }
  if (rf & 2) {
    const ctx_r1 = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](4);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵproperty"]("ngForOf", ctx_r1.linkedLawyers);
  }
}
class LinkLawyerComponent {
  constructor(router, firebaseService) {
    this.router = router;
    this.firebaseService = firebaseService;
    this.linkedLawyers = [];
    this.assistantCode = '';
    this.codePreview = null;
    this.isLinking = false;
    this.isLoading = false;
  }
  ngOnInit() {
    this.loadLinkedLawyers();
  }
  loadLinkedLawyers() {
    var _this = this;
    return (0,C_Users_acer_Desktop_angularprojects_veritus_Veritus_veritus_secretary_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__["default"])(function* () {
      _this.isLoading = true;
      const currentUser = _this.firebaseService.getCurrentUser();
      if (currentUser) {
        try {
          _this.linkedLawyers = yield _this.firebaseService.getLinkedLawyers(currentUser.uid);
        } catch (error) {
          console.error('Error loading linked lawyers:', error);
        }
      }
      _this.isLoading = false;
    })();
  }
  onCodeInput(event) {
    const value = event.target.value.toUpperCase();
    this.assistantCode = value;
    if (this.codePreview) {
      this.codePreview = null;
    }
  }
  isValidCode() {
    return this.assistantCode.length === 8;
  }
  linkToLawyer() {
    var _this2 = this;
    return (0,C_Users_acer_Desktop_angularprojects_veritus_Veritus_veritus_secretary_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__["default"])(function* () {
      if (!_this2.isValidCode()) return;
      const currentUser = _this2.firebaseService.getCurrentUser();
      if (!currentUser) return;
      _this2.isLinking = true;
      try {
        const codeData = yield _this2.firebaseService.validateAssistantCode(_this2.assistantCode);
        if (!codeData) {
          alert('Invalid or expired code');
          _this2.isLinking = false;
          return;
        }
        const alreadyLinked = _this2.linkedLawyers.some(link => link.lawyerId === codeData.lawyerId);
        if (alreadyLinked) {
          alert('You are already linked to this lawyer');
          _this2.isLinking = false;
          return;
        }
        _this2.codePreview = codeData;
        _this2.isLinking = false;
      } catch (error) {
        console.error('Error validating code:', error);
        alert('Error validating code');
        _this2.isLinking = false;
      }
    })();
  }
  confirmLink() {
    var _this3 = this;
    return (0,C_Users_acer_Desktop_angularprojects_veritus_Veritus_veritus_secretary_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__["default"])(function* () {
      if (!_this3.codePreview) return;
      const currentUser = _this3.firebaseService.getCurrentUser();
      if (!currentUser) return;
      _this3.isLinking = true;
      try {
        const link = yield _this3.firebaseService.useAssistantCode(_this3.assistantCode, currentUser.uid);
        _this3.linkedLawyers.unshift(link);
        _this3.assistantCode = '';
        _this3.codePreview = null;
        alert(`Successfully linked to ${link.lawyerName}`);
      } catch (error) {
        console.error('Error linking to lawyer:', error);
        alert('Failed to link to lawyer');
      } finally {
        _this3.isLinking = false;
      }
    })();
  }
  cancelPreview() {
    this.codePreview = null;
    this.assistantCode = '';
  }
  switchToLawyer(lawyer) {
    localStorage.setItem('selectedLawyerContext', JSON.stringify({
      lawyerId: lawyer.lawyerId,
      lawyerName: lawyer.lawyerName,
      permissions: lawyer.permissions
    }));
    this.router.navigate(['/dashboard']);
  }
  formatDate(date) {
    return new Date(date).toLocaleDateString();
  }
  static {
    this.ɵfac = function LinkLawyerComponent_Factory(t) {
      return new (t || LinkLawyerComponent)(_angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵdirectiveInject"](_angular_router__WEBPACK_IMPORTED_MODULE_3__.Router), _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵdirectiveInject"](_services_firebase_service__WEBPACK_IMPORTED_MODULE_1__.FirebaseService));
    };
  }
  static {
    this.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵdefineComponent"]({
      type: LinkLawyerComponent,
      selectors: [["app-link-lawyer"]],
      decls: 29,
      vars: 3,
      consts: [[1, "link-lawyer-container"], [1, "header-section"], [1, "form-section"], [1, "link-card"], ["appearance", "outline", 1, "full-width"], ["matInput", "", "placeholder", "XXXXXXXX", "maxlength", "8", 1, "code-input", 3, "ngModelChange", "input", "ngModel"], [1, "code-info"], ["mat-raised-button", "", "color", "primary", 1, "full-width", 3, "click", "disabled"], ["class", "linked-lawyers-section", 4, "ngIf"], [1, "linked-lawyers-section"], [1, "lawyers-grid"], ["class", "lawyer-card", 4, "ngFor", "ngForOf"], [1, "lawyer-card"], [1, "permissions"], [1, "permission-chips"], ["color", "primary", "selected", "", 4, "ngIf"], ["mat-button", "", "color", "primary"], ["mat-button", ""], ["color", "primary", "selected", ""]],
      template: function LinkLawyerComponent_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](0, "div", 0)(1, "div", 1)(2, "h1");
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](3, "Link to Lawyer");
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](4, "p");
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](5, "Enter the assistant code provided by your lawyer");
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](6, "div", 2)(7, "mat-card", 3)(8, "mat-card-header")(9, "mat-card-title");
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](10, "Assistant Code");
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](11, "mat-card-subtitle");
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](12, "8-character code from lawyer");
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](13, "mat-card-content")(14, "mat-form-field", 4)(15, "mat-label");
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](16, "Enter Code");
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](17, "input", 5);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtwoWayListener"]("ngModelChange", function LinkLawyerComponent_Template_input_ngModelChange_17_listener($event) {
            _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtwoWayBindingSet"](ctx.assistantCode, $event) || (ctx.assistantCode = $event);
            return $event;
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵlistener"]("input", function LinkLawyerComponent_Template_input_input_17_listener($event) {
            return ctx.onCodeInput($event);
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](18, "div", 6)(19, "mat-icon");
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](20, "info");
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](21, "span");
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](22, "Codes are 8 characters long and expire after 24 hours");
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](23, "mat-card-actions")(24, "button", 7);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵlistener"]("click", function LinkLawyerComponent_Template_button_click_24_listener() {
            return ctx.linkToLawyer();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](25, "mat-icon");
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](26, "link");
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](27, " Link to Lawyer ");
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtemplate"](28, LinkLawyerComponent_div_28_Template, 5, 1, "div", 8);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
        }
        if (rf & 2) {
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](17);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtwoWayProperty"]("ngModel", ctx.assistantCode);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](7);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵproperty"]("disabled", !ctx.isValidCode());
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](4);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵproperty"]("ngIf", ctx.linkedLawyers.length > 0);
        }
      },
      dependencies: [_angular_common__WEBPACK_IMPORTED_MODULE_4__.NgForOf, _angular_common__WEBPACK_IMPORTED_MODULE_4__.NgIf, _angular_forms__WEBPACK_IMPORTED_MODULE_5__.DefaultValueAccessor, _angular_forms__WEBPACK_IMPORTED_MODULE_5__.NgControlStatus, _angular_forms__WEBPACK_IMPORTED_MODULE_5__.MaxLengthValidator, _angular_forms__WEBPACK_IMPORTED_MODULE_5__.NgModel, _angular_material_button__WEBPACK_IMPORTED_MODULE_6__.MatButton, _angular_material_card__WEBPACK_IMPORTED_MODULE_7__.MatCard, _angular_material_card__WEBPACK_IMPORTED_MODULE_7__.MatCardActions, _angular_material_card__WEBPACK_IMPORTED_MODULE_7__.MatCardContent, _angular_material_card__WEBPACK_IMPORTED_MODULE_7__.MatCardHeader, _angular_material_card__WEBPACK_IMPORTED_MODULE_7__.MatCardSubtitle, _angular_material_card__WEBPACK_IMPORTED_MODULE_7__.MatCardTitle, _angular_material_input__WEBPACK_IMPORTED_MODULE_8__.MatInput, _angular_material_form_field__WEBPACK_IMPORTED_MODULE_9__.MatFormField, _angular_material_form_field__WEBPACK_IMPORTED_MODULE_9__.MatLabel, _angular_material_icon__WEBPACK_IMPORTED_MODULE_10__.MatIcon, _angular_material_chips__WEBPACK_IMPORTED_MODULE_11__.MatChip],
      styles: [".link-lawyer-container[_ngcontent-%COMP%] {\n  padding: 24px;\n  max-width: 800px;\n  margin: 0 auto;\n}\n\n.header-section[_ngcontent-%COMP%] {\n  text-align: center;\n  margin-bottom: 32px;\n}\n\n.header-section[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\n  font-size: 2.5rem;\n  color: #1976d2;\n  margin-bottom: 8px;\n}\n\n.form-section[_ngcontent-%COMP%] {\n  margin-bottom: 48px;\n}\n\n.link-card[_ngcontent-%COMP%] {\n  max-width: 500px;\n  margin: 0 auto;\n}\n\n.full-width[_ngcontent-%COMP%] {\n  width: 100%;\n}\n\n.code-input[_ngcontent-%COMP%] {\n  font-family: monospace;\n  font-size: 18px;\n  font-weight: bold;\n  letter-spacing: 2px;\n  text-transform: uppercase;\n  text-align: center;\n}\n\n.code-info[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  margin-top: 16px;\n  color: #666;\n  font-size: 14px;\n}\n\n.lawyers-grid[_ngcontent-%COMP%] {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\n  gap: 24px;\n}\n\n.lawyer-card[_ngcontent-%COMP%] {\n  height: -moz-fit-content;\n  height: fit-content;\n}\n\n.permissions[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\n  margin: 0 0 12px 0;\n  color: #666;\n}\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvbGluay1sYXd5ZXIvbGluay1sYXd5ZXIuY29tcG9uZW50LnRzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUNJO0VBQ0UsYUFBQTtFQUNBLGdCQUFBO0VBQ0EsY0FBQTtBQUFOOztBQUdJO0VBQ0Usa0JBQUE7RUFDQSxtQkFBQTtBQUFOOztBQUdJO0VBQ0UsaUJBQUE7RUFDQSxjQUFBO0VBQ0Esa0JBQUE7QUFBTjs7QUFHSTtFQUNFLG1CQUFBO0FBQU47O0FBR0k7RUFDRSxnQkFBQTtFQUNBLGNBQUE7QUFBTjs7QUFHSTtFQUNFLFdBQUE7QUFBTjs7QUFHSTtFQUNFLHNCQUFBO0VBQ0EsZUFBQTtFQUNBLGlCQUFBO0VBQ0EsbUJBQUE7RUFDQSx5QkFBQTtFQUNBLGtCQUFBO0FBQU47O0FBR0k7RUFDRSxhQUFBO0VBQ0EsbUJBQUE7RUFDQSxRQUFBO0VBQ0EsZ0JBQUE7RUFDQSxXQUFBO0VBQ0EsZUFBQTtBQUFOOztBQUdJO0VBQ0UsYUFBQTtFQUNBLDJEQUFBO0VBQ0EsU0FBQTtBQUFOOztBQUdJO0VBQ0Usd0JBQUE7RUFBQSxtQkFBQTtBQUFOOztBQUdJO0VBQ0Usa0JBQUE7RUFDQSxXQUFBO0FBQU4iLCJzb3VyY2VzQ29udGVudCI6WyJcbiAgICAubGluay1sYXd5ZXItY29udGFpbmVyIHtcbiAgICAgIHBhZGRpbmc6IDI0cHg7XG4gICAgICBtYXgtd2lkdGg6IDgwMHB4O1xuICAgICAgbWFyZ2luOiAwIGF1dG87XG4gICAgfVxuICAgIFxuICAgIC5oZWFkZXItc2VjdGlvbiB7XG4gICAgICB0ZXh0LWFsaWduOiBjZW50ZXI7XG4gICAgICBtYXJnaW4tYm90dG9tOiAzMnB4O1xuICAgIH1cbiAgICBcbiAgICAuaGVhZGVyLXNlY3Rpb24gaDEge1xuICAgICAgZm9udC1zaXplOiAyLjVyZW07XG4gICAgICBjb2xvcjogIzE5NzZkMjtcbiAgICAgIG1hcmdpbi1ib3R0b206IDhweDtcbiAgICB9XG4gICAgXG4gICAgLmZvcm0tc2VjdGlvbiB7XG4gICAgICBtYXJnaW4tYm90dG9tOiA0OHB4O1xuICAgIH1cbiAgICBcbiAgICAubGluay1jYXJkIHtcbiAgICAgIG1heC13aWR0aDogNTAwcHg7XG4gICAgICBtYXJnaW46IDAgYXV0bztcbiAgICB9XG4gICAgXG4gICAgLmZ1bGwtd2lkdGgge1xuICAgICAgd2lkdGg6IDEwMCU7XG4gICAgfVxuICAgIFxuICAgIC5jb2RlLWlucHV0IHtcbiAgICAgIGZvbnQtZmFtaWx5OiBtb25vc3BhY2U7XG4gICAgICBmb250LXNpemU6IDE4cHg7XG4gICAgICBmb250LXdlaWdodDogYm9sZDtcbiAgICAgIGxldHRlci1zcGFjaW5nOiAycHg7XG4gICAgICB0ZXh0LXRyYW5zZm9ybTogdXBwZXJjYXNlO1xuICAgICAgdGV4dC1hbGlnbjogY2VudGVyO1xuICAgIH1cbiAgICBcbiAgICAuY29kZS1pbmZvIHtcbiAgICAgIGRpc3BsYXk6IGZsZXg7XG4gICAgICBhbGlnbi1pdGVtczogY2VudGVyO1xuICAgICAgZ2FwOiA4cHg7XG4gICAgICBtYXJnaW4tdG9wOiAxNnB4O1xuICAgICAgY29sb3I6ICM2NjY7XG4gICAgICBmb250LXNpemU6IDE0cHg7XG4gICAgfVxuICAgIFxuICAgIC5sYXd5ZXJzLWdyaWQge1xuICAgICAgZGlzcGxheTogZ3JpZDtcbiAgICAgIGdyaWQtdGVtcGxhdGUtY29sdW1uczogcmVwZWF0KGF1dG8tZml0LCBtaW5tYXgoMzAwcHgsIDFmcikpO1xuICAgICAgZ2FwOiAyNHB4O1xuICAgIH1cbiAgICBcbiAgICAubGF3eWVyLWNhcmQge1xuICAgICAgaGVpZ2h0OiBmaXQtY29udGVudDtcbiAgICB9XG4gICAgXG4gICAgLnBlcm1pc3Npb25zIGg0IHtcbiAgICAgIG1hcmdpbjogMCAwIDEycHggMDtcbiAgICAgIGNvbG9yOiAjNjY2O1xuICAgIH1cbiAgIl0sInNvdXJjZVJvb3QiOiIifQ== */"]
    });
  }
}

/***/ }),

/***/ 3644:
/*!******************************************!*\
  !*** ./src/app/login/login.component.ts ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   LoginComponent: () => (/* binding */ LoginComponent)
/* harmony export */ });
/* harmony import */ var C_Users_acer_Desktop_angularprojects_veritus_Veritus_veritus_secretary_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js */ 9204);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @angular/core */ 7580);
/* harmony import */ var _angular_router__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @angular/router */ 5072);
/* harmony import */ var _services_firebase_service__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../services/firebase.service */ 8287);
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @angular/common */ 316);
/* harmony import */ var _angular_forms__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @angular/forms */ 4456);
/* harmony import */ var _angular_material_button__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @angular/material/button */ 4175);
/* harmony import */ var _angular_material_card__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @angular/material/card */ 3777);
/* harmony import */ var _angular_material_input__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @angular/material/input */ 5541);
/* harmony import */ var _angular_material_form_field__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @angular/material/form-field */ 4950);
/* harmony import */ var _angular_material_icon__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @angular/material/icon */ 3840);











function LoginComponent_span_31_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](0, "span");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](1, "Sign In");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
  }
}
function LoginComponent_span_32_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](0, "span");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](1, "Signing In...");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
  }
}
function LoginComponent_div_33_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](0, "div", 14);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const ctx_r0 = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtextInterpolate1"](" ", ctx_r0.errorMessage, " ");
  }
}
class LoginComponent {
  constructor(router, firebaseService) {
    this.router = router;
    this.firebaseService = firebaseService;
    this.email = '';
    this.password = '';
    this.hidePassword = true;
    this.isLoading = false;
    this.errorMessage = '';
  }
  login() {
    var _this = this;
    return (0,C_Users_acer_Desktop_angularprojects_veritus_Veritus_veritus_secretary_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__["default"])(function* () {
      if (!_this.email || !_this.password) {
        _this.errorMessage = 'Please enter both email and password';
        return;
      }
      _this.isLoading = true;
      _this.errorMessage = '';
      try {
        console.log('Attempting login for:', _this.email);
        // Authenticate with Firebase
        const user = yield _this.firebaseService.signIn(_this.email, _this.password);
        console.log('Login successful:', user);
        // Check if user has secretary profile
        const secretaryProfile = yield _this.firebaseService.getSecretaryProfile(user.uid);
        if (!secretaryProfile) {
          throw new Error('No secretary profile found. Please contact your administrator.');
        }
        console.log('Secretary profile found:', secretaryProfile);
        // Navigate to dashboard
        _this.router.navigate(['/secretary-tabs']).then(() => {
          console.log('Navigation to secretary dashboard successful');
        });
      } catch (error) {
        console.error('Login error:', error);
        // Handle specific Firebase auth errors
        if (error.code === 'auth/user-not-found') {
          _this.errorMessage = 'No account found with this email address';
        } else if (error.code === 'auth/wrong-password') {
          _this.errorMessage = 'Incorrect password';
        } else if (error.code === 'auth/invalid-email') {
          _this.errorMessage = 'Invalid email address';
        } else if (error.code === 'auth/too-many-requests') {
          _this.errorMessage = 'Too many failed attempts. Please try again later';
        } else if (error.message) {
          _this.errorMessage = error.message;
        } else {
          _this.errorMessage = 'Login failed. Please try again';
        }
      } finally {
        _this.isLoading = false;
      }
    })();
  }
  forgotPassword() {
    var _this2 = this;
    return (0,C_Users_acer_Desktop_angularprojects_veritus_Veritus_veritus_secretary_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__["default"])(function* () {
      if (!_this2.email) {
        _this2.errorMessage = 'Please enter your email address first';
        return;
      }
      try {
        yield _this2.firebaseService.resetPassword(_this2.email);
        _this2.errorMessage = '';
        alert('Password reset email sent! Check your inbox.');
      } catch (error) {
        console.error('Password reset error:', error);
        _this2.errorMessage = 'Failed to send password reset email';
      }
    })();
  }
  createDemoAccount() {
    var _this3 = this;
    return (0,C_Users_acer_Desktop_angularprojects_veritus_Veritus_veritus_secretary_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__["default"])(function* () {
      _this3.isLoading = true;
      _this3.errorMessage = '';
      try {
        console.log('Creating demo secretary account...');
        // Create Firebase user
        const userCredential = yield _this3.firebaseService.signUp('<EMAIL>', 'secretary123', {
          name: 'Demo Secretary',
          role: 'secretary'
        });
        console.log('Demo user created:', userCredential);
        // Create secretary profile
        const secretaryProfile = {
          uid: userCredential.uid,
          email: '<EMAIL>',
          name: 'Demo Secretary',
          phone: '+**********',
          role: 'secretary',
          linkedLawyers: [],
          permissions: {
            canManageCalendar: true,
            canManageFiles: true,
            canManageCases: true,
            canManageRetainers: false,
            canViewFinances: true,
            canManageFinances: false
          },
          createdAt: new Date(),
          updatedAt: new Date()
        };
        yield _this3.firebaseService.createSecretaryProfile(secretaryProfile);
        alert('Demo account created successfully! You can now login with:\nEmail: <EMAIL>\nPassword: secretary123');
        // Auto-fill the form
        _this3.email = '<EMAIL>';
        _this3.password = 'secretary123';
      } catch (error) {
        console.error('Demo account creation error:', error);
        if (error.code === 'auth/email-already-in-use') {
          _this3.errorMessage = 'Demo account already exists. You can login with the provided credentials.';
          // Auto-fill the form
          _this3.email = '<EMAIL>';
          _this3.password = 'secretary123';
        } else {
          _this3.errorMessage = 'Failed to create demo account: ' + (error.message || 'Unknown error');
        }
      } finally {
        _this3.isLoading = false;
      }
    })();
  }
  static {
    this.ɵfac = function LoginComponent_Factory(t) {
      return new (t || LoginComponent)(_angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵdirectiveInject"](_angular_router__WEBPACK_IMPORTED_MODULE_3__.Router), _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵdirectiveInject"](_services_firebase_service__WEBPACK_IMPORTED_MODULE_1__.FirebaseService));
    };
  }
  static {
    this.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵdefineComponent"]({
      type: LoginComponent,
      selectors: [["app-login"]],
      decls: 39,
      vars: 10,
      consts: [[1, "login-container"], [1, "login-card"], [1, "login-form"], ["appearance", "outline", 1, "full-width"], ["matInput", "", "type", "email", "name", "email", "placeholder", "<EMAIL>", 3, "ngModelChange", "ngModel"], ["matSuffix", ""], ["matInput", "", "name", "password", "placeholder", "Enter your password", 3, "ngModelChange", "type", "ngModel"], ["mat-icon-button", "", "matSuffix", "", "type", "button", 3, "click"], [1, "demo-info"], ["mat-raised-button", "", "color", "primary", 1, "full-width", "login-button", 3, "click", "disabled"], [4, "ngIf"], ["class", "error-message", 4, "ngIf"], ["mat-button", "", "color", "accent", 3, "click", "disabled"], ["mat-button", "", "color", "primary", 3, "click", "disabled"], [1, "error-message"]],
      template: function LoginComponent_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](0, "div", 0)(1, "mat-card", 1)(2, "mat-card-header")(3, "mat-card-title");
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](4, "Secretary Portal Login");
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](5, "mat-card-subtitle");
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](6, "Access your lawyer management dashboard");
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](7, "mat-card-content")(8, "form", 2)(9, "mat-form-field", 3)(10, "mat-label");
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](11, "Email");
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](12, "input", 4);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtwoWayListener"]("ngModelChange", function LoginComponent_Template_input_ngModelChange_12_listener($event) {
            _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtwoWayBindingSet"](ctx.email, $event) || (ctx.email = $event);
            return $event;
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](13, "mat-icon", 5);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](14, "email");
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](15, "mat-form-field", 3)(16, "mat-label");
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](17, "Password");
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](18, "input", 6);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtwoWayListener"]("ngModelChange", function LoginComponent_Template_input_ngModelChange_18_listener($event) {
            _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtwoWayBindingSet"](ctx.password, $event) || (ctx.password = $event);
            return $event;
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](19, "button", 7);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵlistener"]("click", function LoginComponent_Template_button_click_19_listener() {
            return ctx.hidePassword = !ctx.hidePassword;
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](20, "mat-icon");
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](21);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](22, "div", 8)(23, "small")(24, "strong");
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](25, "Demo Credentials:");
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelement"](26, "br");
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](27, " Email: <EMAIL>");
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelement"](28, "br");
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](29, " Password: secretary123 ");
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](30, "button", 9);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵlistener"]("click", function LoginComponent_Template_button_click_30_listener() {
            return ctx.login();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtemplate"](31, LoginComponent_span_31_Template, 2, 0, "span", 10)(32, LoginComponent_span_32_Template, 2, 0, "span", 10);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtemplate"](33, LoginComponent_div_33_Template, 2, 1, "div", 11);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](34, "mat-card-actions")(35, "button", 12);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵlistener"]("click", function LoginComponent_Template_button_click_35_listener() {
            return ctx.forgotPassword();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](36, " Forgot Password? ");
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](37, "button", 13);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵlistener"]("click", function LoginComponent_Template_button_click_37_listener() {
            return ctx.createDemoAccount();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](38, " Create Demo Account ");
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()()()();
        }
        if (rf & 2) {
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](12);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtwoWayProperty"]("ngModel", ctx.email);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](6);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵproperty"]("type", ctx.hidePassword ? "password" : "text");
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtwoWayProperty"]("ngModel", ctx.password);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](3);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtextInterpolate"](ctx.hidePassword ? "visibility_off" : "visibility");
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](9);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵproperty"]("disabled", ctx.isLoading);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵproperty"]("ngIf", !ctx.isLoading);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵproperty"]("ngIf", ctx.isLoading);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵproperty"]("ngIf", ctx.errorMessage);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](2);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵproperty"]("disabled", ctx.isLoading);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](2);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵproperty"]("disabled", ctx.isLoading);
        }
      },
      dependencies: [_angular_common__WEBPACK_IMPORTED_MODULE_4__.NgIf, _angular_forms__WEBPACK_IMPORTED_MODULE_5__["ɵNgNoValidate"], _angular_forms__WEBPACK_IMPORTED_MODULE_5__.DefaultValueAccessor, _angular_forms__WEBPACK_IMPORTED_MODULE_5__.NgControlStatus, _angular_forms__WEBPACK_IMPORTED_MODULE_5__.NgControlStatusGroup, _angular_forms__WEBPACK_IMPORTED_MODULE_5__.NgModel, _angular_forms__WEBPACK_IMPORTED_MODULE_5__.NgForm, _angular_material_button__WEBPACK_IMPORTED_MODULE_6__.MatButton, _angular_material_button__WEBPACK_IMPORTED_MODULE_6__.MatIconButton, _angular_material_card__WEBPACK_IMPORTED_MODULE_7__.MatCard, _angular_material_card__WEBPACK_IMPORTED_MODULE_7__.MatCardActions, _angular_material_card__WEBPACK_IMPORTED_MODULE_7__.MatCardContent, _angular_material_card__WEBPACK_IMPORTED_MODULE_7__.MatCardHeader, _angular_material_card__WEBPACK_IMPORTED_MODULE_7__.MatCardSubtitle, _angular_material_card__WEBPACK_IMPORTED_MODULE_7__.MatCardTitle, _angular_material_input__WEBPACK_IMPORTED_MODULE_8__.MatInput, _angular_material_form_field__WEBPACK_IMPORTED_MODULE_9__.MatFormField, _angular_material_form_field__WEBPACK_IMPORTED_MODULE_9__.MatLabel, _angular_material_form_field__WEBPACK_IMPORTED_MODULE_9__.MatSuffix, _angular_material_icon__WEBPACK_IMPORTED_MODULE_10__.MatIcon],
      styles: [".login-container[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  min-height: 100vh;\n  width: 100%;\n  padding: 24px;\n  box-sizing: border-box;\n}\n\n.login-card[_ngcontent-%COMP%] {\n  width: 100%;\n  max-width: 400px;\n}\n\n.login-form[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  gap: 16px;\n}\n\n.full-width[_ngcontent-%COMP%] {\n  width: 100%;\n}\n\n.login-button[_ngcontent-%COMP%] {\n  height: 48px;\n  font-size: 16px;\n  margin-top: 16px;\n}\n\n.error-message[_ngcontent-%COMP%] {\n  color: #f44336;\n  font-size: 14px;\n  margin-top: 8px;\n  padding: 8px;\n  background-color: #ffebee;\n  border-radius: 4px;\n  border-left: 4px solid #f44336;\n}\n\n.demo-info[_ngcontent-%COMP%] {\n  background-color: #e3f2fd;\n  border: 1px solid #2196f3;\n  border-radius: 4px;\n  padding: 12px;\n  margin: 16px 0;\n  text-align: center;\n}\n\n.demo-info[_ngcontent-%COMP%]   small[_ngcontent-%COMP%] {\n  color: #1976d2;\n  line-height: 1.4;\n}\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */"]
    });
  }
}

/***/ }),

/***/ 8287:
/*!**********************************************!*\
  !*** ./src/app/services/firebase.service.ts ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   FirebaseService: () => (/* binding */ FirebaseService)
/* harmony export */ });
/* harmony import */ var C_Users_acer_Desktop_angularprojects_veritus_Veritus_veritus_secretary_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js */ 9204);
/* harmony import */ var firebase_app__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! firebase/app */ 6725);
/* harmony import */ var firebase_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! firebase/auth */ 2630);
/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! firebase/firestore */ 3783);
/* harmony import */ var firebase_functions__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! firebase/functions */ 8731);
/* harmony import */ var _environments_environment__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../environments/environment */ 5312);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @angular/core */ 7580);







class FirebaseService {
  constructor() {
    this.app = (0,firebase_app__WEBPACK_IMPORTED_MODULE_1__.initializeApp)(_environments_environment__WEBPACK_IMPORTED_MODULE_5__.environment.firebase);
    this.auth = (0,firebase_auth__WEBPACK_IMPORTED_MODULE_2__.getAuth)(this.app);
    this.firestore = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.getFirestore)(this.app);
    this.functions = (0,firebase_functions__WEBPACK_IMPORTED_MODULE_4__.getFunctions)(this.app);
  }
  // Authentication Methods
  signIn(email, password) {
    var _this = this;
    return (0,C_Users_acer_Desktop_angularprojects_veritus_Veritus_veritus_secretary_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__["default"])(function* () {
      const userCredential = yield (0,firebase_auth__WEBPACK_IMPORTED_MODULE_2__.signInWithEmailAndPassword)(_this.auth, email, password);
      return userCredential.user;
    })();
  }
  signUp(email, password, userData) {
    var _this2 = this;
    return (0,C_Users_acer_Desktop_angularprojects_veritus_Veritus_veritus_secretary_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__["default"])(function* () {
      const userCredential = yield (0,firebase_auth__WEBPACK_IMPORTED_MODULE_2__.createUserWithEmailAndPassword)(_this2.auth, email, password);
      return userCredential.user;
    })();
  }
  signOut() {
    var _this3 = this;
    return (0,C_Users_acer_Desktop_angularprojects_veritus_Veritus_veritus_secretary_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__["default"])(function* () {
      yield (0,firebase_auth__WEBPACK_IMPORTED_MODULE_2__.signOut)(_this3.auth);
    })();
  }
  resetPassword(email) {
    var _this4 = this;
    return (0,C_Users_acer_Desktop_angularprojects_veritus_Veritus_veritus_secretary_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__["default"])(function* () {
      yield (0,firebase_auth__WEBPACK_IMPORTED_MODULE_2__.sendPasswordResetEmail)(_this4.auth, email);
    })();
  }
  getCurrentUser() {
    return this.auth.currentUser;
  }
  // Secretary Profile Methods
  createSecretaryProfile(profile) {
    var _this5 = this;
    return (0,C_Users_acer_Desktop_angularprojects_veritus_Veritus_veritus_secretary_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__["default"])(function* () {
      const docRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.doc)(_this5.firestore, 'secretaries', profile.uid);
      yield (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.setDoc)(docRef, profile);
      yield _this5.logActivity(profile.uid, 'secretary', profile.name, 'CREATE', 'secretary_profile', profile.uid);
    })();
  }
  getSecretaryProfile(uid) {
    var _this6 = this;
    return (0,C_Users_acer_Desktop_angularprojects_veritus_Veritus_veritus_secretary_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__["default"])(function* () {
      const docRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.doc)(_this6.firestore, 'secretaries', uid);
      const docSnap = yield (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.getDoc)(docRef);
      if (docSnap.exists()) {
        return docSnap.data();
      }
      return null;
    })();
  }
  updateSecretaryProfile(uid, updates) {
    var _this7 = this;
    return (0,C_Users_acer_Desktop_angularprojects_veritus_Veritus_veritus_secretary_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__["default"])(function* () {
      const docRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.doc)(_this7.firestore, 'secretaries', uid);
      yield (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.updateDoc)(docRef, {
        ...updates,
        updatedAt: new Date()
      });
      const secretary = yield _this7.getSecretaryProfile(uid);
      if (secretary) {
        yield _this7.logActivity(uid, 'secretary', secretary.name, 'UPDATE', 'secretary_profile', uid, updates);
      }
    })();
  }
  // Lawyer Profile Methods (for secretary access)
  getLawyerProfile(uid) {
    var _this8 = this;
    return (0,C_Users_acer_Desktop_angularprojects_veritus_Veritus_veritus_secretary_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__["default"])(function* () {
      const docRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.doc)(_this8.firestore, 'lawyers', uid);
      const docSnap = yield (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.getDoc)(docRef);
      if (docSnap.exists()) {
        return docSnap.data();
      }
      return null;
    })();
  }
  getSecretaryLinkedLawyers(secretaryId) {
    var _this9 = this;
    return (0,C_Users_acer_Desktop_angularprojects_veritus_Veritus_veritus_secretary_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__["default"])(function* () {
      const secretary = yield _this9.getSecretaryProfile(secretaryId);
      if (!secretary || secretary.linkedLawyers.length === 0) {
        return [];
      }
      const lawyers = [];
      for (const lawyerId of secretary.linkedLawyers) {
        const lawyer = yield _this9.getLawyerProfile(lawyerId);
        if (lawyer) {
          lawyers.push(lawyer);
        }
      }
      return lawyers;
    })();
  }
  // Assistant Code Management Methods
  validateAssistantCode(code) {
    var _this0 = this;
    return (0,C_Users_acer_Desktop_angularprojects_veritus_Veritus_veritus_secretary_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__["default"])(function* () {
      const q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.collection)(_this0.firestore, 'assistant_codes'), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.where)('code', '==', code), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.where)('isUsed', '==', false));
      const querySnapshot = yield (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.getDocs)(q);
      if (querySnapshot.empty) {
        return null;
      }
      const doc = querySnapshot.docs[0];
      const assistantCode = {
        id: doc.id,
        ...doc.data()
      };
      // Check if code has expired
      const expiryDate = assistantCode.expiresAt instanceof Date ? assistantCode.expiresAt : assistantCode.expiresAt.toDate();
      if (new Date() > expiryDate) {
        return null;
      }
      return assistantCode;
    })();
  }
  useAssistantCode(code, secretaryId) {
    var _this1 = this;
    return (0,C_Users_acer_Desktop_angularprojects_veritus_Veritus_veritus_secretary_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__["default"])(function* () {
      try {
        const linkSecretary = (0,firebase_functions__WEBPACK_IMPORTED_MODULE_4__.httpsCallable)(_this1.functions, 'linkSecretary');
        const result = yield linkSecretary({
          code
        });
        if (result.data && result.data.success) {
          return result.data.link;
        } else {
          throw new Error('Failed to link secretary');
        }
      } catch (error) {
        console.error('Error calling linkSecretary function:', error);
        throw error;
      }
    })();
  }
  getLinkedLawyers(secretaryId) {
    var _this10 = this;
    return (0,C_Users_acer_Desktop_angularprojects_veritus_Veritus_veritus_secretary_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__["default"])(function* () {
      const q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.collection)(_this10.firestore, 'lawyer_secretary_links'), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.where)('secretaryId', '==', secretaryId), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.where)('status', '==', 'approved'));
      const querySnapshot = yield (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.getDocs)(q);
      return querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));
    })();
  }
  checkSecretaryPermission(secretaryId, lawyerId, permission) {
    var _this11 = this;
    return (0,C_Users_acer_Desktop_angularprojects_veritus_Veritus_veritus_secretary_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__["default"])(function* () {
      const q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.collection)(_this11.firestore, 'lawyer_secretary_links'), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.where)('secretaryId', '==', secretaryId), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.where)('lawyerId', '==', lawyerId), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.where)('status', '==', 'approved'));
      const querySnapshot = yield (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.getDocs)(q);
      if (querySnapshot.empty) {
        return false;
      }
      const link = querySnapshot.docs[0].data();
      return link.permissions[permission] || false;
    })();
  }
  getAuditLogs(_x, _x2, _x3) {
    var _this12 = this;
    return (0,C_Users_acer_Desktop_angularprojects_veritus_Veritus_veritus_secretary_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__["default"])(function* (entityType, entityId, userId, limitCount = 50) {
      let q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.collection)(_this12.firestore, 'audit_logs'), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.orderBy)('timestamp', 'desc'));
      if (entityType) {
        q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.query)(q, (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.where)('entityType', '==', entityType));
      }
      if (entityId) {
        q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.query)(q, (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.where)('entityId', '==', entityId));
      }
      if (userId) {
        q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.query)(q, (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.where)('userId', '==', userId));
      }
      const querySnapshot = yield (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.getDocs)(q);
      return querySnapshot.docs.slice(0, limitCount).map(doc => ({
        id: doc.id,
        ...doc.data()
      }));
    }).apply(this, arguments);
  }
  // Lawyer Availability Management Methods
  getLawyerAvailability(lawyerId, date) {
    var _this13 = this;
    return (0,C_Users_acer_Desktop_angularprojects_veritus_Veritus_veritus_secretary_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__["default"])(function* () {
      let q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.collection)(_this13.firestore, 'availability'), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.where)('lawyerId', '==', lawyerId));
      if (date) {
        q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.query)(q, (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.where)('date', '==', date));
      }
      q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.query)(q, (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.orderBy)('date', 'asc'));
      const querySnapshot = yield (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.getDocs)(q);
      return querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));
    })();
  }
  createLawyerAvailability(availability) {
    var _this14 = this;
    return (0,C_Users_acer_Desktop_angularprojects_veritus_Veritus_veritus_secretary_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__["default"])(function* () {
      const docRef = yield (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.addDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.collection)(_this14.firestore, 'availability'), {
        ...availability,
        createdAt: new Date(),
        updatedAt: new Date()
      });
      // Log activity
      const secretary = yield _this14.getSecretaryProfile(availability.createdBy);
      if (secretary) {
        yield _this14.logActivity(availability.createdBy, 'secretary', secretary.name, 'CREATE', 'availability', docRef.id, {
          lawyerId: availability.lawyerId,
          date: availability.date,
          timeSlots: availability.timeSlots
        });
      }
      return docRef.id;
    })();
  }
  updateLawyerAvailability(availabilityId, updates, updatedBy) {
    var _this15 = this;
    return (0,C_Users_acer_Desktop_angularprojects_veritus_Veritus_veritus_secretary_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__["default"])(function* () {
      const docRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.doc)(_this15.firestore, 'availability', availabilityId);
      yield (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.updateDoc)(docRef, {
        ...updates,
        updatedAt: new Date()
      });
      // Log activity
      const secretary = yield _this15.getSecretaryProfile(updatedBy);
      if (secretary) {
        yield _this15.logActivity(updatedBy, 'secretary', secretary.name, 'UPDATE', 'availability', availabilityId, updates);
      }
    })();
  }
  deleteLawyerAvailability(availabilityId, deletedBy) {
    var _this16 = this;
    return (0,C_Users_acer_Desktop_angularprojects_veritus_Veritus_veritus_secretary_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__["default"])(function* () {
      const docRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.doc)(_this16.firestore, 'availability', availabilityId);
      yield (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.deleteDoc)(docRef);
      // Log activity
      const secretary = yield _this16.getSecretaryProfile(deletedBy);
      if (secretary) {
        yield _this16.logActivity(deletedBy, 'secretary', secretary.name, 'DELETE', 'availability', availabilityId);
      }
    })();
  }
  // Enhanced Appointment Management Methods
  createAppointment(appointmentData) {
    var _this17 = this;
    return (0,C_Users_acer_Desktop_angularprojects_veritus_Veritus_veritus_secretary_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__["default"])(function* () {
      const docRef = yield (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.addDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.collection)(_this17.firestore, 'appointments'), {
        ...appointmentData,
        createdAt: new Date(),
        updatedAt: new Date()
      });
      // Log the activity
      yield _this17.logActivity(appointmentData.managedBy || appointmentData.lawyerId, appointmentData.createdBy, appointmentData.clientName, 'CREATE', 'appointment', docRef.id, {
        lawyerName: appointmentData.lawyerName,
        date: appointmentData.date,
        time: appointmentData.time,
        type: appointmentData.type
      });
      return docRef.id;
    })();
  }
  updateAppointment(appointmentId, updates) {
    var _this18 = this;
    return (0,C_Users_acer_Desktop_angularprojects_veritus_Veritus_veritus_secretary_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__["default"])(function* () {
      const docRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.doc)(_this18.firestore, 'appointments', appointmentId);
      yield (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.updateDoc)(docRef, {
        ...updates,
        updatedAt: new Date()
      });
      // Log the activity
      if (updates.lastModifiedBy) {
        yield _this18.logActivity(updates.lastModifiedBy, updates.lastModifiedByRole || 'secretary', updates.clientName || 'Unknown', 'UPDATE', 'appointment', appointmentId, updates);
      }
    })();
  }
  deleteAppointment(appointmentId, deletedBy, deletedByRole) {
    var _this19 = this;
    return (0,C_Users_acer_Desktop_angularprojects_veritus_Veritus_veritus_secretary_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__["default"])(function* () {
      const docRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.doc)(_this19.firestore, 'appointments', appointmentId);
      // Get appointment details for logging
      const docSnap = yield (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.getDoc)(docRef);
      const appointment = docSnap.data();
      yield (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.deleteDoc)(docRef);
      // Log the activity
      yield _this19.logActivity(deletedBy, deletedByRole, appointment.clientName, 'DELETE', 'appointment', appointmentId, {
        lawyerName: appointment.lawyerName,
        date: appointment.date,
        time: appointment.time,
        type: appointment.type
      });
    })();
  }
  getAppointmentsForSecretary(secretaryId, filter) {
    var _this20 = this;
    return (0,C_Users_acer_Desktop_angularprojects_veritus_Veritus_veritus_secretary_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__["default"])(function* () {
      const secretary = yield _this20.getSecretaryProfile(secretaryId);
      if (!secretary || secretary.linkedLawyers.length === 0) {
        return [];
      }
      let q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.collection)(_this20.firestore, 'appointments'), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.where)('lawyerId', 'in', secretary.linkedLawyers));
      if (filter?.status && filter.status.length > 0) {
        q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.query)(q, (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.where)('status', 'in', filter.status));
      }
      if (filter?.dateFrom) {
        q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.query)(q, (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.where)('date', '>=', filter.dateFrom));
      }
      if (filter?.dateTo) {
        q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.query)(q, (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.where)('date', '<=', filter.dateTo));
      }
      q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.query)(q, (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.orderBy)('date', 'asc'), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.orderBy)('time', 'asc'));
      const querySnapshot = yield (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.getDocs)(q);
      let appointments = querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));
      // Apply additional filters that can't be done in Firestore query
      if (filter?.type && filter.type.length > 0) {
        appointments = appointments.filter(apt => filter.type.includes(apt.type));
      }
      if (filter?.isUrgent !== undefined) {
        appointments = appointments.filter(apt => apt.isUrgent === filter.isUrgent);
      }
      if (filter?.createdBy && filter.createdBy.length > 0) {
        appointments = appointments.filter(apt => filter.createdBy.includes(apt.createdBy));
      }
      return appointments;
    })();
  }
  getAppointmentsByLawyer(lawyerId, dateFrom, dateTo) {
    var _this21 = this;
    return (0,C_Users_acer_Desktop_angularprojects_veritus_Veritus_veritus_secretary_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__["default"])(function* () {
      let q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.collection)(_this21.firestore, 'appointments'), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.where)('lawyerId', '==', lawyerId));
      if (dateFrom) {
        q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.query)(q, (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.where)('date', '>=', dateFrom));
      }
      if (dateTo) {
        q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.query)(q, (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.where)('date', '<=', dateTo));
      }
      q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.query)(q, (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.orderBy)('date', 'asc'), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.orderBy)('time', 'asc'));
      const querySnapshot = yield (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.getDocs)(q);
      return querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));
    })();
  }
  updateAppointmentStatus(appointmentId, status, updatedBy, reason) {
    var _this22 = this;
    return (0,C_Users_acer_Desktop_angularprojects_veritus_Veritus_veritus_secretary_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__["default"])(function* () {
      const updates = {
        status,
        lastModifiedBy: updatedBy,
        lastModifiedByRole: 'secretary',
        updatedAt: new Date()
      };
      if (reason) {
        updates.remarks = reason;
      }
      const docRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.doc)(_this22.firestore, 'appointments', appointmentId);
      yield (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.updateDoc)(docRef, updates);
      // Log activity
      const secretary = yield _this22.getSecretaryProfile(updatedBy);
      if (secretary) {
        yield _this22.logActivity(updatedBy, 'secretary', secretary.name, 'UPDATE', 'appointment', appointmentId, {
          status,
          reason
        });
      }
    })();
  }
  // Reschedule Request Management Methods
  createRescheduleRequest(request) {
    var _this23 = this;
    return (0,C_Users_acer_Desktop_angularprojects_veritus_Veritus_veritus_secretary_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__["default"])(function* () {
      const docRef = yield (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.addDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.collection)(_this23.firestore, 'reschedule_requests'), {
        ...request,
        createdAt: new Date(),
        expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) // Expires in 7 days
      });
      // Log activity
      yield _this23.logActivity(request.requestedBy, 'secretary', request.requestedByName, 'CREATE', 'reschedule_request', docRef.id, {
        appointmentId: request.appointmentId,
        originalDate: request.originalDate,
        proposedDate: request.proposedDate,
        reason: request.reason
      });
      return docRef.id;
    })();
  }
  getRescheduleRequests(appointmentId) {
    var _this24 = this;
    return (0,C_Users_acer_Desktop_angularprojects_veritus_Veritus_veritus_secretary_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__["default"])(function* () {
      let q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.collection)(_this24.firestore, 'reschedule_requests'));
      if (appointmentId) {
        q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.query)(q, (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.where)('appointmentId', '==', appointmentId));
      }
      q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.query)(q, (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.orderBy)('createdAt', 'desc'));
      const querySnapshot = yield (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.getDocs)(q);
      return querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));
    })();
  }
  updateRescheduleRequestStatus(requestId, status, respondedBy, response) {
    var _this25 = this;
    return (0,C_Users_acer_Desktop_angularprojects_veritus_Veritus_veritus_secretary_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__["default"])(function* () {
      const updates = {
        status,
        respondedAt: new Date()
      };
      if (response) {
        updates.clientResponse = response;
      }
      const docRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.doc)(_this25.firestore, 'reschedule_requests', requestId);
      yield (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.updateDoc)(docRef, updates);
      // If approved, update the original appointment
      if (status === 'approved') {
        const request = yield _this25.getRescheduleRequest(requestId);
        if (request) {
          yield _this25.updateAppointmentFromReschedule(request);
        }
      }
      // Log activity
      yield _this25.logActivity(respondedBy, 'secretary', 'Secretary', 'UPDATE', 'reschedule_request', requestId, {
        status,
        response
      });
    })();
  }
  getRescheduleRequest(requestId) {
    var _this26 = this;
    return (0,C_Users_acer_Desktop_angularprojects_veritus_Veritus_veritus_secretary_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__["default"])(function* () {
      const docRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.doc)(_this26.firestore, 'reschedule_requests', requestId);
      const docSnap = yield (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.getDoc)(docRef);
      if (docSnap.exists()) {
        return {
          id: docSnap.id,
          ...docSnap.data()
        };
      }
      return null;
    })();
  }
  updateAppointmentFromReschedule(request) {
    var _this27 = this;
    return (0,C_Users_acer_Desktop_angularprojects_veritus_Veritus_veritus_secretary_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__["default"])(function* () {
      const updates = {
        originalDate: request.originalDate,
        originalTime: request.originalTime,
        date: request.proposedDate,
        time: request.proposedTime,
        status: 'rescheduled',
        rescheduleReason: request.reason,
        rescheduleRequestedBy: request.requestedBy,
        rescheduleRequestedAt: request.createdAt,
        updatedAt: new Date()
      };
      const docRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.doc)(_this27.firestore, 'appointments', request.appointmentId);
      yield (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.updateDoc)(docRef, updates);
    })();
  }
  // Calendar Summary Methods
  getLawyerCalendarSummary(lawyerId, date) {
    var _this28 = this;
    return (0,C_Users_acer_Desktop_angularprojects_veritus_Veritus_veritus_secretary_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__["default"])(function* () {
      const lawyer = yield _this28.getLawyerProfile(lawyerId);
      const appointments = yield _this28.getAppointmentsByLawyer(lawyerId, date, date);
      const availability = yield _this28.getLawyerAvailability(lawyerId, date);
      const totalAppointments = appointments.length;
      const confirmedAppointments = appointments.filter(apt => apt.status === 'confirmed').length;
      const pendingAppointments = appointments.filter(apt => apt.status === 'pending').length;
      // Calculate available slots
      let availableSlots = 0;
      availability.forEach(avail => {
        const bookedSlots = appointments.filter(apt => apt.date === avail.date && avail.timeSlots.includes(apt.time)).length;
        availableSlots += Math.max(0, avail.timeSlots.length - bookedSlots);
      });
      // Find next appointment
      const futureAppointments = appointments.filter(apt => new Date(apt.date + ' ' + apt.time) > new Date()).sort((a, b) => new Date(a.date + ' ' + a.time).getTime() - new Date(b.date + ' ' + b.time).getTime());
      const nextAppointment = futureAppointments.length > 0 ? {
        date: futureAppointments[0].date,
        time: futureAppointments[0].time,
        clientName: futureAppointments[0].clientName
      } : undefined;
      return {
        lawyerId,
        lawyerName: lawyer?.name || 'Unknown',
        totalAppointments,
        confirmedAppointments,
        pendingAppointments,
        availableSlots,
        nextAppointment
      };
    })();
  }
  // Reminder Management Methods
  createAppointmentReminder(reminder) {
    var _this29 = this;
    return (0,C_Users_acer_Desktop_angularprojects_veritus_Veritus_veritus_secretary_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__["default"])(function* () {
      const docRef = yield (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.addDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.collection)(_this29.firestore, 'appointment_reminders'), {
        ...reminder,
        createdAt: new Date()
      });
      return docRef.id;
    })();
  }
  getPendingReminders() {
    var _this30 = this;
    return (0,C_Users_acer_Desktop_angularprojects_veritus_Veritus_veritus_secretary_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__["default"])(function* () {
      const q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.collection)(_this30.firestore, 'appointment_reminders'), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.where)('status', '==', 'pending'), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.where)('scheduledFor', '<=', new Date()));
      const querySnapshot = yield (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.getDocs)(q);
      return querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));
    })();
  }
  markReminderAsSent(reminderId) {
    var _this31 = this;
    return (0,C_Users_acer_Desktop_angularprojects_veritus_Veritus_veritus_secretary_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__["default"])(function* () {
      const docRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.doc)(_this31.firestore, 'appointment_reminders', reminderId);
      yield (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.updateDoc)(docRef, {
        status: 'sent',
        sentAt: new Date()
      });
    })();
  }
  // Audit Logging Methods
  logActivity(userId, userRole, userName, action, entityType, entityId, changes, metadata) {
    var _this32 = this;
    return (0,C_Users_acer_Desktop_angularprojects_veritus_Veritus_veritus_secretary_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__["default"])(function* () {
      const auditLog = {
        userId,
        userRole,
        userName,
        action,
        entityType,
        entityId,
        changes,
        metadata,
        timestamp: new Date()
      };
      yield (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.addDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.collection)(_this32.firestore, 'audit_logs'), auditLog);
    })();
  }
  static {
    this.ɵfac = function FirebaseService_Factory(t) {
      return new (t || FirebaseService)();
    };
  }
  static {
    this.ɵprov = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵdefineInjectable"]({
      token: FirebaseService,
      factory: FirebaseService.ɵfac,
      providedIn: 'root'
    });
  }
}

/***/ }),

/***/ 5312:
/*!*****************************************!*\
  !*** ./src/environments/environment.ts ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   environment: () => (/* binding */ environment)
/* harmony export */ });
const environment = {
  production: false,
  firebase: {
    apiKey: "AIzaSyDll_CbcZsrTCgUBFGt36TNarkpwc4ubHQ",
    authDomain: "veritus-620ca.firebaseapp.com",
    projectId: "veritus-620ca",
    storageBucket: "veritus-620ca.firebasestorage.app",
    messagingSenderId: "931230393501",
    appId: "1:931230393501:web:2ef920ceb1588daf2da074"
  }
};

/***/ }),

/***/ 4429:
/*!*********************!*\
  !*** ./src/main.ts ***!
  \*********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _angular_platform_browser__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @angular/platform-browser */ 436);
/* harmony import */ var _app_app_module__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./app/app.module */ 635);


_angular_platform_browser__WEBPACK_IMPORTED_MODULE_1__.platformBrowser().bootstrapModule(_app_app_module__WEBPACK_IMPORTED_MODULE_0__.AppModule).catch(err => console.error(err));

/***/ }),

/***/ 8996:
/*!******************************************************************************************************************************************!*\
  !*** ./node_modules/@ionic/core/dist/esm/ lazy ^\.\/.*\.entry\.js$ include: \.entry\.js$ exclude: \.system\.entry\.js$ namespace object ***!
  \******************************************************************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

var map = {
	"./ion-accordion_2.entry.js": [
		7518,
		"common",
		"node_modules_ionic_core_dist_esm_ion-accordion_2_entry_js"
	],
	"./ion-action-sheet.entry.js": [
		1981,
		"common",
		"node_modules_ionic_core_dist_esm_ion-action-sheet_entry_js"
	],
	"./ion-alert.entry.js": [
		1603,
		"common",
		"node_modules_ionic_core_dist_esm_ion-alert_entry_js"
	],
	"./ion-app_8.entry.js": [
		2273,
		"common",
		"node_modules_ionic_core_dist_esm_ion-app_8_entry_js"
	],
	"./ion-avatar_3.entry.js": [
		9642,
		"node_modules_ionic_core_dist_esm_ion-avatar_3_entry_js"
	],
	"./ion-back-button.entry.js": [
		2095,
		"common",
		"node_modules_ionic_core_dist_esm_ion-back-button_entry_js"
	],
	"./ion-backdrop.entry.js": [
		2335,
		"node_modules_ionic_core_dist_esm_ion-backdrop_entry_js"
	],
	"./ion-breadcrumb_2.entry.js": [
		8221,
		"common",
		"node_modules_ionic_core_dist_esm_ion-breadcrumb_2_entry_js"
	],
	"./ion-button_2.entry.js": [
		7184,
		"node_modules_ionic_core_dist_esm_ion-button_2_entry_js"
	],
	"./ion-card_5.entry.js": [
		8759,
		"node_modules_ionic_core_dist_esm_ion-card_5_entry_js"
	],
	"./ion-checkbox.entry.js": [
		4248,
		"node_modules_ionic_core_dist_esm_ion-checkbox_entry_js"
	],
	"./ion-chip.entry.js": [
		9863,
		"node_modules_ionic_core_dist_esm_ion-chip_entry_js"
	],
	"./ion-col_3.entry.js": [
		1769,
		"node_modules_ionic_core_dist_esm_ion-col_3_entry_js"
	],
	"./ion-datetime-button.entry.js": [
		2569,
		"default-node_modules_ionic_core_dist_esm_data-bb424ba8_js",
		"node_modules_ionic_core_dist_esm_ion-datetime-button_entry_js"
	],
	"./ion-datetime_3.entry.js": [
		6534,
		"default-node_modules_ionic_core_dist_esm_data-bb424ba8_js",
		"common",
		"node_modules_ionic_core_dist_esm_ion-datetime_3_entry_js"
	],
	"./ion-fab_3.entry.js": [
		5458,
		"common",
		"node_modules_ionic_core_dist_esm_ion-fab_3_entry_js"
	],
	"./ion-img.entry.js": [
		654,
		"node_modules_ionic_core_dist_esm_ion-img_entry_js"
	],
	"./ion-infinite-scroll_2.entry.js": [
		6034,
		"common",
		"node_modules_ionic_core_dist_esm_ion-infinite-scroll_2_entry_js"
	],
	"./ion-input.entry.js": [
		761,
		"default-node_modules_ionic_core_dist_esm_form-controller-21dd62b1_js-node_modules_ionic_core_-a176d1",
		"common",
		"node_modules_ionic_core_dist_esm_ion-input_entry_js"
	],
	"./ion-item-option_3.entry.js": [
		6492,
		"common",
		"node_modules_ionic_core_dist_esm_ion-item-option_3_entry_js"
	],
	"./ion-item_8.entry.js": [
		9557,
		"common",
		"node_modules_ionic_core_dist_esm_ion-item_8_entry_js"
	],
	"./ion-loading.entry.js": [
		8353,
		"common",
		"node_modules_ionic_core_dist_esm_ion-loading_entry_js"
	],
	"./ion-menu_3.entry.js": [
		1024,
		"common",
		"node_modules_ionic_core_dist_esm_ion-menu_3_entry_js"
	],
	"./ion-modal.entry.js": [
		9160,
		"common",
		"node_modules_ionic_core_dist_esm_ion-modal_entry_js"
	],
	"./ion-nav_2.entry.js": [
		393,
		"node_modules_ionic_core_dist_esm_ion-nav_2_entry_js"
	],
	"./ion-picker-column-internal.entry.js": [
		3970,
		"common",
		"node_modules_ionic_core_dist_esm_ion-picker-column-internal_entry_js"
	],
	"./ion-picker-internal.entry.js": [
		437,
		"node_modules_ionic_core_dist_esm_ion-picker-internal_entry_js"
	],
	"./ion-popover.entry.js": [
		6772,
		"common",
		"node_modules_ionic_core_dist_esm_ion-popover_entry_js"
	],
	"./ion-progress-bar.entry.js": [
		4810,
		"node_modules_ionic_core_dist_esm_ion-progress-bar_entry_js"
	],
	"./ion-radio_2.entry.js": [
		4639,
		"common",
		"node_modules_ionic_core_dist_esm_ion-radio_2_entry_js"
	],
	"./ion-range.entry.js": [
		628,
		"common",
		"node_modules_ionic_core_dist_esm_ion-range_entry_js"
	],
	"./ion-refresher_2.entry.js": [
		852,
		"common",
		"node_modules_ionic_core_dist_esm_ion-refresher_2_entry_js"
	],
	"./ion-reorder_2.entry.js": [
		1479,
		"common",
		"node_modules_ionic_core_dist_esm_ion-reorder_2_entry_js"
	],
	"./ion-ripple-effect.entry.js": [
		4065,
		"node_modules_ionic_core_dist_esm_ion-ripple-effect_entry_js"
	],
	"./ion-route_4.entry.js": [
		7971,
		"node_modules_ionic_core_dist_esm_ion-route_4_entry_js"
	],
	"./ion-searchbar.entry.js": [
		3184,
		"common",
		"node_modules_ionic_core_dist_esm_ion-searchbar_entry_js"
	],
	"./ion-segment_2.entry.js": [
		469,
		"common",
		"node_modules_ionic_core_dist_esm_ion-segment_2_entry_js"
	],
	"./ion-select_3.entry.js": [
		8471,
		"common",
		"node_modules_ionic_core_dist_esm_ion-select_3_entry_js"
	],
	"./ion-spinner.entry.js": [
		388,
		"common",
		"node_modules_ionic_core_dist_esm_ion-spinner_entry_js"
	],
	"./ion-split-pane.entry.js": [
		2392,
		"node_modules_ionic_core_dist_esm_ion-split-pane_entry_js"
	],
	"./ion-tab-bar_2.entry.js": [
		6059,
		"common",
		"node_modules_ionic_core_dist_esm_ion-tab-bar_2_entry_js"
	],
	"./ion-tab_2.entry.js": [
		5427,
		"node_modules_ionic_core_dist_esm_ion-tab_2_entry_js"
	],
	"./ion-text.entry.js": [
		7817,
		"node_modules_ionic_core_dist_esm_ion-text_entry_js"
	],
	"./ion-textarea.entry.js": [
		1735,
		"default-node_modules_ionic_core_dist_esm_form-controller-21dd62b1_js-node_modules_ionic_core_-a176d1",
		"node_modules_ionic_core_dist_esm_ion-textarea_entry_js"
	],
	"./ion-toast.entry.js": [
		7510,
		"common",
		"node_modules_ionic_core_dist_esm_ion-toast_entry_js"
	],
	"./ion-toggle.entry.js": [
		5297,
		"common",
		"node_modules_ionic_core_dist_esm_ion-toggle_entry_js"
	]
};
function webpackAsyncContext(req) {
	if(!__webpack_require__.o(map, req)) {
		return Promise.resolve().then(() => {
			var e = new Error("Cannot find module '" + req + "'");
			e.code = 'MODULE_NOT_FOUND';
			throw e;
		});
	}

	var ids = map[req], id = ids[0];
	return Promise.all(ids.slice(1).map(__webpack_require__.e)).then(() => {
		return __webpack_require__(id);
	});
}
webpackAsyncContext.keys = () => (Object.keys(map));
webpackAsyncContext.id = 8996;
module.exports = webpackAsyncContext;

/***/ }),

/***/ 4140:
/*!************************************************************************************************************************************************************!*\
  !*** ./node_modules/@stencil/core/internal/client/ lazy ^\.\/.*\.entry\.js.*$ include: \.entry\.js$ exclude: \.system\.entry\.js$ strict namespace object ***!
  \************************************************************************************************************************************************************/
/***/ ((module) => {

function webpackEmptyAsyncContext(req) {
	// Here Promise.resolve().then() is used instead of new Promise() to prevent
	// uncaught exception popping up in devtools
	return Promise.resolve().then(() => {
		var e = new Error("Cannot find module '" + req + "'");
		e.code = 'MODULE_NOT_FOUND';
		throw e;
	});
}
webpackEmptyAsyncContext.keys = () => ([]);
webpackEmptyAsyncContext.resolve = webpackEmptyAsyncContext;
webpackEmptyAsyncContext.id = 4140;
module.exports = webpackEmptyAsyncContext;

/***/ })

},
/******/ __webpack_require__ => { // webpackRuntimeModules
/******/ var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
/******/ __webpack_require__.O(0, ["vendor"], () => (__webpack_exec__(4429)));
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ }
]);
//# sourceMappingURL=main.js.map