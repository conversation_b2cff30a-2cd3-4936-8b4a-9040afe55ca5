import { Component, OnInit, Input } from '@angular/core';
import { FirebaseService, LawyerProfile } from '../../services/firebase.service';
import { EnhancedAppointment, AppointmentFilter, AppointmentStatus, AppointmentType } from '../../models/scheduling.models';
import { ModalController, ToastController, AlertController } from '@ionic/angular';
import { RescheduleModalComponent } from '../reschedule-modal/reschedule-modal.component';

@Component({
  selector: 'app-appointment-management',
  templateUrl: './appointment-management.component.html',
  styleUrls: ['./appointment-management.component.scss']
})
export class AppointmentManagementComponent implements OnInit {
  @Input() linkedLawyers: LawyerProfile[] = [];

  appointments: EnhancedAppointment[] = [];
  filteredAppointments: EnhancedAppointment[] = [];
  isLoading = false;

  // Filter properties
  filter: AppointmentFilter = {
    status: [],
    dateFrom: new Date().toISOString().split('T')[0], // Today
    dateTo: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 30 days from now
    type: [],
    lawyerId: undefined
  };

  // Filter options
  statusOptions = [
    { value: AppointmentStatus.PENDING, label: 'Pending', color: 'warning' },
    { value: AppointmentStatus.CONFIRMED, label: 'Confirmed', color: 'success' },
    { value: AppointmentStatus.CANCELLED, label: 'Cancelled', color: 'danger' },
    { value: AppointmentStatus.COMPLETED, label: 'Completed', color: 'medium' },
    { value: AppointmentStatus.RESCHEDULED, label: 'Rescheduled', color: 'tertiary' }
  ];

  typeOptions = [
    AppointmentType.CONSULTATION,
    AppointmentType.CASE_REVIEW,
    AppointmentType.COURT_HEARING,
    AppointmentType.DOCUMENT_SIGNING,
    AppointmentType.FOLLOW_UP,
    AppointmentType.EMERGENCY
  ];

  // View options
  viewMode: 'list' | 'grid' = 'list';
  sortBy: 'date' | 'status' | 'lawyer' | 'type' = 'date';
  sortOrder: 'asc' | 'desc' = 'asc';

  constructor(
    private firebaseService: FirebaseService,
    private modalController: ModalController,
    private toastController: ToastController,
    private alertController: AlertController
  ) { }

  ngOnInit() {
    this.loadAppointments();
  }

  async loadAppointments() {
    const currentUser = this.firebaseService.getCurrentUser();
    if (!currentUser) return;

    this.isLoading = true;
    try {
      this.appointments = await this.firebaseService.getAppointmentsForSecretary(
        currentUser.uid,
        this.filter
      );
      this.applyFiltersAndSort();
    } catch (error) {
      console.error('Error loading appointments:', error);
      // Add mock appointments for testing
      this.appointments = [
        {
          id: 'apt1',
          lawyerId: 'lawyer1',
          lawyerName: 'John Smith',
          clientName: 'Alice Johnson',
          date: new Date().toISOString().split('T')[0],
          time: '09:00',
          status: 'pending',
          type: 'Consultation',
          createdBy: 'client',
          isUrgent: false,
          createdAt: new Date(),
          updatedAt: new Date()
        },
        {
          id: 'apt2',
          lawyerId: 'lawyer1',
          lawyerName: 'John Smith',
          clientName: 'Bob Wilson',
          date: new Date().toISOString().split('T')[0],
          time: '14:00',
          status: 'confirmed',
          type: 'Case Review',
          createdBy: 'client',
          isUrgent: true,
          createdAt: new Date(),
          updatedAt: new Date()
        }
      ];
      this.applyFiltersAndSort();
      this.showToast('Using mock appointment data', 'warning');
    } finally {
      this.isLoading = false;
    }
  }

  applyFiltersAndSort() {
    let filtered = [...this.appointments];

    // Apply filters
    if (this.filter.status && this.filter.status.length > 0) {
      filtered = filtered.filter(apt => this.filter.status!.includes(apt.status));
    }

    if (this.filter.type && this.filter.type.length > 0) {
      filtered = filtered.filter(apt => this.filter.type!.includes(apt.type));
    }

    if (this.filter.lawyerId) {
      filtered = filtered.filter(apt => apt.lawyerId === this.filter.lawyerId);
    }

    if (this.filter.dateFrom) {
      filtered = filtered.filter(apt => apt.date >= this.filter.dateFrom!);
    }

    if (this.filter.dateTo) {
      filtered = filtered.filter(apt => apt.date <= this.filter.dateTo!);
    }

    if (this.filter.isUrgent !== undefined) {
      filtered = filtered.filter(apt => apt.isUrgent === this.filter.isUrgent);
    }

    // Apply sorting
    filtered.sort((a, b) => {
      let comparison = 0;
      
      switch (this.sortBy) {
        case 'date':
          comparison = new Date(a.date + ' ' + a.time).getTime() - new Date(b.date + ' ' + b.time).getTime();
          break;
        case 'status':
          comparison = a.status.localeCompare(b.status);
          break;
        case 'lawyer':
          comparison = a.lawyerName.localeCompare(b.lawyerName);
          break;
        case 'type':
          comparison = a.type.localeCompare(b.type);
          break;
      }

      return this.sortOrder === 'desc' ? -comparison : comparison;
    });

    this.filteredAppointments = filtered;
  }

  onFilterChange() {
    this.applyFiltersAndSort();
  }

  onSortChange() {
    this.applyFiltersAndSort();
  }

  toggleSortOrder() {
    this.sortOrder = this.sortOrder === 'asc' ? 'desc' : 'asc';
    this.applyFiltersAndSort();
  }

  async openRescheduleModal(appointment: EnhancedAppointment) {
    const modal = await this.modalController.create({
      component: RescheduleModalComponent,
      componentProps: {
        appointment: appointment,
        linkedLawyers: this.linkedLawyers
      }
    });

    modal.onDidDismiss().then((result) => {
      if (result.data && result.data.rescheduled) {
        this.loadAppointments(); // Reload appointments
        this.showToast('Reschedule request sent successfully', 'success');
      }
    });

    await modal.present();
  }

  async updateAppointmentStatus(appointment: EnhancedAppointment, newStatus: string) {
    const alert = await this.alertController.create({
      header: 'Update Appointment Status',
      message: `Change status to "${newStatus}"?`,
      inputs: [
        {
          name: 'reason',
          type: 'textarea',
          placeholder: 'Reason for status change (optional)'
        }
      ],
      buttons: [
        {
          text: 'Cancel',
          role: 'cancel'
        },
        {
          text: 'Update',
          handler: async (data) => {
            await this.performStatusUpdate(appointment, newStatus, data.reason);
          }
        }
      ]
    });

    await alert.present();
  }

  private async performStatusUpdate(appointment: EnhancedAppointment, newStatus: string, reason?: string) {
    const currentUser = this.firebaseService.getCurrentUser();
    if (!currentUser || !appointment.id) return;

    try {
      await this.firebaseService.updateAppointmentStatus(
        appointment.id,
        newStatus,
        currentUser.uid,
        reason
      );

      // Update local appointment
      appointment.status = newStatus as any;
      appointment.remarks = reason || appointment.remarks;
      
      this.applyFiltersAndSort();
      this.showToast('Appointment status updated', 'success');
    } catch (error) {
      console.error('Error updating appointment status:', error);
      this.showToast('Error updating appointment status', 'danger');
    }
  }

  getStatusColor(status: string): string {
    const statusOption = this.statusOptions.find(opt => opt.value === status);
    return statusOption?.color || 'medium';
  }

  getStatusLabel(status: string): string {
    const statusOption = this.statusOptions.find(opt => opt.value === status);
    return statusOption?.label || status;
  }

  isStatusSelected(status: string): boolean {
    return this.filter.status?.includes(status) || false;
  }

  toggleStatusFilter(status: string) {
    if (!this.filter.status) {
      this.filter.status = [];
    }

    const index = this.filter.status.indexOf(status);
    if (index > -1) {
      this.filter.status.splice(index, 1);
    } else {
      this.filter.status.push(status);
    }

    this.onFilterChange();
  }

  isTypeSelected(type: string): boolean {
    return this.filter.type?.includes(type) || false;
  }

  toggleTypeFilter(type: string) {
    if (!this.filter.type) {
      this.filter.type = [];
    }

    const index = this.filter.type.indexOf(type);
    if (index > -1) {
      this.filter.type.splice(index, 1);
    } else {
      this.filter.type.push(type);
    }

    this.onFilterChange();
  }

  clearFilters() {
    this.filter = {
      status: [],
      dateFrom: new Date().toISOString().split('T')[0],
      dateTo: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      type: [],
      lawyerId: undefined
    };
    this.onFilterChange();
  }

  formatDateTime(date: string, time: string): string {
    const dateObj = new Date(date + ' ' + time);
    return dateObj.toLocaleDateString() + ' at ' + dateObj.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  }

  isUpcoming(appointment: EnhancedAppointment): boolean {
    const appointmentDateTime = new Date(appointment.date + ' ' + appointment.time);
    return appointmentDateTime > new Date();
  }

  isPast(appointment: EnhancedAppointment): boolean {
    const appointmentDateTime = new Date(appointment.date + ' ' + appointment.time);
    return appointmentDateTime < new Date();
  }

  canReschedule(appointment: EnhancedAppointment): boolean {
    return this.isUpcoming(appointment) && 
           appointment.status !== AppointmentStatus.CANCELLED && 
           appointment.status !== AppointmentStatus.COMPLETED;
  }

  private async showToast(message: string, color: string) {
    const toast = await this.toastController.create({
      message,
      duration: 3000,
      color,
      position: 'top'
    });
    await toast.present();
  }

  get totalAppointments(): number {
    return this.appointments.length;
  }

  get filteredCount(): number {
    return this.filteredAppointments.length;
  }

  get upcomingCount(): number {
    return this.filteredAppointments.filter(apt => this.isUpcoming(apt)).length;
  }

  get pendingCount(): number {
    return this.filteredAppointments.filter(apt => apt.status === AppointmentStatus.PENDING).length;
  }
}
