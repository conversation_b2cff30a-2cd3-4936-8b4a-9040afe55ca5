<div class="dashboard-container">
  <div class="dashboard-header">
    <h1>Dashboard Overview</h1>
    <p>Welcome back, {{ currentAdmin?.displayName }}! Here's what's happening today.</p>
  </div>

  <!-- Statistics Cards -->
  <div class="stats-grid">
    <div class="stat-card">
      <div class="stat-icon pending">📋</div>
      <div class="stat-content">
        <h3>{{ stats.pendingVerifications }}</h3>
        <p>Pending Verifications</p>
        <span class="stat-change positive">+{{ stats.newVerificationsToday }} today</span>
      </div>
    </div>

    <div class="stat-card">
      <div class="stat-icon verified">✅</div>
      <div class="stat-content">
        <h3>{{ stats.verifiedLawyers }}</h3>
        <p>Verified Lawyers</p>
        <span class="stat-change positive">+{{ stats.verifiedToday }} this week</span>
      </div>
    </div>

    <div class="stat-card">
      <div class="stat-icon users">👥</div>
      <div class="stat-content">
        <h3>{{ stats.totalUsers }}</h3>
        <p>Total Users</p>
        <span class="stat-change positive">+{{ stats.newUsersToday }} today</span>
      </div>
    </div>

    <div class="stat-card">
      <div class="stat-icon templates">📄</div>
      <div class="stat-content">
        <h3>{{ stats.activeTemplates }}</h3>
        <p>Active Templates</p>
        <span class="stat-change neutral">{{ stats.templatesUpdated }} updated</span>
      </div>
    </div>
  </div>

  <!-- Main Content Grid -->
  <div class="dashboard-grid">
    <!-- Recent Verifications -->
    <div class="dashboard-card">
      <div class="card-header">
        <h2>Recent Verification Requests</h2>
        <a routerLink="/admin/lawyer-verification" class="view-all-link">View All</a>
      </div>
      <div class="card-content">
        <div class="verification-list">
          <div class="verification-item" *ngFor="let verification of recentVerifications">
            <div class="lawyer-info">
              <div class="lawyer-avatar">
                <div class="avatar-placeholder" *ngIf="!verification.avatar">
                  {{ getInitials(verification.name) }}
                </div>
                <img *ngIf="verification.avatar"
                     [src]="verification.avatar"
                     [alt]="verification.name"
                     (error)="onImageError($event, verification)">
              </div>
              <div class="lawyer-details">
                <h4>{{ verification.name }}</h4>
                <p>Roll No: {{ verification.rollNumber }}</p>
                <p>{{ verification.firm }}</p>
              </div>
            </div>
            <div class="verification-status">
              <span class="status-badge" [class]="verification.status">
                {{ verification.status | titlecase }}
              </span>
              <span class="verification-time">{{ verification.submittedAt | date:'short' }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- System Activity -->
    <div class="dashboard-card">
      <div class="card-header">
        <h2>System Activity</h2>
        <select class="time-filter" [(ngModel)]="activityFilter" (change)="filterActivity()">
          <option value="today">Today</option>
          <option value="week">This Week</option>
          <option value="month">This Month</option>
        </select>
      </div>
      <div class="card-content">
        <div class="activity-list">
          <div class="activity-item" *ngFor="let activity of systemActivities">
            <div class="activity-icon" [class]="activity.type">
              {{ getActivityIcon(activity.type) }}
            </div>
            <div class="activity-content">
              <p>{{ activity.description }}</p>
              <span class="activity-time">{{ activity.timestamp | date:'short' }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>


    <!-- Platform Health -->
    <div class="dashboard-card">
      <div class="card-header">
        <h2>Platform Health</h2>
      </div>
      <div class="card-content">
        <div class="health-metrics">
          <div class="metric">
            <div class="metric-label">Server Status</div>
            <div class="metric-value healthy">
              <span class="status-dot"></span>
              Online
            </div>
          </div>
          <div class="metric">
            <div class="metric-label">Database</div>
            <div class="metric-value healthy">
              <span class="status-dot"></span>
              Connected
            </div>
          </div>
          <div class="metric">
            <div class="metric-label">Storage</div>
            <div class="metric-value warning">
              <span class="status-dot"></span>
              75% Used
            </div>
          </div>
          <div class="metric">
            <div class="metric-label">API Response</div>
            <div class="metric-value healthy">
              <span class="status-dot"></span>
              {{ platformHealth.apiResponseTime }}ms
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
