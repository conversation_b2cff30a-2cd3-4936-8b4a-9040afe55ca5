import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { FirebaseService, ClientProfile } from '../services/firebase.service';
import { MockDataService, MockLawyer, MockCase, MockAppointment } from '../services/mock-data.service';

interface LawyerCard {
  id: string;
  name: string;
  firm: string;
  avatar: string;
  nextAppointment?: string;
  isMyLawyer: boolean;
}

interface DocumentTemplate {
  id: string;
  title: string;
  icon: string;
  description: string;
}

@Component({
  selector: 'app-client-home',
  templateUrl: './client-home.page.html',
  styleUrls: ['./client-home.page.scss'],
  standalone: false,
})
export class ClientHomePage implements OnInit {
  clientProfile: ClientProfile | null = null;
  userName = 'Client';

  myLawyer: LawyerCard | null = null;
  upcomingAppointment: MockAppointment | null = null;
  recentCase: MockCase | null = null;

  documentTemplates: DocumentTemplate[] = [
    {
      id: '1',
      title: 'Legal Document Template',
      icon: 'document-text',
      description: 'Generate legal documents quickly'
    },
    {
      id: '2',
      title: 'Contract Template',
      icon: 'contract',
      description: 'Create contracts and agreements'
    },
    {
      id: '3',
      title: 'Affidavit Template',
      icon: 'clipboard',
      description: 'Generate sworn statements'
    }
  ];

  constructor(
    private router: Router,
    private firebaseService: FirebaseService,
    private mockDataService: MockDataService
  ) { }

  ngOnInit() {
    this.loadClientProfile();
    this.loadDashboardData();
  }

  async loadClientProfile() {
    const currentUser = this.firebaseService.getCurrentUser();
    if (currentUser) {
      this.clientProfile = await this.firebaseService.getClientProfile(currentUser.uid);
      if (this.clientProfile) {
        this.userName = this.clientProfile.name;
      }
    }
  }

  onSearchLawyers() {
    this.router.navigate(['/client-tabs/search']);
  }

  onViewLawyerDetails() {
    if (this.myLawyer) {
      this.router.navigate(['/lawyer-detail', this.myLawyer.id]);
    }
  }

  onGenerateDocument(template: DocumentTemplate) {
    this.router.navigate(['/generate-document', template.id]);
  }

  onViewAllDocuments() {
    this.router.navigate(['/client-tabs/documents']);
  }

  async loadDashboardData() {
    // Load my lawyer (first available lawyer for demo)
    this.mockDataService.getLawyers().subscribe(lawyers => {
      if (lawyers.length > 0) {
        const lawyer = lawyers[0];
        this.myLawyer = {
          id: lawyer.id,
          name: lawyer.name,
          firm: lawyer.firm,
          avatar: lawyer.avatar,
          nextAppointment: 'Tomorrow, 2:00 PM',
          isMyLawyer: true
        };
      }
    });

    // Load upcoming appointment
    this.mockDataService.getAppointments().subscribe(appointments => {
      this.upcomingAppointment = appointments.find(apt => apt.status === 'confirmed') || null;
    });

    // Load recent case
    this.mockDataService.getCases().subscribe(cases => {
      this.recentCase = cases.find(c => c.status === 'ongoing') || null;
    });
  }

  onTrackCase() {
    if (this.recentCase) {
      this.router.navigate(['/track-case', this.recentCase.id]);
    } else {
      this.router.navigate(['/client-tabs/search']);
    }
  }

  onDownloadDocuments() {
    this.router.navigate(['/download-documents']);
  }

  getGreeting(): string {
    const hour = new Date().getHours();
    if (hour < 12) return 'Good morning';
    if (hour < 17) return 'Good afternoon';
    return 'Good evening';
  }
}
