{"version": 3, "file": "src_app_secretary-dashboard_secretary-dashboard_module_ts.js", "mappings": ";;;;;;;;;;;;;;;;AACuD;AAEa;;;AAEpE,MAAME,MAAM,GAAW,CACrB;EACEC,IAAI,EAAE,EAAE;EACRC,SAAS,EAAEH,6EAAsBA;CAClC,CACF;AAMK,MAAOI,mCAAmC;;;uBAAnCA,mCAAmC;IAAA;EAAA;;;YAAnCA;IAAmC;EAAA;;;gBAHpCL,yDAAY,CAACM,QAAQ,CAACJ,MAAM,CAAC,EAC7BF,yDAAY;IAAA;EAAA;;;sHAEXK,mCAAmC;IAAAE,OAAA,GAAAC,yDAAA;IAAAC,OAAA,GAFpCT,yDAAY;EAAA;AAAA,K;;;;;;;;;;;;;;;;;;;;ACbuB;AACF;AACA;AAE8C;AACvB;;AAW9D,MAAOa,4BAA4B;;;uBAA5BA,4BAA4B;IAAA;EAAA;;;YAA5BA;IAA4B;EAAA;;;gBAPrCH,yDAAY,EACZC,uDAAW,EACXC,uDAAW,EACXP,oGAAmC;IAAA;EAAA;;;sHAI1BQ,4BAA4B;IAAAC,YAAA,GAFxBb,6EAAsB;IAAAM,OAAA,GALnCG,yDAAY,EACZC,uDAAW,EACXC,uDAAW,EACXP,oGAAmC;EAAA;AAAA,K;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IEuE/BU,4DAAA,cAAgG;IAAlCA,wDAAA,mBAAAG,kEAAA;MAAA,MAAAC,SAAA,GAAAJ,2DAAA,CAAAM,GAAA,EAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAR,2DAAA;MAAA,OAAAA,yDAAA,CAASQ,MAAA,CAAAG,YAAA,CAAAP,SAAA,CAAAQ,EAAA,CAAuB;IAAA,EAAC;IAE3FZ,4DADF,cAAyB,aACyC;IAAAA,oDAAA,GAAiB;IAAAA,0DAAA,EAAK;IAEpFA,4DADF,cAA0B,eACA;IAAAA,oDAAA,GAA4B;IAAAA,0DAAA,EAAO;IAC3DA,4DAAA,eAAwB;IAAAA,oDAAA,GAA0C;IACpEA,0DADoE,EAAO,EACrE;IACNA,4DAAA,YAA2D;IAAAA,oDAAA,IAAwC;IACrGA,0DADqG,EAAI,EACnG;IACNA,uDAAA,oBAAiE;IACnEA,0DAAA,EAAM;;;;IAR8DA,uDAAA,GAAiB;IAAjBA,+DAAA,CAAAI,SAAA,CAAAc,IAAA,CAAiB;IAEvDlB,uDAAA,GAA4B;IAA5BA,gEAAA,KAAAI,SAAA,CAAAgB,SAAA,WAA4B;IAC5BpB,uDAAA,GAA0C;IAA1CA,gEAAA,KAAAI,SAAA,CAAAiB,gBAAA,kBAA0C;IAETrB,uDAAA,GAAwC;IAAxCA,gEAAA,oBAAAI,SAAA,CAAAkB,YAAA,KAAwC;;;;;;IAdvGtB,4DAFJ,cAAsD,cACxB,aACsC;IAAAA,oDAAA,qBAAc;IAAAA,0DAAA,EAAK;IACnFA,4DAAA,iBAAuF;IAA5BA,wDAAA,mBAAAuB,+DAAA;MAAAvB,2DAAA,CAAAwB,GAAA;MAAA,MAAAhB,MAAA,GAAAR,2DAAA;MAAA,OAAAA,yDAAA,CAASQ,MAAA,CAAAiB,eAAA,EAAiB;IAAA,EAAC;IACpFzB,oDAAA,mBACF;IACFA,0DADE,EAAS,EACL;IAENA,4DAAA,cAA0B;IACxBA,wDAAA,IAAA2B,4CAAA,mBAAgG;IAYpG3B,0DADE,EAAM,EACF;;;;IAZ0CA,uDAAA,GAAgB;IAAhBA,wDAAA,YAAAQ,MAAA,CAAAqB,aAAA,CAAgB;;;;;;IAgB9D7B,4DADF,cAAwD,cAC7B;IACvBA,uDAAA,mBAA8D;IAC9DA,4DAAA,aAA8D;IAAAA,oDAAA,wBAAiB;IAAAA,0DAAA,EAAK;IACpFA,4DAAA,YAA+D;IAC7DA,oDAAA,gEACF;IAAAA,0DAAA,EAAI;IACJA,4DAAA,iBAAgE;IAA5BA,wDAAA,mBAAA8B,+DAAA;MAAA9B,2DAAA,CAAA+B,GAAA;MAAA,MAAAvB,MAAA,GAAAR,2DAAA;MAAA,OAAAA,yDAAA,CAASQ,MAAA,CAAAiB,eAAA,EAAiB;IAAA,EAAC;IAC7DzB,oDAAA,yBACF;IAEJA,0DAFI,EAAS,EACL,EACF;;;;;IAOAA,4DADF,cAAqE,cACxC;IACzBA,uDAAA,mBAAsF;IACxFA,0DAAA,EAAM;IAEJA,4DADF,cAA8B,YACoB;IAAAA,oDAAA,GAA0B;IAAAA,0DAAA,EAAI;IAE5EA,4DADF,cAA2B,eACqC;IAAAA,oDAAA,GAAmB;IAAAA,0DAAA,EAAO;IACxFA,4DAAA,eAAgE;IAAAA,oDAAA,IAAqB;IAG3FA,0DAH2F,EAAO,EACxF,EACF,EACF;;;;IATQA,uDAAA,GAAgE;IAAhEA,wDAAA,SAAAgC,WAAA,CAAAC,IAAA,2CAAgE;IAG1BjC,uDAAA,GAA0B;IAA1BA,+DAAA,CAAAgC,WAAA,CAAAE,WAAA,CAA0B;IAEVlC,uDAAA,GAAmB;IAAnBA,+DAAA,CAAAgC,WAAA,CAAAG,IAAA,CAAmB;IACjBnC,uDAAA,GAAqB;IAArBA,+DAAA,CAAAgC,WAAA,CAAAI,MAAA,CAAqB;;;;;IAV7FpC,4DADF,cAAyD,aACS;IAAAA,oDAAA,wBAAiB;IAAAA,0DAAA,EAAK;IACtFA,4DAAA,cAA2B;IACzBA,wDAAA,IAAAqC,4CAAA,mBAAqE;IAazErC,0DADE,EAAM,EACF;;;;IAb8CA,uDAAA,GAAmB;IAAnBA,wDAAA,YAAAQ,MAAA,CAAA8B,gBAAA,CAAmB;;;;;IAwB7DtC,4DAAA,eAAgF;IAAAA,oDAAA,GAAiB;IAAAA,0DAAA,EAAO;;;;IAAxBA,uDAAA,EAAiB;IAAjBA,+DAAA,CAAAuC,OAAA,CAAAH,MAAA,CAAiB;;;;;IACjGpC,4DAAA,eAA8E;IAAAA,oDAAA,GAAuB;IAAAA,0DAAA,EAAO;;;;IAA9BA,uDAAA,EAAuB;IAAvBA,gEAAA,UAAAuC,OAAA,CAAAC,OAAA,KAAuB;;;;;IAN3GxC,4DAAA,cAAyD;IACvDA,uDAAA,cAAuE;IAErEA,4DADF,cAA0B,YACoB;IAAAA,oDAAA,GAAsB;IAAAA,0DAAA,EAAI;IACtEA,4DAAA,cAAuB;IAErBA,wDADA,IAAAyC,mDAAA,mBAAgF,IAAAC,mDAAA,mBACF;IAElF1C,0DADE,EAAM,EACF;IACNA,uDAAA,mBAAiE;IACnEA,0DAAA,EAAM;;;;IATuBA,uDAAA,EAAqC;IAArCA,wDAAA,eAAAuC,OAAA,CAAAK,QAAA,CAAqC;IAElB5C,uDAAA,GAAsB;IAAtBA,+DAAA,CAAAuC,OAAA,CAAAL,WAAA,CAAsB;IAEHlC,uDAAA,GAAiB;IAAjBA,wDAAA,SAAAuC,OAAA,CAAAH,MAAA,CAAiB;IACpBpC,uDAAA,EAAkB;IAAlBA,wDAAA,SAAAuC,OAAA,CAAAC,OAAA,CAAkB;;;;;IARpFxC,4DADF,cAAqD,aACa;IAAAA,oDAAA,oBAAa;IAAAA,0DAAA,EAAK;IAClFA,4DAAA,cAAuB;IACrBA,wDAAA,IAAA6C,4CAAA,kBAAyD;IAY7D7C,0DADE,EAAM,EACF;;;;IAZsCA,uDAAA,GAAe;IAAfA,wDAAA,YAAAQ,MAAA,CAAAsC,YAAA,CAAe;;;ADhHzD,MAAO5D,sBAAsB;EAajC6D,YACUC,MAAc,EACdC,eAAgC;IADhC,KAAAD,MAAM,GAANA,MAAM;IACN,KAAAC,eAAe,GAAfA,eAAe;IAdzB,KAAAC,aAAa,GAAG,WAAW;IAC3B,KAAAC,gBAAgB,GAA4B,IAAI;IAChD,KAAAC,KAAK,GAAmB;MACtBvB,aAAa,EAAE,CAAC;MAChBwB,UAAU,EAAE,CAAC;MACbC,iBAAiB,EAAE;KACpB;IAED,KAAAzB,aAAa,GAAiB,EAAE;IAChC,KAAAS,gBAAgB,GAAU,EAAE;IAC5B,KAAAQ,YAAY,GAAU,EAAE;EAKpB;EAEJS,QAAQA,CAAA;IACN,IAAI,CAACC,oBAAoB,EAAE;IAC3B,IAAI,CAACC,iBAAiB,EAAE;EAC1B;EAEMD,oBAAoBA,CAAA;IAAA,IAAAE,KAAA;IAAA,OAAAC,6KAAA;MACxB,MAAMC,WAAW,GAAGF,KAAI,CAACT,eAAe,CAACY,cAAc,EAAE;MACzD,IAAID,WAAW,EAAE;QACfF,KAAI,CAACP,gBAAgB,SAASO,KAAI,CAACT,eAAe,CAACa,mBAAmB,CAACF,WAAW,CAACG,GAAG,CAAC;QACvF,IAAIL,KAAI,CAACP,gBAAgB,EAAE;UACzBO,KAAI,CAACR,aAAa,GAAGQ,KAAI,CAACP,gBAAgB,CAACjC,IAAI;;;IAElD;EACH;EAEMuC,iBAAiBA,CAAA;IAAA,IAAAO,MAAA;IAAA,OAAAL,6KAAA;MACrB,MAAMC,WAAW,GAAGI,MAAI,CAACf,eAAe,CAACY,cAAc,EAAE;MACzD,IAAID,WAAW,EAAE;QACf,IAAI;UACF;UACA,MAAMK,OAAO,SAASD,MAAI,CAACf,eAAe,CAACiB,yBAAyB,CAACN,WAAW,CAACG,GAAG,CAAC;UACrFC,MAAI,CAACZ,KAAK,CAACvB,aAAa,GAAGoC,OAAO,CAACE,MAAM;UAEzCH,MAAI,CAACnC,aAAa,GAAGoC,OAAO,CAACG,GAAG,CAAEhC,MAAM,KAAM;YAC5CxB,EAAE,EAAEwB,MAAM,CAAC2B,GAAG;YACd7C,IAAI,EAAEkB,MAAM,CAAClB,IAAI;YACjBE,SAAS,EAAE,CAAC;YACZC,gBAAgB,EAAE,CAAC;YACnBC,YAAY,EAAE;WACf,CAAC,CAAC;UAEH;UACA0C,MAAI,CAAC1B,gBAAgB,GAAG,CACtB;YACEL,IAAI,EAAE,aAAa;YACnBC,WAAW,EAAE,oCAAoC;YACjDC,IAAI,EAAE,aAAa;YACnBC,MAAM,EAAE;WACT,EACD;YACEH,IAAI,EAAE,MAAM;YACZC,WAAW,EAAE,oCAAoC;YACjDC,IAAI,EAAE,aAAa;YACnBC,MAAM,EAAE;WACT,CACF;UAED;UACA4B,MAAI,CAAClB,YAAY,GAAG,CAClB;YACEb,IAAI,EAAE,UAAU;YAChBC,WAAW,EAAE,4CAA4C;YACzDU,QAAQ,EAAE,MAAM;YAChBR,MAAM,EAAE;WACT,EACD;YACEH,IAAI,EAAE,UAAU;YAChBC,WAAW,EAAE,qCAAqC;YAClDU,QAAQ,EAAE,QAAQ;YAClBJ,OAAO,EAAE;WACV,CACF;SAEF,CAAC,OAAO6B,KAAK,EAAE;UACdC,OAAO,CAACD,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;;;IAExD;EACH;EAEA1D,YAAYA,CAAC4D,QAAgB;IAC3B,IAAI,CAACvB,MAAM,CAACwB,QAAQ,CAAC,CAAC,mBAAmB,EAAED,QAAQ,CAAC,CAAC;EACvD;EAEAE,gBAAgBA,CAAA;IACd,IAAI,CAACzB,MAAM,CAACwB,QAAQ,CAAC,CAAC,qBAAqB,CAAC,CAAC;EAC/C;EAEAE,aAAaA,CAAA;IACX,IAAI,CAAC1B,MAAM,CAACwB,QAAQ,CAAC,CAAC,kBAAkB,CAAC,CAAC;EAC5C;EAEAG,aAAaA,CAAA;IACX,IAAI,CAAC3B,MAAM,CAACwB,QAAQ,CAAC,CAAC,kBAAkB,CAAC,CAAC;EAC5C;EAEAI,cAAcA,CAAA;IACZ,IAAI,CAAC5B,MAAM,CAACwB,QAAQ,CAAC,CAAC,qBAAqB,CAAC,CAAC;EAC/C;EAEAK,cAAcA,CAAA;IACZ,IAAI,CAAC7B,MAAM,CAACwB,QAAQ,CAAC,CAAC,kBAAkB,CAAC,CAAC;EAC5C;EAEA/C,eAAeA,CAAA;IACb,IAAI,CAACuB,MAAM,CAACwB,QAAQ,CAAC,CAAC,wBAAwB,CAAC,CAAC;EAClD;EAEAM,WAAWA,CAAA;IACT,MAAMC,IAAI,GAAG,IAAIC,IAAI,EAAE,CAACC,QAAQ,EAAE;IAClC,IAAIF,IAAI,GAAG,EAAE,EAAE,OAAO,cAAc;IACpC,IAAIA,IAAI,GAAG,EAAE,EAAE,OAAO,gBAAgB;IACtC,OAAO,cAAc;EACvB;;;uBAxHW7F,sBAAsB,EAAAc,+DAAA,CAAAP,mDAAA,GAAAO,+DAAA,CAAAoF,uEAAA;IAAA;EAAA;;;YAAtBlG,sBAAsB;MAAAoG,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,gCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCjB3B5F,4DANR,qBAA2D,aACF,aAGvB,aACE,YAC+C;UACzEA,oDAAA,GACF;UAAAA,0DAAA,EAAK;UACLA,4DAAA,WAAgE;UAC9DA,oDAAA,oDACF;UAEJA,0DAFI,EAAI,EACA,EACF;UAKFA,4DAFJ,aAAwB,aACC,cACE;UACrBA,uDAAA,mBAA2C;UAC7CA,0DAAA,EAAM;UAEJA,4DADF,eAA0B,cACmC;UAAAA,oDAAA,IAAyB;UAAAA,0DAAA,EAAK;UACzFA,4DAAA,aAAwD;UAAAA,oDAAA,sBAAc;UAE1EA,0DAF0E,EAAI,EACtE,EACF;UAGJA,4DADF,cAAuB,cACE;UACrBA,uDAAA,oBAA8C;UAChDA,0DAAA,EAAM;UAEJA,4DADF,eAA0B,cACmC;UAAAA,oDAAA,IAAsB;UAAAA,0DAAA,EAAK;UACtFA,4DAAA,aAAwD;UAAAA,oDAAA,mBAAW;UAEvEA,0DAFuE,EAAI,EACnE,EACF;UAGJA,4DADF,cAAuB,cACE;UACrBA,uDAAA,oBAA6C;UAC/CA,0DAAA,EAAM;UAEJA,4DADF,eAA0B,cACmC;UAAAA,oDAAA,IAA6B;UAAAA,0DAAA,EAAK;UAC7FA,4DAAA,aAAwD;UAAAA,oDAAA,oBAAY;UAG1EA,0DAH0E,EAAI,EACpE,EACF,EACF;UAIJA,4DADF,eAAqB,cAC6C;UAAAA,oDAAA,qBAAa;UAAAA,0DAAA,EAAK;UAEhFA,4DADF,eAAyB,kBACiC;UAA7BA,wDAAA,mBAAA8F,yDAAA;YAAA,OAASD,GAAA,CAAApB,gBAAA,EAAkB;UAAA,EAAC;UACrDzE,uDAAA,oBAAyD;UACzDA,4DAAA,gBAA2B;UAAAA,oDAAA,gBAAQ;UACrCA,0DADqC,EAAO,EACnC;UAETA,4DAAA,kBAAqD;UAA1BA,wDAAA,mBAAA+F,yDAAA;YAAA,OAASF,GAAA,CAAAlB,aAAA,EAAe;UAAA,EAAC;UAClD3E,uDAAA,oBAAuD;UACvDA,4DAAA,gBAA2B;UAAAA,oDAAA,aAAK;UAClCA,0DADkC,EAAO,EAChC;UAETA,4DAAA,kBAAqD;UAA1BA,wDAAA,mBAAAgG,yDAAA;YAAA,OAASH,GAAA,CAAAnB,aAAA,EAAe;UAAA,EAAC;UAClD1E,uDAAA,oBAAyD;UACzDA,4DAAA,gBAA2B;UAAAA,oDAAA,aAAK;UAClCA,0DADkC,EAAO,EAChC;UAETA,4DAAA,kBAAsD;UAA3BA,wDAAA,mBAAAiG,yDAAA;YAAA,OAASJ,GAAA,CAAAjB,cAAA,EAAgB;UAAA,EAAC;UACnD5E,uDAAA,oBAAqD;UACrDA,4DAAA,gBAA2B;UAAAA,oDAAA,gBAAQ;UAGzCA,0DAHyC,EAAO,EACnC,EACL,EACF;UA4DNA,wDAzDA,KAAAkG,sCAAA,kBAAsD,KAAAC,sCAAA,kBAwBE,KAAAC,sCAAA,kBAcC,KAAAC,sCAAA,kBAmBJ;UAkBzDrG,0DADE,EAAM,EACM;;;UA/IJA,uDAAA,GACF;UADEA,gEAAA,MAAA6F,GAAA,CAAAf,WAAA,UAAAe,GAAA,CAAA3C,aAAA,OACF;UAc6DlD,uDAAA,GAAyB;UAAzBA,+DAAA,CAAA6F,GAAA,CAAAzC,KAAA,CAAAvB,aAAA,CAAyB;UAUzB7B,uDAAA,GAAsB;UAAtBA,+DAAA,CAAA6F,GAAA,CAAAzC,KAAA,CAAAC,UAAA,CAAsB;UAUtBrD,uDAAA,GAA6B;UAA7BA,+DAAA,CAAA6F,GAAA,CAAAzC,KAAA,CAAAE,iBAAA,CAA6B;UAiCxEtD,uDAAA,IAA8B;UAA9BA,wDAAA,SAAA6F,GAAA,CAAAhE,aAAA,CAAAsC,MAAA,KAA8B;UAwB9BnE,uDAAA,EAAgC;UAAhCA,wDAAA,SAAA6F,GAAA,CAAAhE,aAAA,CAAAsC,MAAA,OAAgC;UAchCnE,uDAAA,EAAiC;UAAjCA,wDAAA,SAAA6F,GAAA,CAAAvD,gBAAA,CAAA6B,MAAA,KAAiC;UAmBjCnE,uDAAA,EAA6B;UAA7BA,wDAAA,SAAA6F,GAAA,CAAA/C,YAAA,CAAAqB,MAAA,KAA6B", "sources": ["./src/app/secretary-dashboard/secretary-dashboard-routing.module.ts", "./src/app/secretary-dashboard/secretary-dashboard.module.ts", "./src/app/secretary-dashboard/secretary-dashboard.page.ts", "./src/app/secretary-dashboard/secretary-dashboard.page.html"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { Routes, RouterModule } from '@angular/router';\r\n\r\nimport { SecretaryDashboardPage } from './secretary-dashboard.page';\r\n\r\nconst routes: Routes = [\r\n  {\r\n    path: '',\r\n    component: SecretaryDashboardPage\r\n  }\r\n];\r\n\r\n@NgModule({\r\n  imports: [RouterModule.forChild(routes)],\r\n  exports: [RouterModule],\r\n})\r\nexport class SecretaryDashboardPageRoutingModule {}\r\n", "import { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { FormsModule } from '@angular/forms';\r\nimport { IonicModule } from '@ionic/angular';\r\n\r\nimport { SecretaryDashboardPageRoutingModule } from './secretary-dashboard-routing.module';\r\nimport { SecretaryDashboardPage } from './secretary-dashboard.page';\r\n\r\n@NgModule({\r\n  imports: [\r\n    CommonModule,\r\n    FormsModule,\r\n    IonicModule,\r\n    SecretaryDashboardPageRoutingModule\r\n  ],\r\n  declarations: [SecretaryDashboardPage]\r\n})\r\nexport class SecretaryDashboardPageModule {}\r\n", "import { Component, OnInit } from '@angular/core';\r\nimport { Router } from '@angular/router';\r\nimport { FirebaseService, SecretaryProfile, LawyerProfile } from '../services/firebase.service';\r\n\r\ninterface DashboardStats {\r\n  linkedLawyers: number;\r\n  totalCases: number;\r\n  totalAppointments: number;\r\n}\r\n\r\ninterface LawyerCard {\r\n  id: string;\r\n  name: string;\r\n  caseCount: number;\r\n  appointmentCount: number;\r\n  lastActivity: string;\r\n}\r\n\r\n@Component({\r\n  selector: 'app-secretary-dashboard',\r\n  templateUrl: './secretary-dashboard.page.html',\r\n  styleUrls: ['./secretary-dashboard.page.scss'],\r\n})\r\nexport class SecretaryDashboardPage implements OnInit {\r\n  secretaryName = 'Secretary';\r\n  secretaryProfile: SecretaryProfile | null = null;\r\n  stats: DashboardStats = {\r\n    linkedLawyers: 0,\r\n    totalCases: 0,\r\n    totalAppointments: 0\r\n  };\r\n  \r\n  linkedLawyers: LawyerCard[] = [];\r\n  recentActivities: any[] = [];\r\n  pendingTasks: any[] = [];\r\n\r\n  constructor(\r\n    private router: Router,\r\n    private firebaseService: FirebaseService\r\n  ) { }\r\n\r\n  ngOnInit() {\r\n    this.loadSecretaryProfile();\r\n    this.loadDashboardData();\r\n  }\r\n\r\n  async loadSecretaryProfile() {\r\n    const currentUser = this.firebaseService.getCurrentUser();\r\n    if (currentUser) {\r\n      this.secretaryProfile = await this.firebaseService.getSecretaryProfile(currentUser.uid);\r\n      if (this.secretaryProfile) {\r\n        this.secretaryName = this.secretaryProfile.name;\r\n      }\r\n    }\r\n  }\r\n\r\n  async loadDashboardData() {\r\n    const currentUser = this.firebaseService.getCurrentUser();\r\n    if (currentUser) {\r\n      try {\r\n        // Load linked lawyers\r\n        const lawyers = await this.firebaseService.getSecretaryLinkedLawyers(currentUser.uid);\r\n        this.stats.linkedLawyers = lawyers.length;\r\n        \r\n        this.linkedLawyers = lawyers.map((lawyer) => ({\r\n          id: lawyer.uid,\r\n          name: lawyer.name,\r\n          caseCount: 0, // Will be loaded separately\r\n          appointmentCount: 0, // Will be loaded separately\r\n          lastActivity: 'Today'\r\n        }));\r\n\r\n        // Load recent activities (mock for now)\r\n        this.recentActivities = [\r\n          {\r\n            type: 'appointment',\r\n            description: 'Scheduled appointment for John Doe',\r\n            time: '2 hours ago',\r\n            lawyer: 'Atty. Smith'\r\n          },\r\n          {\r\n            type: 'case',\r\n            description: 'Updated case progress for ABC Corp',\r\n            time: '4 hours ago',\r\n            lawyer: 'Atty. Johnson'\r\n          }\r\n        ];\r\n\r\n        // Load pending tasks (mock for now)\r\n        this.pendingTasks = [\r\n          {\r\n            type: 'approval',\r\n            description: 'Client information update pending approval',\r\n            priority: 'high',\r\n            lawyer: 'Atty. Smith'\r\n          },\r\n          {\r\n            type: 'reminder',\r\n            description: 'Send appointment reminder to client',\r\n            priority: 'medium',\r\n            dueTime: '30 minutes'\r\n          }\r\n        ];\r\n\r\n      } catch (error) {\r\n        console.error('Error loading dashboard data:', error);\r\n      }\r\n    }\r\n  }\r\n\r\n  onViewLawyer(lawyerId: string) {\r\n    this.router.navigate(['/secretary/lawyer', lawyerId]);\r\n  }\r\n\r\n  onManageCalendar() {\r\n    this.router.navigate(['/secretary/calendar']);\r\n  }\r\n\r\n  onManageFiles() {\r\n    this.router.navigate(['/secretary/files']);\r\n  }\r\n\r\n  onManageCases() {\r\n    this.router.navigate(['/secretary/cases']);\r\n  }\r\n\r\n  onViewFinances() {\r\n    this.router.navigate(['/secretary/finances']);\r\n  }\r\n\r\n  onViewAuditLog() {\r\n    this.router.navigate(['/secretary/audit']);\r\n  }\r\n\r\n  onLinkNewLawyer() {\r\n    this.router.navigate(['/secretary/link-lawyer']);\r\n  }\r\n\r\n  getGreeting(): string {\r\n    const hour = new Date().getHours();\r\n    if (hour < 12) return 'Good morning';\r\n    if (hour < 17) return 'Good afternoon';\r\n    return 'Good evening';\r\n  }\r\n}\r\n", "<ion-content class=\"dashboard-content veritus-gradient-bg\">\r\n  <div class=\"dashboard-container veritus-safe-area-top\">\r\n    \r\n    <!-- Header -->\r\n    <div class=\"dashboard-header\">\r\n      <div class=\"greeting-section\">\r\n        <h1 class=\"greeting veritus-text-2xl veritus-font-bold veritus-text-white\">\r\n          {{ getGreeting() }}, {{ secretaryName }}!\r\n        </h1>\r\n        <p class=\"sub-greeting veritus-text-base veritus-text-white-70\">\r\n          Manage your lawyers' workspaces efficiently\r\n        </p>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Statistics Cards -->\r\n    <div class=\"stats-grid\">\r\n      <div class=\"stat-card\">\r\n        <div class=\"stat-icon\">\r\n          <ion-icon name=\"people-outline\"></ion-icon>\r\n        </div>\r\n        <div class=\"stat-content\">\r\n          <h3 class=\"stat-number veritus-text-2xl veritus-font-bold\">{{ stats.linkedLawyers }}</h3>\r\n          <p class=\"stat-label veritus-text-sm veritus-text-gray\">Linked Lawyers</p>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"stat-card\">\r\n        <div class=\"stat-icon\">\r\n          <ion-icon name=\"briefcase-outline\"></ion-icon>\r\n        </div>\r\n        <div class=\"stat-content\">\r\n          <h3 class=\"stat-number veritus-text-2xl veritus-font-bold\">{{ stats.totalCases }}</h3>\r\n          <p class=\"stat-label veritus-text-sm veritus-text-gray\">Total Cases</p>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"stat-card\">\r\n        <div class=\"stat-icon\">\r\n          <ion-icon name=\"calendar-outline\"></ion-icon>\r\n        </div>\r\n        <div class=\"stat-content\">\r\n          <h3 class=\"stat-number veritus-text-2xl veritus-font-bold\">{{ stats.totalAppointments }}</h3>\r\n          <p class=\"stat-label veritus-text-sm veritus-text-gray\">Appointments</p>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Quick Actions -->\r\n    <div class=\"section\">\r\n      <h2 class=\"section-title veritus-text-lg veritus-font-semibold\">Quick Actions</h2>\r\n      <div class=\"action-grid\">\r\n        <button class=\"action-btn\" (click)=\"onManageCalendar()\">\r\n          <ion-icon name=\"calendar\" class=\"action-icon\"></ion-icon>\r\n          <span class=\"action-label\">Calendar</span>\r\n        </button>\r\n        \r\n        <button class=\"action-btn\" (click)=\"onManageCases()\">\r\n          <ion-icon name=\"folder\" class=\"action-icon\"></ion-icon>\r\n          <span class=\"action-label\">Cases</span>\r\n        </button>\r\n        \r\n        <button class=\"action-btn\" (click)=\"onManageFiles()\">\r\n          <ion-icon name=\"document\" class=\"action-icon\"></ion-icon>\r\n          <span class=\"action-label\">Files</span>\r\n        </button>\r\n        \r\n        <button class=\"action-btn\" (click)=\"onViewFinances()\">\r\n          <ion-icon name=\"card\" class=\"action-icon\"></ion-icon>\r\n          <span class=\"action-label\">Finances</span>\r\n        </button>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Linked Lawyers -->\r\n    <div class=\"section\" *ngIf=\"linkedLawyers.length > 0\">\r\n      <div class=\"section-header\">\r\n        <h2 class=\"section-title veritus-text-lg veritus-font-semibold\">Linked Lawyers</h2>\r\n        <button class=\"link-btn veritus-text-sm veritus-text-gold\" (click)=\"onLinkNewLawyer()\">\r\n          + Link New\r\n        </button>\r\n      </div>\r\n      \r\n      <div class=\"lawyer-cards\">\r\n        <div class=\"lawyer-card\" *ngFor=\"let lawyer of linkedLawyers\" (click)=\"onViewLawyer(lawyer.id)\">\r\n          <div class=\"lawyer-info\">\r\n            <h3 class=\"lawyer-name veritus-text-base veritus-font-semibold\">{{ lawyer.name }}</h3>\r\n            <div class=\"lawyer-stats\">\r\n              <span class=\"stat-item\">{{ lawyer.caseCount }} cases</span>\r\n              <span class=\"stat-item\">{{ lawyer.appointmentCount }} appointments</span>\r\n            </div>\r\n            <p class=\"last-activity veritus-text-xs veritus-text-gray\">Last activity: {{ lawyer.lastActivity }}</p>\r\n          </div>\r\n          <ion-icon name=\"chevron-forward\" class=\"chevron-icon\"></ion-icon>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- No Lawyers Linked -->\r\n    <div class=\"section\" *ngIf=\"linkedLawyers.length === 0\">\r\n      <div class=\"empty-state\">\r\n        <ion-icon name=\"people-outline\" class=\"empty-icon\"></ion-icon>\r\n        <h3 class=\"empty-title veritus-text-lg veritus-font-semibold\">No Lawyers Linked</h3>\r\n        <p class=\"empty-description veritus-text-sm veritus-text-gray\">\r\n          Request access to a lawyer's workspace using their code\r\n        </p>\r\n        <button class=\"veritus-btn-primary\" (click)=\"onLinkNewLawyer()\">\r\n          Link with Lawyer\r\n        </button>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Recent Activities -->\r\n    <div class=\"section\" *ngIf=\"recentActivities.length > 0\">\r\n      <h2 class=\"section-title veritus-text-lg veritus-font-semibold\">Recent Activities</h2>\r\n      <div class=\"activity-list\">\r\n        <div class=\"activity-item\" *ngFor=\"let activity of recentActivities\">\r\n          <div class=\"activity-icon\">\r\n            <ion-icon [name]=\"activity.type === 'appointment' ? 'calendar' : 'folder'\"></ion-icon>\r\n          </div>\r\n          <div class=\"activity-content\">\r\n            <p class=\"activity-description veritus-text-sm\">{{ activity.description }}</p>\r\n            <div class=\"activity-meta\">\r\n              <span class=\"activity-time veritus-text-xs veritus-text-gray\">{{ activity.time }}</span>\r\n              <span class=\"activity-lawyer veritus-text-xs veritus-text-gold\">{{ activity.lawyer }}</span>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Pending Tasks -->\r\n    <div class=\"section\" *ngIf=\"pendingTasks.length > 0\">\r\n      <h2 class=\"section-title veritus-text-lg veritus-font-semibold\">Pending Tasks</h2>\r\n      <div class=\"task-list\">\r\n        <div class=\"task-item\" *ngFor=\"let task of pendingTasks\">\r\n          <div class=\"task-priority\" [class]=\"'priority-' + task.priority\"></div>\r\n          <div class=\"task-content\">\r\n            <p class=\"task-description veritus-text-sm\">{{ task.description }}</p>\r\n            <div class=\"task-meta\">\r\n              <span class=\"task-lawyer veritus-text-xs veritus-text-gold\" *ngIf=\"task.lawyer\">{{ task.lawyer }}</span>\r\n              <span class=\"task-due veritus-text-xs veritus-text-gray\" *ngIf=\"task.dueTime\">Due: {{ task.dueTime }}</span>\r\n            </div>\r\n          </div>\r\n          <ion-icon name=\"chevron-forward\" class=\"chevron-icon\"></ion-icon>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n  </div>\r\n</ion-content>\r\n"], "names": ["RouterModule", "SecretaryDashboardPage", "routes", "path", "component", "SecretaryDashboardPageRoutingModule", "<PERSON><PERSON><PERSON><PERSON>", "imports", "i1", "exports", "CommonModule", "FormsModule", "IonicModule", "SecretaryDashboardPageModule", "declarations", "i0", "ɵɵelementStart", "ɵɵlistener", "SecretaryDashboardPage_div_53_div_7_Template_div_click_0_listener", "lawyer_r4", "ɵɵrestoreView", "_r3", "$implicit", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "onView<PERSON>awyer", "id", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement", "ɵɵadvance", "ɵɵtextInterpolate", "name", "ɵɵtextInterpolate1", "caseCount", "appointmentCount", "lastActivity", "SecretaryDashboardPage_div_53_Template_button_click_4_listener", "_r1", "onLinkNewLawyer", "ɵɵtemplate", "SecretaryDashboardPage_div_53_div_7_Template", "ɵɵproperty", "linkedLawyers", "SecretaryDashboardPage_div_54_Template_button_click_7_listener", "_r5", "activity_r6", "type", "description", "time", "lawyer", "SecretaryDashboardPage_div_55_div_4_Template", "recentActivities", "task_r7", "dueTime", "SecretaryDashboardPage_div_56_div_4_span_6_Template", "SecretaryDashboardPage_div_56_div_4_span_7_Template", "ɵɵclassMap", "priority", "SecretaryDashboardPage_div_56_div_4_Template", "pendingTasks", "constructor", "router", "firebaseService", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "stats", "totalCases", "totalAppointments", "ngOnInit", "loadSecretaryProfile", "loadDashboardData", "_this", "_asyncToGenerator", "currentUser", "getCurrentUser", "getSecretaryProfile", "uid", "_this2", "lawyers", "getSecretaryLinkedLawyers", "length", "map", "error", "console", "lawyerId", "navigate", "onManageCalendar", "onManageFiles", "onManageCases", "onViewFinances", "onViewAuditLog", "getGreeting", "hour", "Date", "getHours", "ɵɵdirectiveInject", "Router", "i2", "FirebaseService", "selectors", "decls", "vars", "consts", "template", "SecretaryDashboardPage_Template", "rf", "ctx", "SecretaryDashboardPage_Template_button_click_37_listener", "SecretaryDashboardPage_Template_button_click_41_listener", "SecretaryDashboardPage_Template_button_click_45_listener", "SecretaryDashboardPage_Template_button_click_49_listener", "SecretaryDashboardPage_div_53_Template", "SecretaryDashboardPage_div_54_Template", "SecretaryDashboardPage_div_55_Template", "SecretaryDashboardPage_div_56_Template", "ɵɵtextInterpolate2"], "sourceRoot": "webpack:///", "x_google_ignoreList": []}