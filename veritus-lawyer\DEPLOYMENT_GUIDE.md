# Veritus Platform Deployment Guide

## 🏗️ **Complete Architecture Implementation**

This guide covers the deployment of the complete Veritus legal platform with all missing components now implemented.

## 📊 **Architecture Overview**

### **Applications:**
1. **Mobile App** (`veritus-lawyer/`) - Ionic+Angular for lawyers and clients
2. **Secretary Portal** (`veritus-secretary/`) - Angular web app for secretaries
3. **Cloud Functions** (`functions/`) - Backend business logic
4. **Security Rules** - Firestore and Storage protection

### **Deployment Targets:**
- **app.veritus.app** - Mobile PWA/native app
- **secretary.veritus.app** - Secretary web portal
- **Firebase Functions** - Backend services
- **Firestore/Storage** - Database and file storage

## 🚀 **Quick Start**

### **1. Prerequisites**
```bash
# Install required tools
npm install -g @angular/cli
npm install -g @ionic/cli
npm install -g firebase-tools

# Authenticate with Firebase
firebase login
```

### **2. Setup Firebase Project**
```bash
# Initialize Firebase in the project
firebase init

# Select:
# - Firestore
# - Functions
# - Hosting
# - Storage

# Configure project ID: veritus-620ca
```

### **3. Install Dependencies**
```bash
# Mobile app dependencies
cd veritus-lawyer
npm install

# Secretary portal dependencies
cd ../veritus-secretary
npm install

# Cloud Functions dependencies
cd ../veritus-lawyer/functions
npm install
```

### **4. Environment Configuration**
Create environment files:

**veritus-lawyer/src/environments/environment.ts:**
```typescript
export const environment = {
  production: false,
  firebase: {
    apiKey: "your-api-key",
    authDomain: "veritus-620ca.firebaseapp.com",
    projectId: "veritus-620ca",
    storageBucket: "veritus-620ca.appspot.com",
    messagingSenderId: "your-sender-id",
    appId: "your-app-id"
  }
};
```

**veritus-secretary/src/environments/environment.ts:**
```typescript
export const environment = {
  production: false,
  firebase: {
    // Same Firebase config
  }
};
```

## 🔧 **Development Setup**

### **1. Start Development Servers**
```bash
# Terminal 1: Mobile app
cd veritus-lawyer
ionic serve

# Terminal 2: Secretary portal
cd veritus-secretary
ng serve --port 4201

# Terminal 3: Firebase emulators
firebase emulators:start
```

### **2. Access Applications**
- **Mobile App**: http://localhost:8100
- **Secretary Portal**: http://localhost:4201
- **Firebase Emulator UI**: http://localhost:4000

## 📦 **Build Process**

### **1. Build Mobile App**
```bash
cd veritus-lawyer
npm run build
# Output: dist/
```

### **2. Build Secretary Portal**
```bash
cd veritus-secretary
npm run build
# Output: ../veritus-lawyer/dist-secretary/
```

### **3. Build Cloud Functions**
```bash
cd veritus-lawyer/functions
npm run build
# Output: lib/
```

## 🚀 **Deployment**

### **1. Deploy to Staging**
```bash
# Deploy all components
firebase use staging
firebase deploy

# Deploy specific components
firebase deploy --only hosting:mobile
firebase deploy --only hosting:secretary
firebase deploy --only functions
firebase deploy --only firestore:rules,firestore:indexes
firebase deploy --only storage
```

### **2. Deploy to Production**
```bash
firebase use production
firebase deploy
```

### **3. Custom Domain Setup**
```bash
# Add custom domains in Firebase Console
# - app.veritus.app → mobile hosting
# - secretary.veritus.app → secretary hosting
```

## 🔒 **Security Configuration**

### **1. Firestore Rules**
The `firestore.rules` file implements:
- Role-based access control
- Lawyer-secretary relationship validation
- Client data protection
- Audit log security

### **2. Storage Rules**
The `storage.rules` file implements:
- File access based on user roles
- Case file protection
- Profile image management
- Temporary upload cleanup

### **3. Cloud Functions Security**
- Authentication required for all functions
- Input validation and sanitization
- Rate limiting and abuse prevention
- Audit logging for all operations

## 📊 **Monitoring & Analytics**

### **1. Firebase Analytics**
```bash
# Enable Analytics in Firebase Console
# Track user engagement and app performance
```

### **2. Performance Monitoring**
```bash
# Add Performance SDK to both apps
npm install firebase/performance
```

### **3. Crashlytics**
```bash
# Add Crashlytics for error tracking
npm install firebase/crashlytics
```

## 🔄 **CI/CD Pipeline**

### **1. GitHub Actions Setup**
The `.github/workflows/deploy.yml` implements:
- Automated testing
- Multi-environment deployment
- Security scanning
- Performance monitoring

### **2. Required Secrets**
Add to GitHub repository secrets:
```
FIREBASE_TOKEN - Firebase CLI token
FIREBASE_SERVICE_ACCOUNT - Service account JSON
FIREBASE_TOKEN_PROD - Production Firebase token
FIREBASE_SERVICE_ACCOUNT_PROD - Production service account
```

### **3. Deployment Workflow**
1. **Push to main** → Triggers deployment
2. **Run tests** → Lint, unit tests, build
3. **Deploy staging** → Test environment
4. **Deploy functions** → Backend services
5. **Deploy production** → Live environment

## 📱 **Mobile App Deployment**

### **1. PWA Deployment**
```bash
# Build with PWA support
ng build --configuration=production

# Deploy to Firebase Hosting
firebase deploy --only hosting:mobile
```

### **2. Native App Build**
```bash
# Add platforms
ionic capacitor add ios
ionic capacitor add android

# Build native apps
ionic capacitor build ios
ionic capacitor build android

# Open in IDEs
ionic capacitor open ios
ionic capacitor open android
```

### **3. App Store Deployment**
- **iOS**: Upload to App Store Connect
- **Android**: Upload to Google Play Console

## 🌐 **Secretary Portal Deployment**

### **1. Web App Build**
```bash
cd veritus-secretary
ng build --configuration=production
```

### **2. Firebase Hosting**
```bash
firebase deploy --only hosting:secretary
```

### **3. Custom Domain**
Configure `secretary.veritus.app` in Firebase Console

## 🔧 **Maintenance**

### **1. Database Maintenance**
```bash
# Clean expired codes (automated via Cloud Function)
# Monitor storage usage
# Backup critical data
```

### **2. Security Updates**
```bash
# Regular dependency updates
npm audit fix

# Security rule reviews
# Access log monitoring
```

### **3. Performance Optimization**
```bash
# Bundle size analysis
npm run analyze

# Performance monitoring
# User experience metrics
```

## 📋 **Troubleshooting**

### **Common Issues:**

1. **Build Failures**
   - Check Node.js version (18+)
   - Clear node_modules and reinstall
   - Verify environment variables

2. **Deployment Errors**
   - Verify Firebase authentication
   - Check project permissions
   - Review security rules

3. **Function Errors**
   - Check function logs: `firebase functions:log`
   - Verify Firestore indexes
   - Review authentication tokens

## 🎯 **Production Checklist**

- [ ] Environment variables configured
- [ ] Security rules deployed
- [ ] Cloud Functions deployed
- [ ] Custom domains configured
- [ ] SSL certificates active
- [ ] Analytics enabled
- [ ] Monitoring configured
- [ ] Backup strategy implemented
- [ ] CI/CD pipeline tested
- [ ] Performance optimized

## 📞 **Support**

For deployment issues:
1. Check Firebase Console logs
2. Review GitHub Actions logs
3. Monitor application performance
4. Check security rule violations

## 🎉 **Success!**

Your complete Veritus legal platform is now deployed with:
- ✅ **Mobile App** at app.veritus.app
- ✅ **Secretary Portal** at secretary.veritus.app
- ✅ **Cloud Functions** for backend security
- ✅ **Complete Security Rules** for data protection
- ✅ **CI/CD Pipeline** for automated deployment

**The platform is now production-ready!** 🚀
