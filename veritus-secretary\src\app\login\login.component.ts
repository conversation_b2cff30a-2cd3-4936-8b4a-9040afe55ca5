import { Component } from '@angular/core';
import { Router } from '@angular/router';
import { FirebaseService } from '../services/firebase.service';

@Component({
  selector: 'app-login',
  templateUrl: './login.component.html',
  styleUrls: ['./login.component.scss']
})
export class LoginComponent {
  email = '';
  password = '';
  showPassword = false;
  isLoading = false;
  errorMessage = '';

  constructor(
    private router: Router,
    private firebaseService: FirebaseService
  ) { }

  togglePasswordVisibility() {
    this.showPassword = !this.showPassword;
  }

  goToRegister() {
    this.router.navigate(['/register']);
  }

  onGoogleSignIn() {
    // TODO: Implement Google Sign In for secretary
    console.log('Google Sign In clicked');
    alert('Google Sign In will be implemented soon.');
  }

  async login() {
    if (!this.email || !this.password) {
      this.errorMessage = 'Please enter both email and password';
      return;
    }

    this.isLoading = true;
    this.errorMessage = '';

    try {
      console.log('Attempting login for:', this.email);

      // Authenticate with Firebase
      const user = await this.firebaseService.signIn(this.email, this.password);
      console.log('Login successful:', user);

      // Check if user has secretary profile
      const secretaryProfile = await this.firebaseService.getSecretaryProfile(user.uid);

      if (!secretaryProfile) {
        throw new Error('No secretary profile found. Please contact your administrator.');
      }

      console.log('Secretary profile found:', secretaryProfile);

      // Navigate to dashboard
      this.router.navigate(['/secretary-tabs']).then(() => {
        console.log('Navigation to secretary dashboard successful');
      });

    } catch (error: any) {
      console.error('Login error:', error);

      // Handle specific Firebase auth errors
      if (error.code === 'auth/user-not-found') {
        this.errorMessage = 'No account found with this email address';
      } else if (error.code === 'auth/wrong-password') {
        this.errorMessage = 'Incorrect password';
      } else if (error.code === 'auth/invalid-email') {
        this.errorMessage = 'Invalid email address';
      } else if (error.code === 'auth/too-many-requests') {
        this.errorMessage = 'Too many failed attempts. Please try again later';
      } else if (error.message) {
        this.errorMessage = error.message;
      } else {
        this.errorMessage = 'Login failed. Please try again';
      }
    } finally {
      this.isLoading = false;
    }
  }

  async forgotPassword() {
    if (!this.email) {
      this.errorMessage = 'Please enter your email address first';
      return;
    }

    try {
      await this.firebaseService.resetPassword(this.email);
      this.errorMessage = '';
      alert('Password reset email sent! Check your inbox.');
    } catch (error: any) {
      console.error('Password reset error:', error);
      this.errorMessage = 'Failed to send password reset email';
    }
  }


}
