import { Component, OnInit } from '@angular/core';
import { NavController } from '@ionic/angular';
import { FirebaseService } from '../services/firebase.service';

interface AvailabilitySlot {
  date: string;
  startTime: string;
  endTime: string;
}

@Component({
  selector: 'app-availability',
  templateUrl: './availability.page.html',
  styleUrls: ['./availability.page.scss'],
  standalone: false
})
export class AvailabilityPage implements OnInit {
  currentDate = new Date();
  startTime: string = '2025-01-01T05:00:00';
  endTime: string = '2025-01-01T17:00:00';

  monthNames = [
    'January', 'February', 'March', 'April', 'May', 'June',
    'July', 'August', 'September', 'October', 'November', 'December'
  ];

  dayHeaders = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];

  calendarDays: { date: number, isOtherMonth: boolean, isAvailable: boolean }[] = [];

  weekDays = [
    { label: 'Mon', value: 1, selected: false },
    { label: 'Tue', value: 2, selected: false },
    { label: 'Wed', value: 3, selected: false },
    { label: 'Thu', value: 4, selected: false },
    { label: 'Fri', value: 5, selected: false },
    { label: 'Sat', value: 6, selected: false },
    { label: 'Sun', value: 0, selected: false },
  ];

  constructor(
    private navCtrl: NavController,
    private firebaseService: FirebaseService
  ) {}

  ngOnInit() {
    this.generateCalendar();
  }

  formatTime(raw: string): string {
    const date = new Date(raw);
    const hours = date.getHours();
    const minutes = date.getMinutes().toString().padStart(2, '0');
    const suffix = hours >= 12 ? 'PM' : 'AM';
    const hour12 = hours % 12 || 12;
    return `${hour12}:${minutes} ${suffix}`;
  }

  getMonthYearDisplay(): string {
    return `${this.monthNames[this.currentDate.getMonth()]}`;
  }

  generateCalendar() {
    const year = this.currentDate.getFullYear();
    const month = this.currentDate.getMonth();
    const firstDay = new Date(year, month, 1);
    const lastDay = new Date(year, month + 1, 0);
    const prevMonth = new Date(year, month, 0);

    let firstDayOfWeek = firstDay.getDay();
    firstDayOfWeek = firstDayOfWeek === 0 ? 6 : firstDayOfWeek - 1;

    this.calendarDays = [];

    for (let i = firstDayOfWeek - 1; i >= 0; i--) {
      const date = prevMonth.getDate() - i;
      this.calendarDays.push({ date, isOtherMonth: true, isAvailable: false });
    }

    for (let date = 1; date <= lastDay.getDate(); date++) {
      this.calendarDays.push({ date, isOtherMonth: false, isAvailable: false });
    }

    const remaining = 42 - this.calendarDays.length;
    for (let date = 1; date <= remaining; date++) {
      this.calendarDays.push({ date, isOtherMonth: true, isAvailable: false });
    }
  }

  toggleAvailability(day: any) {
    if (day.isOtherMonth) return;
    day.isAvailable = !day.isAvailable;
  }

  async saveAvailability() {
    const start = new Date(this.startTime).getTime();
    const end = new Date(this.endTime).getTime();

    if (start >= end) {
      alert('Start time must be earlier than end time.');
      return;
    }

    const selected = this.calendarDays
      .filter(day => day.isAvailable && !day.isOtherMonth)
      .map(day => ({
        date: `${this.monthNames[this.currentDate.getMonth()]} ${day.date.toString().padStart(2, '0')}`,
        startTime: this.formatTime(this.startTime),
        endTime: this.formatTime(this.endTime)
      }));

    const recurringDates = this.calendarDays
      .filter(day => {
        const fullDate = new Date(this.currentDate.getFullYear(), this.currentDate.getMonth(), day.date);
        return this.weekDays.some(w => w.selected && fullDate.getDay() === w.value);
      })
      .map(day => ({
        date: `${this.monthNames[this.currentDate.getMonth()]} ${day.date.toString().padStart(2, '0')}`,
        startTime: this.formatTime(this.startTime),
        endTime: this.formatTime(this.endTime)
      }));

    const existing = await this.firebaseService.getLawyerAvailability();
    const all = [...existing, ...selected, ...recurringDates];

    const unique = Array.from(new Map(all.map(slot => [slot.date, slot])).values());

    await this.firebaseService.saveLawyerAvailability(unique);

    document.dispatchEvent(new CustomEvent('refresh-calendar'));
    this.navCtrl.back();
  }

  previousMonth() {
    this.currentDate = new Date(this.currentDate.getFullYear(), this.currentDate.getMonth() - 1, 1);
    this.generateCalendar();
  }

  nextMonth() {
    this.currentDate = new Date(this.currentDate.getFullYear(), this.currentDate.getMonth() + 1, 1);
    this.generateCalendar();
  }
}
