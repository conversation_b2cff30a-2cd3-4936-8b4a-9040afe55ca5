<div class="multi-lawyer-calendar-container">
  
  <!-- Header with Lawyer Selection -->
  <div class="calendar-header">
    <div class="header-left">
      <h2>Calendar Management</h2>
      <p class="subtitle">Manage schedules for {{ totalLinkedLawyers }} linked lawyer{{ totalLinkedLawyers !== 1 ? 's' : '' }}</p>
    </div>
    
    <div class="header-actions">
      <ion-button fill="outline" size="small" (click)="refreshData()" [disabled]="isLoading">
        <ion-icon name="refresh-outline" slot="start"></ion-icon>
        Refresh
      </ion-button>
      
      <ion-button fill="solid" size="small" (click)="goToToday()">
        <ion-icon name="today-outline" slot="start"></ion-icon>
        Today
      </ion-button>
    </div>
  </div>

  <!-- No Lawyers State -->
  <div *ngIf="!hasLinkedLawyers" class="no-lawyers-state">
    <ion-icon name="people-outline" class="large-icon"></ion-icon>
    <h3>No Linked Lawyers</h3>
    <p>You need to be linked with at least one lawyer to manage calendars.</p>
  </div>

  <!-- Main Calendar Content -->
  <div *ngIf="hasLinkedLawyers" class="calendar-content">
    
    <!-- Lawyer Selection Panel -->
    <div class="lawyer-selection-panel">
      <div class="panel-header">
        <h3>Select Lawyer</h3>
        <ion-button 
          fill="clear" 
          size="small" 
          [color]="showAllLawyers ? 'primary' : 'medium'"
          (click)="toggleAllLawyers()">
          <ion-icon name="people" slot="start"></ion-icon>
          All Lawyers
        </ion-button>
      </div>
      
      <div class="lawyers-grid">
        <div 
          *ngFor="let lawyer of linkedLawyers"
          class="lawyer-card"
          [class.selected]="selectedLawyer?.uid === lawyer.uid"
          [class.loading]="isLoading"
          (click)="selectLawyer(lawyer)">
          
          <div class="lawyer-info">
            <div class="lawyer-avatar">
              <img *ngIf="lawyer.avatar" [src]="lawyer.avatar" [alt]="lawyer.name">
              <ion-icon *ngIf="!lawyer.avatar" name="person-circle-outline"></ion-icon>
            </div>
            
            <div class="lawyer-details">
              <h4>{{ lawyer.name }}</h4>
              <p class="lawyer-role">{{ lawyer.rollNumber }}</p>
            </div>
          </div>
          
          <div class="lawyer-status">
            <ion-chip 
              [color]="getLawyerStatusColor(lawyer)" 
              size="small" 
              class="status-chip">
              {{ getLawyerStatusText(lawyer) }}
            </ion-chip>
          </div>
          
          <div *ngIf="!isLoading" class="lawyer-stats">
            <div class="stat-item">
              <span class="stat-number">{{ getLawyerSummary(lawyer.uid)?.totalAppointments || 0 }}</span>
              <span class="stat-label">Appointments</span>
            </div>
            <div class="stat-item">
              <span class="stat-number">{{ getLawyerSummary(lawyer.uid)?.availableSlots || 0 }}</span>
              <span class="stat-label">Available</span>
            </div>
          </div>
          
          <div *ngIf="isLoading" class="loading-stats">
            <ion-skeleton-text animated style="width: 60%"></ion-skeleton-text>
            <ion-skeleton-text animated style="width: 40%"></ion-skeleton-text>
          </div>
        </div>
      </div>
    </div>

    <!-- Calendar Navigation -->
    <div class="calendar-navigation">
      <div class="nav-controls">
        <ion-button fill="clear" size="small" (click)="previousWeek()">
          <ion-icon name="chevron-back"></ion-icon>
        </ion-button>
        
        <div class="current-period">
          <h3>{{ formatWeekRange() }}</h3>
          <p *ngIf="selectedLawyer">{{ selectedLawyerName }}</p>
          <p *ngIf="showAllLawyers">All Lawyers View</p>
        </div>
        
        <ion-button fill="clear" size="small" (click)="nextWeek()">
          <ion-icon name="chevron-forward"></ion-icon>
        </ion-button>
      </div>
      
      <div class="view-controls">
        <ion-segment [(ngModel)]="calendarView.type" (ionChange)="onViewTypeChange($event)">
          <ion-segment-button value="day">
            <ion-label>Day</ion-label>
          </ion-segment-button>
          <ion-segment-button value="week">
            <ion-label>Week</ion-label>
          </ion-segment-button>
          <ion-segment-button value="month">
            <ion-label>Month</ion-label>
          </ion-segment-button>
        </ion-segment>
      </div>
    </div>

    <!-- Week View Calendar -->
    <div *ngIf="calendarView.type === 'week'" class="week-calendar">
      <div class="week-header">
        <div 
          *ngFor="let day of weekDays" 
          class="day-header"
          [class.today]="isToday(day)"
          [class.weekend]="isWeekend(day)"
          [class.selected]="isSelected(day)"
          (click)="selectDate(day)">
          
          <div class="day-name">{{ formatDateDisplay(day) }}</div>
          <div class="day-number">{{ day.getDate() }}</div>
          
          <div class="day-indicators">
            <div class="appointments-count" *ngIf="getTotalAppointmentsForDay(day) > 0">
              {{ getTotalAppointmentsForDay(day) }} apt{{ getTotalAppointmentsForDay(day) !== 1 ? 's' : '' }}
            </div>
            <div class="available-count" *ngIf="getAvailableSlotsForDay(day) > 0">
              {{ getAvailableSlotsForDay(day) }} available
            </div>
          </div>
        </div>
      </div>
      
      <div class="week-body">
        <!-- Time slots and appointments would go here -->
        <div class="time-slots">
          <div class="time-slot" *ngFor="let hour of [9,10,11,12,13,14,15,16,17]">
            <div class="time-label">{{ hour }}:00</div>
            <div class="slot-content">
              <!-- Appointment blocks would be rendered here -->
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Day View -->
    <div *ngIf="calendarView.type === 'day'" class="day-calendar">
      <div class="day-header">
        <h3>{{ formatDateDisplay(calendarView.selectedDate!) }}</h3>
        <div class="day-stats">
          <span>{{ getTotalAppointmentsForDay(calendarView.selectedDate!) }} appointments</span>
          <span>{{ getAvailableSlotsForDay(calendarView.selectedDate!) }} available slots</span>
        </div>
      </div>
      
      <div class="day-content">
        <!-- Detailed day view content -->
        <p class="placeholder-text">Day view content will be implemented with detailed appointment and availability management.</p>
      </div>
    </div>

    <!-- Month View -->
    <div *ngIf="calendarView.type === 'month'" class="month-calendar">
      <div class="month-header">
        <h3>{{ calendarView.currentDate.toLocaleDateString('en-US', { month: 'long', year: 'numeric' }) }}</h3>
      </div>
      
      <div class="month-content">
        <!-- Month grid would go here -->
        <p class="placeholder-text">Month view will show a full calendar grid with appointment indicators.</p>
      </div>
    </div>

    <!-- Quick Stats Summary -->
    <div class="quick-stats" *ngIf="!isLoading">
      <div class="stats-grid">
        <div class="stat-card">
          <div class="stat-number">{{ totalAppointmentsToday }}</div>
          <div class="stat-label">Today's Appointments</div>
        </div>
        <div class="stat-card">
          <div class="stat-number">{{ totalAvailableSlotsToday }}</div>
          <div class="stat-label">Available Slots Today</div>
        </div>
        <div class="stat-card">
          <div class="stat-number">{{ linkedLawyers.length }}</div>
          <div class="stat-label">Linked Lawyers</div>
        </div>
      </div>
    </div>

  </div>
</div>
