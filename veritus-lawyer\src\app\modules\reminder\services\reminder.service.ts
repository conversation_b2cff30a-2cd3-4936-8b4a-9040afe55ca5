import { Injectable } from '@angular/core';
import { Firestore, doc, setDoc, getDoc, updateDoc, deleteDoc, collection, query, where, getDocs, orderBy } from '@angular/fire/firestore';
import { NotificationService } from './notification.service';
import { SchedulerService } from './scheduler.service';

export interface Reminder {
  id: string;
  userId: string;
  title: string;
  description?: string;
  type: 'appointment' | 'deadline' | 'payment' | 'court_date' | 'follow_up' | 'custom';
  scheduledFor: Date;
  reminderTime: Date; // When to send the reminder
  isRecurring: boolean;
  recurrencePattern?: {
    frequency: 'daily' | 'weekly' | 'monthly' | 'yearly';
    interval: number; // Every X days/weeks/months/years
    endDate?: Date;
  };
  status: 'pending' | 'sent' | 'dismissed' | 'snoozed';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  relatedEntityId?: string; // Case ID, Appointment ID, etc.
  relatedEntityType?: 'case' | 'appointment' | 'client' | 'document';
  notificationChannels: ('push' | 'email' | 'sms')[];
  createdAt: Date;
  updatedAt: Date;
  sentAt?: Date;
  snoozeUntil?: Date;
}

export interface ReminderTemplate {
  id: string;
  name: string;
  title: string;
  description: string;
  type: Reminder['type'];
  defaultReminderOffset: number; // Minutes before event
  isActive: boolean;
  createdBy: string;
}

@Injectable({
  providedIn: 'root'
})
export class ReminderService {

  constructor(
    private firestore: Firestore,
    private notificationService: NotificationService,
    private schedulerService: SchedulerService
  ) { }

  // Create reminder
  async createReminder(reminder: Omit<Reminder, 'id' | 'createdAt' | 'updatedAt'>): Promise<Reminder> {
    const id = `reminder_${Date.now()}_${Math.random().toString(36).substring(2)}`;
    
    const newReminder: Reminder = {
      ...reminder,
      id,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    // Save to Firestore
    const docRef = doc(this.firestore, 'reminders', id);
    await setDoc(docRef, newReminder);

    // Schedule notification
    await this.schedulerService.scheduleReminder(newReminder);

    return newReminder;
  }

  // Get reminder by ID
  async getReminder(id: string): Promise<Reminder | null> {
    const docRef = doc(this.firestore, 'reminders', id);
    const docSnap = await getDoc(docRef);
    
    return docSnap.exists() ? docSnap.data() as Reminder : null;
  }

  // Get reminders by user
  async getRemindersByUser(userId: string, status?: Reminder['status']): Promise<Reminder[]> {
    let q = query(
      collection(this.firestore, 'reminders'),
      where('userId', '==', userId),
      orderBy('reminderTime', 'asc')
    );

    if (status) {
      q = query(q, where('status', '==', status));
    }

    const querySnapshot = await getDocs(q);
    return querySnapshot.docs.map(doc => doc.data() as Reminder);
  }

  // Get upcoming reminders
  async getUpcomingReminders(userId: string, hours: number = 24): Promise<Reminder[]> {
    const now = new Date();
    const futureTime = new Date(now.getTime() + (hours * 60 * 60 * 1000));

    const q = query(
      collection(this.firestore, 'reminders'),
      where('userId', '==', userId),
      where('status', '==', 'pending'),
      where('reminderTime', '>=', now),
      where('reminderTime', '<=', futureTime),
      orderBy('reminderTime', 'asc')
    );

    const querySnapshot = await getDocs(q);
    return querySnapshot.docs.map(doc => doc.data() as Reminder);
  }

  // Update reminder
  async updateReminder(id: string, updates: Partial<Reminder>): Promise<void> {
    const docRef = doc(this.firestore, 'reminders', id);
    await updateDoc(docRef, { ...updates, updatedAt: new Date() });

    // If reminder time changed, reschedule
    if (updates.reminderTime) {
      const reminder = await this.getReminder(id);
      if (reminder) {
        await this.schedulerService.rescheduleReminder(reminder);
      }
    }
  }

  // Delete reminder
  async deleteReminder(id: string): Promise<void> {
    // Cancel scheduled notification
    await this.schedulerService.cancelReminder(id);
    
    // Delete from Firestore
    const docRef = doc(this.firestore, 'reminders', id);
    await deleteDoc(docRef);
  }

  // Snooze reminder
  async snoozeReminder(id: string, snoozeMinutes: number): Promise<void> {
    const snoozeUntil = new Date(Date.now() + (snoozeMinutes * 60 * 1000));
    
    await this.updateReminder(id, {
      status: 'snoozed',
      snoozeUntil
    });

    // Reschedule for snooze time
    const reminder = await this.getReminder(id);
    if (reminder) {
      reminder.reminderTime = snoozeUntil;
      reminder.status = 'pending';
      await this.schedulerService.rescheduleReminder(reminder);
    }
  }

  // Dismiss reminder
  async dismissReminder(id: string): Promise<void> {
    await this.updateReminder(id, { status: 'dismissed' });
    await this.schedulerService.cancelReminder(id);
  }

  // Mark reminder as sent
  async markReminderSent(id: string): Promise<void> {
    await this.updateReminder(id, { 
      status: 'sent', 
      sentAt: new Date() 
    });
  }

  // Create reminder from appointment
  async createAppointmentReminder(
    appointmentId: string,
    appointmentDate: Date,
    userId: string,
    title: string,
    reminderMinutes: number = 60
  ): Promise<Reminder> {
    const reminderTime = new Date(appointmentDate.getTime() - (reminderMinutes * 60 * 1000));

    return this.createReminder({
      userId,
      title,
      description: `Appointment reminder: ${title}`,
      type: 'appointment',
      scheduledFor: appointmentDate,
      reminderTime,
      isRecurring: false,
      status: 'pending',
      priority: 'medium',
      relatedEntityId: appointmentId,
      relatedEntityType: 'appointment',
      notificationChannels: ['push']
    });
  }

  // Create deadline reminder
  async createDeadlineReminder(
    caseId: string,
    deadline: Date,
    userId: string,
    title: string,
    description?: string,
    reminderDays: number = 1
  ): Promise<Reminder> {
    const reminderTime = new Date(deadline.getTime() - (reminderDays * 24 * 60 * 60 * 1000));

    return this.createReminder({
      userId,
      title,
      description: description || `Deadline reminder: ${title}`,
      type: 'deadline',
      scheduledFor: deadline,
      reminderTime,
      isRecurring: false,
      status: 'pending',
      priority: 'high',
      relatedEntityId: caseId,
      relatedEntityType: 'case',
      notificationChannels: ['push', 'email']
    });
  }

  // Create payment reminder
  async createPaymentReminder(
    clientId: string,
    dueDate: Date,
    userId: string,
    amount: number,
    description?: string
  ): Promise<Reminder> {
    const reminderTime = new Date(dueDate.getTime() - (24 * 60 * 60 * 1000)); // 1 day before

    return this.createReminder({
      userId,
      title: `Payment Due: $${amount}`,
      description: description || `Payment reminder for $${amount}`,
      type: 'payment',
      scheduledFor: dueDate,
      reminderTime,
      isRecurring: false,
      status: 'pending',
      priority: 'medium',
      relatedEntityId: clientId,
      relatedEntityType: 'client',
      notificationChannels: ['push', 'email']
    });
  }

  // Process due reminders (called by scheduler)
  async processDueReminders(): Promise<void> {
    const now = new Date();
    
    const q = query(
      collection(this.firestore, 'reminders'),
      where('status', '==', 'pending'),
      where('reminderTime', '<=', now)
    );

    const querySnapshot = await getDocs(q);
    const dueReminders = querySnapshot.docs.map(doc => doc.data() as Reminder);

    for (const reminder of dueReminders) {
      try {
        // Send notification
        await this.notificationService.sendReminderNotification(reminder);
        
        // Mark as sent
        await this.markReminderSent(reminder.id);
        
        // Handle recurring reminders
        if (reminder.isRecurring && reminder.recurrencePattern) {
          await this.createRecurringReminder(reminder);
        }
      } catch (error) {
        console.error(`Failed to process reminder ${reminder.id}:`, error);
      }
    }
  }

  // Create next occurrence of recurring reminder
  private async createRecurringReminder(originalReminder: Reminder): Promise<void> {
    if (!originalReminder.recurrencePattern) return;

    const pattern = originalReminder.recurrencePattern;
    let nextDate = new Date(originalReminder.scheduledFor);

    // Calculate next occurrence
    switch (pattern.frequency) {
      case 'daily':
        nextDate.setDate(nextDate.getDate() + pattern.interval);
        break;
      case 'weekly':
        nextDate.setDate(nextDate.getDate() + (pattern.interval * 7));
        break;
      case 'monthly':
        nextDate.setMonth(nextDate.getMonth() + pattern.interval);
        break;
      case 'yearly':
        nextDate.setFullYear(nextDate.getFullYear() + pattern.interval);
        break;
    }

    // Check if we've reached the end date
    if (pattern.endDate && nextDate > pattern.endDate) {
      return;
    }

    // Calculate next reminder time
    const timeDiff = originalReminder.reminderTime.getTime() - originalReminder.scheduledFor.getTime();
    const nextReminderTime = new Date(nextDate.getTime() + timeDiff);

    // Create next reminder
    await this.createReminder({
      userId: originalReminder.userId,
      title: originalReminder.title,
      description: originalReminder.description,
      type: originalReminder.type,
      scheduledFor: nextDate,
      reminderTime: nextReminderTime,
      isRecurring: true,
      recurrencePattern: originalReminder.recurrencePattern,
      status: 'pending',
      priority: originalReminder.priority,
      relatedEntityId: originalReminder.relatedEntityId,
      relatedEntityType: originalReminder.relatedEntityType,
      notificationChannels: originalReminder.notificationChannels
    });
  }
}
