<header class="admin-header">
  <div class="header-left">
    <button class="mobile-menu-btn" (click)="toggleMobileMenu()">
      <span class="hamburger"></span>
    </button>
    <h1 class="page-title">{{ getPageTitle() }}</h1>
  </div>
  
  <div class="header-right">
    <div class="search-container" *ngIf="showSearch">
      <input 
        type="text" 
        class="search-input" 
        placeholder="Search..."
        [(ngModel)]="searchQuery"
        (input)="onSearch($event)">
      <svg class="search-icon" width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M21 21L16.514 16.506L21 21ZM19 10.5C19 15.194 15.194 19 10.5 19C5.806 19 2 15.194 2 10.5C2 5.806 5.806 2 10.5 2C15.194 2 19 5.806 19 10.5Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
      </svg>
    </div>
    
    <div class="notifications">
      <button class="notification-btn" (click)="toggleNotifications()">
        <svg class="notification-icon" width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 19V20H3V19L5 17V11C5 7.9 7.03 5.17 10 4.29C10 4.19 10 4.1 10 4C10 2.34 11.34 1 13 1C14.66 1 16 2.34 16 4C16 4.1 16 4.19 16 4.29C18.97 5.17 21 7.9 21 11V17L23 19ZM7 18H17V11C17 8.24 14.76 6 12 6S7 8.24 7 11V18Z" fill="currentColor"/>
          <circle cx="19" cy="3" r="3" fill="#ff4444"/>
        </svg>
        <span class="notification-badge" *ngIf="notificationCount > 0">{{ notificationCount }}</span>
      </button>
      
      <div class="notification-dropdown" *ngIf="showNotifications">
        <div class="notification-header">
          <h3>Notifications</h3>
          <button class="mark-all-read" (click)="markAllAsRead()">Mark all as read</button>
        </div>
        <div class="notification-list">
          <div class="notification-item" *ngFor="let notification of notifications" [class.unread]="!notification.read">
            <div class="notification-content">
              <p class="notification-text">{{ notification.message }}</p>
              <span class="notification-time">{{ notification.timestamp | date:'short' }}</span>
            </div>
          </div>
          <div class="no-notifications" *ngIf="notifications.length === 0">
            <p>No new notifications</p>
          </div>
        </div>
      </div>
    </div>
    
    <div class="admin-profile">
      <div class="profile-info" (click)="toggleProfileMenu()">
        <div class="avatar">
          <div class="avatar-placeholder" *ngIf="!currentAdmin?.avatar">
            {{ getInitials(currentAdmin?.displayName || 'Admin') }}
          </div>
          <img *ngIf="currentAdmin?.avatar"
               [src]="currentAdmin?.avatar"
               [alt]="currentAdmin?.displayName"
               (error)="onImageError($event)">
        </div>
        <div class="admin-details">
          <span class="admin-name">Hi, {{ currentAdmin?.displayName || 'Admin' }}</span>
          <span class="admin-role">{{ currentAdmin?.role | titlecase }}</span>
        </div>
        <i class="dropdown-arrow">▼</i>
      </div>
      
      <div class="profile-dropdown" *ngIf="showProfileMenu">
        <ul class="profile-menu">
          <li><a routerLink="/admin/profile">Profile Settings</a></li>
          <li><a routerLink="/admin/settings">Account Settings</a></li>
          <li class="divider"></li>
          <li><button (click)="logout()">Sign Out</button></li>
        </ul>
      </div>
    </div>
  </div>
</header>
