import { Component, OnInit, Input } from '@angular/core';
import { ModalController, ToastController } from '@ionic/angular';
import { FirebaseService, LawyerProfile } from '../../services/firebase.service';
import { EnhancedAppointment, LawyerAvailability, RescheduleRequest } from '../../models/scheduling.models';

@Component({
  selector: 'app-reschedule-modal',
  templateUrl: './reschedule-modal.component.html',
  styleUrls: ['./reschedule-modal.component.scss']
})
export class RescheduleModalComponent implements OnInit {
  @Input() appointment!: EnhancedAppointment;
  @Input() linkedLawyers: LawyerProfile[] = [];

  // Form data
  proposedDate: string = '';
  proposedTime: string = '';
  reason: string = '';
  
  // Available time slots for the selected date
  availableTimeSlots: string[] = [];
  isLoadingSlots = false;
  isSubmitting = false;

  // Validation
  isFormValid = false;

  // Date constraints
  minDate = new Date().toISOString();

  constructor(
    private modalController: ModalController,
    private firebaseService: FirebaseService,
    private toastController: ToastController
  ) { }

  ngOnInit() {
    // Set minimum date to today
    this.proposedDate = new Date().toISOString().split('T')[0];
    this.validateForm();
  }

  async onDateChange() {
    if (this.proposedDate) {
      await this.loadAvailableTimeSlots();
    }
    this.proposedTime = ''; // Reset time when date changes
    this.validateForm();
  }

  onTimeChange() {
    this.validateForm();
  }

  onReasonChange() {
    this.validateForm();
  }

  async loadAvailableTimeSlots() {
    if (!this.proposedDate || !this.appointment.lawyerId) return;

    this.isLoadingSlots = true;
    this.availableTimeSlots = [];

    try {
      // Get lawyer's availability for the selected date
      const availabilities = await this.firebaseService.getLawyerAvailability(
        this.appointment.lawyerId,
        this.proposedDate
      );

      if (availabilities.length > 0) {
        const availability = availabilities[0];
        
        // Get existing appointments for the date to filter out booked slots
        const existingAppointments = await this.firebaseService.getAppointmentsByLawyer(
          this.appointment.lawyerId,
          this.proposedDate,
          this.proposedDate
        );

        const bookedTimes = existingAppointments
          .filter(apt => apt.id !== this.appointment.id) // Exclude current appointment
          .map(apt => apt.time);

        // Filter available slots
        this.availableTimeSlots = availability.timeSlots.filter(
          time => !bookedTimes.includes(time)
        );
      }
    } catch (error) {
      console.error('Error loading available time slots:', error);
      this.showToast('Error loading available time slots', 'danger');
    } finally {
      this.isLoadingSlots = false;
    }
  }

  validateForm() {
    this.isFormValid = !!(
      this.proposedDate &&
      this.proposedTime &&
      this.reason.trim().length >= 10 && // Minimum reason length
      this.proposedDate !== this.appointment.date || this.proposedTime !== this.appointment.time // Must be different from original
    );
  }

  async submitRescheduleRequest() {
    if (!this.isFormValid || this.isSubmitting) return;

    const currentUser = this.firebaseService.getCurrentUser();
    if (!currentUser) {
      this.showToast('User not authenticated', 'danger');
      return;
    }

    // Get secretary profile for name
    const secretaryProfile = await this.firebaseService.getSecretaryProfile(currentUser.uid);
    if (!secretaryProfile) {
      this.showToast('Secretary profile not found', 'danger');
      return;
    }

    this.isSubmitting = true;

    try {
      const rescheduleRequest: Omit<RescheduleRequest, 'id'> = {
        appointmentId: this.appointment.id!,
        originalDate: this.appointment.date,
        originalTime: this.appointment.time,
        proposedDate: this.proposedDate,
        proposedTime: this.proposedTime,
        reason: this.reason.trim(),
        requestedBy: currentUser.uid,
        requestedByName: secretaryProfile.name,
        status: 'pending',
        clientApproval: 'pending',
        lawyerApproval: 'pending',
        createdAt: new Date(),
        expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) // 7 days from now
      };

      await this.firebaseService.createRescheduleRequest(rescheduleRequest);

      // Close modal with success result
      await this.modalController.dismiss({
        rescheduled: true,
        request: rescheduleRequest
      });

    } catch (error) {
      console.error('Error creating reschedule request:', error);
      this.showToast('Error creating reschedule request', 'danger');
    } finally {
      this.isSubmitting = false;
    }
  }

  async cancel() {
    await this.modalController.dismiss({
      rescheduled: false
    });
  }

  formatTime(time: string): string {
    const [hours, minutes] = time.split(':');
    const hour = parseInt(hours);
    const ampm = hour >= 12 ? 'PM' : 'AM';
    const displayHour = hour > 12 ? hour - 12 : hour === 0 ? 12 : hour;
    return `${displayHour}:${minutes} ${ampm}`;
  }

  formatDate(date: string): string {
    return new Date(date).toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  }

  private async showToast(message: string, color: string) {
    const toast = await this.toastController.create({
      message,
      duration: 3000,
      color,
      position: 'top'
    });
    await toast.present();
  }

  get isDateInPast(): boolean {
    if (!this.proposedDate) return false;
    const selectedDate = new Date(this.proposedDate);
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    return selectedDate < today;
  }

  get isSameDateTime(): boolean {
    return this.proposedDate === this.appointment.date && 
           this.proposedTime === this.appointment.time;
  }

  get reasonTooShort(): boolean {
    return this.reason.trim().length > 0 && this.reason.trim().length < 10;
  }

  get hasAvailableSlots(): boolean {
    return this.availableTimeSlots.length > 0;
  }

  get noSlotsMessage(): string {
    if (this.isLoadingSlots) return 'Loading available time slots...';
    if (!this.proposedDate) return 'Please select a date first';
    if (!this.hasAvailableSlots) return 'No available time slots for this date';
    return '';
  }

  // Quick date selection helpers
  selectTomorrow() {
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    this.proposedDate = tomorrow.toISOString().split('T')[0];
    this.onDateChange();
  }

  selectNextWeek() {
    const nextWeek = new Date();
    nextWeek.setDate(nextWeek.getDate() + 7);
    this.proposedDate = nextWeek.toISOString().split('T')[0];
    this.onDateChange();
  }

  selectNextMonth() {
    const nextMonth = new Date();
    nextMonth.setMonth(nextMonth.getMonth() + 1);
    this.proposedDate = nextMonth.toISOString().split('T')[0];
    this.onDateChange();
  }
}
