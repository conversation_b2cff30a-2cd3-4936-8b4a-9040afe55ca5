<div class="verification-container">
  <div class="verification-header">
    <h1>Lawyer Credential Verification</h1>
    <div class="header-actions">
      <div class="search-container">
        <input 
          type="text" 
          class="search-input" 
          placeholder="Search lawyer..."
          [(ngModel)]="searchQuery"
          (input)="filterLawyers()">
        <svg class="search-icon" width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M21 21L16.514 16.506L21 21ZM19 10.5C19 15.194 15.194 19 10.5 19C5.806 19 2 15.194 2 10.5C2 5.806 5.806 2 10.5 2C15.194 2 19 5.806 19 10.5Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
      </div>
      <select class="status-filter" [(ngModel)]="statusFilter" (change)="filterLawyers()">
        <option value="">All Status</option>
        <option value="pending">Pending</option>
        <option value="approved">Approved</option>
        <option value="rejected">Rejected</option>
        <option value="under_review">Under Review</option>
      </select>
    </div>
  </div>

  <div class="verification-stats">
    <div class="stat-item">
      <span class="stat-number">{{ getFilteredCount('pending') }}</span>
      <span class="stat-label">Pending</span>
    </div>
    <div class="stat-item">
      <span class="stat-number">{{ getFilteredCount('under_review') }}</span>
      <span class="stat-label">Under Review</span>
    </div>
    <div class="stat-item">
      <span class="stat-number">{{ getFilteredCount('approved') }}</span>
      <span class="stat-label">Approved</span>
    </div>
    <div class="stat-item">
      <span class="stat-number">{{ getFilteredCount('rejected') }}</span>
      <span class="stat-label">Rejected</span>
    </div>
  </div>

  <div class="verification-list">
    <div class="lawyer-card" *ngFor="let lawyer of filteredLawyers">
      <div class="lawyer-info">
        <div class="lawyer-avatar">
          <div class="avatar-placeholder" *ngIf="!lawyer.avatar">
            {{ getInitials(lawyer.name) }}
          </div>
          <img *ngIf="lawyer.avatar"
               [src]="lawyer.avatar"
               [alt]="lawyer.name"
               (error)="onImageError($event, lawyer)">
        </div>
        <div class="lawyer-details">
          <h3>{{ lawyer.name }}</h3>
          <p><strong>Roll No:</strong> {{ lawyer.rollNumber }}</p>
          <p><strong>Firm:</strong> {{ lawyer.firm }}</p>
          <p><strong>Email:</strong> {{ lawyer.email }}</p>
          <p><strong>Phone:</strong> {{ lawyer.phone }}</p>
          <p><strong>Submitted:</strong> {{ lawyer.submittedAt | date:'medium' }}</p>
        </div>
      </div>

      <div class="verification-status">
        <span class="status-badge" [class]="lawyer.status">
          {{ lawyer.status | titlecase }}
        </span>
        <div class="verification-progress" *ngIf="lawyer.status === 'under_review'">
          <div class="progress-bar">
            <div class="progress-fill" [style.width.%]="lawyer.verificationProgress"></div>
          </div>
          <span class="progress-text">{{ lawyer.verificationProgress }}% Complete</span>
        </div>
      </div>

      <div class="lawyer-actions">
        <button class="action-btn view-btn" (click)="viewLawyerDetails(lawyer)">
          <i class="btn-icon">👁</i>
          View Details
        </button>
        <button class="action-btn approve-btn" 
                (click)="approveLawyer(lawyer)"
                [disabled]="lawyer.status === 'approved'">
          <i class="btn-icon">✓</i>
          Approve
        </button>
        <button class="action-btn reject-btn" 
                (click)="rejectLawyer(lawyer)"
                [disabled]="lawyer.status === 'rejected'">
          <i class="btn-icon">✗</i>
          Reject
        </button>
      </div>
    </div>

    <div class="no-results" *ngIf="filteredLawyers.length === 0">
      <div class="no-results-icon">📋</div>
      <h3>No lawyers found</h3>
      <p>Try adjusting your search criteria or filters.</p>
    </div>
  </div>

  <!-- Pagination -->
  <div class="pagination" *ngIf="totalPages > 1">
    <button class="page-btn" 
            (click)="goToPage(currentPage - 1)"
            [disabled]="currentPage === 1">
      Previous
    </button>
    
    <span class="page-info">
      Page {{ currentPage }} of {{ totalPages }}
    </span>
    
    <button class="page-btn" 
            (click)="goToPage(currentPage + 1)"
            [disabled]="currentPage === totalPages">
      Next
    </button>
  </div>
</div>

<!-- Lawyer Details Modal -->
<div class="modal-overlay" *ngIf="selectedLawyer" (click)="closeModal()">
  <div class="modal-content" (click)="$event.stopPropagation()">
    <div class="modal-header">
      <h2>Lawyer Verification Details</h2>
      <button class="close-btn" (click)="closeModal()">×</button>
    </div>
    
    <div class="modal-body">
      <div class="lawyer-profile">
        <div class="profile-image">
          <div class="avatar-placeholder" *ngIf="!selectedLawyer.avatar">
            {{ getInitials(selectedLawyer.name) }}
          </div>
          <img *ngIf="selectedLawyer.avatar"
               [src]="selectedLawyer.avatar"
               [alt]="selectedLawyer.name"
               (error)="onImageError($event, selectedLawyer)">
        </div>
        <div class="profile-info">
          <h3>{{ selectedLawyer.name }}</h3>
          <p><strong>Roll Number:</strong> {{ selectedLawyer.rollNumber }}</p>
          <p><strong>Firm:</strong> {{ selectedLawyer.firm }}</p>
          <p><strong>Email:</strong> {{ selectedLawyer.email }}</p>
          <p><strong>Phone:</strong> {{ selectedLawyer.phone }}</p>
          <p><strong>Years of Practice:</strong> {{ selectedLawyer.yearsOfPractice }}</p>
          <p><strong>Specialization:</strong> {{ selectedLawyer.specialization }}</p>
        </div>
      </div>

      <div class="documents-section">
        <h4>Submitted Documents</h4>
        <div class="document-list">
          <div class="document-item" *ngFor="let doc of selectedLawyer.documents">
            <div class="doc-info">
              <i class="doc-icon">📄</i>
              <span class="doc-name">{{ doc.name }}</span>
              <span class="doc-type">({{ doc.type }})</span>
            </div>
            <div class="doc-actions">
              <button class="doc-btn view" (click)="viewDocument(doc)">View</button>
              <button class="doc-btn download" (click)="downloadDocument(doc)">Download</button>
            </div>
          </div>
        </div>
      </div>

      <div class="verification-section">
        <h4>Verification Actions</h4>
        <div class="verification-form">
          <div class="form-group">
            <label for="verificationNotes">Verification Notes</label>
            <textarea 
              id="verificationNotes"
              [(ngModel)]="verificationNotes"
              placeholder="Add notes about the verification process..."
              rows="4"></textarea>
          </div>
          
          <div class="form-group">
            <label for="ibpVerification">IBP Verification Status</label>
            <select id="ibpVerification" [(ngModel)]="ibpVerificationStatus">
              <option value="">Select Status</option>
              <option value="verified">Verified by IBP</option>
              <option value="pending">Pending IBP Check</option>
              <option value="not_found">Not Found in IBP Database</option>
              <option value="invalid">Invalid Roll Number</option>
            </select>

            <!-- IBP Verification Tools -->
            <div class="ibp-tools">
              <button
                type="button"
                class="ibp-btn"
                (click)="openIBPLawyersList()">
                📋 Open IBP Lawyers List
              </button>
            </div>
          </div>

          <div class="action-buttons">
            <button class="modal-btn approve" (click)="approveFromModal()">
              <i class="btn-icon">✓</i>
              Approve Lawyer
            </button>
            <button class="modal-btn reject" (click)="rejectFromModal()">
              <i class="btn-icon">✗</i>
              Reject Application
            </button>
            <button class="modal-btn request-docs" (click)="requestAdditionalDocs()">
              <i class="btn-icon">📄</i>
              Request Additional Documents
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
