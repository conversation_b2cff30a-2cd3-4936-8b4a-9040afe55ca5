"use strict";(self.webpackChunkveritus_secretary=self.webpackChunkveritus_secretary||[]).push([[8970],{8970:(H,C,p)=>{p.r(C),p.d(C,{ion_route:()=>k,ion_route_redirect:()=>N,ion_router:()=>oe,ion_router_link:()=>G});var f=p(467),d=p(4363),y=p(5638),E=p(333),D=p(611);const k=class{constructor(e){(0,d.r)(this,e),this.ionRouteDataChanged=(0,d.d)(this,"ionRouteDataChanged",7),this.url="",this.component=void 0,this.componentProps=void 0,this.beforeLeave=void 0,this.beforeEnter=void 0}onUpdate(e){this.ionRouteDataChanged.emit(e)}onComponentProps(e,t){if(e===t)return;const n=e?Object.keys(e):[],r=t?Object.keys(t):[];if(n.length===r.length){for(const o of n)if(e[o]!==t[o])return void this.onUpdate(e)}else this.onUpdate(e)}connectedCallback(){this.ionRouteDataChanged.emit()}static get watchers(){return{url:["onUpdate"],component:["onUpdate"],componentProps:["onComponentProps"]}}},N=class{constructor(e){(0,d.r)(this,e),this.ionRouteRedirectChanged=(0,d.d)(this,"ionRouteRedirectChanged",7),this.from=void 0,this.to=void 0}propDidChange(){this.ionRouteRedirectChanged.emit()}connectedCallback(){this.ionRouteRedirectChanged.emit()}static get watchers(){return{from:["propDidChange"],to:["propDidChange"]}}},u="root",h="forward",_=e=>"/"+e.filter(n=>n.length>0).join("/"),g=e=>{let n,t=[""];if(null!=e){const r=e.indexOf("?");r>-1&&(n=e.substring(r+1),e=e.substring(0,r)),t=e.split("/").map(o=>o.trim()).filter(o=>o.length>0),0===t.length&&(t=[""])}return{segments:t,queryString:n}},j=function(){var e=(0,f.A)(function*(t,n,r,o,s=!1,i){try{const a=O(t);if(o>=n.length||!a)return s;yield new Promise(v=>(0,y.c)(a,v));const l=n[o],c=yield a.setRouteId(l.id,l.params,r,i);return c.changed&&(r=u,s=!0),s=yield j(c.element,n,r,o+1,s,i),c.markVisible&&(yield c.markVisible()),s}catch(a){return console.error(a),!1}});return function(n,r,o,s){return e.apply(this,arguments)}}(),Q=function(){var e=(0,f.A)(function*(t){const n=[];let r,o=t;for(;r=O(o);){const s=yield r.getRouteId();if(!s)break;o=s.element,s.element=void 0,n.push(s)}return{ids:n,outlet:r}});return function(n){return e.apply(this,arguments)}}(),I=":not([no-router]) ion-nav, :not([no-router]) ion-tabs, :not([no-router]) ion-router-outlet",O=e=>{if(e)return e.matches(I)?e:e.querySelector(I)??void 0},M=(e,t)=>t.find(n=>((e,t)=>{const{from:n,to:r}=t;if(void 0===r||n.length>e.length)return!1;for(let o=0;o<n.length;o++){const s=n[o];if("*"===s)return!0;if(s!==e[o])return!1}return n.length===e.length})(e,n)),Z=(e,t)=>{const n=Math.min(e.length,t.length);let r=0;for(let o=0;o<n;o++){const s=e[o],i=t[o];if(s.id.toLowerCase()!==i.id)break;if(s.params){const a=Object.keys(s.params);if(a.length===i.segments.length){const l=a.map(c=>`:${c}`);for(let c=0;c<l.length&&l[c].toLowerCase()===i.segments[c];c++)r++}}r++}return r},V=(e,t)=>{const n=new ne(e);let o,r=!1;for(let i=0;i<t.length;i++){const a=t[i].segments;if(""===a[0])r=!0;else{for(const l of a){const c=n.next();if(":"===l[0]){if(""===c)return null;o=o||[],(o[i]||(o[i]={}))[l.slice(1)]=c}else if(c!==l)return null}r=!1}}return r&&r!==(""===n.next())?null:o?t.map((i,a)=>({id:i.id,segments:i.segments,params:W(i.params,o[a]),beforeEnter:i.beforeEnter,beforeLeave:i.beforeLeave})):t},W=(e,t)=>e||t?Object.assign(Object.assign({},e),t):void 0,A=(e,t)=>{let n=null,r=0;for(const o of t){const s=V(e,o);if(null!==s){const i=te(s);i>r&&(r=i,n=s)}}return n},te=e=>{let t=1,n=1;for(const r of e)for(const o of r.segments)":"===o[0]?t+=Math.pow(1,n):""!==o&&(t+=Math.pow(2,n)),n++;return t};class ne{constructor(t){this.segments=t.slice()}next(){return this.segments.length>0?this.segments.shift():""}}const w=(e,t)=>t in e?e[t]:e.hasAttribute(t)?e.getAttribute(t):null,T=e=>Array.from(e.children).filter(t=>"ION-ROUTE-REDIRECT"===t.tagName).map(t=>{const n=w(t,"to");return{from:g(w(t,"from")).segments,to:null==n?void 0:g(n)}}),P=e=>re($(e)),$=e=>Array.from(e.children).filter(t=>"ION-ROUTE"===t.tagName&&t.component).map(t=>{const n=w(t,"component");return{segments:g(w(t,"url")).segments,id:n.toLowerCase(),params:t.componentProps,beforeLeave:t.beforeLeave,beforeEnter:t.beforeEnter,children:$(t)}}),re=e=>{const t=[];for(const n of e)B([],t,n);return t},B=(e,t,n)=>{if(e=[...e,{id:n.id,segments:n.segments,params:n.params,beforeLeave:n.beforeLeave,beforeEnter:n.beforeEnter}],0!==n.children.length)for(const r of n.children)B(e,t,r);else t.push(e)},oe=class{constructor(e){(0,d.r)(this,e),this.ionRouteWillChange=(0,d.d)(this,"ionRouteWillChange",7),this.ionRouteDidChange=(0,d.d)(this,"ionRouteDidChange",7),this.previousPath=null,this.busy=!1,this.state=0,this.lastState=0,this.root="/",this.useHash=!0}componentWillLoad(){var e=this;return(0,f.A)(function*(){yield O(document.body)?Promise.resolve():new Promise(e=>{window.addEventListener("ionNavWillLoad",()=>e(),{once:!0})});const t=yield e.runGuards(e.getSegments());if(!0!==t){if("object"==typeof t){const{redirect:n}=t,r=g(n);e.setSegments(r.segments,u,r.queryString),yield e.writeNavStateRoot(r.segments,u)}}else yield e.onRoutesChanged()})()}componentDidLoad(){window.addEventListener("ionRouteRedirectChanged",(0,y.q)(this.onRedirectChanged.bind(this),10)),window.addEventListener("ionRouteDataChanged",(0,y.q)(this.onRoutesChanged.bind(this),100))}onPopState(){var e=this;return(0,f.A)(function*(){const t=e.historyDirection();let n=e.getSegments();const r=yield e.runGuards(n);if(!0!==r){if("object"!=typeof r)return!1;n=g(r.redirect).segments}return e.writeNavStateRoot(n,t)})()}onBackButton(e){e.detail.register(0,t=>{this.back(),t()})}canTransition(){var e=this;return(0,f.A)(function*(){const t=yield e.runGuards();return!0===t||"object"==typeof t&&t.redirect})()}push(e){var t=this;return(0,f.A)(function*(n,r="forward",o){var s;if(n.startsWith(".")){const l=null!==(s=t.previousPath)&&void 0!==s?s:"/",c=new URL(n,`https://host/${l}`);n=c.pathname+c.search}let i=g(n);const a=yield t.runGuards(i.segments);if(!0!==a){if("object"!=typeof a)return!1;i=g(a.redirect)}return t.setSegments(i.segments,r,i.queryString),t.writeNavStateRoot(i.segments,r,o)}).apply(this,arguments)}back(){return window.history.back(),Promise.resolve(this.waitPromise)}printDebug(){var e=this;return(0,f.A)(function*(){(e=>{console.group(`[ion-core] ROUTES[${e.length}]`);for(const t of e){const n=[];t.forEach(o=>n.push(...o.segments));const r=t.map(o=>o.id);console.debug(`%c ${_(n)}`,"font-weight: bold; padding-left: 20px","=>\t",`(${r.join(", ")})`)}console.groupEnd()})(P(e.el)),(e=>{console.group(`[ion-core] REDIRECTS[${e.length}]`);for(const t of e)t.to&&console.debug("FROM: ",`$c ${_(t.from)}`,"font-weight: bold"," TO: ",`$c ${_(t.to.segments)}`,"font-weight: bold");console.groupEnd()})(T(e.el))})()}navChanged(e){var t=this;return(0,f.A)(function*(){if(t.busy)return console.warn("[ion-router] router is busy, navChanged was cancelled"),!1;const{ids:n,outlet:r}=yield Q(window.document.body),s=((e,t)=>{let n=null,r=0;for(const o of t){const s=Z(e,o);s>r&&(n=o,r=s)}return n?n.map((o,s)=>{var i;return{id:o.id,segments:o.segments,params:W(o.params,null===(i=e[s])||void 0===i?void 0:i.params)}}):null})(n,P(t.el));if(!s)return console.warn("[ion-router] no matching URL for ",n.map(a=>a.id)),!1;const i=(e=>{const t=[];for(const n of e)for(const r of n.segments)if(":"===r[0]){const o=n.params&&n.params[r.slice(1)];if(!o)return null;t.push(o)}else""!==r&&t.push(r);return t})(s);return i?(t.setSegments(i,e),yield t.safeWriteNavState(r,s,u,i,null,n.length),!0):(console.warn("[ion-router] router could not match path because some required param is missing"),!1)})()}onRedirectChanged(){const e=this.getSegments();e&&M(e,T(this.el))&&this.writeNavStateRoot(e,u)}onRoutesChanged(){return this.writeNavStateRoot(this.getSegments(),u)}historyDirection(){var e;const t=window;null===t.history.state&&(this.state++,t.history.replaceState(this.state,t.document.title,null===(e=t.document.location)||void 0===e?void 0:e.href));const n=t.history.state,r=this.lastState;return this.lastState=n,n>r||n>=r&&r>0?h:n<r?"back":u}writeNavStateRoot(e,t,n){var r=this;return(0,f.A)(function*(){if(!e)return console.error("[ion-router] URL is not part of the routing set"),!1;const o=T(r.el),s=M(e,o);let i=null;if(s){const{segments:c,queryString:v}=s.to;r.setSegments(c,t,v),i=s.from,e=c}const a=P(r.el),l=A(e,a);return l?r.safeWriteNavState(document.body,l,t,e,i,0,n):(console.error("[ion-router] the path does not match any route"),!1)})()}safeWriteNavState(e,t,n,r,o){var s=this;return(0,f.A)(function*(i,a,l,c,v,x=0,U){const b=yield s.lock();let L=!1;try{L=yield s.writeNavState(i,a,l,c,v,x,U)}catch(ie){console.error(ie)}return b(),L}).apply(this,arguments)}lock(){var e=this;return(0,f.A)(function*(){const t=e.waitPromise;let n;return e.waitPromise=new Promise(r=>n=r),void 0!==t&&(yield t),n})()}runGuards(){var e=this;return(0,f.A)(function*(t=e.getSegments(),n){if(void 0===n&&(n=g(e.previousPath).segments),!t||!n)return!0;const r=P(e.el),o=A(n,r),s=o&&o[o.length-1].beforeLeave,i=!s||(yield s());if(!1===i||"object"==typeof i)return i;const a=A(t,r),l=a&&a[a.length-1].beforeEnter;return!l||l()}).apply(this,arguments)}writeNavState(e,t,n,r,o){var s=this;return(0,f.A)(function*(i,a,l,c,v,x=0,U){if(s.busy)return console.warn("[ion-router] router is busy, transition was cancelled"),!1;s.busy=!0;const b=s.routeChangeEvent(c,v);b&&s.ionRouteWillChange.emit(b);const L=yield j(i,a,l,x,!1,U);return s.busy=!1,b&&s.ionRouteDidChange.emit(b),L}).apply(this,arguments)}setSegments(e,t,n){this.state++,((e,t,n,r,o,s,i)=>{const a=((e,t,n)=>{let r=_(e);return t&&(r="#"+r),void 0!==n&&(r+="?"+n),r})([...g(t).segments,...r],n,i);o===h?e.pushState(s,"",a):e.replaceState(s,"",a)})(window.history,this.root,this.useHash,e,t,this.state,n)}getSegments(){return((e,t,n)=>{const r=g(this.root).segments,o=n?e.hash.slice(1):e.pathname;return((e,t)=>{if(e.length>t.length)return null;if(e.length<=1&&""===e[0])return t;for(let n=0;n<e.length;n++)if(e[n]!==t[n])return null;return t.length===e.length?[""]:t.slice(e.length)})(r,g(o).segments)})(window.location,0,this.useHash)}routeChangeEvent(e,t){const n=this.previousPath,r=_(e);return this.previousPath=r,r===n?null:{from:n,redirectedFrom:t?_(t):null,to:r}}get el(){return(0,d.f)(this)}},G=class{constructor(e){(0,d.r)(this,e),this.onClick=t=>{(0,E.o)(this.href,t,this.routerDirection,this.routerAnimation)},this.color=void 0,this.href=void 0,this.rel=void 0,this.routerDirection="forward",this.routerAnimation=void 0,this.target=void 0}render(){const e=(0,D.b)(this),t={href:this.href,rel:this.rel,target:this.target};return(0,d.h)(d.H,{key:"e69892f4c6a6baf040a20c429afdec0e4db2dc0e",onClick:this.onClick,class:(0,E.c)(this.color,{[e]:!0,"ion-activatable":!0})},(0,d.h)("a",Object.assign({key:"648cb22526f2933abe0865c86da6f30eac3ccb87"},t),(0,d.h)("slot",{key:"d6bc2e28b7c5cc228caedffebe653bde35934b67"})))}};G.style=":host{--background:transparent;--color:var(--ion-color-primary, #3880ff);background:var(--background);color:var(--color)}:host(.ion-color){color:var(--ion-color-base)}a{font-family:inherit;font-size:inherit;font-style:inherit;font-weight:inherit;letter-spacing:inherit;text-decoration:inherit;text-indent:inherit;text-overflow:inherit;text-transform:inherit;text-align:inherit;white-space:inherit;color:inherit}"},333:(H,C,p)=>{p.d(C,{c:()=>y,g:()=>D,h:()=>d,o:()=>N});var f=p(467);const d=(u,h)=>null!==h.closest(u),y=(u,h)=>"string"==typeof u&&u.length>0?Object.assign({"ion-color":!0,[`ion-color-${u}`]:!0},h):h,D=u=>{const h={};return(u=>void 0!==u?(Array.isArray(u)?u:u.split(" ")).filter(m=>null!=m).map(m=>m.trim()).filter(m=>""!==m):[])(u).forEach(m=>h[m]=!0),h},k=/^[a-z][a-z0-9+\-.]*:/,N=function(){var u=(0,f.A)(function*(h,m,_,S){if(null!=h&&"#"!==h[0]&&!k.test(h)){const R=document.querySelector("ion-router");if(R)return m?.preventDefault(),R.push(h,_,S)}return!1});return function(m,_,S,R){return u.apply(this,arguments)}}()}}]);