// Veritus Complete App Theme - Exact Mockup Specifications
// Color Palette
:root {
  // Primary Colors
  --veritus-primary: #0A49FF;
  --veritus-primary-dark: #0841e6;
  --veritus-primary-light: #4A90E2;

  // Gold Accent
  --veritus-gold: #B88A42;
  --veritus-gold-light: #D4AF37;
  --veritus-gold-dark: #9A7635;

  // Charcoal Gradients
  --veritus-gradient-auth: linear-gradient(180deg, #2c3e50 0%, #34495e 50%, #3c5a78 100%);
  --veritus-gradient-dark: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
  
  // Neutral Colors
  --client-white: #ffffff;
  --client-black: #000000;
  --client-gray-50: #f8f9fa;
  --client-gray-100: #f0f0f0;
  --client-gray-200: #e5e5e5;
  --client-gray-300: #dee2e6;
  --client-gray-400: #ced4da;
  --client-gray-500: #888888;
  --client-gray-600: #666666;
  --client-gray-700: #495057;
  --client-gray-800: #343a40;
  --client-gray-900: #212529;
  
  // Status Colors
  --client-success: #28a745;
  --client-warning: #ffc107;
  --client-danger: #dc3545;
  --client-info: #17a2b8;
  
  // Typography
  --client-font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --client-font-size-xs: 11px;
  --client-font-size-sm: 12px;
  --client-font-size-base: 14px;
  --client-font-size-md: 16px;
  --client-font-size-lg: 18px;
  --client-font-size-xl: 20px;
  --client-font-size-2xl: 24px;
  --client-font-size-3xl: 32px;
  
  // Font Weights
  --client-font-weight-normal: 400;
  --client-font-weight-medium: 500;
  --client-font-weight-semibold: 600;
  --client-font-weight-bold: 700;
  
  // Spacing
  --client-spacing-xs: 4px;
  --client-spacing-sm: 8px;
  --client-spacing-md: 12px;
  --client-spacing-lg: 16px;
  --client-spacing-xl: 20px;
  --client-spacing-2xl: 24px;
  --client-spacing-3xl: 32px;
  --client-spacing-4xl: 40px;
  --client-spacing-5xl: 60px;
  
  // Border Radius
  --client-radius-sm: 8px;
  --client-radius-md: 12px;
  --client-radius-lg: 16px;
  --client-radius-xl: 24px;
  --client-radius-full: 50%;
  
  // Shadows
  --client-shadow-sm: 0 2px 8px rgba(0, 0, 0, 0.06);
  --client-shadow-md: 0 2px 12px rgba(0, 0, 0, 0.06);
  --client-shadow-lg: 0 4px 20px rgba(0, 0, 0, 0.08);
  --client-shadow-xl: 0 4px 20px rgba(0, 0, 0, 0.1);
  
  // Gradients
  --client-gradient-primary: linear-gradient(135deg, #0A49FF 0%, #4A90E2 100%);
  --client-gradient-gold: linear-gradient(135deg, #B88A42 0%, #D4AF37 100%);
  --client-gradient-auth: linear-gradient(180deg, #2c3e50 0%, #34495e 50%, #3c5a78 100%);
}

// Global Client Styles
.client-container {
  padding: var(--client-spacing-xl);
  min-height: 100vh;
  background: var(--client-gray-50);
}

.client-card {
  background: var(--client-white);
  border-radius: var(--client-radius-md);
  padding: var(--client-spacing-lg);
  box-shadow: var(--client-shadow-md);
  border: 1px solid var(--client-gray-200);
}

.client-btn {
  border: none;
  border-radius: var(--client-radius-sm);
  padding: var(--client-spacing-md) var(--client-spacing-xl);
  font-size: var(--client-font-size-base);
  font-weight: var(--client-font-weight-medium);
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--client-spacing-sm);
  
  &:active {
    transform: scale(0.95);
  }
}

.client-btn-primary {
  @extend .client-btn;
  background: var(--client-primary);
  color: var(--client-white);
  
  &:hover {
    background: var(--client-primary-dark);
  }
}

.client-btn-secondary {
  @extend .client-btn;
  background: var(--client-gray-100);
  color: var(--client-gray-700);
  border: 1px solid var(--client-gray-300);
  
  &:hover {
    background: var(--client-gray-200);
  }
}

.client-btn-gold {
  @extend .client-btn;
  background: var(--client-gradient-gold);
  color: var(--client-white);
  
  &:hover {
    background: var(--client-gold-dark);
  }
}

// Typography Classes
.client-text-xs { font-size: var(--client-font-size-xs); }
.client-text-sm { font-size: var(--client-font-size-sm); }
.client-text-base { font-size: var(--client-font-size-base); }
.client-text-md { font-size: var(--client-font-size-md); }
.client-text-lg { font-size: var(--client-font-size-lg); }
.client-text-xl { font-size: var(--client-font-size-xl); }
.client-text-2xl { font-size: var(--client-font-size-2xl); }
.client-text-3xl { font-size: var(--client-font-size-3xl); }

.client-font-normal { font-weight: var(--client-font-weight-normal); }
.client-font-medium { font-weight: var(--client-font-weight-medium); }
.client-font-semibold { font-weight: var(--client-font-weight-semibold); }
.client-font-bold { font-weight: var(--client-font-weight-bold); }

// Color Classes
.client-text-primary { color: var(--client-primary); }
.client-text-gold { color: var(--client-gold); }
.client-text-white { color: var(--client-white); }
.client-text-black { color: var(--client-black); }
.client-text-gray { color: var(--client-gray-600); }
.client-text-gray-light { color: var(--client-gray-500); }
.client-text-gray-dark { color: var(--client-gray-700); }

// Background Classes
.client-bg-primary { background: var(--client-primary); }
.client-bg-gold { background: var(--client-gold); }
.client-bg-white { background: var(--client-white); }
.client-bg-gray { background: var(--client-gray-50); }

// Spacing Classes
.client-p-xs { padding: var(--client-spacing-xs); }
.client-p-sm { padding: var(--client-spacing-sm); }
.client-p-md { padding: var(--client-spacing-md); }
.client-p-lg { padding: var(--client-spacing-lg); }
.client-p-xl { padding: var(--client-spacing-xl); }

.client-m-xs { margin: var(--client-spacing-xs); }
.client-m-sm { margin: var(--client-spacing-sm); }
.client-m-md { margin: var(--client-spacing-md); }
.client-m-lg { margin: var(--client-spacing-lg); }
.client-m-xl { margin: var(--client-spacing-xl); }

// Responsive Design
@media (max-width: 375px) {
  .client-container {
    padding: var(--client-spacing-lg);
  }
  
  .client-text-3xl {
    font-size: 28px;
  }
  
  .client-text-2xl {
    font-size: 20px;
  }
}

// Safe Area Support
.client-safe-area-top {
  padding-top: env(safe-area-inset-top, 20px);
}

.client-safe-area-bottom {
  padding-bottom: env(safe-area-inset-bottom, 20px);
}
