import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { IonicModule } from '@ionic/angular';

import { AuthRoutingModule } from './auth-routing.module';

import { SignInPage } from './signin/signin.page';
import { RegisterPage } from './register/register.page';
import { RoleSelectionPage } from './role-selection/role-selection.page';
import { ForgotPasswordPage } from './forgot-password/forgot-password.page';

@NgModule({
  declarations: [
    SignInPage,
    RegisterPage,
    RoleSelectionPage,
    ForgotPasswordPage
  ],
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    IonicModule,
    AuthRoutingModule
  ]
})
export class AuthModule { }
