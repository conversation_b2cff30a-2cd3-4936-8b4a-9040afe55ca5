import { Injectable } from '@angular/core';
import { CanActivate, Router } from '@angular/router';
import { FirebaseService } from '../services/firebase.service';

@Injectable({
  providedIn: 'root'
})
export class AuthGuard implements CanActivate {
  
  constructor(
    private firebaseService: FirebaseService,
    private router: Router
  ) {}

  canActivate(): boolean {
    const currentUser = this.firebaseService.getCurrentUser();
    const isAuthenticated = localStorage.getItem('isAuthenticated') === 'true';

    if (currentUser || isAuthenticated) {
      return true;
    } else {
      this.router.navigate(['/auth/signin']);
      return false;
    }
  }
}
