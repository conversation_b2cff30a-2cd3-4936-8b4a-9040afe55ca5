"use strict";
(self["webpackChunkveritus_secretary"] = self["webpackChunkveritus_secretary"] || []).push([["src_app_secretary-tabs_secretary-tabs_module_ts"],{

/***/ 4590:
/*!*****************************************************************!*\
  !*** ./src/app/secretary-tabs/secretary-tabs-routing.module.ts ***!
  \*****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   SecretaryTabsPageRoutingModule: () => (/* binding */ SecretaryTabsPageRoutingModule)
/* harmony export */ });
/* harmony import */ var _angular_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @angular/router */ 5072);
/* harmony import */ var _secretary_tabs_page__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./secretary-tabs.page */ 256);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @angular/core */ 7580);




const routes = [{
  path: '',
  component: _secretary_tabs_page__WEBPACK_IMPORTED_MODULE_0__.SecretaryTabsPage,
  children: [{
    path: 'dashboard',
    loadChildren: () => __webpack_require__.e(/*! import() */ "src_app_secretary-dashboard_secretary-dashboard_module_ts").then(__webpack_require__.bind(__webpack_require__, /*! ../secretary-dashboard/secretary-dashboard.module */ 5503)).then(m => m.SecretaryDashboardPageModule)
  }, {
    path: 'calendar',
    loadChildren: () => __webpack_require__.e(/*! import() */ "src_app_secretary-calendar_secretary-calendar_module_ts").then(__webpack_require__.bind(__webpack_require__, /*! ../secretary-calendar/secretary-calendar.module */ 3487)).then(m => m.SecretaryCalendarPageModule)
  }, {
    path: 'cases',
    loadChildren: () => __webpack_require__.e(/*! import() */ "src_app_secretary-cases_secretary-cases_module_ts").then(__webpack_require__.bind(__webpack_require__, /*! ../secretary-cases/secretary-cases.module */ 4963)).then(m => m.SecretaryCasesPageModule)
  }, {
    path: 'files',
    loadChildren: () => __webpack_require__.e(/*! import() */ "src_app_secretary-files_secretary-files_module_ts").then(__webpack_require__.bind(__webpack_require__, /*! ../secretary-files/secretary-files.module */ 6615)).then(m => m.SecretaryFilesPageModule)
  }, {
    path: 'profile',
    loadChildren: () => __webpack_require__.e(/*! import() */ "src_app_secretary-profile_secretary-profile_module_ts").then(__webpack_require__.bind(__webpack_require__, /*! ../secretary-profile/secretary-profile.module */ 3523)).then(m => m.SecretaryProfilePageModule)
  }, {
    path: '',
    redirectTo: '/secretary-tabs/dashboard',
    pathMatch: 'full'
  }]
}];
class SecretaryTabsPageRoutingModule {
  static {
    this.ɵfac = function SecretaryTabsPageRoutingModule_Factory(t) {
      return new (t || SecretaryTabsPageRoutingModule)();
    };
  }
  static {
    this.ɵmod = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵdefineNgModule"]({
      type: SecretaryTabsPageRoutingModule
    });
  }
  static {
    this.ɵinj = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵdefineInjector"]({
      imports: [_angular_router__WEBPACK_IMPORTED_MODULE_2__.RouterModule.forChild(routes), _angular_router__WEBPACK_IMPORTED_MODULE_2__.RouterModule]
    });
  }
}
(function () {
  (typeof ngJitMode === "undefined" || ngJitMode) && _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵsetNgModuleScope"](SecretaryTabsPageRoutingModule, {
    imports: [_angular_router__WEBPACK_IMPORTED_MODULE_2__.RouterModule],
    exports: [_angular_router__WEBPACK_IMPORTED_MODULE_2__.RouterModule]
  });
})();

/***/ }),

/***/ 6519:
/*!*********************************************************!*\
  !*** ./src/app/secretary-tabs/secretary-tabs.module.ts ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   SecretaryTabsPageModule: () => (/* binding */ SecretaryTabsPageModule)
/* harmony export */ });
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @angular/common */ 316);
/* harmony import */ var _angular_forms__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @angular/forms */ 4456);
/* harmony import */ var _ionic_angular__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @ionic/angular */ 7401);
/* harmony import */ var _secretary_tabs_routing_module__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./secretary-tabs-routing.module */ 4590);
/* harmony import */ var _secretary_tabs_page__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./secretary-tabs.page */ 256);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @angular/core */ 7580);






class SecretaryTabsPageModule {
  static {
    this.ɵfac = function SecretaryTabsPageModule_Factory(t) {
      return new (t || SecretaryTabsPageModule)();
    };
  }
  static {
    this.ɵmod = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵdefineNgModule"]({
      type: SecretaryTabsPageModule
    });
  }
  static {
    this.ɵinj = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵdefineInjector"]({
      imports: [_angular_common__WEBPACK_IMPORTED_MODULE_3__.CommonModule, _angular_forms__WEBPACK_IMPORTED_MODULE_4__.FormsModule, _ionic_angular__WEBPACK_IMPORTED_MODULE_5__.IonicModule, _secretary_tabs_routing_module__WEBPACK_IMPORTED_MODULE_0__.SecretaryTabsPageRoutingModule]
    });
  }
}
(function () {
  (typeof ngJitMode === "undefined" || ngJitMode) && _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵsetNgModuleScope"](SecretaryTabsPageModule, {
    declarations: [_secretary_tabs_page__WEBPACK_IMPORTED_MODULE_1__.SecretaryTabsPage],
    imports: [_angular_common__WEBPACK_IMPORTED_MODULE_3__.CommonModule, _angular_forms__WEBPACK_IMPORTED_MODULE_4__.FormsModule, _ionic_angular__WEBPACK_IMPORTED_MODULE_5__.IonicModule, _secretary_tabs_routing_module__WEBPACK_IMPORTED_MODULE_0__.SecretaryTabsPageRoutingModule]
  });
})();

/***/ }),

/***/ 256:
/*!*******************************************************!*\
  !*** ./src/app/secretary-tabs/secretary-tabs.page.ts ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   SecretaryTabsPage: () => (/* binding */ SecretaryTabsPage)
/* harmony export */ });
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @angular/core */ 7580);
/* harmony import */ var _ionic_angular__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @ionic/angular */ 7401);


class SecretaryTabsPage {
  constructor() {}
  ngOnInit() {}
  static {
    this.ɵfac = function SecretaryTabsPage_Factory(t) {
      return new (t || SecretaryTabsPage)();
    };
  }
  static {
    this.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵdefineComponent"]({
      type: SecretaryTabsPage,
      selectors: [["app-secretary-tabs"]],
      decls: 23,
      vars: 0,
      consts: [["slot", "bottom", 1, "veritus-tab-bar"], ["tab", "dashboard"], ["name", "home"], ["tab", "calendar"], ["name", "calendar"], ["tab", "cases"], ["name", "folder"], ["tab", "files"], ["name", "document"], ["tab", "profile"], ["name", "person"]],
      template: function SecretaryTabsPage_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](0, "ion-tabs");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelement"](1, "ion-router-outlet");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](2, "ion-tab-bar", 0)(3, "ion-tab-button", 1);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelement"](4, "ion-icon", 2);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](5, "ion-label");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](6, "Dashboard");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](7, "ion-tab-button", 3);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelement"](8, "ion-icon", 4);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](9, "ion-label");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](10, "Calendar");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](11, "ion-tab-button", 5);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelement"](12, "ion-icon", 6);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](13, "ion-label");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](14, "Cases");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](15, "ion-tab-button", 7);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelement"](16, "ion-icon", 8);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](17, "ion-label");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](18, "Files");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](19, "ion-tab-button", 9);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelement"](20, "ion-icon", 10);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](21, "ion-label");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](22, "Profile");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]()()()();
        }
      },
      dependencies: [_ionic_angular__WEBPACK_IMPORTED_MODULE_1__.IonIcon, _ionic_angular__WEBPACK_IMPORTED_MODULE_1__.IonLabel, _ionic_angular__WEBPACK_IMPORTED_MODULE_1__.IonTabBar, _ionic_angular__WEBPACK_IMPORTED_MODULE_1__.IonTabButton, _ionic_angular__WEBPACK_IMPORTED_MODULE_1__.IonTabs, _ionic_angular__WEBPACK_IMPORTED_MODULE_1__.IonRouterOutlet],
      styles: [".veritus-tab-bar[_ngcontent-%COMP%] {\n  --background: rgba(213, 208, 200, 0.38);\n  --color: #666;\n  --color-selected: #d4af37;\n  border-top: 1px solid rgba(255, 255, 255, 0.1);\n  -webkit-backdrop-filter: blur(10px);\n          backdrop-filter: blur(10px);\n}\n\nion-tab-button[_ngcontent-%COMP%] {\n  --color: rgba(255, 255, 255, 0.6);\n  --color-selected: #d4af37;\n  --ripple-color: rgba(212, 175, 55, 0.2);\n}\nion-tab-button.tab-selected[_ngcontent-%COMP%] {\n  --color: #d4af37;\n}\nion-tab-button.tab-selected[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\n  color: #d4af37;\n}\nion-tab-button.tab-selected[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%] {\n  color: #d4af37;\n  font-weight: 600;\n}\n\nion-icon[_ngcontent-%COMP%] {\n  font-size: 22px;\n  margin-bottom: 4px;\n}\n\nion-label[_ngcontent-%COMP%] {\n  font-size: 12px;\n  font-weight: 500;\n}\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvc2VjcmV0YXJ5LXRhYnMvc2VjcmV0YXJ5LXRhYnMucGFnZS5zY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBO0VBQ0UsdUNBQUE7RUFDQSxhQUFBO0VBQ0EseUJBQUE7RUFDQSw4Q0FBQTtFQUNBLG1DQUFBO1VBQUEsMkJBQUE7QUFDRjs7QUFFQTtFQUNFLGlDQUFBO0VBQ0EseUJBQUE7RUFDQSx1Q0FBQTtBQUNGO0FBQ0U7RUFDRSxnQkFBQTtBQUNKO0FBQ0k7RUFDRSxjQUFBO0FBQ047QUFFSTtFQUNFLGNBQUE7RUFDQSxnQkFBQTtBQUFOOztBQUtBO0VBQ0UsZUFBQTtFQUNBLGtCQUFBO0FBRkY7O0FBS0E7RUFDRSxlQUFBO0VBQ0EsZ0JBQUE7QUFGRiIsInNvdXJjZXNDb250ZW50IjpbIi52ZXJpdHVzLXRhYi1iYXIge1xyXG4gIC0tYmFja2dyb3VuZDogcmdiYSgyMTMsIDIwOCwgMjAwLCAwLjM4KTtcclxuICAtLWNvbG9yOiAjNjY2O1xyXG4gIC0tY29sb3Itc2VsZWN0ZWQ6ICNkNGFmMzc7XHJcbiAgYm9yZGVyLXRvcDogMXB4IHNvbGlkIHJnYmEoMjU1LCAyNTUsIDI1NSwgMC4xKTtcclxuICBiYWNrZHJvcC1maWx0ZXI6IGJsdXIoMTBweCk7XHJcbn1cclxuXHJcbmlvbi10YWItYnV0dG9uIHtcclxuICAtLWNvbG9yOiByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuNik7XHJcbiAgLS1jb2xvci1zZWxlY3RlZDogI2Q0YWYzNztcclxuICAtLXJpcHBsZS1jb2xvcjogcmdiYSgyMTIsIDE3NSwgNTUsIDAuMik7XHJcbiAgXHJcbiAgJi50YWItc2VsZWN0ZWQge1xyXG4gICAgLS1jb2xvcjogI2Q0YWYzNztcclxuICAgIFxyXG4gICAgaW9uLWljb24ge1xyXG4gICAgICBjb2xvcjogI2Q0YWYzNztcclxuICAgIH1cclxuICAgIFxyXG4gICAgaW9uLWxhYmVsIHtcclxuICAgICAgY29sb3I6ICNkNGFmMzc7XHJcbiAgICAgIGZvbnQtd2VpZ2h0OiA2MDA7XHJcbiAgICB9XHJcbiAgfVxyXG59XHJcblxyXG5pb24taWNvbiB7XHJcbiAgZm9udC1zaXplOiAyMnB4O1xyXG4gIG1hcmdpbi1ib3R0b206IDRweDtcclxufVxyXG5cclxuaW9uLWxhYmVsIHtcclxuICBmb250LXNpemU6IDEycHg7XHJcbiAgZm9udC13ZWlnaHQ6IDUwMDtcclxufVxyXG4iXSwic291cmNlUm9vdCI6IiJ9 */"]
    });
  }
}

/***/ })

}]);
//# sourceMappingURL=src_app_secretary-tabs_secretary-tabs_module_ts.js.map