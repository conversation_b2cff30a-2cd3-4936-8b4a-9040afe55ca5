.profile-content {
  --background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
}

.profile-container {
  padding: 20px;
  min-height: 100vh;
}

.profile-header {
  margin-bottom: 30px;
}

.avatar-section {
  text-align: center;
  padding: 20px;
}

.avatar-circle {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 16px;
  border: 2px solid #d4af37;
}

.avatar-image {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  object-fit: cover;
}

.avatar-icon {
  font-size: 40px;
  color: #d4af37;
}

.profile-name {
  margin-bottom: 4px;
}

.profile-email {
  opacity: 0.8;
}

.stats-section {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 15px;
  margin-bottom: 30px;
}

.stat-card {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 16px;
  text-align: center;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.stat-number {
  margin-bottom: 4px;
}

.stat-label {
  opacity: 0.8;
}

.info-section {
  margin-bottom: 30px;
}

.section-title {
  margin-bottom: 15px;
}

.info-card {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 20px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.info-item {
  margin-bottom: 20px;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.info-label {
  display: block;
  margin-bottom: 4px;
  font-weight: 500;
}

.info-value {
  margin: 0;
}

.edit-btn {
  width: 100%;
  margin-top: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.edit-form {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 20px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.form-item {
  margin-bottom: 20px;
}

.form-label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
}

.form-input {
  --background: rgba(255, 255, 255, 0.1);
  --color: white;
  --placeholder-color: rgba(255, 255, 255, 0.6);
  --border-radius: 8px;
  --padding-start: 12px;
  --padding-end: 12px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
}

.form-actions {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
  margin-top: 20px;
}

.save-btn, .cancel-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.btn-icon {
  font-size: 16px;
}

.actions-section {
  margin-bottom: 30px;
}

.action-cards {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.action-card {
  background: rgba(255, 255, 255, 0.1);
  border: none;
  border-radius: 12px;
  padding: 16px;
  display: grid;
  grid-template-columns: auto 1fr auto;
  gap: 15px;
  align-items: center;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
  text-align: left;
  
  &:hover {
    background: rgba(255, 255, 255, 0.15);
    transform: translateX(5px);
  }
}

.action-icon {
  font-size: 24px;
  color: #d4af37;
}

.action-content {
  flex: 1;
}

.action-title {
  margin-bottom: 4px;
}

.action-description {
  margin: 0;
}

.chevron-icon {
  color: #d4af37;
  font-size: 18px;
}

.logout-section {
  margin-top: 40px;
  padding-top: 20px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.logout-btn {
  width: 100%;
  background: rgba(244, 67, 54, 0.2);
  border: 1px solid rgba(244, 67, 54, 0.3);
  border-radius: 12px;
  padding: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  transition: all 0.3s ease;
  
  &:hover {
    background: rgba(244, 67, 54, 0.3);
    transform: translateY(-2px);
  }
}

.logout-icon {
  font-size: 20px;
  color: #f44336;
}

.logout-text {
  color: #f44336;
  font-weight: 500;
}

@media (max-width: 768px) {
  .stats-section {
    grid-template-columns: 1fr;
  }
  
  .form-actions {
    grid-template-columns: 1fr;
  }
  
  .action-card {
    grid-template-columns: 1fr;
    text-align: center;
    gap: 10px;
  }
}
