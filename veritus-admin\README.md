# Veritus Admin Panel

A comprehensive admin panel for the Veritus legal platform, built with Angular and Firebase.

## Features

### 🔐 Authentication & Security
- Secure admin login with role-based access control
- Firebase Authentication integration
- Admin-only access with permission management
- Session management and auto-logout

### 📊 Dashboard
- Real-time statistics and metrics
- Recent activity monitoring
- System health indicators
- Quick action buttons for common tasks

### ⚖️ Lawyer Credential Verification
- Review and approve lawyer registrations
- Document verification system
- IBP (Integrated Bar of the Philippines) integration
- Bulk approval/rejection capabilities
- Verification history tracking

### 👥 User Management
- Comprehensive user directory
- Account status management (active/inactive/flagged)
- User type filtering (lawyers, clients, secretaries)
- Account deletion and suspension

### 🏢 Platform Control
- **Template Management**: Create, edit, and manage legal document templates
- **System Announcements**: Platform-wide notifications and alerts
- **System Settings**: Configure platform behavior and features
- **Account Management**: Advanced user account controls

### 📄 Template System
- Legal document template library
- Template usage analytics
- Version control and updates
- Template categorization

### ⚙️ System Configuration
- Platform settings management
- Maintenance mode controls
- Feature toggles
- Notification preferences

## Technology Stack

- **Frontend**: Angular 17+ with TypeScript
- **Styling**: SCSS with custom design system
- **Backend**: Firebase (Firestore, Authentication, Storage)
- **State Management**: RxJS Observables
- **UI Components**: Custom component library
- **Icons**: Unicode emoji icons for simplicity

## Design System

### Color Palette
- **Primary**: #C49A56 (Adbun Gold)
- **Primary Dark**: #B8894A
- **Success**: #28a745
- **Danger**: #dc3545
- **Warning**: #ffc107
- **Info**: #17a2b8

### Typography
- **Font Family**: Inter (Google Fonts)
- **Weights**: 300, 400, 500, 600, 700

## Project Structure

```
veritus-admin/
├── src/
│   ├── app/
│   │   ├── auth/                 # Authentication components
│   │   ├── dashboard/            # Main dashboard
│   │   ├── lawyer-verification/  # Lawyer verification system
│   │   ├── lawyer-list/          # Lawyer directory
│   │   ├── platform-control/     # Platform management
│   │   ├── templates/            # Template management
│   │   ├── users/                # User management
│   │   ├── settings/             # Admin settings
│   │   ├── shared/               # Shared components
│   │   │   ├── header/           # Top navigation
│   │   │   └── sidebar/          # Side navigation
│   │   ├── services/             # Business logic services
│   │   ├── guards/               # Route guards
│   │   └── models/               # TypeScript interfaces
│   ├── assets/                   # Static assets
│   ├── environments/             # Environment configurations
│   └── styles.scss               # Global styles
├── package.json
├── angular.json
└── README.md
```

## Installation & Setup

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd veritus-admin
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Configure Firebase**
   - Update `src/environments/environment.ts` with your Firebase config
   - Ensure Firebase project has Firestore, Authentication, and Storage enabled

4. **Start development server**
   ```bash
   npm start
   ```

5. **Access the application**
   - Open http://localhost:4200
   - Use admin credentials to log in

## Firebase Setup

### Required Collections

1. **admins** - Admin user accounts
   ```typescript
   {
     uid: string,
     email: string,
     displayName: string,
     role: 'super_admin' | 'admin' | 'moderator',
     permissions: string[],
     isActive: boolean,
     lastLogin: Date,
     createdAt: Date
   }
   ```

2. **lawyer_verifications** - Lawyer verification requests
   ```typescript
   {
     name: string,
     email: string,
     rollNumber: string,
     firm: string,
     status: 'pending' | 'approved' | 'rejected' | 'under_review',
     documents: DocumentReference[],
     submittedAt: Date,
     verificationHistory: VerificationAction[]
   }
   ```

3. **templates** - Legal document templates
   ```typescript
   {
     name: string,
     type: string,
     description: string,
     content: string,
     usageCount: number,
     isActive: boolean,
     lastUpdated: Date
   }
   ```

4. **system_settings** - Platform configuration
   ```typescript
   {
     platformName: string,
     maintenanceMode: boolean,
     allowRegistration: boolean,
     emailNotifications: boolean,
     lastUpdated: Date
   }
   ```

### Security Rules

Ensure proper Firestore security rules are configured to restrict admin access.

## Key Features Implementation

### Lawyer Verification Workflow
1. Lawyers submit verification requests through the main platform
2. Admin receives notification of new verification
3. Admin reviews documents and credentials
4. Optional IBP verification check
5. Approve/reject with notes
6. Automatic notification to lawyer

### Template Management
1. Upload legal document templates
2. Categorize by type (contracts, agreements, etc.)
3. Track usage analytics
4. Version control and updates
5. Bulk operations

### User Management
1. View all platform users
2. Filter by type and status
3. Account activation/deactivation
4. Flag suspicious accounts
5. Export user data

## Development Guidelines

### Component Structure
- Each feature has its own module
- Shared components in `/shared` directory
- Services handle all business logic
- Guards protect admin routes

### Styling Conventions
- Use SCSS with BEM methodology
- Responsive design (mobile-first)
- Consistent spacing and typography
- Custom CSS variables for theming

### State Management
- Services use RxJS Observables
- Reactive forms for user input
- Error handling with try-catch blocks
- Loading states for better UX

## Deployment

### Build for Production
```bash
npm run build
```

### Deploy to Firebase Hosting
```bash
firebase deploy --only hosting
```

### Environment Variables
- Update `environment.prod.ts` for production
- Configure Firebase project settings
- Set up proper security rules

## Security Considerations

1. **Authentication**: Only verified admin accounts can access
2. **Authorization**: Role-based permissions for different admin levels
3. **Data Protection**: Sensitive data encryption and secure transmission
4. **Audit Trail**: All admin actions are logged for accountability
5. **Session Management**: Automatic logout and session validation

## Contributing

1. Follow Angular style guide
2. Write unit tests for new features
3. Update documentation for API changes
4. Use conventional commit messages
5. Test on multiple browsers and devices

## Support

For technical support or feature requests, please contact the development team.

---

**Veritus Admin Panel** - Empowering legal professionals with efficient platform management.
