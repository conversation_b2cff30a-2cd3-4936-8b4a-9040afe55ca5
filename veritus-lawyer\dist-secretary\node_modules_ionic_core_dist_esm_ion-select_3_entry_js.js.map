{"version": 3, "file": "node_modules_ionic_core_dist_esm_ion-select_3_entry_js.js", "mappings": ";;;;;;;;;;;;;;AAAA;AACA;AACA;AAC2D;;AAE3D;AACA;AACA;AACA;AACA;AACA;AACA,MAAME,0BAA0B,GAAIC,EAAE,IAAK;EACvC,MAAMC,SAAS,GAAGD,EAAE;EACpB,IAAIE,aAAa;EACjB,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;IAC3B,IAAID,aAAa,KAAKE,SAAS,EAAE;MAC7B;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACY,MAAMC,YAAY,GAAGJ,SAAS,CAACK,KAAK,KAAKF,SAAS,IAAIG,YAAY,CAACN,SAAS,CAAC;MAC7E,MAAMO,qBAAqB,GAAGP,SAAS,CAACQ,YAAY,CAAC,YAAY,CAAC;MAC9D;MACCR,SAAS,CAACQ,YAAY,CAAC,iBAAiB,CAAC,IAAIR,SAAS,CAACS,UAAU,KAAK,IAAK;MAChF,MAAMC,eAAe,GAAGb,uDAAa,CAACG,SAAS,CAAC;MAChD;AACZ;AACA;AACA;MACYC,aAAa,GACTD,SAAS,CAACW,MAAM,KAAK,IAAI,IAAK,CAACP,YAAY,IAAI,CAACG,qBAAqB,IAAIG,eAAe,KAAK,IAAK;IAC1G;IACA,OAAOT,aAAa;EACxB,CAAC;EACD,OAAO;IAAEC;EAAiB,CAAC;AAC/B,CAAC;AACD,MAAMI,YAAY,GAAIN,SAAS,IAAK;EAChC;AACJ;AACA;AACA;AACA;EACI,IAAIY,2BAA2B,CAACC,QAAQ,CAACb,SAAS,CAACc,OAAO,CAAC,IAAId,SAAS,CAACe,aAAa,CAAC,gBAAgB,CAAC,KAAK,IAAI,EAAE;IAC/G,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;EACI,IAAIC,6BAA6B,CAACH,QAAQ,CAACb,SAAS,CAACc,OAAO,CAAC,IAAId,SAAS,CAACiB,WAAW,KAAK,EAAE,EAAE;IAC3F,OAAO,IAAI;EACf;EACA,OAAO,KAAK;AAChB,CAAC;AACD,MAAML,2BAA2B,GAAG,CAAC,WAAW,EAAE,cAAc,EAAE,YAAY,EAAE,WAAW,CAAC;AAC5F,MAAMI,6BAA6B,GAAG,CAAC,YAAY,EAAE,cAAc,EAAE,WAAW,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC7DjF;AACA;AACA;AAC+H;AAC/C;AACJ;AACkB;AAC0D;AAC7F;AACsE;AAClF;AACmD;AAC/B;AACW;AACjB;AAChC;AACe;AACF;AAE1C,MAAM0C,YAAY,GAAG,q7RAAq7R;AAC18R,MAAMC,kBAAkB,GAAGD,YAAY;AAEvC,MAAME,WAAW,GAAG,29jBAA29jB;AAC/+jB,MAAMC,iBAAiB,GAAGD,WAAW;AAErC,MAAME,MAAM,GAAG,MAAM;EACjBC,WAAWA,CAACC,OAAO,EAAE;IACjB5C,qDAAgB,CAAC,IAAI,EAAE4C,OAAO,CAAC;IAC/B,IAAI,CAACC,SAAS,GAAG3C,qDAAW,CAAC,IAAI,EAAE,WAAW,EAAE,CAAC,CAAC;IAClD,IAAI,CAAC4C,SAAS,GAAG5C,qDAAW,CAAC,IAAI,EAAE,WAAW,EAAE,CAAC,CAAC;IAClD,IAAI,CAAC6C,UAAU,GAAG7C,qDAAW,CAAC,IAAI,EAAE,YAAY,EAAE,CAAC,CAAC;IACpD,IAAI,CAAC8C,QAAQ,GAAG9C,qDAAW,CAAC,IAAI,EAAE,UAAU,EAAE,CAAC,CAAC;IAChD,IAAI,CAAC+C,OAAO,GAAG/C,qDAAW,CAAC,IAAI,EAAE,SAAS,EAAE,CAAC,CAAC;IAC9C,IAAI,CAACgD,QAAQ,GAAGhD,qDAAW,CAAC,IAAI,EAAE,UAAU,EAAE,CAAC,CAAC;IAChD,IAAI,CAACiD,OAAO,GAAG,WAAWC,SAAS,EAAE,EAAE;IACvC,IAAI,CAACC,mBAAmB,GAAG,CAAC,CAAC;IAC7B;IACA,IAAI,CAACC,2BAA2B,GAAG,KAAK;IACxC,IAAI,CAACC,OAAO,GAAIC,EAAE,IAAK;MACnB,MAAMC,MAAM,GAAGD,EAAE,CAACC,MAAM;MACxB,MAAMC,WAAW,GAAGD,MAAM,CAACE,OAAO,CAAC,8BAA8B,CAAC;MAClE,IAAIF,MAAM,KAAK,IAAI,CAAC9E,EAAE,IAAI+E,WAAW,KAAK,IAAI,EAAE;QAC5C,IAAI,CAACE,QAAQ,CAAC,CAAC;QACf,IAAI,CAACC,IAAI,CAACL,EAAE,CAAC;MACjB,CAAC,MACI;QACD;AAChB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;QACgBA,EAAE,CAACM,cAAc,CAAC,CAAC;MACvB;IACJ,CAAC;IACD,IAAI,CAACC,OAAO,GAAG,MAAM;MACjB,IAAI,CAACf,QAAQ,CAACgB,IAAI,CAAC,CAAC;IACxB,CAAC;IACD,IAAI,CAACC,MAAM,GAAG,MAAM;MAChB,IAAI,CAAChB,OAAO,CAACe,IAAI,CAAC,CAAC;IACvB,CAAC;IACD,IAAI,CAACE,UAAU,GAAG,KAAK;IACvB,IAAI,CAACC,UAAU,GAAG,QAAQ;IAC1B,IAAI,CAACC,KAAK,GAAGrF,SAAS;IACtB,IAAI,CAACsF,WAAW,GAAGtF,SAAS;IAC5B,IAAI,CAACuF,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACC,IAAI,GAAGxF,SAAS;IACrB,IAAI,CAACyF,SAAS,GAAG,OAAO;IACxB,IAAI,CAACC,gBAAgB,GAAG,CAAC,CAAC;IAC1B,IAAI,CAACC,OAAO,GAAG,eAAe;IAC9B,IAAI,CAACzF,KAAK,GAAGF,SAAS;IACtB,IAAI,CAAC4F,cAAc,GAAG,OAAO;IAC7B,IAAI,CAACpF,MAAM,GAAGR,SAAS;IACvB,IAAI,CAAC6F,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACC,IAAI,GAAG,IAAI,CAAC1B,OAAO;IACxB,IAAI,CAAC2B,MAAM,GAAG,IAAI;IAClB,IAAI,CAACC,WAAW,GAAGhG,SAAS;IAC5B,IAAI,CAACiG,YAAY,GAAGjG,SAAS;IAC7B,IAAI,CAACkG,UAAU,GAAGlG,SAAS;IAC3B,IAAI,CAACmG,YAAY,GAAGnG,SAAS;IAC7B,IAAI,CAACoG,KAAK,GAAGpG,SAAS;IACtB,IAAI,CAACqG,KAAK,GAAGrG,SAAS;EAC1B;EACAsG,YAAYA,CAAA,EAAG;IACX,IAAI,CAACC,SAAS,CAAC,CAAC;EACpB;EACAC,QAAQA,CAACH,KAAK,EAAE;IACZ,IAAI,CAACA,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACvC,SAAS,CAACmB,IAAI,CAAC;MAAEoB;IAAM,CAAC,CAAC;EAClC;EACAI,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAACnC,mBAAmB,GAAGxC,uDAAiB,CAAC,IAAI,CAAClC,EAAE,EAAE,CAAC,YAAY,CAAC,CAAC;EACzE;EACM8G,iBAAiBA,CAAA,EAAG;IAAA,IAAAC,KAAA;IAAA,OAAAC,6KAAA;MACtB,MAAM;QAAEhH;MAAG,CAAC,GAAG+G,KAAI;MACnBA,KAAI,CAACE,oBAAoB,GAAGlH,+DAA0B,CAACC,EAAE,CAAC;MAC1D+G,KAAI,CAACG,eAAe,GAAGpF,gEAAqB,CAAC9B,EAAE,EAAE,MAAM+G,KAAI,CAACI,aAAa,EAAE,MAAMJ,KAAI,CAACK,SAAS,CAAC;MAChGL,KAAI,CAACM,oBAAoB,CAAC,CAAC;MAC3BN,KAAI,CAACJ,SAAS,CAAC,CAAC;MAChBI,KAAI,CAACO,SAAS,GAAGhE,8DAAe,CAACyD,KAAI,CAAC/G,EAAE,EAAE,mBAAmB,eAAAgH,6KAAA,CAAE,aAAY;QACvED,KAAI,CAACM,oBAAoB,CAAC,CAAC;QAC3B;AACZ;AACA;AACA;AACA;AACA;QACYxF,qDAAW,CAACkF,KAAI,CAAC;MACrB,CAAC,EAAC;IAAC;EACP;EACAQ,oBAAoBA,CAAA,EAAG;IACnB,IAAI,IAAI,CAACD,SAAS,EAAE;MAChB,IAAI,CAACA,SAAS,CAACE,UAAU,CAAC,CAAC;MAC3B,IAAI,CAACF,SAAS,GAAGlH,SAAS;IAC9B;IACA,IAAI,IAAI,CAAC8G,eAAe,EAAE;MACtB,IAAI,CAACA,eAAe,CAACO,OAAO,CAAC,CAAC;MAC9B,IAAI,CAACP,eAAe,GAAG9G,SAAS;IACpC;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;EACU8E,IAAIA,CAACwC,KAAK,EAAE;IAAA,IAAAC,MAAA;IAAA,OAAAX,6KAAA;MACd,IAAIW,MAAI,CAAChC,QAAQ,IAAIgC,MAAI,CAACpC,UAAU,EAAE;QAClC,OAAOnF,SAAS;MACpB;MACAuH,MAAI,CAACpC,UAAU,GAAG,IAAI;MACtB,MAAMqC,OAAO,GAAID,MAAI,CAACC,OAAO,SAASD,MAAI,CAACE,aAAa,CAACH,KAAK,CAAE;MAChEE,OAAO,CAACE,YAAY,CAAC,CAAC,CAACC,IAAI,CAAC,MAAM;QAC9BJ,MAAI,CAACC,OAAO,GAAGxH,SAAS;QACxBuH,MAAI,CAACpC,UAAU,GAAG,KAAK;QACvBoC,MAAI,CAACvD,UAAU,CAACiB,IAAI,CAAC,CAAC;QACtBsC,MAAI,CAAC1C,QAAQ,CAAC,CAAC;MACnB,CAAC,CAAC;MACF,MAAM2C,OAAO,CAACI,OAAO,CAAC,CAAC;MACvB;MACA,IAAIL,MAAI,CAAC9B,SAAS,KAAK,SAAS,EAAE;QAC9B,MAAMoC,eAAe,GAAGN,MAAI,CAACO,SAAS,CAACC,GAAG,CAAEC,CAAC,IAAKA,CAAC,CAAC3B,KAAK,CAAC,CAAC4B,OAAO,CAACV,MAAI,CAAClB,KAAK,CAAC;QAC9E,IAAIwB,eAAe,GAAG,CAAC,CAAC,EAAE;UACtB,MAAMK,YAAY,GAAGV,OAAO,CAAC5G,aAAa,CAAC,sCAAsCiH,eAAe,GAAG,CAAC,GAAG,CAAC;UACxG,IAAIK,YAAY,EAAE;YACdnG,uDAAmB,CAACmG,YAAY,CAAC;YACjC;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;YACoB,MAAMC,aAAa,GAAGD,YAAY,CAACtH,aAAa,CAAC,yBAAyB,CAAC;YAC3E,IAAIuH,aAAa,EAAE;cACfA,aAAa,CAACC,KAAK,CAAC,CAAC;YACzB;UACJ;QACJ,CAAC,MACI;UACD;AAChB;AACA;UACgB,MAAMC,kBAAkB,GAAGb,OAAO,CAAC5G,aAAa,CAAC,sEAAsE,CAAC;UACxH,IAAIyH,kBAAkB,EAAE;YACpBtG,uDAAmB,CAACsG,kBAAkB,CAACzD,OAAO,CAAC,UAAU,CAAC,CAAC;YAC3D;AACpB;AACA;YACoByD,kBAAkB,CAACD,KAAK,CAAC,CAAC;UAC9B;QACJ;MACJ;MACA,OAAOZ,OAAO;IAAC;EACnB;EACAC,aAAaA,CAAChD,EAAE,EAAE;IACd,IAAI6D,eAAe,GAAG,IAAI,CAAC7C,SAAS;IACpC,IAAI6C,eAAe,KAAK,cAAc,IAAI,IAAI,CAACzC,QAAQ,EAAE;MACrD0C,OAAO,CAACC,IAAI,CAAC,+BAA+BF,eAAe,mEAAmE,CAAC;MAC/HA,eAAe,GAAG,OAAO;IAC7B;IACA,IAAIA,eAAe,KAAK,SAAS,IAAI,CAAC7D,EAAE,EAAE;MACtC8D,OAAO,CAACC,IAAI,CAAC,iCAAiCF,eAAe,kEAAkE,CAAC;MAChIA,eAAe,GAAG,OAAO;IAC7B;IACA,IAAIA,eAAe,KAAK,cAAc,EAAE;MACpC,OAAO,IAAI,CAACG,eAAe,CAAC,CAAC;IACjC;IACA,IAAIH,eAAe,KAAK,SAAS,EAAE;MAC/B,OAAO,IAAI,CAACI,WAAW,CAACjE,EAAE,CAAC;IAC/B;IACA,OAAO,IAAI,CAACkE,SAAS,CAAC,CAAC;EAC3B;EACA1B,oBAAoBA,CAAA,EAAG;IACnB,MAAMO,OAAO,GAAG,IAAI,CAACA,OAAO;IAC5B,IAAI,CAACA,OAAO,EAAE;MACV;IACJ;IACA,MAAMM,SAAS,GAAG,IAAI,CAACA,SAAS;IAChC,MAAMzB,KAAK,GAAG,IAAI,CAACA,KAAK;IACxB,QAAQ,IAAI,CAACZ,SAAS;MAClB,KAAK,cAAc;QACf+B,OAAO,CAACoB,OAAO,GAAG,IAAI,CAACC,wBAAwB,CAACf,SAAS,EAAEzB,KAAK,CAAC;QACjE;MACJ,KAAK,SAAS;QACV,MAAMyC,OAAO,GAAGtB,OAAO,CAAC5G,aAAa,CAAC,oBAAoB,CAAC;QAC3D,IAAIkI,OAAO,EAAE;UACTA,OAAO,CAACC,OAAO,GAAG,IAAI,CAACC,oBAAoB,CAAClB,SAAS,EAAEzB,KAAK,CAAC;QACjE;QACA;MACJ,KAAK,OAAO;QACR,MAAM4C,SAAS,GAAG,IAAI,CAACpD,QAAQ,GAAG,UAAU,GAAG,OAAO;QACtD2B,OAAO,CAAC0B,MAAM,GAAG,IAAI,CAACC,iBAAiB,CAACrB,SAAS,EAAEmB,SAAS,EAAE5C,KAAK,CAAC;QACpE;IACR;EACJ;EACAwC,wBAAwBA,CAACO,IAAI,EAAEC,WAAW,EAAE;IACxC,MAAMC,kBAAkB,GAAGF,IAAI,CAACrB,GAAG,CAAEwB,MAAM,IAAK;MAC5C,MAAMlD,KAAK,GAAGmD,cAAc,CAACD,MAAM,CAAC;MACpC;MACA,MAAME,WAAW,GAAGC,KAAK,CAACC,IAAI,CAACJ,MAAM,CAACK,SAAS,CAAC,CAC3CC,MAAM,CAAEC,GAAG,IAAKA,GAAG,KAAK,UAAU,CAAC,CACnCC,IAAI,CAAC,GAAG,CAAC;MACd,MAAMC,QAAQ,GAAG,GAAGC,YAAY,IAAIR,WAAW,EAAE;MACjD,OAAO;QACHS,IAAI,EAAEvI,kEAAgB,CAAC0H,WAAW,EAAEhD,KAAK,EAAE,IAAI,CAACf,WAAW,CAAC,GAAG,UAAU,GAAG,EAAE;QAC9E6E,IAAI,EAAEZ,MAAM,CAACzI,WAAW;QACxBsJ,QAAQ,EAAEJ,QAAQ;QAClBK,OAAO,EAAEA,CAAA,KAAM;UACX,IAAI,CAAC7D,QAAQ,CAACH,KAAK,CAAC;QACxB;MACJ,CAAC;IACL,CAAC,CAAC;IACF;IACAiD,kBAAkB,CAACgB,IAAI,CAAC;MACpBH,IAAI,EAAE,IAAI,CAAC/E,UAAU;MACrB8E,IAAI,EAAE,QAAQ;MACdG,OAAO,EAAEA,CAAA,KAAM;QACX,IAAI,CAACtG,SAAS,CAACkB,IAAI,CAAC,CAAC;MACzB;IACJ,CAAC,CAAC;IACF,OAAOqE,kBAAkB;EAC7B;EACAH,iBAAiBA,CAACC,IAAI,EAAEH,SAAS,EAAEI,WAAW,EAAE;IAC5C,MAAMkB,WAAW,GAAGnB,IAAI,CAACrB,GAAG,CAAEwB,MAAM,IAAK;MACrC,MAAMlD,KAAK,GAAGmD,cAAc,CAACD,MAAM,CAAC;MACpC;MACA,MAAME,WAAW,GAAGC,KAAK,CAACC,IAAI,CAACJ,MAAM,CAACK,SAAS,CAAC,CAC3CC,MAAM,CAAEC,GAAG,IAAKA,GAAG,KAAK,UAAU,CAAC,CACnCC,IAAI,CAAC,GAAG,CAAC;MACd,MAAMC,QAAQ,GAAG,GAAGC,YAAY,IAAIR,WAAW,EAAE;MACjD,OAAO;QACHe,IAAI,EAAEvB,SAAS;QACfmB,QAAQ,EAAEJ,QAAQ;QAClB9J,KAAK,EAAEqJ,MAAM,CAACzI,WAAW,IAAI,EAAE;QAC/BuF,KAAK;QACLoE,OAAO,EAAE9I,kEAAgB,CAAC0H,WAAW,EAAEhD,KAAK,EAAE,IAAI,CAACf,WAAW,CAAC;QAC/DC,QAAQ,EAAEgE,MAAM,CAAChE;MACrB,CAAC;IACL,CAAC,CAAC;IACF,OAAOgF,WAAW;EACtB;EACAvB,oBAAoBA,CAACI,IAAI,EAAEC,WAAW,EAAE;IACpC,MAAMqB,cAAc,GAAGtB,IAAI,CAACrB,GAAG,CAAEwB,MAAM,IAAK;MACxC,MAAMlD,KAAK,GAAGmD,cAAc,CAACD,MAAM,CAAC;MACpC;MACA,MAAME,WAAW,GAAGC,KAAK,CAACC,IAAI,CAACJ,MAAM,CAACK,SAAS,CAAC,CAC3CC,MAAM,CAAEC,GAAG,IAAKA,GAAG,KAAK,UAAU,CAAC,CACnCC,IAAI,CAAC,GAAG,CAAC;MACd,MAAMC,QAAQ,GAAG,GAAGC,YAAY,IAAIR,WAAW,EAAE;MACjD,OAAO;QACHU,IAAI,EAAEZ,MAAM,CAACzI,WAAW,IAAI,EAAE;QAC9BsJ,QAAQ,EAAEJ,QAAQ;QAClB3D,KAAK;QACLoE,OAAO,EAAE9I,kEAAgB,CAAC0H,WAAW,EAAEhD,KAAK,EAAE,IAAI,CAACf,WAAW,CAAC;QAC/DC,QAAQ,EAAEgE,MAAM,CAAChE,QAAQ;QACzB8E,OAAO,EAAGM,QAAQ,IAAK;UACnB,IAAI,CAACnE,QAAQ,CAACmE,QAAQ,CAAC;UACvB,IAAI,CAAC,IAAI,CAAC9E,QAAQ,EAAE;YAChB,IAAI,CAAC+E,KAAK,CAAC,CAAC;UAChB;QACJ;MACJ,CAAC;IACL,CAAC,CAAC;IACF,OAAOF,cAAc;EACzB;EACMhC,WAAWA,CAACjE,EAAE,EAAE;IAAA,IAAAoG,MAAA;IAAA,OAAAjE,6KAAA;MAClB,MAAM;QAAEpB,IAAI;QAAEI;MAAe,CAAC,GAAGiF,MAAI;MACrC,MAAMnF,gBAAgB,GAAGmF,MAAI,CAACnF,gBAAgB;MAC9C,MAAMoF,IAAI,GAAGxH,6DAAU,CAACuH,MAAI,CAAC;MAC7B,MAAME,YAAY,GAAGD,IAAI,KAAK,IAAI,GAAG,KAAK,GAAG,IAAI;MACjD,MAAMjF,QAAQ,GAAGgF,MAAI,CAAChF,QAAQ;MAC9B,MAAMQ,KAAK,GAAGwE,MAAI,CAACxE,KAAK;MACxB,IAAIiB,KAAK,GAAG7C,EAAE;MACd,IAAIuG,IAAI,GAAG,MAAM;MACjB,IAAIH,MAAI,CAAChE,oBAAoB,CAAC9G,gBAAgB,CAAC,CAAC,EAAE;QAC9C,MAAMkL,IAAI,GAAGJ,MAAI,CAACjL,EAAE,CAACgF,OAAO,CAAC,UAAU,CAAC;QACxC;QACA;QACA;QACA,IAAIqG,IAAI,KAAKA,IAAI,CAACrB,SAAS,CAACsB,QAAQ,CAAC,qBAAqB,CAAC,IAAID,IAAI,CAACrB,SAAS,CAACsB,QAAQ,CAAC,oBAAoB,CAAC,CAAC,EAAE;UAC3G5D,KAAK,GAAG6D,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE3G,EAAE,CAAC,EAAE;YAAE4G,MAAM,EAAE;cAC/CC,eAAe,EAAEL;YACrB;UAAE,CAAC,CAAC;UACRD,IAAI,GAAG,OAAO;QAClB;MACJ,CAAC,MACI;QACD,MAAMO,yBAAyB,GAAG3F,cAAc,KAAK,UAAU,IAAIA,cAAc,KAAK,SAAS;QAC/F;AACZ;AACA;AACA;AACA;QACY,IAAI2F,yBAAyB,IAAKT,IAAI,KAAK,IAAI,IAAItF,IAAI,KAAKxF,SAAU,EAAE;UACpEgL,IAAI,GAAG,OAAO;UACd;AAChB;AACA;AACA;AACA;QACY,CAAC,MACI;UACD1D,KAAK,GAAG6D,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE3G,EAAE,CAAC,EAAE;YAAE4G,MAAM,EAAE;cAC/CC,eAAe,EAAET,MAAI,CAACW;YAC1B;UAAE,CAAC,CAAC;QACZ;MACJ;MACA,MAAMC,WAAW,GAAGN,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC;QAAEN,IAAI;QAClDxD,KAAK;QAAEoE,SAAS,EAAE,QAAQ;QAAEV,IAAI;QAChCD;MAAa,CAAC,EAAErF,gBAAgB,CAAC,EAAE;QAAEiG,SAAS,EAAE,oBAAoB;QAAEvB,QAAQ,EAAE,CAAC,gBAAgB,EAAE1E,gBAAgB,CAAC0E,QAAQ,CAAC;QAAEwB,cAAc,EAAE;UAC3IC,MAAM,EAAEnG,gBAAgB,CAACmG,MAAM;UAC/BC,SAAS,EAAEpG,gBAAgB,CAACoG,SAAS;UACrCC,OAAO,EAAErG,gBAAgB,CAACqG,OAAO;UACjClG,QAAQ;UACRQ,KAAK;UACL0C,OAAO,EAAE8B,MAAI,CAAC7B,oBAAoB,CAAC6B,MAAI,CAAC/C,SAAS,EAAEzB,KAAK;QAC5D;MAAE,CAAC,CAAC;MACR,OAAOhE,oDAAiB,CAAC2J,MAAM,CAACP,WAAW,CAAC;IAAC;EACjD;EACMhD,eAAeA,CAAA,EAAG;IAAA,IAAAwD,MAAA;IAAA,OAAArF,6KAAA;MACpB,MAAMkE,IAAI,GAAGxH,6DAAU,CAAC2I,MAAI,CAAC;MAC7B,MAAMvG,gBAAgB,GAAGuG,MAAI,CAACvG,gBAAgB;MAC9C,MAAMwG,eAAe,GAAGf,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC;QAAEN;MAAK,CAAC,EAAEpF,gBAAgB,CAAC,EAAE;QAAEkD,OAAO,EAAEqD,MAAI,CAACpD,wBAAwB,CAACoD,MAAI,CAACnE,SAAS,EAAEmE,MAAI,CAAC5F,KAAK,CAAC;QAAE+D,QAAQ,EAAE,CAAC,qBAAqB,EAAE1E,gBAAgB,CAAC0E,QAAQ;MAAE,CAAC,CAAC;MACtN,OAAO7H,oDAAqB,CAACyJ,MAAM,CAACE,eAAe,CAAC;IAAC;EACzD;EACMvD,SAASA,CAAA,EAAG;IAAA,IAAAwD,MAAA;IAAA,OAAAvF,6KAAA;MACd;AACR;AACA;AACA;AACA;AACA;AACA;MACQ,IAAI1G,KAAK;MACT,IAAIkM,SAAS;MACb,IAAID,MAAI,CAACtF,oBAAoB,CAAC9G,gBAAgB,CAAC,CAAC,EAAE;QAC9CG,KAAK,GAAGiM,MAAI,CAACE,QAAQ,CAAC,CAAC;QACvBD,SAAS,GAAGlM,KAAK,GAAGA,KAAK,CAACY,WAAW,GAAG,IAAI;MAChD,CAAC,MACI;QACDsL,SAAS,GAAGD,MAAI,CAACC,SAAS;MAC9B;MACA,MAAM1G,gBAAgB,GAAGyG,MAAI,CAACzG,gBAAgB;MAC9C,MAAMuD,SAAS,GAAGkD,MAAI,CAACtG,QAAQ,GAAG,UAAU,GAAG,OAAO;MACtD,MAAMiF,IAAI,GAAGxH,6DAAU,CAAC6I,MAAI,CAAC;MAC7B,MAAMG,SAAS,GAAGnB,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC;QAAEN;MAAK,CAAC,EAAEpF,gBAAgB,CAAC,EAAE;QAAEmG,MAAM,EAAEnG,gBAAgB,CAACmG,MAAM,GAAGnG,gBAAgB,CAACmG,MAAM,GAAGO,SAAS;QAAElD,MAAM,EAAEiD,MAAI,CAAChD,iBAAiB,CAACgD,MAAI,CAACrE,SAAS,EAAEmB,SAAS,EAAEkD,MAAI,CAAC9F,KAAK,CAAC;QAAEuC,OAAO,EAAE,CAC5N;UACIuB,IAAI,EAAEgC,MAAI,CAAC/G,UAAU;UACrB8E,IAAI,EAAE,QAAQ;UACdG,OAAO,EAAEA,CAAA,KAAM;YACX8B,MAAI,CAACpI,SAAS,CAACkB,IAAI,CAAC,CAAC;UACzB;QACJ,CAAC,EACD;UACIkF,IAAI,EAAEgC,MAAI,CAACpG,MAAM;UACjBsE,OAAO,EAAGkC,cAAc,IAAK;YACzBJ,MAAI,CAAC3F,QAAQ,CAAC+F,cAAc,CAAC;UACjC;QACJ,CAAC,CACJ;QAAEnC,QAAQ,EAAE,CACT,cAAc,EACd1E,gBAAgB,CAAC0E,QAAQ,EACzB+B,MAAI,CAACtG,QAAQ,GAAG,uBAAuB,GAAG,qBAAqB;MACjE,CAAC,CAAC;MACR,OAAOpD,oDAAe,CAACuJ,MAAM,CAACM,SAAS,CAAC;IAAC;EAC7C;EACA;AACJ;AACA;EACI1B,KAAKA,CAAA,EAAG;IACJ,IAAI,CAAC,IAAI,CAACpD,OAAO,EAAE;MACf,OAAOgF,OAAO,CAACC,OAAO,CAAC,KAAK,CAAC;IACjC;IACA,OAAO,IAAI,CAACjF,OAAO,CAACkF,OAAO,CAAC,CAAC;EACjC;EACA;EACAL,QAAQA,CAAA,EAAG;IACP,OAAO3M,uDAAa,CAAC,IAAI,CAACE,EAAE,CAAC;EACjC;EACA+M,QAAQA,CAAA,EAAG;IACP,OAAO,IAAI,CAACC,OAAO,CAAC,CAAC,KAAK,EAAE;EAChC;EACA,IAAI9E,SAASA,CAAA,EAAG;IACZ,OAAO4B,KAAK,CAACC,IAAI,CAAC,IAAI,CAAC/J,EAAE,CAACiN,gBAAgB,CAAC,mBAAmB,CAAC,CAAC;EACpE;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACI,IAAIT,SAASA,CAAA,EAAG;IACZ,MAAM;MAAElM;IAAM,CAAC,GAAG,IAAI;IACtB,IAAIA,KAAK,KAAKF,SAAS,EAAE;MACrB,OAAOE,KAAK;IAChB;IACA,MAAM;MAAE8G;IAAU,CAAC,GAAG,IAAI;IAC1B,IAAIA,SAAS,KAAK,IAAI,EAAE;MACpB,OAAOA,SAAS,CAAClG,WAAW;IAChC;IACA;EACJ;EACA8L,OAAOA,CAAA,EAAG;IACN,MAAM3G,YAAY,GAAG,IAAI,CAACA,YAAY;IACtC,IAAIA,YAAY,IAAI,IAAI,IAAIA,YAAY,KAAK,EAAE,EAAE;MAC7C,OAAOA,YAAY;IACvB;IACA,OAAO6G,YAAY,CAAC,IAAI,CAAChF,SAAS,EAAE,IAAI,CAACzB,KAAK,EAAE,IAAI,CAACf,WAAW,CAAC;EACrE;EACAT,QAAQA,CAAA,EAAG;IACP,IAAI,IAAI,CAACkI,OAAO,EAAE;MACd,IAAI,CAACA,OAAO,CAAC3E,KAAK,CAAC,CAAC;IACxB;EACJ;EACA7B,SAASA,CAAA,EAAG;IACR,MAAM;MAAEhB;IAAS,CAAC,GAAG,IAAI;IACzB,MAAMyH,KAAK,GAAG;MACV,sBAAsB,EAAEzH;IAC5B,CAAC;IACD,IAAI,IAAI,CAACsB,oBAAoB,CAAC9G,gBAAgB,CAAC,CAAC,EAAE;MAC9CiN,KAAK,CAAC,aAAa,CAAC,GAAG,IAAI;MAC3BA,KAAK,CAAC,QAAQ,CAAC,GAAG,IAAI;MACtBA,KAAK,CAAC,iBAAiB,CAAC,GAAGzH,QAAQ;MACnCyH,KAAK,CAAC,iBAAiB,CAAC,GAAG,IAAI,CAAChH,WAAW,KAAKhG,SAAS;MACzDgN,KAAK,CAAC,WAAW,CAAC,GAAG,IAAI,CAACL,QAAQ,CAAC,CAAC;MACpCK,KAAK,CAAC,WAAW,CAAC,GAAG,IAAI,CAAC7H,UAAU;MACpC;MACA6H,KAAK,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,IAAI,CAACxM,MAAM;IACnC;IACA,IAAI,CAAC2D,QAAQ,CAACc,IAAI,CAAC+H,KAAK,CAAC;EAC7B;EACAC,WAAWA,CAAA,EAAG;IACV,MAAM;MAAE/M;IAAM,CAAC,GAAG,IAAI;IACtB,OAAQT,qDAAC,CAAC,KAAK,EAAE;MAAEyN,KAAK,EAAE;QAClB,oBAAoB,EAAE,IAAI;QAC1B,2BAA2B,EAAE,CAAC,IAAI,CAACC;MACvC,CAAC;MAAEC,IAAI,EAAE;IAAQ,CAAC,EAAElN,KAAK,KAAKF,SAAS,GAAGP,qDAAC,CAAC,MAAM,EAAE;MAAEqG,IAAI,EAAE;IAAQ,CAAC,CAAC,GAAGrG,qDAAC,CAAC,KAAK,EAAE;MAAEyN,KAAK,EAAE;IAAa,CAAC,EAAEhN,KAAK,CAAC,CAAC;EAC1H;EACAmN,kBAAkBA,CAAA,EAAG;IACjB,IAAIC,EAAE;IACN,CAACA,EAAE,GAAG,IAAI,CAACxG,eAAe,MAAM,IAAI,IAAIwG,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACC,mBAAmB,CAAC,CAAC;EAC7F;EACA;AACJ;AACA;AACA;EACI,IAAIvG,SAASA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACpH,EAAE,CAACgB,aAAa,CAAC,gBAAgB,CAAC;EAClD;EACA;AACJ;AACA;AACA;AACA;AACA;EACI,IAAIuM,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACjN,KAAK,KAAKF,SAAS,IAAI,IAAI,CAACgH,SAAS,KAAK,IAAI;EAC9D;EACA;AACJ;AACA;AACA;EACIwG,oBAAoBA,CAAA,EAAG;IACnB,MAAM1C,IAAI,GAAGxH,6DAAU,CAAC,IAAI,CAAC;IAC7B,MAAMmK,cAAc,GAAG3C,IAAI,KAAK,IAAI,IAAI,IAAI,CAACtF,IAAI,KAAK,SAAS;IAC/D,IAAIiI,cAAc,EAAE;MAChB;AACZ;AACA;AACA;AACA;AACA;AACA;MACY,OAAO,CACHhO,qDAAC,CAAC,KAAK,EAAE;QAAEyN,KAAK,EAAE;MAA2B,CAAC,EAAEzN,qDAAC,CAAC,KAAK,EAAE;QAAEyN,KAAK,EAAE;MAAuB,CAAC,CAAC,EAAEzN,qDAAC,CAAC,KAAK,EAAE;QAAEyN,KAAK,EAAE;UACvG,sBAAsB,EAAE,IAAI;UAC5B,6BAA6B,EAAE,CAAC,IAAI,CAACC;QACzC;MAAE,CAAC,EAAE1N,qDAAC,CAAC,KAAK,EAAE;QAAEyN,KAAK,EAAE,cAAc;QAAE,aAAa,EAAE,MAAM;QAAEQ,GAAG,EAAG9N,EAAE,IAAM,IAAI,CAACmH,aAAa,GAAGnH;MAAI,CAAC,EAAE,IAAI,CAACM,KAAK,CAAC,CAAC,EAAET,qDAAC,CAAC,KAAK,EAAE;QAAEyN,KAAK,EAAE;MAAqB,CAAC,CAAC,CAAC,EACpK,IAAI,CAACD,WAAW,CAAC,CAAC,CACrB;IACL;IACA;AACR;AACA;AACA;IACQ,OAAO,IAAI,CAACA,WAAW,CAAC,CAAC;EAC7B;EACAU,YAAYA,CAAA,EAAG;IACX,MAAM;MAAEpI,QAAQ;MAAE3F,EAAE;MAAEuF,UAAU;MAAEgB,YAAY;MAAEP,cAAc;MAAED,OAAO;MAAEK,WAAW;MAAER,IAAI;MAAEY,KAAK;MAAEN,IAAI;MAAEO;IAAM,CAAC,GAAG,IAAI;IACvH,MAAMyE,IAAI,GAAGxH,6DAAU,CAAC,IAAI,CAAC;IAC7B,MAAMiI,yBAAyB,GAAG3F,cAAc,KAAK,UAAU,IAAIA,cAAc,KAAK,SAAS;IAC/F,MAAMgI,cAAc,GAAG,CAACrC,yBAAyB;IACjD,MAAMsC,GAAG,GAAGjL,mDAAK,CAAChD,EAAE,CAAC,GAAG,KAAK,GAAG,KAAK;IACrC,MAAMkO,MAAM,GAAGjL,qDAAW,CAAC,UAAU,EAAE,IAAI,CAACjD,EAAE,CAAC;IAC/C,MAAMmO,qBAAqB,GAAGjD,IAAI,KAAK,IAAI,IAAItF,IAAI,KAAK,SAAS,IAAI,CAACsI,MAAM;IAC5E,MAAMnB,QAAQ,GAAG,IAAI,CAACA,QAAQ,CAAC,CAAC;IAChC,MAAMqB,gBAAgB,GAAGpO,EAAE,CAACgB,aAAa,CAAC,8BAA8B,CAAC,KAAK,IAAI;IAClFoB,uDAAiB,CAAC,IAAI,EAAEpC,EAAE,EAAEkG,IAAI,EAAEmI,UAAU,CAAC5H,KAAK,CAAC,EAAEd,QAAQ,CAAC;IAC9D;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,MAAM2I,gBAAgB,GAAGtI,cAAc,KAAK,SAAS,IAAKA,cAAc,KAAK,UAAU,KAAK+G,QAAQ,IAAIxH,UAAU,IAAI6I,gBAAgB,CAAE;IACxI,OAAQvO,qDAAC,CAAC4B,iDAAI,EAAE;MAAEmD,OAAO,EAAE,IAAI,CAACA,OAAO;MAAE0I,KAAK,EAAEpK,qDAAkB,CAAC,IAAI,CAACuC,KAAK,EAAE;QACvE,CAACyF,IAAI,GAAG,IAAI;QACZ,SAAS,EAAEgD,MAAM;QACjB,eAAe,EAAEjL,qDAAW,CAAC,oBAAoB,EAAEjD,EAAE,CAAC;QACtD,iBAAiB,EAAE2F,QAAQ;QAC3B,iBAAiB,EAAEJ,UAAU;QAC7B,mBAAmB,EAAEgB,YAAY,KAAKnG,SAAS;QAC/C,WAAW,EAAE2M,QAAQ;QACrB,gBAAgB,EAAEuB,gBAAgB;QAClC,iBAAiB,EAAElI,WAAW,KAAKhG,SAAS;QAC5C,eAAe,EAAE,IAAI;QACrB,CAAC,UAAU6N,GAAG,EAAE,GAAG,IAAI;QACvB,CAAC,eAAerI,IAAI,EAAE,GAAGA,IAAI,KAAKxF,SAAS;QAC3C,CAAC,kBAAkB2F,OAAO,EAAE,GAAGiI,cAAc;QAC7C,CAAC,gBAAgBxH,KAAK,EAAE,GAAGA,KAAK,KAAKpG,SAAS;QAC9C,CAAC,0BAA0B4F,cAAc,EAAE,GAAG;MAClD,CAAC;IAAE,CAAC,EAAEnG,qDAAC,CAAC,OAAO,EAAE;MAAEyN,KAAK,EAAE,gBAAgB;MAAEiB,EAAE,EAAE;IAAe,CAAC,EAAE,IAAI,CAACX,oBAAoB,CAAC,CAAC,EAAE/N,qDAAC,CAAC,KAAK,EAAE;MAAEyN,KAAK,EAAE;IAAuB,CAAC,EAAEzN,qDAAC,CAAC,MAAM,EAAE;MAAEqG,IAAI,EAAE;IAAQ,CAAC,CAAC,EAAErG,qDAAC,CAAC,KAAK,EAAE;MAAEyN,KAAK,EAAE,gBAAgB;MAAEQ,GAAG,EAAG9N,EAAE,IAAM,IAAI,CAAC4L,eAAe,GAAG5L,EAAG;MAAEwN,IAAI,EAAE;IAAY,CAAC,EAAE,IAAI,CAACgB,gBAAgB,CAAC,CAAC,EAAE,IAAI,CAACC,aAAa,CAAC,CAAC,CAAC,EAAE5O,qDAAC,CAAC,MAAM,EAAE;MAAEqG,IAAI,EAAE;IAAM,CAAC,CAAC,EAAE,CAACyF,yBAAyB,IAAI,IAAI,CAAC+C,gBAAgB,CAAC,CAAC,CAAC,EAAE/C,yBAAyB,IAAI,IAAI,CAAC+C,gBAAgB,CAAC,CAAC,EAAEP,qBAAqB,IAAItO,qDAAC,CAAC,KAAK,EAAE;MAAEyN,KAAK,EAAE;IAAmB,CAAC,CAAC,CAAC,CAAC;EAChhB;EACA;EACAqB,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAAC,IAAI,CAAChK,2BAA2B,EAAE;MACnCnC,qDAAe,CAAC;AAC5B;AACA;AACA;AACA;AACA,gNAAgN,EAAE,IAAI,CAACxC,EAAE,CAAC;MAC9M,IAAI,IAAI,CAACY,MAAM,EAAE;QACb4B,qDAAe,CAAC;AAChC,0HAA0H,EAAE,IAAI,CAACxC,EAAE,CAAC;MACxH;MACA,IAAI,CAAC2E,2BAA2B,GAAG,IAAI;IAC3C;IACA,MAAM;MAAEgB,QAAQ;MAAE3F,EAAE;MAAEwE,OAAO;MAAEe,UAAU;MAAEgB,YAAY;MAAEL,IAAI;MAAEE,WAAW;MAAEK;IAAM,CAAC,GAAG,IAAI;IAC1F,MAAMyE,IAAI,GAAGxH,6DAAU,CAAC,IAAI,CAAC;IAC7B,MAAM;MAAE8I,SAAS;MAAEoC;IAAQ,CAAC,GAAGtM,uDAAY,CAACtC,EAAE,EAAEwE,OAAO,CAAC;IACxDpC,uDAAiB,CAAC,IAAI,EAAEpC,EAAE,EAAEkG,IAAI,EAAEmI,UAAU,CAAC5H,KAAK,CAAC,EAAEd,QAAQ,CAAC;IAC9D,MAAMkJ,YAAY,GAAG,IAAI,CAAC7B,OAAO,CAAC,CAAC;IACnC,IAAI8B,UAAU,GAAGD,YAAY;IAC7B,IAAIC,UAAU,KAAK,EAAE,IAAI1I,WAAW,KAAKhG,SAAS,EAAE;MAChD0O,UAAU,GAAG1I,WAAW;IAC5B;IACA;IACA;IACA;IACA;IACA,MAAM2I,YAAY,GAAGvC,SAAS,KAAKpM,SAAS,GAAI0O,UAAU,KAAK,EAAE,GAAG,GAAGA,UAAU,KAAKtC,SAAS,EAAE,GAAGA,SAAS,GAAIsC,UAAU;IAC3H,OAAQjP,qDAAC,CAAC4B,iDAAI,EAAE;MAAEmD,OAAO,EAAE,IAAI,CAACA,OAAO;MAAE0F,IAAI,EAAE,QAAQ;MAAE,eAAe,EAAE,SAAS;MAAE,eAAe,EAAE3E,QAAQ,GAAG,MAAM,GAAG,IAAI;MAAE,YAAY,EAAEoJ,YAAY;MAAEzB,KAAK,EAAE;QAC3J,CAACpC,IAAI,GAAG,IAAI;QACZ,SAAS,EAAEjI,qDAAW,CAAC,UAAU,EAAEjD,EAAE,CAAC;QACtC,eAAe,EAAEiD,qDAAW,CAAC,oBAAoB,EAAEjD,EAAE,CAAC;QACtD,iBAAiB,EAAE2F,QAAQ;QAC3B,iBAAiB,EAAEJ,UAAU;QAC7B,mBAAmB,EAAEgB,YAAY,KAAKnG,SAAS;QAC/C,eAAe,EAAE;MACrB;IAAE,CAAC,EAAE,IAAI,CAACoO,gBAAgB,CAAC,CAAC,EAAE,IAAI,CAACE,gBAAgB,CAAC,CAAC,EAAE7O,qDAAC,CAAC,OAAO,EAAE;MAAE0O,EAAE,EAAEK;IAAQ,CAAC,EAAEG,YAAY,CAAC,EAAE,IAAI,CAACN,aAAa,CAAC,CAAC,CAAC;EAC/H;EACA;AACJ;AACA;AACA;AACA;EACID,gBAAgBA,CAAA,EAAG;IACf,MAAM;MAAEpI;IAAY,CAAC,GAAG,IAAI;IAC5B,MAAMyI,YAAY,GAAG,IAAI,CAAC7B,OAAO,CAAC,CAAC;IACnC,IAAIgC,mBAAmB,GAAG,KAAK;IAC/B,IAAIF,UAAU,GAAGD,YAAY;IAC7B,IAAIC,UAAU,KAAK,EAAE,IAAI1I,WAAW,KAAKhG,SAAS,EAAE;MAChD0O,UAAU,GAAG1I,WAAW;MACxB4I,mBAAmB,GAAG,IAAI;IAC9B;IACA,MAAMC,iBAAiB,GAAG;MACtB,aAAa,EAAE,IAAI;MACnB,oBAAoB,EAAED;IAC1B,CAAC;IACD,MAAME,QAAQ,GAAGF,mBAAmB,GAAG,aAAa,GAAG,MAAM;IAC7D,OAAQnP,qDAAC,CAAC,KAAK,EAAE;MAAE,aAAa,EAAE,MAAM;MAAEyN,KAAK,EAAE2B,iBAAiB;MAAEzB,IAAI,EAAE0B;IAAS,CAAC,EAAEJ,UAAU,CAAC;EACrG;EACA;AACJ;AACA;AACA;EACIJ,gBAAgBA,CAAA,EAAG;IACf,MAAMxD,IAAI,GAAGxH,6DAAU,CAAC,IAAI,CAAC;IAC7B,MAAM;MAAE6B,UAAU;MAAEe,UAAU;MAAEC;IAAa,CAAC,GAAG,IAAI;IACrD,IAAI4I,IAAI;IACR,IAAI5J,UAAU,IAAIgB,YAAY,KAAKnG,SAAS,EAAE;MAC1C+O,IAAI,GAAG5I,YAAY;IACvB,CAAC,MACI;MACD,MAAM6I,WAAW,GAAGlE,IAAI,KAAK,KAAK,GAAG3H,kDAAa,GAAGE,kDAAc;MACnE0L,IAAI,GAAG7I,UAAU,KAAK,IAAI,IAAIA,UAAU,KAAK,KAAK,CAAC,GAAGA,UAAU,GAAG8I,WAAW;IAClF;IACA,OAAOvP,qDAAC,CAAC,UAAU,EAAE;MAAEyN,KAAK,EAAE,aAAa;MAAEE,IAAI,EAAE,MAAM;MAAE,aAAa,EAAE,MAAM;MAAE2B,IAAI,EAAEA;IAAK,CAAC,CAAC;EACnG;EACA,IAAIE,SAASA,CAAA,EAAG;IACZ,IAAI3B,EAAE,EAAE4B,EAAE;IACV,MAAM;MAAElJ,WAAW;MAAEpG,EAAE;MAAEwE,OAAO;MAAEE;IAAoB,CAAC,GAAG,IAAI;IAC9D,MAAMmK,YAAY,GAAG,IAAI,CAAC7B,OAAO,CAAC,CAAC;IACnC,MAAM;MAAER;IAAU,CAAC,GAAGlK,uDAAY,CAACtC,EAAE,EAAEwE,OAAO,CAAC;IAC/C,MAAM+K,YAAY,GAAG,CAACD,EAAE,GAAG,CAAC5B,EAAE,GAAG,IAAI,CAAClB,SAAS,MAAM,IAAI,IAAIkB,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAGhJ,mBAAmB,CAAC,YAAY,CAAC,MAAM,IAAI,IAAI4K,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG9C,SAAS;IAC/J;AACR;AACA;AACA;AACA;IACQ,IAAIgD,aAAa,GAAGX,YAAY;IAChC,IAAIW,aAAa,KAAK,EAAE,IAAIpJ,WAAW,KAAKhG,SAAS,EAAE;MACnDoP,aAAa,GAAGpJ,WAAW;IAC/B;IACA;AACR;AACA;AACA;AACA;AACA;AACA;IACQ,IAAImJ,YAAY,KAAKnP,SAAS,EAAE;MAC5BoP,aAAa,GAAGA,aAAa,KAAK,EAAE,GAAGD,YAAY,GAAG,GAAGA,YAAY,KAAKC,aAAa,EAAE;IAC7F;IACA,OAAOA,aAAa;EACxB;EACAf,aAAaA,CAAA,EAAG;IACZ,MAAM;MAAE9I,QAAQ;MAAEnB,OAAO;MAAEe;IAAW,CAAC,GAAG,IAAI;IAC9C,OAAQ1F,qDAAC,CAAC,QAAQ,EAAE;MAAE8F,QAAQ,EAAEA,QAAQ;MAAE4I,EAAE,EAAE/J,OAAO;MAAE,YAAY,EAAE,IAAI,CAAC6K,SAAS;MAAE,eAAe,EAAE,QAAQ;MAAE,eAAe,EAAE,GAAG9J,UAAU,EAAE;MAAEH,OAAO,EAAE,IAAI,CAACA,OAAO;MAAEE,MAAM,EAAE,IAAI,CAACA,MAAM;MAAEwI,GAAG,EAAGX,OAAO,IAAM,IAAI,CAACA,OAAO,GAAGA;IAAS,CAAC,CAAC;EAC/O;EACAsC,MAAMA,CAAA,EAAG;IACL,MAAM;MAAExI;IAAqB,CAAC,GAAG,IAAI;IACrC,OAAOA,oBAAoB,CAAC9G,gBAAgB,CAAC,CAAC,GAAG,IAAI,CAACwO,kBAAkB,CAAC,CAAC,GAAG,IAAI,CAACZ,YAAY,CAAC,CAAC;EACpG;EACA,IAAI/N,EAAEA,CAAA,EAAG;IAAE,OAAO2B,qDAAU,CAAC,IAAI,CAAC;EAAE;EACpC,WAAW+N,QAAQA,CAAA,EAAG;IAAE,OAAO;MAC3B,UAAU,EAAE,CAAC,cAAc,CAAC;MAC5B,YAAY,EAAE,CAAC,cAAc,CAAC;MAC9B,aAAa,EAAE,CAAC,cAAc,CAAC;MAC/B,OAAO,EAAE,CAAC,cAAc;IAC5B,CAAC;EAAE;AACP,CAAC;AACD,MAAM9F,cAAc,GAAI5J,EAAE,IAAK;EAC3B,MAAMyG,KAAK,GAAGzG,EAAE,CAACyG,KAAK;EACtB,OAAOA,KAAK,KAAKrG,SAAS,GAAGJ,EAAE,CAACkB,WAAW,IAAI,EAAE,GAAGuF,KAAK;AAC7D,CAAC;AACD,MAAM4H,UAAU,GAAI5H,KAAK,IAAK;EAC1B,IAAIA,KAAK,IAAI,IAAI,EAAE;IACf,OAAOrG,SAAS;EACpB;EACA,IAAI0J,KAAK,CAAC6F,OAAO,CAAClJ,KAAK,CAAC,EAAE;IACtB,OAAOA,KAAK,CAAC0D,IAAI,CAAC,GAAG,CAAC;EAC1B;EACA,OAAO1D,KAAK,CAACmJ,QAAQ,CAAC,CAAC;AAC3B,CAAC;AACD,MAAM1C,YAAY,GAAGA,CAAC2C,IAAI,EAAEpJ,KAAK,EAAEf,WAAW,KAAK;EAC/C,IAAIe,KAAK,KAAKrG,SAAS,EAAE;IACrB,OAAO,EAAE;EACb;EACA,IAAI0J,KAAK,CAAC6F,OAAO,CAAClJ,KAAK,CAAC,EAAE;IACtB,OAAOA,KAAK,CACP0B,GAAG,CAAE2H,CAAC,IAAKC,YAAY,CAACF,IAAI,EAAEC,CAAC,EAAEpK,WAAW,CAAC,CAAC,CAC9CuE,MAAM,CAAE+F,GAAG,IAAKA,GAAG,KAAK,IAAI,CAAC,CAC7B7F,IAAI,CAAC,IAAI,CAAC;EACnB,CAAC,MACI;IACD,OAAO4F,YAAY,CAACF,IAAI,EAAEpJ,KAAK,EAAEf,WAAW,CAAC,IAAI,EAAE;EACvD;AACJ,CAAC;AACD,MAAMqK,YAAY,GAAGA,CAACF,IAAI,EAAEpJ,KAAK,EAAEf,WAAW,KAAK;EAC/C,MAAMuK,SAAS,GAAGJ,IAAI,CAACK,IAAI,CAAEF,GAAG,IAAK;IACjC,OAAOhO,kEAAc,CAACyE,KAAK,EAAEmD,cAAc,CAACoG,GAAG,CAAC,EAAEtK,WAAW,CAAC;EAClE,CAAC,CAAC;EACF,OAAOuK,SAAS,GAAGA,SAAS,CAAC/O,WAAW,GAAG,IAAI;AACnD,CAAC;AACD,IAAIuD,SAAS,GAAG,CAAC;AACjB,MAAM4F,YAAY,GAAG,yBAAyB;AAC9CtG,MAAM,CAACqJ,KAAK,GAAG;EACX+C,GAAG,EAAEvM,kBAAkB;EACvBwM,EAAE,EAAEtM;AACR,CAAC;AAED,MAAMuM,eAAe,GAAG,qBAAqB;AAC7C,MAAMC,qBAAqB,GAAGD,eAAe;AAE7C,MAAME,YAAY,GAAG,MAAM;EACvBvM,WAAWA,CAACC,OAAO,EAAE;IACjB5C,qDAAgB,CAAC,IAAI,EAAE4C,OAAO,CAAC;IAC/B,IAAI,CAACO,OAAO,GAAG,cAAcgM,eAAe,EAAE,EAAE;IAChD,IAAI,CAAC7K,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACc,KAAK,GAAGrG,SAAS;EAC1B;EACAqP,MAAMA,CAAA,EAAG;IACL,OAAO5P,qDAAC,CAAC4B,iDAAI,EAAE;MAAEgP,GAAG,EAAE,0CAA0C;MAAEnG,IAAI,EAAE,QAAQ;MAAEiE,EAAE,EAAE,IAAI,CAAC/J,OAAO;MAAE8I,KAAK,EAAE5J,6DAAU,CAAC,IAAI;IAAE,CAAC,CAAC;EAClI;EACA,IAAI1D,EAAEA,CAAA,EAAG;IAAE,OAAO2B,qDAAU,CAAC,IAAI,CAAC;EAAE;AACxC,CAAC;AACD,IAAI6O,eAAe,GAAG,CAAC;AACvBD,YAAY,CAACnD,KAAK,GAAGkD,qBAAqB;AAE1C,MAAMI,mBAAmB,GAAG,iTAAiT;AAC7U,MAAMC,yBAAyB,GAAGD,mBAAmB;AAErD,MAAME,kBAAkB,GAAG,qhCAAqhC;AAChjC,MAAMC,wBAAwB,GAAGD,kBAAkB;AAEnD,MAAME,aAAa,GAAG,MAAM;EACxB9M,WAAWA,CAACC,OAAO,EAAE;IACjB5C,qDAAgB,CAAC,IAAI,EAAE4C,OAAO,CAAC;IAC/B,IAAI,CAACgI,MAAM,GAAG7L,SAAS;IACvB,IAAI,CAAC8L,SAAS,GAAG9L,SAAS;IAC1B,IAAI,CAAC+L,OAAO,GAAG/L,SAAS;IACxB,IAAI,CAAC6F,QAAQ,GAAG7F,SAAS;IACzB,IAAI,CAAC+I,OAAO,GAAG,EAAE;EACrB;EACA4H,mBAAmBA,CAAClM,EAAE,EAAE;IACpB,MAAM;MAAEsE;IAAQ,CAAC,GAAG,IAAI;IACxB,OAAOA,OAAO,CAAC+G,IAAI,CAAE9H,CAAC,IAAKA,CAAC,CAAC3B,KAAK,KAAK5B,EAAE,CAACC,MAAM,CAAC2B,KAAK,CAAC;EAC3D;EACA;AACJ;AACA;AACA;AACA;EACIuK,iBAAiBA,CAACnM,EAAE,EAAE;IAClB,MAAM8E,MAAM,GAAG,IAAI,CAACoH,mBAAmB,CAAClM,EAAE,CAAC;IAC3C,MAAMoM,MAAM,GAAG,IAAI,CAACC,SAAS,CAACrM,EAAE,CAAC;IACjC,IAAI8E,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACc,OAAO,EAAE;MAChE1H,wDAAQ,CAAC4G,MAAM,CAACc,OAAO,EAAEwG,MAAM,CAAC;IACpC;EACJ;EACA;AACJ;AACA;AACA;EACIE,oBAAoBA,CAAA,EAAG;IACnB,MAAMjI,OAAO,GAAG,IAAI,CAAClJ,EAAE,CAACgF,OAAO,CAAC,aAAa,CAAC;IAC9C,IAAIkE,OAAO,EAAE;MACTA,OAAO,CAAC4D,OAAO,CAAC,CAAC;IACrB;EACJ;EACAsE,UAAUA,CAACvM,EAAE,EAAE;IACX,MAAM;MAAEoB;IAAS,CAAC,GAAG,IAAI;IACzB,MAAM0D,MAAM,GAAG,IAAI,CAACoH,mBAAmB,CAAClM,EAAE,CAAC;IAC3C;IACA;IACA,IAAIoB,QAAQ,IAAI0D,MAAM,EAAE;MACpBA,MAAM,CAACkB,OAAO,GAAGhG,EAAE,CAAC4G,MAAM,CAACZ,OAAO;IACtC;EACJ;EACAqG,SAASA,CAACrM,EAAE,EAAE;IACV,MAAM;MAAEoB,QAAQ;MAAEkD;IAAQ,CAAC,GAAG,IAAI;IAClC,IAAIlD,QAAQ,EAAE;MACV;MACA;MACA,OAAOkD,OAAO,CAACc,MAAM,CAAE7B,CAAC,IAAKA,CAAC,CAACyC,OAAO,CAAC,CAAC1C,GAAG,CAAEC,CAAC,IAAKA,CAAC,CAAC3B,KAAK,CAAC;IAC/D;IACA;IACA;IACA,MAAMkD,MAAM,GAAG,IAAI,CAACoH,mBAAmB,CAAClM,EAAE,CAAC;IAC3C,OAAO8E,MAAM,GAAGA,MAAM,CAAClD,KAAK,GAAGrG,SAAS;EAC5C;EACAiR,aAAaA,CAAClI,OAAO,EAAE;IACnB,MAAM;MAAElD;IAAS,CAAC,GAAG,IAAI;IACzB,QAAQA,QAAQ;MACZ,KAAK,IAAI;QACL,OAAO,IAAI,CAACqL,qBAAqB,CAACnI,OAAO,CAAC;MAC9C;QACI,OAAO,IAAI,CAACoI,kBAAkB,CAACpI,OAAO,CAAC;IAC/C;EACJ;EACAmI,qBAAqBA,CAACnI,OAAO,EAAE;IAC3B,OAAOA,OAAO,CAAChB,GAAG,CAAEwB,MAAM,IAAM9J,qDAAC,CAAC,UAAU,EAAE;MAAEyN,KAAK,EAAE/B,MAAM,CAACC,MAAM,CAAC;QAC7D;QACA,uBAAuB,EAAE7B,MAAM,CAACkB;MACpC,CAAC,EAAEzH,qDAAW,CAACuG,MAAM,CAACa,QAAQ,CAAC;IAAE,CAAC,EAAE3K,qDAAC,CAAC,cAAc,EAAE;MAAE4G,KAAK,EAAEkD,MAAM,CAAClD,KAAK;MAAEd,QAAQ,EAAEgE,MAAM,CAAChE,QAAQ;MAAEkF,OAAO,EAAElB,MAAM,CAACkB,OAAO;MAAE9E,OAAO,EAAE,OAAO;MAAEC,cAAc,EAAE,KAAK;MAAEwL,WAAW,EAAG3M,EAAE,IAAK;QAC3L,IAAI,CAACuM,UAAU,CAACvM,EAAE,CAAC;QACnB,IAAI,CAACmM,iBAAiB,CAACnM,EAAE,CAAC;QAC1B;QACAhD,qDAAW,CAAC,IAAI,CAAC;MACrB;IAAE,CAAC,EAAE8H,MAAM,CAACY,IAAI,CAAC,CAAE,CAAC;EAC5B;EACAgH,kBAAkBA,CAACpI,OAAO,EAAE;IACxB,MAAM0B,OAAO,GAAG1B,OAAO,CAACc,MAAM,CAAE7B,CAAC,IAAKA,CAAC,CAACyC,OAAO,CAAC,CAAC1C,GAAG,CAAEC,CAAC,IAAKA,CAAC,CAAC3B,KAAK,CAAC,CAAC,CAAC,CAAC;IACvE,OAAQ5G,qDAAC,CAAC,iBAAiB,EAAE;MAAE4G,KAAK,EAAEoE,OAAO;MAAE2G,WAAW,EAAG3M,EAAE,IAAK,IAAI,CAACmM,iBAAiB,CAACnM,EAAE;IAAE,CAAC,EAAEsE,OAAO,CAAChB,GAAG,CAAEwB,MAAM,IAAM9J,qDAAC,CAAC,UAAU,EAAE;MAAEyN,KAAK,EAAE/B,MAAM,CAACC,MAAM,CAAC;QACxJ;QACA,oBAAoB,EAAE7B,MAAM,CAAClD,KAAK,KAAKoE;MAC3C,CAAC,EAAEzH,qDAAW,CAACuG,MAAM,CAACa,QAAQ,CAAC;IAAE,CAAC,EAAE3K,qDAAC,CAAC,WAAW,EAAE;MAAE4G,KAAK,EAAEkD,MAAM,CAAClD,KAAK;MAAEd,QAAQ,EAAEgE,MAAM,CAAChE,QAAQ;MAAEf,OAAO,EAAEA,CAAA,KAAM,IAAI,CAACuM,oBAAoB,CAAC,CAAC;MAAEM,OAAO,EAAG5M,EAAE,IAAK;QAC9J,IAAIA,EAAE,CAAC4L,GAAG,KAAK,GAAG,EAAE;UAChB;AACpB;AACA;AACA;AACA;UACoB,IAAI,CAACU,oBAAoB,CAAC,CAAC;QAC/B;MACJ;IAAE,CAAC,EAAExH,MAAM,CAACY,IAAI,CAAC,CAAE,CAAC,CAAC;EAC7B;EACAkF,MAAMA,CAAA,EAAG;IACL,MAAM;MAAExD,MAAM;MAAEE,OAAO;MAAEhD,OAAO;MAAE+C;IAAU,CAAC,GAAG,IAAI;IACpD,MAAMwF,qBAAqB,GAAGxF,SAAS,KAAK9L,SAAS,IAAI+L,OAAO,KAAK/L,SAAS;IAC9E,OAAQP,qDAAC,CAAC4B,iDAAI,EAAE;MAAEgP,GAAG,EAAE,0CAA0C;MAAEnD,KAAK,EAAE5J,6DAAU,CAAC,IAAI;IAAE,CAAC,EAAE7D,qDAAC,CAAC,UAAU,EAAE;MAAE4Q,GAAG,EAAE;IAA2C,CAAC,EAAExE,MAAM,KAAK7L,SAAS,IAAIP,qDAAC,CAAC,iBAAiB,EAAE;MAAE4Q,GAAG,EAAE;IAA2C,CAAC,EAAExE,MAAM,CAAC,EAAEyF,qBAAqB,IAAK7R,qDAAC,CAAC,UAAU,EAAE;MAAE4Q,GAAG,EAAE;IAA2C,CAAC,EAAE5Q,qDAAC,CAAC,WAAW,EAAE;MAAE4Q,GAAG,EAAE,0CAA0C;MAAEnD,KAAK,EAAE;IAAgB,CAAC,EAAEpB,SAAS,KAAK9L,SAAS,IAAIP,qDAAC,CAAC,IAAI,EAAE;MAAE4Q,GAAG,EAAE;IAA2C,CAAC,EAAEvE,SAAS,CAAC,EAAEC,OAAO,KAAK/L,SAAS,IAAIP,qDAAC,CAAC,GAAG,EAAE;MAAE4Q,GAAG,EAAE;IAA2C,CAAC,EAAEtE,OAAO,CAAC,CAAC,CAAE,EAAE,IAAI,CAACkF,aAAa,CAAClI,OAAO,CAAC,CAAC,CAAC;EAC5qB;EACA,IAAInJ,EAAEA,CAAA,EAAG;IAAE,OAAO2B,qDAAU,CAAC,IAAI,CAAC;EAAE;AACxC,CAAC;AACDmP,aAAa,CAAC1D,KAAK,GAAG;EAClB+C,GAAG,EAAEQ,yBAAyB;EAC9BP,EAAE,EAAES;AACR,CAAC;;;;;;;;;;;;;;;;;ACn2BD;AACA;AACA;AAC+C;AACE;;AAEjD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM/O,qBAAqB,GAAGA,CAAC9B,EAAE,EAAEgS,gBAAgB,EAAEC,YAAY,KAAK;EAClE,IAAIC,iBAAiB;EACrB,MAAMC,uBAAuB,GAAGA,CAAA,KAAM;IAClC,MAAMhL,aAAa,GAAG6K,gBAAgB,CAAC,CAAC;IACxC;IACA;AACR;AACA;AACA;IACQ7K,aAAa,KAAK/G,SAAS;IACvB;AACZ;AACA;AACA;AACA;IACYJ,EAAE,CAACM,KAAK,KAAKF,SAAS,IACtB6R,YAAY,CAAC,CAAC,KAAK,IAAI,EAAE;MACzB,OAAO,KAAK;IAChB;IACA,OAAO,IAAI;EACf,CAAC;EACD,MAAMtE,mBAAmB,GAAGA,CAAA,KAAM;IAC9B,IAAIwE,uBAAuB,CAAC,CAAC,EAAE;MAC3B;AACZ;AACA;AACA;AACA;AACA;MACYJ,uDAAG,CAAC,MAAM;QACNK,aAAa,CAAC,CAAC;MACnB,CAAC,CAAC;IACN;EACJ,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,MAAMA,aAAa,GAAGA,CAAA,KAAM;IACxB,MAAMjL,aAAa,GAAG6K,gBAAgB,CAAC,CAAC;IACxC,IAAI7K,aAAa,KAAK/G,SAAS,EAAE;MAC7B;IACJ;IACA,IAAI,CAAC+R,uBAAuB,CAAC,CAAC,EAAE;MAC5BhL,aAAa,CAACiG,KAAK,CAACiF,cAAc,CAAC,OAAO,CAAC;MAC3C;IACJ;IACA,MAAMC,KAAK,GAAGL,YAAY,CAAC,CAAC,CAACM,WAAW;IACxC;IACA;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQD,KAAK,KAAK,CAAC,IACPnL,aAAa,CAACqL,YAAY,KAAK,IAAI,IACnCV,iDAAG,KAAK1R,SAAS,IACjB,2EAA6B,EAAE;MAC/B;AACZ;AACA;AACA;AACA;AACA;AACA;MACY,IAAI8R,iBAAiB,KAAK9R,SAAS,EAAE;QACjC;MACJ;MACA,MAAMqS,EAAE,GAAIP,iBAAiB,GAAG,IAAIQ,oBAAoB,CAAE7N,EAAE,IAAK;QAC7D;AAChB;AACA;AACA;QACgB,IAAIA,EAAE,CAAC,CAAC,CAAC,CAAC8N,iBAAiB,KAAK,CAAC,EAAE;UAC/BP,aAAa,CAAC,CAAC;UACfK,EAAE,CAACjL,UAAU,CAAC,CAAC;UACf0K,iBAAiB,GAAG9R,SAAS;QACjC;MACJ,CAAC;MACD;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACY;QAAEwS,SAAS,EAAE,IAAI;QAAEC,IAAI,EAAE7S;MAAG,CAAC,CAAE;MAC/ByS,EAAE,CAACK,OAAO,CAAC3L,aAAa,CAAC;MACzB;IACJ;IACA;AACR;AACA;AACA;AACA;AACA;AACA;IACQA,aAAa,CAACiG,KAAK,CAAC2F,WAAW,CAAC,OAAO,EAAE,GAAGT,KAAK,GAAG,IAAI,IAAI,CAAC;EACjE,CAAC;EACD,MAAM7K,OAAO,GAAGA,CAAA,KAAM;IAClB,IAAIyK,iBAAiB,EAAE;MACnBA,iBAAiB,CAAC1K,UAAU,CAAC,CAAC;MAC9B0K,iBAAiB,GAAG9R,SAAS;IACjC;EACJ,CAAC;EACD,OAAO;IACHuN,mBAAmB;IACnBlG;EACJ,CAAC;AACL,CAAC", "sources": ["./node_modules/@ionic/core/dist/esm/form-controller-21dd62b1.js", "./node_modules/@ionic/core/dist/esm/ion-select_3.entry.js", "./node_modules/@ionic/core/dist/esm/notch-controller-6bd3e0f9.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { h as findItemLabel } from './helpers-be245865.js';\n\n/**\n * Creates a controller that tracks whether a form control is using the legacy or modern syntax. This should be removed when the legacy form control syntax is removed.\n *\n * @internal\n * @prop el: The Ionic form component to reference\n */\nconst createLegacyFormController = (el) => {\n    const controlEl = el;\n    let legacyControl;\n    const hasLegacyControl = () => {\n        if (legacyControl === undefined) {\n            /**\n             * Detect if developers are using the legacy form control syntax\n             * so a deprecation warning is logged. This warning can be disabled\n             * by either using the new `label` property or setting `aria-label`\n             * on the control.\n             * Alternatively, components that use a slot for the label\n             * can check to see if the component has slotted text\n             * in the light DOM.\n             */\n            const hasLabelProp = controlEl.label !== undefined || hasLabelSlot(controlEl);\n            const hasAriaLabelAttribute = controlEl.hasAttribute('aria-label') ||\n                // Shadow DOM form controls cannot use aria-labelledby\n                (controlEl.hasAttribute('aria-labelledby') && controlEl.shadowRoot === null);\n            const legacyItemLabel = findItemLabel(controlEl);\n            /**\n             * Developers can manually opt-out of the modern form markup\n             * by setting `legacy=\"true\"` on components.\n             */\n            legacyControl =\n                controlEl.legacy === true || (!hasLabelProp && !hasAriaLabelAttribute && legacyItemLabel !== null);\n        }\n        return legacyControl;\n    };\n    return { hasLegacyControl };\n};\nconst hasLabelSlot = (controlEl) => {\n    /**\n     * Components that have a named label slot\n     * also have other slots, so we need to query for\n     * anything that is explicitly passed to slot=\"label\"\n     */\n    if (NAMED_LABEL_SLOT_COMPONENTS.includes(controlEl.tagName) && controlEl.querySelector('[slot=\"label\"]') !== null) {\n        return true;\n    }\n    /**\n     * Components that have an unnamed slot for the label\n     * have no other slots, so we can check the textContent\n     * of the element.\n     */\n    if (UNNAMED_LABEL_SLOT_COMPONENTS.includes(controlEl.tagName) && controlEl.textContent !== '') {\n        return true;\n    }\n    return false;\n};\nconst NAMED_LABEL_SLOT_COMPONENTS = ['ION-INPUT', 'ION-TEXTAREA', 'ION-SELECT', 'ION-RANGE'];\nconst UNNAMED_LABEL_SLOT_COMPONENTS = ['ION-TOGGLE', 'ION-CHECKBOX', 'ION-RADIO'];\n\nexport { createLegacyFormController as c };\n", "/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, d as createEvent, h, H as Host, f as getElement, i as forceUpdate } from './index-a1a47f01.js';\nimport { c as createLegacyFormController } from './form-controller-21dd62b1.js';\nimport { c as createNotchController } from './notch-controller-6bd3e0f9.js';\nimport { i as isOptionSelected, c as compareOptions } from './compare-with-utils-a96ff2ea.js';\nimport { k as inheritAttributes, f as focusVisibleElement, h as findItemLabel, d as renderHiddenInput, e as getAriaLabel } from './helpers-be245865.js';\nimport { p as printIonWarning } from './index-9b0d46f4.js';\nimport { c as popoverController, b as actionSheetController, a as alertController, s as safeCall } from './overlays-b874c3c3.js';\nimport { i as isRTL } from './dir-babeabeb.js';\nimport { h as hostContext, c as createColorClasses, g as getClassMap } from './theme-01f3f29c.js';\nimport { w as watchForOptions } from './watch-options-c2911ace.js';\nimport { w as chevronExpand, q as caretDownSharp } from './index-f7dc70ba.js';\nimport { b as getIonMode } from './ionic-global-94f25d1b.js';\nimport './index-a5d50daf.js';\nimport './hardware-back-button-6107a37c.js';\nimport './framework-delegate-ed4ba327.js';\n\nconst selectIosCss = \":host{--padding-top:0px;--padding-end:0px;--padding-bottom:0px;--padding-start:0px;--placeholder-color:currentColor;--placeholder-opacity:0.6;--background:transparent;--border-style:solid;--highlight-color-focused:var(--ion-color-primary, #3880ff);--highlight-color-valid:var(--ion-color-success, #2dd36f);--highlight-color-invalid:var(--ion-color-danger, #eb445a);--highlight-color:var(--highlight-color-focused);display:block;position:relative;font-family:var(--ion-font-family, inherit);white-space:nowrap;cursor:pointer;z-index:2}:host(:not(.legacy-select)){width:100%;min-height:44px}:host(.select-label-placement-floating),:host(.select-label-placement-stacked){min-height:56px}:host(.ion-color){--highlight-color-focused:var(--ion-color-base)}:host(.legacy-select){-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;overflow:hidden}:host(.in-item:not(.legacy-select)){-ms-flex:1 1 0px;flex:1 1 0}:host(.in-item.legacy-select){position:static;max-width:45%}:host(.select-disabled){pointer-events:none}:host(.ion-focused) button{border:2px solid #5e9ed6}:host([slot=start]:not(.legacy-select)),:host([slot=end]:not(.legacy-select)){width:auto}.select-placeholder{color:var(--placeholder-color);opacity:var(--placeholder-opacity)}:host(.legacy-select) label{top:0;margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;position:absolute;width:100%;height:100%;border:0;background:transparent;cursor:pointer;-webkit-appearance:none;-moz-appearance:none;appearance:none;outline:none;display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;opacity:0}@supports (inset-inline-start: 0){:host(.legacy-select) label{inset-inline-start:0}}@supports not (inset-inline-start: 0){:host(.legacy-select) label{left:0}:host-context([dir=rtl]):host(.legacy-select) label,:host-context([dir=rtl]).legacy-select label{left:unset;right:unset;right:0}@supports selector(:dir(rtl)){:host(.legacy-select:dir(rtl)) label{left:unset;right:unset;right:0}}}:host(.legacy-select) label::-moz-focus-inner{border:0}button{position:absolute;top:0;left:0;right:0;bottom:0;width:100%;height:100%;margin:0;padding:0;border:0;outline:0;clip:rect(0 0 0 0);opacity:0;overflow:hidden;-webkit-appearance:none;-moz-appearance:none}.select-icon{-webkit-margin-start:4px;margin-inline-start:4px;-webkit-margin-end:0;margin-inline-end:0;margin-top:0;margin-bottom:0;position:relative;-ms-flex-negative:0;flex-shrink:0}:host(.in-item-color) .select-icon{color:inherit}:host(.select-label-placement-stacked) .select-icon,:host(.select-label-placement-floating) .select-icon{position:absolute;height:100%}:host(.select-ltr.select-label-placement-stacked) .select-icon,:host(.select-ltr.select-label-placement-floating) .select-icon{right:var(--padding-end, 0)}:host(.select-rtl.select-label-placement-stacked) .select-icon,:host(.select-rtl.select-label-placement-floating) .select-icon{left:var(--padding-start, 0)}.select-text{-ms-flex:1;flex:1;min-width:16px;font-size:inherit;text-overflow:ellipsis;white-space:inherit;overflow:hidden}.select-wrapper{-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);border-radius:var(--border-radius);display:-ms-flexbox;display:flex;position:relative;-ms-flex-positive:1;flex-grow:1;-ms-flex-align:center;align-items:center;height:inherit;min-height:inherit;-webkit-transition:background-color 15ms linear;transition:background-color 15ms linear;background:var(--background);line-height:normal;cursor:inherit;-webkit-box-sizing:border-box;box-sizing:border-box}.select-wrapper .select-placeholder{-webkit-transition:opacity 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:opacity 150ms cubic-bezier(0.4, 0, 0.2, 1)}.select-wrapper-inner{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;overflow:hidden}:host(.select-label-placement-stacked) .select-wrapper-inner,:host(.select-label-placement-floating) .select-wrapper-inner{-ms-flex-positive:1;flex-grow:1}:host(.ion-touched.ion-invalid){--highlight-color:var(--highlight-color-invalid)}:host(.ion-valid){--highlight-color:var(--highlight-color-valid)}.label-text-wrapper{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;max-width:200px;-webkit-transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), transform 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), transform 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1);pointer-events:none}.label-text,::slotted([slot=label]){text-overflow:ellipsis;white-space:nowrap;overflow:hidden}.label-text-wrapper-hidden,.select-outline-notch-hidden{display:none}.native-wrapper{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-webkit-transition:opacity 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:opacity 150ms cubic-bezier(0.4, 0, 0.2, 1);overflow:hidden}:host(.select-justify-space-between) .select-wrapper{-ms-flex-pack:justify;justify-content:space-between}:host(.select-justify-start) .select-wrapper{-ms-flex-pack:start;justify-content:start}:host(.select-justify-end) .select-wrapper{-ms-flex-pack:end;justify-content:end}:host(.select-label-placement-start) .select-wrapper{-ms-flex-direction:row;flex-direction:row}:host(.select-label-placement-start) .label-text-wrapper{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px;margin-top:0;margin-bottom:0}:host(.select-label-placement-end) .select-wrapper{-ms-flex-direction:row-reverse;flex-direction:row-reverse}:host(.select-label-placement-end) .label-text-wrapper{-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:0;margin-inline-end:0;margin-top:0;margin-bottom:0}:host(.select-label-placement-fixed) .label-text-wrapper{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px;margin-top:0;margin-bottom:0}:host(.select-label-placement-fixed) .label-text-wrapper{-ms-flex:0 0 100px;flex:0 0 100px;width:100px;min-width:100px;max-width:200px}:host(.select-label-placement-stacked) .select-wrapper,:host(.select-label-placement-floating) .select-wrapper{-ms-flex-direction:column;flex-direction:column;-ms-flex-align:start;align-items:start}:host(.select-label-placement-stacked) .label-text-wrapper,:host(.select-label-placement-floating) .label-text-wrapper{max-width:100%}:host(.select-ltr.select-label-placement-stacked) .label-text-wrapper,:host(.select-ltr.select-label-placement-floating) .label-text-wrapper{-webkit-transform-origin:left top;transform-origin:left top}:host(.select-rtl.select-label-placement-stacked) .label-text-wrapper,:host(.select-rtl.select-label-placement-floating) .label-text-wrapper{-webkit-transform-origin:right top;transform-origin:right top}:host(.select-label-placement-stacked) .native-wrapper,:host(.select-label-placement-floating) .native-wrapper{margin-left:0;margin-right:0;margin-top:1px;margin-bottom:0;-ms-flex-positive:1;flex-grow:1;width:100%}:host(.select-label-placement-floating) .label-text-wrapper{-webkit-transform:translateY(100%) scale(1);transform:translateY(100%) scale(1)}:host(.select-label-placement-floating:not(.label-floating)) .native-wrapper .select-placeholder{opacity:0}:host(.select-expanded.select-label-placement-floating) .native-wrapper .select-placeholder,:host(.ion-focused.select-label-placement-floating) .native-wrapper .select-placeholder,:host(.has-value.select-label-placement-floating) .native-wrapper .select-placeholder{opacity:1}:host(.label-floating) .label-text-wrapper{-webkit-transform:translateY(50%) scale(0.75);transform:translateY(50%) scale(0.75);max-width:calc(100% / 0.75)}::slotted([slot=start]),::slotted([slot=end]){-ms-flex-negative:0;flex-shrink:0}::slotted([slot=start]){-webkit-margin-end:16px;margin-inline-end:16px;-webkit-margin-start:0;margin-inline-start:0}::slotted([slot=end]){-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:0;margin-inline-end:0}:host(.legacy-select){--padding-top:10px;--padding-end:8px;--padding-bottom:10px;--padding-start:16px}.select-icon{width:1.125rem;height:1.125rem;color:var(--ion-color-step-650, #595959)}:host(.select-label-placement-stacked) .select-wrapper-inner,:host(.select-label-placement-floating) .select-wrapper-inner{width:calc(100% - 1.125rem - 4px)}:host(.select-disabled){opacity:0.3}::slotted(ion-button[slot=start].button-has-icon-only),::slotted(ion-button[slot=end].button-has-icon-only){--border-radius:50%;--padding-start:0;--padding-end:0;--padding-top:0;--padding-bottom:0;aspect-ratio:1}\";\nconst IonSelectIosStyle0 = selectIosCss;\n\nconst selectMdCss = \":host{--padding-top:0px;--padding-end:0px;--padding-bottom:0px;--padding-start:0px;--placeholder-color:currentColor;--placeholder-opacity:0.6;--background:transparent;--border-style:solid;--highlight-color-focused:var(--ion-color-primary, #3880ff);--highlight-color-valid:var(--ion-color-success, #2dd36f);--highlight-color-invalid:var(--ion-color-danger, #eb445a);--highlight-color:var(--highlight-color-focused);display:block;position:relative;font-family:var(--ion-font-family, inherit);white-space:nowrap;cursor:pointer;z-index:2}:host(:not(.legacy-select)){width:100%;min-height:44px}:host(.select-label-placement-floating),:host(.select-label-placement-stacked){min-height:56px}:host(.ion-color){--highlight-color-focused:var(--ion-color-base)}:host(.legacy-select){-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;overflow:hidden}:host(.in-item:not(.legacy-select)){-ms-flex:1 1 0px;flex:1 1 0}:host(.in-item.legacy-select){position:static;max-width:45%}:host(.select-disabled){pointer-events:none}:host(.ion-focused) button{border:2px solid #5e9ed6}:host([slot=start]:not(.legacy-select)),:host([slot=end]:not(.legacy-select)){width:auto}.select-placeholder{color:var(--placeholder-color);opacity:var(--placeholder-opacity)}:host(.legacy-select) label{top:0;margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;position:absolute;width:100%;height:100%;border:0;background:transparent;cursor:pointer;-webkit-appearance:none;-moz-appearance:none;appearance:none;outline:none;display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;opacity:0}@supports (inset-inline-start: 0){:host(.legacy-select) label{inset-inline-start:0}}@supports not (inset-inline-start: 0){:host(.legacy-select) label{left:0}:host-context([dir=rtl]):host(.legacy-select) label,:host-context([dir=rtl]).legacy-select label{left:unset;right:unset;right:0}@supports selector(:dir(rtl)){:host(.legacy-select:dir(rtl)) label{left:unset;right:unset;right:0}}}:host(.legacy-select) label::-moz-focus-inner{border:0}button{position:absolute;top:0;left:0;right:0;bottom:0;width:100%;height:100%;margin:0;padding:0;border:0;outline:0;clip:rect(0 0 0 0);opacity:0;overflow:hidden;-webkit-appearance:none;-moz-appearance:none}.select-icon{-webkit-margin-start:4px;margin-inline-start:4px;-webkit-margin-end:0;margin-inline-end:0;margin-top:0;margin-bottom:0;position:relative;-ms-flex-negative:0;flex-shrink:0}:host(.in-item-color) .select-icon{color:inherit}:host(.select-label-placement-stacked) .select-icon,:host(.select-label-placement-floating) .select-icon{position:absolute;height:100%}:host(.select-ltr.select-label-placement-stacked) .select-icon,:host(.select-ltr.select-label-placement-floating) .select-icon{right:var(--padding-end, 0)}:host(.select-rtl.select-label-placement-stacked) .select-icon,:host(.select-rtl.select-label-placement-floating) .select-icon{left:var(--padding-start, 0)}.select-text{-ms-flex:1;flex:1;min-width:16px;font-size:inherit;text-overflow:ellipsis;white-space:inherit;overflow:hidden}.select-wrapper{-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);border-radius:var(--border-radius);display:-ms-flexbox;display:flex;position:relative;-ms-flex-positive:1;flex-grow:1;-ms-flex-align:center;align-items:center;height:inherit;min-height:inherit;-webkit-transition:background-color 15ms linear;transition:background-color 15ms linear;background:var(--background);line-height:normal;cursor:inherit;-webkit-box-sizing:border-box;box-sizing:border-box}.select-wrapper .select-placeholder{-webkit-transition:opacity 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:opacity 150ms cubic-bezier(0.4, 0, 0.2, 1)}.select-wrapper-inner{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;overflow:hidden}:host(.select-label-placement-stacked) .select-wrapper-inner,:host(.select-label-placement-floating) .select-wrapper-inner{-ms-flex-positive:1;flex-grow:1}:host(.ion-touched.ion-invalid){--highlight-color:var(--highlight-color-invalid)}:host(.ion-valid){--highlight-color:var(--highlight-color-valid)}.label-text-wrapper{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;max-width:200px;-webkit-transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), transform 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), transform 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1);pointer-events:none}.label-text,::slotted([slot=label]){text-overflow:ellipsis;white-space:nowrap;overflow:hidden}.label-text-wrapper-hidden,.select-outline-notch-hidden{display:none}.native-wrapper{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-webkit-transition:opacity 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:opacity 150ms cubic-bezier(0.4, 0, 0.2, 1);overflow:hidden}:host(.select-justify-space-between) .select-wrapper{-ms-flex-pack:justify;justify-content:space-between}:host(.select-justify-start) .select-wrapper{-ms-flex-pack:start;justify-content:start}:host(.select-justify-end) .select-wrapper{-ms-flex-pack:end;justify-content:end}:host(.select-label-placement-start) .select-wrapper{-ms-flex-direction:row;flex-direction:row}:host(.select-label-placement-start) .label-text-wrapper{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px;margin-top:0;margin-bottom:0}:host(.select-label-placement-end) .select-wrapper{-ms-flex-direction:row-reverse;flex-direction:row-reverse}:host(.select-label-placement-end) .label-text-wrapper{-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:0;margin-inline-end:0;margin-top:0;margin-bottom:0}:host(.select-label-placement-fixed) .label-text-wrapper{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px;margin-top:0;margin-bottom:0}:host(.select-label-placement-fixed) .label-text-wrapper{-ms-flex:0 0 100px;flex:0 0 100px;width:100px;min-width:100px;max-width:200px}:host(.select-label-placement-stacked) .select-wrapper,:host(.select-label-placement-floating) .select-wrapper{-ms-flex-direction:column;flex-direction:column;-ms-flex-align:start;align-items:start}:host(.select-label-placement-stacked) .label-text-wrapper,:host(.select-label-placement-floating) .label-text-wrapper{max-width:100%}:host(.select-ltr.select-label-placement-stacked) .label-text-wrapper,:host(.select-ltr.select-label-placement-floating) .label-text-wrapper{-webkit-transform-origin:left top;transform-origin:left top}:host(.select-rtl.select-label-placement-stacked) .label-text-wrapper,:host(.select-rtl.select-label-placement-floating) .label-text-wrapper{-webkit-transform-origin:right top;transform-origin:right top}:host(.select-label-placement-stacked) .native-wrapper,:host(.select-label-placement-floating) .native-wrapper{margin-left:0;margin-right:0;margin-top:1px;margin-bottom:0;-ms-flex-positive:1;flex-grow:1;width:100%}:host(.select-label-placement-floating) .label-text-wrapper{-webkit-transform:translateY(100%) scale(1);transform:translateY(100%) scale(1)}:host(.select-label-placement-floating:not(.label-floating)) .native-wrapper .select-placeholder{opacity:0}:host(.select-expanded.select-label-placement-floating) .native-wrapper .select-placeholder,:host(.ion-focused.select-label-placement-floating) .native-wrapper .select-placeholder,:host(.has-value.select-label-placement-floating) .native-wrapper .select-placeholder{opacity:1}:host(.label-floating) .label-text-wrapper{-webkit-transform:translateY(50%) scale(0.75);transform:translateY(50%) scale(0.75);max-width:calc(100% / 0.75)}::slotted([slot=start]),::slotted([slot=end]){-ms-flex-negative:0;flex-shrink:0}::slotted([slot=start]){-webkit-margin-end:16px;margin-inline-end:16px;-webkit-margin-start:0;margin-inline-start:0}::slotted([slot=end]){-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:0;margin-inline-end:0}:host(.select-fill-solid){--background:var(--ion-color-step-50, #f2f2f2);--border-color:var(--ion-color-step-500, gray);--border-radius:4px;--padding-start:16px;--padding-end:16px;min-height:56px}:host(.select-fill-solid) .select-wrapper{border-bottom:var(--border-width) var(--border-style) var(--border-color)}:host(.has-focus.select-fill-solid.ion-valid),:host(.select-fill-solid.ion-touched.ion-invalid){--border-color:var(--highlight-color)}:host(.select-fill-solid) .select-bottom{border-top:none}@media (any-hover: hover){:host(.select-fill-solid:hover){--background:var(--ion-color-step-100, #e6e6e6);--border-color:var(--ion-color-step-750, #404040)}}:host(.select-fill-solid.select-expanded),:host(.select-fill-solid.ion-focused){--background:var(--ion-color-step-150, #d9d9d9);--border-color:var(--ion-color-step-750, #404040)}:host(.select-fill-solid) .select-wrapper{border-top-left-radius:var(--border-radius);border-top-right-radius:var(--border-radius);border-bottom-right-radius:0px;border-bottom-left-radius:0px}:host-context([dir=rtl]):host(.select-fill-solid) .select-wrapper,:host-context([dir=rtl]).select-fill-solid .select-wrapper{border-top-left-radius:var(--border-radius);border-top-right-radius:var(--border-radius);border-bottom-right-radius:0px;border-bottom-left-radius:0px}@supports selector(:dir(rtl)){:host(.select-fill-solid:dir(rtl)) .select-wrapper{border-top-left-radius:var(--border-radius);border-top-right-radius:var(--border-radius);border-bottom-right-radius:0px;border-bottom-left-radius:0px}}:host(.label-floating.select-fill-solid) .label-text-wrapper{max-width:calc(100% / 0.75)}:host(.select-fill-outline){--border-color:var(--ion-color-step-300, #b3b3b3);--border-radius:4px;--padding-start:16px;--padding-end:16px;min-height:56px}:host(.select-fill-outline.select-shape-round){--border-radius:28px;--padding-start:32px;--padding-end:32px}:host(.has-focus.select-fill-outline.ion-valid),:host(.select-fill-outline.ion-touched.ion-invalid){--border-color:var(--highlight-color)}@media (any-hover: hover){:host(.select-fill-outline:hover){--border-color:var(--ion-color-step-750, #404040)}}:host(.select-fill-outline.select-expanded),:host(.select-fill-outline.ion-focused){--border-width:2px;--border-color:var(--highlight-color)}:host(.select-fill-outline) .select-bottom{border-top:none}:host(.select-fill-outline) .select-wrapper{border-bottom:none}:host(.select-ltr.select-fill-outline.select-label-placement-stacked) .label-text-wrapper,:host(.select-ltr.select-fill-outline.select-label-placement-floating) .label-text-wrapper{-webkit-transform-origin:left top;transform-origin:left top}:host(.select-rtl.select-fill-outline.select-label-placement-stacked) .label-text-wrapper,:host(.select-rtl.select-fill-outline.select-label-placement-floating) .label-text-wrapper{-webkit-transform-origin:right top;transform-origin:right top}:host(.select-fill-outline.select-label-placement-stacked) .label-text-wrapper,:host(.select-fill-outline.select-label-placement-floating) .label-text-wrapper{position:absolute;max-width:calc(100% - var(--padding-start) - var(--padding-end))}:host(.select-fill-outline) .label-text-wrapper{position:relative;z-index:1}:host(.label-floating.select-fill-outline) .label-text-wrapper{-webkit-transform:translateY(-32%) scale(0.75);transform:translateY(-32%) scale(0.75);margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;max-width:calc((100% - var(--padding-start) - var(--padding-end) - 8px) / 0.75)}:host(.select-fill-outline.select-label-placement-stacked) select,:host(.select-fill-outline.select-label-placement-floating) select{margin-left:0;margin-right:0;margin-top:6px;margin-bottom:6px}:host(.select-fill-outline) .select-outline-container{left:0;right:0;top:0;bottom:0;display:-ms-flexbox;display:flex;position:absolute;width:100%;height:100%}:host(.select-fill-outline) .select-outline-start,:host(.select-fill-outline) .select-outline-end{pointer-events:none}:host(.select-fill-outline) .select-outline-start,:host(.select-fill-outline) .select-outline-notch,:host(.select-fill-outline) .select-outline-end{border-top:var(--border-width) var(--border-style) var(--border-color);border-bottom:var(--border-width) var(--border-style) var(--border-color);-webkit-box-sizing:border-box;box-sizing:border-box}:host(.select-fill-outline) .select-outline-notch{max-width:calc(100% - var(--padding-start) - var(--padding-end))}:host(.select-fill-outline) .notch-spacer{-webkit-padding-end:8px;padding-inline-end:8px;font-size:calc(1em * 0.75);opacity:0;pointer-events:none}:host(.select-fill-outline) .select-outline-start{-webkit-border-start:var(--border-width) var(--border-style) var(--border-color);border-inline-start:var(--border-width) var(--border-style) var(--border-color)}:host(.select-ltr.select-fill-outline) .select-outline-start{border-radius:var(--border-radius) 0px 0px var(--border-radius)}:host(.select-rtl.select-fill-outline) .select-outline-start{border-radius:0px var(--border-radius) var(--border-radius) 0px}:host(.select-fill-outline) .select-outline-start{width:calc(var(--padding-start) - 4px)}:host(.select-fill-outline) .select-outline-end{-webkit-border-end:var(--border-width) var(--border-style) var(--border-color);border-inline-end:var(--border-width) var(--border-style) var(--border-color)}:host(.select-ltr.select-fill-outline) .select-outline-end{border-radius:0px var(--border-radius) var(--border-radius) 0px}:host(.select-rtl.select-fill-outline) .select-outline-end{border-radius:var(--border-radius) 0px 0px var(--border-radius)}:host(.select-fill-outline) .select-outline-end{-ms-flex-positive:1;flex-grow:1}:host(.label-floating.select-fill-outline) .select-outline-notch{border-top:none}:host{--border-width:1px;--border-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-150, rgba(0, 0, 0, 0.13))))}:host(.legacy-select){--padding-top:10px;--padding-end:0;--padding-bottom:10px;--padding-start:16px}.select-icon{width:0.8125rem;-webkit-transition:-webkit-transform 0.15s cubic-bezier(0.4, 0, 0.2, 1);transition:-webkit-transform 0.15s cubic-bezier(0.4, 0, 0.2, 1);transition:transform 0.15s cubic-bezier(0.4, 0, 0.2, 1);transition:transform 0.15s cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 0.15s cubic-bezier(0.4, 0, 0.2, 1);color:var(--ion-color-step-500, gray)}:host(.select-label-placement-floating.select-expanded) .label-text-wrapper,:host(.select-label-placement-floating.ion-focused) .label-text-wrapper,:host(.select-label-placement-stacked.select-expanded) .label-text-wrapper,:host(.select-label-placement-stacked.ion-focused) .label-text-wrapper{color:var(--highlight-color)}:host(.has-focus.select-label-placement-floating.ion-valid) .label-text-wrapper,:host(.select-label-placement-floating.ion-touched.ion-invalid) .label-text-wrapper,:host(.has-focus.select-label-placement-stacked.ion-valid) .label-text-wrapper,:host(.select-label-placement-stacked.ion-touched.ion-invalid) .label-text-wrapper{color:var(--highlight-color)}.select-highlight{bottom:-1px;position:absolute;width:100%;height:2px;-webkit-transform:scale(0);transform:scale(0);-webkit-transition:-webkit-transform 200ms;transition:-webkit-transform 200ms;transition:transform 200ms;transition:transform 200ms, -webkit-transform 200ms;background:var(--highlight-color)}@supports (inset-inline-start: 0){.select-highlight{inset-inline-start:0}}@supports not (inset-inline-start: 0){.select-highlight{left:0}:host-context([dir=rtl]) .select-highlight{left:unset;right:unset;right:0}[dir=rtl] .select-highlight{left:unset;right:unset;right:0}@supports selector(:dir(rtl)){.select-highlight:dir(rtl){left:unset;right:unset;right:0}}}:host(.select-expanded) .select-highlight,:host(.ion-focused) .select-highlight{-webkit-transform:scale(1);transform:scale(1)}:host(.in-item) .select-highlight{bottom:0}@supports (inset-inline-start: 0){:host(.in-item) .select-highlight{inset-inline-start:0}}@supports not (inset-inline-start: 0){:host(.in-item) .select-highlight{left:0}:host-context([dir=rtl]):host(.in-item) .select-highlight,:host-context([dir=rtl]).in-item .select-highlight{left:unset;right:unset;right:0}@supports selector(:dir(rtl)){:host(.in-item:dir(rtl)) .select-highlight{left:unset;right:unset;right:0}}}:host(.select-expanded:not(.legacy-select):not(.has-expanded-icon)) .select-icon{-webkit-transform:rotate(180deg);transform:rotate(180deg)}:host(.select-expanded) .select-wrapper .select-icon,:host(.has-focus.ion-valid) .select-wrapper .select-icon,:host(.ion-touched.ion-invalid) .select-wrapper .select-icon,:host(.ion-focused) .select-wrapper .select-icon{color:var(--highlight-color)}:host-context(.item-label-stacked) .select-icon,:host-context(.item-label-floating:not(.item-fill-outline)) .select-icon,:host-context(.item-label-floating.item-fill-outline){-webkit-transform:translate3d(0,  -9px,  0);transform:translate3d(0,  -9px,  0)}:host-context(.item-has-focus):host(:not(.has-expanded-icon)) .select-icon{-webkit-transform:rotate(180deg);transform:rotate(180deg)}:host-context(.item-has-focus.item-label-stacked):host(:not(.has-expanded-icon)) .select-icon,:host-context(.item-has-focus.item-label-floating:not(.item-fill-outline)):host(:not(.has-expanded-icon)) .select-icon{-webkit-transform:translate3d(0,  -9px,  0) rotate(180deg);transform:translate3d(0,  -9px,  0) rotate(180deg)}:host(.select-shape-round){--border-radius:16px}:host(.select-label-placement-stacked) .select-wrapper-inner,:host(.select-label-placement-floating) .select-wrapper-inner{width:calc(100% - 0.8125rem - 4px)}:host(.select-disabled){opacity:0.38}::slotted(ion-button[slot=start].button-has-icon-only),::slotted(ion-button[slot=end].button-has-icon-only){--border-radius:50%;--padding-start:8px;--padding-end:8px;--padding-top:8px;--padding-bottom:8px;aspect-ratio:1;min-height:40px}\";\nconst IonSelectMdStyle0 = selectMdCss;\n\nconst Select = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.ionChange = createEvent(this, \"ionChange\", 7);\n        this.ionCancel = createEvent(this, \"ionCancel\", 7);\n        this.ionDismiss = createEvent(this, \"ionDismiss\", 7);\n        this.ionFocus = createEvent(this, \"ionFocus\", 7);\n        this.ionBlur = createEvent(this, \"ionBlur\", 7);\n        this.ionStyle = createEvent(this, \"ionStyle\", 7);\n        this.inputId = `ion-sel-${selectIds++}`;\n        this.inheritedAttributes = {};\n        // This flag ensures we log the deprecation warning at most once.\n        this.hasLoggedDeprecationWarning = false;\n        this.onClick = (ev) => {\n            const target = ev.target;\n            const closestSlot = target.closest('[slot=\"start\"], [slot=\"end\"]');\n            if (target === this.el || closestSlot === null) {\n                this.setFocus();\n                this.open(ev);\n            }\n            else {\n                /**\n                 * Prevent clicks to the start/end slots from opening the select.\n                 * We ensure the target isn't this element in case the select is slotted\n                 * in, for example, an item. This would prevent the select from ever\n                 * being opened since the element itself has slot=\"start\"/\"end\".\n                 *\n                 * Clicking a slotted element also causes a click\n                 * on the <label> element (since it wraps the slots).\n                 * Clicking <label> dispatches another click event on\n                 * the native form control that then bubbles up to this\n                 * listener. This additional event targets the host\n                 * element, so the select overlay is opened.\n                 *\n                 * When the slotted elements are clicked (and therefore\n                 * the ancestor <label> element) we want to prevent the label\n                 * from dispatching another click event.\n                 *\n                 * Do not call stopPropagation() because this will cause\n                 * click handlers on the slotted elements to never fire in React.\n                 * When developers do onClick in React a native \"click\" listener\n                 * is added on the root element, not the slotted element. When that\n                 * native click listener fires, React then dispatches the synthetic\n                 * click event on the slotted element. However, if stopPropagation\n                 * is called then the native click event will never bubble up\n                 * to the root element.\n                 */\n                ev.preventDefault();\n            }\n        };\n        this.onFocus = () => {\n            this.ionFocus.emit();\n        };\n        this.onBlur = () => {\n            this.ionBlur.emit();\n        };\n        this.isExpanded = false;\n        this.cancelText = 'Cancel';\n        this.color = undefined;\n        this.compareWith = undefined;\n        this.disabled = false;\n        this.fill = undefined;\n        this.interface = 'alert';\n        this.interfaceOptions = {};\n        this.justify = 'space-between';\n        this.label = undefined;\n        this.labelPlacement = 'start';\n        this.legacy = undefined;\n        this.multiple = false;\n        this.name = this.inputId;\n        this.okText = 'OK';\n        this.placeholder = undefined;\n        this.selectedText = undefined;\n        this.toggleIcon = undefined;\n        this.expandedIcon = undefined;\n        this.shape = undefined;\n        this.value = undefined;\n    }\n    styleChanged() {\n        this.emitStyle();\n    }\n    setValue(value) {\n        this.value = value;\n        this.ionChange.emit({ value });\n    }\n    componentWillLoad() {\n        this.inheritedAttributes = inheritAttributes(this.el, ['aria-label']);\n    }\n    async connectedCallback() {\n        const { el } = this;\n        this.legacyFormController = createLegacyFormController(el);\n        this.notchController = createNotchController(el, () => this.notchSpacerEl, () => this.labelSlot);\n        this.updateOverlayOptions();\n        this.emitStyle();\n        this.mutationO = watchForOptions(this.el, 'ion-select-option', async () => {\n            this.updateOverlayOptions();\n            /**\n             * We need to re-render the component\n             * because one of the new ion-select-option\n             * elements may match the value. In this case,\n             * the rendered selected text should be updated.\n             */\n            forceUpdate(this);\n        });\n    }\n    disconnectedCallback() {\n        if (this.mutationO) {\n            this.mutationO.disconnect();\n            this.mutationO = undefined;\n        }\n        if (this.notchController) {\n            this.notchController.destroy();\n            this.notchController = undefined;\n        }\n    }\n    /**\n     * Open the select overlay. The overlay is either an alert, action sheet, or popover,\n     * depending on the `interface` property on the `ion-select`.\n     *\n     * @param event The user interface event that called the open.\n     */\n    async open(event) {\n        if (this.disabled || this.isExpanded) {\n            return undefined;\n        }\n        this.isExpanded = true;\n        const overlay = (this.overlay = await this.createOverlay(event));\n        overlay.onDidDismiss().then(() => {\n            this.overlay = undefined;\n            this.isExpanded = false;\n            this.ionDismiss.emit();\n            this.setFocus();\n        });\n        await overlay.present();\n        // focus selected option for popovers\n        if (this.interface === 'popover') {\n            const indexOfSelected = this.childOpts.map((o) => o.value).indexOf(this.value);\n            if (indexOfSelected > -1) {\n                const selectedItem = overlay.querySelector(`.select-interface-option:nth-child(${indexOfSelected + 1})`);\n                if (selectedItem) {\n                    focusVisibleElement(selectedItem);\n                    /**\n                     * Browsers such as Firefox do not\n                     * correctly delegate focus when manually\n                     * focusing an element with delegatesFocus.\n                     * We work around this by manually focusing\n                     * the interactive element.\n                     * ion-radio and ion-checkbox are the only\n                     * elements that ion-select-popover uses, so\n                     * we only need to worry about those two components\n                     * when focusing.\n                     */\n                    const interactiveEl = selectedItem.querySelector('ion-radio, ion-checkbox');\n                    if (interactiveEl) {\n                        interactiveEl.focus();\n                    }\n                }\n            }\n            else {\n                /**\n                 * If no value is set then focus the first enabled option.\n                 */\n                const firstEnabledOption = overlay.querySelector('ion-radio:not(.radio-disabled), ion-checkbox:not(.checkbox-disabled)');\n                if (firstEnabledOption) {\n                    focusVisibleElement(firstEnabledOption.closest('ion-item'));\n                    /**\n                     * Focus the option for the same reason as we do above.\n                     */\n                    firstEnabledOption.focus();\n                }\n            }\n        }\n        return overlay;\n    }\n    createOverlay(ev) {\n        let selectInterface = this.interface;\n        if (selectInterface === 'action-sheet' && this.multiple) {\n            console.warn(`Select interface cannot be \"${selectInterface}\" with a multi-value select. Using the \"alert\" interface instead.`);\n            selectInterface = 'alert';\n        }\n        if (selectInterface === 'popover' && !ev) {\n            console.warn(`Select interface cannot be a \"${selectInterface}\" without passing an event. Using the \"alert\" interface instead.`);\n            selectInterface = 'alert';\n        }\n        if (selectInterface === 'action-sheet') {\n            return this.openActionSheet();\n        }\n        if (selectInterface === 'popover') {\n            return this.openPopover(ev);\n        }\n        return this.openAlert();\n    }\n    updateOverlayOptions() {\n        const overlay = this.overlay;\n        if (!overlay) {\n            return;\n        }\n        const childOpts = this.childOpts;\n        const value = this.value;\n        switch (this.interface) {\n            case 'action-sheet':\n                overlay.buttons = this.createActionSheetButtons(childOpts, value);\n                break;\n            case 'popover':\n                const popover = overlay.querySelector('ion-select-popover');\n                if (popover) {\n                    popover.options = this.createPopoverOptions(childOpts, value);\n                }\n                break;\n            case 'alert':\n                const inputType = this.multiple ? 'checkbox' : 'radio';\n                overlay.inputs = this.createAlertInputs(childOpts, inputType, value);\n                break;\n        }\n    }\n    createActionSheetButtons(data, selectValue) {\n        const actionSheetButtons = data.map((option) => {\n            const value = getOptionValue(option);\n            // Remove hydrated before copying over classes\n            const copyClasses = Array.from(option.classList)\n                .filter((cls) => cls !== 'hydrated')\n                .join(' ');\n            const optClass = `${OPTION_CLASS} ${copyClasses}`;\n            return {\n                role: isOptionSelected(selectValue, value, this.compareWith) ? 'selected' : '',\n                text: option.textContent,\n                cssClass: optClass,\n                handler: () => {\n                    this.setValue(value);\n                },\n            };\n        });\n        // Add \"cancel\" button\n        actionSheetButtons.push({\n            text: this.cancelText,\n            role: 'cancel',\n            handler: () => {\n                this.ionCancel.emit();\n            },\n        });\n        return actionSheetButtons;\n    }\n    createAlertInputs(data, inputType, selectValue) {\n        const alertInputs = data.map((option) => {\n            const value = getOptionValue(option);\n            // Remove hydrated before copying over classes\n            const copyClasses = Array.from(option.classList)\n                .filter((cls) => cls !== 'hydrated')\n                .join(' ');\n            const optClass = `${OPTION_CLASS} ${copyClasses}`;\n            return {\n                type: inputType,\n                cssClass: optClass,\n                label: option.textContent || '',\n                value,\n                checked: isOptionSelected(selectValue, value, this.compareWith),\n                disabled: option.disabled,\n            };\n        });\n        return alertInputs;\n    }\n    createPopoverOptions(data, selectValue) {\n        const popoverOptions = data.map((option) => {\n            const value = getOptionValue(option);\n            // Remove hydrated before copying over classes\n            const copyClasses = Array.from(option.classList)\n                .filter((cls) => cls !== 'hydrated')\n                .join(' ');\n            const optClass = `${OPTION_CLASS} ${copyClasses}`;\n            return {\n                text: option.textContent || '',\n                cssClass: optClass,\n                value,\n                checked: isOptionSelected(selectValue, value, this.compareWith),\n                disabled: option.disabled,\n                handler: (selected) => {\n                    this.setValue(selected);\n                    if (!this.multiple) {\n                        this.close();\n                    }\n                },\n            };\n        });\n        return popoverOptions;\n    }\n    async openPopover(ev) {\n        const { fill, labelPlacement } = this;\n        const interfaceOptions = this.interfaceOptions;\n        const mode = getIonMode(this);\n        const showBackdrop = mode === 'md' ? false : true;\n        const multiple = this.multiple;\n        const value = this.value;\n        let event = ev;\n        let size = 'auto';\n        if (this.legacyFormController.hasLegacyControl()) {\n            const item = this.el.closest('ion-item');\n            // If the select is inside of an item containing a floating\n            // or stacked label then the popover should take up the\n            // full width of the item when it presents\n            if (item && (item.classList.contains('item-label-floating') || item.classList.contains('item-label-stacked'))) {\n                event = Object.assign(Object.assign({}, ev), { detail: {\n                        ionShadowTarget: item,\n                    } });\n                size = 'cover';\n            }\n        }\n        else {\n            const hasFloatingOrStackedLabel = labelPlacement === 'floating' || labelPlacement === 'stacked';\n            /**\n             * The popover should take up the full width\n             * when using a fill in MD mode or if the\n             * label is floating/stacked.\n             */\n            if (hasFloatingOrStackedLabel || (mode === 'md' && fill !== undefined)) {\n                size = 'cover';\n                /**\n                 * Otherwise the popover\n                 * should be positioned relative\n                 * to the native element.\n                 */\n            }\n            else {\n                event = Object.assign(Object.assign({}, ev), { detail: {\n                        ionShadowTarget: this.nativeWrapperEl,\n                    } });\n            }\n        }\n        const popoverOpts = Object.assign(Object.assign({ mode,\n            event, alignment: 'center', size,\n            showBackdrop }, interfaceOptions), { component: 'ion-select-popover', cssClass: ['select-popover', interfaceOptions.cssClass], componentProps: {\n                header: interfaceOptions.header,\n                subHeader: interfaceOptions.subHeader,\n                message: interfaceOptions.message,\n                multiple,\n                value,\n                options: this.createPopoverOptions(this.childOpts, value),\n            } });\n        return popoverController.create(popoverOpts);\n    }\n    async openActionSheet() {\n        const mode = getIonMode(this);\n        const interfaceOptions = this.interfaceOptions;\n        const actionSheetOpts = Object.assign(Object.assign({ mode }, interfaceOptions), { buttons: this.createActionSheetButtons(this.childOpts, this.value), cssClass: ['select-action-sheet', interfaceOptions.cssClass] });\n        return actionSheetController.create(actionSheetOpts);\n    }\n    async openAlert() {\n        /**\n         * TODO FW-3194\n         * Remove legacyFormController logic.\n         * Remove label and labelText vars\n         * Pass `this.labelText` instead of `labelText`\n         * when setting the header.\n         */\n        let label;\n        let labelText;\n        if (this.legacyFormController.hasLegacyControl()) {\n            label = this.getLabel();\n            labelText = label ? label.textContent : null;\n        }\n        else {\n            labelText = this.labelText;\n        }\n        const interfaceOptions = this.interfaceOptions;\n        const inputType = this.multiple ? 'checkbox' : 'radio';\n        const mode = getIonMode(this);\n        const alertOpts = Object.assign(Object.assign({ mode }, interfaceOptions), { header: interfaceOptions.header ? interfaceOptions.header : labelText, inputs: this.createAlertInputs(this.childOpts, inputType, this.value), buttons: [\n                {\n                    text: this.cancelText,\n                    role: 'cancel',\n                    handler: () => {\n                        this.ionCancel.emit();\n                    },\n                },\n                {\n                    text: this.okText,\n                    handler: (selectedValues) => {\n                        this.setValue(selectedValues);\n                    },\n                },\n            ], cssClass: [\n                'select-alert',\n                interfaceOptions.cssClass,\n                this.multiple ? 'multiple-select-alert' : 'single-select-alert',\n            ] });\n        return alertController.create(alertOpts);\n    }\n    /**\n     * Close the select interface.\n     */\n    close() {\n        if (!this.overlay) {\n            return Promise.resolve(false);\n        }\n        return this.overlay.dismiss();\n    }\n    // TODO FW-3194 Remove this\n    getLabel() {\n        return findItemLabel(this.el);\n    }\n    hasValue() {\n        return this.getText() !== '';\n    }\n    get childOpts() {\n        return Array.from(this.el.querySelectorAll('ion-select-option'));\n    }\n    /**\n     * Returns any plaintext associated with\n     * the label (either prop or slot).\n     * Note: This will not return any custom\n     * HTML. Use the `hasLabel` getter if you\n     * want to know if any slotted label content\n     * was passed.\n     */\n    get labelText() {\n        const { label } = this;\n        if (label !== undefined) {\n            return label;\n        }\n        const { labelSlot } = this;\n        if (labelSlot !== null) {\n            return labelSlot.textContent;\n        }\n        return;\n    }\n    getText() {\n        const selectedText = this.selectedText;\n        if (selectedText != null && selectedText !== '') {\n            return selectedText;\n        }\n        return generateText(this.childOpts, this.value, this.compareWith);\n    }\n    setFocus() {\n        if (this.focusEl) {\n            this.focusEl.focus();\n        }\n    }\n    emitStyle() {\n        const { disabled } = this;\n        const style = {\n            'interactive-disabled': disabled,\n        };\n        if (this.legacyFormController.hasLegacyControl()) {\n            style['interactive'] = true;\n            style['select'] = true;\n            style['select-disabled'] = disabled;\n            style['has-placeholder'] = this.placeholder !== undefined;\n            style['has-value'] = this.hasValue();\n            style['has-focus'] = this.isExpanded;\n            // TODO(FW-3194): remove this\n            style['legacy'] = !!this.legacy;\n        }\n        this.ionStyle.emit(style);\n    }\n    renderLabel() {\n        const { label } = this;\n        return (h(\"div\", { class: {\n                'label-text-wrapper': true,\n                'label-text-wrapper-hidden': !this.hasLabel,\n            }, part: \"label\" }, label === undefined ? h(\"slot\", { name: \"label\" }) : h(\"div\", { class: \"label-text\" }, label)));\n    }\n    componentDidRender() {\n        var _a;\n        (_a = this.notchController) === null || _a === void 0 ? void 0 : _a.calculateNotchWidth();\n    }\n    /**\n     * Gets any content passed into the `label` slot,\n     * not the <slot> definition.\n     */\n    get labelSlot() {\n        return this.el.querySelector('[slot=\"label\"]');\n    }\n    /**\n     * Returns `true` if label content is provided\n     * either by a prop or a content. If you want\n     * to get the plaintext value of the label use\n     * the `labelText` getter instead.\n     */\n    get hasLabel() {\n        return this.label !== undefined || this.labelSlot !== null;\n    }\n    /**\n     * Renders the border container\n     * when fill=\"outline\".\n     */\n    renderLabelContainer() {\n        const mode = getIonMode(this);\n        const hasOutlineFill = mode === 'md' && this.fill === 'outline';\n        if (hasOutlineFill) {\n            /**\n             * The outline fill has a special outline\n             * that appears around the select and the label.\n             * Certain stacked and floating label placements cause the\n             * label to translate up and create a \"cut out\"\n             * inside of that border by using the notch-spacer element.\n             */\n            return [\n                h(\"div\", { class: \"select-outline-container\" }, h(\"div\", { class: \"select-outline-start\" }), h(\"div\", { class: {\n                        'select-outline-notch': true,\n                        'select-outline-notch-hidden': !this.hasLabel,\n                    } }, h(\"div\", { class: \"notch-spacer\", \"aria-hidden\": \"true\", ref: (el) => (this.notchSpacerEl = el) }, this.label)), h(\"div\", { class: \"select-outline-end\" })),\n                this.renderLabel(),\n            ];\n        }\n        /**\n         * If not using the outline style,\n         * we can render just the label.\n         */\n        return this.renderLabel();\n    }\n    renderSelect() {\n        const { disabled, el, isExpanded, expandedIcon, labelPlacement, justify, placeholder, fill, shape, name, value } = this;\n        const mode = getIonMode(this);\n        const hasFloatingOrStackedLabel = labelPlacement === 'floating' || labelPlacement === 'stacked';\n        const justifyEnabled = !hasFloatingOrStackedLabel;\n        const rtl = isRTL(el) ? 'rtl' : 'ltr';\n        const inItem = hostContext('ion-item', this.el);\n        const shouldRenderHighlight = mode === 'md' && fill !== 'outline' && !inItem;\n        const hasValue = this.hasValue();\n        const hasStartEndSlots = el.querySelector('[slot=\"start\"], [slot=\"end\"]') !== null;\n        renderHiddenInput(true, el, name, parseValue(value), disabled);\n        /**\n         * If the label is stacked, it should always sit above the select.\n         * For floating labels, the label should move above the select if\n         * the select has a value, is open, or has anything in either\n         * the start or end slot.\n         *\n         * If there is content in the start slot, the label would overlap\n         * it if not forced to float. This is also applied to the end slot\n         * because with the default or solid fills, the select is not\n         * vertically centered in the container, but the label is. This\n         * causes the slots and label to appear vertically offset from each\n         * other when the label isn't floating above the input. This doesn't\n         * apply to the outline fill, but this was not accounted for to keep\n         * things consistent.\n         *\n         * TODO(FW-5592): Remove hasStartEndSlots condition\n         */\n        const labelShouldFloat = labelPlacement === 'stacked' || (labelPlacement === 'floating' && (hasValue || isExpanded || hasStartEndSlots));\n        return (h(Host, { onClick: this.onClick, class: createColorClasses(this.color, {\n                [mode]: true,\n                'in-item': inItem,\n                'in-item-color': hostContext('ion-item.ion-color', el),\n                'select-disabled': disabled,\n                'select-expanded': isExpanded,\n                'has-expanded-icon': expandedIcon !== undefined,\n                'has-value': hasValue,\n                'label-floating': labelShouldFloat,\n                'has-placeholder': placeholder !== undefined,\n                'ion-focusable': true,\n                [`select-${rtl}`]: true,\n                [`select-fill-${fill}`]: fill !== undefined,\n                [`select-justify-${justify}`]: justifyEnabled,\n                [`select-shape-${shape}`]: shape !== undefined,\n                [`select-label-placement-${labelPlacement}`]: true,\n            }) }, h(\"label\", { class: \"select-wrapper\", id: \"select-label\" }, this.renderLabelContainer(), h(\"div\", { class: \"select-wrapper-inner\" }, h(\"slot\", { name: \"start\" }), h(\"div\", { class: \"native-wrapper\", ref: (el) => (this.nativeWrapperEl = el), part: \"container\" }, this.renderSelectText(), this.renderListbox()), h(\"slot\", { name: \"end\" }), !hasFloatingOrStackedLabel && this.renderSelectIcon()), hasFloatingOrStackedLabel && this.renderSelectIcon(), shouldRenderHighlight && h(\"div\", { class: \"select-highlight\" }))));\n    }\n    // TODO FW-3194 - Remove this\n    renderLegacySelect() {\n        if (!this.hasLoggedDeprecationWarning) {\n            printIonWarning(`ion-select now requires providing a label with either the \"label\" property or the \"aria-label\" attribute. To migrate, remove any usage of \"ion-label\" and pass the label text to either the \"label\" property or the \"aria-label\" attribute.\n\nExample: <ion-select label=\"Favorite Color\">...</ion-select>\nExample with aria-label: <ion-select aria-label=\"Favorite Color\">...</ion-select>\n\nDevelopers can use the \"legacy\" property to continue using the legacy form markup. This property will be removed in an upcoming major release of Ionic where this form control will use the modern form markup.`, this.el);\n            if (this.legacy) {\n                printIonWarning(`ion-select is being used with the \"legacy\" property enabled which will forcibly enable the legacy form markup. This property will be removed in an upcoming major release of Ionic where this form control will use the modern form markup.\n    Developers can dismiss this warning by removing their usage of the \"legacy\" property and using the new select syntax.`, this.el);\n            }\n            this.hasLoggedDeprecationWarning = true;\n        }\n        const { disabled, el, inputId, isExpanded, expandedIcon, name, placeholder, value } = this;\n        const mode = getIonMode(this);\n        const { labelText, labelId } = getAriaLabel(el, inputId);\n        renderHiddenInput(true, el, name, parseValue(value), disabled);\n        const displayValue = this.getText();\n        let selectText = displayValue;\n        if (selectText === '' && placeholder !== undefined) {\n            selectText = placeholder;\n        }\n        // If there is a label then we need to concatenate it with the\n        // current value (or placeholder) and a comma so it separates\n        // nicely when the screen reader announces it, otherwise just\n        // announce the value / placeholder\n        const displayLabel = labelText !== undefined ? (selectText !== '' ? `${selectText}, ${labelText}` : labelText) : selectText;\n        return (h(Host, { onClick: this.onClick, role: \"button\", \"aria-haspopup\": \"listbox\", \"aria-disabled\": disabled ? 'true' : null, \"aria-label\": displayLabel, class: {\n                [mode]: true,\n                'in-item': hostContext('ion-item', el),\n                'in-item-color': hostContext('ion-item.ion-color', el),\n                'select-disabled': disabled,\n                'select-expanded': isExpanded,\n                'has-expanded-icon': expandedIcon !== undefined,\n                'legacy-select': true,\n            } }, this.renderSelectText(), this.renderSelectIcon(), h(\"label\", { id: labelId }, displayLabel), this.renderListbox()));\n    }\n    /**\n     * Renders either the placeholder\n     * or the selected values based on\n     * the state of the select.\n     */\n    renderSelectText() {\n        const { placeholder } = this;\n        const displayValue = this.getText();\n        let addPlaceholderClass = false;\n        let selectText = displayValue;\n        if (selectText === '' && placeholder !== undefined) {\n            selectText = placeholder;\n            addPlaceholderClass = true;\n        }\n        const selectTextClasses = {\n            'select-text': true,\n            'select-placeholder': addPlaceholderClass,\n        };\n        const textPart = addPlaceholderClass ? 'placeholder' : 'text';\n        return (h(\"div\", { \"aria-hidden\": \"true\", class: selectTextClasses, part: textPart }, selectText));\n    }\n    /**\n     * Renders the chevron icon\n     * next to the select text.\n     */\n    renderSelectIcon() {\n        const mode = getIonMode(this);\n        const { isExpanded, toggleIcon, expandedIcon } = this;\n        let icon;\n        if (isExpanded && expandedIcon !== undefined) {\n            icon = expandedIcon;\n        }\n        else {\n            const defaultIcon = mode === 'ios' ? chevronExpand : caretDownSharp;\n            icon = toggleIcon !== null && toggleIcon !== void 0 ? toggleIcon : defaultIcon;\n        }\n        return h(\"ion-icon\", { class: \"select-icon\", part: \"icon\", \"aria-hidden\": \"true\", icon: icon });\n    }\n    get ariaLabel() {\n        var _a, _b;\n        const { placeholder, el, inputId, inheritedAttributes } = this;\n        const displayValue = this.getText();\n        const { labelText } = getAriaLabel(el, inputId);\n        const definedLabel = (_b = (_a = this.labelText) !== null && _a !== void 0 ? _a : inheritedAttributes['aria-label']) !== null && _b !== void 0 ? _b : labelText;\n        /**\n         * If developer has specified a placeholder\n         * and there is nothing selected, the selectText\n         * should have the placeholder value.\n         */\n        let renderedLabel = displayValue;\n        if (renderedLabel === '' && placeholder !== undefined) {\n            renderedLabel = placeholder;\n        }\n        /**\n         * If there is a developer-defined label,\n         * then we need to concatenate the developer label\n         * string with the current current value.\n         * The label for the control should be read\n         * before the values of the control.\n         */\n        if (definedLabel !== undefined) {\n            renderedLabel = renderedLabel === '' ? definedLabel : `${definedLabel}, ${renderedLabel}`;\n        }\n        return renderedLabel;\n    }\n    renderListbox() {\n        const { disabled, inputId, isExpanded } = this;\n        return (h(\"button\", { disabled: disabled, id: inputId, \"aria-label\": this.ariaLabel, \"aria-haspopup\": \"dialog\", \"aria-expanded\": `${isExpanded}`, onFocus: this.onFocus, onBlur: this.onBlur, ref: (focusEl) => (this.focusEl = focusEl) }));\n    }\n    render() {\n        const { legacyFormController } = this;\n        return legacyFormController.hasLegacyControl() ? this.renderLegacySelect() : this.renderSelect();\n    }\n    get el() { return getElement(this); }\n    static get watchers() { return {\n        \"disabled\": [\"styleChanged\"],\n        \"isExpanded\": [\"styleChanged\"],\n        \"placeholder\": [\"styleChanged\"],\n        \"value\": [\"styleChanged\"]\n    }; }\n};\nconst getOptionValue = (el) => {\n    const value = el.value;\n    return value === undefined ? el.textContent || '' : value;\n};\nconst parseValue = (value) => {\n    if (value == null) {\n        return undefined;\n    }\n    if (Array.isArray(value)) {\n        return value.join(',');\n    }\n    return value.toString();\n};\nconst generateText = (opts, value, compareWith) => {\n    if (value === undefined) {\n        return '';\n    }\n    if (Array.isArray(value)) {\n        return value\n            .map((v) => textForValue(opts, v, compareWith))\n            .filter((opt) => opt !== null)\n            .join(', ');\n    }\n    else {\n        return textForValue(opts, value, compareWith) || '';\n    }\n};\nconst textForValue = (opts, value, compareWith) => {\n    const selectOpt = opts.find((opt) => {\n        return compareOptions(value, getOptionValue(opt), compareWith);\n    });\n    return selectOpt ? selectOpt.textContent : null;\n};\nlet selectIds = 0;\nconst OPTION_CLASS = 'select-interface-option';\nSelect.style = {\n    ios: IonSelectIosStyle0,\n    md: IonSelectMdStyle0\n};\n\nconst selectOptionCss = \":host{display:none}\";\nconst IonSelectOptionStyle0 = selectOptionCss;\n\nconst SelectOption = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.inputId = `ion-selopt-${selectOptionIds++}`;\n        this.disabled = false;\n        this.value = undefined;\n    }\n    render() {\n        return h(Host, { key: 'abf6e85d60e815f59077910abec922826bf46eb2', role: \"option\", id: this.inputId, class: getIonMode(this) });\n    }\n    get el() { return getElement(this); }\n};\nlet selectOptionIds = 0;\nSelectOption.style = IonSelectOptionStyle0;\n\nconst selectPopoverIosCss = \".sc-ion-select-popover-ios-h ion-list.sc-ion-select-popover-ios{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0}ion-list-header.sc-ion-select-popover-ios,ion-label.sc-ion-select-popover-ios{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0}.sc-ion-select-popover-ios-h{overflow-y:auto}\";\nconst IonSelectPopoverIosStyle0 = selectPopoverIosCss;\n\nconst selectPopoverMdCss = \".sc-ion-select-popover-md-h ion-list.sc-ion-select-popover-md{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0}ion-list-header.sc-ion-select-popover-md,ion-label.sc-ion-select-popover-md{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0}.sc-ion-select-popover-md-h{overflow-y:auto}ion-list.sc-ion-select-popover-md ion-radio.sc-ion-select-popover-md::part(container){opacity:0}ion-item.sc-ion-select-popover-md{--inner-border-width:0}.item-radio-checked.sc-ion-select-popover-md{--background:rgba(var(--ion-color-primary-rgb, 56, 128, 255), 0.08);--background-focused:var(--ion-color-primary, #3880ff);--background-focused-opacity:0.2;--background-hover:var(--ion-color-primary, #3880ff);--background-hover-opacity:0.12}.item-checkbox-checked.sc-ion-select-popover-md{--background-activated:var(--ion-item-color, var(--ion-text-color, #000));--background-focused:var(--ion-item-color, var(--ion-text-color, #000));--background-hover:var(--ion-item-color, var(--ion-text-color, #000));--color:var(--ion-color-primary, #3880ff)}\";\nconst IonSelectPopoverMdStyle0 = selectPopoverMdCss;\n\nconst SelectPopover = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.header = undefined;\n        this.subHeader = undefined;\n        this.message = undefined;\n        this.multiple = undefined;\n        this.options = [];\n    }\n    findOptionFromEvent(ev) {\n        const { options } = this;\n        return options.find((o) => o.value === ev.target.value);\n    }\n    /**\n     * When an option is selected we need to get the value(s)\n     * of the selected option(s) and return it in the option\n     * handler\n     */\n    callOptionHandler(ev) {\n        const option = this.findOptionFromEvent(ev);\n        const values = this.getValues(ev);\n        if (option === null || option === void 0 ? void 0 : option.handler) {\n            safeCall(option.handler, values);\n        }\n    }\n    /**\n     * Dismisses the host popover that the `ion-select-popover`\n     * is rendered within.\n     */\n    dismissParentPopover() {\n        const popover = this.el.closest('ion-popover');\n        if (popover) {\n            popover.dismiss();\n        }\n    }\n    setChecked(ev) {\n        const { multiple } = this;\n        const option = this.findOptionFromEvent(ev);\n        // this is a popover with checkboxes (multiple value select)\n        // we need to set the checked value for this option\n        if (multiple && option) {\n            option.checked = ev.detail.checked;\n        }\n    }\n    getValues(ev) {\n        const { multiple, options } = this;\n        if (multiple) {\n            // this is a popover with checkboxes (multiple value select)\n            // return an array of all the checked values\n            return options.filter((o) => o.checked).map((o) => o.value);\n        }\n        // this is a popover with radio buttons (single value select)\n        // return the value that was clicked, otherwise undefined\n        const option = this.findOptionFromEvent(ev);\n        return option ? option.value : undefined;\n    }\n    renderOptions(options) {\n        const { multiple } = this;\n        switch (multiple) {\n            case true:\n                return this.renderCheckboxOptions(options);\n            default:\n                return this.renderRadioOptions(options);\n        }\n    }\n    renderCheckboxOptions(options) {\n        return options.map((option) => (h(\"ion-item\", { class: Object.assign({\n                // TODO FW-4784\n                'item-checkbox-checked': option.checked\n            }, getClassMap(option.cssClass)) }, h(\"ion-checkbox\", { value: option.value, disabled: option.disabled, checked: option.checked, justify: \"start\", labelPlacement: \"end\", onIonChange: (ev) => {\n                this.setChecked(ev);\n                this.callOptionHandler(ev);\n                // TODO FW-4784\n                forceUpdate(this);\n            } }, option.text))));\n    }\n    renderRadioOptions(options) {\n        const checked = options.filter((o) => o.checked).map((o) => o.value)[0];\n        return (h(\"ion-radio-group\", { value: checked, onIonChange: (ev) => this.callOptionHandler(ev) }, options.map((option) => (h(\"ion-item\", { class: Object.assign({\n                // TODO FW-4784\n                'item-radio-checked': option.value === checked\n            }, getClassMap(option.cssClass)) }, h(\"ion-radio\", { value: option.value, disabled: option.disabled, onClick: () => this.dismissParentPopover(), onKeyUp: (ev) => {\n                if (ev.key === ' ') {\n                    /**\n                     * Selecting a radio option with keyboard navigation,\n                     * either through the Enter or Space keys, should\n                     * dismiss the popover.\n                     */\n                    this.dismissParentPopover();\n                }\n            } }, option.text))))));\n    }\n    render() {\n        const { header, message, options, subHeader } = this;\n        const hasSubHeaderOrMessage = subHeader !== undefined || message !== undefined;\n        return (h(Host, { key: 'ddf45e058c75aae175f8589e3539ff152a5b47ad', class: getIonMode(this) }, h(\"ion-list\", { key: '52dbf712bf6cbdcb9d2e6223b99c67ecc90977ff' }, header !== undefined && h(\"ion-list-header\", { key: '692fc85c97591f09a2a9b0bccc8f71e97681cc09' }, header), hasSubHeaderOrMessage && (h(\"ion-item\", { key: 'ecab23444eaadc3ed21e7053d50890db1012475f' }, h(\"ion-label\", { key: '639f08137d7066fd79316f63e850ddcc6a3b54a7', class: \"ion-text-wrap\" }, subHeader !== undefined && h(\"h3\", { key: 'dc501101ac9d68b1d0ce80679b339a2b132d1ae9' }, subHeader), message !== undefined && h(\"p\", { key: '5ead8c1a2e90d29fe0f05e04a9fa65c7e9e62ca5' }, message)))), this.renderOptions(options))));\n    }\n    get el() { return getElement(this); }\n};\nSelectPopover.style = {\n    ios: IonSelectPopoverIosStyle0,\n    md: IonSelectPopoverMdStyle0\n};\n\nexport { Select as ion_select, SelectOption as ion_select_option, SelectPopover as ion_select_popover };\n", "/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { w as win } from './index-a5d50daf.js';\nimport { r as raf } from './helpers-be245865.js';\n\n/**\n * A utility to calculate the size of an outline notch\n * width relative to the content passed. This is used in\n * components such as `ion-select` with `fill=\"outline\"`\n * where we need to pass slotted HTML content. This is not\n * needed when rendering plaintext content because we can\n * render the plaintext again hidden with `opacity: 0` inside\n * of the notch. As a result we can rely on the intrinsic size\n * of the element to correctly compute the notch width. We\n * cannot do this with slotted content because we cannot project\n * it into 2 places at once.\n *\n * @internal\n * @param el: The host element\n * @param getNotchSpacerEl: A function that returns a reference to the notch spacer element inside of the component template.\n * @param getLabelSlot: A function that returns a reference to the slotted content.\n */\nconst createNotchController = (el, getNotchSpacerEl, getLabelSlot) => {\n    let notchVisibilityIO;\n    const needsExplicitNotchWidth = () => {\n        const notchSpacerEl = getNotchSpacerEl();\n        if (\n        /**\n         * If the notch is not being used\n         * then we do not need to set the notch width.\n         */\n        notchSpacerEl === undefined ||\n            /**\n             * If either the label property is being\n             * used or the label slot is not defined,\n             * then we do not need to estimate the notch width.\n             */\n            el.label !== undefined ||\n            getLabelSlot() === null) {\n            return false;\n        }\n        return true;\n    };\n    const calculateNotchWidth = () => {\n        if (needsExplicitNotchWidth()) {\n            /**\n             * Run this the frame after\n             * the browser has re-painted the host element.\n             * Otherwise, the label element may have a width\n             * of 0 and the IntersectionObserver will be used.\n             */\n            raf(() => {\n                setNotchWidth();\n            });\n        }\n    };\n    /**\n     * When using a label prop we can render\n     * the label value inside of the notch and\n     * let the browser calculate the size of the notch.\n     * However, we cannot render the label slot in multiple\n     * places so we need to manually calculate the notch dimension\n     * based on the size of the slotted content.\n     *\n     * This function should only be used to set the notch width\n     * on slotted label content. The notch width for label prop\n     * content is automatically calculated based on the\n     * intrinsic size of the label text.\n     */\n    const setNotchWidth = () => {\n        const notchSpacerEl = getNotchSpacerEl();\n        if (notchSpacerEl === undefined) {\n            return;\n        }\n        if (!needsExplicitNotchWidth()) {\n            notchSpacerEl.style.removeProperty('width');\n            return;\n        }\n        const width = getLabelSlot().scrollWidth;\n        if (\n        /**\n         * If the computed width of the label is 0\n         * and notchSpacerEl's offsetParent is null\n         * then that means the element is hidden.\n         * As a result, we need to wait for the element\n         * to become visible before setting the notch width.\n         *\n         * We do not check el.offsetParent because\n         * that can be null if the host element has\n         * position: fixed applied to it.\n         * notchSpacerEl does not have position: fixed.\n         */\n        width === 0 &&\n            notchSpacerEl.offsetParent === null &&\n            win !== undefined &&\n            'IntersectionObserver' in win) {\n            /**\n             * If there is an IO already attached\n             * then that will update the notch\n             * once the element becomes visible.\n             * As a result, there is no need to create\n             * another one.\n             */\n            if (notchVisibilityIO !== undefined) {\n                return;\n            }\n            const io = (notchVisibilityIO = new IntersectionObserver((ev) => {\n                /**\n                 * If the element is visible then we\n                 * can try setting the notch width again.\n                 */\n                if (ev[0].intersectionRatio === 1) {\n                    setNotchWidth();\n                    io.disconnect();\n                    notchVisibilityIO = undefined;\n                }\n            }, \n            /**\n             * Set the root to be the host element\n             * This causes the IO callback\n             * to be fired in WebKit as soon as the element\n             * is visible. If we used the default root value\n             * then WebKit would only fire the IO callback\n             * after any animations (such as a modal transition)\n             * finished, and there would potentially be a flicker.\n             */\n            { threshold: 0.01, root: el }));\n            io.observe(notchSpacerEl);\n            return;\n        }\n        /**\n         * If the element is visible then we can set the notch width.\n         * The notch is only visible when the label is scaled,\n         * which is why we multiply the width by 0.75 as this is\n         * the same amount the label element is scaled by in the host CSS.\n         * (See $form-control-label-stacked-scale in ionic.globals.scss).\n         */\n        notchSpacerEl.style.setProperty('width', `${width * 0.75}px`);\n    };\n    const destroy = () => {\n        if (notchVisibilityIO) {\n            notchVisibilityIO.disconnect();\n            notchVisibilityIO = undefined;\n        }\n    };\n    return {\n        calculateNotchWidth,\n        destroy,\n    };\n};\n\nexport { createNotchController as c };\n"], "names": ["h", "findItemLabel", "createLegacyFormController", "el", "controlEl", "legacyControl", "hasLegacyControl", "undefined", "hasLabelProp", "label", "hasLabelSlot", "hasAriaLabelAttribute", "hasAttribute", "shadowRoot", "legacyItemLabel", "legacy", "NAMED_LABEL_SLOT_COMPONENTS", "includes", "tagName", "querySelector", "UNNAMED_LABEL_SLOT_COMPONENTS", "textContent", "c", "r", "registerInstance", "d", "createEvent", "H", "Host", "f", "getElement", "i", "forceUpdate", "createNotchController", "isOptionSelected", "compareOptions", "k", "inheritAttributes", "focusVisibleElement", "renderHiddenInput", "e", "getAriaLabel", "p", "printIonWarning", "popoverController", "b", "actionSheetController", "a", "alertController", "s", "safeCall", "isRTL", "hostContext", "createColorClasses", "g", "getClassMap", "w", "watchForOptions", "chevronExpand", "q", "caretDownSharp", "getIonMode", "selectIosCss", "IonSelectIosStyle0", "selectMdCss", "IonSelectMdStyle0", "Select", "constructor", "hostRef", "ionChange", "ionCancel", "ion<PERSON><PERSON><PERSON>", "ionFocus", "ionBlur", "ionStyle", "inputId", "selectIds", "inheritedAttributes", "hasLoggedDeprecationWarning", "onClick", "ev", "target", "closestSlot", "closest", "setFocus", "open", "preventDefault", "onFocus", "emit", "onBlur", "isExpanded", "cancelText", "color", "compareWith", "disabled", "fill", "interface", "interfaceOptions", "justify", "labelPlacement", "multiple", "name", "okText", "placeholder", "selectedText", "toggleIcon", "expandedIcon", "shape", "value", "styleChanged", "emitStyle", "setValue", "componentWillLoad", "connectedCallback", "_this", "_asyncToGenerator", "legacyFormController", "notchController", "notchSpacerEl", "labelSlot", "updateOverlayOptions", "mutationO", "disconnectedCallback", "disconnect", "destroy", "event", "_this2", "overlay", "createOverlay", "onDid<PERSON><PERSON><PERSON>", "then", "present", "indexOfSelected", "childOpts", "map", "o", "indexOf", "selectedItem", "interactiveEl", "focus", "firstEnabledOption", "selectInterface", "console", "warn", "openActionSheet", "openPopover", "openAlert", "buttons", "createActionSheetButtons", "popover", "options", "createPopoverOptions", "inputType", "inputs", "createAlertInputs", "data", "selectValue", "actionSheetButtons", "option", "getOptionValue", "copyClasses", "Array", "from", "classList", "filter", "cls", "join", "optClass", "OPTION_CLASS", "role", "text", "cssClass", "handler", "push", "alertInputs", "type", "checked", "popoverOptions", "selected", "close", "_this3", "mode", "showBackdrop", "size", "item", "contains", "Object", "assign", "detail", "ionShadowTarget", "hasFloatingOrStackedLabel", "nativeWrapperEl", "popoverOpts", "alignment", "component", "componentProps", "header", "subHeader", "message", "create", "_this4", "actionSheetOpts", "_this5", "labelText", "get<PERSON><PERSON><PERSON>", "alertOpts", "<PERSON><PERSON><PERSON><PERSON>", "Promise", "resolve", "dismiss", "hasValue", "getText", "querySelectorAll", "generateText", "focusEl", "style", "renderLabel", "class", "<PERSON><PERSON><PERSON><PERSON>", "part", "componentDidRender", "_a", "calculateNotchWidth", "renderLabelContainer", "hasOutlineFill", "ref", "renderSelect", "justifyEnabled", "rtl", "inItem", "should<PERSON>ender<PERSON>ighlight", "hasStartEndSlots", "parseValue", "labelShouldFloat", "id", "renderSelectText", "renderListbox", "renderSelectIcon", "renderLegacySelect", "labelId", "displayValue", "selectText", "displayLabel", "addPlaceholderClass", "selectTextClasses", "textPart", "icon", "defaultIcon", "aria<PERSON><PERSON><PERSON>", "_b", "defined<PERSON>abel", "<PERSON><PERSON><PERSON><PERSON>", "render", "watchers", "isArray", "toString", "opts", "v", "textForValue", "opt", "selectOpt", "find", "ios", "md", "selectOptionCss", "IonSelectOptionStyle0", "SelectOption", "selectOptionIds", "key", "selectPopoverIosCss", "IonSelectPopoverIosStyle0", "selectPopoverMdCss", "IonSelectPopoverMdStyle0", "SelectPopover", "findOptionFromEvent", "callOptionHandler", "values", "getV<PERSON>ues", "dismissParentPopover", "setChecked", "renderOptions", "renderCheckboxOptions", "renderRadioOptions", "onIonChange", "onKeyUp", "hasSubHeaderOrMessage", "ion_select", "ion_select_option", "ion_select_popover", "win", "raf", "getNotchSpacerEl", "getLabelSlot", "notchVisibilityIO", "needsExplicitNotchWidth", "set<PERSON><PERSON>chWidth", "removeProperty", "width", "scrollWidth", "offsetParent", "io", "IntersectionObserver", "intersectionRatio", "threshold", "root", "observe", "setProperty"], "sourceRoot": "webpack:///", "x_google_ignoreList": [0, 1, 2]}