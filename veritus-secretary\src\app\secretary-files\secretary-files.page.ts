import { Component, OnInit } from '@angular/core';
import { FirebaseService, LawyerProfile } from '../services/firebase.service';

interface FileItem {
  id: string;
  name: string;
  type: string;
  size: number;
  uploadedBy: string;
  uploadedAt: Date;
  lawyerId: string;
  lawyerName: string;
  caseId?: string;
  caseName?: string;
}

@Component({
  selector: 'app-secretary-files',
  templateUrl: './secretary-files.page.html',
  styleUrls: ['./secretary-files.page.scss'],
})
export class SecretaryFilesPage implements OnInit {
  linkedLawyers: LawyerProfile[] = [];
  selectedLawyer: string = 'all';
  files: FileItem[] = [];
  filteredFiles: FileItem[] = [];
  searchTerm: string = '';
  selectedType: string = 'all';

  constructor(
    private firebaseService: FirebaseService
  ) { }

  ngOnInit() {
    this.loadLinkedLawyers();
    this.loadFiles();
  }

  async loadLinkedLawyers() {
    const currentUser = this.firebaseService.getCurrentUser();
    if (currentUser) {
      this.linkedLawyers = await this.firebaseService.getSecretaryLinkedLawyers(currentUser.uid);
    }
  }

  async loadFiles() {
    // Mock files for now since we don't have the full file service
    const mockFiles: FileItem[] = [
      {
        id: '1',
        name: 'Contract_ABC_Corp.pdf',
        type: 'pdf',
        size: 2048576, // 2MB
        uploadedBy: 'Secretary',
        uploadedAt: new Date('2024-01-20'),
        lawyerId: 'lawyer1',
        lawyerName: 'Atty. Smith',
        caseId: 'case1',
        caseName: 'Contract Dispute - ABC Corp'
      },
      {
        id: '2',
        name: 'Employment_Agreement.docx',
        type: 'docx',
        size: 1024000, // 1MB
        uploadedBy: 'Secretary',
        uploadedAt: new Date('2024-01-18'),
        lawyerId: 'lawyer2',
        lawyerName: 'Atty. Johnson',
        caseId: 'case2',
        caseName: 'Employment Case - John Doe'
      },
      {
        id: '3',
        name: 'Property_Documents.zip',
        type: 'zip',
        size: 5242880, // 5MB
        uploadedBy: 'Secretary',
        uploadedAt: new Date('2024-01-15'),
        lawyerId: 'lawyer1',
        lawyerName: 'Atty. Smith',
        caseId: 'case3',
        caseName: 'Property Settlement'
      }
    ];
    
    this.files = mockFiles.sort((a, b) => 
      new Date(b.uploadedAt).getTime() - new Date(a.uploadedAt).getTime()
    );
    
    this.filterFiles();
  }

  filterFiles() {
    this.filteredFiles = this.files.filter(file => {
      const matchesLawyer = this.selectedLawyer === 'all' || file.lawyerId === this.selectedLawyer;
      const matchesType = this.selectedType === 'all' || file.type === this.selectedType;
      const matchesSearch = !this.searchTerm || 
        file.name.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
        (file.caseName && file.caseName.toLowerCase().includes(this.searchTerm.toLowerCase()));
      
      return matchesLawyer && matchesType && matchesSearch;
    });
  }

  onLawyerChange() {
    this.filterFiles();
  }

  onTypeChange() {
    this.filterFiles();
  }

  onSearchChange() {
    this.filterFiles();
  }

  async onUploadFile() {
    if (this.linkedLawyers.length === 0) {
      alert('You need to be linked with at least one lawyer to upload files.');
      return;
    }

    // For now, just simulate file upload
    const fileName = prompt('Enter file name (with extension):');
    const lawyerName = prompt('Enter lawyer name:', this.linkedLawyers[0]?.name || '');
    const caseName = prompt('Enter case name (optional):');
    
    if (fileName && lawyerName) {
      const lawyer = this.linkedLawyers.find(l => l.name.toLowerCase().includes(lawyerName.toLowerCase()));
      
      if (!lawyer) {
        alert('Lawyer not found. Please enter a valid lawyer name.');
        return;
      }

      const fileExtension = fileName.split('.').pop()?.toLowerCase() || 'unknown';
      
      const newFile: FileItem = {
        id: Date.now().toString(),
        name: fileName,
        type: fileExtension,
        size: Math.floor(Math.random() * 5000000) + 100000, // Random size between 100KB and 5MB
        uploadedBy: 'Secretary',
        uploadedAt: new Date(),
        lawyerId: lawyer.uid,
        lawyerName: lawyer.name,
        caseName: caseName || undefined
      };

      this.files.unshift(newFile);
      this.filterFiles();
      alert('File uploaded successfully!');
    }
  }

  async onDownloadFile(file: FileItem) {
    alert(`Downloading ${file.name}...`);
    // In a real implementation, this would trigger the actual download
  }

  async onDeleteFile(file: FileItem) {
    const confirmed = confirm(`Are you sure you want to delete ${file.name}?`);
    
    if (confirmed) {
      const index = this.files.findIndex(f => f.id === file.id);
      if (index !== -1) {
        this.files.splice(index, 1);
        this.filterFiles();
        alert('File deleted successfully!');
      }
    }
  }

  formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  getFileIcon(type: string): string {
    switch (type.toLowerCase()) {
      case 'pdf': return 'document-text';
      case 'doc':
      case 'docx': return 'document';
      case 'xls':
      case 'xlsx': return 'grid';
      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'gif': return 'image';
      case 'zip':
      case 'rar': return 'archive';
      default: return 'document-outline';
    }
  }

  getFileTypeColor(type: string): string {
    switch (type.toLowerCase()) {
      case 'pdf': return 'danger';
      case 'doc':
      case 'docx': return 'primary';
      case 'xls':
      case 'xlsx': return 'success';
      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'gif': return 'warning';
      case 'zip':
      case 'rar': return 'dark';
      default: return 'medium';
    }
  }

  getTotalFiles(): number {
    return this.files.length;
  }

  getTotalSize(): string {
    const totalBytes = this.files.reduce((sum, file) => sum + file.size, 0);
    return this.formatFileSize(totalBytes);
  }
}
