import { Component, OnInit } from '@angular/core';
import { FirebaseService } from '../services/firebase.service';
import { NavController } from '@ionic/angular';

interface CalendarDay {
  date: number;
  isSelected: boolean;
  isToday: boolean;
  isOtherMonth: boolean;
  isAvailable?: boolean;
  isBooked?: boolean;
}

interface AvailabilitySlot {
  date: string;
  startTime: string;
  endTime: string;
}


interface Appointment {
  id: string;
  date: string;
  time: string;
  type: string;
  clientName?: string;
}

@Component({
  selector: 'app-calendar',
  templateUrl: './calendar.page.html',
  styleUrls: ['./calendar.page.scss'],
  standalone: false,
})
export class CalendarPage implements OnInit {
  currentDate = new Date();
  today = new Date();

  dayHeaders = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];

  monthNames = [
    'January', 'February', 'March', 'April', 'May', 'June',
    'July', 'August', 'September', 'October', 'November', 'December'
  ];

  calendarDays: CalendarDay[] = [];

  availabilitySlots: AvailabilitySlot[] = [];

  upcomingAppointments: Appointment[] = [];

  constructor(private navCtrl: NavController, private firebaseService: FirebaseService) {}
  
  async ionViewWillEnter() {
    this.availabilitySlots = await this.firebaseService.getLawyerAvailability();
    this.generateCalendar();
  }

  async saveAvailability() {
    const selected = this.calendarDays
      .filter(day => day.isAvailable && !day.isOtherMonth)
      .map(day => ({
        date: `${this.monthNames[this.currentDate.getMonth()]} ${day.date.toString().padStart(2, '0')}`,
        time: '11:00 AM'
      }));
  
    const existing = await this.firebaseService.getLawyerAvailability();
  
    // Merge without duplicates
    const all = [...existing, ...selected];
    const unique = Array.from(new Map(all.map(a => [a.date, a])).values());
  
    await this.firebaseService.saveLawyerAvailability(selected);
  
    this.navCtrl.navigateRoot('/tabs/calendar', { animated: false });
  }

  
  ngOnInit() {
    document.addEventListener('refresh-calendar', async () => {
      console.log('[Calendar] Refresh event received');
      this.availabilitySlots = await this.firebaseService.getLawyerAvailability();
      this.generateCalendar();
    });
  
    this.generateCalendar(); // initial render with no availability yet
  }
  

  generateCalendar() {
    this.calendarDays = []; // ✅ Clear before filling
    const year = this.currentDate.getFullYear();
    const month = this.currentDate.getMonth();
    const firstDay = new Date(year, month, 1);
    const lastDay = new Date(year, month + 1, 0);
    const prevMonth = new Date(year, month, 0);
  
    let firstDayOfWeek = firstDay.getDay();
    firstDayOfWeek = firstDayOfWeek === 0 ? 6 : firstDayOfWeek - 1;
  
  
    const formatDate = (d: number) => `${this.monthNames[month]} ${d.toString().padStart(2, '0')}`;
  
    // Previous month days
    for (let i = firstDayOfWeek - 1; i >= 0; i--) {
      const date = prevMonth.getDate() - i;
      this.calendarDays.push({
        date,
        isSelected: false,
        isToday: false,
        isOtherMonth: true
      });
    }
  
    // Current month days
    for (let date = 1; date <= lastDay.getDate(); date++) {
      const formatted = formatDate(date);
      const isAvailable = this.availabilitySlots.some(slot => slot.date === formatted);
      const isBooked = this.upcomingAppointments.some(appt => appt.date === formatted);
  
      this.calendarDays.push({
        date,
        isSelected: false,
        isToday: this.isToday(year, month, date),
        isOtherMonth: false,
        isAvailable,
        isBooked
      });
    }
  
    // Next month fill
    const remaining = 42 - this.calendarDays.length;
    for (let date = 1; date <= remaining; date++) {
      this.calendarDays.push({
        date,
        isSelected: false,
        isToday: false,
        isOtherMonth: true
      });
    }
  }
  
  async removeAvailability(slotToRemove: AvailabilitySlot) {
    this.availabilitySlots = this.availabilitySlots.filter(slot => slot.date !== slotToRemove.date);
    await this.firebaseService.saveLawyerAvailability(this.availabilitySlots);
    this.generateCalendar();
  }
  

  isToday(year: number, month: number, date: number): boolean {
    return year === this.today.getFullYear() &&
           month === this.today.getMonth() &&
           date === this.today.getDate();
  }

  getMonthYearDisplay(): string {
    return `${this.monthNames[this.currentDate.getMonth()]}`;
  }

  previousMonth() {
    this.currentDate = new Date(this.currentDate.getFullYear(), this.currentDate.getMonth() - 1, 1);
    this.generateCalendar();
  }

  nextMonth() {
    this.currentDate = new Date(this.currentDate.getFullYear(), this.currentDate.getMonth() + 1, 1);
    this.generateCalendar();
  }

  selectDate(day: CalendarDay) {
    if (day.isOtherMonth) return;

    // Clear previous selection
    this.calendarDays.forEach(d => d.isSelected = false);
    // Select new date
    day.isSelected = true;

    console.log('Selected date:', day.date);
  }

  onManageAvailability() {
    this.navCtrl.navigateForward('/availability');
  }

  onViewAllAppointments() {
    console.log('View all appointments clicked');
    // TODO: Navigate to appointments list
  }

  onAppointmentClick(appointment: Appointment) {
    console.log('Appointment clicked:', appointment);
    // TODO: Navigate to appointment details
  }
}
