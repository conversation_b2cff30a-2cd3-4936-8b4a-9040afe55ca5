import { Component, OnInit } from '@angular/core';

interface Transaction {
  id: string;
  amount: number;
  type: 'consultation' | 'retainer' | 'expense' | 'payment';
  description: string;
  date: Date;
  client?: string;
}

@Component({
  selector: 'app-finance',
  template: `
    <div class="finance-container">
      <div class="finance-header">
        <h1>Finance</h1>
        <button mat-raised-button color="primary" (click)="openNewTransactionDialog()">
          <mat-icon>add</mat-icon>
          New Transaction
        </button>
      </div>

      <div class="finance-content">
        <!-- Financial Chart -->
        <div class="chart-section">
          <div class="chart-header">
            <h3>Monthly Revenue</h3>
            <div class="chart-controls">
              <button mat-button [class.active]="selectedPeriod === 'month'" (click)="selectPeriod('month')">This month</button>
              <button mat-button [class.active]="selectedPeriod === 'year'" (click)="selectPeriod('year')">This year</button>
            </div>
          </div>
          
          <div class="chart-container">
            <div class="chart-canvas">
              <!-- Simple Bar Chart -->
              <div class="chart-bars">
                <div class="chart-y-axis">
                  <div class="y-label" *ngFor="let label of yAxisLabels">{{ label }}</div>
                </div>
                <div class="bars-container">
                  <div class="bar-group" *ngFor="let month of chartData; let i = index">
                    <div class="bar consultation" [style.height.%]="getBarHeight(month.consultation)"></div>
                    <div class="bar retainer" [style.height.%]="getBarHeight(month.retainer)"></div>
                    <div class="month-label">{{ month.month }}</div>
                  </div>
                </div>
              </div>
              
              <!-- Chart Legend -->
              <div class="chart-legend">
                <div class="legend-item">
                  <div class="legend-color consultation"></div>
                  <span>Consultation</span>
                </div>
                <div class="legend-item">
                  <div class="legend-color retainer"></div>
                  <span>Retainer</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Recent Transactions -->
        <div class="transactions-section">
          <div class="section-header">
            <h3>Recent Transactions</h3>
            <button mat-icon-button>
              <mat-icon>more_vert</mat-icon>
            </button>
          </div>

          <div class="transactions-list">
            <div class="transaction-item" *ngFor="let transaction of recentTransactions">
              <div class="transaction-icon" [class]="transaction.type">
                <mat-icon>{{ getTransactionIcon(transaction.type) }}</mat-icon>
              </div>
              <div class="transaction-info">
                <div class="transaction-description">{{ transaction.description }}</div>
                <div class="transaction-meta">
                  <span class="transaction-date">{{ transaction.date | date:'MMM dd, yyyy' }}</span>
                  <span class="transaction-client" *ngIf="transaction.client">• {{ transaction.client }}</span>
                </div>
              </div>
              <div class="transaction-amount" [class]="transaction.type">
                {{ formatAmount(transaction.amount) }}
              </div>
            </div>
          </div>

          <button mat-button class="view-all-btn">View All Transactions</button>
        </div>

        <!-- Financial Summary Cards -->
        <div class="summary-cards">
          <div class="summary-card">
            <div class="card-icon revenue">
              <mat-icon>trending_up</mat-icon>
            </div>
            <div class="card-content">
              <div class="card-title">Total Revenue</div>
              <div class="card-value">₱{{ formatNumber(totalRevenue) }}</div>
              <div class="card-change positive">+12.5% from last month</div>
            </div>
          </div>

          <div class="summary-card">
            <div class="card-icon expenses">
              <mat-icon>trending_down</mat-icon>
            </div>
            <div class="card-content">
              <div class="card-title">Total Expenses</div>
              <div class="card-value">₱{{ formatNumber(totalExpenses) }}</div>
              <div class="card-change negative">+5.2% from last month</div>
            </div>
          </div>

          <div class="summary-card">
            <div class="card-icon profit">
              <mat-icon>account_balance</mat-icon>
            </div>
            <div class="card-content">
              <div class="card-title">Net Profit</div>
              <div class="card-value">₱{{ formatNumber(netProfit) }}</div>
              <div class="card-change positive">+18.3% from last month</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .finance-container {
      padding: 32px;
      background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
      min-height: calc(100vh - 64px);
      position: relative;
    }

    .finance-container::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background:
        radial-gradient(circle at 25% 25%, rgba(34, 197, 94, 0.05) 0%, transparent 50%),
        radial-gradient(circle at 75% 75%, rgba(239, 68, 68, 0.03) 0%, transparent 50%);
      pointer-events: none;
    }

    .finance-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 40px;
      position: relative;
      z-index: 1;
    }

    .finance-header h1 {
      margin: 0;
      font-size: 36px;
      font-weight: 700;
      color: #1e293b;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      position: relative;
    }

    .finance-header h1::after {
      content: '';
      position: absolute;
      bottom: -8px;
      left: 0;
      width: 80px;
      height: 4px;
      background: linear-gradient(90deg, #22c55e 0%, #16a34a 100%);
      border-radius: 2px;
    }

    .finance-content {
      display: grid;
      grid-template-columns: 2fr 1fr;
      grid-template-rows: auto auto;
      gap: 24px;
    }

    .chart-section {
      background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
      border-radius: 24px;
      padding: 32px;
      box-shadow:
        0 20px 60px rgba(0, 0, 0, 0.1),
        0 8px 24px rgba(0, 0, 0, 0.05);
      grid-row: span 2;
      border: 1px solid rgba(226, 232, 240, 0.8);
      position: relative;
      overflow: hidden;
      transition: all 0.3s ease;
    }

    .chart-section::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 6px;
      background: linear-gradient(90deg, #22c55e 0%, #16a34a 50%, #22c55e 100%);
    }

    .chart-section:hover {
      transform: translateY(-4px);
      box-shadow:
        0 30px 80px rgba(0, 0, 0, 0.15),
        0 12px 32px rgba(0, 0, 0, 0.08);
    }

    .chart-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 24px;
    }

    .chart-header h3 {
      margin: 0;
      font-size: 18px;
      font-weight: 600;
    }

    .chart-controls {
      display: flex;
      gap: 8px;
    }

    .chart-controls button {
      padding: 8px 16px;
      border-radius: 20px;
      font-size: 12px;
    }

    .chart-controls button.active {
      background: #1976d2;
      color: white;
    }

    .chart-container {
      height: 300px;
      position: relative;
    }

    .chart-canvas {
      height: 100%;
      display: flex;
      flex-direction: column;
    }

    .chart-bars {
      flex: 1;
      display: flex;
      align-items: flex-end;
      gap: 16px;
    }

    .chart-y-axis {
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      height: 250px;
      margin-right: 16px;
    }

    .y-label {
      font-size: 12px;
      color: #666;
      text-align: right;
    }

    .bars-container {
      flex: 1;
      display: flex;
      justify-content: space-between;
      align-items: flex-end;
      height: 250px;
    }

    .bar-group {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 4px;
      flex: 1;
      max-width: 60px;
    }

    .bar {
      width: 20px;
      border-radius: 4px 4px 0 0;
      transition: all 0.3s ease;
    }

    .bar.consultation {
      background: #e91e63;
      margin-right: 2px;
    }

    .bar.retainer {
      background: #9c27b0;
      margin-left: 2px;
    }

    .month-label {
      font-size: 11px;
      color: #666;
      margin-top: 8px;
      writing-mode: vertical-rl;
      text-orientation: mixed;
    }

    .chart-legend {
      display: flex;
      justify-content: center;
      gap: 24px;
      margin-top: 16px;
    }

    .legend-item {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 12px;
    }

    .legend-color {
      width: 12px;
      height: 12px;
      border-radius: 2px;
    }

    .legend-color.consultation {
      background: #e91e63;
    }

    .legend-color.retainer {
      background: #9c27b0;
    }

    .transactions-section {
      background: white;
      border-radius: 12px;
      padding: 24px;
      box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }

    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
    }

    .section-header h3 {
      margin: 0;
      font-size: 18px;
      font-weight: 600;
    }

    .transactions-list {
      display: flex;
      flex-direction: column;
      gap: 16px;
      margin-bottom: 20px;
    }

    .transaction-item {
      display: flex;
      align-items: center;
      gap: 16px;
    }

    .transaction-icon {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .transaction-icon.consultation {
      background: #e3f2fd;
      color: #1976d2;
    }

    .transaction-icon.retainer {
      background: #f3e5f5;
      color: #7b1fa2;
    }

    .transaction-icon.expense {
      background: #ffebee;
      color: #d32f2f;
    }

    .transaction-info {
      flex: 1;
    }

    .transaction-description {
      font-weight: 500;
      margin-bottom: 4px;
    }

    .transaction-meta {
      font-size: 12px;
      color: #666;
    }

    .transaction-amount {
      font-weight: 600;
      font-size: 14px;
    }

    .transaction-amount.consultation,
    .transaction-amount.retainer {
      color: #2e7d32;
    }

    .transaction-amount.expense {
      color: #d32f2f;
    }

    .view-all-btn {
      width: 100%;
      color: #1976d2;
    }

    .summary-cards {
      display: flex;
      flex-direction: column;
      gap: 16px;
    }

    .summary-card {
      background: white;
      border-radius: 12px;
      padding: 20px;
      box-shadow: 0 2px 8px rgba(0,0,0,0.1);
      display: flex;
      align-items: center;
      gap: 16px;
    }

    .card-icon {
      width: 50px;
      height: 50px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .card-icon.revenue {
      background: #e8f5e8;
      color: #2e7d32;
    }

    .card-icon.expenses {
      background: #ffebee;
      color: #d32f2f;
    }

    .card-icon.profit {
      background: #e3f2fd;
      color: #1976d2;
    }

    .card-content {
      flex: 1;
    }

    .card-title {
      font-size: 12px;
      color: #666;
      margin-bottom: 4px;
    }

    .card-value {
      font-size: 18px;
      font-weight: 600;
      margin-bottom: 4px;
    }

    .card-change {
      font-size: 11px;
    }

    .card-change.positive {
      color: #2e7d32;
    }

    .card-change.negative {
      color: #d32f2f;
    }

    @media (max-width: 1024px) {
      .finance-content {
        grid-template-columns: 1fr;
        grid-template-rows: auto auto auto;
      }

      .chart-section {
        grid-row: span 1;
      }

      .summary-cards {
        flex-direction: row;
      }
    }

    @media (max-width: 768px) {
      .finance-header {
        flex-direction: column;
        gap: 16px;
        align-items: stretch;
      }

      .summary-cards {
        flex-direction: column;
      }

      .chart-header {
        flex-direction: column;
        gap: 16px;
        align-items: stretch;
      }
    }
  `]
})
export class FinanceComponent implements OnInit {
  selectedPeriod = 'month';
  totalRevenue = 125000;
  totalExpenses = 45000;
  netProfit = 80000;

  yAxisLabels = ['100', '75', '50', '25', '0'];

  chartData = [
    { month: '1', consultation: 45, retainer: 30 },
    { month: '2', consultation: 65, retainer: 40 },
    { month: '3', consultation: 35, retainer: 25 },
    { month: '4', consultation: 80, retainer: 60 },
    { month: '5', consultation: 55, retainer: 45 },
    { month: '6', consultation: 70, retainer: 50 },
    { month: '7', consultation: 90, retainer: 65 },
    { month: '8', consultation: 60, retainer: 40 },
    { month: '9', consultation: 75, retainer: 55 }
  ];

  recentTransactions: Transaction[] = [
    {
      id: '1',
      amount: 15000,
      type: 'consultation',
      description: 'Consultation',
      date: new Date(2025, 5, 15), // June 15, 2025
      client: 'John Doe'
    },
    {
      id: '2',
      amount: 5000,
      type: 'retainer',
      description: 'Retainer',
      date: new Date(2025, 5, 16), // June 16, 2025
      client: 'Jane Smith'
    },
    {
      id: '3',
      amount: 2500,
      type: 'expense',
      description: 'Office Supplies',
      date: new Date(2025, 5, 14), // June 14, 2025
    }
  ];

  ngOnInit() {
    // Component initialization
  }

  selectPeriod(period: string) {
    this.selectedPeriod = period;
    // Update chart data based on selected period
  }

  getBarHeight(value: number): number {
    return (value / 100) * 100; // Convert to percentage
  }

  getTransactionIcon(type: string): string {
    switch (type) {
      case 'consultation': return 'person';
      case 'retainer': return 'account_balance_wallet';
      case 'expense': return 'shopping_cart';
      case 'payment': return 'payment';
      default: return 'attach_money';
    }
  }

  formatAmount(amount: number): string {
    const prefix = amount >= 0 ? '+' : '';
    return `${prefix}₱${amount.toLocaleString()}`;
  }

  formatNumber(num: number): string {
    return num.toLocaleString();
  }

  openNewTransactionDialog() {
    // Implement new transaction dialog
  }
}
