import { Injectable } from '@angular/core';
import { AuthService, LawyerProfile, SecretaryProfile, ClientProfile } from './auth.service';
import { ProfileService } from './profile.service';

export interface OnboardingStep {
  id: string;
  title: string;
  description: string;
  isCompleted: boolean;
  isRequired: boolean;
}

export interface LawyerOnboardingData {
  name: string;
  specialization: string[];
  rollNumber?: string;
  barId?: string;
  firmName?: string;
  phone?: string;
  profilePhoto?: File;
  photoId?: File;
}

export interface SecretaryOnboardingData {
  name: string;
  phone?: string;
  lawyerCode?: string;
  accessLevel: 'basic' | 'advanced' | 'full';
  profilePhoto?: File;
}

export interface ClientOnboardingData {
  name: string;
  phone?: string;
  profilePhoto?: File;
}

@Injectable({
  providedIn: 'root'
})
export class OnboardingService {

  constructor(
    private authService: AuthService,
    private profileService: ProfileService
  ) { }

  // Get onboarding steps for each role
  getLawyerOnboardingSteps(): OnboardingStep[] {
    return [
      {
        id: 'basic-info',
        title: 'Basic Information',
        description: 'Enter your name and contact details',
        isCompleted: false,
        isRequired: true
      },
      {
        id: 'professional-info',
        title: 'Professional Details',
        description: 'Add specialization and credentials',
        isCompleted: false,
        isRequired: true
      },
      {
        id: 'verification',
        title: 'Identity Verification',
        description: 'Upload photo ID for verification',
        isCompleted: false,
        isRequired: true
      },
      {
        id: 'profile-photo',
        title: 'Profile Photo',
        description: 'Add a professional profile photo',
        isCompleted: false,
        isRequired: false
      }
    ];
  }

  getSecretaryOnboardingSteps(): OnboardingStep[] {
    return [
      {
        id: 'basic-info',
        title: 'Basic Information',
        description: 'Enter your name and contact details',
        isCompleted: false,
        isRequired: true
      },
      {
        id: 'lawyer-linking',
        title: 'Link to Lawyer',
        description: 'Enter lawyer code to request access',
        isCompleted: false,
        isRequired: true
      },
      {
        id: 'access-level',
        title: 'Access Permissions',
        description: 'Choose your access level',
        isCompleted: false,
        isRequired: true
      },
      {
        id: 'profile-photo',
        title: 'Profile Photo',
        description: 'Add a profile photo',
        isCompleted: false,
        isRequired: false
      }
    ];
  }

  getClientOnboardingSteps(): OnboardingStep[] {
    return [
      {
        id: 'basic-info',
        title: 'Basic Information',
        description: 'Enter your name and contact details',
        isCompleted: false,
        isRequired: true
      },
      {
        id: 'profile-photo',
        title: 'Profile Photo',
        description: 'Add a profile photo',
        isCompleted: false,
        isRequired: false
      }
    ];
  }

  // Complete onboarding for each role
  async completeLawyerOnboarding(uid: string, data: LawyerOnboardingData): Promise<void> {
    let profilePhotoUrl: string | undefined;
    let photoIdUrl: string | undefined;

    // Upload files if provided
    if (data.profilePhoto) {
      profilePhotoUrl = await this.profileService.uploadProfilePhoto(uid, data.profilePhoto);
    }

    if (data.photoId) {
      photoIdUrl = await this.profileService.uploadPhotoId(uid, data.photoId);
    }

    // Create lawyer profile
    const lawyerProfile: LawyerProfile = {
      uid,
      email: this.authService.getCurrentUser()?.email || '',
      name: data.name,
      role: 'lawyer',
      specialization: data.specialization,
      rollNumber: data.rollNumber,
      barId: data.barId,
      firmName: data.firmName,
      phone: data.phone,
      avatar: profilePhotoUrl,
      photoIdUrl,
      secretaryCode: this.authService.generateSecretaryCode(),
      isVerified: false, // Will be verified manually
      createdAt: new Date(),
      updatedAt: new Date()
    };

    await this.profileService.createLawyerProfile(lawyerProfile);
    
    // Update base user profile
    await this.authService.updateUserProfile(uid, {
      name: data.name,
      phone: data.phone,
      avatar: profilePhotoUrl
    });
  }

  async completeSecretaryOnboarding(uid: string, data: SecretaryOnboardingData): Promise<void> {
    let profilePhotoUrl: string | undefined;

    // Upload profile photo if provided
    if (data.profilePhoto) {
      profilePhotoUrl = await this.profileService.uploadProfilePhoto(uid, data.profilePhoto);
    }

    // Find lawyer by code
    let linkedLawyers: string[] = [];
    if (data.lawyerCode) {
      const lawyer = await this.profileService.findLawyerByCode(data.lawyerCode);
      if (lawyer) {
        linkedLawyers = [lawyer.uid];
        // Create link request
        await this.profileService.requestSecretaryLink(uid, lawyer.uid, data.accessLevel);
      }
    }

    // Set permissions based on access level
    const permissions = this.getPermissionsByAccessLevel(data.accessLevel);

    // Create secretary profile
    const secretaryProfile: SecretaryProfile = {
      uid,
      email: this.authService.getCurrentUser()?.email || '',
      name: data.name,
      role: 'secretary',
      phone: data.phone,
      avatar: profilePhotoUrl,
      linkedLawyers,
      lawyerCode: data.lawyerCode,
      accessLevel: data.accessLevel,
      isApproved: false, // Requires lawyer approval
      permissions,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    await this.profileService.createSecretaryProfile(secretaryProfile);
    
    // Update base user profile
    await this.authService.updateUserProfile(uid, {
      name: data.name,
      phone: data.phone,
      avatar: profilePhotoUrl
    });
  }

  async completeClientOnboarding(uid: string, data: ClientOnboardingData): Promise<void> {
    let profilePhotoUrl: string | undefined;

    // Upload profile photo if provided
    if (data.profilePhoto) {
      profilePhotoUrl = await this.profileService.uploadProfilePhoto(uid, data.profilePhoto);
    }

    // Create client profile
    const clientProfile: ClientProfile = {
      uid,
      email: this.authService.getCurrentUser()?.email || '',
      name: data.name,
      role: 'client',
      phone: data.phone,
      avatar: profilePhotoUrl,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    await this.profileService.createClientProfile(clientProfile);
    
    // Update base user profile
    await this.authService.updateUserProfile(uid, {
      name: data.name,
      phone: data.phone,
      avatar: profilePhotoUrl
    });
  }

  // Helper method to get permissions by access level
  private getPermissionsByAccessLevel(accessLevel: string) {
    switch (accessLevel) {
      case 'basic':
        return {
          canManageCalendar: true,
          canManageFiles: false,
          canManageCases: false,
          canManageRetainers: false,
          canViewFinances: false,
          canManageFinances: false
        };
      case 'advanced':
        return {
          canManageCalendar: true,
          canManageFiles: true,
          canManageCases: true,
          canManageRetainers: true,
          canViewFinances: false,
          canManageFinances: false
        };
      case 'full':
        return {
          canManageCalendar: true,
          canManageFiles: true,
          canManageCases: true,
          canManageRetainers: true,
          canViewFinances: true,
          canManageFinances: true
        };
      default:
        return {
          canManageCalendar: false,
          canManageFiles: false,
          canManageCases: false,
          canManageRetainers: false,
          canViewFinances: false,
          canManageFinances: false
        };
    }
  }
}
