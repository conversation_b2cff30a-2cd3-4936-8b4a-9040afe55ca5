<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Enhanced Secretary Dash<PERSON> - Demo</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
            color: white;
            min-height: 100vh;
            padding: 20px;
        }

        .dashboard-container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .dashboard-header {
            text-align: center;
            margin-bottom: 30px;
        }

        .greeting {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 8px;
        }

        .sub-greeting {
            opacity: 0.8;
            font-size: 1rem;
        }

        /* Profile Card Styles */
        .profile-card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 16px;
            padding: 24px;
            margin-bottom: 30px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .profile-header {
            display: flex;
            align-items: flex-start;
            gap: 16px;
            margin-bottom: 20px;
        }

        .profile-avatar {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.1);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 36px;
            color: #d4af37;
        }

        .profile-info {
            flex: 1;
        }

        .profile-name {
            font-size: 1.25rem;
            font-weight: bold;
            margin-bottom: 4px;
        }

        .profile-email, .profile-phone {
            color: rgba(255, 255, 255, 0.7);
            font-size: 0.875rem;
            margin-bottom: 2px;
        }

        .profile-badges {
            display: flex;
            gap: 8px;
            margin-top: 8px;
        }

        .role-badge, .permissions-badge {
            background: rgba(212, 175, 55, 0.2);
            color: #d4af37;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }

        .profile-edit-btn {
            background: rgba(255, 255, 255, 0.1);
            border: none;
            border-radius: 8px;
            padding: 8px;
            color: #d4af37;
            cursor: pointer;
            font-size: 20px;
        }

        .profile-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 16px;
            padding-top: 16px;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
        }

        .profile-stat {
            text-align: center;
        }

        .profile-stat .stat-value {
            display: block;
            font-size: 18px;
            font-weight: bold;
            color: #d4af37;
            margin-bottom: 4px;
        }

        .profile-stat .stat-label {
            font-size: 12px;
            color: rgba(255, 255, 255, 0.7);
        }

        /* Statistics Cards */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            padding: 20px;
            text-align: center;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .stat-icon {
            margin-bottom: 10px;
            font-size: 24px;
            color: #d4af37;
        }

        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .stat-label {
            font-size: 0.875rem;
            color: rgba(255, 255, 255, 0.7);
        }

        /* Section Styles */
        .section {
            margin-bottom: 30px;
        }

        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }

        .section-title {
            font-size: 1.125rem;
            font-weight: 600;
        }

        .link-btn {
            color: #d4af37;
            text-decoration: none;
            font-size: 0.875rem;
            font-weight: 500;
        }

        /* Enhanced Lawyer Card Styles */
        .lawyer-cards {
            display: flex;
            flex-direction: column;
            gap: 16px;
        }

        .enhanced-lawyer-card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 16px;
            padding: 20px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .enhanced-lawyer-card:hover {
            background: rgba(255, 255, 255, 0.15);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
        }

        .lawyer-header {
            display: flex;
            align-items: flex-start;
            gap: 16px;
            margin-bottom: 16px;
        }

        .lawyer-avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.1);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 28px;
            color: #d4af37;
            flex-shrink: 0;
        }

        .lawyer-basic-info {
            flex: 1;
        }

        .lawyer-name {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 4px;
        }

        .lawyer-email {
            color: rgba(255, 255, 255, 0.7);
            font-size: 12px;
            margin-bottom: 6px;
        }

        .lawyer-credentials {
            display: flex;
            gap: 12px;
            flex-wrap: wrap;
        }

        .credential-item {
            background: rgba(212, 175, 55, 0.2);
            color: #d4af37;
            padding: 2px 8px;
            border-radius: 8px;
            font-size: 11px;
            font-weight: 500;
        }

        .lawyer-status {
            display: flex;
            flex-direction: column;
            align-items: flex-end;
            gap: 8px;
        }

        .status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 500;
            background: rgba(34, 197, 94, 0.2);
            color: #22c55e;
        }

        .chevron-icon {
            color: #d4af37;
            font-size: 18px;
        }

        .lawyer-stats-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 12px;
            margin-bottom: 16px;
            padding: 12px 0;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .lawyer-stat {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .lawyer-stat .stat-icon {
            font-size: 16px;
            color: #d4af37;
            margin-bottom: 0;
        }

        .lawyer-stat .stat-content {
            display: flex;
            flex-direction: column;
        }

        .lawyer-stat .stat-number {
            font-size: 14px;
            font-weight: 600;
            line-height: 1;
            margin-bottom: 0;
        }

        .lawyer-stat .stat-label {
            font-size: 11px;
            color: rgba(255, 255, 255, 0.6);
            line-height: 1;
        }

        .lawyer-permissions .permissions-title {
            color: rgba(255, 255, 255, 0.8);
            margin-bottom: 8px;
            font-size: 12px;
            font-weight: 600;
        }

        .permissions-list {
            display: flex;
            flex-wrap: wrap;
            gap: 6px;
        }

        .permission-tag {
            background: rgba(212, 175, 55, 0.15);
            color: #d4af37;
            padding: 3px 8px;
            border-radius: 10px;
            font-size: 10px;
            font-weight: 500;
            border: 1px solid rgba(212, 175, 55, 0.3);
        }

        @media (max-width: 768px) {
            .profile-header {
                flex-direction: column;
                text-align: center;
            }

            .lawyer-header {
                flex-direction: column;
                text-align: center;
            }

            .lawyer-stats-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="dashboard-container">
        <!-- Header -->
        <div class="dashboard-header">
            <h1 class="greeting">Good morning, Sarah Johnson!</h1>
            <p class="sub-greeting">Manage your lawyers' workspaces efficiently</p>
        </div>

        <!-- Secretary Profile Card -->
        <div class="profile-card">
            <div class="profile-header">
                <div class="profile-avatar">👤</div>
                <div class="profile-info">
                    <h2 class="profile-name">Sarah Johnson</h2>
                    <p class="profile-email"><EMAIL></p>
                    <p class="profile-phone">+****************</p>
                    <div class="profile-badges">
                        <span class="role-badge">Secretary</span>
                        <span class="permissions-badge">4 Permissions</span>
                    </div>
                </div>
                <button class="profile-edit-btn">✏️</button>
            </div>
            
            <div class="profile-stats">
                <div class="profile-stat">
                    <span class="stat-value">2</span>
                    <span class="stat-label">Linked Lawyers</span>
                </div>
                <div class="profile-stat">
                    <span class="stat-value">11 months ago</span>
                    <span class="stat-label">Member Since</span>
                </div>
                <div class="profile-stat">
                    <span class="stat-value">4/6</span>
                    <span class="stat-label">Active Permissions</span>
                </div>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-icon">👥</div>
                <h3 class="stat-number">2</h3>
                <p class="stat-label">Linked Lawyers</p>
            </div>
            <div class="stat-card">
                <div class="stat-icon">💼</div>
                <h3 class="stat-number">20</h3>
                <p class="stat-label">Total Cases</p>
            </div>
            <div class="stat-card">
                <div class="stat-icon">📅</div>
                <h3 class="stat-number">13</h3>
                <p class="stat-label">Appointments</p>
            </div>
        </div>

        <!-- Linked Lawyers Section -->
        <div class="section">
            <div class="section-header">
                <h2 class="section-title">Linked Lawyers (2)</h2>
                <a href="#" class="link-btn">+ Link New</a>
            </div>
            
            <div class="lawyer-cards">
                <!-- Lawyer 1 -->
                <div class="enhanced-lawyer-card">
                    <div class="lawyer-header">
                        <div class="lawyer-avatar">⚖️</div>
                        <div class="lawyer-basic-info">
                            <h3 class="lawyer-name">Attorney John Smith</h3>
                            <p class="lawyer-email"><EMAIL></p>
                            <div class="lawyer-credentials">
                                <span class="credential-item">Roll: BAR12345</span>
                                <span class="credential-item">Bar: NY-67890</span>
                            </div>
                        </div>
                        <div class="lawyer-status">
                            <span class="status-badge">Active</span>
                            <span class="chevron-icon">→</span>
                        </div>
                    </div>
                    
                    <div class="lawyer-stats-grid">
                        <div class="lawyer-stat">
                            <span class="stat-icon">💼</span>
                            <div class="stat-content">
                                <span class="stat-number">12</span>
                                <span class="stat-label">Cases</span>
                            </div>
                        </div>
                        <div class="lawyer-stat">
                            <span class="stat-icon">📅</span>
                            <div class="stat-content">
                                <span class="stat-number">8</span>
                                <span class="stat-label">Appointments</span>
                            </div>
                        </div>
                        <div class="lawyer-stat">
                            <span class="stat-icon">⏰</span>
                            <div class="stat-content">
                                <span class="stat-number">2h ago</span>
                                <span class="stat-label">Last Active</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="lawyer-permissions">
                        <h4 class="permissions-title">Your Permissions:</h4>
                        <div class="permissions-list">
                            <span class="permission-tag">Calendar</span>
                            <span class="permission-tag">Files</span>
                            <span class="permission-tag">Cases</span>
                            <span class="permission-tag">View Finances</span>
                        </div>
                    </div>
                </div>

                <!-- Lawyer 2 -->
                <div class="enhanced-lawyer-card">
                    <div class="lawyer-header">
                        <div class="lawyer-avatar">⚖️</div>
                        <div class="lawyer-basic-info">
                            <h3 class="lawyer-name">Attorney Maria Rodriguez</h3>
                            <p class="lawyer-email"><EMAIL></p>
                            <div class="lawyer-credentials">
                                <span class="credential-item">Roll: BAR54321</span>
                                <span class="credential-item">Bar: CA-12345</span>
                            </div>
                        </div>
                        <div class="lawyer-status">
                            <span class="status-badge">Active</span>
                            <span class="chevron-icon">→</span>
                        </div>
                    </div>
                    
                    <div class="lawyer-stats-grid">
                        <div class="lawyer-stat">
                            <span class="stat-icon">💼</span>
                            <div class="stat-content">
                                <span class="stat-number">8</span>
                                <span class="stat-label">Cases</span>
                            </div>
                        </div>
                        <div class="lawyer-stat">
                            <span class="stat-icon">📅</span>
                            <div class="stat-content">
                                <span class="stat-number">5</span>
                                <span class="stat-label">Appointments</span>
                            </div>
                        </div>
                        <div class="lawyer-stat">
                            <span class="stat-icon">⏰</span>
                            <div class="stat-content">
                                <span class="stat-number">1d ago</span>
                                <span class="stat-label">Last Active</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="lawyer-permissions">
                        <h4 class="permissions-title">Your Permissions:</h4>
                        <div class="permissions-list">
                            <span class="permission-tag">Calendar</span>
                            <span class="permission-tag">Cases</span>
                            <span class="permission-tag">Retainers</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
