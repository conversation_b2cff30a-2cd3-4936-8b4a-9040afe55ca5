"use strict";(self.webpackChunkveritus_secretary=self.webpackChunkveritus_secretary||[]).push([[1711],{1711:(k,m,c)=>{c.r(m),c.d(m,{SecretaryCasesPageModule:()=>P});var g=c(177),p=c(9417),o=c(2276),C=c(8498),u=c(467),e=c(4438),h=c(8287);function y(a,d){if(1&a&&(e.j41(0,"ion-select-option",28),e.EFF(1),e.k0s()),2&a){const t=d.$implicit;e.Y8G("value",t.uid),e.R7$(),e.SpI(" ",t.name," ")}}function b(a,d){if(1&a){const t=e.RV6();e.j41(0,"div",31)(1,"div",32),e.nrm(2,"ion-icon",33),e.k0s(),e.j41(3,"div",34)(4,"h3",35),e.<PERSON><PERSON>(5),e.k0s(),e.j41(6,"p",36),e.EFF(7),e.k0s(),e.j41(8,"p",37),e.EFF(9),e.k0s(),e.j41(10,"p",38),e.EFF(11),e.k0s(),e.j41(12,"div",39)(13,"span",40),e.nrm(14,"ion-icon",41),e.EFF(15),e.k0s(),e.j41(16,"span",42),e.EFF(17),e.nI1(18,"date"),e.k0s()()(),e.j41(19,"div",43)(20,"button",44),e.bIt("click",function(){const n=e.eBV(t).$implicit,i=e.XpG(2);return e.Njj(i.onUpdateCaseProgress(n))}),e.nrm(21,"ion-icon",45),e.k0s()()()}if(2&a){const t=d.$implicit,s=e.XpG(2);e.R7$(2),e.Y8G("name",s.getStatusIcon(t.status))("color",s.getStatusColor(t.status)),e.R7$(3),e.SpI(" ",t.title," "),e.R7$(2),e.JRh(t.clientName),e.R7$(2),e.JRh(t.description),e.R7$(2),e.JRh(t.lawyerName),e.R7$(4),e.SpI(" ",t.fileCount," files "),e.R7$(2),e.SpI(" Updated ",e.i5U(18,8,t.updatedAt,"short")," ")}}function v(a,d){if(1&a&&(e.j41(0,"div",29),e.DNE(1,b,22,11,"div",30),e.k0s()),2&a){const t=e.XpG();e.R7$(),e.Y8G("ngForOf",t.filteredCases)}}function _(a,d){if(1&a){const t=e.RV6();e.j41(0,"div",46),e.nrm(1,"ion-icon",47),e.j41(2,"h3",48),e.EFF(3,"No Cases Found"),e.k0s(),e.j41(4,"p",49),e.EFF(5," No cases match your current filters. "),e.k0s(),e.j41(6,"button",50),e.bIt("click",function(){e.eBV(t);const n=e.XpG();return e.Njj(n.onCreateCase())}),e.EFF(7," Create First Case "),e.k0s()()}}const x=[{path:"",component:(()=>{class a{constructor(t){this.firebaseService=t,this.linkedLawyers=[],this.selectedLawyer="all",this.cases=[],this.filteredCases=[],this.searchTerm="",this.selectedStatus="all"}ngOnInit(){this.loadLinkedLawyers(),this.loadCases()}loadLinkedLawyers(){var t=this;return(0,u.A)(function*(){const s=t.firebaseService.getCurrentUser();s&&(t.linkedLawyers=yield t.firebaseService.getSecretaryLinkedLawyers(s.uid))})()}loadCases(){var t=this;return(0,u.A)(function*(){t.cases=[{id:"1",title:"Contract Dispute - ABC Corp",clientName:"ABC Corporation",description:"Contract dispute regarding service delivery terms",lawyerId:"lawyer1",lawyerName:"Atty. Smith",status:"ongoing",fileCount:5,createdAt:new Date("2024-01-15"),updatedAt:new Date("2024-01-20")},{id:"2",title:"Employment Case - John Doe",clientName:"John Doe",description:"Wrongful termination case",lawyerId:"lawyer2",lawyerName:"Atty. Johnson",status:"pending",fileCount:3,createdAt:new Date("2024-01-10"),updatedAt:new Date("2024-01-18")},{id:"3",title:"Property Settlement",clientName:"Jane Smith",description:"Divorce property settlement case",lawyerId:"lawyer1",lawyerName:"Atty. Smith",status:"closed",fileCount:8,createdAt:new Date("2023-12-01"),updatedAt:new Date("2024-01-05")}].sort((n,i)=>new Date(i.updatedAt).getTime()-new Date(n.updatedAt).getTime()),t.filterCases()})()}filterCases(){this.filteredCases=this.cases.filter(t=>{const s="all"===this.selectedLawyer||t.lawyerId===this.selectedLawyer,n="all"===this.selectedStatus||t.status===this.selectedStatus,i=!this.searchTerm||t.title.toLowerCase().includes(this.searchTerm.toLowerCase())||t.clientName.toLowerCase().includes(this.searchTerm.toLowerCase());return s&&n&&i})}onLawyerChange(){this.filterCases()}onStatusChange(){this.filterCases()}onSearchChange(){this.filterCases()}onCreateCase(){var t=this;return(0,u.A)(function*(){if(0===t.linkedLawyers.length)return void alert("You need to be linked with at least one lawyer to create cases.");const s=prompt("Enter case title:"),n=prompt("Enter client name:"),i=prompt("Enter case description:"),r=prompt("Enter lawyer name:",t.linkedLawyers[0]?.name||"");if(s&&n&&i&&r){const l=t.linkedLawyers.find(M=>M.name.toLowerCase().includes(r.toLowerCase()));if(!l)return void alert("Lawyer not found. Please enter a valid lawyer name.");const f={id:Date.now().toString(),title:s,clientName:n,description:i,lawyerId:l.uid,lawyerName:l.name,status:"ongoing",fileCount:0,createdAt:new Date,updatedAt:new Date};t.cases.unshift(f),t.filterCases(),alert("Case created successfully!")}})()}onUpdateCaseProgress(t){var s=this;return(0,u.A)(function*(){const n=prompt("Enter status (ongoing/closed/pending):",t.status),i=prompt("Enter updated description:",t.description);if(prompt("Enter progress note:"),n&&i){const l=s.cases.findIndex(f=>f.id===t.id);-1!==l&&(s.cases[l]={...t,status:n,description:i,updatedAt:new Date},s.filterCases(),alert("Case progress updated successfully!"))}})()}getStatusColor(t){switch(t){case"ongoing":return"primary";case"closed":return"success";case"pending":return"warning";default:return"medium"}}getStatusIcon(t){switch(t){case"ongoing":return"play-circle";case"closed":return"checkmark-circle";case"pending":return"time";default:return"help-circle"}}getOngoingCount(){return this.cases.filter(t=>"ongoing"===t.status).length}getClosedCount(){return this.cases.filter(t=>"closed"===t.status).length}static{this.\u0275fac=function(s){return new(s||a)(e.rXU(h.f))}}static{this.\u0275cmp=e.VBU({type:a,selectors:[["app-secretary-cases"]],decls:54,vars:10,consts:[[1,"veritus-toolbar"],[1,"veritus-text-white","veritus-font-semibold"],[1,"cases-content","veritus-gradient-bg"],[1,"cases-container","veritus-safe-area-top"],[1,"filters-section"],[1,"filter-row"],[1,"filter-item"],[1,"filter-label","veritus-text-sm","veritus-text-white"],["interface","popover",1,"lawyer-select",3,"ngModelChange","ionChange","ngModel"],["value","all"],[3,"value",4,"ngFor","ngForOf"],["interface","popover",1,"status-select",3,"ngModelChange","ionChange","ngModel"],["value","ongoing"],["value","pending"],["value","closed"],[1,"search-section"],["placeholder","Search cases...",1,"custom-searchbar",3,"ngModelChange","ionInput","ngModel"],[1,"action-section"],[1,"veritus-btn-primary","create-btn",3,"click"],["name","add",1,"btn-icon"],[1,"cases-section"],[1,"section-title","veritus-text-lg","veritus-font-semibold","veritus-text-white"],["class","cases-list",4,"ngIf"],["class","empty-state",4,"ngIf"],[1,"stats-section"],[1,"stat-card"],[1,"stat-number","veritus-text-xl","veritus-font-bold","veritus-text-white"],[1,"stat-label","veritus-text-sm","veritus-text-gray"],[3,"value"],[1,"cases-list"],["class","case-card",4,"ngFor","ngForOf"],[1,"case-card"],[1,"case-status"],[3,"name","color"],[1,"case-details"],[1,"case-title","veritus-text-base","veritus-font-semibold","veritus-text-white"],[1,"case-client","veritus-text-sm","veritus-text-gold"],[1,"case-description","veritus-text-sm","veritus-text-gray"],[1,"case-lawyer","veritus-text-sm","veritus-text-blue"],[1,"case-meta"],[1,"case-files","veritus-text-xs","veritus-text-gray"],["name","document-outline"],[1,"case-date","veritus-text-xs","veritus-text-gray"],[1,"case-actions"],[1,"action-btn","edit-btn",3,"click"],["name","create-outline"],[1,"empty-state"],["name","briefcase-outline",1,"empty-icon"],[1,"empty-title","veritus-text-lg","veritus-font-semibold","veritus-text-white"],[1,"empty-description","veritus-text-sm","veritus-text-gray"],[1,"veritus-btn-secondary",3,"click"]],template:function(s,n){1&s&&(e.j41(0,"ion-header")(1,"ion-toolbar",0)(2,"ion-title",1),e.EFF(3,"Case Management"),e.k0s()()(),e.j41(4,"ion-content",2)(5,"div",3)(6,"div",4)(7,"div",5)(8,"div",6)(9,"ion-label",7),e.EFF(10,"Lawyer"),e.k0s(),e.j41(11,"ion-select",8),e.mxI("ngModelChange",function(r){return e.DH7(n.selectedLawyer,r)||(n.selectedLawyer=r),r}),e.bIt("ionChange",function(){return n.onLawyerChange()}),e.j41(12,"ion-select-option",9),e.EFF(13,"All Lawyers"),e.k0s(),e.DNE(14,y,2,2,"ion-select-option",10),e.k0s()(),e.j41(15,"div",6)(16,"ion-label",7),e.EFF(17,"Status"),e.k0s(),e.j41(18,"ion-select",11),e.mxI("ngModelChange",function(r){return e.DH7(n.selectedStatus,r)||(n.selectedStatus=r),r}),e.bIt("ionChange",function(){return n.onStatusChange()}),e.j41(19,"ion-select-option",9),e.EFF(20,"All Status"),e.k0s(),e.j41(21,"ion-select-option",12),e.EFF(22,"Ongoing"),e.k0s(),e.j41(23,"ion-select-option",13),e.EFF(24,"Pending"),e.k0s(),e.j41(25,"ion-select-option",14),e.EFF(26,"Closed"),e.k0s()()()(),e.j41(27,"div",15)(28,"ion-searchbar",16),e.mxI("ngModelChange",function(r){return e.DH7(n.searchTerm,r)||(n.searchTerm=r),r}),e.bIt("ionInput",function(){return n.onSearchChange()}),e.k0s()()(),e.j41(29,"div",17)(30,"button",18),e.bIt("click",function(){return n.onCreateCase()}),e.nrm(31,"ion-icon",19),e.EFF(32," Create Case "),e.k0s()(),e.j41(33,"div",20)(34,"h2",21),e.EFF(35),e.k0s(),e.DNE(36,v,2,1,"div",22)(37,_,8,0,"div",23),e.k0s(),e.j41(38,"div",24)(39,"div",25)(40,"div",26),e.EFF(41),e.k0s(),e.j41(42,"div",27),e.EFF(43,"Ongoing"),e.k0s()(),e.j41(44,"div",25)(45,"div",26),e.EFF(46),e.k0s(),e.j41(47,"div",27),e.EFF(48,"Closed"),e.k0s()(),e.j41(49,"div",25)(50,"div",26),e.EFF(51),e.k0s(),e.j41(52,"div",27),e.EFF(53,"Total Cases"),e.k0s()()()()()),2&s&&(e.R7$(11),e.R50("ngModel",n.selectedLawyer),e.R7$(3),e.Y8G("ngForOf",n.linkedLawyers),e.R7$(4),e.R50("ngModel",n.selectedStatus),e.R7$(10),e.R50("ngModel",n.searchTerm),e.R7$(7),e.SpI(" Cases (",n.filteredCases.length,") "),e.R7$(),e.Y8G("ngIf",n.filteredCases.length>0),e.R7$(),e.Y8G("ngIf",0===n.filteredCases.length),e.R7$(4),e.SpI(" ",n.getOngoingCount()," "),e.R7$(5),e.SpI(" ",n.getClosedCount()," "),e.R7$(5),e.SpI(" ",n.cases.length," "))},dependencies:[g.Sq,g.bT,p.BC,p.vS,o.W9,o.eU,o.iq,o.he,o.S1,o.Nm,o.Ip,o.BC,o.ai,o.Je,o.Gw,g.vh],styles:[".cases-content[_ngcontent-%COMP%]{--background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%)}.cases-container[_ngcontent-%COMP%]{padding:20px;min-height:100vh}.filters-section[_ngcontent-%COMP%]{margin-bottom:20px}.filter-row[_ngcontent-%COMP%]{display:grid;grid-template-columns:1fr 1fr;gap:15px;margin-bottom:15px}.filter-item[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:8px}.filter-label[_ngcontent-%COMP%]{font-weight:500}.lawyer-select[_ngcontent-%COMP%], .status-select[_ngcontent-%COMP%]{--background: rgba(255, 255, 255, .1);--color: white;--border-radius: 8px;--padding-start: 12px;--padding-end: 12px;border:1px solid rgba(255,255,255,.2);border-radius:8px}.search-section[_ngcontent-%COMP%]{margin-top:15px}.custom-searchbar[_ngcontent-%COMP%]{--background: rgba(255, 255, 255, .1);--color: white;--placeholder-color: rgba(255, 255, 255, .6);--icon-color: rgba(255, 255, 255, .6);--border-radius: 8px;border:1px solid rgba(255,255,255,.2);border-radius:8px}.action-section[_ngcontent-%COMP%]{margin-bottom:25px}.create-btn[_ngcontent-%COMP%]{width:100%;display:flex;align-items:center;justify-content:center;gap:8px}.btn-icon[_ngcontent-%COMP%]{font-size:18px}.cases-section[_ngcontent-%COMP%]{margin-bottom:30px}.section-title[_ngcontent-%COMP%]{margin-bottom:15px}.cases-list[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:12px}.case-card[_ngcontent-%COMP%]{background:#ffffff1a;border-radius:12px;padding:16px;display:grid;grid-template-columns:auto 1fr auto;gap:15px;align-items:center;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px);border:1px solid rgba(255,255,255,.1);transition:all .3s ease}.case-card[_ngcontent-%COMP%]:hover{background:#ffffff26;transform:translateY(-2px)}.case-status[_ngcontent-%COMP%]{width:40px;height:40px;background:#ffffff1a;border-radius:8px;display:flex;align-items:center;justify-content:center}.case-status[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:20px}.case-details[_ngcontent-%COMP%]{flex:1}.case-title[_ngcontent-%COMP%], .case-client[_ngcontent-%COMP%], .case-description[_ngcontent-%COMP%]{margin-bottom:4px}.case-lawyer[_ngcontent-%COMP%]{margin-bottom:8px}.case-meta[_ngcontent-%COMP%]{display:flex;gap:15px;align-items:center}.case-files[_ngcontent-%COMP%], .case-date[_ngcontent-%COMP%]{display:flex;align-items:center;gap:4px}.case-files[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%], .case-date[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:12px}.case-actions[_ngcontent-%COMP%]{display:flex;gap:8px}.action-btn[_ngcontent-%COMP%]{width:36px;height:36px;border-radius:8px;border:none;display:flex;align-items:center;justify-content:center;transition:all .3s ease}.action-btn[_ngcontent-%COMP%]:hover{transform:scale(1.1)}.action-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:16px}.edit-btn[_ngcontent-%COMP%]{background:#2196f333;color:#2196f3}.edit-btn[_ngcontent-%COMP%]:hover{background:#2196f34d}.empty-state[_ngcontent-%COMP%]{text-align:center;padding:40px 20px;background:#ffffff0d;border-radius:12px;border:1px solid rgba(255,255,255,.1)}.empty-icon[_ngcontent-%COMP%]{font-size:48px;color:#d4af37;margin-bottom:16px}.empty-title[_ngcontent-%COMP%]{margin-bottom:8px}.empty-description[_ngcontent-%COMP%]{margin-bottom:20px}.stats-section[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(3,1fr);gap:15px}.stat-card[_ngcontent-%COMP%]{background:#ffffff1a;border-radius:12px;padding:16px;text-align:center;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px);border:1px solid rgba(255,255,255,.1)}.stat-number[_ngcontent-%COMP%]{margin-bottom:4px}.stat-label[_ngcontent-%COMP%]{font-size:11px}@media (max-width: 768px){.filter-row[_ngcontent-%COMP%]{grid-template-columns:1fr}.case-card[_ngcontent-%COMP%]{grid-template-columns:1fr;text-align:center;gap:10px}.case-actions[_ngcontent-%COMP%]{justify-content:center}.stats-section[_ngcontent-%COMP%]{grid-template-columns:1fr}}"]})}}return a})()}];let w=(()=>{class a{static{this.\u0275fac=function(s){return new(s||a)}}static{this.\u0275mod=e.$C({type:a})}static{this.\u0275inj=e.G2t({imports:[C.iI.forChild(x),C.iI]})}}return a})(),P=(()=>{class a{static{this.\u0275fac=function(s){return new(s||a)}}static{this.\u0275mod=e.$C({type:a})}static{this.\u0275inj=e.G2t({imports:[g.MD,p.YN,o.bv,w]})}}return a})()}}]);