.client-toolbar {
  --background: #ffffff;
  --color: #000000;
  --border-color: #e5e5e5;
}

.client-title {
  font-size: 18px;
  font-weight: 600;
  color: #000000;
}

.client-search-content {
  --background: #f8f9fa;
}

.search-container {
  padding: 0 20px 20px 20px;
  min-height: 100vh;
}

// Search Section
.search-section {
  padding: 16px 0;
  position: sticky;
  top: 0;
  background: #f8f9fa;
  z-index: 10;
}

.client-searchbar {
  --background: #ffffff;
  --color: #000000;
  --placeholder-color: #888888;
  --icon-color: #888888;
  --border-radius: 8px;
  --box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border: 1px solid #e5e5e5;
  border-radius: 8px;
  
  .searchbar-input {
    padding: 16px !important;
    font-size: 16px;
  }
}

// Lawyers Section
.lawyers-section {
  margin-top: 8px;
}

.section-header {
  margin-bottom: 16px;
}

.section-title {
  font-size: 18px;
  font-weight: 600;
  color: #000000;
  margin: 0;
}

.lawyers-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

// Lawyer Card
.lawyer-card {
  background: #ffffff;
  border-radius: 12px;
  padding: 16px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);
  border: 1px solid #e5e5e5;
  display: flex;
  gap: 16px;
  transition: all 0.2s ease;
  
  &:hover {
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  }
}

.lawyer-avatar {
  position: relative;
  flex-shrink: 0;

  img {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    object-fit: cover;
  }

  .avatar-placeholder {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #B88A42 0%, #D4AF37 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;

    .avatar-icon {
      font-size: 28px;
      color: #ffffff;
    }
  }
}

.availability-indicator {
  position: absolute;
  bottom: 2px;
  right: 2px;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: #dc3545;
  border: 2px solid #ffffff;
  
  &.available {
    background: #28a745;
  }
}

.lawyer-content {
  flex: 1;
  min-width: 0;
}

.lawyer-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8px;
  gap: 12px;
}

.lawyer-name {
  font-size: 18px;
  font-weight: 600;
  color: #000000;
  margin: 0;
  line-height: 1.2;
}

.lawyer-rating {
  display: flex;
  align-items: center;
  gap: 4px;
  flex-shrink: 0;
}

.stars {
  display: flex;
  gap: 2px;
  
  ion-icon {
    font-size: 14px;
    color: #ddd;
    
    &.filled {
      color: #ffc107;
    }
  }
}

.rating-text {
  font-size: 12px;
  color: #666666;
  font-weight: 500;
}

.lawyer-details {
  margin-bottom: 8px;
  
  p {
    margin: 0 0 4px 0;
    font-size: 14px;
    display: flex;
    align-items: center;
    gap: 6px;
    
    ion-icon {
      font-size: 14px;
      color: #888888;
    }
  }
}

.lawyer-firm {
  color: #666666;
  font-weight: 500;
}

.lawyer-location,
.lawyer-experience {
  color: #888888;
}

.lawyer-bio {
  font-size: 12px;
  color: #666666;
  line-height: 1.4;
  margin: 0 0 12px 0;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.lawyer-specialties {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  margin-bottom: 12px;
}

.specialty-tag {
  background: #f0f0f0;
  color: #333333;
  font-size: 11px;
  padding: 4px 8px;
  border-radius: 12px;
  font-weight: 500;
}

.more-specialties {
  font-size: 11px;
  color: #888888;
  font-weight: 500;
}

.lawyer-actions {
  display: flex;
  flex-direction: column;
  gap: 8px;
  flex-shrink: 0;
  align-self: flex-start;
}

.action-btn {
  padding: 8px 16px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 80px;
  
  &:active {
    transform: scale(0.95);
  }
}

.view-btn {
  background: #f8f9fa;
  color: #0A49FF;
  border: 1px solid #0A49FF;
  
  &:hover {
    background: #0A49FF;
    color: #ffffff;
  }
}

.request-btn {
  background: #0A49FF;
  color: #ffffff;
  
  &:hover {
    background: #0841e6;
  }
  
  &:disabled {
    background: #e9ecef;
    color: #6c757d;
    cursor: not-allowed;
  }
}

// Empty State
.empty-state {
  text-align: center;
  padding: 60px 20px;
  background: #ffffff;
  border-radius: 12px;
  margin-top: 20px;
}

.empty-icon {
  font-size: 64px;
  color: #dee2e6;
  margin-bottom: 16px;
}

.empty-title {
  font-size: 18px;
  font-weight: 600;
  color: #000000;
  margin: 0 0 8px 0;
}

.empty-description {
  font-size: 14px;
  color: #666666;
  margin: 0 0 20px 0;
  line-height: 1.4;
}

.clear-search-btn {
  background: #0A49FF;
  color: #ffffff;
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  
  &:hover {
    background: #0841e6;
  }
  
  &:active {
    transform: scale(0.95);
  }
}

// Responsive Design
@media (max-width: 375px) {
  .search-container {
    padding: 0 16px 16px 16px;
  }
  
  .lawyer-card {
    flex-direction: column;
    gap: 12px;
  }
  
  .lawyer-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .lawyer-actions {
    flex-direction: row;
    align-self: stretch;
    
    .action-btn {
      flex: 1;
    }
  }
}
