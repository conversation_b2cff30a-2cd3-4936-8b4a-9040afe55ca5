import { Component } from '@angular/core';
import { BreakpointObserver, Breakpoints } from '@angular/cdk/layout';
import { Observable } from 'rxjs';
import { map, shareReplay } from 'rxjs/operators';
import { Router, NavigationEnd } from '@angular/router';
import { FirebaseService } from './services/firebase.service';
import { filter } from 'rxjs/operators';

@Component({
  selector: 'app-root',
  template: `
    <!-- Login Page Layout (No Sidebar) -->
    <div *ngIf="isLoginPage" class="login-layout">
      <router-outlet></router-outlet>
    </div>

    <!-- Dashboard Layout (With Sidebar) -->
    <mat-sidenav-container *ngIf="!isLoginPage" class="sidenav-container">
      <mat-sidenav #drawer class="sidenav" fixedInViewport
          [attr.role]="(isHandset$ | async) ? 'dialog' : 'navigation'"
          [mode]="(isHandset$ | async) ? 'over' : 'side'"
          [opened]="(isHandset$ | async) === false">

        <!-- Veritus Logo Header -->
        <div class="sidebar-header">
          <div class="logo-container">
            <mat-icon class="logo-icon">balance</mat-icon>
            <span class="logo-text">Veritus</span>
          </div>
        </div>

        <!-- Navigation Menu -->
        <mat-nav-list class="nav-menu">
          <a mat-list-item routerLink="/secretary-dashboard" routerLinkActive="active-nav-item">
            <mat-icon matListItemIcon>dashboard</mat-icon>
            <span matListItemTitle>Dashboard</span>
          </a>
          <a mat-list-item routerLink="/lawyer-list" routerLinkActive="active-nav-item">
            <mat-icon matListItemIcon>people</mat-icon>
            <span matListItemTitle>Lawyer List</span>
          </a>
          <a mat-list-item routerLink="/secretary-calendar" routerLinkActive="active-nav-item">
            <mat-icon matListItemIcon>event</mat-icon>
            <span matListItemTitle>Calendar</span>
          </a>
          <a mat-list-item routerLink="/secretary-cases" routerLinkActive="active-nav-item">
            <mat-icon matListItemIcon>folder</mat-icon>
            <span matListItemTitle>Cases</span>
          </a>
          <a mat-list-item routerLink="/clients" routerLinkActive="active-nav-item">
            <mat-icon matListItemIcon>person</mat-icon>
            <span matListItemTitle>Clients</span>
          </a>
          <a mat-list-item routerLink="/finance" routerLinkActive="active-nav-item">
            <mat-icon matListItemIcon>account_balance</mat-icon>
            <span matListItemTitle>Finance</span>
          </a>
          <a mat-list-item routerLink="/activity-log" routerLinkActive="active-nav-item">
            <mat-icon matListItemIcon>history</mat-icon>
            <span matListItemTitle>Activity Log</span>
          </a>
          <a mat-list-item routerLink="/retainers" routerLinkActive="active-nav-item">
            <mat-icon matListItemIcon>receipt</mat-icon>
            <span matListItemTitle>Retainers</span>
          </a>
          <a mat-list-item routerLink="/settings" routerLinkActive="active-nav-item">
            <mat-icon matListItemIcon>settings</mat-icon>
            <span matListItemTitle>Settings</span>
          </a>
        </mat-nav-list>

        <!-- Logout Button -->
        <div class="sidebar-footer">
          <button mat-list-item (click)="logout()" class="logout-btn">
            <mat-icon matListItemIcon>logout</mat-icon>
            <span matListItemTitle>Log Out</span>
          </button>
        </div>
      </mat-sidenav>

      <mat-sidenav-content>
        <!-- Top Header -->
        <mat-toolbar class="top-header">
          <button
            type="button"
            aria-label="Toggle sidenav"
            mat-icon-button
            (click)="drawer.toggle()"
            *ngIf="isHandset$ | async">
            <mat-icon aria-label="Side nav toggle icon">menu</mat-icon>
          </button>

          <span class="spacer"></span>

          <!-- User Profile Section -->
          <div class="user-profile">
            <span class="user-greeting">Hi, {{ currentUser?.name || 'Manny P.' }}</span>
            <button mat-icon-button [matMenuTriggerFor]="userMenu" class="user-avatar">
              <img [src]="currentUser?.photoURL || 'assets/default-avatar.png'"
                   alt="User Avatar" class="avatar-img">
            </button>
            <mat-menu #userMenu="matMenu">
              <button mat-menu-item routerLink="/profile">
                <mat-icon>person</mat-icon>
                <span>Profile</span>
              </button>
              <button mat-menu-item (click)="logout()">
                <mat-icon>logout</mat-icon>
                <span>Logout</span>
              </button>
            </mat-menu>
          </div>
        </mat-toolbar>

        <router-outlet></router-outlet>
      </mat-sidenav-content>
    </mat-sidenav-container>
  `,
  styles: [`
    .login-layout {
      height: 100vh;
      width: 100vw;
      background: #FFFFFF;
      display: flex;
      align-items: center;
      justify-content: center;
      position: fixed;
      top: 0;
      left: 0;
      z-index: 9999;
    }

    .sidenav-container {
      height: 100vh;
      background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    }

    .sidenav {
      width: 280px;
      background: #C49A56;
      color: white;
      box-shadow: 4px 0 20px rgba(0, 0, 0, 0.1);
      border-right: none;
    }

    .sidebar-header {
      padding: 24px 20px;
      border-bottom: 1px solid rgba(255, 255, 255, 0.2);
      background: transparent;
    }

    .logo-container {
      display: flex;
      align-items: center;
      gap: 16px;
      transition: all 0.3s ease;
    }

    .logo-container:hover {
      transform: translateY(-2px);
    }

    .logo-icon {
      font-size: 36px;
      width: 36px;
      height: 36px;
      color: #C49A56;
      filter: drop-shadow(0 2px 4px rgba(196, 154, 86, 0.3));
      transition: all 0.3s ease;
    }

    .logo-icon:hover {
      transform: rotate(5deg) scale(1.1);
      color: #D4AF6A;
    }

    .logo-text {
      font-size: 24px;
      font-weight: 600;
      color: white;
      letter-spacing: 0.5px;
    }

    .nav-menu {
      padding: 24px 0;
    }

    .nav-menu .mat-mdc-list-item {
      color: white;
      margin: 4px 16px;
      border-radius: 8px;
      transition: all 0.3s ease;
      position: relative;
      overflow: hidden;
    }

    .nav-menu .mat-mdc-list-item::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(196, 154, 86, 0.1), transparent);
      transition: left 0.5s ease;
    }

    .nav-menu .mat-mdc-list-item:hover::before {
      left: 100%;
    }

    .nav-menu .mat-mdc-list-item:hover {
      background: rgba(255, 255, 255, 0.1);
      color: white;
      transform: translateX(4px);
    }

    .nav-menu .active-nav-item {
      background: rgba(255, 255, 255, 0.2);
      color: white;
      transform: translateX(4px);
    }

    .nav-menu .active-nav-item:hover {
      background: rgba(255, 255, 255, 0.25);
      transform: translateX(4px);
      color: white;
    }

    .sidebar-footer {
      position: absolute;
      bottom: 20px;
      left: 0;
      right: 0;
      padding: 0 16px;
    }

    .logout-btn {
      color: rgba(255, 255, 255, 0.8);
      border-radius: 8px;
      transition: all 0.3s ease;
    }

    .logout-btn:hover {
      background: rgba(255, 255, 255, 0.1);
      color: white;
    }

    .top-header {
      background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
      color: #1e293b;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
      z-index: 100;
      backdrop-filter: blur(20px);
      border-bottom: 1px solid rgba(226, 232, 240, 0.5);
    }

    .spacer {
      flex: 1 1 auto;
    }

    .user-profile {
      display: flex;
      align-items: center;
      gap: 16px;
      padding: 8px 16px;
      border-radius: 12px;
      transition: all 0.3s ease;
    }

    .user-profile:hover {
      background: rgba(226, 232, 240, 0.5);
      transform: translateY(-1px);
    }

    .user-greeting {
      font-size: 15px;
      color: #475569;
      font-weight: 500;
    }

    .user-avatar {
      width: 44px;
      height: 44px;
      border-radius: 50%;
      overflow: hidden;
      border: 3px solid #C49A56;
      transition: all 0.3s ease;
      box-shadow: 0 2px 8px rgba(196, 154, 86, 0.3);
    }

    .user-avatar:hover {
      transform: scale(1.1);
      box-shadow: 0 4px 16px rgba(196, 154, 86, 0.4);
    }

    .avatar-img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      border-radius: 50%;
    }

    mat-sidenav-content {
      background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
      min-height: 100vh;
    }
  `]
})
export class AppComponent {
  title = 'veritus-secretary';
  currentUser: any = {
    name: 'Manny P.',
    photoURL: null
  };

  isLoginPage = false;

  isHandset$: Observable<boolean> = this.breakpointObserver.observe(Breakpoints.Handset)
    .pipe(
      map(result => result.matches),
      shareReplay()
    );

  constructor(
    private breakpointObserver: BreakpointObserver,
    private router: Router,
    private firebaseService: FirebaseService
  ) {
    // Listen to route changes to determine if we're on login or register page
    this.router.events.pipe(
      filter(event => event instanceof NavigationEnd)
    ).subscribe((event) => {
      const navigationEvent = event as NavigationEnd;
      this.isLoginPage = navigationEvent.url === '/login' || navigationEvent.url.startsWith('/login') ||
                        navigationEvent.url === '/register' || navigationEvent.url.startsWith('/register');
      console.log('Route changed:', navigationEvent.url, 'isLoginPage:', this.isLoginPage);
    });

    // Set initial state
    this.isLoginPage = this.router.url === '/login' || this.router.url.startsWith('/login') ||
                      this.router.url === '/register' || this.router.url.startsWith('/register');
  }

  async logout() {
    try {
      console.log('Logout clicked');

      // Show confirmation
      const confirmed = confirm('Are you sure you want to logout?');
      if (!confirmed) {
        return;
      }

      console.log('Starting logout process...');

      // Sign out from Firebase
      await this.firebaseService.signOut();
      console.log('Firebase signOut successful');

      // Clear any local storage
      localStorage.clear();
      console.log('Local storage cleared');

      // Navigate to login page
      await this.router.navigate(['/login']);
      console.log('Successfully logged out and redirected to login');

    } catch (error) {
      console.error('Error during logout:', error);

      // Force logout even if Firebase fails
      localStorage.clear();
      console.log('Forced logout - clearing storage and redirecting');

      // Use window.location as fallback
      window.location.href = '/login';
    }
  }
}
