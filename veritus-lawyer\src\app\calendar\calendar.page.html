<ion-content class="appointments-content">
  <!-- Status Bar Spacer -->
  <div class="status-bar-spacer"></div>

  <div class="appointments-container">
    <!-- Header -->
    <div class="page-header">
      <h1 class="page-title">Appointments</h1>
    </div>

    <!-- Calendar Section -->
    <div class="calendar-section">
      <!-- Month Navigation -->
      <div class="month-navigation">
        <button class="nav-button" (click)="previousMonth()">
          <ion-icon name="chevron-back" class="nav-icon"></ion-icon>
        </button>
        <h2 class="month-title">{{ getMonthYearDisplay() }}</h2>
        <button class="nav-button" (click)="nextMonth()">
          <ion-icon name="chevron-forward" class="nav-icon"></ion-icon>
        </button>
      </div>

      <!-- Calendar Grid -->
      <div class="calendar-grid">
        <!-- Day Headers -->
        <div class="day-headers">
          <div class="day-header" *ngFor="let day of dayHeaders">{{ day }}</div>
        </div>

        <!-- Calendar Days -->
        <div class="calendar-days">
          <div
            class="calendar-day"
            *ngFor="let day of calendarDays"
            [class.today]="day.isToday"
            [class.other-month]="day.isOtherMonth"
            [class.available]="day.isAvailable"
            [class.booked]="day.isBooked"
            [class.selected]="day.isSelected">
            <span class="day-number">{{ day.date }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Bottom Section -->
    <div class="bottom-section">
      <!-- My Availability Card -->
      <div class="availability-card">
        <div class="card-header">
          <h3 class="section-title">My Availability</h3>
        </div>
        <div class="card-content">
          <div class="availability-list">
            <div class="availability-slot-card" *ngFor="let slot of availabilitySlots">
              <div class="slot-info">
                <span class="slot-date">{{ slot.date }}</span>
                <span class="slot-time">{{ slot.startTime }} – {{ slot.endTime }}</span>
              </div>
              <ion-button fill="clear" color="danger" size="small" (click)="removeAvailability(slot)">
              <ion-icon name="trash-outline"></ion-icon>
              </ion-button>
            </div>            
          </div>
          <button class="manage-button" (click)="onManageAvailability()">
            <span class="button-text">Manage</span>
          </button>
        </div>
      </div>

      <!-- Upcoming Appointments Card -->
      <div class="appointments-card">
        <div class="card-header">
          <h3 class="section-title">Upcoming Appointments</h3>
          <button class="view-all-button" (click)="onViewAllAppointments()">
            <span class="view-all-text">View All</span>
            <ion-icon name="chevron-forward" class="chevron-icon"></ion-icon>
          </button>
        </div>
        <div class="card-content">
          <div class="appointments-list">
            <div class="appointment-slot-card" *ngFor="let appointment of upcomingAppointments" (click)="onAppointmentClick(appointment)">
              <div class="appointment-info">
                <span class="appointment-date">{{ appointment.date }}</span>
                <span class="appointment-time">{{ appointment.time }}</span>
                <span class="appointment-type">{{ appointment.type }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div> <!-- bottom-section -->

  </div> <!-- appointments-container -->
</ion-content>