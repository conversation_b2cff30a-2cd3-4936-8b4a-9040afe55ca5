.login-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, var(--veritus-primary) 0%, var(--veritus-primary-dark) 50%, var(--veritus-secondary) 100%);
  padding: 20px;
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
    pointer-events: none;
  }
}

.login-card {
  background: var(--veritus-white);
  border-radius: 16px;
  box-shadow: 0 25px 50px rgba(44, 62, 80, 0.15), 0 10px 20px rgba(196, 154, 86, 0.1);
  width: 100%;
  max-width: 420px;
  overflow: hidden;
  position: relative;
  z-index: 1;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.login-header {
  background: linear-gradient(135deg, var(--veritus-primary) 0%, var(--veritus-primary-dark) 100%);
  color: white;
  padding: 45px 30px;
  text-align: center;
  position: relative;

  &::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--veritus-accent) 0%, var(--veritus-primary-light) 100%);
  }

  .logo h1 {
    margin: 0;
    font-size: 2.8rem;
    font-weight: 700;
    letter-spacing: -1px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .logo p {
    margin: 8px 0 5px 0;
    font-size: 1.1rem;
    opacity: 0.95;
    font-weight: 400;
  }

  .logo-subtitle {
    margin: 0;
    font-size: 0.85rem;
    opacity: 0.8;
    font-weight: 300;
    letter-spacing: 0.5px;
    text-transform: uppercase;
  }
}

.login-form {
  padding: 40px 30px;
}

.form-group {
  margin-bottom: 25px;
  
  label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #333;
    font-size: 0.9rem;
  }
  
  .form-control {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #e1e5e9;
    border-radius: 8px;
    font-size: 1rem;
    transition: all 0.3s ease;
    box-sizing: border-box;
    
    &:focus {
      outline: none;
      border-color: var(--admin-primary);
      box-shadow: 0 0 0 3px rgba(196, 154, 86, 0.1);
    }
    
    &.error {
      border-color: var(--admin-danger);
    }
    
    &::placeholder {
      color: #999;
    }
  }
}

.checkbox-container {
  display: flex;
  align-items: center;
  cursor: pointer;
  font-size: 0.9rem;
  color: #666;
  
  input[type="checkbox"] {
    margin-right: 10px;
    transform: scale(1.2);
  }
}

.login-btn {
  width: 100%;
  background: var(--admin-primary);
  color: white;
  border: none;
  padding: 14px 20px;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  
  &:hover:not(:disabled) {
    background: var(--admin-primary-dark);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(196, 154, 86, 0.3);
  }
  
  &:disabled {
    opacity: 0.7;
    cursor: not-allowed;
  }
  
  .spinner {
    width: 20px;
    height: 20px;
    border: 2px solid transparent;
    border-top: 2px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-right: 10px;
  }
}

.error-message {
  color: var(--admin-danger);
  font-size: 0.85rem;
  margin-top: 8px;
  display: block;
}

.login-footer {
  background: #f8f9fa;
  padding: 20px 30px;
  text-align: center;
  border-top: 1px solid #e1e5e9;

  .setup-link {
    margin-bottom: 15px;

    .setup-btn {
      display: inline-block;
      padding: 8px 16px;
      background: rgba(196, 154, 86, 0.1);
      color: var(--admin-primary);
      border: 1px solid rgba(196, 154, 86, 0.3);
      border-radius: 6px;
      text-decoration: none;
      font-size: 0.85rem;
      font-weight: 500;
      transition: all 0.3s ease;

      &:hover {
        background: rgba(196, 154, 86, 0.2);
        border-color: rgba(196, 154, 86, 0.5);
        transform: translateY(-1px);
      }
    }
  }

  p {
    margin: 0;
    color: #666;
    font-size: 0.85rem;
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

// Responsive design
@media (max-width: 480px) {
  .login-container {
    padding: 10px;
  }
  
  .login-header {
    padding: 30px 20px;
    
    .logo h1 {
      font-size: 2rem;
    }
  }
  
  .login-form {
    padding: 30px 20px;
  }
  
  .login-footer {
    padding: 15px 20px;
  }
}
