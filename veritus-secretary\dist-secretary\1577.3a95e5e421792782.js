"use strict";(self.webpackChunkveritus_secretary=self.webpackChunkveritus_secretary||[]).push([[1577],{1577:(y,c,n)=>{n.r(c),n.d(c,{ion_text:()=>u});var o=n(4363),_=n(333),a=n(611);const u=class{constructor(s){(0,o.r)(this,s),this.color=void 0}render(){const s=(0,a.b)(this);return(0,o.h)(o.H,{key:"4330b56cbc4e15953d9b3162fb40af728a8195dd",class:(0,_.c)(this.color,{[s]:!0})},(0,o.h)("slot",{key:"ec674a71d8fbb04d537fd79d617d9db4a607c340"}))}};u.style=":host(.ion-color){color:var(--ion-color-base)}"},333:(y,c,n)=>{n.d(c,{c:()=>a,g:()=>l,h:()=>_,o:()=>s});var o=n(467);const _=(t,e)=>null!==e.closest(t),a=(t,e)=>"string"==typeof t&&t.length>0?Object.assign({"ion-color":!0,[`ion-color-${t}`]:!0},e):e,l=t=>{const e={};return(t=>void 0!==t?(Array.isArray(t)?t:t.split(" ")).filter(r=>null!=r).map(r=>r.trim()).filter(r=>""!==r):[])(t).forEach(r=>e[r]=!0),e},u=/^[a-z][a-z0-9+\-.]*:/,s=function(){var t=(0,o.A)(function*(e,r,f,h){if(null!=e&&"#"!==e[0]&&!u.test(e)){const i=document.querySelector("ion-router");if(i)return r?.preventDefault(),i.push(e,f,h)}return!1});return function(r,f,h,i){return t.apply(this,arguments)}}()}}]);