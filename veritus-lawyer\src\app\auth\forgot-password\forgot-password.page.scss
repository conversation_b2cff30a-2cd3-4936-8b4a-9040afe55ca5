.auth-content {
  --background: transparent;
}

.auth-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  padding: var(--veritus-spacing-2xl);
}

.auth-logo {
  margin-bottom: var(--veritus-spacing-4xl);
  text-align: center;
}

.auth-form {
  width: 100%;
  max-width: 320px;
  margin-bottom: var(--veritus-spacing-xl);
}

.form-description {
  margin-bottom: var(--veritus-spacing-xl);
  text-align: center;
}

.form-description p {
  margin: 0;
  line-height: var(--veritus-line-height-normal);
  opacity: 0.9;
}

.form-group {
  margin-bottom: var(--veritus-spacing-lg);
}

.veritus-input-auth {
  width: 100%;
  box-sizing: border-box;
}

.auth-submit-btn {
  width: 100%;
  margin-top: var(--veritus-spacing-lg);
}

.success-message {
  text-align: center;
  margin-bottom: var(--veritus-spacing-xl);
}

.success-icon {
  font-size: 64px;
  color: var(--veritus-success);
  margin-bottom: var(--veritus-spacing-lg);
}

.success-title {
  margin: 0 0 var(--veritus-spacing-lg) 0;
}

.success-text {
  margin: 0;
  line-height: var(--veritus-line-height-normal);
  opacity: 0.9;
}

.auth-footer {
  text-align: center;
}

.link-button {
  background: none;
  border: none;
  padding: 0;
  text-decoration: underline;
  cursor: pointer;
}
