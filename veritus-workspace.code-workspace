{"folders": [{"name": "🏛️ Veritus <PERSON> Project", "path": "."}, {"name": "📱 <PERSON><PERSON>tus <PERSON>yer App", "path": "./veritus-lawyer"}, {"name": "💼 Veritus Secretary Portal", "path": "./veritus-secretary"}, {"name": "🔧 Veritus <PERSON>", "path": "./veritus-admin"}], "settings": {"typescript.preferences.includePackageJsonAutoImports": "off", "typescript.suggest.autoImports": false, "git.defaultBranchName": "main", "git.enableSmartCommit": true, "files.exclude": {"**/node_modules": true, "**/dist": true, "**/.angular": true, "**/android": true, "**/ios": true}}, "extensions": {"recommendations": ["angular.ng-template", "ms-vscode.vscode-typescript-next", "ionic.ionic", "ms-vscode.vscode-json", "bradlc.vscode-tailwindcss"]}}