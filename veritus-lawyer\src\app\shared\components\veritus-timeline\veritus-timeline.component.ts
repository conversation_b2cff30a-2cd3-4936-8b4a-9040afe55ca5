import { Component, Input } from '@angular/core';

export interface TimelineItem {
  id: string;
  title: string;
  description: string;
  date: string;
  completed: boolean;
}

@Component({
  selector: 'app-veritus-timeline',
  templateUrl: './veritus-timeline.component.html',
  styleUrls: ['./veritus-timeline.component.scss'],
  standalone: false,
})
export class VeritusTimelineComponent {
  @Input() items: TimelineItem[] = [];
}
