<ion-header>
  <ion-toolbar class="client-toolbar">
    <ion-title class="client-title">Profile</ion-title>
  </ion-toolbar>
</ion-header>

<ion-content class="client-profile-content">
  <div class="profile-container">
    
    <!-- Profile Header -->
    <div class="profile-header">
      <div class="avatar-section">
        <div class="avatar-placeholder">
          <ion-icon name="person" class="avatar-icon"></ion-icon>
        </div>
      </div>
      
      <div class="user-details" *ngIf="clientProfile">
        <h2 class="user-name">{{ clientProfile.name }}</h2>
        <p class="user-email">{{ clientProfile.email }}</p>
        <p class="user-role">Client</p>
      </div>
      
      <div class="user-details" *ngIf="!clientProfile">
        <h2 class="user-name">Loading...</h2>
        <p class="user-email">Please wait...</p>
      </div>
    </div>

    <!-- Profile Options -->
    <div class="profile-options">
      <div class="option-item">
        <div class="option-icon">
          <ion-icon name="person-outline"></ion-icon>
        </div>
        <div class="option-content">
          <h4 class="option-title">Edit Profile</h4>
          <p class="option-description">Update your personal information</p>
        </div>
        <ion-icon name="chevron-forward" class="option-arrow"></ion-icon>
      </div>
      
      <div class="option-item">
        <div class="option-icon">
          <ion-icon name="briefcase-outline"></ion-icon>
        </div>
        <div class="option-content">
          <h4 class="option-title">My Cases</h4>
          <p class="option-description">View your legal cases</p>
        </div>
        <ion-icon name="chevron-forward" class="option-arrow"></ion-icon>
      </div>
      
      <div class="option-item">
        <div class="option-icon">
          <ion-icon name="document-outline"></ion-icon>
        </div>
        <div class="option-content">
          <h4 class="option-title">Documents</h4>
          <p class="option-description">Access your legal documents</p>
        </div>
        <ion-icon name="chevron-forward" class="option-arrow"></ion-icon>
      </div>
      
      <div class="option-item">
        <div class="option-icon">
          <ion-icon name="card-outline"></ion-icon>
        </div>
        <div class="option-content">
          <h4 class="option-title">Payment Methods</h4>
          <p class="option-description">Manage payment options</p>
        </div>
        <ion-icon name="chevron-forward" class="option-arrow"></ion-icon>
      </div>
      
      <div class="option-item">
        <div class="option-icon">
          <ion-icon name="settings-outline"></ion-icon>
        </div>
        <div class="option-content">
          <h4 class="option-title">Settings</h4>
          <p class="option-description">App preferences and privacy</p>
        </div>
        <ion-icon name="chevron-forward" class="option-arrow"></ion-icon>
      </div>
      
      <div class="option-item">
        <div class="option-icon">
          <ion-icon name="help-circle-outline"></ion-icon>
        </div>
        <div class="option-content">
          <h4 class="option-title">Help & Support</h4>
          <p class="option-description">Get help and contact support</p>
        </div>
        <ion-icon name="chevron-forward" class="option-arrow"></ion-icon>
      </div>
    </div>

    <!-- Logout Button -->
    <div class="logout-section">
      <button class="logout-btn" (click)="onLogout()">
        <ion-icon name="log-out-outline" class="logout-icon"></ion-icon>
        Logout
      </button>
    </div>

  </div>
</ion-content>
