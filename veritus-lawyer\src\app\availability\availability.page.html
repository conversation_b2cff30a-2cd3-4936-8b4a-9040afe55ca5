<ion-header>
  <ion-toolbar>
    <ion-title>Manage Availability</ion-title>
    <ion-buttons slot="end">
      <ion-button fill="clear" (click)="saveAvailability()">Save</ion-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>

<ion-content class="availability-content">
  <div class="availability-container">

    <!-- Month Navigation -->
    <div class="month-navigation">
      <ion-button fill="clear" size="small" (click)="previousMonth()">
        <ion-icon slot="icon-only" name="chevron-back-outline"></ion-icon>
      </ion-button>
      <h2 class="month-title">{{ getMonthYearDisplay() }}</h2>
      <ion-button fill="clear" size="small" (click)="nextMonth()">
        <ion-icon slot="icon-only" name="chevron-forward-outline"></ion-icon>
      </ion-button>
    </div>

    <!-- Calendar Grid -->
    <div class="calendar-grid">
      <div class="day-header" *ngFor="let day of dayHeaders">{{ day }}</div>

      <div
        class="calendar-day"
        *ngFor="let day of calendarDays"
        [class.other-month]="day.isOtherMonth"
        [class.available]="day.isAvailable"
        (click)="toggleAvailability(day)">
        {{ day.date }}
      </div>
    </div>

    <!-- Weekly Recurrence -->
    <ion-card class="weekday-picker-card">
      <ion-card-header>
        <ion-card-title>Repeat Weekly On</ion-card-title>
      </ion-card-header>
      <ion-card-content>
        <div class="weekdays">
          <ion-chip
            *ngFor="let day of weekDays"
            [color]="day.selected ? 'primary' : 'medium'"
            [outline]="!day.selected"
            (click)="day.selected = !day.selected">
            {{ day.label }}
          </ion-chip>
        </div>
      </ion-card-content>
    </ion-card>

    <!-- Time Range Selection -->
    <ion-card class="time-picker-card">
      <ion-card-header>
        <ion-card-title>Select Time Range</ion-card-title>
      </ion-card-header>
      <ion-card-content>
        <ion-item>
          <ion-label position="stacked">Start Time</ion-label>
          <ion-datetime
            presentation="time"
            [(ngModel)]="startTime"
            hour-cycle="h12"
            display-format="hh:mm a">
          </ion-datetime>
        </ion-item>

        <ion-item>
          <ion-label position="stacked">End Time</ion-label>
          <ion-datetime
            presentation="time"
            [(ngModel)]="endTime"
            hour-cycle="h12"
            display-format="hh:mm a">
          </ion-datetime>
        </ion-item>
      </ion-card-content>
    </ion-card>

  </div>
</ion-content>
