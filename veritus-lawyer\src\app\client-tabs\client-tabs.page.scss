.client-tab-bar {
  --background: #ffffff;
  --border: 1px solid #e5e5e5;
  height: 80px;
  padding-bottom: env(safe-area-inset-bottom);
  
  ion-tab-button {
    --color: #888888;
    --color-selected: #0A49FF;
    
    &.tab-selected {
      --color: #0A49FF;
    }
    
    ion-icon {
      font-size: 24px;
      margin-bottom: 4px;
    }
    
    ion-label {
      font-size: 12px;
      font-weight: 500;
    }
    
    &.add-tab {
      position: relative;
      
      .add-button {
        width: 48px;
        height: 48px;
        background: linear-gradient(135deg, #B88A42 0%, #D4AF37 100%);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 4px auto;
        box-shadow: 0 4px 12px rgba(212, 175, 55, 0.3);
        
        ion-icon {
          color: white;
          font-size: 24px;
          margin: 0;
        }
      }
      
      ion-label {
        color: #888888;
        font-size: 10px;
      }
      
      &.tab-selected {
        .add-button {
          background: linear-gradient(135deg, #0A49FF 0%, #4A90E2 100%);
          box-shadow: 0 4px 12px rgba(10, 73, 255, 0.3);
        }
        
        ion-label {
          color: #0A49FF;
        }
      }
    }
  }
}
