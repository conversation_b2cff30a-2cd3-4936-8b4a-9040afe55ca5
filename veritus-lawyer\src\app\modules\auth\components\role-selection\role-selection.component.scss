.role-selection-container {
  padding: 20px;
  text-align: center;
}

.selection-title {
  font-size: 24px;
  font-weight: 700;
  color: #000000;
  margin: 0 0 8px 0;
}

.selection-subtitle {
  font-size: 16px;
  color: #666666;
  margin: 0 0 32px 0;
}

.roles-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 16px;
  max-width: 400px;
  margin: 0 auto;
}

.role-card {
  background: #ffffff;
  border: 2px solid #e5e5e5;
  border-radius: 12px;
  padding: 24px 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  
  &:hover {
    border-color: #0A49FF;
    box-shadow: 0 4px 20px rgba(10, 73, 255, 0.1);
  }
  
  &.selected {
    border-color: #0A49FF;
    background: linear-gradient(135deg, rgba(10, 73, 255, 0.05) 0%, rgba(74, 144, 226, 0.05) 100%);
    box-shadow: 0 4px 20px rgba(10, 73, 255, 0.15);
  }
}

.role-icon {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, #B88A42 0%, #D4AF37 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 16px auto;
  
  ion-icon {
    font-size: 28px;
    color: #ffffff;
  }
}

.role-title {
  font-size: 18px;
  font-weight: 600;
  color: #000000;
  margin: 0 0 8px 0;
}

.role-description {
  font-size: 14px;
  color: #666666;
  margin: 0;
  line-height: 1.4;
}

.selection-indicator {
  position: absolute;
  top: 12px;
  right: 12px;
  
  ion-icon {
    font-size: 24px;
    color: #0A49FF;
  }
}

@media (min-width: 768px) {
  .roles-grid {
    grid-template-columns: repeat(3, 1fr);
    max-width: 800px;
  }
}
