import { NgModule } from '@angular/core';
import { PreloadAllModules, RouterModule, Routes } from '@angular/router';
import { AuthGuard } from './guards/auth.guard';

const routes: Routes = [
  {
    path: '',
    redirectTo: '/auth/signin',
    pathMatch: 'full'
  },
  {
    path: 'auth',
    loadChildren: () => import('./auth/auth.module').then(m => m.AuthModule)
  },
  {
    path: 'tabs',
    loadChildren: () => import('./tabs/tabs.module').then(m => m.TabsModule),
    canActivate: [AuthGuard]
  },
  {
    path: 'client-tabs',
    loadChildren: () => import('./client-tabs/client-tabs.module').then(m => m.ClientTabsPageModule),
    canActivate: [AuthGuard]
  },
  {
    path: 'generate-document/:id',
    loadChildren: () => import('./generate-document/generate-document.module').then(m => m.GenerateDocumentPageModule),
    canActivate: [AuthGuard]
  },
  {
    path: 'track-case/:id',
    loadChildren: () => import('./track-case/track-case.module').then(m => m.TrackCasePageModule),
    canActivate: [AuthGuard]
  },
  {
    path: 'download-documents',
    loadChildren: () => import('./download-documents/download-documents.module').then(m => m.DownloadDocumentsPageModule),
    canActivate: [AuthGuard]
  },
  {
    path: 'case-files',
    loadChildren: () => import('./case-files/case-files.module').then(m => m.CaseFilesPageModule),
    canActivate: [AuthGuard]
  },
  {
    path: 'availability',
    loadChildren: () => import('./availability/availability.module').then( m => m.AvailabilityPageModule)
  },
  {
    path: 'profile-information',
    loadChildren: () => import('./profile-information/profile-information.module').then( m => m.ProfileInformationPageModule),
    canActivate: [AuthGuard]
  }

];

@NgModule({
  imports: [
    RouterModule.forRoot(routes, { preloadingStrategy: PreloadAllModules })
  ],
  exports: [RouterModule]
})
export class AppRoutingModule {}
