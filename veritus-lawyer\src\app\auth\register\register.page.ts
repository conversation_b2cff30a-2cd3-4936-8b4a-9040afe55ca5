import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Router, ActivatedRoute } from '@angular/router';
import { FirebaseService } from '../../services/firebase.service';
import { LoadingController, AlertController } from '@ionic/angular';

@Component({
  selector: 'app-register',
  templateUrl: './register.page.html',
  styleUrls: ['./register.page.scss'],
  standalone: false,
})
export class RegisterPage implements OnInit {
  registerForm: FormGroup;
  selectedRole: 'client' | 'lawyer' = 'client';
  showPassword = false;
  showConfirmPassword = false;

  constructor(
    private formBuilder: FormBuilder,
    private router: Router,
    private route: ActivatedRoute,
    private firebaseService: FirebaseService,
    private loadingController: LoadingController,
    private alertController: AlertController
  ) {
    this.registerForm = this.formBuilder.group({
      name: ['', [Validators.required]], // Full name field
      email: ['', [Validators.required, Validators.email]],
      rollNumber: [''], // Will be required conditionally for lawyer
      barId: [''], // Will be required conditionally for lawyer

      password: ['', [Validators.required, Validators.minLength(6)]],
      confirmPassword: ['', [Validators.required]]
    });
  }

  ngOnInit() {
    // Get role from query parameters
    this.route.queryParams.subscribe(params => {
      if (params['role']) {
        this.selectedRole = params['role'];
        this.updateFormValidators();
      } else {
        // If no role specified, redirect to role selection
        this.router.navigate(['/auth/role-selection']);
      }
    });
  }

  goBackToRoleSelection() {
    this.router.navigate(['/auth/role-selection']);
  }

  updateFormValidators() {
    // Reset all validators
    this.registerForm.get('rollNumber')?.clearValidators();
    this.registerForm.get('barId')?.clearValidators();

    // Add validators based on role
    if (this.selectedRole === 'lawyer') {
      this.registerForm.get('rollNumber')?.setValidators([Validators.required]);
      this.registerForm.get('barId')?.setValidators([Validators.required]);
    }

    // Update validity
    this.registerForm.get('rollNumber')?.updateValueAndValidity();
    this.registerForm.get('barId')?.updateValueAndValidity();
  }

  async onRegister() {
    if (this.registerForm.valid && this.passwordsMatch()) {
      const loading = await this.loadingController.create({
        message: 'Creating account...',
        spinner: 'crescent'
      });
      await loading.present();

      try {
        const email = this.registerForm.get('email')?.value;
        const password = this.registerForm.get('password')?.value;

        let userData: any = {
          role: this.selectedRole,
          name: this.registerForm.get('name')?.value // Use the actual name from form
        };

        // Add role-specific data
        if (this.selectedRole === 'lawyer') {
          userData.rollNumber = this.registerForm.get('rollNumber')?.value;
          userData.barId = this.registerForm.get('barId')?.value;
        }

        await this.firebaseService.signUp(email, password, userData);
        await loading.dismiss();

        let successMessage = '';
        if (this.selectedRole === 'lawyer') {
          successMessage = 'Your lawyer account has been created successfully!';
        } else {
          successMessage = 'Your client account has been created successfully!';
        }

        await this.showSuccessAlert('Account Created', successMessage);

        // Redirect based on role
        if (this.selectedRole === 'lawyer') {
          this.router.navigate(['/tabs']);
        } else {
          this.router.navigate(['/client-tabs']);
        }
      } catch (error: any) {
        await loading.dismiss();
        await this.showErrorAlert('Registration Failed', error.message || 'Please try again.');
      }
    }
  }

  passwordsMatch(): boolean {
    const password = this.registerForm.get('password')?.value;
    const confirmPassword = this.registerForm.get('confirmPassword')?.value;
    return password === confirmPassword;
  }

  isFormValid(): boolean {
    return this.registerForm.valid && this.passwordsMatch();
  }

  onGoogleRegister() {
    // Mock Google OAuth
    this.router.navigate(['/tabs']);
  }

  goToSignIn() {
    this.router.navigate(['/auth/signin']);
  }

  private async showSuccessAlert(header: string, message: string) {
    const alert = await this.alertController.create({
      header,
      message,
      buttons: ['OK']
    });
    await alert.present();
  }

  private async showErrorAlert(header: string, message: string) {
    const alert = await this.alertController.create({
      header,
      message,
      buttons: ['OK']
    });
    await alert.present();
  }

  togglePasswordVisibility() {
    this.showPassword = !this.showPassword;
  }

  toggleConfirmPasswordVisibility() {
    this.showConfirmPassword = !this.showConfirmPassword;
  }
}
