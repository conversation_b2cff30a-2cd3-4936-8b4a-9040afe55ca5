.custom-header {
  background: linear-gradient(135deg, #C49A56 0%, #B8944F 100%); // Gold gradient
  color: white;
  text-align: center;
  --border-width: 0;
  --box-shadow: 0 2px 8px rgba(196, 154, 86, 0.3);
  position: relative;
  z-index: 10;

  ion-title {
    font-weight: 600;
    font-size: 18px;
    color: white;
  }

  ion-back-button {
    --color: #000000 !important; // Clean black arrow
    --icon-font-size: 24px;
    margin-left: 8px;
    background: transparent; // No background

    &::part(icon) {
      color: #000000 !important; // Clean black
      font-size: 24px;
    }

    &::part(native) {
      background: transparent !important; // No background
      padding: 8px;
    }

    &:hover {
      &::part(icon) {
        color: #333333 !important; // Slightly darker on hover
      }
    }
  }

  ion-buttons {
    ion-back-button {
      --color: #000000 !important;
      --background: transparent !important;
    }
  }
}

.profile-page {
  --background: #ffffff;
  --padding-top: 0;
  --padding-bottom: 20px;
}

.avatar-container {
  margin-top: 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 20px;

  .avatar-circle {
    width: 110px;
    height: 110px;
    border-radius: 50%;
    overflow: hidden;
    cursor: default; // Not clickable
    pointer-events: none; // Disable all interactions
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;

    .avatar-image {
      width: 100%;
      height: 100%;
      object-fit: cover;
      border-radius: 50%;
    }

    svg {
      width: 110px;
      height: 110px;
      pointer-events: none; // Disable SVG interactions
    }
  }

  .verified-badge {
    margin-top: 8px;
    display: flex;
    align-items: center;
    gap: 6px;

    ion-icon {
      color: #4CAF50;
      font-size: 18px;
    }

    .verified-text {
      font-size: 12px;
      padding: 4px 8px;
      border-radius: 10px;
      --background: #1976D2;
    }
  }
}

.form-container {
  padding: 0 20px;
  margin-bottom: 20px;

  .form-field {
    margin-bottom: 20px;
  }

  .field-label {
    font-size: 14px;
    font-weight: 500;
    color: #333;
    margin-bottom: 8px;
  }

  .field-input {
    border: 1px solid #ddd;
    border-radius: 4px;
    --padding-start: 12px;
    --padding-end: 12px;
    --padding-top: 12px;
    --padding-bottom: 12px;
    font-size: 14px;
    --background: #fff;
  }

  .readonly-field {
    --background: #f5f5f5 !important;
    border: 1px solid #e0e0e0 !important;
    color: #666 !important;
    cursor: not-allowed;

    &::part(native) {
      background: #f5f5f5 !important;
      color: #666 !important;
      cursor: not-allowed;
    }
  }

  .input-with-icon {
    position: relative;

    .location-icon {
      position: absolute;
      right: 12px;
      top: 50%;
      transform: translateY(-50%);
      color: #C49A56;
      font-size: 20px;
      z-index: 10;
      cursor: pointer;
      transition: all 0.3s ease;
      padding: 4px;
      border-radius: 50%;

      &:hover {
        color: #B8944F;
        background-color: rgba(196, 154, 86, 0.1);
        transform: translateY(-50%) scale(1.1);
      }

      &:active {
        transform: translateY(-50%) scale(0.95);
      }
    }
  }

  .form-row {
    display: flex;
    gap: 15px;
  }

  .half-width {
    flex: 1;
  }
}

.save-button {
  margin: 20px;
  --background: #C49A56;
  --border-radius: 4px;
  height: 48px;
  font-weight: 600;
  font-size: 16px;
  --box-shadow: none;
}

// Animations
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.avatar-container, .form-container, .save-button {
  animation: fadeIn 0.5s ease-out forwards;
}

.form-container {
  animation-delay: 0.2s;
}

.save-button {
  animation-delay: 0.3s;
}

// Back Button Clean Styling
ion-back-button {
  --color: #000000 !important;

  .button-native {
    color: #000000 !important;
    background: transparent !important;
  }

  ion-icon {
    color: #000000 !important;
    font-size: 24px !important;
  }
}

// Global back button styling for this page
.custom-header ion-back-button {
  --ripple-color: rgba(0, 0, 0, 0.1) !important;

  .button-native {
    background: transparent !important;

    &:hover {
      background: rgba(0, 0, 0, 0.05) !important;
    }
  }
}

// Responsive Adjustments
@media (max-width: 480px) {
  .form-row {
    flex-direction: column;
    gap: 20px;
  }

  .custom-header ion-back-button {
    --icon-font-size: 22px;

    .button-native {
      background: transparent !important;
    }
  }
}