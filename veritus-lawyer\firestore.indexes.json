{"indexes": [{"collectionGroup": "assistant_codes", "queryScope": "COLLECTION", "fields": [{"fieldPath": "lawyerId", "order": "ASCENDING"}, {"fieldPath": "isUsed", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "assistant_codes", "queryScope": "COLLECTION", "fields": [{"fieldPath": "code", "order": "ASCENDING"}, {"fieldPath": "isUsed", "order": "ASCENDING"}]}, {"collectionGroup": "assistant_codes", "queryScope": "COLLECTION", "fields": [{"fieldPath": "expiresAt", "order": "ASCENDING"}, {"fieldPath": "isUsed", "order": "ASCENDING"}]}, {"collectionGroup": "lawyer_secretary_links", "queryScope": "COLLECTION", "fields": [{"fieldPath": "lawyerId", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "lawyer_secretary_links", "queryScope": "COLLECTION", "fields": [{"fieldPath": "secretaryId", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "cases", "queryScope": "COLLECTION", "fields": [{"fieldPath": "lawyerId", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "updatedAt", "order": "DESCENDING"}]}, {"collectionGroup": "cases", "queryScope": "COLLECTION", "fields": [{"fieldPath": "clientId", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "updatedAt", "order": "DESCENDING"}]}, {"collectionGroup": "appointments", "queryScope": "COLLECTION", "fields": [{"fieldPath": "lawyerId", "order": "ASCENDING"}, {"fieldPath": "date", "order": "ASCENDING"}]}, {"collectionGroup": "appointments", "queryScope": "COLLECTION", "fields": [{"fieldPath": "clientId", "order": "ASCENDING"}, {"fieldPath": "date", "order": "ASCENDING"}]}, {"collectionGroup": "files", "queryScope": "COLLECTION", "fields": [{"fieldPath": "lawyerId", "order": "ASCENDING"}, {"fieldPath": "caseId", "order": "ASCENDING"}, {"fieldPath": "uploadedAt", "order": "DESCENDING"}]}, {"collectionGroup": "retainers", "queryScope": "COLLECTION", "fields": [{"fieldPath": "lawyerId", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "financial_records", "queryScope": "COLLECTION", "fields": [{"fieldPath": "lawyerId", "order": "ASCENDING"}, {"fieldPath": "type", "order": "ASCENDING"}, {"fieldPath": "date", "order": "DESCENDING"}]}, {"collectionGroup": "audit_logs", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "timestamp", "order": "DESCENDING"}]}, {"collectionGroup": "audit_logs", "queryScope": "COLLECTION", "fields": [{"fieldPath": "entityType", "order": "ASCENDING"}, {"fieldPath": "action", "order": "ASCENDING"}, {"fieldPath": "timestamp", "order": "DESCENDING"}]}], "fieldOverrides": []}