import { Injectable } from '@angular/core';
import { Firestore, doc, setDoc, getDoc, updateDoc, collection, query, where, getDocs } from '@angular/fire/firestore';
import { Storage, ref, uploadBytes, getDownloadURL, deleteObject } from '@angular/fire/storage';
import { LawyerProfile, SecretaryProfile, ClientProfile } from './auth.service';

@Injectable({
  providedIn: 'root'
})
export class ProfileService {

  constructor(
    private firestore: Firestore,
    private storage: Storage
  ) { }

  // Lawyer Profile Management
  async createLawyerProfile(profile: LawyerProfile): Promise<void> {
    const docRef = doc(this.firestore, 'lawyers', profile.uid);
    await setDoc(docRef, profile);
  }

  async getLawyerProfile(uid: string): Promise<LawyerProfile | null> {
    const docRef = doc(this.firestore, 'lawyers', uid);
    const docSnap = await getDoc(docRef);
    return docSnap.exists() ? docSnap.data() as LawyerProfile : null;
  }

  async updateLawyerProfile(uid: string, updates: Partial<LawyerProfile>): Promise<void> {
    const docRef = doc(this.firestore, 'lawyers', uid);
    await updateDoc(docRef, { ...updates, updatedAt: new Date() });
  }

  // Secretary Profile Management
  async createSecretaryProfile(profile: SecretaryProfile): Promise<void> {
    const docRef = doc(this.firestore, 'secretaries', profile.uid);
    await setDoc(docRef, profile);
  }

  async getSecretaryProfile(uid: string): Promise<SecretaryProfile | null> {
    const docRef = doc(this.firestore, 'secretaries', uid);
    const docSnap = await getDoc(docRef);
    return docSnap.exists() ? docSnap.data() as SecretaryProfile : null;
  }

  async updateSecretaryProfile(uid: string, updates: Partial<SecretaryProfile>): Promise<void> {
    const docRef = doc(this.firestore, 'secretaries', uid);
    await updateDoc(docRef, { ...updates, updatedAt: new Date() });
  }

  // Client Profile Management
  async createClientProfile(profile: ClientProfile): Promise<void> {
    const docRef = doc(this.firestore, 'clients', profile.uid);
    await setDoc(docRef, profile);
  }

  async getClientProfile(uid: string): Promise<ClientProfile | null> {
    const docRef = doc(this.firestore, 'clients', uid);
    const docSnap = await getDoc(docRef);
    return docSnap.exists() ? docSnap.data() as ClientProfile : null;
  }

  async updateClientProfile(uid: string, updates: Partial<ClientProfile>): Promise<void> {
    const docRef = doc(this.firestore, 'clients', uid);
    await updateDoc(docRef, { ...updates, updatedAt: new Date() });
  }

  // File Upload for Profile Photos and Documents
  async uploadProfilePhoto(uid: string, file: File): Promise<string> {
    const fileName = `profile-photos/${uid}/${Date.now()}_${file.name}`;
    const storageRef = ref(this.storage, fileName);
    
    await uploadBytes(storageRef, file);
    const downloadURL = await getDownloadURL(storageRef);
    
    return downloadURL;
  }

  async uploadPhotoId(uid: string, file: File): Promise<string> {
    const fileName = `photo-ids/${uid}/${Date.now()}_${file.name}`;
    const storageRef = ref(this.storage, fileName);
    
    await uploadBytes(storageRef, file);
    const downloadURL = await getDownloadURL(storageRef);
    
    return downloadURL;
  }

  async deleteFile(url: string): Promise<void> {
    const storageRef = ref(this.storage, url);
    await deleteObject(storageRef);
  }

  // Secretary-Lawyer Linking
  async findLawyerByCode(code: string): Promise<LawyerProfile | null> {
    const q = query(
      collection(this.firestore, 'lawyers'),
      where('secretaryCode', '==', code)
    );
    
    const querySnapshot = await getDocs(q);
    if (!querySnapshot.empty) {
      const doc = querySnapshot.docs[0];
      return doc.data() as LawyerProfile;
    }
    
    return null;
  }

  async requestSecretaryLink(secretaryUid: string, lawyerUid: string, accessLevel: string): Promise<void> {
    const linkRequest = {
      secretaryUid,
      lawyerUid,
      accessLevel,
      status: 'pending',
      requestedAt: new Date()
    };

    const docRef = doc(this.firestore, 'secretary-requests', `${secretaryUid}_${lawyerUid}`);
    await setDoc(docRef, linkRequest);
  }

  async approveSecretaryLink(secretaryUid: string, lawyerUid: string): Promise<void> {
    // Update secretary profile
    const secretaryProfile = await this.getSecretaryProfile(secretaryUid);
    if (secretaryProfile) {
      secretaryProfile.linkedLawyers.push(lawyerUid);
      secretaryProfile.isApproved = true;
      await this.updateSecretaryProfile(secretaryUid, secretaryProfile);
    }

    // Update link request status
    const docRef = doc(this.firestore, 'secretary-requests', `${secretaryUid}_${lawyerUid}`);
    await updateDoc(docRef, { 
      status: 'approved', 
      approvedAt: new Date() 
    });
  }

  // Search and Discovery
  async searchLawyers(searchTerm: string, specialization?: string): Promise<LawyerProfile[]> {
    // TODO: Implement full-text search with Algolia or similar
    // For now, return mock data
    return [];
  }

  async getAllLawyers(): Promise<LawyerProfile[]> {
    const q = query(collection(this.firestore, 'lawyers'));
    const querySnapshot = await getDocs(q);
    
    return querySnapshot.docs.map(doc => doc.data() as LawyerProfile);
  }
}
