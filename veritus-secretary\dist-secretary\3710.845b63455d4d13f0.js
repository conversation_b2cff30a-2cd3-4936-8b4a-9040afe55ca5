"use strict";(self.webpackChunkveritus_secretary=self.webpackChunkveritus_secretary||[]).push([[3710],{3710:(P,u,l)=>{l.r(u),l.d(u,{SecretaryCalendarPageModule:()=>M});var d=l(177),g=l(9417),s=l(2276),f=l(8498),m=l(467),t=l(4438),h=l(8287);function C(a,c){if(1&a&&(t.j41(0,"ion-select-option",23),t.EFF(1),t.k0s()),2&a){const e=c.$implicit;t.Y8G("value",e.uid),t.R7$(),t.SpI(" ",e.name," ")}}function v(a,c){if(1&a){const e=t.RV6();t.j41(0,"div",26)(1,"div",27)(2,"span",28),t.<PERSON><PERSON>(3),t.k0s()(),t.j41(4,"div",29)(5,"h3",30),t.<PERSON><PERSON>(6),t.k0s(),t.j41(7,"p",31),t.EFF(8),t.k0s(),t.j41(9,"p",32),t.EFF(10),t.k0s(),t.j41(11,"div",33)(12,"ion-chip",34)(13,"ion-label",35),t.EFF(14),t.nI1(15,"titlecase"),t.k0s()()()(),t.j41(16,"div",36)(17,"button",37),t.bIt("click",function(){const n=t.eBV(e).$implicit,r=t.XpG(2);return t.Njj(r.onEditAppointment(n))}),t.nrm(18,"ion-icon",38),t.k0s()()()}if(2&a){const e=c.$implicit,i=t.XpG(2);t.R7$(3),t.JRh(e.time),t.R7$(3),t.SpI(" ",e.clientName," "),t.R7$(2),t.JRh(e.type),t.R7$(2),t.JRh(e.lawyer),t.R7$(2),t.Y8G("color",i.getStatusColor(e.status)),t.R7$(2),t.JRh(t.bMT(15,6,e.status))}}function b(a,c){if(1&a&&(t.j41(0,"div",24),t.DNE(1,v,19,8,"div",25),t.k0s()),2&a){const e=t.XpG();t.R7$(),t.Y8G("ngForOf",e.filteredAppointments)}}function x(a,c){if(1&a){const e=t.RV6();t.j41(0,"div",39),t.nrm(1,"ion-icon",40),t.j41(2,"h3",41),t.EFF(3,"No Appointments"),t.k0s(),t.j41(4,"p",42),t.EFF(5," No appointments found for the selected date and lawyer. "),t.k0s(),t.j41(6,"button",43),t.bIt("click",function(){t.eBV(e);const n=t.XpG();return t.Njj(n.onCreateAppointment())}),t.EFF(7," Create First Appointment "),t.k0s()()}}const _=[{path:"",component:(()=>{class a{constructor(e){this.firebaseService=e,this.selectedDate=(new Date).toISOString().split("T")[0],this.linkedLawyers=[],this.selectedLawyer="all",this.appointments=[],this.filteredAppointments=[]}ngOnInit(){this.loadLinkedLawyers(),this.loadAppointments()}loadLinkedLawyers(){var e=this;return(0,m.A)(function*(){const i=e.firebaseService.getCurrentUser();i&&(e.linkedLawyers=yield e.firebaseService.getSecretaryLinkedLawyers(i.uid))})()}loadAppointments(){var e=this;return(0,m.A)(function*(){e.firebaseService.getCurrentUser()&&(e.appointments=[{id:"1",title:"Consultation - John Doe",date:e.selectedDate,time:"09:00",lawyer:"Atty. Smith",lawyerId:"lawyer1",status:"confirmed",type:"Consultation",clientName:"John Doe"},{id:"2",title:"Case Review - Jane Smith",date:e.selectedDate,time:"14:00",lawyer:"Atty. Johnson",lawyerId:"lawyer2",status:"new",type:"Case Review",clientName:"Jane Smith"}].sort((o,p)=>new Date(o.date+" "+o.time).getTime()-new Date(p.date+" "+p.time).getTime()),e.filterAppointments())})()}filterAppointments(){this.filteredAppointments=this.appointments.filter(e=>e.date===this.selectedDate&&("all"===this.selectedLawyer||e.lawyerId===this.selectedLawyer))}onDateChange(){this.filterAppointments()}onLawyerChange(){this.filterAppointments()}onCreateAppointment(){var e=this;return(0,m.A)(function*(){if(0===e.linkedLawyers.length)return void alert("You need to be linked with at least one lawyer to create appointments.");const i=prompt("Enter client name:"),n=prompt("Enter appointment type (e.g., Consultation):"),r=prompt("Enter time (HH:MM format):","09:00");if(i&&n&&r){const o={id:Date.now().toString(),title:`${n} - ${i}`,date:e.selectedDate,time:r,lawyer:e.linkedLawyers[0]?.name||"Unknown",lawyerId:e.linkedLawyers[0]?.uid||"",status:"new",type:n,clientName:i};e.appointments.push(o),e.filterAppointments(),alert("Appointment created successfully!")}})()}onEditAppointment(e){var i=this;return(0,m.A)(function*(){const n=prompt("Enter client name:",e.clientName),r=prompt("Enter appointment type:",e.type),o=prompt("Enter time (HH:MM format):",e.time),p=prompt("Enter status (new/confirmed/completed/cancelled):",e.status);if(n&&r&&o&&p){const y=i.appointments.findIndex(k=>k.id===e.id);-1!==y&&(i.appointments[y]={...e,clientName:n,type:r,time:o,status:p,title:`${r} - ${n}`},i.filterAppointments(),alert("Appointment updated successfully!"))}})()}getStatusColor(e){switch(e){case"confirmed":return"success";case"completed":return"medium";case"cancelled":return"danger";default:return"warning"}}getConfirmedCount(){return this.filteredAppointments.filter(e=>"confirmed"===e.status).length}static{this.\u0275fac=function(i){return new(i||a)(t.rXU(h.f))}}static{this.\u0275cmp=t.VBU({type:a,selectors:[["app-secretary-calendar"]],decls:44,vars:9,consts:[[1,"veritus-toolbar"],[1,"veritus-text-white","veritus-font-semibold"],[1,"calendar-content","veritus-gradient-bg"],[1,"calendar-container","veritus-safe-area-top"],[1,"filters-section"],[1,"filter-row"],[1,"filter-item"],[1,"filter-label","veritus-text-sm","veritus-text-white"],["type","date",1,"date-input",3,"ngModelChange","ionInput","ngModel"],["interface","popover",1,"lawyer-select",3,"ngModelChange","ionChange","ngModel"],["value","all"],[3,"value",4,"ngFor","ngForOf"],[1,"action-section"],[1,"veritus-btn-primary","create-btn",3,"click"],["name","add",1,"btn-icon"],[1,"appointments-section"],[1,"section-title","veritus-text-lg","veritus-font-semibold","veritus-text-white"],["class","appointments-list",4,"ngIf"],["class","empty-state",4,"ngIf"],[1,"stats-section"],[1,"stat-card"],[1,"stat-number","veritus-text-xl","veritus-font-bold","veritus-text-white"],[1,"stat-label","veritus-text-sm","veritus-text-gray"],[3,"value"],[1,"appointments-list"],["class","appointment-card",4,"ngFor","ngForOf"],[1,"appointment-card"],[1,"appointment-time"],[1,"time","veritus-text-lg","veritus-font-bold","veritus-text-gold"],[1,"appointment-details"],[1,"appointment-title","veritus-text-base","veritus-font-semibold","veritus-text-white"],[1,"appointment-type","veritus-text-sm","veritus-text-gray"],[1,"appointment-lawyer","veritus-text-sm","veritus-text-gold"],[1,"appointment-status"],[1,"status-chip",3,"color"],[1,"veritus-text-xs"],[1,"appointment-actions"],[1,"action-btn","edit-btn",3,"click"],["name","create-outline"],[1,"empty-state"],["name","calendar-outline",1,"empty-icon"],[1,"empty-title","veritus-text-lg","veritus-font-semibold","veritus-text-white"],[1,"empty-description","veritus-text-sm","veritus-text-gray"],[1,"veritus-btn-secondary",3,"click"]],template:function(i,n){1&i&&(t.j41(0,"ion-header")(1,"ion-toolbar",0)(2,"ion-title",1),t.EFF(3,"Calendar Management"),t.k0s()()(),t.j41(4,"ion-content",2)(5,"div",3)(6,"div",4)(7,"div",5)(8,"div",6)(9,"ion-label",7),t.EFF(10,"Date"),t.k0s(),t.j41(11,"ion-input",8),t.mxI("ngModelChange",function(o){return t.DH7(n.selectedDate,o)||(n.selectedDate=o),o}),t.bIt("ionInput",function(){return n.onDateChange()}),t.k0s()(),t.j41(12,"div",6)(13,"ion-label",7),t.EFF(14,"Lawyer"),t.k0s(),t.j41(15,"ion-select",9),t.mxI("ngModelChange",function(o){return t.DH7(n.selectedLawyer,o)||(n.selectedLawyer=o),o}),t.bIt("ionChange",function(){return n.onLawyerChange()}),t.j41(16,"ion-select-option",10),t.EFF(17,"All Lawyers"),t.k0s(),t.DNE(18,C,2,2,"ion-select-option",11),t.k0s()()()(),t.j41(19,"div",12)(20,"button",13),t.bIt("click",function(){return n.onCreateAppointment()}),t.nrm(21,"ion-icon",14),t.EFF(22," Create Appointment "),t.k0s()(),t.j41(23,"div",15)(24,"h2",16),t.EFF(25),t.k0s(),t.DNE(26,b,2,1,"div",17)(27,x,8,0,"div",18),t.k0s(),t.j41(28,"div",19)(29,"div",20)(30,"div",21),t.EFF(31),t.k0s(),t.j41(32,"div",22),t.EFF(33,"Total Appointments"),t.k0s()(),t.j41(34,"div",20)(35,"div",21),t.EFF(36),t.k0s(),t.j41(37,"div",22),t.EFF(38,"Linked Lawyers"),t.k0s()(),t.j41(39,"div",20)(40,"div",21),t.EFF(41),t.k0s(),t.j41(42,"div",22),t.EFF(43,"Confirmed Today"),t.k0s()()()()()),2&i&&(t.R7$(11),t.R50("ngModel",n.selectedDate),t.R7$(4),t.R50("ngModel",n.selectedLawyer),t.R7$(3),t.Y8G("ngForOf",n.linkedLawyers),t.R7$(7),t.SpI(" Appointments for ",n.selectedDate," "),t.R7$(),t.Y8G("ngIf",n.filteredAppointments.length>0),t.R7$(),t.Y8G("ngIf",0===n.filteredAppointments.length),t.R7$(4),t.SpI(" ",n.appointments.length," "),t.R7$(5),t.SpI(" ",n.linkedLawyers.length," "),t.R7$(5),t.SpI(" ",n.getConfirmedCount()," "))},dependencies:[d.Sq,d.bT,g.BC,g.vS,s.ZB,s.W9,s.eU,s.iq,s.$w,s.he,s.Nm,s.Ip,s.BC,s.ai,s.Je,s.Gw,d.PV],styles:[".calendar-content[_ngcontent-%COMP%]{--background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%)}.calendar-container[_ngcontent-%COMP%]{padding:20px;min-height:100vh}.filters-section[_ngcontent-%COMP%]{margin-bottom:20px}.filter-row[_ngcontent-%COMP%]{display:grid;grid-template-columns:1fr 1fr;gap:15px}.filter-item[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:8px}.filter-label[_ngcontent-%COMP%]{font-weight:500}.date-input[_ngcontent-%COMP%], .lawyer-select[_ngcontent-%COMP%]{--background: rgba(255, 255, 255, .1);--color: white;--border-radius: 8px;--padding-start: 12px;--padding-end: 12px;border:1px solid rgba(255,255,255,.2);border-radius:8px}.action-section[_ngcontent-%COMP%]{margin-bottom:25px}.create-btn[_ngcontent-%COMP%]{width:100%;display:flex;align-items:center;justify-content:center;gap:8px}.btn-icon[_ngcontent-%COMP%]{font-size:18px}.appointments-section[_ngcontent-%COMP%]{margin-bottom:30px}.section-title[_ngcontent-%COMP%]{margin-bottom:15px}.appointments-list[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:12px}.appointment-card[_ngcontent-%COMP%]{background:#ffffff1a;border-radius:12px;padding:16px;display:grid;grid-template-columns:auto 1fr auto;gap:15px;align-items:center;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px);border:1px solid rgba(255,255,255,.1);transition:all .3s ease}.appointment-card[_ngcontent-%COMP%]:hover{background:#ffffff26;transform:translateY(-2px)}.appointment-time[_ngcontent-%COMP%]{text-align:center;min-width:60px}.time[_ngcontent-%COMP%]{display:block}.appointment-details[_ngcontent-%COMP%]{flex:1}.appointment-title[_ngcontent-%COMP%], .appointment-type[_ngcontent-%COMP%]{margin-bottom:4px}.appointment-lawyer[_ngcontent-%COMP%]{margin-bottom:8px}.appointment-status[_ngcontent-%COMP%]{display:flex;align-items:center}.status-chip[_ngcontent-%COMP%]{--background: rgba(212, 175, 55, .2);--color: #d4af37;height:24px}.status-chip[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-size:11px;font-weight:500}.appointment-actions[_ngcontent-%COMP%]{display:flex;gap:8px}.action-btn[_ngcontent-%COMP%]{width:36px;height:36px;border-radius:8px;border:none;background:#d4af3733;color:#d4af37;display:flex;align-items:center;justify-content:center;transition:all .3s ease}.action-btn[_ngcontent-%COMP%]:hover{background:#d4af374d;transform:scale(1.1)}.action-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:16px}.empty-state[_ngcontent-%COMP%]{text-align:center;padding:40px 20px;background:#ffffff0d;border-radius:12px;border:1px solid rgba(255,255,255,.1)}.empty-icon[_ngcontent-%COMP%]{font-size:48px;color:#d4af37;margin-bottom:16px}.empty-title[_ngcontent-%COMP%]{margin-bottom:8px}.empty-description[_ngcontent-%COMP%]{margin-bottom:20px}.stats-section[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(3,1fr);gap:15px}.stat-card[_ngcontent-%COMP%]{background:#ffffff1a;border-radius:12px;padding:16px;text-align:center;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px);border:1px solid rgba(255,255,255,.1)}.stat-number[_ngcontent-%COMP%]{margin-bottom:4px}.stat-label[_ngcontent-%COMP%]{font-size:11px}@media (max-width: 768px){.filter-row[_ngcontent-%COMP%]{grid-template-columns:1fr}.appointment-card[_ngcontent-%COMP%]{grid-template-columns:1fr;text-align:center}.stats-section[_ngcontent-%COMP%]{grid-template-columns:1fr}}"]})}}return a})()}];let w=(()=>{class a{static{this.\u0275fac=function(i){return new(i||a)}}static{this.\u0275mod=t.$C({type:a})}static{this.\u0275inj=t.G2t({imports:[f.iI.forChild(_),f.iI]})}}return a})(),M=(()=>{class a{static{this.\u0275fac=function(i){return new(i||a)}}static{this.\u0275mod=t.$C({type:a})}static{this.\u0275inj=t.G2t({imports:[d.MD,g.YN,s.bv,w]})}}return a})()}}]);