/*
 * App Global CSS
 * ----------------------------------------------------------------------------
 * Put style rules here that you want to apply globally. These styles are for
 * the entire app and not just one component. Additionally, this file can be
 * used as an entry point to import other CSS/Sass files to be included in the
 * output CSS.
 * For more information on global stylesheets, visit the documentation:
 * https://ionicframework.com/docs/layout/global-stylesheets
 */

/* Core CSS required for Ionic components to work properly */
@import "~@ionic/angular/css/core.css";

/* Basic CSS for apps built with Ionic */
@import "~@ionic/angular/css/normalize.css";
@import "~@ionic/angular/css/structure.css";
@import "~@ionic/angular/css/typography.css";
@import '~@ionic/angular/css/display.css';

/* Optional CSS utils that can be commented out */
@import "~@ionic/angular/css/padding.css";
@import "~@ionic/angular/css/float-elements.css";
@import "~@ionic/angular/css/text-alignment.css";
@import "~@ionic/angular/css/text-transformation.css";
@import "~@ionic/angular/css/flex-utils.css";


@import "./theme/variables.scss";

// Import Client Theme
@import "./app/client-theme.scss";

body, html {
  background: var(--veritus-white) !important;
  background-color: var(--veritus-white) !important;
}
ion-app {
  background: var(--veritus-white) !important;
  background-color: var(--veritus-white) !important;
}
ion-content {
  --background: var(--veritus-white) !important;
  background: var(--veritus-white) !important;
  background-color: var(--veritus-white) !important;
}

/* ----------------------------------------------------------------------------
   5) Typography Utilities
   ---------------------------------------------------------------------------- */
.veritus-text-xs   { font-size: var(--veritus-font-size-xs); }
.veritus-text-sm   { font-size: var(--veritus-font-size-sm); }
.veritus-text-base { font-size: var(--veritus-font-size-base); }
.veritus-text-lg   { font-size: var(--veritus-font-size-lg); }
.veritus-text-xl   { font-size: var(--veritus-font-size-xl); }
.veritus-text-2xl  { font-size: var(--veritus-font-size-2xl); }
.veritus-text-3xl  { font-size: var(--veritus-font-size-3xl); }

.veritus-font-normal   { font-weight: var(--veritus-font-weight-normal); }
.veritus-font-medium   { font-weight: var(--veritus-font-weight-medium); }
.veritus-font-semibold { font-weight: var(--veritus-font-weight-semibold); }
.veritus-font-bold     { font-weight: var(--veritus-font-weight-bold); }

/* ----------------------------------------------------------------------------
   6) Color Utilities
   ---------------------------------------------------------------------------- */
.veritus-text-primary { color: var(--veritus-primary); }
.veritus-text-gold    { color: var(--veritus-gold); }
.veritus-text-white   { color: var(--veritus-white); }
.veritus-text-gray    { color: var(--veritus-gray-medium); }
.veritus-text-dark    { color: var(--veritus-gray-dark); }
.veritus-text-black   { color: var(--veritus-black); }

.veritus-bg-primary      { background-color: var(--veritus-primary); }
.veritus-bg-gold         { background-color: var(--veritus-gold); }
.veritus-bg-white        { background-color: var(--veritus-white); }
.veritus-bg-gray-light   { background-color: var(--veritus-gray-light); }

/* ----------------------------------------------------------------------------
   7) Gradient Backgrounds
   ---------------------------------------------------------------------------- */
.veritus-gradient-auth {
  background: linear-gradient(
    180deg,
    var(--veritus-gradient-dark) 0%,
    var(--veritus-gradient-light) 100%
  );
}
.veritus-gradient-header {
  background: var(--veritus-gold);
}

/* ----------------------------------------------------------------------------
   8) Border Radius
   ---------------------------------------------------------------------------- */
.veritus-rounded-sm   { border-radius: var(--veritus-radius-sm); }
.veritus-rounded-md   { border-radius: var(--veritus-radius-md); }
.veritus-rounded-lg   { border-radius: var(--veritus-radius-lg); }
.veritus-rounded-xl   { border-radius: var(--veritus-radius-xl); }
.veritus-rounded-2xl  { border-radius: var(--veritus-radius-2xl); }
.veritus-rounded-full { border-radius: var(--veritus-radius-full); }

/* ----------------------------------------------------------------------------
   9) Shadows
   ---------------------------------------------------------------------------- */
.veritus-shadow-sm { box-shadow: var(--veritus-shadow-sm); }
.veritus-shadow-md { box-shadow: var(--veritus-shadow-md); }
.veritus-shadow-lg { box-shadow: var(--veritus-shadow-lg); }

/* ----------------------------------------------------------------------------
   10) Button Styles
   ---------------------------------------------------------------------------- */
.veritus-btn-primary {
  background-color: var(--veritus-primary);
  color: var(--veritus-white);
  border-radius: var(--veritus-radius-xl);
  padding: var(--veritus-spacing-lg);
  font-size: var(--veritus-font-size-base);
  font-weight: var(--veritus-font-weight-medium);
  border: none;
  min-height: 48px;
}
.veritus-btn-outline-gold {
  background-color: transparent;
  color: var(--veritus-gold);
  border: 2px solid var(--veritus-gold);
  border-radius: var(--veritus-radius-md);
  padding: var(--veritus-spacing-lg);
  font-size: var(--veritus-font-size-base);
  font-weight: var(--veritus-font-weight-medium);
}

/* ----------------------------------------------------------------------------
   11) Cards
   ---------------------------------------------------------------------------- */
.veritus-card {
  background-color: var(--veritus-white);
  border-radius: var(--veritus-radius-lg);
  box-shadow: var(--veritus-shadow-md);
  padding: var(--veritus-spacing-lg);
  margin: var(--veritus-spacing-sm) 0;
}

/* ----------------------------------------------------------------------------
   12) Inputs
   ---------------------------------------------------------------------------- */
.veritus-input {
  background-color: var(--veritus-white);
  border: 2px solid var(--veritus-gold-muted);
  border-radius: var(--veritus-radius-md);
  padding: var(--veritus-spacing-lg);
  font-size: var(--veritus-font-size-base);

  &::placeholder {
    color: var(--veritus-gold-muted);
  }
  &:focus {
    border-color: var(--veritus-gold);
    outline: none;
  }
}
.veritus-input-auth {
  background-color: transparent;
  border: 2px solid var(--veritus-gold);
  border-radius: var(--veritus-radius-md);
  padding: var(--veritus-spacing-lg);
  color: var(--veritus-white);
  font-size: var(--veritus-font-size-base);

  &::placeholder {
    color: var(--veritus-gold-muted);
  }
}

/* ----------------------------------------------------------------------------
   13) Avatars
   ---------------------------------------------------------------------------- */
.veritus-avatar {
  border-radius: var(--veritus-radius-full);
  object-fit: cover;
}
.veritus-avatar-sm { width: 48px;  height: 48px;  }
.veritus-avatar-md { width: 72px;  height: 72px;  }
.veritus-avatar-lg { width: 100px; height: 100px; }

/* ----------------------------------------------------------------------------
   14) Safe‐Area Helpers
   ---------------------------------------------------------------------------- */
.veritus-safe-area-top    { padding-top:    env(safe-area-inset-top);    }
.veritus-safe-area-bottom { padding-bottom: env(safe-area-inset-bottom); }

/* ----------------------------------------------------------------------------
   15) Status Badges
   ---------------------------------------------------------------------------- */
.veritus-badge {
  padding: var(--veritus-spacing-xs) var(--veritus-spacing-md);
  border-radius: var(--veritus-radius-xl);
  font-size: var(--veritus-font-size-xs);
  font-weight: var(--veritus-font-weight-medium);
  color: var(--veritus-white);

  &.badge-new     { background-color: var(--veritus-success); }
  &.badge-active  { background-color: var(--veritus-primary); }
  &.badge-closed  { background-color: var(--veritus-gray-medium); }
  &.badge-overdue { background-color: var(--veritus-danger); }
}

/* ----------------------------------------------------------------------------
   16) Mobile Responsive Tweaks
   ---------------------------------------------------------------------------- */
@media (max-width: 375px) {
  .veritus-text-3xl { font-size: 28px; }
  .veritus-text-2xl { font-size: 20px; }
}