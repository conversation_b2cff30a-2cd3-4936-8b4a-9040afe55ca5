.verification-container {
  padding: 0;
  margin-left: 250px; // Account for sidebar
}

.verification-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  
  h1 {
    margin: 0;
    font-size: 2rem;
    font-weight: 700;
    color: #333;
  }
  
  .header-actions {
    display: flex;
    gap: 15px;
    align-items: center;
    
    .search-container {
      position: relative;
      
      .search-input {
        width: 300px;
        padding: 10px 40px 10px 15px;
        border: 1px solid #e1e5e9;
        border-radius: 25px;
        font-size: 0.9rem;
        transition: all 0.3s ease;
        
        &:focus {
          outline: none;
          border-color: var(--admin-primary);
          box-shadow: 0 0 0 3px rgba(196, 154, 86, 0.1);
        }
        
        &::placeholder {
          color: #999;
        }
      }
      
      .search-icon {
        position: absolute;
        right: 15px;
        top: 50%;
        transform: translateY(-50%);
        color: #666;
        font-size: 16px;
      }
    }
    
    .status-filter {
      padding: 10px 15px;
      border: 1px solid #e1e5e9;
      border-radius: 8px;
      font-size: 0.9rem;
      background: white;
      min-width: 150px;
      
      &:focus {
        outline: none;
        border-color: var(--admin-primary);
      }
    }
  }
}

.verification-stats {
  display: flex;
  gap: 20px;
  margin-bottom: 30px;
  
  .stat-item {
    background: white;
    padding: 20px;
    border-radius: 8px;
    border: 1px solid #e1e5e9;
    text-align: center;
    min-width: 120px;
    
    .stat-number {
      display: block;
      font-size: 2rem;
      font-weight: 700;
      color: var(--admin-primary);
      margin-bottom: 5px;
    }
    
    .stat-label {
      font-size: 0.9rem;
      color: #666;
      font-weight: 500;
    }
  }
}

.verification-list {
  .lawyer-card {
    background: white;
    border: 1px solid #e1e5e9;
    border-radius: 12px;
    padding: 25px;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 25px;
    transition: all 0.3s ease;
    
    &:hover {
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
      transform: translateY(-2px);
    }
    
    .lawyer-info {
      display: flex;
      align-items: center;
      flex: 1;
      gap: 20px;
      
      .lawyer-avatar {
        width: 80px;
        height: 80px;
        border-radius: 50%;
        overflow: hidden;
        border: 3px solid #e1e5e9;
        position: relative;

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
          display: block;
        }

        .avatar-placeholder {
          width: 100%;
          height: 100%;
          background: linear-gradient(135deg, var(--admin-primary), #d4a574);
          display: flex;
          align-items: center;
          justify-content: center;
          color: white;
          font-weight: 600;
          font-size: 24px;
          text-transform: uppercase;
        }
      }
      
      .lawyer-details {
        h3 {
          margin: 0 0 10px 0;
          font-size: 1.3rem;
          font-weight: 600;
          color: #333;
        }
        
        p {
          margin: 0 0 5px 0;
          font-size: 0.9rem;
          color: #666;
          line-height: 1.4;
          
          strong {
            color: #333;
          }
        }
      }
    }
    
    .verification-status {
      text-align: center;
      min-width: 150px;
      
      .status-badge {
        display: inline-block;
        padding: 8px 16px;
        border-radius: 20px;
        font-size: 0.85rem;
        font-weight: 600;
        margin-bottom: 10px;
        
        &.pending {
          background: rgba(255, 193, 7, 0.1);
          color: #ffc107;
          border: 1px solid rgba(255, 193, 7, 0.3);
        }
        
        &.approved {
          background: rgba(40, 167, 69, 0.1);
          color: #28a745;
          border: 1px solid rgba(40, 167, 69, 0.3);
        }
        
        &.rejected {
          background: rgba(220, 53, 69, 0.1);
          color: #dc3545;
          border: 1px solid rgba(220, 53, 69, 0.3);
        }
        
        &.under_review {
          background: rgba(23, 162, 184, 0.1);
          color: #17a2b8;
          border: 1px solid rgba(23, 162, 184, 0.3);
        }
      }
      
      .verification-progress {
        .progress-bar {
          width: 100%;
          height: 6px;
          background: #e1e5e9;
          border-radius: 3px;
          overflow: hidden;
          margin-bottom: 5px;
          
          .progress-fill {
            height: 100%;
            background: var(--admin-primary);
            transition: width 0.3s ease;
          }
        }
        
        .progress-text {
          font-size: 0.75rem;
          color: #666;
        }
      }
    }
    
    .lawyer-actions {
      display: flex;
      flex-direction: column;
      gap: 10px;
      min-width: 120px;
      
      .action-btn {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 8px;
        padding: 10px 15px;
        border: none;
        border-radius: 6px;
        font-size: 0.85rem;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.3s ease;
        
        .btn-icon {
          font-size: 14px;
        }
        
        &:disabled {
          opacity: 0.5;
          cursor: not-allowed;
        }
        
        &.view-btn {
          background: #6c757d;
          color: white;
          
          &:hover:not(:disabled) {
            background: #5a6268;
          }
        }
        
        &.approve-btn {
          background: #28a745;
          color: white;
          
          &:hover:not(:disabled) {
            background: #218838;
          }
        }
        
        &.reject-btn {
          background: #dc3545;
          color: white;
          
          &:hover:not(:disabled) {
            background: #c82333;
          }
        }
      }
    }
  }
  
  .no-results {
    text-align: center;
    padding: 60px 20px;
    background: white;
    border-radius: 12px;
    border: 1px solid #e1e5e9;
    
    .no-results-icon {
      font-size: 4rem;
      margin-bottom: 20px;
      opacity: 0.5;
    }
    
    h3 {
      margin: 0 0 10px 0;
      color: #333;
      font-size: 1.5rem;
    }
    
    p {
      margin: 0;
      color: #666;
      font-size: 1rem;
    }
  }
}

.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 20px;
  margin-top: 30px;
  padding: 20px;

  .page-btn {
    padding: 10px 20px;
    border: 1px solid #e1e5e9;
    background: white;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover:not(:disabled) {
      background: var(--admin-primary);
      color: white;
      border-color: var(--admin-primary);
    }

    &:disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }
  }

  .page-info {
    font-size: 0.9rem;
    color: #666;
  }
}

// Modal styles
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;

  .modal-content {
    background: white;
    border-radius: 12px;
    width: 90%;
    max-width: 800px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);

    .modal-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 25px 30px;
      border-bottom: 1px solid #e1e5e9;

      h2 {
        margin: 0;
        font-size: 1.5rem;
        font-weight: 600;
        color: #333;
      }

      .close-btn {
        background: none;
        border: none;
        font-size: 2rem;
        cursor: pointer;
        color: #666;
        line-height: 1;

        &:hover {
          color: #333;
        }
      }
    }

    .modal-body {
      padding: 30px;

      .lawyer-profile {
        display: flex;
        gap: 25px;
        margin-bottom: 30px;
        padding-bottom: 25px;
        border-bottom: 1px solid #e1e5e9;

        .profile-image {
          width: 120px;
          height: 120px;
          border-radius: 50%;
          overflow: hidden;
          border: 4px solid #e1e5e9;
          position: relative;

          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            display: block;
          }

          .avatar-placeholder {
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, var(--admin-primary), #d4a574);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
            font-size: 36px;
            text-transform: uppercase;
          }
        }

        .profile-info {
          flex: 1;

          h3 {
            margin: 0 0 15px 0;
            font-size: 1.8rem;
            font-weight: 600;
            color: #333;
          }

          p {
            margin: 0 0 8px 0;
            font-size: 1rem;
            color: #666;

            strong {
              color: #333;
              margin-right: 8px;
            }
          }
        }
      }

      .documents-section {
        margin-bottom: 30px;

        h4 {
          margin: 0 0 20px 0;
          font-size: 1.2rem;
          font-weight: 600;
          color: #333;
        }

        .document-list {
          .document-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px;
            border: 1px solid #e1e5e9;
            border-radius: 8px;
            margin-bottom: 10px;

            .doc-info {
              display: flex;
              align-items: center;
              gap: 12px;

              .doc-icon {
                font-size: 20px;
              }

              .doc-name {
                font-weight: 500;
                color: #333;
              }

              .doc-type {
                color: #666;
                font-size: 0.85rem;
              }
            }

            .doc-actions {
              display: flex;
              gap: 10px;

              .doc-btn {
                padding: 6px 12px;
                border: none;
                border-radius: 4px;
                font-size: 0.8rem;
                cursor: pointer;
                transition: all 0.3s ease;

                &.view {
                  background: #17a2b8;
                  color: white;

                  &:hover {
                    background: #138496;
                  }
                }

                &.download {
                  background: #6c757d;
                  color: white;

                  &:hover {
                    background: #5a6268;
                  }
                }
              }
            }
          }
        }
      }

      .verification-section {
        h4 {
          margin: 0 0 20px 0;
          font-size: 1.2rem;
          font-weight: 600;
          color: #333;
        }

        .verification-form {
          .form-group {
            margin-bottom: 20px;

            label {
              display: block;
              margin-bottom: 8px;
              font-weight: 500;
              color: #333;
            }

            textarea, select {
              width: 100%;
              padding: 12px;
              border: 1px solid #e1e5e9;
              border-radius: 6px;
              font-size: 0.9rem;
              box-sizing: border-box;

              &:focus {
                outline: none;
                border-color: var(--admin-primary);
                box-shadow: 0 0 0 3px rgba(196, 154, 86, 0.1);
              }
            }

            textarea {
              resize: vertical;
              min-height: 100px;
            }

            .ibp-tools {
              margin-top: 15px;
              padding: 15px;
              background: #f8f9fa;
              border: 1px solid #e1e5e9;
              border-radius: 6px;
              text-align: center;

              .ibp-btn {
                background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-size: 0.85rem;
                font-weight: 500;
                cursor: pointer;
                transition: all 0.3s ease;
                box-shadow: 0 2px 4px rgba(23, 162, 184, 0.2);

                &:hover {
                  background: linear-gradient(135deg, #138496 0%, #117a8b 100%);
                  transform: translateY(-1px);
                  box-shadow: 0 4px 8px rgba(23, 162, 184, 0.3);
                }

                &:active {
                  transform: translateY(0);
                  box-shadow: 0 2px 4px rgba(23, 162, 184, 0.2);
                }
              }
            }
          }

          .action-buttons {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;

            .modal-btn {
              display: flex;
              align-items: center;
              gap: 8px;
              padding: 12px 20px;
              border: none;
              border-radius: 6px;
              font-size: 0.9rem;
              font-weight: 500;
              cursor: pointer;
              transition: all 0.3s ease;

              .btn-icon {
                font-size: 16px;
              }

              &.approve {
                background: #28a745;
                color: white;

                &:hover {
                  background: #218838;
                }
              }

              &.reject {
                background: #dc3545;
                color: white;

                &:hover {
                  background: #c82333;
                }
              }

              &.request-docs {
                background: #ffc107;
                color: #212529;

                &:hover {
                  background: #e0a800;
                }
              }
            }
          }
        }
      }
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .verification-container {
    margin-left: 0;
    padding: 15px;
  }

  .verification-header {
    flex-direction: column;
    gap: 20px;
    align-items: stretch;

    .header-actions {
      flex-direction: column;
      gap: 15px;

      .search-container .search-input {
        width: 100%;
      }
    }
  }

  .verification-stats {
    flex-wrap: wrap;
    gap: 15px;

    .stat-item {
      flex: 1;
      min-width: 100px;
    }
  }

  .lawyer-card {
    flex-direction: column;
    gap: 20px;

    .lawyer-info {
      flex-direction: column;
      text-align: center;
      gap: 15px;
    }

    .lawyer-actions {
      flex-direction: row;
      justify-content: center;
      flex-wrap: wrap;
    }
  }

  .modal-content {
    width: 95%;
    margin: 20px;

    .modal-body {
      padding: 20px;

      .lawyer-profile {
        flex-direction: column;
        text-align: center;

        .profile-image {
          align-self: center;
        }
      }

      .verification-form .action-buttons {
        flex-direction: column;

        .modal-btn {
          justify-content: center;
        }
      }
    }
  }
}
