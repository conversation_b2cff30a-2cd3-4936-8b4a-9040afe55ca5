{"version": 3, "file": "node_modules_ionic_core_dist_esm_ion-radio_2_entry_js.js", "mappings": ";;;;;;;;;;;;;;AAAA;AACA;AACA;AAC2D;;AAE3D;AACA;AACA;AACA;AACA;AACA;AACA,MAAME,0BAA0B,GAAIC,EAAE,IAAK;EACvC,MAAMC,SAAS,GAAGD,EAAE;EACpB,IAAIE,aAAa;EACjB,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;IAC3B,IAAID,aAAa,KAAKE,SAAS,EAAE;MAC7B;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACY,MAAMC,YAAY,GAAGJ,SAAS,CAACK,KAAK,KAAKF,SAAS,IAAIG,YAAY,CAACN,SAAS,CAAC;MAC7E,MAAMO,qBAAqB,GAAGP,SAAS,CAACQ,YAAY,CAAC,YAAY,CAAC;MAC9D;MACCR,SAAS,CAACQ,YAAY,CAAC,iBAAiB,CAAC,IAAIR,SAAS,CAACS,UAAU,KAAK,IAAK;MAChF,MAAMC,eAAe,GAAGb,uDAAa,CAACG,SAAS,CAAC;MAChD;AACZ;AACA;AACA;MACYC,aAAa,GACTD,SAAS,CAACW,MAAM,KAAK,IAAI,IAAK,CAACP,YAAY,IAAI,CAACG,qBAAqB,IAAIG,eAAe,KAAK,IAAK;IAC1G;IACA,OAAOT,aAAa;EACxB,CAAC;EACD,OAAO;IAAEC;EAAiB,CAAC;AAC/B,CAAC;AACD,MAAMI,YAAY,GAAIN,SAAS,IAAK;EAChC;AACJ;AACA;AACA;AACA;EACI,IAAIY,2BAA2B,CAACC,QAAQ,CAACb,SAAS,CAACc,OAAO,CAAC,IAAId,SAAS,CAACe,aAAa,CAAC,gBAAgB,CAAC,KAAK,IAAI,EAAE;IAC/G,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;EACI,IAAIC,6BAA6B,CAACH,QAAQ,CAACb,SAAS,CAACc,OAAO,CAAC,IAAId,SAAS,CAACiB,WAAW,KAAK,EAAE,EAAE;IAC3F,OAAO,IAAI;EACf;EACA,OAAO,KAAK;AAChB,CAAC;AACD,MAAML,2BAA2B,GAAG,CAAC,WAAW,EAAE,cAAc,EAAE,YAAY,EAAE,WAAW,CAAC;AAC5F,MAAMI,6BAA6B,GAAG,CAAC,YAAY,EAAE,cAAc,EAAE,WAAW,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;AC7DjF;AACA;AACA;AAC6G;AAC7B;AACmD;AAC1D;AACd;AACqB;AACnB;AAE7D,MAAMyB,WAAW,GAAG,kmNAAkmN;AACtnN,MAAMC,iBAAiB,GAAGD,WAAW;AAErC,MAAME,UAAU,GAAG,y/OAAy/O;AAC5gP,MAAMC,gBAAgB,GAAGD,UAAU;AAEnC,MAAME,KAAK,GAAG,MAAM;EAChBC,WAAWA,CAACC,OAAO,EAAE;IACjB3B,qDAAgB,CAAC,IAAI,EAAE2B,OAAO,CAAC;IAC/B,IAAI,CAACC,QAAQ,GAAG1B,qDAAW,CAAC,IAAI,EAAE,UAAU,EAAE,CAAC,CAAC;IAChD,IAAI,CAAC2B,QAAQ,GAAG3B,qDAAW,CAAC,IAAI,EAAE,UAAU,EAAE,CAAC,CAAC;IAChD,IAAI,CAAC4B,OAAO,GAAG5B,qDAAW,CAAC,IAAI,EAAE,SAAS,EAAE,CAAC,CAAC;IAC9C,IAAI,CAAC6B,OAAO,GAAG,UAAUC,cAAc,EAAE,EAAE;IAC3C,IAAI,CAACC,UAAU,GAAG,IAAI;IACtB;IACA,IAAI,CAACC,2BAA2B,GAAG,KAAK;IACxC,IAAI,CAACC,WAAW,GAAG,MAAM;MACrB,IAAI,IAAI,CAACF,UAAU,EAAE;QACjB,MAAM;UAAEG,WAAW;UAAEC,KAAK,EAAEC;QAAgB,CAAC,GAAG,IAAI,CAACL,UAAU;QAC/D,IAAI,CAACM,OAAO,GAAGxB,kEAAgB,CAACuB,eAAe,EAAE,IAAI,CAACD,KAAK,EAAED,WAAW,CAAC;MAC7E;IACJ,CAAC;IACD,IAAI,CAACI,OAAO,GAAG,MAAM;MACjB,MAAM;QAAEP,UAAU;QAAEM,OAAO;QAAEE;MAAS,CAAC,GAAG,IAAI;MAC9C,IAAIA,QAAQ,EAAE;QACV;MACJ;MACA;AACZ;AACA;AACA;AACA;AACA;AACA;MACY,IAAI,IAAI,CAACC,oBAAoB,CAAC5D,gBAAgB,CAAC,CAAC,EAAE;QAC9C,IAAI,CAACyD,OAAO,GAAG,IAAI,CAACI,WAAW,CAACJ,OAAO;QACvC;MACJ;MACA;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACY,IAAIA,OAAO,KAAKN,UAAU,KAAK,IAAI,IAAIA,UAAU,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,UAAU,CAACW,mBAAmB,CAAC,EAAE;QACrG,IAAI,CAACL,OAAO,GAAG,KAAK;MACxB,CAAC,MACI;QACD,IAAI,CAACA,OAAO,GAAG,IAAI;MACvB;IACJ,CAAC;IACD,IAAI,CAACM,OAAO,GAAG,MAAM;MACjB,IAAI,CAAChB,QAAQ,CAACiB,IAAI,CAAC,CAAC;IACxB,CAAC;IACD,IAAI,CAACC,MAAM,GAAG,MAAM;MAChB,IAAI,CAACjB,OAAO,CAACgB,IAAI,CAAC,CAAC;IACvB,CAAC;IACD,IAAI,CAACP,OAAO,GAAG,KAAK;IACpB,IAAI,CAACS,cAAc,GAAG,CAAC,CAAC;IACxB,IAAI,CAACC,KAAK,GAAGlE,SAAS;IACtB,IAAI,CAACmE,IAAI,GAAG,IAAI,CAACnB,OAAO;IACxB,IAAI,CAACU,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACJ,KAAK,GAAGtD,SAAS;IACtB,IAAI,CAACoE,cAAc,GAAG,OAAO;IAC7B,IAAI,CAAC5D,MAAM,GAAGR,SAAS;IACvB,IAAI,CAACqE,OAAO,GAAG,eAAe;IAC9B,IAAI,CAACC,SAAS,GAAG,QAAQ;EAC7B;EACAC,YAAYA,CAAA,EAAG;IACX;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACnB,WAAW,CAAC,CAAC;EACtB;EACA;EACMoB,QAAQA,CAACC,EAAE,EAAE;IAAA,IAAAC,KAAA;IAAA,OAAAC,6KAAA;MACfF,EAAE,CAACG,eAAe,CAAC,CAAC;MACpBH,EAAE,CAACI,cAAc,CAAC,CAAC;MACnBH,KAAI,CAAC9E,EAAE,CAACkF,KAAK,CAAC,CAAC;IAAC;EACpB;EACA;EACMC,iBAAiBA,CAACzB,KAAK,EAAE;IAAA,IAAA0B,MAAA;IAAA,OAAAL,6KAAA;MAC3BK,MAAI,CAACf,cAAc,GAAGX,KAAK;IAAC;EAChC;EACA2B,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAACtB,oBAAoB,GAAGhE,+DAA0B,CAAC,IAAI,CAACC,EAAE,CAAC;IAC/D,IAAI,IAAI,CAAC0D,KAAK,KAAKtD,SAAS,EAAE;MAC1B,IAAI,CAACsD,KAAK,GAAG,IAAI,CAACN,OAAO;IAC7B;IACA,MAAME,UAAU,GAAI,IAAI,CAACA,UAAU,GAAG,IAAI,CAACtD,EAAE,CAACsF,OAAO,CAAC,iBAAiB,CAAE;IACzE,IAAIhC,UAAU,EAAE;MACZ,IAAI,CAACE,WAAW,CAAC,CAAC;MAClB3B,uDAAgB,CAACyB,UAAU,EAAE,gBAAgB,EAAE,IAAI,CAACE,WAAW,CAAC;IACpE;EACJ;EACA+B,oBAAoBA,CAAA,EAAG;IACnB,MAAMjC,UAAU,GAAG,IAAI,CAACA,UAAU;IAClC,IAAIA,UAAU,EAAE;MACZvB,uDAAmB,CAACuB,UAAU,EAAE,gBAAgB,EAAE,IAAI,CAACE,WAAW,CAAC;MACnE,IAAI,CAACF,UAAU,GAAG,IAAI;IAC1B;EACJ;EACAkC,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAACC,SAAS,CAAC,CAAC;EACpB;EACAC,YAAYA,CAAA,EAAG;IACX,IAAI,CAACD,SAAS,CAAC,CAAC;EACpB;EACAA,SAASA,CAAA,EAAG;IACR,MAAME,KAAK,GAAG;MACV,sBAAsB,EAAE,IAAI,CAAC7B,QAAQ;MACrC;MACAlD,MAAM,EAAE,CAAC,CAAC,IAAI,CAACA;IACnB,CAAC;IACD,IAAI,IAAI,CAACmD,oBAAoB,CAAC5D,gBAAgB,CAAC,CAAC,EAAE;MAC9CwF,KAAK,CAAC,eAAe,CAAC,GAAG,IAAI,CAAC/B,OAAO;IACzC;IACA,IAAI,CAACX,QAAQ,CAACkB,IAAI,CAACwB,KAAK,CAAC;EAC7B;EACA,IAAIC,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAAC5F,EAAE,CAACkB,WAAW,KAAK,EAAE;EACrC;EACA2E,kBAAkBA,CAAA,EAAG;IACjB,OAAQhG,qDAAC,CAAC,KAAK,EAAE;MAAEiG,KAAK,EAAE,YAAY;MAAEC,IAAI,EAAE;IAAY,CAAC,EAAElG,qDAAC,CAAC,KAAK,EAAE;MAAEiG,KAAK,EAAE,aAAa;MAAEC,IAAI,EAAE;IAAO,CAAC,CAAC,EAAElG,qDAAC,CAAC,KAAK,EAAE;MAAEiG,KAAK,EAAE;IAAe,CAAC,CAAC,CAAC;EACvJ;EACAE,MAAMA,CAAA,EAAG;IACL,MAAM;MAAEjC;IAAqB,CAAC,GAAG,IAAI;IACrC,OAAOA,oBAAoB,CAAC5D,gBAAgB,CAAC,CAAC,GAAG,IAAI,CAAC8F,iBAAiB,CAAC,CAAC,GAAG,IAAI,CAACC,WAAW,CAAC,CAAC;EAClG;EACAA,WAAWA,CAAA,EAAG;IACV,MAAM;MAAEtC,OAAO;MAAEE,QAAQ;MAAEQ,KAAK;MAAEtE,EAAE;MAAEyE,OAAO;MAAED,cAAc;MAAEoB,QAAQ;MAAEvB,cAAc;MAAEK;IAAU,CAAC,GAAG,IAAI;IAC3G,MAAMyB,IAAI,GAAG1D,4DAAU,CAAC,IAAI,CAAC;IAC7B,MAAM2D,MAAM,GAAG7D,qDAAW,CAAC,UAAU,EAAEvC,EAAE,CAAC;IAC1C,OAAQH,qDAAC,CAAC4B,iDAAI,EAAE;MAAEyC,OAAO,EAAE,IAAI,CAACA,OAAO;MAAEE,MAAM,EAAE,IAAI,CAACA,MAAM;MAAEP,OAAO,EAAE,IAAI,CAACA,OAAO;MAAEiC,KAAK,EAAEtD,qDAAkB,CAAC8B,KAAK,EAAE;QAC9G,CAAC6B,IAAI,GAAG,IAAI;QACZ,SAAS,EAAEC,MAAM;QACjB,eAAe,EAAExC,OAAO;QACxB,gBAAgB,EAAEE,QAAQ;QAC1B,CAAC,iBAAiBW,OAAO,EAAE,GAAG,IAAI;QAClC,CAAC,mBAAmBC,SAAS,EAAE,GAAG,IAAI;QACtC,CAAC,yBAAyBF,cAAc,EAAE,GAAG,IAAI;QACjD;QACA,iBAAiB,EAAE,CAAC4B,MAAM;QAC1B,eAAe,EAAE,CAACA;MACtB,CAAC,CAAC;MAAEC,IAAI,EAAE,OAAO;MAAE,cAAc,EAAEzC,OAAO,GAAG,MAAM,GAAG,OAAO;MAAE,eAAe,EAAEE,QAAQ,GAAG,MAAM,GAAG,IAAI;MAAEwC,QAAQ,EAAEjC;IAAe,CAAC,EAAExE,qDAAC,CAAC,OAAO,EAAE;MAAEiG,KAAK,EAAE;IAAgB,CAAC,EAAEjG,qDAAC,CAAC,KAAK,EAAE;MAAEiG,KAAK,EAAE;QAC3L,oBAAoB,EAAE,IAAI;QAC1B,2BAA2B,EAAE,CAACF;MAClC,CAAC;MAAEG,IAAI,EAAE;IAAQ,CAAC,EAAElG,qDAAC,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC,EAAEA,qDAAC,CAAC,KAAK,EAAE;MAAEiG,KAAK,EAAE;IAAiB,CAAC,EAAE,IAAI,CAACD,kBAAkB,CAAC,CAAC,CAAC,CAAC,CAAC;EAChH;EACAI,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAAC,IAAI,CAAC1C,2BAA2B,EAAE;MACnCjB,qDAAe,CAAC;AAC5B;AACA;AACA;AACA;AACA,gNAAgN,EAAE,IAAI,CAACtC,EAAE,CAAC;MAC9M,IAAI,IAAI,CAACY,MAAM,EAAE;QACb0B,qDAAe,CAAC;AAChC;AACA,qHAAqH,EAAE,IAAI,CAACtC,EAAE,CAAC;MACnH;MACA,IAAI,CAACuD,2BAA2B,GAAG,IAAI;IAC3C;IACA,MAAM;MAAEH,OAAO;MAAEU,QAAQ;MAAEF,OAAO;MAAEU,KAAK;MAAEtE,EAAE;MAAEqE;IAAe,CAAC,GAAG,IAAI;IACtE,MAAM8B,IAAI,GAAG1D,4DAAU,CAAC,IAAI,CAAC;IAC7B,MAAM;MAAEnC,KAAK;MAAEiG,OAAO;MAAEC;IAAU,CAAC,GAAGvE,uDAAY,CAACjC,EAAE,EAAEoD,OAAO,CAAC;IAC/D,OAAQvD,qDAAC,CAAC4B,iDAAI,EAAE;MAAE,cAAc,EAAE,GAAGmC,OAAO,EAAE;MAAE,aAAa,EAAEE,QAAQ,GAAG,MAAM,GAAG,IAAI;MAAE,iBAAiB,EAAExD,KAAK,GAAGiG,OAAO,GAAG,IAAI;MAAEF,IAAI,EAAE,OAAO;MAAEC,QAAQ,EAAEjC,cAAc;MAAEH,OAAO,EAAE,IAAI,CAACA,OAAO;MAAEE,MAAM,EAAE,IAAI,CAACA,MAAM;MAAEP,OAAO,EAAE,IAAI,CAACA,OAAO;MAAEiC,KAAK,EAAEtD,qDAAkB,CAAC8B,KAAK,EAAE;QACzQ,CAAC6B,IAAI,GAAG,IAAI;QACZ,SAAS,EAAE5D,qDAAW,CAAC,UAAU,EAAEvC,EAAE,CAAC;QACtCyG,WAAW,EAAE,IAAI;QACjB,eAAe,EAAE7C,OAAO;QACxB,gBAAgB,EAAEE,QAAQ;QAC1B,cAAc,EAAE;MACpB,CAAC;IAAE,CAAC,EAAE,IAAI,CAAC+B,kBAAkB,CAAC,CAAC,EAAEhG,qDAAC,CAAC,OAAO,EAAE;MAAE6G,OAAO,EAAEtD;IAAQ,CAAC,EAAEoD,SAAS,CAAC,EAAE3G,qDAAC,CAAC,OAAO,EAAE;MAAE8G,IAAI,EAAE,OAAO;MAAE/C,OAAO,EAAEA,OAAO;MAAEE,QAAQ,EAAEA,QAAQ;MAAEwC,QAAQ,EAAE,IAAI;MAAEM,EAAE,EAAExD,OAAO;MAAEyD,GAAG,EAAGC,QAAQ,IAAM,IAAI,CAAC9C,WAAW,GAAG8C;IAAU,CAAC,CAAC,CAAC;EACzO;EACA,IAAI9G,EAAEA,CAAA,EAAG;IAAE,OAAO2B,qDAAU,CAAC,IAAI,CAAC;EAAE;EACpC,WAAWoF,QAAQA,CAAA,EAAG;IAAE,OAAO;MAC3B,OAAO,EAAE,CAAC,cAAc,CAAC;MACzB,SAAS,EAAE,CAAC,cAAc,CAAC;MAC3B,OAAO,EAAE,CAAC,cAAc,CAAC;MACzB,UAAU,EAAE,CAAC,cAAc;IAC/B,CAAC;EAAE;AACP,CAAC;AACD,IAAI1D,cAAc,GAAG,CAAC;AACtBP,KAAK,CAAC6C,KAAK,GAAG;EACVqB,GAAG,EAAErE,iBAAiB;EACtBsE,EAAE,EAAEpE;AACR,CAAC;AAED,MAAMqE,UAAU,GAAG,MAAM;EACrBnE,WAAWA,CAACC,OAAO,EAAE;IACjB3B,qDAAgB,CAAC,IAAI,EAAE2B,OAAO,CAAC;IAC/B,IAAI,CAACmE,SAAS,GAAG5F,qDAAW,CAAC,IAAI,EAAE,WAAW,EAAE,CAAC,CAAC;IAClD,IAAI,CAAC6F,cAAc,GAAG7F,qDAAW,CAAC,IAAI,EAAE,gBAAgB,EAAE,CAAC,CAAC;IAC5D,IAAI,CAAC6B,OAAO,GAAG,UAAUiE,aAAa,EAAE,EAAE;IAC1C,IAAI,CAACd,OAAO,GAAG,GAAG,IAAI,CAACnD,OAAO,MAAM;IACpC,IAAI,CAACkE,gBAAgB,GAAI5D,KAAK,IAAK;MAC/B,MAAM6D,MAAM,GAAG,IAAI,CAACC,SAAS,CAAC,CAAC;MAC/B;MACA,MAAMC,KAAK,GAAGF,MAAM,CAACG,IAAI,CAAEC,KAAK,IAAK,CAACA,KAAK,CAAC7D,QAAQ,CAAC;MACrD,MAAMF,OAAO,GAAG2D,MAAM,CAACG,IAAI,CAAEC,KAAK,IAAKA,KAAK,CAACjE,KAAK,KAAKA,KAAK,IAAI,CAACiE,KAAK,CAAC7D,QAAQ,CAAC;MAChF,IAAI,CAAC2D,KAAK,IAAI,CAAC7D,OAAO,EAAE;QACpB;MACJ;MACA;MACA;MACA,MAAMgE,SAAS,GAAGhE,OAAO,IAAI6D,KAAK;MAClC,KAAK,MAAME,KAAK,IAAIJ,MAAM,EAAE;QACxB,MAAMjB,QAAQ,GAAGqB,KAAK,KAAKC,SAAS,GAAG,CAAC,GAAG,CAAC,CAAC;QAC7CD,KAAK,CAACxC,iBAAiB,CAACmB,QAAQ,CAAC;MACrC;IACJ,CAAC;IACD,IAAI,CAACzC,OAAO,GAAIgB,EAAE,IAAK;MACnBA,EAAE,CAACI,cAAc,CAAC,CAAC;MACnB;AACZ;AACA;AACA;AACA;AACA;MACY,MAAM4C,aAAa,GAAGhD,EAAE,CAACiD,MAAM,IAAIjD,EAAE,CAACiD,MAAM,CAACxC,OAAO,CAAC,WAAW,CAAC;MACjE;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;MACY,IAAIuC,aAAa,IAAI,CAACA,aAAa,CAAC/D,QAAQ,EAAE;QAC1C,MAAMiE,YAAY,GAAG,IAAI,CAACrE,KAAK;QAC/B,MAAMsE,QAAQ,GAAGH,aAAa,CAACnE,KAAK;QACpC,IAAIsE,QAAQ,KAAKD,YAAY,EAAE;UAC3B,IAAI,CAACrE,KAAK,GAAGsE,QAAQ;UACrB,IAAI,CAACC,eAAe,CAACpD,EAAE,CAAC;QAC5B,CAAC,MACI,IAAI,IAAI,CAACZ,mBAAmB,EAAE;UAC/B,IAAI,CAACP,KAAK,GAAGtD,SAAS;UACtB,IAAI,CAAC6H,eAAe,CAACpD,EAAE,CAAC;QAC5B;MACJ;IACJ,CAAC;IACD,IAAI,CAACZ,mBAAmB,GAAG,KAAK;IAChC,IAAI,CAACR,WAAW,GAAGrD,SAAS;IAC5B,IAAI,CAACmE,IAAI,GAAG,IAAI,CAACnB,OAAO;IACxB,IAAI,CAACM,KAAK,GAAGtD,SAAS;EAC1B;EACAuE,YAAYA,CAACjB,KAAK,EAAE;IAChB,IAAI,CAAC4D,gBAAgB,CAAC5D,KAAK,CAAC;IAC5B,IAAI,CAAC0D,cAAc,CAACjD,IAAI,CAAC;MAAET;IAAM,CAAC,CAAC;EACvC;EACAwE,gBAAgBA,CAAA,EAAG;IACf;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACvD,YAAY,CAAC,IAAI,CAACjB,KAAK,CAAC;EACjC;EACM2B,iBAAiBA,CAAA,EAAG;IAAA,IAAA8C,MAAA;IAAA,OAAApD,6KAAA;MACtB;MACA;MACA,MAAMqD,MAAM,GAAGD,MAAI,CAACnI,EAAE,CAACgB,aAAa,CAAC,iBAAiB,CAAC,IAAImH,MAAI,CAACnI,EAAE,CAACgB,aAAa,CAAC,kBAAkB,CAAC;MACpG,IAAIoH,MAAM,EAAE;QACR,MAAM9H,KAAK,GAAI6H,MAAI,CAAC7H,KAAK,GAAG8H,MAAM,CAACpH,aAAa,CAAC,WAAW,CAAE;QAC9D,IAAIV,KAAK,EAAE;UACP6H,MAAI,CAAC5B,OAAO,GAAGjG,KAAK,CAACsG,EAAE,GAAGuB,MAAI,CAAC5D,IAAI,GAAG,MAAM;QAChD;MACJ;IAAC;EACL;EACAiD,SAASA,CAAA,EAAG;IACR,OAAOa,KAAK,CAACC,IAAI,CAAC,IAAI,CAACtI,EAAE,CAACuI,gBAAgB,CAAC,WAAW,CAAC,CAAC;EAC5D;EACA;AACJ;AACA;AACA;AACA;AACA;EACIN,eAAeA,CAACO,KAAK,EAAE;IACnB,MAAM;MAAE9E;IAAM,CAAC,GAAG,IAAI;IACtB,IAAI,CAACyD,SAAS,CAAChD,IAAI,CAAC;MAAET,KAAK;MAAE8E;IAAM,CAAC,CAAC;EACzC;EACAC,SAASA,CAAC5D,EAAE,EAAE;IACV,MAAM6D,eAAe,GAAG,CAAC,CAAC,IAAI,CAAC1I,EAAE,CAACsF,OAAO,CAAC,oBAAoB,CAAC;IAC/D,IAAIT,EAAE,CAACiD,MAAM,IAAI,CAAC,IAAI,CAAC9H,EAAE,CAAC2I,QAAQ,CAAC9D,EAAE,CAACiD,MAAM,CAAC,EAAE;MAC3C;IACJ;IACA;IACA;IACA,MAAMP,MAAM,GAAG,IAAI,CAACC,SAAS,CAAC,CAAC,CAACoB,MAAM,CAAEjB,KAAK,IAAK,CAACA,KAAK,CAAC7D,QAAQ,CAAC;IAClE;IACA,IAAIe,EAAE,CAACiD,MAAM,IAAIP,MAAM,CAACzG,QAAQ,CAAC+D,EAAE,CAACiD,MAAM,CAAC,EAAE;MACzC,MAAMe,KAAK,GAAGtB,MAAM,CAACuB,SAAS,CAAEnB,KAAK,IAAKA,KAAK,KAAK9C,EAAE,CAACiD,MAAM,CAAC;MAC9D,MAAMiB,OAAO,GAAGxB,MAAM,CAACsB,KAAK,CAAC;MAC7B,IAAIG,IAAI;MACR;MACA;MACA,IAAI,CAAC,WAAW,EAAE,YAAY,CAAC,CAAClI,QAAQ,CAAC+D,EAAE,CAACoE,GAAG,CAAC,EAAE;QAC9CD,IAAI,GAAGH,KAAK,KAAKtB,MAAM,CAAC2B,MAAM,GAAG,CAAC,GAAG3B,MAAM,CAAC,CAAC,CAAC,GAAGA,MAAM,CAACsB,KAAK,GAAG,CAAC,CAAC;MACtE;MACA;MACA;MACA,IAAI,CAAC,SAAS,EAAE,WAAW,CAAC,CAAC/H,QAAQ,CAAC+D,EAAE,CAACoE,GAAG,CAAC,EAAE;QAC3CD,IAAI,GAAGH,KAAK,KAAK,CAAC,GAAGtB,MAAM,CAACA,MAAM,CAAC2B,MAAM,GAAG,CAAC,CAAC,GAAG3B,MAAM,CAACsB,KAAK,GAAG,CAAC,CAAC;MACtE;MACA,IAAIG,IAAI,IAAIzB,MAAM,CAACzG,QAAQ,CAACkI,IAAI,CAAC,EAAE;QAC/BA,IAAI,CAACpE,QAAQ,CAACC,EAAE,CAAC;QACjB,IAAI,CAAC6D,eAAe,EAAE;UAClB,IAAI,CAAChF,KAAK,GAAGsF,IAAI,CAACtF,KAAK;UACvB,IAAI,CAACuE,eAAe,CAACpD,EAAE,CAAC;QAC5B;MACJ;MACA;MACA;MACA,IAAI,CAAC,GAAG,CAAC,CAAC/D,QAAQ,CAAC+D,EAAE,CAACoE,GAAG,CAAC,EAAE;QACxB,MAAME,aAAa,GAAG,IAAI,CAACzF,KAAK;QAChC,IAAI,CAACA,KAAK,GAAG,IAAI,CAACO,mBAAmB,IAAI,IAAI,CAACP,KAAK,KAAKtD,SAAS,GAAGA,SAAS,GAAG2I,OAAO,CAACrF,KAAK;QAC7F,IAAIyF,aAAa,KAAK,IAAI,CAACzF,KAAK,IAAI,IAAI,CAACO,mBAAmB,EAAE;UAC1D;AACpB;AACA;AACA;AACA;AACA;UACoB,IAAI,CAACgE,eAAe,CAACpD,EAAE,CAAC;QAC5B;QACA;QACA;QACAA,EAAE,CAACI,cAAc,CAAC,CAAC;MACvB;IACJ;EACJ;EACAe,MAAMA,CAAA,EAAG;IACL,MAAM;MAAE1F,KAAK;MAAEiG,OAAO;MAAEvG,EAAE;MAAEuE,IAAI;MAAEb;IAAM,CAAC,GAAG,IAAI;IAChD,MAAMyC,IAAI,GAAG1D,4DAAU,CAAC,IAAI,CAAC;IAC7BP,uDAAiB,CAAC,IAAI,EAAElC,EAAE,EAAEuE,IAAI,EAAEb,KAAK,EAAE,KAAK,CAAC;IAC/C,OAAO7D,qDAAC,CAAC4B,iDAAI,EAAE;MAAEwH,GAAG,EAAE,0CAA0C;MAAE5C,IAAI,EAAE,YAAY;MAAE,iBAAiB,EAAE/F,KAAK,GAAGiG,OAAO,GAAG,IAAI;MAAE1C,OAAO,EAAE,IAAI,CAACA,OAAO;MAAEiC,KAAK,EAAEK;IAAK,CAAC,CAAC;EAC1K;EACA,IAAInG,EAAEA,CAAA,EAAG;IAAE,OAAO2B,qDAAU,CAAC,IAAI,CAAC;EAAE;EACpC,WAAWoF,QAAQA,CAAA,EAAG;IAAE,OAAO;MAC3B,OAAO,EAAE,CAAC,cAAc;IAC5B,CAAC;EAAE;AACP,CAAC;AACD,IAAIM,aAAa,GAAG,CAAC", "sources": ["./node_modules/@ionic/core/dist/esm/form-controller-21dd62b1.js", "./node_modules/@ionic/core/dist/esm/ion-radio_2.entry.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { h as findItemLabel } from './helpers-be245865.js';\n\n/**\n * Creates a controller that tracks whether a form control is using the legacy or modern syntax. This should be removed when the legacy form control syntax is removed.\n *\n * @internal\n * @prop el: The Ionic form component to reference\n */\nconst createLegacyFormController = (el) => {\n    const controlEl = el;\n    let legacyControl;\n    const hasLegacyControl = () => {\n        if (legacyControl === undefined) {\n            /**\n             * Detect if developers are using the legacy form control syntax\n             * so a deprecation warning is logged. This warning can be disabled\n             * by either using the new `label` property or setting `aria-label`\n             * on the control.\n             * Alternatively, components that use a slot for the label\n             * can check to see if the component has slotted text\n             * in the light DOM.\n             */\n            const hasLabelProp = controlEl.label !== undefined || hasLabelSlot(controlEl);\n            const hasAriaLabelAttribute = controlEl.hasAttribute('aria-label') ||\n                // Shadow DOM form controls cannot use aria-labelledby\n                (controlEl.hasAttribute('aria-labelledby') && controlEl.shadowRoot === null);\n            const legacyItemLabel = findItemLabel(controlEl);\n            /**\n             * Developers can manually opt-out of the modern form markup\n             * by setting `legacy=\"true\"` on components.\n             */\n            legacyControl =\n                controlEl.legacy === true || (!hasLabelProp && !hasAriaLabelAttribute && legacyItemLabel !== null);\n        }\n        return legacyControl;\n    };\n    return { hasLegacyControl };\n};\nconst hasLabelSlot = (controlEl) => {\n    /**\n     * Components that have a named label slot\n     * also have other slots, so we need to query for\n     * anything that is explicitly passed to slot=\"label\"\n     */\n    if (NAMED_LABEL_SLOT_COMPONENTS.includes(controlEl.tagName) && controlEl.querySelector('[slot=\"label\"]') !== null) {\n        return true;\n    }\n    /**\n     * Components that have an unnamed slot for the label\n     * have no other slots, so we can check the textContent\n     * of the element.\n     */\n    if (UNNAMED_LABEL_SLOT_COMPONENTS.includes(controlEl.tagName) && controlEl.textContent !== '') {\n        return true;\n    }\n    return false;\n};\nconst NAMED_LABEL_SLOT_COMPONENTS = ['ION-INPUT', 'ION-TEXTAREA', 'ION-SELECT', 'ION-RANGE'];\nconst UNNAMED_LABEL_SLOT_COMPONENTS = ['ION-TOGGLE', 'ION-CHECKBOX', 'ION-RADIO'];\n\nexport { createLegacyFormController as c };\n", "/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, d as createEvent, h, H as Host, f as getElement } from './index-a1a47f01.js';\nimport { c as createLegacyFormController } from './form-controller-21dd62b1.js';\nimport { a as addEventListener, b as removeEventListener, e as getAriaLabel, d as renderHiddenInput } from './helpers-be245865.js';\nimport { i as isOptionSelected } from './compare-with-utils-a96ff2ea.js';\nimport { p as printIonWarning } from './index-9b0d46f4.js';\nimport { h as hostContext, c as createColorClasses } from './theme-01f3f29c.js';\nimport { b as getIonMode } from './ionic-global-94f25d1b.js';\n\nconst radioIosCss = \":host{--inner-border-radius:50%;display:inline-block;position:relative;-webkit-box-sizing:border-box;box-sizing:border-box;max-width:100%;min-height:inherit;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;z-index:2}:host(:not(.legacy-radio)){cursor:pointer}:host(.radio-disabled){pointer-events:none}.radio-icon{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;width:100%;height:100%;contain:layout size style}.radio-icon,.radio-inner{-webkit-box-sizing:border-box;box-sizing:border-box}:host(.legacy-radio) label{top:0;margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;position:absolute;width:100%;height:100%;border:0;background:transparent;cursor:pointer;-webkit-appearance:none;-moz-appearance:none;appearance:none;outline:none;display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;opacity:0}@supports (inset-inline-start: 0){:host(.legacy-radio) label{inset-inline-start:0}}@supports not (inset-inline-start: 0){:host(.legacy-radio) label{left:0}:host-context([dir=rtl]):host(.legacy-radio) label,:host-context([dir=rtl]).legacy-radio label{left:unset;right:unset;right:0}@supports selector(:dir(rtl)){:host(.legacy-radio:dir(rtl)) label{left:unset;right:unset;right:0}}}:host(.legacy-radio) label::-moz-focus-inner{border:0}input{position:absolute;top:0;left:0;right:0;bottom:0;width:100%;height:100%;margin:0;padding:0;border:0;outline:0;clip:rect(0 0 0 0);opacity:0;overflow:hidden;-webkit-appearance:none;-moz-appearance:none}:host(:focus){outline:none}:host(.in-item:not(.legacy-radio)){width:100%;height:100%}:host([slot=start]:not(.legacy-radio)),:host([slot=end]:not(.legacy-radio)){width:auto}.radio-wrapper{display:-ms-flexbox;display:flex;position:relative;-ms-flex-positive:1;flex-grow:1;-ms-flex-align:center;align-items:center;height:inherit;min-height:inherit;cursor:inherit}.label-text-wrapper{text-overflow:ellipsis;white-space:nowrap;overflow:hidden}:host(.in-item:not(.legacy-radio)) .label-text-wrapper{margin-top:10px;margin-bottom:10px}:host(.in-item.radio-label-placement-stacked) .label-text-wrapper{margin-top:10px;margin-bottom:16px}:host(.in-item.radio-label-placement-stacked) .native-wrapper{margin-bottom:10px}.label-text-wrapper-hidden{display:none}.native-wrapper{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center}:host(.radio-justify-space-between) .radio-wrapper{-ms-flex-pack:justify;justify-content:space-between}:host(.radio-justify-start) .radio-wrapper{-ms-flex-pack:start;justify-content:start}:host(.radio-justify-end) .radio-wrapper{-ms-flex-pack:end;justify-content:end}:host(.radio-alignment-start) .radio-wrapper{-ms-flex-align:start;align-items:start}:host(.radio-alignment-center) .radio-wrapper{-ms-flex-align:center;align-items:center}:host(.radio-label-placement-start) .radio-wrapper{-ms-flex-direction:row;flex-direction:row}:host(.radio-label-placement-start) .label-text-wrapper{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px}:host(.radio-label-placement-end) .radio-wrapper{-ms-flex-direction:row-reverse;flex-direction:row-reverse}:host(.radio-label-placement-end) .label-text-wrapper{-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:0;margin-inline-end:0}:host(.radio-label-placement-fixed) .label-text-wrapper{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px}:host(.radio-label-placement-fixed) .label-text-wrapper{-ms-flex:0 0 100px;flex:0 0 100px;width:100px;min-width:100px}:host(.radio-label-placement-stacked) .radio-wrapper{-ms-flex-direction:column;flex-direction:column}:host(.radio-label-placement-stacked) .label-text-wrapper{-webkit-transform:scale(0.75);transform:scale(0.75);margin-left:0;margin-right:0;margin-bottom:16px;max-width:calc(100% / 0.75)}:host(.radio-label-placement-stacked.radio-alignment-start) .label-text-wrapper{-webkit-transform-origin:left top;transform-origin:left top}:host-context([dir=rtl]):host(.radio-label-placement-stacked.radio-alignment-start) .label-text-wrapper,:host-context([dir=rtl]).radio-label-placement-stacked.radio-alignment-start .label-text-wrapper{-webkit-transform-origin:right top;transform-origin:right top}@supports selector(:dir(rtl)){:host(.radio-label-placement-stacked.radio-alignment-start:dir(rtl)) .label-text-wrapper{-webkit-transform-origin:right top;transform-origin:right top}}:host(.radio-label-placement-stacked.radio-alignment-center) .label-text-wrapper{-webkit-transform-origin:center top;transform-origin:center top}:host-context([dir=rtl]):host(.radio-label-placement-stacked.radio-alignment-center) .label-text-wrapper,:host-context([dir=rtl]).radio-label-placement-stacked.radio-alignment-center .label-text-wrapper{-webkit-transform-origin:calc(100% - center) top;transform-origin:calc(100% - center) top}@supports selector(:dir(rtl)){:host(.radio-label-placement-stacked.radio-alignment-center:dir(rtl)) .label-text-wrapper{-webkit-transform-origin:calc(100% - center) top;transform-origin:calc(100% - center) top}}:host{--color-checked:var(--ion-color-primary, #3880ff)}:host(.legacy-radio){width:0.9375rem;height:1.5rem}:host(.ion-color.radio-checked) .radio-inner{border-color:var(--ion-color-base)}.item-radio.item-ios ion-label{-webkit-margin-start:0;margin-inline-start:0}.radio-inner{width:33%;height:50%}:host(.radio-checked) .radio-inner{-webkit-transform:rotate(45deg);transform:rotate(45deg);border-width:0.125rem;border-top-width:0;border-left-width:0;border-style:solid;border-color:var(--color-checked)}:host(.radio-disabled){opacity:0.3}:host(.ion-focused) .radio-icon::after{border-radius:var(--inner-border-radius);top:-8px;display:block;position:absolute;width:36px;height:36px;background:var(--ion-color-primary-tint, #4c8dff);content:\\\"\\\";opacity:0.2}@supports (inset-inline-start: 0){:host(.ion-focused) .radio-icon::after{inset-inline-start:-9px}}@supports not (inset-inline-start: 0){:host(.ion-focused) .radio-icon::after{left:-9px}:host-context([dir=rtl]):host(.ion-focused) .radio-icon::after,:host-context([dir=rtl]).ion-focused .radio-icon::after{left:unset;right:unset;right:-9px}@supports selector(:dir(rtl)){:host(.ion-focused:dir(rtl)) .radio-icon::after{left:unset;right:unset;right:-9px}}}:host(.in-item.legacy-radio){-webkit-margin-start:8px;margin-inline-start:8px;-webkit-margin-end:11px;margin-inline-end:11px;margin-top:8px;margin-bottom:8px;display:block;position:static}:host(.in-item.legacy-radio[slot=start]){-webkit-margin-start:3px;margin-inline-start:3px;-webkit-margin-end:21px;margin-inline-end:21px;margin-top:8px;margin-bottom:8px}.native-wrapper .radio-icon{width:0.9375rem;height:1.5rem}\";\nconst IonRadioIosStyle0 = radioIosCss;\n\nconst radioMdCss = \":host{--inner-border-radius:50%;display:inline-block;position:relative;-webkit-box-sizing:border-box;box-sizing:border-box;max-width:100%;min-height:inherit;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;z-index:2}:host(:not(.legacy-radio)){cursor:pointer}:host(.radio-disabled){pointer-events:none}.radio-icon{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;width:100%;height:100%;contain:layout size style}.radio-icon,.radio-inner{-webkit-box-sizing:border-box;box-sizing:border-box}:host(.legacy-radio) label{top:0;margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;position:absolute;width:100%;height:100%;border:0;background:transparent;cursor:pointer;-webkit-appearance:none;-moz-appearance:none;appearance:none;outline:none;display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;opacity:0}@supports (inset-inline-start: 0){:host(.legacy-radio) label{inset-inline-start:0}}@supports not (inset-inline-start: 0){:host(.legacy-radio) label{left:0}:host-context([dir=rtl]):host(.legacy-radio) label,:host-context([dir=rtl]).legacy-radio label{left:unset;right:unset;right:0}@supports selector(:dir(rtl)){:host(.legacy-radio:dir(rtl)) label{left:unset;right:unset;right:0}}}:host(.legacy-radio) label::-moz-focus-inner{border:0}input{position:absolute;top:0;left:0;right:0;bottom:0;width:100%;height:100%;margin:0;padding:0;border:0;outline:0;clip:rect(0 0 0 0);opacity:0;overflow:hidden;-webkit-appearance:none;-moz-appearance:none}:host(:focus){outline:none}:host(.in-item:not(.legacy-radio)){width:100%;height:100%}:host([slot=start]:not(.legacy-radio)),:host([slot=end]:not(.legacy-radio)){width:auto}.radio-wrapper{display:-ms-flexbox;display:flex;position:relative;-ms-flex-positive:1;flex-grow:1;-ms-flex-align:center;align-items:center;height:inherit;min-height:inherit;cursor:inherit}.label-text-wrapper{text-overflow:ellipsis;white-space:nowrap;overflow:hidden}:host(.in-item:not(.legacy-radio)) .label-text-wrapper{margin-top:10px;margin-bottom:10px}:host(.in-item.radio-label-placement-stacked) .label-text-wrapper{margin-top:10px;margin-bottom:16px}:host(.in-item.radio-label-placement-stacked) .native-wrapper{margin-bottom:10px}.label-text-wrapper-hidden{display:none}.native-wrapper{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center}:host(.radio-justify-space-between) .radio-wrapper{-ms-flex-pack:justify;justify-content:space-between}:host(.radio-justify-start) .radio-wrapper{-ms-flex-pack:start;justify-content:start}:host(.radio-justify-end) .radio-wrapper{-ms-flex-pack:end;justify-content:end}:host(.radio-alignment-start) .radio-wrapper{-ms-flex-align:start;align-items:start}:host(.radio-alignment-center) .radio-wrapper{-ms-flex-align:center;align-items:center}:host(.radio-label-placement-start) .radio-wrapper{-ms-flex-direction:row;flex-direction:row}:host(.radio-label-placement-start) .label-text-wrapper{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px}:host(.radio-label-placement-end) .radio-wrapper{-ms-flex-direction:row-reverse;flex-direction:row-reverse}:host(.radio-label-placement-end) .label-text-wrapper{-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:0;margin-inline-end:0}:host(.radio-label-placement-fixed) .label-text-wrapper{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px}:host(.radio-label-placement-fixed) .label-text-wrapper{-ms-flex:0 0 100px;flex:0 0 100px;width:100px;min-width:100px}:host(.radio-label-placement-stacked) .radio-wrapper{-ms-flex-direction:column;flex-direction:column}:host(.radio-label-placement-stacked) .label-text-wrapper{-webkit-transform:scale(0.75);transform:scale(0.75);margin-left:0;margin-right:0;margin-bottom:16px;max-width:calc(100% / 0.75)}:host(.radio-label-placement-stacked.radio-alignment-start) .label-text-wrapper{-webkit-transform-origin:left top;transform-origin:left top}:host-context([dir=rtl]):host(.radio-label-placement-stacked.radio-alignment-start) .label-text-wrapper,:host-context([dir=rtl]).radio-label-placement-stacked.radio-alignment-start .label-text-wrapper{-webkit-transform-origin:right top;transform-origin:right top}@supports selector(:dir(rtl)){:host(.radio-label-placement-stacked.radio-alignment-start:dir(rtl)) .label-text-wrapper{-webkit-transform-origin:right top;transform-origin:right top}}:host(.radio-label-placement-stacked.radio-alignment-center) .label-text-wrapper{-webkit-transform-origin:center top;transform-origin:center top}:host-context([dir=rtl]):host(.radio-label-placement-stacked.radio-alignment-center) .label-text-wrapper,:host-context([dir=rtl]).radio-label-placement-stacked.radio-alignment-center .label-text-wrapper{-webkit-transform-origin:calc(100% - center) top;transform-origin:calc(100% - center) top}@supports selector(:dir(rtl)){:host(.radio-label-placement-stacked.radio-alignment-center:dir(rtl)) .label-text-wrapper{-webkit-transform-origin:calc(100% - center) top;transform-origin:calc(100% - center) top}}:host{--color:rgb(var(--ion-text-color-rgb, 0, 0, 0), 0.6);--color-checked:var(--ion-color-primary, #3880ff);--border-width:0.125rem;--border-style:solid;--border-radius:50%}:host(.legacy-radio){width:1.25rem;height:1.25rem}:host(.ion-color) .radio-inner{background:var(--ion-color-base)}:host(.ion-color.radio-checked) .radio-icon{border-color:var(--ion-color-base)}.radio-icon{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;border-radius:var(--border-radius);border-width:var(--border-width);border-style:var(--border-style);border-color:var(--color)}.radio-inner{border-radius:var(--inner-border-radius);width:calc(50% + var(--border-width));height:calc(50% + var(--border-width));-webkit-transform:scale3d(0, 0, 0);transform:scale3d(0, 0, 0);-webkit-transition:-webkit-transform 280ms cubic-bezier(0.4, 0, 0.2, 1);transition:-webkit-transform 280ms cubic-bezier(0.4, 0, 0.2, 1);transition:transform 280ms cubic-bezier(0.4, 0, 0.2, 1);transition:transform 280ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 280ms cubic-bezier(0.4, 0, 0.2, 1);background:var(--color-checked)}:host(.radio-checked) .radio-icon{border-color:var(--color-checked)}:host(.radio-checked) .radio-inner{-webkit-transform:scale3d(1, 1, 1);transform:scale3d(1, 1, 1)}:host(.legacy-radio.radio-disabled),:host(.radio-disabled) .label-text-wrapper{opacity:0.38}:host(.radio-disabled) .native-wrapper{opacity:0.63}:host(.ion-focused.legacy-radio) .radio-icon::after{top:-12px}@supports (inset-inline-start: 0){:host(.ion-focused.legacy-radio) .radio-icon::after{inset-inline-start:-12px}}@supports not (inset-inline-start: 0){:host(.ion-focused.legacy-radio) .radio-icon::after{left:-12px}:host-context([dir=rtl]):host(.ion-focused.legacy-radio) .radio-icon::after,:host-context([dir=rtl]).ion-focused.legacy-radio .radio-icon::after{left:unset;right:unset;right:-12px}@supports selector(:dir(rtl)){:host(.ion-focused.legacy-radio:dir(rtl)) .radio-icon::after{left:unset;right:unset;right:-12px}}}:host(.ion-focused) .radio-icon::after{border-radius:var(--inner-border-radius);display:block;position:absolute;width:36px;height:36px;background:var(--ion-color-primary-tint, #4c8dff);content:\\\"\\\";opacity:0.2}:host(.in-item.legacy-radio){margin-left:0;margin-right:0;margin-top:9px;margin-bottom:9px;display:block;position:static}:host(.in-item.legacy-radio[slot=start]){-webkit-margin-start:4px;margin-inline-start:4px;-webkit-margin-end:36px;margin-inline-end:36px;margin-top:11px;margin-bottom:10px}.native-wrapper .radio-icon{width:1.25rem;height:1.25rem}\";\nconst IonRadioMdStyle0 = radioMdCss;\n\nconst Radio = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.ionStyle = createEvent(this, \"ionStyle\", 7);\n        this.ionFocus = createEvent(this, \"ionFocus\", 7);\n        this.ionBlur = createEvent(this, \"ionBlur\", 7);\n        this.inputId = `ion-rb-${radioButtonIds++}`;\n        this.radioGroup = null;\n        // This flag ensures we log the deprecation warning at most once.\n        this.hasLoggedDeprecationWarning = false;\n        this.updateState = () => {\n            if (this.radioGroup) {\n                const { compareWith, value: radioGroupValue } = this.radioGroup;\n                this.checked = isOptionSelected(radioGroupValue, this.value, compareWith);\n            }\n        };\n        this.onClick = () => {\n            const { radioGroup, checked, disabled } = this;\n            if (disabled) {\n                return;\n            }\n            /**\n             * The legacy control uses a native input inside\n             * of the radio host, so we can set this.checked\n             * to the state of the nativeInput. RadioGroup\n             * will prevent the native input from checking if\n             * allowEmptySelection=\"false\" by calling ev.preventDefault().\n             */\n            if (this.legacyFormController.hasLegacyControl()) {\n                this.checked = this.nativeInput.checked;\n                return;\n            }\n            /**\n             * The modern control does not use a native input\n             * inside of the radio host, so we cannot rely on the\n             * ev.preventDefault() behavior above. If the radio\n             * is checked and the parent radio group allows for empty\n             * selection, then we can set the checked state to false.\n             * Otherwise, the checked state should always be set\n             * to true because the checked state cannot be toggled.\n             */\n            if (checked && (radioGroup === null || radioGroup === void 0 ? void 0 : radioGroup.allowEmptySelection)) {\n                this.checked = false;\n            }\n            else {\n                this.checked = true;\n            }\n        };\n        this.onFocus = () => {\n            this.ionFocus.emit();\n        };\n        this.onBlur = () => {\n            this.ionBlur.emit();\n        };\n        this.checked = false;\n        this.buttonTabindex = -1;\n        this.color = undefined;\n        this.name = this.inputId;\n        this.disabled = false;\n        this.value = undefined;\n        this.labelPlacement = 'start';\n        this.legacy = undefined;\n        this.justify = 'space-between';\n        this.alignment = 'center';\n    }\n    valueChanged() {\n        /**\n         * The new value of the radio may\n         * match the radio group's value,\n         * so we see if it should be checked.\n         */\n        this.updateState();\n    }\n    /** @internal */\n    async setFocus(ev) {\n        ev.stopPropagation();\n        ev.preventDefault();\n        this.el.focus();\n    }\n    /** @internal */\n    async setButtonTabindex(value) {\n        this.buttonTabindex = value;\n    }\n    connectedCallback() {\n        this.legacyFormController = createLegacyFormController(this.el);\n        if (this.value === undefined) {\n            this.value = this.inputId;\n        }\n        const radioGroup = (this.radioGroup = this.el.closest('ion-radio-group'));\n        if (radioGroup) {\n            this.updateState();\n            addEventListener(radioGroup, 'ionValueChange', this.updateState);\n        }\n    }\n    disconnectedCallback() {\n        const radioGroup = this.radioGroup;\n        if (radioGroup) {\n            removeEventListener(radioGroup, 'ionValueChange', this.updateState);\n            this.radioGroup = null;\n        }\n    }\n    componentWillLoad() {\n        this.emitStyle();\n    }\n    styleChanged() {\n        this.emitStyle();\n    }\n    emitStyle() {\n        const style = {\n            'interactive-disabled': this.disabled,\n            // TODO(FW-3125): remove this\n            legacy: !!this.legacy,\n        };\n        if (this.legacyFormController.hasLegacyControl()) {\n            style['radio-checked'] = this.checked;\n        }\n        this.ionStyle.emit(style);\n    }\n    get hasLabel() {\n        return this.el.textContent !== '';\n    }\n    renderRadioControl() {\n        return (h(\"div\", { class: \"radio-icon\", part: \"container\" }, h(\"div\", { class: \"radio-inner\", part: \"mark\" }), h(\"div\", { class: \"radio-ripple\" })));\n    }\n    render() {\n        const { legacyFormController } = this;\n        return legacyFormController.hasLegacyControl() ? this.renderLegacyRadio() : this.renderRadio();\n    }\n    renderRadio() {\n        const { checked, disabled, color, el, justify, labelPlacement, hasLabel, buttonTabindex, alignment } = this;\n        const mode = getIonMode(this);\n        const inItem = hostContext('ion-item', el);\n        return (h(Host, { onFocus: this.onFocus, onBlur: this.onBlur, onClick: this.onClick, class: createColorClasses(color, {\n                [mode]: true,\n                'in-item': inItem,\n                'radio-checked': checked,\n                'radio-disabled': disabled,\n                [`radio-justify-${justify}`]: true,\n                [`radio-alignment-${alignment}`]: true,\n                [`radio-label-placement-${labelPlacement}`]: true,\n                // Focus and active styling should not apply when the radio is in an item\n                'ion-activatable': !inItem,\n                'ion-focusable': !inItem,\n            }), role: \"radio\", \"aria-checked\": checked ? 'true' : 'false', \"aria-disabled\": disabled ? 'true' : null, tabindex: buttonTabindex }, h(\"label\", { class: \"radio-wrapper\" }, h(\"div\", { class: {\n                'label-text-wrapper': true,\n                'label-text-wrapper-hidden': !hasLabel,\n            }, part: \"label\" }, h(\"slot\", null)), h(\"div\", { class: \"native-wrapper\" }, this.renderRadioControl()))));\n    }\n    renderLegacyRadio() {\n        if (!this.hasLoggedDeprecationWarning) {\n            printIonWarning(`ion-radio now requires providing a label with either the default slot or the \"aria-label\" attribute. To migrate, remove any usage of \"ion-label\" and pass the label text to either the component or the \"aria-label\" attribute.\n\nExample: <ion-radio>Option Label</ion-radio>\nExample with aria-label: <ion-radio aria-label=\"Option Label\"></ion-radio>\n\nDevelopers can use the \"legacy\" property to continue using the legacy form markup. This property will be removed in an upcoming major release of Ionic where this form control will use the modern form markup.`, this.el);\n            if (this.legacy) {\n                printIonWarning(`ion-radio is being used with the \"legacy\" property enabled which will forcibly enable the legacy form markup. This property will be removed in an upcoming major release of Ionic where this form control will use the modern form markup.\n\nDevelopers can dismiss this warning by removing their usage of the \"legacy\" property and using the new radio syntax.`, this.el);\n            }\n            this.hasLoggedDeprecationWarning = true;\n        }\n        const { inputId, disabled, checked, color, el, buttonTabindex } = this;\n        const mode = getIonMode(this);\n        const { label, labelId, labelText } = getAriaLabel(el, inputId);\n        return (h(Host, { \"aria-checked\": `${checked}`, \"aria-hidden\": disabled ? 'true' : null, \"aria-labelledby\": label ? labelId : null, role: \"radio\", tabindex: buttonTabindex, onFocus: this.onFocus, onBlur: this.onBlur, onClick: this.onClick, class: createColorClasses(color, {\n                [mode]: true,\n                'in-item': hostContext('ion-item', el),\n                interactive: true,\n                'radio-checked': checked,\n                'radio-disabled': disabled,\n                'legacy-radio': true,\n            }) }, this.renderRadioControl(), h(\"label\", { htmlFor: inputId }, labelText), h(\"input\", { type: \"radio\", checked: checked, disabled: disabled, tabindex: \"-1\", id: inputId, ref: (nativeEl) => (this.nativeInput = nativeEl) })));\n    }\n    get el() { return getElement(this); }\n    static get watchers() { return {\n        \"value\": [\"valueChanged\"],\n        \"checked\": [\"styleChanged\"],\n        \"color\": [\"styleChanged\"],\n        \"disabled\": [\"styleChanged\"]\n    }; }\n};\nlet radioButtonIds = 0;\nRadio.style = {\n    ios: IonRadioIosStyle0,\n    md: IonRadioMdStyle0\n};\n\nconst RadioGroup = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.ionChange = createEvent(this, \"ionChange\", 7);\n        this.ionValueChange = createEvent(this, \"ionValueChange\", 7);\n        this.inputId = `ion-rg-${radioGroupIds++}`;\n        this.labelId = `${this.inputId}-lbl`;\n        this.setRadioTabindex = (value) => {\n            const radios = this.getRadios();\n            // Get the first radio that is not disabled and the checked one\n            const first = radios.find((radio) => !radio.disabled);\n            const checked = radios.find((radio) => radio.value === value && !radio.disabled);\n            if (!first && !checked) {\n                return;\n            }\n            // If an enabled checked radio exists, set it to be the focusable radio\n            // otherwise we default to focus the first radio\n            const focusable = checked || first;\n            for (const radio of radios) {\n                const tabindex = radio === focusable ? 0 : -1;\n                radio.setButtonTabindex(tabindex);\n            }\n        };\n        this.onClick = (ev) => {\n            ev.preventDefault();\n            /**\n             * The Radio Group component mandates that only one radio button\n             * within the group can be selected at any given time. Since `ion-radio`\n             * is a shadow DOM component, it cannot natively perform this behavior\n             * using the `name` attribute.\n             */\n            const selectedRadio = ev.target && ev.target.closest('ion-radio');\n            /**\n             * Our current disabled prop definition causes Stencil to mark it\n             * as optional. While this is not desired, fixing this behavior\n             * in Stencil is a significant breaking change, so this effort is\n             * being de-risked in STENCIL-917. Until then, we compromise\n             * here by checking for falsy `disabled` values instead of strictly\n             * checking `disabled === false`.\n             */\n            if (selectedRadio && !selectedRadio.disabled) {\n                const currentValue = this.value;\n                const newValue = selectedRadio.value;\n                if (newValue !== currentValue) {\n                    this.value = newValue;\n                    this.emitValueChange(ev);\n                }\n                else if (this.allowEmptySelection) {\n                    this.value = undefined;\n                    this.emitValueChange(ev);\n                }\n            }\n        };\n        this.allowEmptySelection = false;\n        this.compareWith = undefined;\n        this.name = this.inputId;\n        this.value = undefined;\n    }\n    valueChanged(value) {\n        this.setRadioTabindex(value);\n        this.ionValueChange.emit({ value });\n    }\n    componentDidLoad() {\n        /**\n         * There's an issue when assigning a value to the radio group\n         * within the Angular primary content (rendering within the\n         * app component template). When the template is isolated to a route,\n         * the value is assigned correctly.\n         * To address this issue, we need to ensure that the watcher is\n         * called after the component has finished loading,\n         * allowing the emit to be dispatched correctly.\n         */\n        this.valueChanged(this.value);\n    }\n    async connectedCallback() {\n        // Get the list header if it exists and set the id\n        // this is used to set aria-labelledby\n        const header = this.el.querySelector('ion-list-header') || this.el.querySelector('ion-item-divider');\n        if (header) {\n            const label = (this.label = header.querySelector('ion-label'));\n            if (label) {\n                this.labelId = label.id = this.name + '-lbl';\n            }\n        }\n    }\n    getRadios() {\n        return Array.from(this.el.querySelectorAll('ion-radio'));\n    }\n    /**\n     * Emits an `ionChange` event.\n     *\n     * This API should be called for user committed changes.\n     * This API should not be used for external value changes.\n     */\n    emitValueChange(event) {\n        const { value } = this;\n        this.ionChange.emit({ value, event });\n    }\n    onKeydown(ev) {\n        const inSelectPopover = !!this.el.closest('ion-select-popover');\n        if (ev.target && !this.el.contains(ev.target)) {\n            return;\n        }\n        // Get all radios inside of the radio group and then\n        // filter out disabled radios since we need to skip those\n        const radios = this.getRadios().filter((radio) => !radio.disabled);\n        // Only move the radio if the current focus is in the radio group\n        if (ev.target && radios.includes(ev.target)) {\n            const index = radios.findIndex((radio) => radio === ev.target);\n            const current = radios[index];\n            let next;\n            // If hitting arrow down or arrow right, move to the next radio\n            // If we're on the last radio, move to the first radio\n            if (['ArrowDown', 'ArrowRight'].includes(ev.key)) {\n                next = index === radios.length - 1 ? radios[0] : radios[index + 1];\n            }\n            // If hitting arrow up or arrow left, move to the previous radio\n            // If we're on the first radio, move to the last radio\n            if (['ArrowUp', 'ArrowLeft'].includes(ev.key)) {\n                next = index === 0 ? radios[radios.length - 1] : radios[index - 1];\n            }\n            if (next && radios.includes(next)) {\n                next.setFocus(ev);\n                if (!inSelectPopover) {\n                    this.value = next.value;\n                    this.emitValueChange(ev);\n                }\n            }\n            // Update the radio group value when a user presses the\n            // space bar on top of a selected radio\n            if ([' '].includes(ev.key)) {\n                const previousValue = this.value;\n                this.value = this.allowEmptySelection && this.value !== undefined ? undefined : current.value;\n                if (previousValue !== this.value || this.allowEmptySelection) {\n                    /**\n                     * Value change should only be emitted if the value is different,\n                     * such as selecting a new radio with the space bar or if\n                     * the radio group allows for empty selection and the user\n                     * is deselecting a checked radio.\n                     */\n                    this.emitValueChange(ev);\n                }\n                // Prevent browsers from jumping\n                // to the bottom of the screen\n                ev.preventDefault();\n            }\n        }\n    }\n    render() {\n        const { label, labelId, el, name, value } = this;\n        const mode = getIonMode(this);\n        renderHiddenInput(true, el, name, value, false);\n        return h(Host, { key: '6065674a08ac2ead25e87219b5628879a759b75a', role: \"radiogroup\", \"aria-labelledby\": label ? labelId : null, onClick: this.onClick, class: mode });\n    }\n    get el() { return getElement(this); }\n    static get watchers() { return {\n        \"value\": [\"valueChanged\"]\n    }; }\n};\nlet radioGroupIds = 0;\n\nexport { Radio as ion_radio, RadioGroup as ion_radio_group };\n"], "names": ["h", "findItemLabel", "createLegacyFormController", "el", "controlEl", "legacyControl", "hasLegacyControl", "undefined", "hasLabelProp", "label", "hasLabelSlot", "hasAriaLabelAttribute", "hasAttribute", "shadowRoot", "legacyItemLabel", "legacy", "NAMED_LABEL_SLOT_COMPONENTS", "includes", "tagName", "querySelector", "UNNAMED_LABEL_SLOT_COMPONENTS", "textContent", "c", "r", "registerInstance", "d", "createEvent", "H", "Host", "f", "getElement", "a", "addEventListener", "b", "removeEventListener", "e", "getAriaLabel", "renderHiddenInput", "i", "isOptionSelected", "p", "printIonWarning", "hostContext", "createColorClasses", "getIonMode", "radioIosCss", "IonRadioIosStyle0", "radioMdCss", "IonRadioMdStyle0", "Radio", "constructor", "hostRef", "ionStyle", "ionFocus", "ionBlur", "inputId", "radioButtonIds", "radioGroup", "hasLoggedDeprecationWarning", "updateState", "compareWith", "value", "radioGroupValue", "checked", "onClick", "disabled", "legacyFormController", "nativeInput", "allowEmptySelection", "onFocus", "emit", "onBlur", "buttonTabindex", "color", "name", "labelPlacement", "justify", "alignment", "valueChanged", "setFocus", "ev", "_this", "_asyncToGenerator", "stopPropagation", "preventDefault", "focus", "setButtonTabindex", "_this2", "connectedCallback", "closest", "disconnectedCallback", "componentWillLoad", "emitStyle", "styleChanged", "style", "<PERSON><PERSON><PERSON><PERSON>", "renderRadioControl", "class", "part", "render", "renderLegacyRadio", "renderRadio", "mode", "inItem", "role", "tabindex", "labelId", "labelText", "interactive", "htmlFor", "type", "id", "ref", "nativeEl", "watchers", "ios", "md", "RadioGroup", "ionChange", "ionValueChange", "radioGroupIds", "setRadioTabindex", "radios", "getRadios", "first", "find", "radio", "focusable", "selectedRadio", "target", "currentValue", "newValue", "emitValueChange", "componentDidLoad", "_this3", "header", "Array", "from", "querySelectorAll", "event", "onKeydown", "inSelectPopover", "contains", "filter", "index", "findIndex", "current", "next", "key", "length", "previousValue", "ion_radio", "ion_radio_group"], "sourceRoot": "webpack:///", "x_google_ignoreList": [0, 1]}