import * as functions from 'firebase-functions';
import * as admin from 'firebase-admin';

// Initialize Firebase Admin
admin.initializeApp();

/**
 * Simple test function to verify Cloud Functions are working
 */
export const testFunction = functions.https.onCall(async (data, context) => {
  return {
    success: true,
    message: 'Cloud Functions are working!',
    timestamp: new Date().toISOString(),
    user: context.auth?.uid || 'anonymous'
  };
});

/**
 * Simple function to validate assistant codes
 */
export const validateCode = functions.https.onCall(async (data, context) => {
  if (!context.auth) {
    throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
  }

  const { code } = data;
  
  if (!code || typeof code !== 'string' || code.length !== 8) {
    throw new functions.https.HttpsError('invalid-argument', 'Valid 8-character code required');
  }

  // Simple validation - in production this would check Firestore
  return {
    success: true,
    valid: code.length === 8,
    message: 'Code format is valid'
  };
});
