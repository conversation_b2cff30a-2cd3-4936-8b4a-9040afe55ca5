import { Component, OnInit, ViewChild, ElementRef } from '@angular/core';
import { Router, ActivatedRoute } from '@angular/router';
import { Alert<PERSON>ontroller, LoadingController, ToastController } from '@ionic/angular';
import { FirebaseService, Case } from '../services/firebase.service';

export interface CaseFile {
  id: string;
  name: string;
  originalName: string;
  type: string;
  size: number;
  caseId: string;
  folderId?: string;
  uploadedBy: string;
  uploadedAt: Date;
  downloadUrl: string;
  storagePath: string;
  key?: JsonWebKey;
  iv?: number[];
}

export interface CaseFolder {
  id: string;
  name: string;
  caseId: string;
  parentFolderId?: string;
  createdBy: string;
  createdAt: Date;
  fileCount: number;
  subFolderCount: number;
}

export interface UploadProgress {
  fileName: string;
  progress: number;
  status: 'uploading' | 'completed' | 'error';
}

@Component({
  selector: 'app-case-files',
  templateUrl: './case-files.page.html',
  styleUrls: ['./case-files.page.scss'],
  standalone: false,
})
export class CaseFilesPage implements OnInit {
  @ViewChild('fileInput') fileInput!: ElementRef<HTMLInputElement>;

  cases: Case[] = [];
  selectedCaseId: string = '';
  selectedCase: Case | null = null;
  caseFiles: CaseFile[] = [];
  caseFolders: CaseFolder[] = [];
  currentFolderId: string | null = null;
  currentFolder: CaseFolder | null = null;
  folderPath: CaseFolder[] = [];
  uploadProgress: UploadProgress[] = [];
  isLoading = false;

  constructor(
    private router: Router,
    private route: ActivatedRoute,
    private firebaseService: FirebaseService,
    private alertController: AlertController,
    private loadingController: LoadingController,
    private toastController: ToastController
  ) {}

  ngOnInit() {
    this.loadCases();

    this.route.queryParams.subscribe(params => {
      if (params['caseId']) {
        this.selectedCaseId = params['caseId'];
        setTimeout(() => {
          this.onCaseChange();
        }, 1000);
      }
    });
  }

  async loadCases() {
    const currentUser = this.firebaseService.getCurrentUser();
    if (!currentUser) return;

    try {
      this.cases = await this.firebaseService.getCases(currentUser.uid);
    } catch (error) {
      console.error('Error loading cases:', error);
      this.showToast('Failed to load case list', 'danger');
    }
  }

  onCaseChange() {
    const selected = this.cases.find(c => c.id === this.selectedCaseId);
    this.selectedCase = selected || null;

    if (this.selectedCase) {
      this.loadCaseFiles();
    }
  }

  selectFirstCase() {
    if (this.cases.length > 0) {
      this.selectedCaseId = this.cases[0].id!;
      this.onCaseChange();
    }
  }

  async loadCaseFiles() {
    if (!this.selectedCaseId) return;
  
    this.isLoading = true;
    try {
      this.caseFiles = await this.firebaseService.getEncryptedFiles(this.selectedCaseId);
      this.updateFolderPath();
    } catch (error) {
      console.error('Error loading case files:', error);
      this.caseFiles = [];
      this.showToast('Failed to load files', 'danger');
    } finally {
      this.isLoading = false;
    }
  }

  triggerFileInput() {
    if (!this.selectedCaseId) {
      this.showToast('Please select a case first', 'warning');
      return;
    }

    try {
      this.fileInput.nativeElement.click();
    } catch (error) {
      console.error('Error triggering file input:', error);
      this.showToast('Error opening file selector', 'danger');
    }
  }

  async onFilesSelected(event: any) {
    const files: FileList = event.target.files;
    if (!files || files.length === 0 || !this.selectedCaseId) return;

    const currentUser = this.firebaseService.getCurrentUser();
    if (!currentUser) {
      this.showToast('Please sign in to upload files', 'warning');
      return;
    }

    const validFiles: File[] = [];
    for (let i = 0; i < files.length; i++) {
      const file = files[i];

      if (file.size > 10 * 1024 * 1024) {
        this.showToast(`File ${file.name} is too large (max 10MB)`, 'warning');
        continue;
      }

      const allowedTypes = [
        'application/pdf',
        'application/msword',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'image/jpeg',
        'image/jpg',
        'image/png',
        'text/plain'
      ];

      if (!allowedTypes.includes(file.type)) {
        this.showToast(`File type not supported: ${file.name}`, 'warning');
        continue;
      }

      validFiles.push(file);
    }

    if (validFiles.length === 0) {
      this.showToast('No valid files selected', 'warning');
      return;
    }

    for (const file of validFiles) {
      await this.uploadFile(file, currentUser.uid);
    }

    this.fileInput.nativeElement.value = '';
  }

  async uploadFile(file: File, userId: string) {
    const progressItem: UploadProgress = {
      fileName: file.name,
      progress: 0,
      status: 'uploading'
    };
    this.uploadProgress.push(progressItem);
  
    try {
      const key = await crypto.subtle.generateKey(
        { name: 'AES-GCM', length: 256 },
        true,
        ['encrypt', 'decrypt']
      );
      const iv = crypto.getRandomValues(new Uint8Array(12));
      const rawKey = await crypto.subtle.exportKey('jwk', key); 
  
      const fileBuffer = await file.arrayBuffer();
      const encryptedBuffer = await crypto.subtle.encrypt(
        { name: 'AES-GCM', iv },
        key,
        fileBuffer
      );
  
      const blob = new Blob([encryptedBuffer], { type: 'application/octet-stream' });
      const filePath = `encrypted_files/${this.selectedCaseId}/${Date.now()}-${file.name}`;
  
      await this.firebaseService.uploadFileToStorage(filePath, blob);
  
      const fileMetadata = {
        caseId: this.selectedCaseId,
        folderId: this.currentFolderId,
        name: file.name,
        type: file.type,
        size: file.size,
        uploadedBy: userId,
        uploadedAt: new Date(),
        storagePath: filePath,
        iv: Array.from(iv),
        key: rawKey,
      };
  
      await this.firebaseService.saveEncryptedFileMetadata(fileMetadata);
      await this.firebaseService.updateCaseFileCount(this.selectedCaseId);
      await this.loadCaseFiles();
      if (this.currentFolderId) {
        await this.firebaseService.incrementFolderFileCount(this.currentFolderId); 
      }
      progressItem.status = 'completed';
      progressItem.progress = 100;
      this.showToast(`${file.name} uploaded securely`, 'success');
    } catch (error) {
      console.error('Encryption/upload failed:', error);
      progressItem.status = 'error';
      this.showToast(`Failed to upload ${file.name}`, 'danger');
    }
  
    setTimeout(() => {
      const index = this.uploadProgress.indexOf(progressItem);
      if (index > -1) {
        this.uploadProgress.splice(index, 1);
      }
    }, 1000);
  }
  
  async downloadFile(file: CaseFile, event?: Event) {
    if (event) event.stopPropagation();
  
    const loading = await this.loadingController.create({
      message: 'Downloading file...',
      spinner: 'crescent'
    });
    await loading.present();
  
    try {
      if (!file.key || !file.iv) {
        this.showToast('Missing decryption metadata', 'danger');
        return;
      }
  
      const encryptedBlob = await this.firebaseService.downloadEncryptedBlob(file.storagePath);
      const encryptedBuffer = await encryptedBlob.arrayBuffer();
  
      const key = await crypto.subtle.importKey(
        'jwk',
        file.key as JsonWebKey,
        { name: 'AES-GCM' },
        true,
        ['decrypt']
      );
  
      const decryptedBuffer = await crypto.subtle.decrypt(
        {
          name: 'AES-GCM',
          iv: new Uint8Array(file.iv)
        },
        key,
        encryptedBuffer
      );
  
      const decryptedBlob = new Blob([decryptedBuffer], { type: file.type });
      const url = URL.createObjectURL(decryptedBlob);
  
      const link = document.createElement('a');
      link.href = url;
      link.download = file.originalName;
      link.target = '_blank';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
  
      this.showToast('File decrypted and downloaded!', 'success');
  
    } catch (error) {
      console.error('Decryption failed:', error);
      this.showToast('Failed to decrypt or download file', 'danger');
    } finally {
      await loading.dismiss();
    }
  }

  updateFolderPath() {
    this.folderPath = [];
    if (this.currentFolderId) {
      const allFolders = this.caseFolders;
      let currentFolder = allFolders.find(f => f.id === this.currentFolderId);

      while (currentFolder) {
        this.folderPath.unshift(currentFolder);
        currentFolder = allFolders.find(f => f.id === currentFolder?.parentFolderId);
      }

      this.currentFolder = this.folderPath[this.folderPath.length - 1] || null;
    } else {
      this.currentFolder = null;
    }
  }

  navigateToFolder(folder: CaseFolder) {
    this.currentFolderId = folder.id;
    this.loadCaseFiles();
  }

  navigateToParentFolder() {
    this.currentFolderId = this.currentFolder?.parentFolderId || null;
    this.loadCaseFiles();
  }

  navigateToRoot() {
    this.currentFolderId = null;
    this.loadCaseFiles();
  }

  navigateToFolderInPath(folder: CaseFolder) {
    this.currentFolderId = folder.id;
    this.loadCaseFiles();
  }

  onFileClick(file: CaseFile) {
    this.downloadFile(file);
  }
  
  async deleteFile(file: CaseFile, event: Event) {
    event.stopPropagation();

    const alert = await this.alertController.create({
      header: 'Delete File',
      message: `Are you sure you want to delete "${file.originalName}"? This action cannot be undone.`,
      buttons: [
        {
          text: 'Cancel',
          role: 'cancel'
        },
        {
          text: 'Delete',
          role: 'destructive',
          handler: async () => {
            await this.performDelete(file);
          }
        }
      ]
    });

    await alert.present();
  }

  async performDelete(file: CaseFile) {
    const loading = await this.loadingController.create({
      message: 'Deleting file...',
      spinner: 'crescent'
    });
    await loading.present();
  
    try {
      // Delete from Firestore and Storage
      await this.firebaseService.deleteCaseFile(file.id, file.storagePath);
  
      // Optionally decrement folder file count if folderId is tracked
      if (file.folderId) {
        await this.firebaseService.decrementFolderFileCount(file.folderId);
      }
  
      // Update case file count
      await this.firebaseService.updateCaseFileCount(this.selectedCaseId);
      await this.loadCaseFiles();
      // Remove from local list
      this.caseFiles = this.caseFiles.filter(f => f.id !== file.id);
  
      this.showToast('File deleted successfully', 'success');
    } catch (error) {
      console.error('Delete error:', error);
      this.showToast('Failed to delete file', 'danger');
    } finally {
      await loading.dismiss();
    }
  }  

  openUploadModal() {
    if (!this.selectedCaseId) {
      this.showToast('Please select a case first', 'warning');
      return;
    }
    this.triggerFileInput();
  }

  getFileIcon(fileType: string): string {
    if (fileType.includes('pdf')) return 'document-text';
    if (fileType.includes('word') || fileType.includes('doc')) return 'document';
    if (fileType.includes('image')) return 'image';
    if (fileType.includes('text')) return 'document-text-outline';
    return 'document-outline';
  }

  getFileColor(fileType: string): string {
    if (fileType.includes('pdf')) return 'danger';
    if (fileType.includes('word') || fileType.includes('doc')) return 'primary';
    if (fileType.includes('image')) return 'success';
    if (fileType.includes('text')) return 'medium';
    return 'medium';
  }

  formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  formatDate(date: Date): string {
    return new Date(date).toLocaleDateString();
  }

  async showToast(message: string, color: 'success' | 'warning' | 'danger' = 'success') {
    const toast = await this.toastController.create({
      message,
      duration: 3000,
      color,
      position: 'bottom'
    });
    await toast.present();
  }
}
