"use strict";
(self["webpackChunkveritus_secretary"] = self["webpackChunkveritus_secretary"] || []).push([["node_modules_ionic_core_dist_esm_ion-text_entry_js"],{

/***/ 7817:
/*!*************************************************************!*\
  !*** ./node_modules/@ionic/core/dist/esm/ion-text.entry.js ***!
  \*************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ion_text: () => (/* binding */ Text)
/* harmony export */ });
/* harmony import */ var _index_a1a47f01_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./index-a1a47f01.js */ 2856);
/* harmony import */ var _theme_01f3f29c_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./theme-01f3f29c.js */ 1882);
/* harmony import */ var _ionic_global_94f25d1b_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ionic-global-94f25d1b.js */ 3502);
/*!
 * (C) Ionic http://ionicframework.com - MIT License
 */



const textCss = ":host(.ion-color){color:var(--ion-color-base)}";
const IonTextStyle0 = textCss;
const Text = class {
  constructor(hostRef) {
    (0,_index_a1a47f01_js__WEBPACK_IMPORTED_MODULE_0__.r)(this, hostRef);
    this.color = undefined;
  }
  render() {
    const mode = (0,_ionic_global_94f25d1b_js__WEBPACK_IMPORTED_MODULE_2__.b)(this);
    return (0,_index_a1a47f01_js__WEBPACK_IMPORTED_MODULE_0__.h)(_index_a1a47f01_js__WEBPACK_IMPORTED_MODULE_0__.H, {
      key: '4330b56cbc4e15953d9b3162fb40af728a8195dd',
      class: (0,_theme_01f3f29c_js__WEBPACK_IMPORTED_MODULE_1__.c)(this.color, {
        [mode]: true
      })
    }, (0,_index_a1a47f01_js__WEBPACK_IMPORTED_MODULE_0__.h)("slot", {
      key: 'ec674a71d8fbb04d537fd79d617d9db4a607c340'
    }));
  }
};
Text.style = IonTextStyle0;


/***/ })

}]);
//# sourceMappingURL=node_modules_ionic_core_dist_esm_ion-text_entry_js.js.map