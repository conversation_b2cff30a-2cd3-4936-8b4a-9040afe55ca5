import { Injectable } from '@angular/core';
import { Observable, of, delay } from 'rxjs';

export interface MockLawyer {
  id: string;
  name: string;
  title: string;
  firm: string;
  location: string;
  avatar: string;
  bio: string;
  specialties: string[];
  rating: number;
  experience: string;
  isAvailable: boolean;
  phone: string;
  email: string;
  availability: {
    monday: boolean;
    tuesday: boolean;
    wednesday: boolean;
    thursday: boolean;
    friday: boolean;
    saturday: boolean;
    sunday: boolean;
  };
}

export interface MockCase {
  id: string;
  title: string;
  clientName: string;
  lawyerName: string;
  status: 'ongoing' | 'closed' | 'cancelled';
  type: string;
  startDate: Date;
  lastUpdate: Date;
  progress: number;
  description: string;
  timeline: MockTimelineEvent[];
}

export interface MockTimelineEvent {
  id: string;
  title: string;
  description: string;
  date: Date;
  isCompleted: boolean;
  type: 'filing' | 'meeting' | 'court' | 'document' | 'milestone' | 'hearing' | 'appeal' | 'resolution' | 'execution';
}

export interface MockAppointment {
  id: string;
  clientName: string;
  lawyerName: string;
  date: Date;
  time: string;
  duration: number;
  type: string;
  status: 'scheduled' | 'confirmed' | 'completed' | 'cancelled';
  notes?: string;
  location?: string;
}

export interface MockRetainer {
  id: string;
  clientName: string;
  amount: number;
  status: 'active' | 'inactive' | 'overdue';
  startDate: Date;
  endDate: Date;
  description: string;
  paymentHistory: MockPayment[];
}

export interface MockPayment {
  id: string;
  amount: number;
  date: Date;
  method: string;
  status: 'paid' | 'pending' | 'overdue';
}

export interface MockDocument {
  id: string;
  name: string;
  type: string;
  size: string;
  uploadDate: Date;
  category: string;
  caseId?: string;
  clientId?: string;
}

@Injectable({
  providedIn: 'root'
})
export class MockDataService {

  private mockLawyers: MockLawyer[] = [
    {
      id: '1',
      name: 'Atty. Sarah Johnson',
      title: 'Senior Partner',
      firm: 'Johnson & Associates',
      location: 'Manila, Philippines',
      avatar: 'assets/avatars/lawyer1.jpg',
      bio: 'Experienced corporate lawyer specializing in business law and contracts with over 12 years of practice.',
      specialties: ['Corporate Law', 'Business Contracts', 'Mergers & Acquisitions'],
      rating: 4.8,
      experience: '12 years',
      isAvailable: true,
      phone: '+63 ************',
      email: '<EMAIL>',
      availability: {
        monday: true,
        tuesday: true,
        wednesday: true,
        thursday: true,
        friday: true,
        saturday: false,
        sunday: false
      }
    },
    {
      id: '2',
      name: 'Atty. Michael Chen',
      title: 'Criminal Defense Attorney',
      firm: 'Chen Legal Services',
      location: 'Makati, Philippines',
      avatar: 'assets/avatars/lawyer2.jpg',
      bio: 'Criminal defense attorney with extensive trial experience and a track record of successful cases.',
      specialties: ['Criminal Defense', 'Trial Advocacy', 'Appeals'],
      rating: 4.9,
      experience: '15 years',
      isAvailable: true,
      phone: '+63 ************',
      email: '<EMAIL>',
      availability: {
        monday: true,
        tuesday: true,
        wednesday: false,
        thursday: true,
        friday: true,
        saturday: true,
        sunday: false
      }
    },
    {
      id: '3',
      name: 'Atty. Maria Santos',
      title: 'Family Law Specialist',
      firm: 'Santos Family Law',
      location: 'Quezon City, Philippines',
      avatar: 'assets/avatars/lawyer3.jpg',
      bio: 'Family law specialist focusing on divorce, custody, and adoption cases with compassionate approach.',
      specialties: ['Family Law', 'Divorce', 'Child Custody', 'Adoption'],
      rating: 4.7,
      experience: '10 years',
      isAvailable: false,
      phone: '+63 ************',
      email: '<EMAIL>',
      availability: {
        monday: true,
        tuesday: false,
        wednesday: true,
        thursday: true,
        friday: true,
        saturday: false,
        sunday: false
      }
    }
  ];

  private mockCases: MockCase[] = [
    {
      id: '1',
      title: 'Damages Case',
      clientName: 'Kobe Bryant',
      lawyerName: 'Atty. Sarah Johnson',
      status: 'ongoing',
      type: 'Civil Law',
      startDate: new Date('2025-07-01'),
      lastUpdate: new Date('2025-08-10'),
      progress: 33,
      description: 'Civil case for damages filed against defendant.',
      timeline: [
        {
          id: '1',
          title: 'Complaint Filed',
          description: 'Complaint for damages filed',
          date: new Date('2025-07-10'),
          isCompleted: true,
          type: 'filing'
        },
        {
          id: '2',
          title: 'Initial Hearing Set',
          description: 'Preliminary conference',
          date: new Date('2025-08-10'),
          isCompleted: true,
          type: 'court'
        },
        {
          id: '3',
          title: 'Resolution Released',
          description: 'Court resolution pending',
          date: new Date('2025-09-15'),
          isCompleted: false,
          type: 'court'
        },
        {
          id: '4',
          title: 'Appeal Filed',
          description: 'Appeal process if necessary',
          date: new Date('2025-10-01'),
          isCompleted: false,
          type: 'filing'
        },
        {
          id: '5',
          title: 'Writ Of Execution Issued',
          description: 'Execution of judgment',
          date: new Date('2025-11-01'),
          isCompleted: false,
          type: 'court'
        },
        {
          id: '6',
          title: 'Case Closed',
          description: 'Final case closure',
          date: new Date('2025-12-01'),
          isCompleted: false,
          type: 'milestone'
        }
      ]
    },
    {
      id: '2',
      title: 'Criminal Defense Case',
      clientName: 'Lebron James',
      lawyerName: 'Atty. Michael Chen',
      status: 'ongoing',
      type: 'Criminal Law',
      startDate: new Date('2025-06-15'),
      lastUpdate: new Date('2025-07-20'),
      progress: 50,
      description: 'Criminal defense case for assault charges.',
      timeline: [
        {
          id: '1',
          title: 'Complaint Filed',
          description: 'Criminal complaint filed by prosecutor',
          date: new Date('2025-06-20'),
          isCompleted: true,
          type: 'filing'
        },
        {
          id: '2',
          title: 'Initial Hearing Set',
          description: 'Arraignment and plea hearing',
          date: new Date('2025-07-15'),
          isCompleted: true,
          type: 'court'
        },
        {
          id: '3',
          title: 'Resolution Released',
          description: 'Pre-trial resolution pending',
          date: new Date('2025-08-20'),
          isCompleted: false,
          type: 'court'
        },
        {
          id: '4',
          title: 'Appeal Filed',
          description: 'Appeal if conviction occurs',
          date: new Date('2025-09-15'),
          isCompleted: false,
          type: 'filing'
        },
        {
          id: '5',
          title: 'Writ Of Execution Issued',
          description: 'Sentence execution if applicable',
          date: new Date('2025-10-15'),
          isCompleted: false,
          type: 'court'
        },
        {
          id: '6',
          title: 'Case Closed',
          description: 'Final case disposition',
          date: new Date('2025-11-15'),
          isCompleted: false,
          type: 'milestone'
        }
      ]
    },
    {
      id: '3',
      title: 'Family Law Case',
      clientName: 'Jane Smith',
      lawyerName: 'Atty. Maria Santos',
      status: 'ongoing',
      type: 'Family Law',
      startDate: new Date('2025-05-01'),
      lastUpdate: new Date('2025-06-15'),
      progress: 67,
      description: 'Child custody and support case.',
      timeline: [
        {
          id: '1',
          title: 'Complaint Filed',
          description: 'Petition for child custody filed',
          date: new Date('2025-05-10'),
          isCompleted: true,
          type: 'filing'
        },
        {
          id: '2',
          title: 'Initial Hearing Set',
          description: 'Mediation and preliminary hearing',
          date: new Date('2025-06-05'),
          isCompleted: true,
          type: 'court'
        },
        {
          id: '3',
          title: 'Resolution Released',
          description: 'Custody arrangement decision',
          date: new Date('2025-07-10'),
          isCompleted: true,
          type: 'court'
        },
        {
          id: '4',
          title: 'Appeal Filed',
          description: 'Appeal process if needed',
          date: new Date('2025-08-01'),
          isCompleted: false,
          type: 'filing'
        },
        {
          id: '5',
          title: 'Writ Of Execution Issued',
          description: 'Enforcement of custody order',
          date: new Date('2025-08-15'),
          isCompleted: false,
          type: 'court'
        },
        {
          id: '6',
          title: 'Case Closed',
          description: 'Final case resolution',
          date: new Date('2025-09-01'),
          isCompleted: false,
          type: 'milestone'
        }
      ]
    }
  ];

  private mockAppointments: MockAppointment[] = [
    {
      id: '1',
      clientName: 'John Doe',
      lawyerName: 'Atty. Sarah Johnson',
      date: new Date('2024-12-10'),
      time: '10:00 AM',
      duration: 60,
      type: 'Consultation',
      status: 'confirmed',
      notes: 'Initial consultation for business contract review',
      location: 'Johnson & Associates Office'
    },
    {
      id: '2',
      clientName: 'Jane Smith',
      lawyerName: 'Atty. Michael Chen',
      date: new Date('2024-12-11'),
      time: '2:00 PM',
      duration: 90,
      type: 'Case Review',
      status: 'scheduled',
      notes: 'Review criminal defense strategy',
      location: 'Chen Legal Services'
    }
  ];

  constructor() { }

  // TODO: Replace with real Firebase integration
  getLawyers(): Observable<MockLawyer[]> {
    return of(this.mockLawyers).pipe(delay(500));
  }

  getLawyerById(id: string): Observable<MockLawyer | undefined> {
    const lawyer = this.mockLawyers.find(l => l.id === id);
    return of(lawyer).pipe(delay(300));
  }

  searchLawyers(searchTerm: string): Observable<MockLawyer[]> {
    const filtered = this.mockLawyers.filter(lawyer =>
      lawyer.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      lawyer.firm.toLowerCase().includes(searchTerm.toLowerCase()) ||
      lawyer.specialties.some(s => s.toLowerCase().includes(searchTerm.toLowerCase()))
    );
    return of(filtered).pipe(delay(500));
  }

  getCases(): Observable<MockCase[]> {
    return of(this.mockCases).pipe(delay(500));
  }

  getCaseById(id: string): Observable<MockCase | undefined> {
    const case_ = this.mockCases.find(c => c.id === id);
    return of(case_).pipe(delay(300));
  }

  getAppointments(): Observable<MockAppointment[]> {
    return of(this.mockAppointments).pipe(delay(500));
  }

  // TODO: Implement with Firebase
  requestRetainer(lawyerId: string, clientData: any): Observable<boolean> {
    console.log('Requesting retainer for lawyer:', lawyerId, 'with data:', clientData);
    return of(true).pipe(delay(1000));
  }

  // TODO: Implement with Firebase
  scheduleAppointment(appointmentData: any): Observable<boolean> {
    console.log('Scheduling appointment:', appointmentData);
    return of(true).pipe(delay(1000));
  }

  // TODO: Implement with Firebase
  generateDocument(templateId: string, formData: any): Observable<string> {
    console.log('Generating document:', templateId, formData);
    return of('mock-document-url.pdf').pipe(delay(2000));
  }
}
