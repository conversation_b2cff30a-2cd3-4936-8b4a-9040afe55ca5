import { Injectable } from '@angular/core';
import { Reminder } from './reminder.service';

// TODO: Install Capacitor Push Notifications
// npm install @capacitor/push-notifications

export interface NotificationPayload {
  title: string;
  body: string;
  data?: any;
  badge?: number;
  sound?: string;
  icon?: string;
  image?: string;
  actions?: NotificationAction[];
}

export interface NotificationAction {
  id: string;
  title: string;
  icon?: string;
}

export interface NotificationSettings {
  userId: string;
  pushEnabled: boolean;
  emailEnabled: boolean;
  smsEnabled: boolean;
  quietHours: {
    enabled: boolean;
    startTime: string; // HH:MM format
    endTime: string;   // HH:MM format
  };
  reminderTypes: {
    appointments: boolean;
    deadlines: boolean;
    payments: boolean;
    courtDates: boolean;
    followUps: boolean;
  };
}

@Injectable({
  providedIn: 'root'
})
export class NotificationService {

  private fcmToken: string | null = null;

  constructor() {
    this.initializeNotifications();
  }

  // Initialize push notifications
  private async initializeNotifications(): Promise<void> {
    try {
      // TODO: Implement Capacitor Push Notifications
      // const { PushNotifications } = await import('@capacitor/push-notifications');
      
      // Request permission
      // const permission = await PushNotifications.requestPermissions();
      
      // if (permission.receive === 'granted') {
      //   await PushNotifications.register();
      // }

      // Listen for registration
      // PushNotifications.addListener('registration', (token) => {
      //   this.fcmToken = token.value;
      //   this.saveFCMToken(token.value);
      // });

      // Listen for incoming notifications
      // PushNotifications.addListener('pushNotificationReceived', (notification) => {
      //   this.handleIncomingNotification(notification);
      // });

      // Listen for notification actions
      // PushNotifications.addListener('pushNotificationActionPerformed', (action) => {
      //   this.handleNotificationAction(action);
      // });

      console.log('Push notifications initialized (placeholder)');
    } catch (error) {
      console.error('Failed to initialize push notifications:', error);
    }
  }

  // Send reminder notification
  async sendReminderNotification(reminder: Reminder): Promise<void> {
    const payload = this.createReminderPayload(reminder);
    
    // Send push notification if enabled
    if (reminder.notificationChannels.includes('push')) {
      await this.sendPushNotification(reminder.userId, payload);
    }

    // Send email notification if enabled
    if (reminder.notificationChannels.includes('email')) {
      await this.sendEmailNotification(reminder.userId, payload);
    }

    // Send SMS notification if enabled
    if (reminder.notificationChannels.includes('sms')) {
      await this.sendSMSNotification(reminder.userId, payload);
    }
  }

  // Create notification payload from reminder
  private createReminderPayload(reminder: Reminder): NotificationPayload {
    const actions: NotificationAction[] = [
      { id: 'dismiss', title: 'Dismiss' },
      { id: 'snooze', title: 'Snooze 15min' }
    ];

    let icon = 'notifications';
    switch (reminder.type) {
      case 'appointment':
        icon = 'calendar';
        break;
      case 'deadline':
        icon = 'alarm';
        break;
      case 'payment':
        icon = 'card';
        break;
      case 'court_date':
        icon = 'business';
        break;
    }

    return {
      title: reminder.title,
      body: reminder.description || '',
      data: {
        reminderId: reminder.id,
        type: reminder.type,
        relatedEntityId: reminder.relatedEntityId,
        relatedEntityType: reminder.relatedEntityType
      },
      icon,
      actions
    };
  }

  // Send push notification
  private async sendPushNotification(userId: string, payload: NotificationPayload): Promise<void> {
    try {
      // TODO: Implement Firebase Cloud Messaging
      // This would typically involve:
      // 1. Get user's FCM token from database
      // 2. Send notification via Firebase Admin SDK or FCM REST API
      
      console.log('Sending push notification (placeholder):', payload);
      
      // Placeholder implementation
      if ('Notification' in window && Notification.permission === 'granted') {
        new Notification(payload.title, {
          body: payload.body,
          icon: payload.icon,
          badge: payload.badge,
          data: payload.data
        });
      }
    } catch (error) {
      console.error('Failed to send push notification:', error);
    }
  }

  // Send email notification
  private async sendEmailNotification(userId: string, payload: NotificationPayload): Promise<void> {
    try {
      // TODO: Implement email sending via Firebase Functions or email service
      console.log('Sending email notification (placeholder):', payload);
    } catch (error) {
      console.error('Failed to send email notification:', error);
    }
  }

  // Send SMS notification
  private async sendSMSNotification(userId: string, payload: NotificationPayload): Promise<void> {
    try {
      // TODO: Implement SMS sending via Twilio or similar service
      console.log('Sending SMS notification (placeholder):', payload);
    } catch (error) {
      console.error('Failed to send SMS notification:', error);
    }
  }

  // Handle incoming notification
  private handleIncomingNotification(notification: any): void {
    console.log('Received notification:', notification);
    
    // Show local notification if app is in foreground
    if (document.visibilityState === 'visible') {
      this.showLocalNotification(notification);
    }
  }

  // Handle notification action
  private handleNotificationAction(action: any): void {
    console.log('Notification action performed:', action);
    
    const { actionId, notification } = action;
    const reminderId = notification.data?.reminderId;
    
    if (!reminderId) return;

    switch (actionId) {
      case 'dismiss':
        this.dismissReminder(reminderId);
        break;
      case 'snooze':
        this.snoozeReminder(reminderId, 15); // 15 minutes
        break;
    }
  }

  // Show local notification
  private showLocalNotification(notification: any): void {
    if ('Notification' in window && Notification.permission === 'granted') {
      new Notification(notification.title, {
        body: notification.body,
        icon: notification.data?.icon || 'assets/icon/favicon.png',
        data: notification.data
      });
    }
  }

  // Request notification permission
  async requestPermission(): Promise<boolean> {
    if (!('Notification' in window)) {
      console.log('This browser does not support notifications');
      return false;
    }

    if (Notification.permission === 'granted') {
      return true;
    }

    if (Notification.permission === 'denied') {
      return false;
    }

    const permission = await Notification.requestPermission();
    return permission === 'granted';
  }

  // Save FCM token
  private async saveFCMToken(token: string): Promise<void> {
    // TODO: Save token to Firestore for the current user
    console.log('FCM Token (placeholder):', token);
  }

  // Dismiss reminder
  private async dismissReminder(reminderId: string): Promise<void> {
    // TODO: Call ReminderService to dismiss reminder
    console.log('Dismissing reminder:', reminderId);
  }

  // Snooze reminder
  private async snoozeReminder(reminderId: string, minutes: number): Promise<void> {
    // TODO: Call ReminderService to snooze reminder
    console.log('Snoozing reminder:', reminderId, 'for', minutes, 'minutes');
  }

  // Get notification settings
  async getNotificationSettings(userId: string): Promise<NotificationSettings> {
    // TODO: Get from Firestore
    return {
      userId,
      pushEnabled: true,
      emailEnabled: true,
      smsEnabled: false,
      quietHours: {
        enabled: true,
        startTime: '22:00',
        endTime: '08:00'
      },
      reminderTypes: {
        appointments: true,
        deadlines: true,
        payments: true,
        courtDates: true,
        followUps: true
      }
    };
  }

  // Update notification settings
  async updateNotificationSettings(settings: NotificationSettings): Promise<void> {
    // TODO: Save to Firestore
    console.log('Updating notification settings (placeholder):', settings);
  }

  // Check if notifications should be sent (respect quiet hours)
  shouldSendNotification(settings: NotificationSettings): boolean {
    if (!settings.quietHours.enabled) {
      return true;
    }

    const now = new Date();
    const currentTime = now.getHours() * 60 + now.getMinutes();
    
    const [startHour, startMin] = settings.quietHours.startTime.split(':').map(Number);
    const [endHour, endMin] = settings.quietHours.endTime.split(':').map(Number);
    
    const startTime = startHour * 60 + startMin;
    const endTime = endHour * 60 + endMin;

    // Handle overnight quiet hours (e.g., 22:00 to 08:00)
    if (startTime > endTime) {
      return !(currentTime >= startTime || currentTime <= endTime);
    } else {
      return !(currentTime >= startTime && currentTime <= endTime);
    }
  }
}
