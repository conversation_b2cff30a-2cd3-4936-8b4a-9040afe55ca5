{"firestore": {"rules": "firestore.rules", "indexes": "firestore.indexes.json"}, "functions": {"predeploy": ["npm --prefix \"$RESOURCE_DIR\" run build"], "source": "functions"}, "hosting": [{"target": "mobile", "public": "dist", "ignore": ["firebase.json", "**/.*", "**/node_modules/**"], "rewrites": [{"source": "**", "destination": "/index.html"}], "headers": [{"source": "**/*.@(js|css)", "headers": [{"key": "Cache-Control", "value": "max-age=31536000"}]}]}, {"target": "secretary", "public": "dist-secretary", "ignore": ["firebase.json", "**/.*", "**/node_modules/**"], "rewrites": [{"source": "**", "destination": "/index.html"}]}], "storage": {"rules": "storage.rules"}, "emulators": {"auth": {"port": 9099}, "functions": {"port": 5001}, "firestore": {"port": 8080}, "hosting": {"port": 5000}, "storage": {"port": 9199}, "ui": {"enabled": true}}}