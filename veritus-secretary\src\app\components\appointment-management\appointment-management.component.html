<div class="appointment-management-container">
  
  <!-- Header with Stats -->
  <div class="management-header">
    <div class="header-title">
      <h2>Appointment Management</h2>
      <p>Manage and track all appointments for your linked lawyers</p>
    </div>
    
    <div class="stats-grid">
      <div class="stat-card">
        <div class="stat-number">{{ totalAppointments }}</div>
        <div class="stat-label">Total</div>
      </div>
      <div class="stat-card">
        <div class="stat-number">{{ filteredCount }}</div>
        <div class="stat-label">Filtered</div>
      </div>
      <div class="stat-card">
        <div class="stat-number">{{ upcomingCount }}</div>
        <div class="stat-label">Upcoming</div>
      </div>
      <div class="stat-card">
        <div class="stat-number">{{ pendingCount }}</div>
        <div class="stat-label">Pending</div>
      </div>
    </div>
  </div>

  <!-- Filters Section -->
  <div class="filters-section">
    <div class="filters-header">
      <h3>Filters</h3>
      <ion-button fill="clear" size="small" (click)="clearFilters()">
        <ion-icon name="refresh-outline" slot="start"></ion-icon>
        Clear All
      </ion-button>
    </div>

    <!-- Date Range Filter -->
    <div class="filter-group">
      <h4>Date Range</h4>
      <div class="date-range-inputs">
        <div class="date-input-group">
          <ion-label>From</ion-label>
          <ion-datetime
            presentation="date"
            [(ngModel)]="filter.dateFrom"
            (ionChange)="onFilterChange()"
            class="date-filter">
          </ion-datetime>
        </div>
        <div class="date-input-group">
          <ion-label>To</ion-label>
          <ion-datetime
            presentation="date"
            [(ngModel)]="filter.dateTo"
            (ionChange)="onFilterChange()"
            class="date-filter">
          </ion-datetime>
        </div>
      </div>
    </div>

    <!-- Lawyer Filter -->
    <div class="filter-group" *ngIf="linkedLawyers.length > 1">
      <h4>Lawyer</h4>
      <ion-select
        [(ngModel)]="filter.lawyerId"
        (ionChange)="onFilterChange()"
        placeholder="All Lawyers"
        class="lawyer-filter">
        <ion-select-option value="">All Lawyers</ion-select-option>
        <ion-select-option *ngFor="let lawyer of linkedLawyers" [value]="lawyer.uid">
          {{ lawyer.name }}
        </ion-select-option>
      </ion-select>
    </div>

    <!-- Status Filter -->
    <div class="filter-group">
      <h4>Status</h4>
      <div class="status-chips">
        <ion-chip
          *ngFor="let status of statusOptions"
          [color]="isStatusSelected(status.value) ? status.color : 'medium'"
          [outline]="!isStatusSelected(status.value)"
          (click)="toggleStatusFilter(status.value)"
          class="filter-chip">
          {{ status.label }}
        </ion-chip>
      </div>
    </div>

    <!-- Type Filter -->
    <div class="filter-group">
      <h4>Appointment Type</h4>
      <div class="type-chips">
        <ion-chip
          *ngFor="let type of typeOptions"
          [color]="isTypeSelected(type) ? 'primary' : 'medium'"
          [outline]="!isTypeSelected(type)"
          (click)="toggleTypeFilter(type)"
          class="filter-chip">
          {{ type }}
        </ion-chip>
      </div>
    </div>
  </div>

  <!-- View Controls -->
  <div class="view-controls">
    <div class="view-mode-toggle">
      <ion-segment [(ngModel)]="viewMode" (ionChange)="onFilterChange()">
        <ion-segment-button value="list">
          <ion-icon name="list-outline"></ion-icon>
          <ion-label>List</ion-label>
        </ion-segment-button>
        <ion-segment-button value="grid">
          <ion-icon name="grid-outline"></ion-icon>
          <ion-label>Grid</ion-label>
        </ion-segment-button>
      </ion-segment>
    </div>

    <div class="sort-controls">
      <ion-select
        [(ngModel)]="sortBy"
        (ionChange)="onSortChange()"
        placeholder="Sort by"
        class="sort-select">
        <ion-select-option value="date">Date</ion-select-option>
        <ion-select-option value="status">Status</ion-select-option>
        <ion-select-option value="lawyer">Lawyer</ion-select-option>
        <ion-select-option value="type">Type</ion-select-option>
      </ion-select>
      
      <ion-button
        fill="clear"
        size="small"
        (click)="toggleSortOrder()"
        class="sort-order-btn">
        <ion-icon [name]="sortOrder === 'asc' ? 'arrow-up' : 'arrow-down'"></ion-icon>
      </ion-button>
    </div>
  </div>

  <!-- Loading State -->
  <div *ngIf="isLoading" class="loading-container">
    <ion-spinner name="crescent"></ion-spinner>
    <p>Loading appointments...</p>
  </div>

  <!-- Appointments List/Grid -->
  <div *ngIf="!isLoading" class="appointments-container">
    
    <!-- Empty State -->
    <div *ngIf="filteredAppointments.length === 0" class="empty-state">
      <ion-icon name="calendar-outline" class="empty-icon"></ion-icon>
      <h3>No Appointments Found</h3>
      <p *ngIf="totalAppointments === 0">No appointments have been scheduled yet.</p>
      <p *ngIf="totalAppointments > 0">Try adjusting your filters to see more appointments.</p>
    </div>

    <!-- List View -->
    <div *ngIf="viewMode === 'list' && filteredAppointments.length > 0" class="appointments-list">
      <div
        *ngFor="let appointment of filteredAppointments"
        class="appointment-item"
        [class.urgent]="appointment.isUrgent"
        [class.past]="isPast(appointment)">
        
        <div class="appointment-main">
          <div class="appointment-info">
            <div class="client-name">{{ appointment.clientName }}</div>
            <div class="appointment-type">{{ appointment.type }}</div>
            <div class="lawyer-name">with {{ appointment.lawyerName }}</div>
          </div>
          
          <div class="appointment-datetime">
            <div class="date">{{ formatDateTime(appointment.date, appointment.time) }}</div>
            <div class="time-until" *ngIf="isUpcoming(appointment)">
              <!-- Add time until appointment calculation here -->
            </div>
          </div>
          
          <div class="appointment-status">
            <ion-chip [color]="getStatusColor(appointment.status)" class="status-chip">
              {{ getStatusLabel(appointment.status) }}
            </ion-chip>
            <ion-chip *ngIf="appointment.isUrgent" color="danger" class="urgent-chip">
              <ion-icon name="warning" slot="start"></ion-icon>
              Urgent
            </ion-chip>
          </div>
        </div>
        
        <div class="appointment-actions">
          <ion-button
            *ngIf="canReschedule(appointment)"
            fill="outline"
            size="small"
            (click)="openRescheduleModal(appointment)">
            <ion-icon name="calendar-outline" slot="start"></ion-icon>
            Reschedule
          </ion-button>
          
          <ion-button
            fill="clear"
            size="small"
            (click)="updateAppointmentStatus(appointment, 'confirmed')"
            *ngIf="appointment.status === 'pending'">
            <ion-icon name="checkmark" slot="start"></ion-icon>
            Confirm
          </ion-button>
          
          <ion-button
            fill="clear"
            size="small"
            color="danger"
            (click)="updateAppointmentStatus(appointment, 'cancelled')"
            *ngIf="appointment.status !== 'cancelled' && appointment.status !== 'completed'">
            <ion-icon name="close" slot="start"></ion-icon>
            Cancel
          </ion-button>
        </div>
        
        <div *ngIf="appointment.remarks" class="appointment-remarks">
          <ion-icon name="chatbubble-outline"></ion-icon>
          <span>{{ appointment.remarks }}</span>
        </div>
      </div>
    </div>

    <!-- Grid View -->
    <div *ngIf="viewMode === 'grid' && filteredAppointments.length > 0" class="appointments-grid">
      <div
        *ngFor="let appointment of filteredAppointments"
        class="appointment-card"
        [class.urgent]="appointment.isUrgent"
        [class.past]="isPast(appointment)">
        
        <div class="card-header">
          <div class="client-name">{{ appointment.clientName }}</div>
          <ion-chip [color]="getStatusColor(appointment.status)" class="status-chip">
            {{ getStatusLabel(appointment.status) }}
          </ion-chip>
        </div>
        
        <div class="card-content">
          <div class="appointment-type">{{ appointment.type }}</div>
          <div class="lawyer-name">{{ appointment.lawyerName }}</div>
          <div class="appointment-datetime">
            {{ formatDateTime(appointment.date, appointment.time) }}
          </div>
        </div>
        
        <div class="card-actions">
          <ion-button
            *ngIf="canReschedule(appointment)"
            fill="outline"
            size="small"
            (click)="openRescheduleModal(appointment)">
            Reschedule
          </ion-button>
        </div>
      </div>
    </div>
  </div>
</div>
