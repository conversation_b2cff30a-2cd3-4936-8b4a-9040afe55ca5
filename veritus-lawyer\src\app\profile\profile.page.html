<ion-content class="profile-content">
  <div class="profile-container">
    <!-- Header -->
    <div class="profile-header">
      <h1 class="page-title">Profile</h1>
    </div>

    <!-- Profile Avatar Section -->
    <div class="avatar-section">
      <div class="profile-avatar"
           [class.uploading]="isUploading">

        <!-- Clickable area overlay -->
        <div class="click-area"
             (click)="onUploadImage()"
             (mouseenter)="isHovering = true"
             (mouseleave)="isHovering = false"></div>

        <!-- User uploaded image -->
        <img *ngIf="profileImageUrl"
             [src]="profileImageUrl"
             alt="Profile"
             class="avatar-image">

        <!-- Default SVG avatar when no image is uploaded -->
        <svg *ngIf="!profileImageUrl" width="80" height="80" viewBox="0 0 80 80" fill="none" xmlns="http://www.w3.org/2000/svg">
          <!-- Background circle -->
          <circle cx="40" cy="40" r="40" fill="#C49A56"/>

          <!-- Lawyer figure -->
          <!-- Head -->
          <circle cx="40" cy="28" r="8" fill="#F4C2A1"/>

          <!-- Body/Suit -->
          <rect x="32" y="36" width="16" height="20" rx="2" fill="#2C3E50"/>

          <!-- Tie -->
          <rect x="38" y="36" width="4" height="16" fill="#C49A56"/>

          <!-- Briefcase -->
          <rect x="50" y="42" width="8" height="6" rx="1" fill="#8B4513"/>
          <rect x="51" y="44" width="6" height="2" fill="#654321"/>

          <!-- Scale of Justice -->
          <circle cx="28" cy="45" r="3" fill="#FFD700"/>
          <rect x="27" y="42" width="2" height="8" fill="#8B4513"/>
          <rect x="24" y="41" width="8" height="1" fill="#8B4513"/>
          <circle cx="22" cy="40" r="1.5" fill="#FFD700"/>
          <circle cx="34" cy="40" r="1.5" fill="#FFD700"/>
        </svg>

        <!-- Upload overlay -->
        <div class="upload-overlay" [class.visible]="isHovering || isUploading">
          <ion-icon name="camera" class="upload-icon" *ngIf="!isUploading"></ion-icon>
          <ion-icon name="cloud-upload" class="upload-icon uploading-icon" *ngIf="isUploading"></ion-icon>
          <span class="upload-text">{{ isUploading ? 'Uploading...' : 'Upload Photo' }}</span>
        </div>

        <!-- Upload progress -->
        <div class="upload-progress" *ngIf="isUploading">
          <ion-spinner name="crescent" class="upload-spinner"></ion-spinner>
        </div>
      </div>

      <!-- Hidden file input -->
      <input #fileInput
             type="file"
             accept="image/jpeg,image/jpg,image/png,image/gif,image/webp"
             (change)="onFileSelected($event)"
             style="display: none;"
             title="Select profile image">

      <div class="verified-badge">
        <ion-icon name="checkmark-circle" class="verified-icon"></ion-icon>
        <span class="verified-text">Verified</span>
      </div>
    </div>

    <!-- Menu Options -->
    <div class="menu-section">
      <div class="menu-item" (click)="onProfileInformation()" style="position: relative; z-index: 1;">
        <div class="menu-item-content">
          <ion-icon name="person-outline" class="menu-icon"></ion-icon>
          <span class="menu-text">Profile Information</span>
        </div>
        <ion-icon name="chevron-forward-outline" class="chevron-icon"></ion-icon>
      </div>

      <div class="menu-item" (click)="onProfileIntroduction()">
        <div class="menu-item-content">
          <ion-icon name="document-text-outline" class="menu-icon"></ion-icon>
          <span class="menu-text">Profile Introduction & description</span>
        </div>
        <ion-icon name="chevron-forward-outline" class="chevron-icon"></ion-icon>
      </div>

      <div class="menu-item" (click)="onPermissions()">
        <div class="menu-item-content">
          <ion-icon name="shield-checkmark-outline" class="menu-icon"></ion-icon>
          <span class="menu-text">Permissions</span>
        </div>
        <ion-icon name="chevron-forward-outline" class="chevron-icon"></ion-icon>
      </div>

      <div class="menu-item logout-item" (click)="onLogout()">
        <div class="menu-item-content">
          <ion-icon name="log-out-outline" class="menu-icon logout-icon"></ion-icon>
          <span class="menu-text logout-text">Logout</span>
        </div>
        <ion-icon name="chevron-forward-outline" class="chevron-icon"></ion-icon>
      </div>
    </div>
  </div>
</ion-content>
