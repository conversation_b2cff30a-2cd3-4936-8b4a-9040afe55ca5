<ion-header>
  <ion-toolbar class="download-toolbar">
    <ion-buttons slot="start">
      <ion-button (click)="onBack()">
        <ion-icon name="arrow-back" class="back-icon"></ion-icon>
      </ion-button>
    </ion-buttons>
    <ion-title class="download-title">Download Documents</ion-title>
  </ion-toolbar>
</ion-header>

<ion-content class="download-content">
  <div class="download-container">
    
    <!-- Category Filter -->
    <div class="filter-section">
      <h3 class="filter-title">Filter by Category</h3>
      <div class="category-chips">
        <div 
          class="category-chip" 
          *ngFor="let category of categories"
          [class.active]="selectedCategory === category.id"
          (click)="onCategoryChange(category.id)">
          {{ category.name }}
        </div>
      </div>
    </div>

    <!-- Documents List -->
    <div class="documents-section" *ngIf="!isLoading">
      <h3 class="section-title">
        {{ filteredDocuments.length }} Document{{ filteredDocuments.length !== 1 ? 's' : '' }}
      </h3>
      
      <div class="documents-list" *ngIf="filteredDocuments.length > 0">
        <div 
          class="document-card" 
          *ngFor="let document of filteredDocuments">
          
          <div class="document-icon">
            <ion-icon [name]="getDocumentIcon(document.type)"></ion-icon>
          </div>
          
          <div class="document-info">
            <h4 class="document-name">{{ document.name }}</h4>
            <div class="document-meta">
              <span class="document-size">{{ document.size }}</span>
              <span class="document-date">{{ formatDate(document.uploadDate) }}</span>
            </div>
            <div class="document-type">
              <span class="type-badge">{{ document.type }}</span>
            </div>
          </div>
          
          <div class="document-actions">
            <button 
              class="download-btn"
              (click)="onDownloadDocument(document)">
              <ion-icon name="download" class="download-icon"></ion-icon>
              Download PDF
            </button>
          </div>
        </div>
      </div>

      <!-- Empty State -->
      <div class="empty-state" *ngIf="filteredDocuments.length === 0">
        <ion-icon name="folder-open-outline" class="empty-icon"></ion-icon>
        <h3 class="empty-title">No Documents Found</h3>
        <p class="empty-description">
          No documents found in the selected category.
        </p>
      </div>
    </div>

    <!-- Loading State -->
    <div class="loading-state" *ngIf="isLoading">
      <ion-spinner name="crescent"></ion-spinner>
      <p>Loading documents...</p>
    </div>

  </div>
</ion-content>
