// Modern White & Gold Appointments Design
.appointments-content {
  --background: #FFFFFF;
  background: #FFFFFF;
}

.status-bar-spacer {
  height: 44px;
  background: #FFFFFF;
}

.appointments-container {
  padding: 16px;
  background: #FFFFFF;
  min-height: calc(100vh - 44px);
}

// Page Header
.page-header {
  margin-bottom: 24px;
  padding-top: 8px;
}

.page-title {
  font-size: 24px;
  font-weight: 700;
  color: #000000;
  margin: 0;
  letter-spacing: -0.01em;
}

// Calendar Section
.calendar-section {
  margin-bottom: 32px;
}

.month-navigation {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
  padding: 0 8px;
}

.nav-button {
  background: none;
  border: none;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background: rgba(196, 154, 86, 0.1);
  }

  &:active {
    transform: scale(0.95);
  }
}

.nav-icon {
  font-size: 20px;
  color: #616161;
  transition: color 0.2s ease;

  .nav-button:hover & {
    color: #C49A56;
  }
}

.month-title {
  font-size: 18px;
  font-weight: 600;
  color: #000000;
  margin: 0;
  text-align: center;
  flex: 1;
}

// Calendar Grid
.calendar-grid {
  background: #FFFFFF;
  border-radius: 12px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border: 1px solid rgba(0, 0, 0, 0.04);
}

.day-headers {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 8px;
  margin-bottom: 12px;
}

.day-header {
  text-align: center;
  font-size: 12px;
  font-weight: 500;
  color: #9E9E9E;
  padding: 8px 4px;
}

.calendar-days {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 8px;
}

.calendar-day {
  aspect-ratio: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;

  .day-number {
    font-size: 14px;
    font-weight: 500;
    color: #000000;
    z-index: 1;
  }

  &.other-month {
    .day-number {
      color: #E0E0E0;
    }
  }

  &.selected {
    background: #C49A56;

    .day-number {
      color: #FFFFFF;
      font-weight: 600;
    }
  }

  &:hover:not(.selected):not(.other-month) {
    background: rgba(196, 154, 86, 0.05);
  }
}

// Bottom Section
.bottom-section {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
  align-items: start;
}

// Card Styling
.availability-card,
.appointments-card {
  flex: 1;
  background: #FFFFFF;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border: 1px solid rgba(0, 0, 0, 0.04);
  overflow: hidden;
}

.card-header {
  padding: 16px 16px 0 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-content {
  padding: 16px;
}

.section-title {
  font-size: 20px;
  font-weight: 600;
  color: #000000;
  margin: 0 0 16px 0;
}

.calendar-day.available {
  background-color: #3498db; // blue
  color: white;
  border-radius: 50%;
}

.calendar-day.booked {
  background-color: #a0522d; // brown
  color: white;
  border-radius: 50%;
}


.availability-list {
  margin-bottom: 16px;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.availability-slot-card {
  background: #F8F9FA;
  border: 1px solid rgba(0, 0, 0, 0.06);
  border-radius: 8px;
  padding: 16px;
  transition: all 0.2s ease;

  &:hover {
    background: #F5F5F5;
    border-color: rgba(196, 154, 86, 0.2);
  }
}

.slot-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.slot-date {
  font-size: 16px;
  font-weight: 600;
  color: #000000;
  line-height: 1.2;
}

.slot-time {
  font-size: 16px;
  color: #616161;
  line-height: 1.2;
}

.manage-button {
  background: #C49A56;
  border: none;
  border-radius: 8px;
  padding: 12px 24px;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background: #B8894A;
    transform: translateY(-1px);
  }

  &:active {
    transform: translateY(0);
  }
}

.button-text {
  font-size: 14px;
  font-weight: 600;
  color: #FFFFFF;
}

// Upcoming Appointments Section
.appointments-section {
  flex: 1;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.view-all-button {
  background: none;
  border: none;
  display: flex;
  align-items: center;
  gap: 4px;
  cursor: pointer;
  padding: 4px 0;
  transition: color 0.2s ease;

  &:hover {
    .view-all-text {
      color: #B8894A;
    }

    .chevron-icon {
      color: #B8894A;
    }
  }
}

.view-all-text {
  font-size: 14px;
  font-weight: 500;
  color: #C49A56;
  transition: color 0.2s ease;
}

.chevron-icon {
  font-size: 16px;
  color: #C49A56;
  transition: color 0.2s ease;
}

.appointments-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.appointment-slot-card {
  background: #F8F9FA;
  border: 1px solid rgba(0, 0, 0, 0.06);
  border-radius: 8px;
  padding: 16px;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background: #F5F5F5;
    border-color: rgba(196, 154, 86, 0.2);
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  }

  &:active {
    transform: translateY(0);
  }
}

.appointment-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.appointment-date {
  font-size: 14px;
  font-weight: 600;
  color: #000000;
  line-height: 1.2;
}

.appointment-time {
  font-size: 14px;
  color: #616161;
  line-height: 1.2;
}

.appointment-type {
  font-size: 14px;
  color: #616161;
  line-height: 1.2;
}

// Responsive Design
@media (max-width: 480px) {
  .appointments-container {
    padding: 12px;
  }

  .page-title {
    font-size: 22px;
  }

  .calendar-grid {
    padding: 12px;
  }

  .bottom-section {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .calendar-days {
    gap: 6px;
  }

  .calendar-day {
    .day-number {
      font-size: 13px;
    }
  }

  .day-header {
    font-size: 11px;
    padding: 6px 4px;
  }

  .card-header {
    padding: 12px 12px 0 12px;
  }

  .card-content {
    padding: 12px;
  }

  .availability-slot-card,
  .appointment-slot-card {
    padding: 12px;
  }

  .availability-list,
  .appointments-list {
    gap: 8px;
  }

  .month-navigation {
    padding: 0 4px;
  }

  .nav-button {
    width: 36px;
    height: 36px;
  }

  .nav-icon {
    font-size: 18px;
  }
}
