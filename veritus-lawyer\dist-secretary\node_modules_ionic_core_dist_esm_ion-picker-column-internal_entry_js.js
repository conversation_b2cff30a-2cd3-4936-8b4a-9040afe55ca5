"use strict";
(self["webpackChunkveritus_secretary"] = self["webpackChunkveritus_secretary"] || []).push([["node_modules_ionic_core_dist_esm_ion-picker-column-internal_entry_js"],{

/***/ 3970:
/*!*******************************************************************************!*\
  !*** ./node_modules/@ionic/core/dist/esm/ion-picker-column-internal.entry.js ***!
  \*******************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ion_picker_column_internal: () => (/* binding */ PickerColumnInternal)
/* harmony export */ });
/* harmony import */ var C_Users_acer_Desktop_angularprojects_veritus_Veritus_veritus_secretary_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js */ 9204);
/* harmony import */ var _index_a1a47f01_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./index-a1a47f01.js */ 2856);
/* harmony import */ var _helpers_be245865_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./helpers-be245865.js */ 2641);
/* harmony import */ var _haptic_554688a5_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./haptic-554688a5.js */ 1416);
/* harmony import */ var _ionic_global_94f25d1b_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./ionic-global-94f25d1b.js */ 3502);
/* harmony import */ var _theme_01f3f29c_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./theme-01f3f29c.js */ 1882);
/* harmony import */ var _capacitor_59395cbd_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./capacitor-59395cbd.js */ 1341);
/* harmony import */ var _index_a5d50daf_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./index-a5d50daf.js */ 6639);

/*!
 * (C) Ionic http://ionicframework.com - MIT License
 */







const pickerColumnInternalIosCss = ":host{-webkit-padding-start:16px;padding-inline-start:16px;-webkit-padding-end:16px;padding-inline-end:16px;padding-top:0px;padding-bottom:0px;height:200px;outline:none;font-size:22px;-webkit-scroll-snap-type:y mandatory;-ms-scroll-snap-type:y mandatory;scroll-snap-type:y mandatory;overflow-x:hidden;overflow-y:scroll;scrollbar-width:none;text-align:center}:host::-webkit-scrollbar{display:none}:host .picker-item{padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;display:block;width:100%;height:34px;border:0px;outline:none;background:transparent;color:inherit;font-family:var(--ion-font-family, inherit);font-size:inherit;line-height:34px;text-align:inherit;text-overflow:ellipsis;white-space:nowrap;cursor:pointer;overflow:hidden;scroll-snap-align:center}:host .picker-item-empty,:host .picker-item[disabled]{cursor:default}:host .picker-item-empty,:host(:not([disabled])) .picker-item[disabled]{scroll-snap-align:none}:host([disabled]){overflow-y:hidden}:host .picker-item[disabled]{opacity:0.4}:host(.picker-column-active) .picker-item.picker-item-active{color:var(--ion-color-base)}@media (any-hover: hover){:host(:focus){outline:none;background:rgba(var(--ion-color-base-rgb), 0.2)}}";
const IonPickerColumnInternalIosStyle0 = pickerColumnInternalIosCss;
const pickerColumnInternalMdCss = ":host{-webkit-padding-start:16px;padding-inline-start:16px;-webkit-padding-end:16px;padding-inline-end:16px;padding-top:0px;padding-bottom:0px;height:200px;outline:none;font-size:22px;-webkit-scroll-snap-type:y mandatory;-ms-scroll-snap-type:y mandatory;scroll-snap-type:y mandatory;overflow-x:hidden;overflow-y:scroll;scrollbar-width:none;text-align:center}:host::-webkit-scrollbar{display:none}:host .picker-item{padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;display:block;width:100%;height:34px;border:0px;outline:none;background:transparent;color:inherit;font-family:var(--ion-font-family, inherit);font-size:inherit;line-height:34px;text-align:inherit;text-overflow:ellipsis;white-space:nowrap;cursor:pointer;overflow:hidden;scroll-snap-align:center}:host .picker-item-empty,:host .picker-item[disabled]{cursor:default}:host .picker-item-empty,:host(:not([disabled])) .picker-item[disabled]{scroll-snap-align:none}:host([disabled]){overflow-y:hidden}:host .picker-item[disabled]{opacity:0.4}:host(.picker-column-active) .picker-item.picker-item-active{color:var(--ion-color-base)}@media (any-hover: hover){:host(:focus){outline:none;background:rgba(var(--ion-color-base-rgb), 0.2)}}:host .picker-item-active{color:var(--ion-color-base)}";
const IonPickerColumnInternalMdStyle0 = pickerColumnInternalMdCss;
const PickerColumnInternal = class {
  constructor(hostRef) {
    (0,_index_a1a47f01_js__WEBPACK_IMPORTED_MODULE_1__.r)(this, hostRef);
    this.ionChange = (0,_index_a1a47f01_js__WEBPACK_IMPORTED_MODULE_1__.d)(this, "ionChange", 7);
    this.isScrolling = false;
    this.isColumnVisible = false;
    this.canExitInputMode = true;
    this.centerPickerItemInView = (target, smooth = true, canExitInputMode = true) => {
      const {
        el,
        isColumnVisible
      } = this;
      if (isColumnVisible) {
        // (Vertical offset from parent) - (three empty picker rows) + (half the height of the target to ensure the scroll triggers)
        const top = target.offsetTop - 3 * target.clientHeight + target.clientHeight / 2;
        if (el.scrollTop !== top) {
          /**
           * Setting this flag prevents input
           * mode from exiting in the picker column's
           * scroll callback. This is useful when the user manually
           * taps an item or types on the keyboard as both
           * of these can cause a scroll to occur.
           */
          this.canExitInputMode = canExitInputMode;
          el.scroll({
            top,
            left: 0,
            behavior: smooth ? 'smooth' : undefined
          });
        }
      }
    };
    this.setPickerItemActiveState = (item, isActive) => {
      if (isActive) {
        item.classList.add(PICKER_ITEM_ACTIVE_CLASS);
        item.part.add(PICKER_ITEM_ACTIVE_PART);
      } else {
        item.classList.remove(PICKER_ITEM_ACTIVE_CLASS);
        item.part.remove(PICKER_ITEM_ACTIVE_PART);
      }
    };
    /**
     * When ionInputModeChange is emitted, each column
     * needs to check if it is the one being made available
     * for text entry.
     */
    this.inputModeChange = ev => {
      if (!this.numericInput) {
        return;
      }
      const {
        useInputMode,
        inputModeColumn
      } = ev.detail;
      /**
       * If inputModeColumn is undefined then this means
       * all numericInput columns are being selected.
       */
      const isColumnActive = inputModeColumn === undefined || inputModeColumn === this.el;
      if (!useInputMode || !isColumnActive) {
        this.setInputModeActive(false);
        return;
      }
      this.setInputModeActive(true);
    };
    /**
     * Setting isActive will cause a re-render.
     * As a result, we do not want to cause the
     * re-render mid scroll as this will cause
     * the picker column to jump back to
     * whatever value was selected at the
     * start of the scroll interaction.
     */
    this.setInputModeActive = state => {
      if (this.isScrolling) {
        this.scrollEndCallback = () => {
          this.isActive = state;
        };
        return;
      }
      this.isActive = state;
    };
    /**
     * When the column scrolls, the component
     * needs to determine which item is centered
     * in the view and will emit an ionChange with
     * the item object.
     */
    this.initializeScrollListener = () => {
      /**
       * The haptics for the wheel picker are
       * an iOS-only feature. As a result, they should
       * be disabled on Android.
       */
      const enableHaptics = (0,_ionic_global_94f25d1b_js__WEBPACK_IMPORTED_MODULE_4__.a)('ios');
      const {
        el
      } = this;
      let timeout;
      let activeEl = this.activeItem;
      const scrollCallback = () => {
        (0,_helpers_be245865_js__WEBPACK_IMPORTED_MODULE_2__.r)(() => {
          if (timeout) {
            clearTimeout(timeout);
            timeout = undefined;
          }
          if (!this.isScrolling) {
            enableHaptics && (0,_haptic_554688a5_js__WEBPACK_IMPORTED_MODULE_3__.a)();
            this.isScrolling = true;
          }
          /**
           * Select item in the center of the column
           * which is the month/year that we want to select
           */
          const bbox = el.getBoundingClientRect();
          const centerX = bbox.x + bbox.width / 2;
          const centerY = bbox.y + bbox.height / 2;
          const activeElement = el.shadowRoot.elementFromPoint(centerX, centerY);
          if (activeEl !== null) {
            this.setPickerItemActiveState(activeEl, false);
          }
          if (activeElement === null || activeElement.disabled) {
            return;
          }
          /**
           * If we are selecting a new value,
           * we need to run haptics again.
           */
          if (activeElement !== activeEl) {
            enableHaptics && (0,_haptic_554688a5_js__WEBPACK_IMPORTED_MODULE_3__.b)();
            if (this.canExitInputMode) {
              /**
               * The native iOS wheel picker
               * only dismisses the keyboard
               * once the selected item has changed
               * as a result of a swipe
               * from the user. If `canExitInputMode` is
               * `false` then this means that the
               * scroll is happening as a result of
               * the `value` property programmatically changing
               * either by an application or by the user via the keyboard.
               */
              this.exitInputMode();
            }
          }
          activeEl = activeElement;
          this.setPickerItemActiveState(activeElement, true);
          timeout = setTimeout(() => {
            this.isScrolling = false;
            enableHaptics && (0,_haptic_554688a5_js__WEBPACK_IMPORTED_MODULE_3__.h)();
            /**
             * Certain tasks (such as those that
             * cause re-renders) should only be done
             * once scrolling has finished, otherwise
             * flickering may occur.
             */
            const {
              scrollEndCallback
            } = this;
            if (scrollEndCallback) {
              scrollEndCallback();
              this.scrollEndCallback = undefined;
            }
            /**
             * Reset this flag as the
             * next scroll interaction could
             * be a scroll from the user. In this
             * case, we should exit input mode.
             */
            this.canExitInputMode = true;
            const dataIndex = activeElement.getAttribute('data-index');
            /**
             * If no value it is
             * possible we hit one of the
             * empty padding columns.
             */
            if (dataIndex === null) {
              return;
            }
            const index = parseInt(dataIndex, 10);
            const selectedItem = this.items[index];
            if (selectedItem.value !== this.value) {
              this.setValue(selectedItem.value);
            }
          }, 250);
        });
      };
      /**
       * Wrap this in an raf so that the scroll callback
       * does not fire when component is initially shown.
       */
      (0,_helpers_be245865_js__WEBPACK_IMPORTED_MODULE_2__.r)(() => {
        el.addEventListener('scroll', scrollCallback);
        this.destroyScrollListener = () => {
          el.removeEventListener('scroll', scrollCallback);
        };
      });
    };
    /**
     * Tells the parent picker to
     * exit text entry mode. This is only called
     * when the selected item changes during scroll, so
     * we know that the user likely wants to scroll
     * instead of type.
     */
    this.exitInputMode = () => {
      const {
        parentEl
      } = this;
      if (parentEl == null) return;
      parentEl.exitInputMode();
      /**
       * setInputModeActive only takes
       * effect once scrolling stops to avoid
       * a component re-render while scrolling.
       * However, we want the visual active
       * indicator to go away immediately, so
       * we call classList.remove here.
       */
      this.el.classList.remove('picker-column-active');
    };
    this.isActive = false;
    this.disabled = false;
    this.items = [];
    this.value = undefined;
    this.color = 'primary';
    this.numericInput = false;
  }
  valueChange() {
    if (this.isColumnVisible) {
      /**
       * Only scroll the active item into view when the picker column
       * is actively visible to the user.
       */
      this.scrollActiveItemIntoView();
    }
  }
  /**
   * Only setup scroll listeners
   * when the picker is visible, otherwise
   * the container will have a scroll
   * height of 0px.
   */
  componentWillLoad() {
    const visibleCallback = entries => {
      /**
       * Browsers will sometimes group multiple IO events into a single callback.
       * As a result, we want to grab the last/most recent event in case there are multiple events.
       */
      const ev = entries[entries.length - 1];
      if (ev.isIntersecting) {
        const {
          activeItem,
          el
        } = this;
        this.isColumnVisible = true;
        /**
         * Because this initial call to scrollActiveItemIntoView has to fire before
         * the scroll listener is set up, we need to manage the active class manually.
         */
        const oldActive = (0,_helpers_be245865_js__WEBPACK_IMPORTED_MODULE_2__.g)(el).querySelector(`.${PICKER_ITEM_ACTIVE_CLASS}`);
        if (oldActive) {
          this.setPickerItemActiveState(oldActive, false);
        }
        this.scrollActiveItemIntoView();
        if (activeItem) {
          this.setPickerItemActiveState(activeItem, true);
        }
        this.initializeScrollListener();
      } else {
        this.isColumnVisible = false;
        if (this.destroyScrollListener) {
          this.destroyScrollListener();
          this.destroyScrollListener = undefined;
        }
      }
    };
    new IntersectionObserver(visibleCallback, {
      threshold: 0.001
    }).observe(this.el);
    const parentEl = this.parentEl = this.el.closest('ion-picker-internal');
    if (parentEl !== null) {
      // TODO(FW-2832): type
      parentEl.addEventListener('ionInputModeChange', ev => this.inputModeChange(ev));
    }
  }
  componentDidRender() {
    var _a;
    const {
      activeItem,
      items,
      isColumnVisible,
      value
    } = this;
    if (isColumnVisible) {
      if (activeItem) {
        this.scrollActiveItemIntoView();
      } else if (((_a = items[0]) === null || _a === void 0 ? void 0 : _a.value) !== value) {
        /**
         * If the picker column does not have an active item and the current value
         * does not match the first item in the picker column, that means
         * the value is out of bounds. In this case, we assign the value to the
         * first item to match the scroll position of the column.
         *
         */
        this.setValue(items[0].value);
      }
    }
  }
  /** @internal  */
  scrollActiveItemIntoView() {
    var _this = this;
    return (0,C_Users_acer_Desktop_angularprojects_veritus_Veritus_veritus_secretary_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__["default"])(function* () {
      const activeEl = _this.activeItem;
      if (activeEl) {
        _this.centerPickerItemInView(activeEl, false, false);
      }
    })();
  }
  /**
   * Sets the value prop and fires the ionChange event.
   * This is used when we need to fire ionChange from
   * user-generated events that cannot be caught with normal
   * input/change event listeners.
   * @internal
   */
  setValue(value) {
    var _this2 = this;
    return (0,C_Users_acer_Desktop_angularprojects_veritus_Veritus_veritus_secretary_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__["default"])(function* () {
      const {
        items
      } = _this2;
      _this2.value = value;
      const findItem = items.find(item => item.value === value && item.disabled !== true);
      if (findItem) {
        _this2.ionChange.emit(findItem);
      }
    })();
  }
  get activeItem() {
    // If the whole picker column is disabled, the current value should appear active
    // If the current value item is specifically disabled, it should not appear active
    const selector = `.picker-item[data-value="${this.value}"]${this.disabled ? '' : ':not([disabled])'}`;
    return (0,_helpers_be245865_js__WEBPACK_IMPORTED_MODULE_2__.g)(this.el).querySelector(selector);
  }
  render() {
    const {
      items,
      color,
      disabled: pickerDisabled,
      isActive,
      numericInput
    } = this;
    const mode = (0,_ionic_global_94f25d1b_js__WEBPACK_IMPORTED_MODULE_4__.b)(this);
    /**
     * exportparts is needed so ion-datetime can expose the parts
     * from two layers of shadow nesting. If this causes problems,
     * the attribute can be moved to datetime.tsx and set on every
     * instance of ion-picker-column-internal there instead.
     */
    return (0,_index_a1a47f01_js__WEBPACK_IMPORTED_MODULE_1__.h)(_index_a1a47f01_js__WEBPACK_IMPORTED_MODULE_1__.H, {
      key: '42a034f2533d30d19f96a121eb74d5f757e1c684',
      exportparts: `${PICKER_ITEM_PART}, ${PICKER_ITEM_ACTIVE_PART}`,
      disabled: pickerDisabled,
      tabindex: pickerDisabled ? null : 0,
      class: (0,_theme_01f3f29c_js__WEBPACK_IMPORTED_MODULE_5__.c)(color, {
        [mode]: true,
        ['picker-column-active']: isActive,
        ['picker-column-numeric-input']: numericInput
      })
    }, (0,_index_a1a47f01_js__WEBPACK_IMPORTED_MODULE_1__.h)("div", {
      key: '85efccb40c87d473c06026b8041d57b40d2369c3',
      class: "picker-item picker-item-empty",
      "aria-hidden": "true"
    }, "\u00A0"), (0,_index_a1a47f01_js__WEBPACK_IMPORTED_MODULE_1__.h)("div", {
      key: '9fae4dd6697f23acba18c218ba250ea77954b18d',
      class: "picker-item picker-item-empty",
      "aria-hidden": "true"
    }, "\u00A0"), (0,_index_a1a47f01_js__WEBPACK_IMPORTED_MODULE_1__.h)("div", {
      key: 'f117afeb204a4f6bb34a1cd0e1b786fa479d8b32',
      class: "picker-item picker-item-empty",
      "aria-hidden": "true"
    }, "\u00A0"), items.map((item, index) => {
      const isItemDisabled = pickerDisabled || item.disabled || false;
      return (0,_index_a1a47f01_js__WEBPACK_IMPORTED_MODULE_1__.h)("button", {
        tabindex: "-1",
        class: {
          'picker-item': true
        },
        "data-value": item.value,
        "data-index": index,
        onClick: ev => {
          this.centerPickerItemInView(ev.target, true);
        },
        disabled: isItemDisabled,
        part: PICKER_ITEM_PART
      }, item.text);
    }), (0,_index_a1a47f01_js__WEBPACK_IMPORTED_MODULE_1__.h)("div", {
      key: '28aa37f9ce90e88b9c3a5b2c399e3066e9f339e1',
      class: "picker-item picker-item-empty",
      "aria-hidden": "true"
    }, "\u00A0"), (0,_index_a1a47f01_js__WEBPACK_IMPORTED_MODULE_1__.h)("div", {
      key: 'ef4ae6bee2b17918f0c2aba9d5c720c1d95987e4',
      class: "picker-item picker-item-empty",
      "aria-hidden": "true"
    }, "\u00A0"), (0,_index_a1a47f01_js__WEBPACK_IMPORTED_MODULE_1__.h)("div", {
      key: '564967bc8e42a9018163850da3a967a933b3de7b',
      class: "picker-item picker-item-empty",
      "aria-hidden": "true"
    }, "\u00A0"));
  }
  get el() {
    return (0,_index_a1a47f01_js__WEBPACK_IMPORTED_MODULE_1__.f)(this);
  }
  static get watchers() {
    return {
      "value": ["valueChange"]
    };
  }
};
const PICKER_ITEM_ACTIVE_CLASS = 'picker-item-active';
const PICKER_ITEM_PART = 'wheel-item';
const PICKER_ITEM_ACTIVE_PART = 'active';
PickerColumnInternal.style = {
  ios: IonPickerColumnInternalIosStyle0,
  md: IonPickerColumnInternalMdStyle0
};


/***/ })

}]);
//# sourceMappingURL=node_modules_ionic_core_dist_esm_ion-picker-column-internal_entry_js.js.map