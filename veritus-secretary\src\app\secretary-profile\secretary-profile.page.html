<ion-header>
  <ion-toolbar class="veritus-toolbar">
    <ion-title class="veritus-text-white veritus-font-semibold">Profile</ion-title>
  </ion-toolbar>
</ion-header>

<ion-content class="profile-content veritus-gradient-bg">
  <div class="profile-container veritus-safe-area-top">
    
    <!-- Profile Header -->
    <div class="profile-header" *ngIf="secretary">
      <div class="avatar-section">
        <div class="avatar-circle">
          <img *ngIf="secretary.avatar" [src]="secretary.avatar" alt="Profile" class="avatar-image">
          <ion-icon *ngIf="!secretary.avatar" name="person" class="avatar-icon"></ion-icon>
        </div>
        <h2 class="profile-name veritus-text-xl veritus-font-bold veritus-text-white">
          {{ secretary.name }}
        </h2>
        <p class="profile-email veritus-text-sm veritus-text-gray">{{ secretary.email }}</p>
      </div>
    </div>

    <!-- Profile Stats -->
    <div class="stats-section" *ngIf="secretary">
      <div class="stat-card">
        <div class="stat-number veritus-text-lg veritus-font-bold veritus-text-white">
          {{ getLinkedLawyersCount() }}
        </div>
        <div class="stat-label veritus-text-xs veritus-text-gray">Linked Lawyers</div>
      </div>
      
      <div class="stat-card">
        <div class="stat-number veritus-text-lg veritus-font-bold veritus-text-white">
          {{ getPermissionCount() }}
        </div>
        <div class="stat-label veritus-text-xs veritus-text-gray">Permissions</div>
      </div>
      
      <div class="stat-card">
        <div class="stat-number veritus-text-lg veritus-font-bold veritus-text-white">
          Active
        </div>
        <div class="stat-label veritus-text-xs veritus-text-gray">Status</div>
      </div>
    </div>

    <!-- Profile Information -->
    <div class="info-section" *ngIf="secretary">
      <h3 class="section-title veritus-text-lg veritus-font-semibold veritus-text-white">
        Profile Information
      </h3>
      
      <div class="info-card" *ngIf="!isEditing">
        <div class="info-item">
          <label class="info-label veritus-text-sm veritus-text-gray">Full Name</label>
          <p class="info-value veritus-text-base veritus-text-white">{{ secretary.name }}</p>
        </div>
        
        <div class="info-item">
          <label class="info-label veritus-text-sm veritus-text-gray">Email</label>
          <p class="info-value veritus-text-base veritus-text-white">{{ secretary.email }}</p>
        </div>
        
        <div class="info-item">
          <label class="info-label veritus-text-sm veritus-text-gray">Phone</label>
          <p class="info-value veritus-text-base veritus-text-white">{{ secretary.phone || 'Not provided' }}</p>
        </div>
        
        <div class="info-item">
          <label class="info-label veritus-text-sm veritus-text-gray">Role</label>
          <p class="info-value veritus-text-base veritus-text-white">Secretary</p>
        </div>
        
        <button class="veritus-btn-secondary edit-btn" (click)="onEditProfile()">
          <ion-icon name="create-outline" class="btn-icon"></ion-icon>
          Edit Profile
        </button>
      </div>
      
      <!-- Edit Form -->
      <div class="edit-form" *ngIf="isEditing">
        <div class="form-item">
          <ion-label class="form-label veritus-text-sm veritus-text-white">Full Name</ion-label>
          <ion-input
            [(ngModel)]="editForm.name"
            placeholder="Enter your full name"
            class="form-input">
          </ion-input>
        </div>
        
        <div class="form-item">
          <ion-label class="form-label veritus-text-sm veritus-text-white">Phone</ion-label>
          <ion-input
            [(ngModel)]="editForm.phone"
            placeholder="Enter your phone number"
            class="form-input">
          </ion-input>
        </div>
        
        <div class="form-item">
          <ion-label class="form-label veritus-text-sm veritus-text-white">Avatar URL</ion-label>
          <ion-input
            [(ngModel)]="editForm.avatar"
            placeholder="Enter avatar image URL"
            class="form-input">
          </ion-input>
        </div>
        
        <div class="form-actions">
          <button class="veritus-btn-primary save-btn" (click)="onSaveProfile()">
            <ion-icon name="checkmark" class="btn-icon"></ion-icon>
            Save Changes
          </button>
          <button class="veritus-btn-secondary cancel-btn" (click)="onCancelEdit()">
            <ion-icon name="close" class="btn-icon"></ion-icon>
            Cancel
          </button>
        </div>
      </div>
    </div>

    <!-- Quick Actions -->
    <div class="actions-section">
      <h3 class="section-title veritus-text-lg veritus-font-semibold veritus-text-white">
        Quick Actions
      </h3>
      
      <div class="action-cards">
        <button class="action-card" (click)="onManageLinkedLawyers()">
          <ion-icon name="people" class="action-icon"></ion-icon>
          <div class="action-content">
            <h4 class="action-title veritus-text-base veritus-font-semibold veritus-text-white">
              Manage Lawyers
            </h4>
            <p class="action-description veritus-text-sm veritus-text-gray">
              View and manage linked lawyers
            </p>
          </div>
          <ion-icon name="chevron-forward" class="chevron-icon"></ion-icon>
        </button>
        
        <button class="action-card" (click)="onViewAuditLog()">
          <ion-icon name="list" class="action-icon"></ion-icon>
          <div class="action-content">
            <h4 class="action-title veritus-text-base veritus-font-semibold veritus-text-white">
              Activity Log
            </h4>
            <p class="action-description veritus-text-sm veritus-text-gray">
              View your activity history
            </p>
          </div>
          <ion-icon name="chevron-forward" class="chevron-icon"></ion-icon>
        </button>
      </div>
    </div>

    <!-- Logout Section -->
    <div class="logout-section">
      <button class="logout-btn" (click)="onLogout()">
        <ion-icon name="log-out-outline" class="logout-icon"></ion-icon>
        <span class="logout-text veritus-text-base">Logout</span>
      </button>
    </div>

  </div>
</ion-content>
