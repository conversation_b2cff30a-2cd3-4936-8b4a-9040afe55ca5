{"version": 3, "file": "src_app_secretary-files_secretary-files_module_ts.js", "mappings": ";;;;;;;;;;;;;;;;AACuD;AAEK;;;AAE5D,MAAME,MAAM,GAAW,CACrB;EACEC,IAAI,EAAE,EAAE;EACRC,SAAS,EAAEH,qEAAkBA;CAC9B,CACF;AAMK,MAAOI,+BAA+B;;;uBAA/BA,+BAA+B;IAAA;EAAA;;;YAA/BA;IAA+B;EAAA;;;gBAHhCL,yDAAY,CAACM,QAAQ,CAACJ,MAAM,CAAC,EAC7BF,yDAAY;IAAA;EAAA;;;sHAEXK,+BAA+B;IAAAE,OAAA,GAAAC,yDAAA;IAAAC,OAAA,GAFhCT,yDAAY;EAAA;AAAA,K;;;;;;;;;;;;;;;;;;;;ACbuB;AACF;AACA;AAEsC;AACvB;;AAWtD,MAAOa,wBAAwB;;;uBAAxBA,wBAAwB;IAAA;EAAA;;;YAAxBA;IAAwB;EAAA;;;gBAPjCH,yDAAY,EACZC,uDAAW,EACXC,uDAAW,EACXP,4FAA+B;IAAA;EAAA;;;sHAItBQ,wBAAwB;IAAAC,YAAA,GAFpBb,qEAAkB;IAAAM,OAAA,GAL/BG,yDAAY,EACZC,uDAAW,EACXC,uDAAW,EACXP,4FAA+B;EAAA;AAAA,K;;;;;;;;;;;;;;;;;;;;;;;;;;;;IEOvBU,4DAAA,4BAA6E;IAC3EA,oDAAA,GACF;IAAAA,0DAAA,EAAoB;;;;IAFoCA,wDAAA,UAAAK,SAAA,CAAAC,GAAA,CAAoB;IAC1EN,uDAAA,EACF;IADEA,gEAAA,MAAAK,SAAA,CAAAI,IAAA,MACF;;;;;IAyDAT,4DAAA,YAA6E;IAC3EA,oDAAA,GACF;IAAAA,0DAAA,EAAI;;;;IADFA,uDAAA,EACF;IADEA,gEAAA,MAAAU,OAAA,CAAAC,QAAA,MACF;;;;;;IAbFX,4DADF,cAA0D,cACjC;IACrBA,uDAAA,mBAA2F;IAC7FA,0DAAA,EAAM;IAGJA,4DADF,cAA0B,aACyD;IAC/EA,oDAAA,GACF;IAAAA,0DAAA,EAAK;IACLA,4DAAA,YAAuD;IACrDA,oDAAA,GACF;IAAAA,0DAAA,EAAI;IACJA,wDAAA,IAAAc,4CAAA,gBAA6E;IAG7Ed,4DAAA,YAAuD;IACrDA,oDAAA,IACF;;IACFA,0DADE,EAAI,EACA;IAGJA,4DADF,eAA0B,kBAC+C;IAA/BA,wDAAA,mBAAAgB,kEAAA;MAAA,MAAAN,OAAA,GAAAV,2DAAA,CAAAkB,GAAA,EAAAC,SAAA;MAAA,MAAAC,MAAA,GAAApB,2DAAA;MAAA,OAAAA,yDAAA,CAASoB,MAAA,CAAAG,cAAA,CAAAb,OAAA,CAAoB;IAAA,EAAC;IACpEV,uDAAA,oBAA6C;IAC/CA,0DAAA,EAAS;IACTA,4DAAA,kBAAmE;IAA7BA,wDAAA,mBAAAwB,kEAAA;MAAA,MAAAd,OAAA,GAAAV,2DAAA,CAAAkB,GAAA,EAAAC,SAAA;MAAA,MAAAC,MAAA,GAAApB,2DAAA;MAAA,OAAAA,yDAAA,CAASoB,MAAA,CAAAK,YAAA,CAAAf,OAAA,CAAkB;IAAA,EAAC;IAChEV,uDAAA,oBAA0C;IAGhDA,0DAFI,EAAS,EACL,EACF;;;;;IA1BQA,uDAAA,GAA+B;IAACA,wDAAhC,SAAAoB,MAAA,CAAAM,WAAA,CAAAhB,OAAA,CAAAiB,IAAA,EAA+B,UAAAP,MAAA,CAAAQ,gBAAA,CAAAlB,OAAA,CAAAiB,IAAA,EAAsC;IAK7E3B,uDAAA,GACF;IADEA,gEAAA,MAAAU,OAAA,CAAAD,IAAA,MACF;IAEET,uDAAA,GACF;IADEA,gEAAA,MAAAoB,MAAA,CAAAU,cAAA,CAAApB,OAAA,CAAAqB,IAAA,eAAArB,OAAA,CAAAsB,UAAA,MACF;IACwDhC,uDAAA,EAAmB;IAAnBA,wDAAA,SAAAU,OAAA,CAAAC,QAAA,CAAmB;IAIzEX,uDAAA,GACF;IADEA,gEAAA,eAAAA,yDAAA,QAAAU,OAAA,CAAAwB,UAAA,gBACF;;;;;IAlBNlC,4DAAA,cAAyD;IACvDA,wDAAA,IAAAmC,wCAAA,oBAA0D;IA6B5DnC,0DAAA,EAAM;;;;IA7BoCA,uDAAA,EAAgB;IAAhBA,wDAAA,YAAAoB,MAAA,CAAAgB,aAAA,CAAgB;;;;;;IAgC1DpC,4DAAA,cAA4D;IAC1DA,uDAAA,mBAA8D;IAC9DA,4DAAA,aAAiF;IAAAA,oDAAA,qBAAc;IAAAA,0DAAA,EAAK;IACpGA,4DAAA,YAA+D;IAC7DA,oDAAA,6CACF;IAAAA,0DAAA,EAAI;IACJA,4DAAA,iBAA+D;IAAzBA,wDAAA,mBAAAqC,2DAAA;MAAArC,2DAAA,CAAAsC,GAAA;MAAA,MAAAlB,MAAA,GAAApB,2DAAA;MAAA,OAAAA,yDAAA,CAASoB,MAAA,CAAAmB,YAAA,EAAc;IAAA,EAAC;IAC5DvC,oDAAA,0BACF;IACFA,0DADE,EAAS,EACL;;;ADvFN,MAAOd,kBAAkB;EAQ7BsD,YACUC,eAAgC;IAAhC,KAAAA,eAAe,GAAfA,eAAe;IARzB,KAAAC,aAAa,GAAoB,EAAE;IACnC,KAAAC,cAAc,GAAW,KAAK;IAC9B,KAAAC,KAAK,GAAe,EAAE;IACtB,KAAAR,aAAa,GAAe,EAAE;IAC9B,KAAAS,UAAU,GAAW,EAAE;IACvB,KAAAC,YAAY,GAAW,KAAK;EAIxB;EAEJC,QAAQA,CAAA;IACN,IAAI,CAACC,iBAAiB,EAAE;IACxB,IAAI,CAACC,SAAS,EAAE;EAClB;EAEMD,iBAAiBA,CAAA;IAAA,IAAAE,KAAA;IAAA,OAAAC,6KAAA;MACrB,MAAMC,WAAW,GAAGF,KAAI,CAACT,eAAe,CAACY,cAAc,EAAE;MACzD,IAAID,WAAW,EAAE;QACfF,KAAI,CAACR,aAAa,SAASQ,KAAI,CAACT,eAAe,CAACa,yBAAyB,CAACF,WAAW,CAAC9C,GAAG,CAAC;;IAC3F;EACH;EAEM2C,SAASA,CAAA;IAAA,IAAAM,MAAA;IAAA,OAAAJ,6KAAA;MACb;MACA,MAAMK,SAAS,GAAe,CAC5B;QACEC,EAAE,EAAE,GAAG;QACPhD,IAAI,EAAE,uBAAuB;QAC7BkB,IAAI,EAAE,KAAK;QACXI,IAAI,EAAE,OAAO;QACb2B,UAAU,EAAE,WAAW;QACvBxB,UAAU,EAAE,IAAIyB,IAAI,CAAC,YAAY,CAAC;QAClCC,QAAQ,EAAE,SAAS;QACnB5B,UAAU,EAAE,aAAa;QACzB6B,MAAM,EAAE,OAAO;QACflD,QAAQ,EAAE;OACX,EACD;QACE8C,EAAE,EAAE,GAAG;QACPhD,IAAI,EAAE,2BAA2B;QACjCkB,IAAI,EAAE,MAAM;QACZI,IAAI,EAAE,OAAO;QACb2B,UAAU,EAAE,WAAW;QACvBxB,UAAU,EAAE,IAAIyB,IAAI,CAAC,YAAY,CAAC;QAClCC,QAAQ,EAAE,SAAS;QACnB5B,UAAU,EAAE,eAAe;QAC3B6B,MAAM,EAAE,OAAO;QACflD,QAAQ,EAAE;OACX,EACD;QACE8C,EAAE,EAAE,GAAG;QACPhD,IAAI,EAAE,wBAAwB;QAC9BkB,IAAI,EAAE,KAAK;QACXI,IAAI,EAAE,OAAO;QACb2B,UAAU,EAAE,WAAW;QACvBxB,UAAU,EAAE,IAAIyB,IAAI,CAAC,YAAY,CAAC;QAClCC,QAAQ,EAAE,SAAS;QACnB5B,UAAU,EAAE,aAAa;QACzB6B,MAAM,EAAE,OAAO;QACflD,QAAQ,EAAE;OACX,CACF;MAED4C,MAAI,CAACX,KAAK,GAAGY,SAAS,CAACM,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAC/B,IAAIL,IAAI,CAACK,CAAC,CAAC9B,UAAU,CAAC,CAAC+B,OAAO,EAAE,GAAG,IAAIN,IAAI,CAACI,CAAC,CAAC7B,UAAU,CAAC,CAAC+B,OAAO,EAAE,CACpE;MAEDV,MAAI,CAACW,WAAW,EAAE;IAAC;EACrB;EAEAA,WAAWA,CAAA;IACT,IAAI,CAAC9B,aAAa,GAAG,IAAI,CAACQ,KAAK,CAACuB,MAAM,CAACC,IAAI,IAAG;MAC5C,MAAMC,aAAa,GAAG,IAAI,CAAC1B,cAAc,KAAK,KAAK,IAAIyB,IAAI,CAACR,QAAQ,KAAK,IAAI,CAACjB,cAAc;MAC5F,MAAM2B,WAAW,GAAG,IAAI,CAACxB,YAAY,KAAK,KAAK,IAAIsB,IAAI,CAACzC,IAAI,KAAK,IAAI,CAACmB,YAAY;MAClF,MAAMyB,aAAa,GAAG,CAAC,IAAI,CAAC1B,UAAU,IACpCuB,IAAI,CAAC3D,IAAI,CAAC+D,WAAW,EAAE,CAACC,QAAQ,CAAC,IAAI,CAAC5B,UAAU,CAAC2B,WAAW,EAAE,CAAC,IAC9DJ,IAAI,CAACzD,QAAQ,IAAIyD,IAAI,CAACzD,QAAQ,CAAC6D,WAAW,EAAE,CAACC,QAAQ,CAAC,IAAI,CAAC5B,UAAU,CAAC2B,WAAW,EAAE,CAAE;MAExF,OAAOH,aAAa,IAAIC,WAAW,IAAIC,aAAa;IACtD,CAAC,CAAC;EACJ;EAEAG,cAAcA,CAAA;IACZ,IAAI,CAACR,WAAW,EAAE;EACpB;EAEAS,YAAYA,CAAA;IACV,IAAI,CAACT,WAAW,EAAE;EACpB;EAEAU,cAAcA,CAAA;IACZ,IAAI,CAACV,WAAW,EAAE;EACpB;EAEM3B,YAAYA,CAAA;IAAA,IAAAsC,MAAA;IAAA,OAAA1B,6KAAA;MAChB,IAAI0B,MAAI,CAACnC,aAAa,CAACoC,MAAM,KAAK,CAAC,EAAE;QACnCC,KAAK,CAAC,iEAAiE,CAAC;QACxE;;MAGF;MACA,MAAMC,QAAQ,GAAGC,MAAM,CAAC,mCAAmC,CAAC;MAC5D,MAAMjD,UAAU,GAAGiD,MAAM,CAAC,oBAAoB,EAAEJ,MAAI,CAACnC,aAAa,CAAC,CAAC,CAAC,EAAEjC,IAAI,IAAI,EAAE,CAAC;MAClF,MAAME,QAAQ,GAAGsE,MAAM,CAAC,6BAA6B,CAAC;MAEtD,IAAID,QAAQ,IAAIhD,UAAU,EAAE;QAC1B,MAAMkD,MAAM,GAAGL,MAAI,CAACnC,aAAa,CAACyC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC3E,IAAI,CAAC+D,WAAW,EAAE,CAACC,QAAQ,CAACzC,UAAU,CAACwC,WAAW,EAAE,CAAC,CAAC;QAEpG,IAAI,CAACU,MAAM,EAAE;UACXH,KAAK,CAAC,qDAAqD,CAAC;UAC5D;;QAGF,MAAMM,aAAa,GAAGL,QAAQ,CAACM,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,EAAE,EAAEf,WAAW,EAAE,IAAI,SAAS;QAE3E,MAAMgB,OAAO,GAAa;UACxB/B,EAAE,EAAEE,IAAI,CAAC8B,GAAG,EAAE,CAACC,QAAQ,EAAE;UACzBjF,IAAI,EAAEuE,QAAQ;UACdrD,IAAI,EAAE0D,aAAa;UACnBtD,IAAI,EAAE4D,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,EAAE,GAAG,OAAO,CAAC,GAAG,MAAM;UAClDnC,UAAU,EAAE,WAAW;UACvBxB,UAAU,EAAE,IAAIyB,IAAI,EAAE;UACtBC,QAAQ,EAAEsB,MAAM,CAAC5E,GAAG;UACpB0B,UAAU,EAAEkD,MAAM,CAACzE,IAAI;UACvBE,QAAQ,EAAEA,QAAQ,IAAImF;SACvB;QAEDjB,MAAI,CAACjC,KAAK,CAACmD,OAAO,CAACP,OAAO,CAAC;QAC3BX,MAAI,CAACX,WAAW,EAAE;QAClBa,KAAK,CAAC,6BAA6B,CAAC;;IACrC;EACH;EAEMxD,cAAcA,CAAC6C,IAAc;IAAA,OAAAjB,6KAAA;MACjC4B,KAAK,CAAC,eAAeX,IAAI,CAAC3D,IAAI,KAAK,CAAC;MACpC;IAAA;EACF;EAEMgB,YAAYA,CAAC2C,IAAc;IAAA,IAAA4B,MAAA;IAAA,OAAA7C,6KAAA;MAC/B,MAAM8C,SAAS,GAAGC,OAAO,CAAC,mCAAmC9B,IAAI,CAAC3D,IAAI,GAAG,CAAC;MAE1E,IAAIwF,SAAS,EAAE;QACb,MAAME,KAAK,GAAGH,MAAI,CAACpD,KAAK,CAACwD,SAAS,CAACC,CAAC,IAAIA,CAAC,CAAC5C,EAAE,KAAKW,IAAI,CAACX,EAAE,CAAC;QACzD,IAAI0C,KAAK,KAAK,CAAC,CAAC,EAAE;UAChBH,MAAI,CAACpD,KAAK,CAAC0D,MAAM,CAACH,KAAK,EAAE,CAAC,CAAC;UAC3BH,MAAI,CAAC9B,WAAW,EAAE;UAClBa,KAAK,CAAC,4BAA4B,CAAC;;;IAEtC;EACH;EAEAjD,cAAcA,CAACyE,KAAa;IAC1B,IAAIA,KAAK,KAAK,CAAC,EAAE,OAAO,SAAS;IAEjC,MAAMC,CAAC,GAAG,IAAI;IACd,MAAMC,KAAK,GAAG,CAAC,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IACzC,MAAMC,CAAC,GAAGf,IAAI,CAACC,KAAK,CAACD,IAAI,CAACgB,GAAG,CAACJ,KAAK,CAAC,GAAGZ,IAAI,CAACgB,GAAG,CAACH,CAAC,CAAC,CAAC;IAEnD,OAAOI,UAAU,CAAC,CAACL,KAAK,GAAGZ,IAAI,CAACkB,GAAG,CAACL,CAAC,EAAEE,CAAC,CAAC,EAAEI,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,GAAGL,KAAK,CAACC,CAAC,CAAC;EACzE;EAEAhF,WAAWA,CAACC,IAAY;IACtB,QAAQA,IAAI,CAAC6C,WAAW,EAAE;MACxB,KAAK,KAAK;QAAE,OAAO,eAAe;MAClC,KAAK,KAAK;MACV,KAAK,MAAM;QAAE,OAAO,UAAU;MAC9B,KAAK,KAAK;MACV,KAAK,MAAM;QAAE,OAAO,MAAM;MAC1B,KAAK,KAAK;MACV,KAAK,MAAM;MACX,KAAK,KAAK;MACV,KAAK,KAAK;QAAE,OAAO,OAAO;MAC1B,KAAK,KAAK;MACV,KAAK,KAAK;QAAE,OAAO,SAAS;MAC5B;QAAS,OAAO,kBAAkB;;EAEtC;EAEA5C,gBAAgBA,CAACD,IAAY;IAC3B,QAAQA,IAAI,CAAC6C,WAAW,EAAE;MACxB,KAAK,KAAK;QAAE,OAAO,QAAQ;MAC3B,KAAK,KAAK;MACV,KAAK,MAAM;QAAE,OAAO,SAAS;MAC7B,KAAK,KAAK;MACV,KAAK,MAAM;QAAE,OAAO,SAAS;MAC7B,KAAK,KAAK;MACV,KAAK,MAAM;MACX,KAAK,KAAK;MACV,KAAK,KAAK;QAAE,OAAO,SAAS;MAC5B,KAAK,KAAK;MACV,KAAK,KAAK;QAAE,OAAO,MAAM;MACzB;QAAS,OAAO,QAAQ;;EAE5B;EAEAuC,aAAaA,CAAA;IACX,OAAO,IAAI,CAACnE,KAAK,CAACkC,MAAM;EAC1B;EAEAkC,YAAYA,CAAA;IACV,MAAMC,UAAU,GAAG,IAAI,CAACrE,KAAK,CAACsE,MAAM,CAAC,CAACC,GAAG,EAAE/C,IAAI,KAAK+C,GAAG,GAAG/C,IAAI,CAACrC,IAAI,EAAE,CAAC,CAAC;IACvE,OAAO,IAAI,CAACD,cAAc,CAACmF,UAAU,CAAC;EACxC;;;uBA5MW/H,kBAAkB,EAAAc,+DAAA,CAAAP,uEAAA;IAAA;EAAA;;;YAAlBP,kBAAkB;MAAAoI,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,4BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCnB3B5H,4DAFJ,iBAAY,qBAC2B,mBACyB;UAAAA,oDAAA,sBAAe;UAE/EA,0DAF+E,EAAY,EAC3E,EACH;UASHA,4DAPV,qBAAuD,aACF,aAGpB,aACH,aACG,mBAC4C;UAAAA,oDAAA,cAAM;UAAAA,0DAAA,EAAY;UACrFA,4DAAA,qBAIwB;UAHtBA,8DAAA,2BAAA+H,iEAAAC,MAAA;YAAAhI,gEAAA,CAAA6H,GAAA,CAAAlF,cAAA,EAAAqF,MAAA,MAAAH,GAAA,CAAAlF,cAAA,GAAAqF,MAAA;YAAA,OAAAA,MAAA;UAAA,EAA4B;UAC5BhI,wDAAA,uBAAAkI,6DAAA;YAAA,OAAaL,GAAA,CAAAnD,cAAA,EAAgB;UAAA,EAAC;UAG9B1E,4DAAA,4BAA+B;UAAAA,oDAAA,mBAAW;UAAAA,0DAAA,EAAoB;UAC9DA,wDAAA,KAAAmI,gDAAA,gCAA6E;UAIjFnI,0DADE,EAAa,EACT;UAGJA,4DADF,cAAyB,oBAC4C;UAAAA,oDAAA,YAAI;UAAAA,0DAAA,EAAY;UACnFA,4DAAA,sBAIsB;UAHpBA,8DAAA,2BAAAoI,iEAAAJ,MAAA;YAAAhI,gEAAA,CAAA6H,GAAA,CAAA/E,YAAA,EAAAkF,MAAA,MAAAH,GAAA,CAAA/E,YAAA,GAAAkF,MAAA;YAAA,OAAAA,MAAA;UAAA,EAA0B;UAC1BhI,wDAAA,uBAAAqI,6DAAA;YAAA,OAAaR,GAAA,CAAAlD,YAAA,EAAc;UAAA,EAAC;UAG5B3E,4DAAA,4BAA+B;UAAAA,oDAAA,iBAAS;UAAAA,0DAAA,EAAoB;UAC5DA,4DAAA,6BAA+B;UAAAA,oDAAA,WAAG;UAAAA,0DAAA,EAAoB;UACtDA,4DAAA,6BAAgC;UAAAA,oDAAA,YAAI;UAAAA,0DAAA,EAAoB;UACxDA,4DAAA,6BAAgC;UAAAA,oDAAA,aAAK;UAAAA,0DAAA,EAAoB;UACzDA,4DAAA,6BAA+B;UAAAA,oDAAA,eAAO;UAG5CA,0DAH4C,EAAoB,EAC/C,EACT,EACF;UAGJA,4DADF,eAA4B,yBAKC;UAHzBA,8DAAA,2BAAAsI,oEAAAN,MAAA;YAAAhI,gEAAA,CAAA6H,GAAA,CAAAhF,UAAA,EAAAmF,MAAA,MAAAH,GAAA,CAAAhF,UAAA,GAAAmF,MAAA;YAAA,OAAAA,MAAA;UAAA,EAAwB;UACxBhI,wDAAA,sBAAAuI,+DAAA;YAAA,OAAYV,GAAA,CAAAjD,cAAA,EAAgB;UAAA,EAAC;UAKnC5E,0DAFI,EAAgB,EACZ,EACF;UAIJA,4DADF,eAA4B,kBAC8C;UAAzBA,wDAAA,mBAAAwI,qDAAA;YAAA,OAASX,GAAA,CAAAtF,YAAA,EAAc;UAAA,EAAC;UACrEvC,uDAAA,oBAA0D;UAC1DA,oDAAA,qBACF;UACFA,0DADE,EAAS,EACL;UAIJA,4DADF,eAA2B,cAC0D;UACjFA,oDAAA,IACF;UAAAA,0DAAA,EAAK;UAmCLA,wDAjCA,KAAAyI,kCAAA,kBAAyD,KAAAC,kCAAA,kBAiCG;UAU9D1I,0DAAA,EAAM;UAKFA,4DAFJ,eAA2B,eACF,eACyD;UAC5EA,oDAAA,IACF;UAAAA,0DAAA,EAAM;UACNA,4DAAA,eAA0D;UAAAA,oDAAA,mBAAW;UACvEA,0DADuE,EAAM,EACvE;UAGJA,4DADF,eAAuB,eACyD;UAC5EA,oDAAA,IACF;UAAAA,0DAAA,EAAM;UACNA,4DAAA,eAA0D;UAAAA,oDAAA,sBAAc;UAC1EA,0DAD0E,EAAM,EAC1E;UAGJA,4DADF,eAAuB,eACyD;UAC5EA,oDAAA,IACF;UAAAA,0DAAA,EAAM;UACNA,4DAAA,eAA0D;UAAAA,oDAAA,kBAAU;UAK5EA,0DAL4E,EAAM,EACtE,EACF,EAEF,EACM;;;UAzHFA,uDAAA,IAA4B;UAA5BA,8DAAA,YAAA6H,GAAA,CAAAlF,cAAA,CAA4B;UAKU3C,uDAAA,GAAgB;UAAhBA,wDAAA,YAAA6H,GAAA,CAAAnF,aAAA,CAAgB;UAStD1C,uDAAA,GAA0B;UAA1BA,8DAAA,YAAA6H,GAAA,CAAA/E,YAAA,CAA0B;UAe5B9C,uDAAA,IAAwB;UAAxBA,8DAAA,YAAA6H,GAAA,CAAAhF,UAAA,CAAwB;UAmB1B7C,uDAAA,GACF;UADEA,gEAAA,aAAA6H,GAAA,CAAAzF,aAAA,CAAA0C,MAAA,OACF;UAEyB9E,uDAAA,EAA8B;UAA9BA,wDAAA,SAAA6H,GAAA,CAAAzF,aAAA,CAAA0C,MAAA,KAA8B;UAiC7B9E,uDAAA,EAAgC;UAAhCA,wDAAA,SAAA6H,GAAA,CAAAzF,aAAA,CAAA0C,MAAA,OAAgC;UAgBtD9E,uDAAA,GACF;UADEA,gEAAA,MAAA6H,GAAA,CAAAd,aAAA,QACF;UAME/G,uDAAA,GACF;UADEA,gEAAA,MAAA6H,GAAA,CAAAnF,aAAA,CAAAoC,MAAA,MACF;UAME9E,uDAAA,GACF;UADEA,gEAAA,MAAA6H,GAAA,CAAAb,YAAA,QACF", "sources": ["./src/app/secretary-files/secretary-files-routing.module.ts", "./src/app/secretary-files/secretary-files.module.ts", "./src/app/secretary-files/secretary-files.page.ts", "./src/app/secretary-files/secretary-files.page.html"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { Routes, RouterModule } from '@angular/router';\r\n\r\nimport { SecretaryFilesPage } from './secretary-files.page';\r\n\r\nconst routes: Routes = [\r\n  {\r\n    path: '',\r\n    component: SecretaryFilesPage\r\n  }\r\n];\r\n\r\n@NgModule({\r\n  imports: [RouterModule.forChild(routes)],\r\n  exports: [RouterModule],\r\n})\r\nexport class SecretaryFilesPageRoutingModule {}\r\n", "import { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { FormsModule } from '@angular/forms';\r\nimport { IonicModule } from '@ionic/angular';\r\n\r\nimport { SecretaryFilesPageRoutingModule } from './secretary-files-routing.module';\r\nimport { SecretaryFilesPage } from './secretary-files.page';\r\n\r\n@NgModule({\r\n  imports: [\r\n    CommonModule,\r\n    FormsModule,\r\n    IonicModule,\r\n    SecretaryFilesPageRoutingModule\r\n  ],\r\n  declarations: [SecretaryFilesPage]\r\n})\r\nexport class SecretaryFilesPageModule {}\r\n", "import { Component, OnInit } from '@angular/core';\r\nimport { FirebaseService, LawyerProfile } from '../services/firebase.service';\r\n\r\ninterface FileItem {\r\n  id: string;\r\n  name: string;\r\n  type: string;\r\n  size: number;\r\n  uploadedBy: string;\r\n  uploadedAt: Date;\r\n  lawyerId: string;\r\n  lawyerName: string;\r\n  caseId?: string;\r\n  caseName?: string;\r\n}\r\n\r\n@Component({\r\n  selector: 'app-secretary-files',\r\n  templateUrl: './secretary-files.page.html',\r\n  styleUrls: ['./secretary-files.page.scss'],\r\n})\r\nexport class SecretaryFilesPage implements OnInit {\r\n  linkedLawyers: LawyerProfile[] = [];\r\n  selectedLawyer: string = 'all';\r\n  files: FileItem[] = [];\r\n  filteredFiles: FileItem[] = [];\r\n  searchTerm: string = '';\r\n  selectedType: string = 'all';\r\n\r\n  constructor(\r\n    private firebaseService: FirebaseService\r\n  ) { }\r\n\r\n  ngOnInit() {\r\n    this.loadLinkedLawyers();\r\n    this.loadFiles();\r\n  }\r\n\r\n  async loadLinkedLawyers() {\r\n    const currentUser = this.firebaseService.getCurrentUser();\r\n    if (currentUser) {\r\n      this.linkedLawyers = await this.firebaseService.getSecretaryLinkedLawyers(currentUser.uid);\r\n    }\r\n  }\r\n\r\n  async loadFiles() {\r\n    // Mock files for now since we don't have the full file service\r\n    const mockFiles: FileItem[] = [\r\n      {\r\n        id: '1',\r\n        name: 'Contract_ABC_Corp.pdf',\r\n        type: 'pdf',\r\n        size: 2048576, // 2MB\r\n        uploadedBy: 'Secretary',\r\n        uploadedAt: new Date('2024-01-20'),\r\n        lawyerId: 'lawyer1',\r\n        lawyerName: 'Atty. Smith',\r\n        caseId: 'case1',\r\n        caseName: 'Contract Dispute - ABC Corp'\r\n      },\r\n      {\r\n        id: '2',\r\n        name: 'Employment_Agreement.docx',\r\n        type: 'docx',\r\n        size: 1024000, // 1MB\r\n        uploadedBy: 'Secretary',\r\n        uploadedAt: new Date('2024-01-18'),\r\n        lawyerId: 'lawyer2',\r\n        lawyerName: 'Atty. Johnson',\r\n        caseId: 'case2',\r\n        caseName: 'Employment Case - John Doe'\r\n      },\r\n      {\r\n        id: '3',\r\n        name: 'Property_Documents.zip',\r\n        type: 'zip',\r\n        size: 5242880, // 5MB\r\n        uploadedBy: 'Secretary',\r\n        uploadedAt: new Date('2024-01-15'),\r\n        lawyerId: 'lawyer1',\r\n        lawyerName: 'Atty. Smith',\r\n        caseId: 'case3',\r\n        caseName: 'Property Settlement'\r\n      }\r\n    ];\r\n    \r\n    this.files = mockFiles.sort((a, b) => \r\n      new Date(b.uploadedAt).getTime() - new Date(a.uploadedAt).getTime()\r\n    );\r\n    \r\n    this.filterFiles();\r\n  }\r\n\r\n  filterFiles() {\r\n    this.filteredFiles = this.files.filter(file => {\r\n      const matchesLawyer = this.selectedLawyer === 'all' || file.lawyerId === this.selectedLawyer;\r\n      const matchesType = this.selectedType === 'all' || file.type === this.selectedType;\r\n      const matchesSearch = !this.searchTerm || \r\n        file.name.toLowerCase().includes(this.searchTerm.toLowerCase()) ||\r\n        (file.caseName && file.caseName.toLowerCase().includes(this.searchTerm.toLowerCase()));\r\n      \r\n      return matchesLawyer && matchesType && matchesSearch;\r\n    });\r\n  }\r\n\r\n  onLawyerChange() {\r\n    this.filterFiles();\r\n  }\r\n\r\n  onTypeChange() {\r\n    this.filterFiles();\r\n  }\r\n\r\n  onSearchChange() {\r\n    this.filterFiles();\r\n  }\r\n\r\n  async onUploadFile() {\r\n    if (this.linkedLawyers.length === 0) {\r\n      alert('You need to be linked with at least one lawyer to upload files.');\r\n      return;\r\n    }\r\n\r\n    // For now, just simulate file upload\r\n    const fileName = prompt('Enter file name (with extension):');\r\n    const lawyerName = prompt('Enter lawyer name:', this.linkedLawyers[0]?.name || '');\r\n    const caseName = prompt('Enter case name (optional):');\r\n    \r\n    if (fileName && lawyerName) {\r\n      const lawyer = this.linkedLawyers.find(l => l.name.toLowerCase().includes(lawyerName.toLowerCase()));\r\n      \r\n      if (!lawyer) {\r\n        alert('Lawyer not found. Please enter a valid lawyer name.');\r\n        return;\r\n      }\r\n\r\n      const fileExtension = fileName.split('.').pop()?.toLowerCase() || 'unknown';\r\n      \r\n      const newFile: FileItem = {\r\n        id: Date.now().toString(),\r\n        name: fileName,\r\n        type: fileExtension,\r\n        size: Math.floor(Math.random() * 5000000) + 100000, // Random size between 100KB and 5MB\r\n        uploadedBy: 'Secretary',\r\n        uploadedAt: new Date(),\r\n        lawyerId: lawyer.uid,\r\n        lawyerName: lawyer.name,\r\n        caseName: caseName || undefined\r\n      };\r\n\r\n      this.files.unshift(newFile);\r\n      this.filterFiles();\r\n      alert('File uploaded successfully!');\r\n    }\r\n  }\r\n\r\n  async onDownloadFile(file: FileItem) {\r\n    alert(`Downloading ${file.name}...`);\r\n    // In a real implementation, this would trigger the actual download\r\n  }\r\n\r\n  async onDeleteFile(file: FileItem) {\r\n    const confirmed = confirm(`Are you sure you want to delete ${file.name}?`);\r\n    \r\n    if (confirmed) {\r\n      const index = this.files.findIndex(f => f.id === file.id);\r\n      if (index !== -1) {\r\n        this.files.splice(index, 1);\r\n        this.filterFiles();\r\n        alert('File deleted successfully!');\r\n      }\r\n    }\r\n  }\r\n\r\n  formatFileSize(bytes: number): string {\r\n    if (bytes === 0) return '0 Bytes';\r\n    \r\n    const k = 1024;\r\n    const sizes = ['Bytes', 'KB', 'MB', 'GB'];\r\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\r\n    \r\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\r\n  }\r\n\r\n  getFileIcon(type: string): string {\r\n    switch (type.toLowerCase()) {\r\n      case 'pdf': return 'document-text';\r\n      case 'doc':\r\n      case 'docx': return 'document';\r\n      case 'xls':\r\n      case 'xlsx': return 'grid';\r\n      case 'jpg':\r\n      case 'jpeg':\r\n      case 'png':\r\n      case 'gif': return 'image';\r\n      case 'zip':\r\n      case 'rar': return 'archive';\r\n      default: return 'document-outline';\r\n    }\r\n  }\r\n\r\n  getFileTypeColor(type: string): string {\r\n    switch (type.toLowerCase()) {\r\n      case 'pdf': return 'danger';\r\n      case 'doc':\r\n      case 'docx': return 'primary';\r\n      case 'xls':\r\n      case 'xlsx': return 'success';\r\n      case 'jpg':\r\n      case 'jpeg':\r\n      case 'png':\r\n      case 'gif': return 'warning';\r\n      case 'zip':\r\n      case 'rar': return 'dark';\r\n      default: return 'medium';\r\n    }\r\n  }\r\n\r\n  getTotalFiles(): number {\r\n    return this.files.length;\r\n  }\r\n\r\n  getTotalSize(): string {\r\n    const totalBytes = this.files.reduce((sum, file) => sum + file.size, 0);\r\n    return this.formatFileSize(totalBytes);\r\n  }\r\n}\r\n", "<ion-header>\r\n  <ion-toolbar class=\"veritus-toolbar\">\r\n    <ion-title class=\"veritus-text-white veritus-font-semibold\">File Management</ion-title>\r\n  </ion-toolbar>\r\n</ion-header>\r\n\r\n<ion-content class=\"files-content veritus-gradient-bg\">\r\n  <div class=\"files-container veritus-safe-area-top\">\r\n    \r\n    <!-- Filters -->\r\n    <div class=\"filters-section\">\r\n      <div class=\"filter-row\">\r\n        <div class=\"filter-item\">\r\n          <ion-label class=\"filter-label veritus-text-sm veritus-text-white\">Lawyer</ion-label>\r\n          <ion-select\r\n            [(ngModel)]=\"selectedLawyer\"\r\n            (ionChange)=\"onLawyerChange()\"\r\n            interface=\"popover\"\r\n            class=\"lawyer-select\">\r\n            <ion-select-option value=\"all\">All Lawyers</ion-select-option>\r\n            <ion-select-option *ngFor=\"let lawyer of linkedLawyers\" [value]=\"lawyer.uid\">\r\n              {{ lawyer.name }}\r\n            </ion-select-option>\r\n          </ion-select>\r\n        </div>\r\n        \r\n        <div class=\"filter-item\">\r\n          <ion-label class=\"filter-label veritus-text-sm veritus-text-white\">Type</ion-label>\r\n          <ion-select\r\n            [(ngModel)]=\"selectedType\"\r\n            (ionChange)=\"onTypeChange()\"\r\n            interface=\"popover\"\r\n            class=\"type-select\">\r\n            <ion-select-option value=\"all\">All Types</ion-select-option>\r\n            <ion-select-option value=\"pdf\">PDF</ion-select-option>\r\n            <ion-select-option value=\"docx\">Word</ion-select-option>\r\n            <ion-select-option value=\"xlsx\">Excel</ion-select-option>\r\n            <ion-select-option value=\"zip\">Archive</ion-select-option>\r\n          </ion-select>\r\n        </div>\r\n      </div>\r\n      \r\n      <div class=\"search-section\">\r\n        <ion-searchbar\r\n          [(ngModel)]=\"searchTerm\"\r\n          (ionInput)=\"onSearchChange()\"\r\n          placeholder=\"Search files...\"\r\n          class=\"custom-searchbar\">\r\n        </ion-searchbar>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Upload Button -->\r\n    <div class=\"action-section\">\r\n      <button class=\"veritus-btn-primary upload-btn\" (click)=\"onUploadFile()\">\r\n        <ion-icon name=\"cloud-upload\" class=\"btn-icon\"></ion-icon>\r\n        Upload File\r\n      </button>\r\n    </div>\r\n\r\n    <!-- Files List -->\r\n    <div class=\"files-section\">\r\n      <h2 class=\"section-title veritus-text-lg veritus-font-semibold veritus-text-white\">\r\n        Files ({{ filteredFiles.length }})\r\n      </h2>\r\n      \r\n      <div class=\"files-list\" *ngIf=\"filteredFiles.length > 0\">\r\n        <div class=\"file-card\" *ngFor=\"let file of filteredFiles\">\r\n          <div class=\"file-icon\">\r\n            <ion-icon [name]=\"getFileIcon(file.type)\" [color]=\"getFileTypeColor(file.type)\"></ion-icon>\r\n          </div>\r\n          \r\n          <div class=\"file-details\">\r\n            <h3 class=\"file-name veritus-text-base veritus-font-semibold veritus-text-white\">\r\n              {{ file.name }}\r\n            </h3>\r\n            <p class=\"file-info veritus-text-sm veritus-text-gray\">\r\n              {{ formatFileSize(file.size) }} • {{ file.lawyerName }}\r\n            </p>\r\n            <p class=\"file-case veritus-text-sm veritus-text-gold\" *ngIf=\"file.caseName\">\r\n              {{ file.caseName }}\r\n            </p>\r\n            <p class=\"file-date veritus-text-xs veritus-text-gray\">\r\n              Uploaded {{ file.uploadedAt | date:'short' }}\r\n            </p>\r\n          </div>\r\n          \r\n          <div class=\"file-actions\">\r\n            <button class=\"action-btn download-btn\" (click)=\"onDownloadFile(file)\">\r\n              <ion-icon name=\"download-outline\"></ion-icon>\r\n            </button>\r\n            <button class=\"action-btn delete-btn\" (click)=\"onDeleteFile(file)\">\r\n              <ion-icon name=\"trash-outline\"></ion-icon>\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      \r\n      <!-- Empty State -->\r\n      <div class=\"empty-state\" *ngIf=\"filteredFiles.length === 0\">\r\n        <ion-icon name=\"folder-outline\" class=\"empty-icon\"></ion-icon>\r\n        <h3 class=\"empty-title veritus-text-lg veritus-font-semibold veritus-text-white\">No Files Found</h3>\r\n        <p class=\"empty-description veritus-text-sm veritus-text-gray\">\r\n          No files match your current filters.\r\n        </p>\r\n        <button class=\"veritus-btn-secondary\" (click)=\"onUploadFile()\">\r\n          Upload First File\r\n        </button>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Quick Stats -->\r\n    <div class=\"stats-section\">\r\n      <div class=\"stat-card\">\r\n        <div class=\"stat-number veritus-text-xl veritus-font-bold veritus-text-white\">\r\n          {{ getTotalFiles() }}\r\n        </div>\r\n        <div class=\"stat-label veritus-text-sm veritus-text-gray\">Total Files</div>\r\n      </div>\r\n      \r\n      <div class=\"stat-card\">\r\n        <div class=\"stat-number veritus-text-xl veritus-font-bold veritus-text-white\">\r\n          {{ linkedLawyers.length }}\r\n        </div>\r\n        <div class=\"stat-label veritus-text-sm veritus-text-gray\">Linked Lawyers</div>\r\n      </div>\r\n      \r\n      <div class=\"stat-card\">\r\n        <div class=\"stat-number veritus-text-xl veritus-font-bold veritus-text-white\">\r\n          {{ getTotalSize() }}\r\n        </div>\r\n        <div class=\"stat-label veritus-text-sm veritus-text-gray\">Total Size</div>\r\n      </div>\r\n    </div>\r\n\r\n  </div>\r\n</ion-content>\r\n"], "names": ["RouterModule", "Secretary<PERSON><PERSON><PERSON><PERSON>", "routes", "path", "component", "SecretaryFilesPageRoutingModule", "<PERSON><PERSON><PERSON><PERSON>", "imports", "i1", "exports", "CommonModule", "FormsModule", "IonicModule", "SecretaryFilesPageModule", "declarations", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "lawyer_r1", "uid", "ɵɵadvance", "ɵɵtextInterpolate1", "name", "file_r3", "caseName", "ɵɵelement", "ɵɵtemplate", "SecretaryFilesPage_div_38_div_1_p_8_Template", "ɵɵlistener", "SecretaryFilesPage_div_38_div_1_Template_button_click_13_listener", "ɵɵrestoreView", "_r2", "$implicit", "ctx_r3", "ɵɵnextContext", "ɵɵresetView", "onDownloadFile", "SecretaryFilesPage_div_38_div_1_Template_button_click_15_listener", "onDeleteFile", "getFileIcon", "type", "getFileTypeColor", "ɵɵtextInterpolate2", "formatFileSize", "size", "<PERSON><PERSON><PERSON>", "ɵɵpipeBind2", "uploadedAt", "SecretaryFilesPage_div_38_div_1_Template", "filteredFiles", "SecretaryFilesPage_div_39_Template_button_click_6_listener", "_r5", "onUploadFile", "constructor", "firebaseService", "linkedLawyers", "<PERSON><PERSON><PERSON><PERSON>", "files", "searchTerm", "selectedType", "ngOnInit", "loadLinkedLawyers", "loadFiles", "_this", "_asyncToGenerator", "currentUser", "getCurrentUser", "getSecretaryLinkedLawyers", "_this2", "mockFiles", "id", "uploadedBy", "Date", "lawyerId", "caseId", "sort", "a", "b", "getTime", "filterFiles", "filter", "file", "matchesLawyer", "matchesType", "matchesSearch", "toLowerCase", "includes", "onLawyerChange", "onTypeChange", "onSearchChange", "_this3", "length", "alert", "fileName", "prompt", "lawyer", "find", "l", "fileExtension", "split", "pop", "newFile", "now", "toString", "Math", "floor", "random", "undefined", "unshift", "_this4", "confirmed", "confirm", "index", "findIndex", "f", "splice", "bytes", "k", "sizes", "i", "log", "parseFloat", "pow", "toFixed", "getTotalFiles", "getTotalSize", "totalBytes", "reduce", "sum", "ɵɵdirectiveInject", "FirebaseService", "selectors", "decls", "vars", "consts", "template", "SecretaryFiles<PERSON>age_Template", "rf", "ctx", "ɵɵtwoWayListener", "SecretaryFiles<PERSON>age_Template_ion_select_ngModelChange_11_listener", "$event", "ɵɵtwoWayBindingSet", "SecretaryFiles<PERSON>age_Template_ion_select_ionChange_11_listener", "SecretaryFilesPage_ion_select_option_14_Template", "SecretaryFiles<PERSON>age_Template_ion_select_ngModelChange_18_listener", "SecretaryFiles<PERSON>age_Template_ion_select_ionChange_18_listener", "SecretaryFiles<PERSON>age_Template_ion_searchbar_ngModelChange_30_listener", "SecretaryFiles<PERSON>age_Template_ion_searchbar_ionInput_30_listener", "SecretaryFiles<PERSON>age_Template_button_click_32_listener", "SecretaryFiles<PERSON>age_div_38_Template", "SecretaryFiles<PERSON>age_div_39_Template", "ɵɵtwoWayProperty"], "sourceRoot": "webpack:///", "x_google_ignoreList": []}