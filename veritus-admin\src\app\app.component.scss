// Global styles for the admin application
.app-container {
  height: 100vh;
  width: 100vw;
  overflow: hidden;
}

.admin-layout {
  display: flex;
  height: 100vh;
  
  .main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    
    .content-area {
      flex: 1;
      padding: 20px;
      overflow-y: auto;
      background-color: #f8f9fa;
    }
  }
}

.login-layout {
  height: 100vh;
  width: 100vw;
}

// Global color variables
:root {
  --admin-primary: #C49A56;
  --admin-primary-dark: #B8894A;
  --admin-secondary: #f8f9fa;
  --admin-success: #28a745;
  --admin-danger: #dc3545;
  --admin-warning: #ffc107;
  --admin-info: #17a2b8;
  --admin-light: #f8f9fa;
  --admin-dark: #343a40;
  --admin-white: #ffffff;
  --admin-gray: #6c757d;
  --admin-gray-light: #e9ecef;
}
