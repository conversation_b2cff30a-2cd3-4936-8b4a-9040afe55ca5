{"version": 3, "file": "node_modules_ionic_core_dist_esm_ion-text_entry_js.js", "mappings": ";;;;;;;;;;;;;;;;AAAA;AACA;AACA;AAC0E;AACZ;AACD;AAE7D,MAAMS,OAAO,GAAG,gDAAgD;AAChE,MAAMC,aAAa,GAAGD,OAAO;AAE7B,MAAME,IAAI,GAAG,MAAM;EACfC,WAAWA,CAACC,OAAO,EAAE;IACjBZ,qDAAgB,CAAC,IAAI,EAAEY,OAAO,CAAC;IAC/B,IAAI,CAACC,KAAK,GAAGC,SAAS;EAC1B;EACAC,MAAMA,CAAA,EAAG;IACL,MAAMC,IAAI,GAAGT,4DAAU,CAAC,IAAI,CAAC;IAC7B,OAAQN,qDAAC,CAACE,iDAAI,EAAE;MAAEc,GAAG,EAAE,0CAA0C;MAAEC,KAAK,EAAEb,qDAAkB,CAAC,IAAI,CAACQ,KAAK,EAAE;QACjG,CAACG,IAAI,GAAG;MACZ,CAAC;IAAE,CAAC,EAAEf,qDAAC,CAAC,MAAM,EAAE;MAAEgB,GAAG,EAAE;IAA2C,CAAC,CAAC,CAAC;EAC7E;AACJ,CAAC;AACDP,IAAI,CAACS,KAAK,GAAGV,aAAa", "sources": ["./node_modules/@ionic/core/dist/esm/ion-text.entry.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, h, H as Host } from './index-a1a47f01.js';\nimport { c as createColorClasses } from './theme-01f3f29c.js';\nimport { b as getIonMode } from './ionic-global-94f25d1b.js';\n\nconst textCss = \":host(.ion-color){color:var(--ion-color-base)}\";\nconst IonTextStyle0 = textCss;\n\nconst Text = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.color = undefined;\n    }\n    render() {\n        const mode = getIonMode(this);\n        return (h(Host, { key: '4330b56cbc4e15953d9b3162fb40af728a8195dd', class: createColorClasses(this.color, {\n                [mode]: true,\n            }) }, h(\"slot\", { key: 'ec674a71d8fbb04d537fd79d617d9db4a607c340' })));\n    }\n};\nText.style = IonTextStyle0;\n\nexport { Text as ion_text };\n"], "names": ["r", "registerInstance", "h", "H", "Host", "c", "createColorClasses", "b", "getIonMode", "textCss", "IonTextStyle0", "Text", "constructor", "hostRef", "color", "undefined", "render", "mode", "key", "class", "style", "ion_text"], "sourceRoot": "webpack:///", "x_google_ignoreList": [0]}