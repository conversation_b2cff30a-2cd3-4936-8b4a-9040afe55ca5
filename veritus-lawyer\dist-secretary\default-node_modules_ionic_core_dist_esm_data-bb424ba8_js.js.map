{"version": 3, "file": "default-node_modules_ionic_core_dist_esm_data-bb424ba8_js.js", "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AAC2D;;AAE3D;AACA;AACA;AACA,MAAME,SAAS,GAAGA,CAACC,SAAS,EAAEC,YAAY,KAAK;EAC3C,OAAQD,SAAS,CAACE,KAAK,KAAKD,YAAY,CAACC,KAAK,IAAIF,SAAS,CAACG,GAAG,KAAKF,YAAY,CAACE,GAAG,IAAIH,SAAS,CAACI,IAAI,KAAKH,YAAY,CAACG,IAAI;AAChI,CAAC;AACD;AACA;AACA;AACA,MAAMC,QAAQ,GAAGA,CAACL,SAAS,EAAEC,YAAY,KAAK;EAC1C,OAAO,CAAC,EAAED,SAAS,CAACI,IAAI,GAAGH,YAAY,CAACG,IAAI,IACvCJ,SAAS,CAACI,IAAI,KAAKH,YAAY,CAACG,IAAI,IAAIJ,SAAS,CAACE,KAAK,GAAGD,YAAY,CAACC,KAAM,IAC7EF,SAAS,CAACI,IAAI,KAAKH,YAAY,CAACG,IAAI,IACjCJ,SAAS,CAACE,KAAK,KAAKD,YAAY,CAACC,KAAK,IACtCF,SAAS,CAACG,GAAG,KAAK,IAAI,IACtBH,SAAS,CAACG,GAAG,GAAGF,YAAY,CAACE,GAAI,CAAC;AAC9C,CAAC;AACD;AACA;AACA;AACA,MAAMG,OAAO,GAAGA,CAACN,SAAS,EAAEC,YAAY,KAAK;EACzC,OAAO,CAAC,EAAED,SAAS,CAACI,IAAI,GAAGH,YAAY,CAACG,IAAI,IACvCJ,SAAS,CAACI,IAAI,KAAKH,YAAY,CAACG,IAAI,IAAIJ,SAAS,CAACE,KAAK,GAAGD,YAAY,CAACC,KAAM,IAC7EF,SAAS,CAACI,IAAI,KAAKH,YAAY,CAACG,IAAI,IACjCJ,SAAS,CAACE,KAAK,KAAKD,YAAY,CAACC,KAAK,IACtCF,SAAS,CAACG,GAAG,KAAK,IAAI,IACtBH,SAAS,CAACG,GAAG,GAAGF,YAAY,CAACE,GAAI,CAAC;AAC9C,CAAC;AACD,MAAMI,sBAAsB,GAAGA,CAACC,KAAK,EAAEC,GAAG,EAAEC,GAAG,KAAK;EAChD,MAAMC,UAAU,GAAGC,KAAK,CAACC,OAAO,CAACL,KAAK,CAAC,GAAGA,KAAK,GAAG,CAACA,KAAK,CAAC;EACzD,KAAK,MAAMM,GAAG,IAAIH,UAAU,EAAE;IAC1B,IAAKF,GAAG,KAAKM,SAAS,IAAIV,QAAQ,CAACS,GAAG,EAAEL,GAAG,CAAC,IAAMC,GAAG,KAAKK,SAAS,IAAIT,OAAO,CAACQ,GAAG,EAAEJ,GAAG,CAAE,EAAE;MACvFZ,qDAAe,CAAC,0DAA0D,GACtE,QAAQkB,IAAI,CAACC,SAAS,CAACR,GAAG,CAAC,IAAI,GAC/B,QAAQO,IAAI,CAACC,SAAS,CAACP,GAAG,CAAC,IAAI,GAC/B,UAAUM,IAAI,CAACC,SAAS,CAACT,KAAK,CAAC,EAAE,CAAC;MACtC;IACJ;EACJ;AACJ,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,MAAMU,UAAU,GAAId,IAAI,IAAK;EACzB,OAAQA,IAAI,GAAG,CAAC,KAAK,CAAC,IAAIA,IAAI,GAAG,GAAG,KAAK,CAAC,IAAKA,IAAI,GAAG,GAAG,KAAK,CAAC;AACnE,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA,MAAMe,YAAY,GAAGA,CAACC,MAAM,EAAEC,SAAS,KAAK;EACxC;AACJ;AACA;AACA;EACI,IAAIA,SAAS,KAAKN,SAAS,EAAE;IACzB,OAAOM,SAAS;EACpB;EACA;AACJ;AACA;AACA;AACA;AACA;EACI,MAAMC,SAAS,GAAG,IAAIC,IAAI,CAACC,cAAc,CAACJ,MAAM,EAAE;IAAEK,IAAI,EAAE;EAAU,CAAC,CAAC;EACtE,MAAMC,OAAO,GAAGJ,SAAS,CAACK,eAAe,CAAC,CAAC;EAC3C,IAAID,OAAO,CAACL,SAAS,KAAKN,SAAS,EAAE;IACjC,OAAOW,OAAO,CAACL,SAAS;EAC5B;EACA;AACJ;AACA;AACA;AACA;EACI,MAAMO,IAAI,GAAG,IAAIC,IAAI,CAAC,iBAAiB,CAAC;EACxC,MAAMC,KAAK,GAAGR,SAAS,CAACS,aAAa,CAACH,IAAI,CAAC;EAC3C,MAAMH,IAAI,GAAGK,KAAK,CAACE,IAAI,CAAEnC,CAAC,IAAKA,CAAC,CAACoC,IAAI,KAAK,MAAM,CAAC;EACjD,IAAI,CAACR,IAAI,EAAE;IACP,MAAM,IAAIS,KAAK,CAAC,0CAA0C,CAAC;EAC/D;EACA;AACJ;AACA;AACA;AACA;AACA;EACI,QAAQT,IAAI,CAACjB,KAAK;IACd,KAAK,GAAG;MACJ,OAAO,KAAK;IAChB,KAAK,IAAI;MACL,OAAO,KAAK;IAChB,KAAK,IAAI;MACL,OAAO,KAAK;IAChB,KAAK,IAAI;MACL,OAAO,KAAK;IAChB;MACI,MAAM,IAAI0B,KAAK,CAAC,uBAAuBb,SAAS,GAAG,CAAC;EAC5D;AACJ,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA,MAAMc,QAAQ,GAAId,SAAS,IAAK;EAC5B,OAAOA,SAAS,KAAK,KAAK,IAAIA,SAAS,KAAK,KAAK;AACrD,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA,MAAMe,iBAAiB,GAAGA,CAAClC,KAAK,EAAEE,IAAI,KAAK;EACvC,OAAOF,KAAK,KAAK,CAAC,IAAIA,KAAK,KAAK,CAAC,IAAIA,KAAK,KAAK,CAAC,IAAIA,KAAK,KAAK,EAAE,GAC1D,EAAE,GACFA,KAAK,KAAK,CAAC,GACPgB,UAAU,CAACd,IAAI,CAAC,GACZ,EAAE,GACF,EAAE,GACN,EAAE;AAChB,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMiC,kBAAkB,GAAGA,CAACjB,MAAM,EAAEkB,aAAa,GAAG;EAChDpC,KAAK,EAAE,SAAS;EAChBE,IAAI,EAAE;AACV,CAAC,KAAK;EACF;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,MAAM0B,KAAK,GAAG,IAAIP,IAAI,CAACC,cAAc,CAACJ,MAAM,EAAEkB,aAAa,CAAC,CAACP,aAAa,CAAC,IAAIF,IAAI,CAAC,CAAC,CAAC;EACtF,OAAOC,KAAK,CAAC,CAAC,CAAC,CAACG,IAAI,KAAK,OAAO;AACpC,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA,MAAMM,oBAAoB,GAAInB,MAAM,IAAK;EACrC,MAAMU,KAAK,GAAG,IAAIP,IAAI,CAACC,cAAc,CAACJ,MAAM,EAAE;IAAEK,IAAI,EAAE;EAAU,CAAC,CAAC,CAACM,aAAa,CAAC,IAAIF,IAAI,CAAC,CAAC,CAAC;EAC5F,OAAOC,KAAK,CAAC,CAAC,CAAC,CAACG,IAAI,KAAK,WAAW;AACxC,CAAC;AAED,MAAMO,eAAe;AACrB;AACA,oIAAoI;AACpI;AACA,MAAMC,WAAW,GAAG,qFAAqF;AACzG;AACA;AACA;AACA;AACA,MAAMC,uBAAuB,GAAIC,KAAK,IAAK;EACvC,IAAIA,KAAK,KAAK5B,SAAS,EAAE;IACrB;EACJ;EACA,IAAI6B,cAAc,GAAGD,KAAK;EAC1B,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;IAC3B;IACA;IACAC,cAAc,GAAGD,KAAK,CAACE,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC;EAC9D;EACA,IAAIC,MAAM;EACV,IAAInC,KAAK,CAACC,OAAO,CAAC+B,cAAc,CAAC,EAAE;IAC/B;IACAG,MAAM,GAAGH,cAAc,CAACI,GAAG,CAAEC,GAAG,IAAKC,QAAQ,CAACD,GAAG,EAAE,EAAE,CAAC,CAAC,CAACE,MAAM,CAACC,QAAQ,CAAC;EAC5E,CAAC,MACI;IACDL,MAAM,GAAG,CAACH,cAAc,CAAC;EAC7B;EACA,OAAOG,MAAM;AACjB,CAAC;AACD;AACA;AACA;AACA;AACA;AACA,MAAMM,uBAAuB,GAAIC,EAAE,IAAK;EACpC,OAAO;IACHpD,KAAK,EAAEgD,QAAQ,CAACI,EAAE,CAACC,YAAY,CAAC,YAAY,CAAC,EAAE,EAAE,CAAC;IAClDpD,GAAG,EAAE+C,QAAQ,CAACI,EAAE,CAACC,YAAY,CAAC,UAAU,CAAC,EAAE,EAAE,CAAC;IAC9CnD,IAAI,EAAE8C,QAAQ,CAACI,EAAE,CAACC,YAAY,CAAC,WAAW,CAAC,EAAE,EAAE,CAAC;IAChDC,SAAS,EAAEN,QAAQ,CAACI,EAAE,CAACC,YAAY,CAAC,kBAAkB,CAAC,EAAE,EAAE;EAC/D,CAAC;AACL,CAAC;AACD,SAASE,SAASA,CAAC3C,GAAG,EAAE;EACpB,IAAIF,KAAK,CAACC,OAAO,CAACC,GAAG,CAAC,EAAE;IACpB,MAAM4C,WAAW,GAAG,EAAE;IACtB,KAAK,MAAMC,MAAM,IAAI7C,GAAG,EAAE;MACtB,MAAM8C,SAAS,GAAGH,SAAS,CAACE,MAAM,CAAC;MACnC;AACZ;AACA;AACA;AACA;AACA;AACA;MACY,IAAI,CAACC,SAAS,EAAE;QACZ,OAAO7C,SAAS;MACpB;MACA2C,WAAW,CAACG,IAAI,CAACD,SAAS,CAAC;IAC/B;IACA,OAAOF,WAAW;EACtB;EACA;EACA;EACA,IAAII,KAAK,GAAG,IAAI;EAChB,IAAIhD,GAAG,IAAI,IAAI,IAAIA,GAAG,KAAK,EAAE,EAAE;IAC3B;IACAgD,KAAK,GAAGrB,WAAW,CAACsB,IAAI,CAACjD,GAAG,CAAC;IAC7B,IAAIgD,KAAK,EAAE;MACP;MACAA,KAAK,CAACE,OAAO,CAACjD,SAAS,EAAEA,SAAS,CAAC;MACnC+C,KAAK,CAAC,CAAC,CAAC,GAAGA,KAAK,CAAC,CAAC,CAAC,GAAG/C,SAAS;IACnC,CAAC,MACI;MACD;MACA+C,KAAK,GAAGtB,eAAe,CAACuB,IAAI,CAACjD,GAAG,CAAC;IACrC;EACJ;EACA,IAAIgD,KAAK,KAAK,IAAI,EAAE;IAChB;IACAhE,qDAAe,CAAC,gCAAgCgB,GAAG,oDAAoD,CAAC;IACxG,OAAOC,SAAS;EACpB;EACA;EACA,KAAK,IAAIkD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;IACxBH,KAAK,CAACG,CAAC,CAAC,GAAGH,KAAK,CAACG,CAAC,CAAC,KAAKlD,SAAS,GAAGmC,QAAQ,CAACY,KAAK,CAACG,CAAC,CAAC,EAAE,EAAE,CAAC,GAAGlD,SAAS;EAC1E;EACA;EACA,OAAO;IACHX,IAAI,EAAE0D,KAAK,CAAC,CAAC,CAAC;IACd5D,KAAK,EAAE4D,KAAK,CAAC,CAAC,CAAC;IACf3D,GAAG,EAAE2D,KAAK,CAAC,CAAC,CAAC;IACbrC,IAAI,EAAEqC,KAAK,CAAC,CAAC,CAAC;IACdI,MAAM,EAAEJ,KAAK,CAAC,CAAC,CAAC;IAChBK,IAAI,EAAEL,KAAK,CAAC,CAAC,CAAC,GAAG,EAAE,GAAG,IAAI,GAAG;EACjC,CAAC;AACL;AACA,MAAMM,SAAS,GAAGA,CAACC,SAAS,EAAEC,QAAQ,EAAEC,QAAQ,KAAK;EACjD,IAAID,QAAQ,IAAIjE,QAAQ,CAACgE,SAAS,EAAEC,QAAQ,CAAC,EAAE;IAC3C,OAAOA,QAAQ;EACnB,CAAC,MACI,IAAIC,QAAQ,IAAIjE,OAAO,CAAC+D,SAAS,EAAEE,QAAQ,CAAC,EAAE;IAC/C,OAAOA,QAAQ;EACnB;EACA,OAAOF,SAAS;AACpB,CAAC;AACD;AACA;AACA;AACA;AACA;AACA,MAAMG,SAAS,GAAI/C,IAAI,IAAK;EACxB,OAAOA,IAAI,IAAI,EAAE,GAAG,IAAI,GAAG,IAAI;AACnC,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA,MAAMgD,aAAa,GAAGA,CAAC/D,GAAG,EAAEgE,UAAU,KAAK;EACvC,MAAMC,MAAM,GAAGlB,SAAS,CAAC/C,GAAG,CAAC;EAC7B;AACJ;AACA;EACI,IAAIiE,MAAM,KAAK5D,SAAS,EAAE;IACtB;EACJ;EACA,MAAM;IAAEb,KAAK;IAAEC,GAAG;IAAEC,IAAI;IAAEqB,IAAI;IAAEyC;EAAO,CAAC,GAAGS,MAAM;EACjD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,MAAMC,SAAS,GAAGxE,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAK,KAAK,CAAC,GAAGA,IAAI,GAAGsE,UAAU,CAACtE,IAAI;EAC3E,MAAMyE,UAAU,GAAG3E,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK,CAAC,GAAGA,KAAK,GAAG,EAAE;EAClE,OAAO;IACHA,KAAK,EAAE2E,UAAU;IACjB1E,GAAG,EAAEA,GAAG,KAAK,IAAI,IAAIA,GAAG,KAAK,KAAK,CAAC,GAAGA,GAAG,GAAGiC,iBAAiB,CAACyC,UAAU,EAAED,SAAS,CAAC;IACpF;AACR;AACA;AACA;AACA;IACQxE,IAAI,EAAEwE,SAAS;IACfnD,IAAI,EAAEA,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAK,KAAK,CAAC,GAAGA,IAAI,GAAG,EAAE;IAClDyC,MAAM,EAAEA,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAGA,MAAM,GAAG;EAC5D,CAAC;AACL,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA,MAAMY,aAAa,GAAGA,CAACrE,GAAG,EAAEiE,UAAU,KAAK;EACvC,MAAMC,MAAM,GAAGlB,SAAS,CAAChD,GAAG,CAAC;EAC7B;AACJ;AACA;EACI,IAAIkE,MAAM,KAAK5D,SAAS,EAAE;IACtB;EACJ;EACA,MAAM;IAAEb,KAAK;IAAEC,GAAG;IAAEC,IAAI;IAAEqB,IAAI;IAAEyC;EAAO,CAAC,GAAGS,MAAM;EACjD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,OAAO;IACHzE,KAAK,EAAEA,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK,CAAC,GAAGA,KAAK,GAAG,CAAC;IACrDC,GAAG,EAAEA,GAAG,KAAK,IAAI,IAAIA,GAAG,KAAK,KAAK,CAAC,GAAGA,GAAG,GAAG,CAAC;IAC7C;AACR;AACA;AACA;AACA;IACQC,IAAI,EAAEA,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAK,KAAK,CAAC,GAAGA,IAAI,GAAGsE,UAAU,CAACtE,IAAI;IAC/DqB,IAAI,EAAEA,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAK,KAAK,CAAC,GAAGA,IAAI,GAAG,CAAC;IACjDyC,MAAM,EAAEA,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAGA,MAAM,GAAG;EAC5D,CAAC;AACL,CAAC;AAED,MAAMa,QAAQ,GAAIjE,GAAG,IAAK;EACtB,OAAO,CAAC,GAAG,IAAIA,GAAG,KAAKC,SAAS,GAAGiE,IAAI,CAACC,GAAG,CAACnE,GAAG,CAAC,GAAG,GAAG,CAAC,EAAEoE,KAAK,CAAC,CAAC,CAAC,CAAC;AACtE,CAAC;AACD,MAAMC,SAAS,GAAIrE,GAAG,IAAK;EACvB,OAAO,CAAC,KAAK,IAAIA,GAAG,KAAKC,SAAS,GAAGiE,IAAI,CAACC,GAAG,CAACnE,GAAG,CAAC,GAAG,GAAG,CAAC,EAAEoE,KAAK,CAAC,CAAC,CAAC,CAAC;AACxE,CAAC;AACD,SAASE,gBAAgBA,CAACC,IAAI,EAAE;EAC5B,IAAIzE,KAAK,CAACC,OAAO,CAACwE,IAAI,CAAC,EAAE;IACrB,OAAOA,IAAI,CAACrC,GAAG,CAAElB,KAAK,IAAKsD,gBAAgB,CAACtD,KAAK,CAAC,CAAC;EACvD;EACA;EACA,IAAIwD,GAAG,GAAG,EAAE;EACZ,IAAID,IAAI,CAACjF,IAAI,KAAKW,SAAS,EAAE;IACzB;IACAuE,GAAG,GAAGH,SAAS,CAACE,IAAI,CAACjF,IAAI,CAAC;IAC1B,IAAIiF,IAAI,CAACnF,KAAK,KAAKa,SAAS,EAAE;MAC1B;MACAuE,GAAG,IAAI,GAAG,GAAGP,QAAQ,CAACM,IAAI,CAACnF,KAAK,CAAC;MACjC,IAAImF,IAAI,CAAClF,GAAG,KAAKY,SAAS,EAAE;QACxB;QACAuE,GAAG,IAAI,GAAG,GAAGP,QAAQ,CAACM,IAAI,CAAClF,GAAG,CAAC;QAC/B,IAAIkF,IAAI,CAAC5D,IAAI,KAAKV,SAAS,EAAE;UACzB;UACAuE,GAAG,IAAI,IAAIP,QAAQ,CAACM,IAAI,CAAC5D,IAAI,CAAC,IAAIsD,QAAQ,CAACM,IAAI,CAACnB,MAAM,CAAC,KAAK;QAChE;MACJ;IACJ;EACJ,CAAC,MACI,IAAImB,IAAI,CAAC5D,IAAI,KAAKV,SAAS,EAAE;IAC9B;IACAuE,GAAG,GAAGP,QAAQ,CAACM,IAAI,CAAC5D,IAAI,CAAC,GAAG,GAAG,GAAGsD,QAAQ,CAACM,IAAI,CAACnB,MAAM,CAAC;EAC3D;EACA,OAAOoB,GAAG;AACd;AACA;AACA;AACA;AACA,MAAMC,qBAAqB,GAAGA,CAAC9D,IAAI,EAAE0C,IAAI,KAAK;EAC1C,IAAIA,IAAI,KAAKpD,SAAS,EAAE;IACpB,OAAOU,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACI,IAAI0C,IAAI,KAAK,IAAI,EAAE;IACf,IAAI1C,IAAI,KAAK,EAAE,EAAE;MACb,OAAO,CAAC;IACZ;IACA,OAAOA,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACI,IAAIA,IAAI,KAAK,EAAE,EAAE;IACb,OAAO,EAAE;EACb;EACA,OAAOA,IAAI,GAAG,EAAE;AACpB,CAAC;AACD,MAAM+D,cAAc,GAAIC,QAAQ,IAAK;EACjC,MAAM;IAAEjC;EAAU,CAAC,GAAGiC,QAAQ;EAC9B,IAAIjC,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAKzC,SAAS,EAAE;IAC/C,MAAM,IAAImB,KAAK,CAAC,yBAAyB,CAAC;EAC9C;EACA,OAAOwD,YAAY,CAACD,QAAQ,EAAEjC,SAAS,CAAC;AAC5C,CAAC;AACD,MAAMmC,YAAY,GAAIF,QAAQ,IAAK;EAC/B,MAAM;IAAEjC;EAAU,CAAC,GAAGiC,QAAQ;EAC9B,IAAIjC,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAKzC,SAAS,EAAE;IAC/C,MAAM,IAAImB,KAAK,CAAC,yBAAyB,CAAC;EAC9C;EACA,OAAO0D,OAAO,CAACH,QAAQ,EAAE,CAAC,GAAGjC,SAAS,CAAC;AAC3C,CAAC;AACD,MAAMqC,UAAU,GAAIJ,QAAQ,IAAK;EAC7B,OAAOG,OAAO,CAACH,QAAQ,EAAE,CAAC,CAAC;AAC/B,CAAC;AACD,MAAMK,cAAc,GAAIL,QAAQ,IAAK;EACjC,OAAOC,YAAY,CAACD,QAAQ,EAAE,CAAC,CAAC;AACpC,CAAC;AACD,MAAMM,eAAe,GAAIN,QAAQ,IAAK;EAClC,OAAOC,YAAY,CAACD,QAAQ,EAAE,CAAC,CAAC;AACpC,CAAC;AACD,MAAMO,WAAW,GAAIP,QAAQ,IAAK;EAC9B,OAAOG,OAAO,CAACH,QAAQ,EAAE,CAAC,CAAC;AAC/B,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,YAAY,GAAGA,CAACD,QAAQ,EAAEQ,OAAO,KAAK;EACxC,MAAM;IAAE/F,KAAK;IAAEC,GAAG;IAAEC;EAAK,CAAC,GAAGqF,QAAQ;EACrC,IAAItF,GAAG,KAAK,IAAI,EAAE;IACd,MAAM,IAAI+B,KAAK,CAAC,iBAAiB,CAAC;EACtC;EACA,MAAMgE,YAAY,GAAG;IACjBhG,KAAK;IACLC,GAAG;IACHC;EACJ,CAAC;EACD8F,YAAY,CAAC/F,GAAG,GAAGA,GAAG,GAAG8F,OAAO;EAChC;AACJ;AACA;AACA;EACI,IAAIC,YAAY,CAAC/F,GAAG,GAAG,CAAC,EAAE;IACtB+F,YAAY,CAAChG,KAAK,IAAI,CAAC;EAC3B;EACA;AACJ;AACA;AACA;EACI,IAAIgG,YAAY,CAAChG,KAAK,GAAG,CAAC,EAAE;IACxBgG,YAAY,CAAChG,KAAK,GAAG,EAAE;IACvBgG,YAAY,CAAC9F,IAAI,IAAI,CAAC;EAC1B;EACA;AACJ;AACA;AACA;EACI,IAAI8F,YAAY,CAAC/F,GAAG,GAAG,CAAC,EAAE;IACtB,MAAMgG,WAAW,GAAG/D,iBAAiB,CAAC8D,YAAY,CAAChG,KAAK,EAAEgG,YAAY,CAAC9F,IAAI,CAAC;IAC5E;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQ8F,YAAY,CAAC/F,GAAG,GAAGgG,WAAW,GAAGD,YAAY,CAAC/F,GAAG;EACrD;EACA,OAAO+F,YAAY;AACvB,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA,MAAMN,OAAO,GAAGA,CAACH,QAAQ,EAAEQ,OAAO,KAAK;EACnC,MAAM;IAAE/F,KAAK;IAAEC,GAAG;IAAEC;EAAK,CAAC,GAAGqF,QAAQ;EACrC,IAAItF,GAAG,KAAK,IAAI,EAAE;IACd,MAAM,IAAI+B,KAAK,CAAC,iBAAiB,CAAC;EACtC;EACA,MAAMgE,YAAY,GAAG;IACjBhG,KAAK;IACLC,GAAG;IACHC;EACJ,CAAC;EACD,MAAM+F,WAAW,GAAG/D,iBAAiB,CAAClC,KAAK,EAAEE,IAAI,CAAC;EAClD8F,YAAY,CAAC/F,GAAG,GAAGA,GAAG,GAAG8F,OAAO;EAChC;AACJ;AACA;AACA;EACI,IAAIC,YAAY,CAAC/F,GAAG,GAAGgG,WAAW,EAAE;IAChCD,YAAY,CAAC/F,GAAG,IAAIgG,WAAW;IAC/BD,YAAY,CAAChG,KAAK,IAAI,CAAC;EAC3B;EACA;AACJ;AACA;AACA;EACI,IAAIgG,YAAY,CAAChG,KAAK,GAAG,EAAE,EAAE;IACzBgG,YAAY,CAAChG,KAAK,GAAG,CAAC;IACtBgG,YAAY,CAAC9F,IAAI,IAAI,CAAC;EAC1B;EACA,OAAO8F,YAAY;AACvB,CAAC;AACD;AACA;AACA;AACA,MAAME,gBAAgB,GAAIX,QAAQ,IAAK;EACnC;AACJ;AACA;AACA;EACI,MAAMvF,KAAK,GAAGuF,QAAQ,CAACvF,KAAK,KAAK,CAAC,GAAG,EAAE,GAAGuF,QAAQ,CAACvF,KAAK,GAAG,CAAC;EAC5D,MAAME,IAAI,GAAGqF,QAAQ,CAACvF,KAAK,KAAK,CAAC,GAAGuF,QAAQ,CAACrF,IAAI,GAAG,CAAC,GAAGqF,QAAQ,CAACrF,IAAI;EACrE,MAAMiG,cAAc,GAAGjE,iBAAiB,CAAClC,KAAK,EAAEE,IAAI,CAAC;EACrD,MAAMD,GAAG,GAAGkG,cAAc,GAAGZ,QAAQ,CAACtF,GAAG,GAAGkG,cAAc,GAAGZ,QAAQ,CAACtF,GAAG;EACzE,OAAO;IAAED,KAAK;IAAEE,IAAI;IAAED;EAAI,CAAC;AAC/B,CAAC;AACD;AACA;AACA;AACA,MAAMmG,YAAY,GAAIb,QAAQ,IAAK;EAC/B;AACJ;AACA;AACA;EACI,MAAMvF,KAAK,GAAGuF,QAAQ,CAACvF,KAAK,KAAK,EAAE,GAAG,CAAC,GAAGuF,QAAQ,CAACvF,KAAK,GAAG,CAAC;EAC5D,MAAME,IAAI,GAAGqF,QAAQ,CAACvF,KAAK,KAAK,EAAE,GAAGuF,QAAQ,CAACrF,IAAI,GAAG,CAAC,GAAGqF,QAAQ,CAACrF,IAAI;EACtE,MAAMiG,cAAc,GAAGjE,iBAAiB,CAAClC,KAAK,EAAEE,IAAI,CAAC;EACrD,MAAMD,GAAG,GAAGkG,cAAc,GAAGZ,QAAQ,CAACtF,GAAG,GAAGkG,cAAc,GAAGZ,QAAQ,CAACtF,GAAG;EACzE,OAAO;IAAED,KAAK;IAAEE,IAAI;IAAED;EAAI,CAAC;AAC/B,CAAC;AACD,MAAMoG,UAAU,GAAGA,CAACd,QAAQ,EAAEe,SAAS,KAAK;EACxC,MAAMtG,KAAK,GAAGuF,QAAQ,CAACvF,KAAK;EAC5B,MAAME,IAAI,GAAGqF,QAAQ,CAACrF,IAAI,GAAGoG,SAAS;EACtC,MAAMH,cAAc,GAAGjE,iBAAiB,CAAClC,KAAK,EAAEE,IAAI,CAAC;EACrD,MAAMD,GAAG,GAAGkG,cAAc,GAAGZ,QAAQ,CAACtF,GAAG,GAAGkG,cAAc,GAAGZ,QAAQ,CAACtF,GAAG;EACzE,OAAO;IAAED,KAAK;IAAEE,IAAI;IAAED;EAAI,CAAC;AAC/B,CAAC;AACD;AACA;AACA;AACA,MAAMsG,eAAe,GAAIhB,QAAQ,IAAK;EAClC,OAAOc,UAAU,CAACd,QAAQ,EAAE,CAAC,CAAC,CAAC;AACnC,CAAC;AACD;AACA;AACA;AACA,MAAMiB,WAAW,GAAIjB,QAAQ,IAAK;EAC9B,OAAOc,UAAU,CAACd,QAAQ,EAAE,CAAC,CAAC;AAClC,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA,MAAMkB,oBAAoB,GAAGA,CAAClF,IAAI,EAAEmF,SAAS,EAAEzC,IAAI,KAAK;EACpD,IAAIyC,SAAS,EAAE;IACX,OAAOnF,IAAI;EACf;EACA,OAAO8D,qBAAqB,CAAC9D,IAAI,EAAE0C,IAAI,CAAC;AAC5C,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM0C,qBAAqB,GAAGA,CAACC,YAAY,EAAEC,OAAO,KAAK;EACrD,MAAM;IAAE5C,IAAI,EAAE6C,WAAW;IAAEvF;EAAK,CAAC,GAAGqF,YAAY;EAChD,IAAIG,OAAO,GAAGxF,IAAI;EAClB;AACJ;AACA;AACA;EACI,IAAIuF,WAAW,KAAK,IAAI,IAAID,OAAO,KAAK,IAAI,EAAE;IAC1CE,OAAO,GAAG1B,qBAAqB,CAAC0B,OAAO,EAAE,IAAI,CAAC;IAC9C;AACR;AACA;EACI,CAAC,MACI,IAAID,WAAW,KAAK,IAAI,IAAID,OAAO,KAAK,IAAI,EAAE;IAC/CE,OAAO,GAAGjC,IAAI,CAACC,GAAG,CAACgC,OAAO,GAAG,EAAE,CAAC;EACpC;EACA,OAAOA,OAAO;AAClB,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,aAAa,GAAGA,CAACpF,KAAK,EAAEwC,QAAQ,EAAEC,QAAQ,KAAK;EACjD,MAAM;IAAErE,KAAK;IAAEC,GAAG;IAAEC;EAAK,CAAC,GAAG0B,KAAK;EAClC,MAAMqF,SAAS,GAAG/C,SAAS,CAACgD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEvF,KAAK,CAAC,EAAEwC,QAAQ,EAAEC,QAAQ,CAAC;EACzE,MAAM0B,OAAO,GAAG7D,iBAAiB,CAAClC,KAAK,EAAEE,IAAI,CAAC;EAC9C;AACJ;AACA;AACA;AACA;AACA;EACI,IAAID,GAAG,KAAK,IAAI,IAAI8F,OAAO,GAAG9F,GAAG,EAAE;IAC/BgH,SAAS,CAAChH,GAAG,GAAG8F,OAAO;EAC3B;EACA;AACJ;AACA;AACA;EACI,IAAI3B,QAAQ,KAAKvD,SAAS,IAAIhB,SAAS,CAACoH,SAAS,EAAE7C,QAAQ,CAAC,EAAE;IAC1D;AACR;AACA;AACA;AACA;AACA;IACQ,IAAI6C,SAAS,CAAC1F,IAAI,KAAKV,SAAS,IAAIuD,QAAQ,CAAC7C,IAAI,KAAKV,SAAS,EAAE;MAC7D,IAAIoG,SAAS,CAAC1F,IAAI,GAAG6C,QAAQ,CAAC7C,IAAI,EAAE;QAChC0F,SAAS,CAAC1F,IAAI,GAAG6C,QAAQ,CAAC7C,IAAI;QAC9B0F,SAAS,CAACjD,MAAM,GAAGI,QAAQ,CAACJ,MAAM;QAClC;AAChB;AACA;AACA;MACY,CAAC,MACI,IAAIiD,SAAS,CAAC1F,IAAI,KAAK6C,QAAQ,CAAC7C,IAAI,IACrC0F,SAAS,CAACjD,MAAM,KAAKnD,SAAS,IAC9BuD,QAAQ,CAACJ,MAAM,KAAKnD,SAAS,IAC7BoG,SAAS,CAACjD,MAAM,GAAGI,QAAQ,CAACJ,MAAM,EAAE;QACpCiD,SAAS,CAACjD,MAAM,GAAGI,QAAQ,CAACJ,MAAM;MACtC;IACJ;EACJ;EACA;AACJ;AACA;AACA;EACI,IAAIK,QAAQ,KAAKxD,SAAS,IAAIhB,SAAS,CAAC+B,KAAK,EAAEyC,QAAQ,CAAC,EAAE;IACtD;AACR;AACA;AACA;AACA;AACA;IACQ,IAAI4C,SAAS,CAAC1F,IAAI,KAAKV,SAAS,IAAIwD,QAAQ,CAAC9C,IAAI,KAAKV,SAAS,EAAE;MAC7D,IAAIoG,SAAS,CAAC1F,IAAI,GAAG8C,QAAQ,CAAC9C,IAAI,EAAE;QAChC0F,SAAS,CAAC1F,IAAI,GAAG8C,QAAQ,CAAC9C,IAAI;QAC9B0F,SAAS,CAACjD,MAAM,GAAGK,QAAQ,CAACL,MAAM;QAClC;AAChB;AACA;AACA;MACY,CAAC,MACI,IAAIiD,SAAS,CAAC1F,IAAI,KAAK8C,QAAQ,CAAC9C,IAAI,IACrC0F,SAAS,CAACjD,MAAM,KAAKnD,SAAS,IAC9BwD,QAAQ,CAACL,MAAM,KAAKnD,SAAS,IAC7BoG,SAAS,CAACjD,MAAM,GAAGK,QAAQ,CAACL,MAAM,EAAE;QACpCiD,SAAS,CAACjD,MAAM,GAAGK,QAAQ,CAACL,MAAM;MACtC;IACJ;EACJ;EACA,OAAOiD,SAAS;AACpB,CAAC;AACD;AACA;AACA;AACA;AACA;AACA,MAAMG,mBAAmB,GAAGA,CAAC;EAAE7B,QAAQ;EAAE8B,WAAW;EAAEC,SAAS;EAAEC,UAAU;EAAEC,UAAU;EAAEC,YAAY;EAAErD,QAAQ;EAAEC;AAAU,CAAC,KAAK;EAC7H,MAAM;IAAE9C,IAAI;IAAEyC,MAAM;IAAE/D,GAAG;IAAED,KAAK;IAAEE;EAAK,CAAC,GAAGqF,QAAQ;EACnD,MAAMmC,SAAS,GAAGR,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE5B,QAAQ,CAAC,EAAE;IAAEjC,SAAS,EAAEzC;EAAU,CAAC,CAAC;EACtF,IAAI0G,UAAU,KAAK1G,SAAS,EAAE;IAC1B;IACA,MAAM8G,aAAa,GAAGJ,UAAU,CAACtE,MAAM,CAAE/C,IAAI,IAAK;MAC9C,IAAIkE,QAAQ,KAAKvD,SAAS,IAAIX,IAAI,GAAGkE,QAAQ,CAAClE,IAAI,EAAE;QAChD,OAAO,KAAK;MAChB;MACA,IAAImE,QAAQ,KAAKxD,SAAS,IAAIX,IAAI,GAAGmE,QAAQ,CAACnE,IAAI,EAAE;QAChD,OAAO,KAAK;MAChB;MACA,OAAO,IAAI;IACf,CAAC,CAAC;IACFwH,SAAS,CAACxH,IAAI,GAAG0H,gBAAgB,CAAC1H,IAAI,EAAEyH,aAAa,CAAC;EAC1D;EACA,IAAIN,WAAW,KAAKxG,SAAS,EAAE;IAC3B;IACA,MAAMgH,cAAc,GAAGR,WAAW,CAACpE,MAAM,CAAEjD,KAAK,IAAK;MACjD,IAAIoE,QAAQ,KAAKvD,SAAS,IAAI6G,SAAS,CAACxH,IAAI,KAAKkE,QAAQ,CAAClE,IAAI,IAAIF,KAAK,GAAGoE,QAAQ,CAACpE,KAAK,EAAE;QACtF,OAAO,KAAK;MAChB;MACA,IAAIqE,QAAQ,KAAKxD,SAAS,IAAI6G,SAAS,CAACxH,IAAI,KAAKmE,QAAQ,CAACnE,IAAI,IAAIF,KAAK,GAAGqE,QAAQ,CAACrE,KAAK,EAAE;QACtF,OAAO,KAAK;MAChB;MACA,OAAO,IAAI;IACf,CAAC,CAAC;IACF0H,SAAS,CAAC1H,KAAK,GAAG4H,gBAAgB,CAAC5H,KAAK,EAAE6H,cAAc,CAAC;EAC7D;EACA;EACA,IAAI5H,GAAG,KAAK,IAAI,IAAIqH,SAAS,KAAKzG,SAAS,EAAE;IACzC;IACA,MAAMiH,YAAY,GAAGR,SAAS,CAACrE,MAAM,CAAEhD,GAAG,IAAK;MAC3C,IAAImE,QAAQ,KAAKvD,SAAS,IAAIV,QAAQ,CAAC+G,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEO,SAAS,CAAC,EAAE;QAAEzH;MAAI,CAAC,CAAC,EAAEmE,QAAQ,CAAC,EAAE;QACpG,OAAO,KAAK;MAChB;MACA,IAAIC,QAAQ,KAAKxD,SAAS,IAAIT,OAAO,CAAC8G,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEO,SAAS,CAAC,EAAE;QAAEzH;MAAI,CAAC,CAAC,EAAEoE,QAAQ,CAAC,EAAE;QACnG,OAAO,KAAK;MAChB;MACA,OAAO,IAAI;IACf,CAAC,CAAC;IACFqD,SAAS,CAACzH,GAAG,GAAG2H,gBAAgB,CAAC3H,GAAG,EAAE6H,YAAY,CAAC;EACvD;EACA,IAAIvG,IAAI,KAAKV,SAAS,IAAI2G,UAAU,KAAK3G,SAAS,EAAE;IAChD;IACA,MAAMkH,aAAa,GAAGP,UAAU,CAACvE,MAAM,CAAE1B,IAAI,IAAK;MAC9C,IAAI,CAAC6C,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAAC7C,IAAI,MAAMV,SAAS,IAAIhB,SAAS,CAAC6H,SAAS,EAAEtD,QAAQ,CAAC,IAAI7C,IAAI,GAAG6C,QAAQ,CAAC7C,IAAI,EAAE;QAC7I,OAAO,KAAK;MAChB;MACA,IAAI,CAAC8C,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAAC9C,IAAI,MAAMV,SAAS,IAAIhB,SAAS,CAAC6H,SAAS,EAAErD,QAAQ,CAAC,IAAI9C,IAAI,GAAG8C,QAAQ,CAAC9C,IAAI,EAAE;QAC7I,OAAO,KAAK;MAChB;MACA,OAAO,IAAI;IACf,CAAC,CAAC;IACFmG,SAAS,CAACnG,IAAI,GAAGqG,gBAAgB,CAACrG,IAAI,EAAEwG,aAAa,CAAC;IACtDL,SAAS,CAACzD,IAAI,GAAGK,SAAS,CAACoD,SAAS,CAACnG,IAAI,CAAC;EAC9C;EACA,IAAIyC,MAAM,KAAKnD,SAAS,IAAI4G,YAAY,KAAK5G,SAAS,EAAE;IACpD;IACA,MAAMmH,eAAe,GAAGP,YAAY,CAACxE,MAAM,CAAEe,MAAM,IAAK;MACpD,IAAI,CAACI,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACJ,MAAM,MAAMnD,SAAS,IACnFhB,SAAS,CAAC6H,SAAS,EAAEtD,QAAQ,CAAC,IAC9BsD,SAAS,CAACnG,IAAI,KAAK6C,QAAQ,CAAC7C,IAAI,IAChCyC,MAAM,GAAGI,QAAQ,CAACJ,MAAM,EAAE;QAC1B,OAAO,KAAK;MAChB;MACA,IAAI,CAACK,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACL,MAAM,MAAMnD,SAAS,IACnFhB,SAAS,CAAC6H,SAAS,EAAErD,QAAQ,CAAC,IAC9BqD,SAAS,CAACnG,IAAI,KAAK8C,QAAQ,CAAC9C,IAAI,IAChCyC,MAAM,GAAGK,QAAQ,CAACL,MAAM,EAAE;QAC1B,OAAO,KAAK;MAChB;MACA,OAAO,IAAI;IACf,CAAC,CAAC;IACF0D,SAAS,CAAC1D,MAAM,GAAG4D,gBAAgB,CAAC5D,MAAM,EAAEgE,eAAe,CAAC;EAChE;EACA,OAAON,SAAS;AACpB,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAME,gBAAgB,GAAGA,CAACK,SAAS,EAAEpF,MAAM,KAAK;EAC5C,IAAIqF,YAAY,GAAGrF,MAAM,CAAC,CAAC,CAAC;EAC5B,IAAIsF,IAAI,GAAGrD,IAAI,CAACC,GAAG,CAACmD,YAAY,GAAGD,SAAS,CAAC;EAC7C,KAAK,IAAIlE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGlB,MAAM,CAACuF,MAAM,EAAErE,CAAC,EAAE,EAAE;IACpC,MAAMzD,KAAK,GAAGuC,MAAM,CAACkB,CAAC,CAAC;IACvB;AACR;AACA;AACA;AACA;AACA;AACA;IACQ,MAAMsE,SAAS,GAAGvD,IAAI,CAACC,GAAG,CAACzE,KAAK,GAAG2H,SAAS,CAAC;IAC7C,IAAII,SAAS,GAAGF,IAAI,EAAE;MAClBD,YAAY,GAAG5H,KAAK;MACpB6H,IAAI,GAAGE,SAAS;IACpB;EACJ;EACA,OAAOH,YAAY;AACvB,CAAC;AAED,MAAMI,qBAAqB,GAAIC,SAAS,IAAK;EACzC,IAAIA,SAAS,KAAK1H,SAAS,EAAE;IACzB,OAAO,EAAE;EACb;EACA,OAAO0H,SAAS,CAACC,WAAW,CAAC,CAAC;AAClC,CAAC;AACD;AACA;AACA;AACA;AACA;AACA,MAAMC,aAAa,GAAIrG,aAAa,IAAK;EACrC,OAAO8E,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE/E,aAAa,CAAC,EAAE;IACnD;AACR;AACA;AACA;AACA;IACQsG,QAAQ,EAAE,KAAK;IACf;AACR;AACA;IACQC,YAAY,EAAE9H;EAAU,CAAC,CAAC;AAClC,CAAC;AACD,MAAM+H,gBAAgB,GAAGA,CAAC1H,MAAM,EAAEqE,QAAQ,EAAEpE,SAAS,EAAEiB,aAAa,GAAG;EAAEb,IAAI,EAAE,SAAS;EAAEyC,MAAM,EAAE;AAAU,CAAC,KAAK;EAC9G,MAAM6E,SAAS,GAAG;IACdtH,IAAI,EAAEgE,QAAQ,CAAChE,IAAI;IACnByC,MAAM,EAAEuB,QAAQ,CAACvB;EACrB,CAAC;EACD,IAAI6E,SAAS,CAACtH,IAAI,KAAKV,SAAS,IAAIgI,SAAS,CAAC7E,MAAM,KAAKnD,SAAS,EAAE;IAChE,OAAO,cAAc;EACzB;EACA,OAAO,IAAIQ,IAAI,CAACC,cAAc,CAACJ,MAAM,EAAEgG,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEsB,aAAa,CAACrG,aAAa,CAAC,CAAC,EAAE;IAClG;AACR;AACA;AACA;IACQjB;EAAU,CAAC,CAAC,CAAC,CAAC2H,MAAM,CAAC,IAAInH,IAAI,CAACuD,gBAAgB,CAACgC,MAAM,CAACC,MAAM,CAAC;IAC7D;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQjH,IAAI,EAAE,IAAI;IAAED,GAAG,EAAE,CAAC;IAAED,KAAK,EAAE;EAAE,CAAC,EAAE6I,SAAS,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC;AAC3D,CAAC;AACD;AACA;AACA;AACA;AACA,MAAME,cAAc,GAAIzI,KAAK,IAAK;EAC9B,MAAM0I,aAAa,GAAG1I,KAAK,CAAC2I,QAAQ,CAAC,CAAC;EACtC,IAAID,aAAa,CAACZ,MAAM,GAAG,CAAC,EAAE;IAC1B,OAAOY,aAAa;EACxB;EACA,OAAO,IAAIA,aAAa,EAAE;AAC9B,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA,MAAME,gBAAgB,GAAGA,CAAC3H,IAAI,EAAEJ,SAAS,KAAK;EAC1C;AACJ;AACA;AACA;AACA;AACA;EACI,IAAII,IAAI,KAAK,CAAC,EAAE;IACZ,QAAQJ,SAAS;MACb,KAAK,KAAK;QACN,OAAO,GAAG;MACd,KAAK,KAAK;QACN,OAAO,IAAI;MACf,KAAK,KAAK;QACN,OAAO,IAAI;MACf,KAAK,KAAK;QACN,OAAO,IAAI;MACf;QACI,MAAM,IAAIa,KAAK,CAAC,uBAAuBb,SAAS,GAAG,CAAC;IAC5D;EACJ;EACA,MAAMuF,SAAS,GAAGzE,QAAQ,CAACd,SAAS,CAAC;EACrC;AACJ;AACA;EACI,IAAIuF,SAAS,EAAE;IACX,OAAOqC,cAAc,CAACxH,IAAI,CAAC;EAC/B;EACA,OAAOA,IAAI,CAAC0H,QAAQ,CAAC,CAAC;AAC1B,CAAC;AACD;AACA;AACA;AACA;AACA;AACA,MAAME,oBAAoB,GAAGA,CAACjI,MAAM,EAAEkI,KAAK,EAAE7D,QAAQ,KAAK;EACtD,IAAIA,QAAQ,CAACtF,GAAG,KAAK,IAAI,EAAE;IACvB,OAAO,IAAI;EACf;EACA;AACJ;AACA;EACI,MAAMyB,IAAI,GAAG2H,iBAAiB,CAAC9D,QAAQ,CAAC;EACxC,MAAM+D,WAAW,GAAG,IAAIjI,IAAI,CAACC,cAAc,CAACJ,MAAM,EAAE;IAChDqI,OAAO,EAAE,MAAM;IACfvJ,KAAK,EAAE,MAAM;IACbC,GAAG,EAAE,SAAS;IACdyI,QAAQ,EAAE;EACd,CAAC,CAAC,CAACI,MAAM,CAACpH,IAAI,CAAC;EACf;AACJ;AACA;AACA;EACI,OAAO0H,KAAK,GAAG,UAAUE,WAAW,EAAE,GAAGA,WAAW;AACxD,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA,MAAME,eAAe,GAAGA,CAACtI,MAAM,EAAEqE,QAAQ,KAAK;EAC1C,MAAM7D,IAAI,GAAG2H,iBAAiB,CAAC9D,QAAQ,CAAC;EACxC,OAAO,IAAIlE,IAAI,CAACC,cAAc,CAACJ,MAAM,EAAE;IAAElB,KAAK,EAAE,MAAM;IAAEE,IAAI,EAAE,SAAS;IAAEwI,QAAQ,EAAE;EAAM,CAAC,CAAC,CAACI,MAAM,CAACpH,IAAI,CAAC;AAC5G,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM+H,MAAM,GAAGA,CAACvI,MAAM,EAAEqE,QAAQ,KAAK;EACjC,OAAOmE,yBAAyB,CAACxI,MAAM,EAAEqE,QAAQ,EAAE;IAAEtF,GAAG,EAAE;EAAU,CAAC,CAAC,CAAC6B,IAAI,CAAE6H,GAAG,IAAKA,GAAG,CAAC5H,IAAI,KAAK,KAAK,CAAC,CAACzB,KAAK;AAClH,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA,MAAMsJ,OAAO,GAAGA,CAAC1I,MAAM,EAAEqE,QAAQ,KAAK;EAClC,OAAOsE,oBAAoB,CAAC3I,MAAM,EAAEqE,QAAQ,EAAE;IAAErF,IAAI,EAAE;EAAU,CAAC,CAAC;AACtE,CAAC;AACD;AACA;AACA;AACA;AACA,MAAMmJ,iBAAiB,GAAI9D,QAAQ,IAAK;EACpC,IAAIuE,EAAE,EAAEC,EAAE,EAAEC,EAAE;EACd,MAAMC,UAAU,GAAG1E,QAAQ,CAAChE,IAAI,KAAKV,SAAS,IAAI0E,QAAQ,CAACvB,MAAM,KAAKnD,SAAS,GAAG,IAAI0E,QAAQ,CAAChE,IAAI,IAAIgE,QAAQ,CAACvB,MAAM,EAAE,GAAG,EAAE;EAC7H;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,OAAO,IAAIrC,IAAI,CAAC,GAAG,CAACmI,EAAE,GAAGvE,QAAQ,CAACvF,KAAK,MAAM,IAAI,IAAI8J,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,CAAC,IAAI,CAACC,EAAE,GAAGxE,QAAQ,CAACtF,GAAG,MAAM,IAAI,IAAI8J,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,CAAC,IAAI,CAACC,EAAE,GAAGzE,QAAQ,CAACrF,IAAI,MAAM,IAAI,IAAI8J,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,IAAI,GAAGC,UAAU,WAAW,CAAC;AAChO,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMJ,oBAAoB,GAAGA,CAAC3I,MAAM,EAAEqE,QAAQ,EAAE/D,OAAO,KAAK;EACxD,MAAME,IAAI,GAAG2H,iBAAiB,CAAC9D,QAAQ,CAAC;EACxC,OAAO2E,iBAAiB,CAAChJ,MAAM,EAAEuH,aAAa,CAACjH,OAAO,CAAC,CAAC,CAACsH,MAAM,CAACpH,IAAI,CAAC;AACzE,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA,MAAMgI,yBAAyB,GAAGA,CAACxI,MAAM,EAAEqE,QAAQ,EAAE/D,OAAO,KAAK;EAC7D,MAAME,IAAI,GAAG2H,iBAAiB,CAAC9D,QAAQ,CAAC;EACxC,OAAO2E,iBAAiB,CAAChJ,MAAM,EAAEM,OAAO,CAAC,CAACK,aAAa,CAACH,IAAI,CAAC;AACjE,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA,MAAMwI,iBAAiB,GAAGA,CAAChJ,MAAM,EAAEM,OAAO,KAAK;EAC3C,OAAO,IAAIH,IAAI,CAACC,cAAc,CAACJ,MAAM,EAAEgG,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE3F,OAAO,CAAC,EAAE;IAAEkH,QAAQ,EAAE;EAAM,CAAC,CAAC,CAAC;AAC1G,CAAC;AACD;AACA;AACA;AACA;AACA;AACA,MAAMyB,aAAa,GAAIjJ,MAAM,IAAK;EAC9B,IAAI,oBAAoB,IAAIG,IAAI,EAAE;IAC9B,MAAM+I,KAAK,GAAG,IAAI/I,IAAI,CAACgJ,kBAAkB,CAACnJ,MAAM,EAAE;MAAEoJ,OAAO,EAAE;IAAO,CAAC,CAAC,CAACxB,MAAM,CAAC,CAAC,EAAE,KAAK,CAAC;IACvF,OAAOsB,KAAK,CAACG,MAAM,CAAC,CAAC,CAAC,CAAC/B,WAAW,CAAC,CAAC,GAAG4B,KAAK,CAACpF,KAAK,CAAC,CAAC,CAAC;EACzD,CAAC,MACI;IACD,OAAO,OAAO;EAClB;AACJ,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMwF,kBAAkB,GAAI9I,IAAI,IAAK;EACjC,MAAM+I,QAAQ,GAAG/I,IAAI,CAACgJ,iBAAiB,CAAC,CAAC;EACzChJ,IAAI,CAACiJ,UAAU,CAACjJ,IAAI,CAACkJ,UAAU,CAAC,CAAC,GAAGH,QAAQ,CAAC;EAC7C,OAAO/I,IAAI;AACf,CAAC;AACD,MAAMmJ,OAAO,GAAGL,kBAAkB,CAAC,IAAI7I,IAAI,CAAC,YAAY,CAAC,CAAC;AAC1D,MAAMmJ,OAAO,GAAGN,kBAAkB,CAAC,IAAI7I,IAAI,CAAC,YAAY,CAAC,CAAC;AAC1D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMoJ,qBAAqB,GAAGA,CAAC7J,MAAM,EAAEqH,SAAS,KAAK;EACjD,MAAM7G,IAAI,GAAG6G,SAAS,KAAK,IAAI,GAAGsC,OAAO,GAAGC,OAAO;EACnD,MAAME,kBAAkB,GAAG,IAAI3J,IAAI,CAACC,cAAc,CAACJ,MAAM,EAAE;IACvDK,IAAI,EAAE,SAAS;IACfmH,QAAQ,EAAE;EACd,CAAC,CAAC,CACG7G,aAAa,CAACH,IAAI,CAAC,CACnBI,IAAI,CAAEmJ,IAAI,IAAKA,IAAI,CAAClJ,IAAI,KAAK,WAAW,CAAC;EAC9C,IAAIiJ,kBAAkB,EAAE;IACpB,OAAOA,kBAAkB,CAAC1K,KAAK;EACnC;EACA,OAAOgI,qBAAqB,CAACC,SAAS,CAAC;AAC3C,CAAC;AACD;AACA;AACA;AACA;AACA;AACA,MAAM2C,WAAW,GAAI5K,KAAK,IAAK;EAC3B,OAAOI,KAAK,CAACC,OAAO,CAACL,KAAK,CAAC,GAAGA,KAAK,CAAC6K,IAAI,CAAC,GAAG,CAAC,GAAG7K,KAAK;AACzD,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,MAAM8K,QAAQ,GAAGA,CAAA,KAAM;EACnB;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,OAAOZ,kBAAkB,CAAC,IAAI7I,IAAI,CAAC,CAAC,CAAC,CAAC0J,WAAW,CAAC,CAAC;AACvD,CAAC;AACD,MAAMC,OAAO,GAAG,CACZ,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EACpH,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CACjH;AACD;AACA,MAAMC,MAAM,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC;AACrD;AACA,MAAMC,MAAM,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC;AACrD;AACA,MAAMC,MAAM,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;AACrG;AACA,MAAMC,MAAM,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;AACrG;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,aAAa,GAAGA,CAACzK,MAAM,EAAE0K,IAAI,EAAEC,cAAc,GAAG,CAAC,KAAK;EACxD;AACJ;AACA;AACA;AACA;EACI,MAAMC,aAAa,GAAGF,IAAI,KAAK,KAAK,GAAG,OAAO,GAAG,QAAQ;EACzD,MAAMG,IAAI,GAAG,IAAI1K,IAAI,CAACC,cAAc,CAACJ,MAAM,EAAE;IAAEqI,OAAO,EAAEuC;EAAc,CAAC,CAAC;EACxE,MAAME,SAAS,GAAG,IAAIrK,IAAI,CAAC,YAAY,CAAC;EACxC,MAAMsK,UAAU,GAAG,EAAE;EACrB;AACJ;AACA;AACA;EACI,KAAK,IAAIlI,CAAC,GAAG8H,cAAc,EAAE9H,CAAC,GAAG8H,cAAc,GAAG,CAAC,EAAE9H,CAAC,EAAE,EAAE;IACtD,MAAMmI,WAAW,GAAG,IAAIvK,IAAI,CAACqK,SAAS,CAAC;IACvCE,WAAW,CAACC,OAAO,CAACD,WAAW,CAACE,OAAO,CAAC,CAAC,GAAGrI,CAAC,CAAC;IAC9CkI,UAAU,CAACtI,IAAI,CAACoI,IAAI,CAACjD,MAAM,CAACoD,WAAW,CAAC,CAAC;EAC7C;EACA,OAAOD,UAAU;AACrB,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMI,cAAc,GAAGA,CAACrM,KAAK,EAAEE,IAAI,EAAE2L,cAAc,KAAK;EACpD,MAAM9F,OAAO,GAAG7D,iBAAiB,CAAClC,KAAK,EAAEE,IAAI,CAAC;EAC9C,MAAMoM,YAAY,GAAG,IAAI3K,IAAI,CAAC,GAAG3B,KAAK,MAAME,IAAI,EAAE,CAAC,CAACuJ,MAAM,CAAC,CAAC;EAC5D;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,MAAM8C,MAAM,GAAGD,YAAY,IAAIT,cAAc,GAAGS,YAAY,IAAIT,cAAc,GAAG,CAAC,CAAC,GAAG,CAAC,IAAIA,cAAc,GAAGS,YAAY,CAAC;EACzH,IAAIE,IAAI,GAAG,EAAE;EACb,KAAK,IAAIzI,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAIgC,OAAO,EAAEhC,CAAC,EAAE,EAAE;IAC/ByI,IAAI,CAAC7I,IAAI,CAAC;MAAE1D,GAAG,EAAE8D,CAAC;MAAET,SAAS,EAAE,CAACiJ,MAAM,GAAGxI,CAAC,IAAI;IAAE,CAAC,CAAC;EACtD;EACA,KAAK,IAAIA,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAIwI,MAAM,EAAExI,CAAC,EAAE,EAAE;IAC9ByI,IAAI,GAAG,CAAC;MAAEvM,GAAG,EAAE,IAAI;MAAEqD,SAAS,EAAE;IAAK,CAAC,EAAE,GAAGkJ,IAAI,CAAC;EACpD;EACA,OAAOA,IAAI;AACf,CAAC;AACD;AACA;AACA;AACA;AACA,MAAMC,WAAW,GAAItL,SAAS,IAAK;EAC/B,QAAQA,SAAS;IACb,KAAK,KAAK;MACN,OAAOoK,MAAM;IACjB,KAAK,KAAK;MACN,OAAOC,MAAM;IACjB,KAAK,KAAK;MACN,OAAOC,MAAM;IACjB,KAAK,KAAK;MACN,OAAOC,MAAM;IACjB;MACI,MAAM,IAAI1J,KAAK,CAAC,uBAAuBb,SAAS,GAAG,CAAC;EAC5D;AACJ,CAAC;AACD;AACA;AACA;AACA;AACA;AACA,MAAMuL,YAAY,GAAGA,CAACxL,MAAM,EAAEqE,QAAQ,EAAEpE,SAAS,GAAG,KAAK,EAAEiD,QAAQ,EAAEC,QAAQ,EAAEmD,UAAU,EAAEC,YAAY,KAAK;EACxG,MAAMkF,iBAAiB,GAAG1L,YAAY,CAACC,MAAM,EAAEC,SAAS,CAAC;EACzD,MAAMuF,SAAS,GAAGzE,QAAQ,CAAC0K,iBAAiB,CAAC;EAC7C,IAAIC,cAAc,GAAGH,WAAW,CAACE,iBAAiB,CAAC;EACnD,IAAIE,gBAAgB,GAAGvB,OAAO;EAC9B,IAAIwB,WAAW,GAAG,IAAI;EACtB,IAAIC,WAAW,GAAG,IAAI;EACtB,IAAIvF,UAAU,EAAE;IACZoF,cAAc,GAAGA,cAAc,CAAC3J,MAAM,CAAE1B,IAAI,IAAKiG,UAAU,CAACwF,QAAQ,CAACzL,IAAI,CAAC,CAAC;EAC/E;EACA,IAAIkG,YAAY,EAAE;IACdoF,gBAAgB,GAAGA,gBAAgB,CAAC5J,MAAM,CAAEe,MAAM,IAAKyD,YAAY,CAACuF,QAAQ,CAAChJ,MAAM,CAAC,CAAC;EACzF;EACA,IAAII,QAAQ,EAAE;IACV;AACR;AACA;AACA;AACA;IACQ,IAAIvE,SAAS,CAAC0F,QAAQ,EAAEnB,QAAQ,CAAC,EAAE;MAC/B;AACZ;AACA;AACA;AACA;MACY,IAAIA,QAAQ,CAAC7C,IAAI,KAAKV,SAAS,EAAE;QAC7B+L,cAAc,GAAGA,cAAc,CAAC3J,MAAM,CAAE1B,IAAI,IAAK;UAC7C,MAAM0L,aAAa,GAAG1H,QAAQ,CAACtB,IAAI,KAAK,IAAI,GAAG,CAAC1C,IAAI,GAAG,EAAE,IAAI,EAAE,GAAGA,IAAI;UACtE,OAAO,CAACmF,SAAS,GAAGnF,IAAI,GAAG0L,aAAa,KAAK7I,QAAQ,CAAC7C,IAAI;QAC9D,CAAC,CAAC;QACFuL,WAAW,GAAG1I,QAAQ,CAAC7C,IAAI,GAAG,EAAE;MACpC;MACA,IAAI6C,QAAQ,CAACJ,MAAM,KAAKnD,SAAS,EAAE;QAC/B;AAChB;AACA;AACA;AACA;AACA;AACA;QACgB,IAAIqM,aAAa,GAAG,KAAK;QACzB,IAAI9I,QAAQ,CAAC7C,IAAI,KAAKV,SAAS,IAAI0E,QAAQ,CAAChE,IAAI,KAAKV,SAAS,EAAE;UAC5D,IAAI0E,QAAQ,CAAChE,IAAI,GAAG6C,QAAQ,CAAC7C,IAAI,EAAE;YAC/B2L,aAAa,GAAG,IAAI;UACxB;QACJ;QACAL,gBAAgB,GAAGA,gBAAgB,CAAC5J,MAAM,CAAEe,MAAM,IAAK;UACnD,IAAIkJ,aAAa,EAAE;YACf,OAAO,IAAI;UACf;UACA,OAAOlJ,MAAM,IAAII,QAAQ,CAACJ,MAAM;QACpC,CAAC,CAAC;MACN;MACA;AACZ;AACA;AACA;IACQ,CAAC,MACI,IAAI7D,QAAQ,CAACoF,QAAQ,EAAEnB,QAAQ,CAAC,EAAE;MACnCwI,cAAc,GAAG,EAAE;MACnBC,gBAAgB,GAAG,EAAE;MACrBC,WAAW,GAAGC,WAAW,GAAG,KAAK;IACrC;EACJ;EACA,IAAI1I,QAAQ,EAAE;IACV;AACR;AACA;AACA;AACA;IACQ,IAAIxE,SAAS,CAAC0F,QAAQ,EAAElB,QAAQ,CAAC,EAAE;MAC/B;AACZ;AACA;AACA;AACA;MACY,IAAIA,QAAQ,CAAC9C,IAAI,KAAKV,SAAS,EAAE;QAC7B+L,cAAc,GAAGA,cAAc,CAAC3J,MAAM,CAAE1B,IAAI,IAAK;UAC7C,MAAM0L,aAAa,GAAG1H,QAAQ,CAACtB,IAAI,KAAK,IAAI,GAAG,CAAC1C,IAAI,GAAG,EAAE,IAAI,EAAE,GAAGA,IAAI;UACtE,OAAO,CAACmF,SAAS,GAAGnF,IAAI,GAAG0L,aAAa,KAAK5I,QAAQ,CAAC9C,IAAI;QAC9D,CAAC,CAAC;QACFwL,WAAW,GAAG1I,QAAQ,CAAC9C,IAAI,IAAI,EAAE;MACrC;MACA,IAAI8C,QAAQ,CAACL,MAAM,KAAKnD,SAAS,IAAI0E,QAAQ,CAAChE,IAAI,KAAK8C,QAAQ,CAAC9C,IAAI,EAAE;QAClE;QACA;QACA;QACA;QACAsL,gBAAgB,GAAGA,gBAAgB,CAAC5J,MAAM,CAAEe,MAAM,IAAKA,MAAM,IAAIK,QAAQ,CAACL,MAAM,CAAC;MACrF;MACA;AACZ;AACA;AACA;IACQ,CAAC,MACI,IAAI5D,OAAO,CAACmF,QAAQ,EAAElB,QAAQ,CAAC,EAAE;MAClCuI,cAAc,GAAG,EAAE;MACnBC,gBAAgB,GAAG,EAAE;MACrBC,WAAW,GAAGC,WAAW,GAAG,KAAK;IACrC;EACJ;EACA,OAAO;IACHI,KAAK,EAAEP,cAAc;IACrBtB,OAAO,EAAEuB,gBAAgB;IACzBO,EAAE,EAAEN,WAAW;IACfO,EAAE,EAAEN;EACR,CAAC;AACL,CAAC;AACD;AACA;AACA;AACA;AACA,MAAMO,cAAc,GAAGA,CAAC/H,QAAQ,EAAEgI,UAAU,KAAK;EAC7C,MAAMC,OAAO,GAAG;IAAExN,KAAK,EAAEuF,QAAQ,CAACvF,KAAK;IAAEE,IAAI,EAAEqF,QAAQ,CAACrF,IAAI;IAAED,GAAG,EAAEsF,QAAQ,CAACtF;EAAI,CAAC;EACjF;AACJ;AACA;AACA;EACI,IAAIsN,UAAU,KAAK1M,SAAS,KAAK0E,QAAQ,CAACvF,KAAK,KAAKuN,UAAU,CAACvN,KAAK,IAAIuF,QAAQ,CAACrF,IAAI,KAAKqN,UAAU,CAACrN,IAAI,CAAC,EAAE;IACxG,MAAMuN,MAAM,GAAG;MAAEzN,KAAK,EAAEuN,UAAU,CAACvN,KAAK;MAAEE,IAAI,EAAEqN,UAAU,CAACrN,IAAI;MAAED,GAAG,EAAEsN,UAAU,CAACtN;IAAI,CAAC;IACtF,MAAMyN,mBAAmB,GAAGvN,QAAQ,CAACsN,MAAM,EAAED,OAAO,CAAC;IACrD,OAAOE,mBAAmB,GACpB,CAACD,MAAM,EAAED,OAAO,EAAEpH,YAAY,CAACb,QAAQ,CAAC,CAAC,GACzC,CAACW,gBAAgB,CAACX,QAAQ,CAAC,EAAEiI,OAAO,EAAEC,MAAM,CAAC;EACvD;EACA,OAAO,CAACvH,gBAAgB,CAACX,QAAQ,CAAC,EAAEiI,OAAO,EAAEpH,YAAY,CAACb,QAAQ,CAAC,CAAC;AACxE,CAAC;AACD,MAAMoI,kBAAkB,GAAGA,CAACzM,MAAM,EAAEqE,QAAQ,EAAEnB,QAAQ,EAAEC,QAAQ,EAAEgD,WAAW,EAAEjF,aAAa,GAAG;EAC3FpC,KAAK,EAAE;AACX,CAAC,KAAK;EACF,MAAM;IAAEE;EAAK,CAAC,GAAGqF,QAAQ;EACzB,MAAMqI,MAAM,GAAG,EAAE;EACjB,IAAIvG,WAAW,KAAKxG,SAAS,EAAE;IAC3B,IAAIgN,eAAe,GAAGxG,WAAW;IACjC,IAAI,CAAChD,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACrE,KAAK,MAAMa,SAAS,EAAE;MACpFgN,eAAe,GAAGA,eAAe,CAAC5K,MAAM,CAAEjD,KAAK,IAAKA,KAAK,IAAIqE,QAAQ,CAACrE,KAAK,CAAC;IAChF;IACA,IAAI,CAACoE,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACpE,KAAK,MAAMa,SAAS,EAAE;MACpFgN,eAAe,GAAGA,eAAe,CAAC5K,MAAM,CAAEjD,KAAK,IAAKA,KAAK,IAAIoE,QAAQ,CAACpE,KAAK,CAAC;IAChF;IACA6N,eAAe,CAACC,OAAO,CAAEC,cAAc,IAAK;MACxC,MAAMrM,IAAI,GAAG,IAAIC,IAAI,CAAC,GAAGoM,cAAc,MAAM7N,IAAI,WAAW,CAAC;MAC7D,MAAM8N,WAAW,GAAG,IAAI3M,IAAI,CAACC,cAAc,CAACJ,MAAM,EAAEgG,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE/E,aAAa,CAAC,EAAE;QAAEsG,QAAQ,EAAE;MAAM,CAAC,CAAC,CAAC,CAACI,MAAM,CAACpH,IAAI,CAAC;MACtIkM,MAAM,CAACjK,IAAI,CAAC;QAAEsK,IAAI,EAAED,WAAW;QAAE1N,KAAK,EAAEyN;MAAe,CAAC,CAAC;IAC7D,CAAC,CAAC;EACN,CAAC,MACI;IACD,MAAMG,QAAQ,GAAG7J,QAAQ,IAAIA,QAAQ,CAACnE,IAAI,KAAKA,IAAI,GAAGmE,QAAQ,CAACrE,KAAK,GAAG,EAAE;IACzE,MAAMmO,QAAQ,GAAG/J,QAAQ,IAAIA,QAAQ,CAAClE,IAAI,KAAKA,IAAI,GAAGkE,QAAQ,CAACpE,KAAK,GAAG,CAAC;IACxE,KAAK,IAAI+D,CAAC,GAAGoK,QAAQ,EAAEpK,CAAC,IAAImK,QAAQ,EAAEnK,CAAC,EAAE,EAAE;MACvC;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACY,MAAMrC,IAAI,GAAG,IAAIC,IAAI,CAAC,GAAGoC,CAAC,MAAM7D,IAAI,WAAW,CAAC;MAChD,MAAM8N,WAAW,GAAG,IAAI3M,IAAI,CAACC,cAAc,CAACJ,MAAM,EAAEgG,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE/E,aAAa,CAAC,EAAE;QAAEsG,QAAQ,EAAE;MAAM,CAAC,CAAC,CAAC,CAACI,MAAM,CAACpH,IAAI,CAAC;MACtIkM,MAAM,CAACjK,IAAI,CAAC;QAAEsK,IAAI,EAAED,WAAW;QAAE1N,KAAK,EAAEyD;MAAE,CAAC,CAAC;IAChD;EACJ;EACA,OAAO6J,MAAM;AACjB,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMQ,gBAAgB,GAAGA,CAAClN,MAAM,EAAEqE,QAAQ,EAAEnB,QAAQ,EAAEC,QAAQ,EAAEiD,SAAS,EAAElF,aAAa,GAAG;EACvFnC,GAAG,EAAE;AACT,CAAC,KAAK;EACF,MAAM;IAAED,KAAK;IAAEE;EAAK,CAAC,GAAGqF,QAAQ;EAChC,MAAMiH,IAAI,GAAG,EAAE;EACf;AACJ;AACA;AACA;AACA;AACA;EACI,MAAMrG,cAAc,GAAGjE,iBAAiB,CAAClC,KAAK,EAAEE,IAAI,CAAC;EACrD,MAAMmO,MAAM,GAAG,CAAChK,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACpE,GAAG,MAAM,IAAI,IAAI,CAACoE,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACpE,GAAG,MAAMY,SAAS,IAAIwD,QAAQ,CAACnE,IAAI,KAAKA,IAAI,IAAImE,QAAQ,CAACrE,KAAK,KAAKA,KAAK,GAC9NqE,QAAQ,CAACpE,GAAG,GACZkG,cAAc;EACpB,MAAMmI,MAAM,GAAG,CAAClK,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACnE,GAAG,MAAM,IAAI,IAAI,CAACmE,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACnE,GAAG,MAAMY,SAAS,IAAIuD,QAAQ,CAAClE,IAAI,KAAKA,IAAI,IAAIkE,QAAQ,CAACpE,KAAK,KAAKA,KAAK,GAC9NoE,QAAQ,CAACnE,GAAG,GACZ,CAAC;EACP,IAAIqH,SAAS,KAAKzG,SAAS,EAAE;IACzB,IAAI0N,aAAa,GAAGjH,SAAS;IAC7BiH,aAAa,GAAGA,aAAa,CAACtL,MAAM,CAAEhD,GAAG,IAAKA,GAAG,IAAIqO,MAAM,IAAIrO,GAAG,IAAIoO,MAAM,CAAC;IAC7EE,aAAa,CAACT,OAAO,CAAEU,YAAY,IAAK;MACpC,MAAM9M,IAAI,GAAG,IAAIC,IAAI,CAAC,GAAG3B,KAAK,IAAIwO,YAAY,IAAItO,IAAI,WAAW,CAAC;MAClE,MAAMuO,SAAS,GAAG,IAAIpN,IAAI,CAACC,cAAc,CAACJ,MAAM,EAAEgG,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE/E,aAAa,CAAC,EAAE;QAAEsG,QAAQ,EAAE;MAAM,CAAC,CAAC,CAAC,CAACI,MAAM,CAACpH,IAAI,CAAC;MACpI8K,IAAI,CAAC7I,IAAI,CAAC;QAAEsK,IAAI,EAAEQ,SAAS;QAAEnO,KAAK,EAAEkO;MAAa,CAAC,CAAC;IACvD,CAAC,CAAC;EACN,CAAC,MACI;IACD,KAAK,IAAIzK,CAAC,GAAGuK,MAAM,EAAEvK,CAAC,IAAIsK,MAAM,EAAEtK,CAAC,EAAE,EAAE;MACnC,MAAMrC,IAAI,GAAG,IAAIC,IAAI,CAAC,GAAG3B,KAAK,IAAI+D,CAAC,IAAI7D,IAAI,WAAW,CAAC;MACvD,MAAMuO,SAAS,GAAG,IAAIpN,IAAI,CAACC,cAAc,CAACJ,MAAM,EAAEgG,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE/E,aAAa,CAAC,EAAE;QAAEsG,QAAQ,EAAE;MAAM,CAAC,CAAC,CAAC,CAACI,MAAM,CAACpH,IAAI,CAAC;MACpI8K,IAAI,CAAC7I,IAAI,CAAC;QAAEsK,IAAI,EAAEQ,SAAS;QAAEnO,KAAK,EAAEyD;MAAE,CAAC,CAAC;IAC5C;EACJ;EACA,OAAOyI,IAAI;AACf,CAAC;AACD,MAAMkC,iBAAiB,GAAGA,CAACxN,MAAM,EAAEqE,QAAQ,EAAEnB,QAAQ,EAAEC,QAAQ,EAAEkD,UAAU,KAAK;EAC5E,IAAIuC,EAAE,EAAEC,EAAE;EACV,IAAI4E,cAAc,GAAG,EAAE;EACvB,IAAIpH,UAAU,KAAK1G,SAAS,EAAE;IAC1B8N,cAAc,GAAGpH,UAAU;IAC3B,IAAI,CAAClD,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACnE,IAAI,MAAMW,SAAS,EAAE;MACnF8N,cAAc,GAAGA,cAAc,CAAC1L,MAAM,CAAE/C,IAAI,IAAKA,IAAI,IAAImE,QAAQ,CAACnE,IAAI,CAAC;IAC3E;IACA,IAAI,CAACkE,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAAClE,IAAI,MAAMW,SAAS,EAAE;MACnF8N,cAAc,GAAGA,cAAc,CAAC1L,MAAM,CAAE/C,IAAI,IAAKA,IAAI,IAAIkE,QAAQ,CAAClE,IAAI,CAAC;IAC3E;EACJ,CAAC,MACI;IACD,MAAM;MAAEA;IAAK,CAAC,GAAGqF,QAAQ;IACzB,MAAMqJ,OAAO,GAAG,CAAC9E,EAAE,GAAGzF,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACnE,IAAI,MAAM,IAAI,IAAI4J,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG5J,IAAI;IAC9H,MAAM2O,OAAO,GAAG,CAAC9E,EAAE,GAAG3F,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAAClE,IAAI,MAAM,IAAI,IAAI6J,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG7J,IAAI,GAAG,GAAG;IACpI,KAAK,IAAI6D,CAAC,GAAG8K,OAAO,EAAE9K,CAAC,IAAI6K,OAAO,EAAE7K,CAAC,EAAE,EAAE;MACrC4K,cAAc,CAAChL,IAAI,CAACI,CAAC,CAAC;IAC1B;EACJ;EACA,OAAO4K,cAAc,CAAC7L,GAAG,CAAE5C,IAAI,KAAM;IACjC+N,IAAI,EAAErE,OAAO,CAAC1I,MAAM,EAAE;MAAEhB,IAAI;MAAEF,KAAK,EAAEuF,QAAQ,CAACvF,KAAK;MAAEC,GAAG,EAAEsF,QAAQ,CAACtF;IAAI,CAAC,CAAC;IACzEK,KAAK,EAAEJ;EACX,CAAC,CAAC,CAAC;AACP,CAAC;AACD;AACA;AACA;AACA;AACA;AACA,MAAM4O,mBAAmB,GAAGA,CAAClI,YAAY,EAAEvC,QAAQ,KAAK;EACpD,IAAIuC,YAAY,CAAC5G,KAAK,KAAKqE,QAAQ,CAACrE,KAAK,IAAI4G,YAAY,CAAC1G,IAAI,KAAKmE,QAAQ,CAACnE,IAAI,EAAE;IAC9E,OAAO,CAAC0G,YAAY,CAAC;EACzB;EACA,OAAO,CAACA,YAAY,EAAE,GAAGkI,mBAAmB,CAAC1I,YAAY,CAACQ,YAAY,CAAC,EAAEvC,QAAQ,CAAC,CAAC;AACvF,CAAC;AACD;AACA;AACA;AACA;AACA;AACA,MAAM0K,yBAAyB,GAAGA,CAAC7N,MAAM,EAAEsD,UAAU,EAAEJ,QAAQ,EAAEC,QAAQ,EAAEiD,SAAS,EAAED,WAAW,KAAK;EAClG,IAAI2H,KAAK,GAAG,EAAE;EACd,IAAIpN,KAAK,GAAG,EAAE;EACd;AACJ;AACA;AACA;AACA;AACA;EACI,IAAIgM,MAAM,GAAGkB,mBAAmB,CAAC1K,QAAQ,EAAEC,QAAQ,CAAC;EACpD;AACJ;AACA;EACI,IAAIgD,WAAW,EAAE;IACbuG,MAAM,GAAGA,MAAM,CAAC3K,MAAM,CAAC,CAAC;MAAEjD;IAAM,CAAC,KAAKqH,WAAW,CAAC2F,QAAQ,CAAChN,KAAK,CAAC,CAAC;EACtE;EACA;AACJ;AACA;AACA;AACA;AACA;EACI4N,MAAM,CAACE,OAAO,CAAEmB,WAAW,IAAK;IAC5B,MAAMC,cAAc,GAAG;MAAElP,KAAK,EAAEiP,WAAW,CAACjP,KAAK;MAAEC,GAAG,EAAE,IAAI;MAAEC,IAAI,EAAE+O,WAAW,CAAC/O;IAAK,CAAC;IACtF,MAAMiP,SAAS,GAAGf,gBAAgB,CAAClN,MAAM,EAAEgO,cAAc,EAAE9K,QAAQ,EAAEC,QAAQ,EAAEiD,SAAS,EAAE;MACtFtH,KAAK,EAAE,OAAO;MACdC,GAAG,EAAE,SAAS;MACdsJ,OAAO,EAAE;IACb,CAAC,CAAC;IACF,MAAMpF,SAAS,GAAG,EAAE;IACpB,MAAMiL,eAAe,GAAG,EAAE;IAC1BD,SAAS,CAACrB,OAAO,CAAEuB,SAAS,IAAK;MAC7B,MAAMC,OAAO,GAAGzP,SAAS,CAACqH,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE+H,cAAc,CAAC,EAAE;QAAEjP,GAAG,EAAEoP,SAAS,CAAC/O;MAAM,CAAC,CAAC,EAAEkE,UAAU,CAAC;MACjH;AACZ;AACA;AACA;MACY4K,eAAe,CAACzL,IAAI,CAAC;QACjBsK,IAAI,EAAEqB,OAAO,GAAGnF,aAAa,CAACjJ,MAAM,CAAC,GAAGmO,SAAS,CAACpB,IAAI;QACtD3N,KAAK,EAAE,GAAG4O,cAAc,CAAChP,IAAI,IAAIgP,cAAc,CAAClP,KAAK,IAAIqP,SAAS,CAAC/O,KAAK;MAC5E,CAAC,CAAC;MACF;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACY6D,SAAS,CAACR,IAAI,CAAC;QACX3D,KAAK,EAAEkP,cAAc,CAAClP,KAAK;QAC3BE,IAAI,EAAEgP,cAAc,CAAChP,IAAI;QACzBD,GAAG,EAAEoP,SAAS,CAAC/O;MACnB,CAAC,CAAC;IACN,CAAC,CAAC;IACFsB,KAAK,GAAG,CAAC,GAAGA,KAAK,EAAE,GAAGuC,SAAS,CAAC;IAChC6K,KAAK,GAAG,CAAC,GAAGA,KAAK,EAAE,GAAGI,eAAe,CAAC;EAC1C,CAAC,CAAC;EACF,OAAO;IACHxN,KAAK;IACLoN;EACJ,CAAC;AACL,CAAC;AACD,MAAMO,kBAAkB,GAAGA,CAACrO,MAAM,EAAEqE,QAAQ,EAAEpE,SAAS,EAAEiD,QAAQ,EAAEC,QAAQ,EAAEmL,iBAAiB,EAAEC,mBAAmB,KAAK;EACpH,MAAM9C,iBAAiB,GAAG1L,YAAY,CAACC,MAAM,EAAEC,SAAS,CAAC;EACzD,MAAMuF,SAAS,GAAGzE,QAAQ,CAAC0K,iBAAiB,CAAC;EAC7C,MAAM;IAAEQ,KAAK;IAAE7B,OAAO;IAAE8B,EAAE;IAAEC;EAAG,CAAC,GAAGX,YAAY,CAACxL,MAAM,EAAEqE,QAAQ,EAAEoH,iBAAiB,EAAEvI,QAAQ,EAAEC,QAAQ,EAAEmL,iBAAiB,EAAEC,mBAAmB,CAAC;EAChJ,MAAMC,UAAU,GAAGvC,KAAK,CAACrK,GAAG,CAAEvB,IAAI,IAAK;IACnC,OAAO;MACH0M,IAAI,EAAE/E,gBAAgB,CAAC3H,IAAI,EAAEoL,iBAAiB,CAAC;MAC/CrM,KAAK,EAAEmG,oBAAoB,CAAClF,IAAI,EAAEmF,SAAS,EAAEnB,QAAQ,CAACtB,IAAI;IAC9D,CAAC;EACL,CAAC,CAAC;EACF,MAAM0L,YAAY,GAAGrE,OAAO,CAACxI,GAAG,CAAEkB,MAAM,IAAK;IACzC,OAAO;MACHiK,IAAI,EAAElF,cAAc,CAAC/E,MAAM,CAAC;MAC5B1D,KAAK,EAAE0D;IACX,CAAC;EACL,CAAC,CAAC;EACF,MAAM4L,cAAc,GAAG,EAAE;EACzB,IAAIxC,EAAE,IAAI,CAAC1G,SAAS,EAAE;IAClBkJ,cAAc,CAACjM,IAAI,CAAC;MAChBsK,IAAI,EAAElD,qBAAqB,CAAC7J,MAAM,EAAE,IAAI,CAAC;MACzCZ,KAAK,EAAE;IACX,CAAC,CAAC;EACN;EACA,IAAI+M,EAAE,IAAI,CAAC3G,SAAS,EAAE;IAClBkJ,cAAc,CAACjM,IAAI,CAAC;MAChBsK,IAAI,EAAElD,qBAAqB,CAAC7J,MAAM,EAAE,IAAI,CAAC;MACzCZ,KAAK,EAAE;IACX,CAAC,CAAC;EACN;EACA,OAAO;IACHuP,WAAW,EAAEF,YAAY;IACzBG,SAAS,EAAEJ,UAAU;IACrBK,aAAa,EAAEH;EACnB,CAAC;AACL,CAAC", "sources": ["./node_modules/@ionic/core/dist/esm/data-bb424ba8.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { p as printIonWarning } from './index-9b0d46f4.js';\n\n/**\n * Returns true if the selected day is equal to the reference day\n */\nconst isSameDay = (baseParts, compareParts) => {\n    return (baseParts.month === compareParts.month && baseParts.day === compareParts.day && baseParts.year === compareParts.year);\n};\n/**\n * Returns true is the selected day is before the reference day.\n */\nconst isBefore = (baseParts, compareParts) => {\n    return !!(baseParts.year < compareParts.year ||\n        (baseParts.year === compareParts.year && baseParts.month < compareParts.month) ||\n        (baseParts.year === compareParts.year &&\n            baseParts.month === compareParts.month &&\n            baseParts.day !== null &&\n            baseParts.day < compareParts.day));\n};\n/**\n * Returns true is the selected day is after the reference day.\n */\nconst isAfter = (baseParts, compareParts) => {\n    return !!(baseParts.year > compareParts.year ||\n        (baseParts.year === compareParts.year && baseParts.month > compareParts.month) ||\n        (baseParts.year === compareParts.year &&\n            baseParts.month === compareParts.month &&\n            baseParts.day !== null &&\n            baseParts.day > compareParts.day));\n};\nconst warnIfValueOutOfBounds = (value, min, max) => {\n    const valueArray = Array.isArray(value) ? value : [value];\n    for (const val of valueArray) {\n        if ((min !== undefined && isBefore(val, min)) || (max !== undefined && isAfter(val, max))) {\n            printIonWarning('The value provided to ion-datetime is out of bounds.\\n\\n' +\n                `Min: ${JSON.stringify(min)}\\n` +\n                `Max: ${JSON.stringify(max)}\\n` +\n                `Value: ${JSON.stringify(value)}`);\n            break;\n        }\n    }\n};\n\n/**\n * Determines if given year is a\n * leap year. Returns `true` if year\n * is a leap year. Returns `false`\n * otherwise.\n */\nconst isLeapYear = (year) => {\n    return (year % 4 === 0 && year % 100 !== 0) || year % 400 === 0;\n};\n/**\n * Determines the hour cycle for a user.\n * If the hour cycle is explicitly defined, just use that.\n * Otherwise, we try to derive it from either the specified\n * locale extension tags or from Intl.DateTimeFormat directly.\n */\nconst getHourCycle = (locale, hourCycle) => {\n    /**\n     * If developer has explicitly enabled 24-hour time\n     * then return early and do not look at the system default.\n     */\n    if (hourCycle !== undefined) {\n        return hourCycle;\n    }\n    /**\n     * If hourCycle was not specified, check the locale\n     * that is set on the user's device. We first check the\n     * Intl.DateTimeFormat hourCycle option as developers can encode this\n     * option into the locale string. Example: `en-US-u-hc-h23`\n     */\n    const formatted = new Intl.DateTimeFormat(locale, { hour: 'numeric' });\n    const options = formatted.resolvedOptions();\n    if (options.hourCycle !== undefined) {\n        return options.hourCycle;\n    }\n    /**\n     * If hourCycle is not specified (either through lack\n     * of browser support or locale information) then fall\n     * back to this slower hourCycle check.\n     */\n    const date = new Date('5/18/2021 00:00');\n    const parts = formatted.formatToParts(date);\n    const hour = parts.find((p) => p.type === 'hour');\n    if (!hour) {\n        throw new Error('Hour value not found from DateTimeFormat');\n    }\n    /**\n     * Midnight for h11 starts at 0:00am\n     * Midnight for h12 starts at 12:00am\n     * Midnight for h23 starts at 00:00\n     * Midnight for h24 starts at 24:00\n     */\n    switch (hour.value) {\n        case '0':\n            return 'h11';\n        case '12':\n            return 'h12';\n        case '00':\n            return 'h23';\n        case '24':\n            return 'h24';\n        default:\n            throw new Error(`Invalid hour cycle \"${hourCycle}\"`);\n    }\n};\n/**\n * Determine if the hour cycle uses a 24-hour format.\n * Returns true for h23 and h24. Returns false otherwise.\n * If you don't know the hourCycle, use getHourCycle above\n * and pass the result into this function.\n */\nconst is24Hour = (hourCycle) => {\n    return hourCycle === 'h23' || hourCycle === 'h24';\n};\n/**\n * Given a date object, returns the number\n * of days in that month.\n * Month value begin at 1, not 0.\n * i.e. January = month 1.\n */\nconst getNumDaysInMonth = (month, year) => {\n    return month === 4 || month === 6 || month === 9 || month === 11\n        ? 30\n        : month === 2\n            ? isLeapYear(year)\n                ? 29\n                : 28\n            : 31;\n};\n/**\n * Certain locales display month then year while\n * others display year then month.\n * We can use Intl.DateTimeFormat to determine\n * the ordering for each locale.\n * The formatOptions param can be used to customize\n * which pieces of a date to compare against the month\n * with. For example, some locales render dd/mm/yyyy\n * while others render mm/dd/yyyy. This function can be\n * used for variations of the same \"month first\" check.\n */\nconst isMonthFirstLocale = (locale, formatOptions = {\n    month: 'numeric',\n    year: 'numeric',\n}) => {\n    /**\n     * By setting month and year we guarantee that only\n     * month, year, and literal (slashes '/', for example)\n     * values are included in the formatToParts results.\n     *\n     * The ordering of the parts will be determined by\n     * the locale. So if the month is the first value,\n     * then we know month should be shown first. If the\n     * year is the first value, then we know year should be shown first.\n     *\n     * This ordering can be controlled by customizing the locale property.\n     */\n    const parts = new Intl.DateTimeFormat(locale, formatOptions).formatToParts(new Date());\n    return parts[0].type === 'month';\n};\n/**\n * Determines if the given locale formats the day period (am/pm) to the\n * left or right of the hour.\n * @param locale The locale to check.\n * @returns `true` if the locale formats the day period to the left of the hour.\n */\nconst isLocaleDayPeriodRTL = (locale) => {\n    const parts = new Intl.DateTimeFormat(locale, { hour: 'numeric' }).formatToParts(new Date());\n    return parts[0].type === 'dayPeriod';\n};\n\nconst ISO_8601_REGEXP = \n// eslint-disable-next-line no-useless-escape\n/^(\\d{4}|[+\\-]\\d{6})(?:-(\\d{2})(?:-(\\d{2}))?)?(?:T(\\d{2}):(\\d{2})(?::(\\d{2})(?:\\.(\\d{3}))?)?(?:(Z)|([+\\-])(\\d{2})(?::(\\d{2}))?)?)?$/;\n// eslint-disable-next-line no-useless-escape\nconst TIME_REGEXP = /^((\\d{2}):(\\d{2})(?::(\\d{2})(?:\\.(\\d{3}))?)?(?:(Z)|([+\\-])(\\d{2})(?::(\\d{2}))?)?)?$/;\n/**\n * Use to convert a string of comma separated numbers or\n * an array of numbers, and clean up any user input\n */\nconst convertToArrayOfNumbers = (input) => {\n    if (input === undefined) {\n        return;\n    }\n    let processedInput = input;\n    if (typeof input === 'string') {\n        // convert the string to an array of strings\n        // auto remove any whitespace and [] characters\n        processedInput = input.replace(/\\[|\\]|\\s/g, '').split(',');\n    }\n    let values;\n    if (Array.isArray(processedInput)) {\n        // ensure each value is an actual number in the returned array\n        values = processedInput.map((num) => parseInt(num, 10)).filter(isFinite);\n    }\n    else {\n        values = [processedInput];\n    }\n    return values;\n};\n/**\n * Extracts date information\n * from a .calendar-day element\n * into DatetimeParts.\n */\nconst getPartsFromCalendarDay = (el) => {\n    return {\n        month: parseInt(el.getAttribute('data-month'), 10),\n        day: parseInt(el.getAttribute('data-day'), 10),\n        year: parseInt(el.getAttribute('data-year'), 10),\n        dayOfWeek: parseInt(el.getAttribute('data-day-of-week'), 10),\n    };\n};\nfunction parseDate(val) {\n    if (Array.isArray(val)) {\n        const parsedArray = [];\n        for (const valStr of val) {\n            const parsedVal = parseDate(valStr);\n            /**\n             * If any of the values weren't parsed correctly, consider\n             * the entire batch incorrect. This simplifies the type\n             * signatures by having \"undefined\" be a general error case\n             * instead of returning (Datetime | undefined)[], which is\n             * harder for TS to perform type narrowing on.\n             */\n            if (!parsedVal) {\n                return undefined;\n            }\n            parsedArray.push(parsedVal);\n        }\n        return parsedArray;\n    }\n    // manually parse IS0 cuz Date.parse cannot be trusted\n    // ISO 8601 format: 1994-12-15T13:47:20Z\n    let parse = null;\n    if (val != null && val !== '') {\n        // try parsing for just time first, HH:MM\n        parse = TIME_REGEXP.exec(val);\n        if (parse) {\n            // adjust the array so it fits nicely with the datetime parse\n            parse.unshift(undefined, undefined);\n            parse[2] = parse[3] = undefined;\n        }\n        else {\n            // try parsing for full ISO datetime\n            parse = ISO_8601_REGEXP.exec(val);\n        }\n    }\n    if (parse === null) {\n        // wasn't able to parse the ISO datetime\n        printIonWarning(`Unable to parse date string: ${val}. Please provide a valid ISO 8601 datetime string.`);\n        return undefined;\n    }\n    // ensure all the parse values exist with at least 0\n    for (let i = 1; i < 8; i++) {\n        parse[i] = parse[i] !== undefined ? parseInt(parse[i], 10) : undefined;\n    }\n    // can also get second and millisecond from parse[6] and parse[7] if needed\n    return {\n        year: parse[1],\n        month: parse[2],\n        day: parse[3],\n        hour: parse[4],\n        minute: parse[5],\n        ampm: parse[4] < 12 ? 'am' : 'pm',\n    };\n}\nconst clampDate = (dateParts, minParts, maxParts) => {\n    if (minParts && isBefore(dateParts, minParts)) {\n        return minParts;\n    }\n    else if (maxParts && isAfter(dateParts, maxParts)) {\n        return maxParts;\n    }\n    return dateParts;\n};\n/**\n * Parses an hour and returns if the value is in the morning (am) or afternoon (pm).\n * @param hour The hour to format, should be 0-23\n * @returns `pm` if the hour is greater than or equal to 12, `am` if less than 12.\n */\nconst parseAmPm = (hour) => {\n    return hour >= 12 ? 'pm' : 'am';\n};\n/**\n * Takes a max date string and creates a DatetimeParts\n * object, filling in any missing information.\n * For example, max=\"2012\" would fill in the missing\n * month, day, hour, and minute information.\n */\nconst parseMaxParts = (max, todayParts) => {\n    const result = parseDate(max);\n    /**\n     * If min was not a valid date then return undefined.\n     */\n    if (result === undefined) {\n        return;\n    }\n    const { month, day, year, hour, minute } = result;\n    /**\n     * When passing in `max` or `min`, developers\n     * can pass in any ISO-8601 string. This means\n     * that not all of the date/time fields are defined.\n     * For example, passing max=\"2012\" is valid even though\n     * there is no month, day, hour, or minute data.\n     * However, all of this data is required when clamping the date\n     * so that the correct initial value can be selected. As a result,\n     * we need to fill in any omitted data with the min or max values.\n     */\n    const yearValue = year !== null && year !== void 0 ? year : todayParts.year;\n    const monthValue = month !== null && month !== void 0 ? month : 12;\n    return {\n        month: monthValue,\n        day: day !== null && day !== void 0 ? day : getNumDaysInMonth(monthValue, yearValue),\n        /**\n         * Passing in \"HH:mm\" is a valid ISO-8601\n         * string, so we just default to the current year\n         * in this case.\n         */\n        year: yearValue,\n        hour: hour !== null && hour !== void 0 ? hour : 23,\n        minute: minute !== null && minute !== void 0 ? minute : 59,\n    };\n};\n/**\n * Takes a min date string and creates a DatetimeParts\n * object, filling in any missing information.\n * For example, min=\"2012\" would fill in the missing\n * month, day, hour, and minute information.\n */\nconst parseMinParts = (min, todayParts) => {\n    const result = parseDate(min);\n    /**\n     * If min was not a valid date then return undefined.\n     */\n    if (result === undefined) {\n        return;\n    }\n    const { month, day, year, hour, minute } = result;\n    /**\n     * When passing in `max` or `min`, developers\n     * can pass in any ISO-8601 string. This means\n     * that not all of the date/time fields are defined.\n     * For example, passing max=\"2012\" is valid even though\n     * there is no month, day, hour, or minute data.\n     * However, all of this data is required when clamping the date\n     * so that the correct initial value can be selected. As a result,\n     * we need to fill in any omitted data with the min or max values.\n     */\n    return {\n        month: month !== null && month !== void 0 ? month : 1,\n        day: day !== null && day !== void 0 ? day : 1,\n        /**\n         * Passing in \"HH:mm\" is a valid ISO-8601\n         * string, so we just default to the current year\n         * in this case.\n         */\n        year: year !== null && year !== void 0 ? year : todayParts.year,\n        hour: hour !== null && hour !== void 0 ? hour : 0,\n        minute: minute !== null && minute !== void 0 ? minute : 0,\n    };\n};\n\nconst twoDigit = (val) => {\n    return ('0' + (val !== undefined ? Math.abs(val) : '0')).slice(-2);\n};\nconst fourDigit = (val) => {\n    return ('000' + (val !== undefined ? Math.abs(val) : '0')).slice(-4);\n};\nfunction convertDataToISO(data) {\n    if (Array.isArray(data)) {\n        return data.map((parts) => convertDataToISO(parts));\n    }\n    // https://www.w3.org/TR/NOTE-datetime\n    let rtn = '';\n    if (data.year !== undefined) {\n        // YYYY\n        rtn = fourDigit(data.year);\n        if (data.month !== undefined) {\n            // YYYY-MM\n            rtn += '-' + twoDigit(data.month);\n            if (data.day !== undefined) {\n                // YYYY-MM-DD\n                rtn += '-' + twoDigit(data.day);\n                if (data.hour !== undefined) {\n                    // YYYY-MM-DDTHH:mm:SS\n                    rtn += `T${twoDigit(data.hour)}:${twoDigit(data.minute)}:00`;\n                }\n            }\n        }\n    }\n    else if (data.hour !== undefined) {\n        // HH:mm\n        rtn = twoDigit(data.hour) + ':' + twoDigit(data.minute);\n    }\n    return rtn;\n}\n/**\n * Converts an 12 hour value to 24 hours.\n */\nconst convert12HourTo24Hour = (hour, ampm) => {\n    if (ampm === undefined) {\n        return hour;\n    }\n    /**\n     * If AM and 12am\n     * then return 00:00.\n     * Otherwise just return\n     * the hour since it is\n     * already in 24 hour format.\n     */\n    if (ampm === 'am') {\n        if (hour === 12) {\n            return 0;\n        }\n        return hour;\n    }\n    /**\n     * If PM and 12pm\n     * just return 12:00\n     * since it is already\n     * in 24 hour format.\n     * Otherwise add 12 hours\n     * to the time.\n     */\n    if (hour === 12) {\n        return 12;\n    }\n    return hour + 12;\n};\nconst getStartOfWeek = (refParts) => {\n    const { dayOfWeek } = refParts;\n    if (dayOfWeek === null || dayOfWeek === undefined) {\n        throw new Error('No day of week provided');\n    }\n    return subtractDays(refParts, dayOfWeek);\n};\nconst getEndOfWeek = (refParts) => {\n    const { dayOfWeek } = refParts;\n    if (dayOfWeek === null || dayOfWeek === undefined) {\n        throw new Error('No day of week provided');\n    }\n    return addDays(refParts, 6 - dayOfWeek);\n};\nconst getNextDay = (refParts) => {\n    return addDays(refParts, 1);\n};\nconst getPreviousDay = (refParts) => {\n    return subtractDays(refParts, 1);\n};\nconst getPreviousWeek = (refParts) => {\n    return subtractDays(refParts, 7);\n};\nconst getNextWeek = (refParts) => {\n    return addDays(refParts, 7);\n};\n/**\n * Given datetime parts, subtract\n * numDays from the date.\n * Returns a new DatetimeParts object\n * Currently can only go backward at most 1 month.\n */\nconst subtractDays = (refParts, numDays) => {\n    const { month, day, year } = refParts;\n    if (day === null) {\n        throw new Error('No day provided');\n    }\n    const workingParts = {\n        month,\n        day,\n        year,\n    };\n    workingParts.day = day - numDays;\n    /**\n     * If wrapping to previous month\n     * update days and decrement month\n     */\n    if (workingParts.day < 1) {\n        workingParts.month -= 1;\n    }\n    /**\n     * If moving to previous year, reset\n     * month to December and decrement year\n     */\n    if (workingParts.month < 1) {\n        workingParts.month = 12;\n        workingParts.year -= 1;\n    }\n    /**\n     * Determine how many days are in the current\n     * month\n     */\n    if (workingParts.day < 1) {\n        const daysInMonth = getNumDaysInMonth(workingParts.month, workingParts.year);\n        /**\n         * Take num days in month and add the\n         * number of underflow days. This number will\n         * be negative.\n         * Example: 1 week before Jan 2, 2021 is\n         * December 26, 2021 so:\n         * 2 - 7 = -5\n         * 31 + (-5) = 26\n         */\n        workingParts.day = daysInMonth + workingParts.day;\n    }\n    return workingParts;\n};\n/**\n * Given datetime parts, add\n * numDays to the date.\n * Returns a new DatetimeParts object\n * Currently can only go forward at most 1 month.\n */\nconst addDays = (refParts, numDays) => {\n    const { month, day, year } = refParts;\n    if (day === null) {\n        throw new Error('No day provided');\n    }\n    const workingParts = {\n        month,\n        day,\n        year,\n    };\n    const daysInMonth = getNumDaysInMonth(month, year);\n    workingParts.day = day + numDays;\n    /**\n     * If wrapping to next month\n     * update days and increment month\n     */\n    if (workingParts.day > daysInMonth) {\n        workingParts.day -= daysInMonth;\n        workingParts.month += 1;\n    }\n    /**\n     * If moving to next year, reset\n     * month to January and increment year\n     */\n    if (workingParts.month > 12) {\n        workingParts.month = 1;\n        workingParts.year += 1;\n    }\n    return workingParts;\n};\n/**\n * Given DatetimeParts, generate the previous month.\n */\nconst getPreviousMonth = (refParts) => {\n    /**\n     * If current month is January, wrap backwards\n     *  to December of the previous year.\n     */\n    const month = refParts.month === 1 ? 12 : refParts.month - 1;\n    const year = refParts.month === 1 ? refParts.year - 1 : refParts.year;\n    const numDaysInMonth = getNumDaysInMonth(month, year);\n    const day = numDaysInMonth < refParts.day ? numDaysInMonth : refParts.day;\n    return { month, year, day };\n};\n/**\n * Given DatetimeParts, generate the next month.\n */\nconst getNextMonth = (refParts) => {\n    /**\n     * If current month is December, wrap forwards\n     *  to January of the next year.\n     */\n    const month = refParts.month === 12 ? 1 : refParts.month + 1;\n    const year = refParts.month === 12 ? refParts.year + 1 : refParts.year;\n    const numDaysInMonth = getNumDaysInMonth(month, year);\n    const day = numDaysInMonth < refParts.day ? numDaysInMonth : refParts.day;\n    return { month, year, day };\n};\nconst changeYear = (refParts, yearDelta) => {\n    const month = refParts.month;\n    const year = refParts.year + yearDelta;\n    const numDaysInMonth = getNumDaysInMonth(month, year);\n    const day = numDaysInMonth < refParts.day ? numDaysInMonth : refParts.day;\n    return { month, year, day };\n};\n/**\n * Given DatetimeParts, generate the previous year.\n */\nconst getPreviousYear = (refParts) => {\n    return changeYear(refParts, -1);\n};\n/**\n * Given DatetimeParts, generate the next year.\n */\nconst getNextYear = (refParts) => {\n    return changeYear(refParts, 1);\n};\n/**\n * If PM, then internal value should\n * be converted to 24-hr time.\n * Does not apply when public\n * values are already 24-hr time.\n */\nconst getInternalHourValue = (hour, use24Hour, ampm) => {\n    if (use24Hour) {\n        return hour;\n    }\n    return convert12HourTo24Hour(hour, ampm);\n};\n/**\n * Unless otherwise stated, all month values are\n * 1 indexed instead of the typical 0 index in JS Date.\n * Example:\n *   January = Month 0 when using JS Date\n *   January = Month 1 when using this datetime util\n */\n/**\n * Given the current datetime parts and a new AM/PM value\n * calculate what the hour should be in 24-hour time format.\n * Used when toggling the AM/PM segment since we store our hours\n * in 24-hour time format internally.\n */\nconst calculateHourFromAMPM = (currentParts, newAMPM) => {\n    const { ampm: currentAMPM, hour } = currentParts;\n    let newHour = hour;\n    /**\n     * If going from AM --> PM, need to update the\n     *\n     */\n    if (currentAMPM === 'am' && newAMPM === 'pm') {\n        newHour = convert12HourTo24Hour(newHour, 'pm');\n        /**\n         * If going from PM --> AM\n         */\n    }\n    else if (currentAMPM === 'pm' && newAMPM === 'am') {\n        newHour = Math.abs(newHour - 12);\n    }\n    return newHour;\n};\n/**\n * Updates parts to ensure that month and day\n * values are valid. For days that do not exist,\n * or are outside the min/max bounds, the closest\n * valid day is used.\n */\nconst validateParts = (parts, minParts, maxParts) => {\n    const { month, day, year } = parts;\n    const partsCopy = clampDate(Object.assign({}, parts), minParts, maxParts);\n    const numDays = getNumDaysInMonth(month, year);\n    /**\n     * If the max number of days\n     * is greater than the day we want\n     * to set, update the DatetimeParts\n     * day field to be the max days.\n     */\n    if (day !== null && numDays < day) {\n        partsCopy.day = numDays;\n    }\n    /**\n     * If value is same day as min day,\n     * make sure the time value is in bounds.\n     */\n    if (minParts !== undefined && isSameDay(partsCopy, minParts)) {\n        /**\n         * If the hour is out of bounds,\n         * update both the hour and minute.\n         * This is done so that the new time\n         * is closest to what the user selected.\n         */\n        if (partsCopy.hour !== undefined && minParts.hour !== undefined) {\n            if (partsCopy.hour < minParts.hour) {\n                partsCopy.hour = minParts.hour;\n                partsCopy.minute = minParts.minute;\n                /**\n                 * If only the minute is out of bounds,\n                 * set it to the min minute.\n                 */\n            }\n            else if (partsCopy.hour === minParts.hour &&\n                partsCopy.minute !== undefined &&\n                minParts.minute !== undefined &&\n                partsCopy.minute < minParts.minute) {\n                partsCopy.minute = minParts.minute;\n            }\n        }\n    }\n    /**\n     * If value is same day as max day,\n     * make sure the time value is in bounds.\n     */\n    if (maxParts !== undefined && isSameDay(parts, maxParts)) {\n        /**\n         * If the hour is out of bounds,\n         * update both the hour and minute.\n         * This is done so that the new time\n         * is closest to what the user selected.\n         */\n        if (partsCopy.hour !== undefined && maxParts.hour !== undefined) {\n            if (partsCopy.hour > maxParts.hour) {\n                partsCopy.hour = maxParts.hour;\n                partsCopy.minute = maxParts.minute;\n                /**\n                 * If only the minute is out of bounds,\n                 * set it to the max minute.\n                 */\n            }\n            else if (partsCopy.hour === maxParts.hour &&\n                partsCopy.minute !== undefined &&\n                maxParts.minute !== undefined &&\n                partsCopy.minute > maxParts.minute) {\n                partsCopy.minute = maxParts.minute;\n            }\n        }\n    }\n    return partsCopy;\n};\n/**\n * Returns the closest date to refParts\n * that also meets the constraints of\n * the *Values params.\n */\nconst getClosestValidDate = ({ refParts, monthValues, dayValues, yearValues, hourValues, minuteValues, minParts, maxParts, }) => {\n    const { hour, minute, day, month, year } = refParts;\n    const copyParts = Object.assign(Object.assign({}, refParts), { dayOfWeek: undefined });\n    if (yearValues !== undefined) {\n        // Filters out years that are out of the min/max bounds\n        const filteredYears = yearValues.filter((year) => {\n            if (minParts !== undefined && year < minParts.year) {\n                return false;\n            }\n            if (maxParts !== undefined && year > maxParts.year) {\n                return false;\n            }\n            return true;\n        });\n        copyParts.year = findClosestValue(year, filteredYears);\n    }\n    if (monthValues !== undefined) {\n        // Filters out months that are out of the min/max bounds\n        const filteredMonths = monthValues.filter((month) => {\n            if (minParts !== undefined && copyParts.year === minParts.year && month < minParts.month) {\n                return false;\n            }\n            if (maxParts !== undefined && copyParts.year === maxParts.year && month > maxParts.month) {\n                return false;\n            }\n            return true;\n        });\n        copyParts.month = findClosestValue(month, filteredMonths);\n    }\n    // Day is nullable but cannot be undefined\n    if (day !== null && dayValues !== undefined) {\n        // Filters out days that are out of the min/max bounds\n        const filteredDays = dayValues.filter((day) => {\n            if (minParts !== undefined && isBefore(Object.assign(Object.assign({}, copyParts), { day }), minParts)) {\n                return false;\n            }\n            if (maxParts !== undefined && isAfter(Object.assign(Object.assign({}, copyParts), { day }), maxParts)) {\n                return false;\n            }\n            return true;\n        });\n        copyParts.day = findClosestValue(day, filteredDays);\n    }\n    if (hour !== undefined && hourValues !== undefined) {\n        // Filters out hours that are out of the min/max bounds\n        const filteredHours = hourValues.filter((hour) => {\n            if ((minParts === null || minParts === void 0 ? void 0 : minParts.hour) !== undefined && isSameDay(copyParts, minParts) && hour < minParts.hour) {\n                return false;\n            }\n            if ((maxParts === null || maxParts === void 0 ? void 0 : maxParts.hour) !== undefined && isSameDay(copyParts, maxParts) && hour > maxParts.hour) {\n                return false;\n            }\n            return true;\n        });\n        copyParts.hour = findClosestValue(hour, filteredHours);\n        copyParts.ampm = parseAmPm(copyParts.hour);\n    }\n    if (minute !== undefined && minuteValues !== undefined) {\n        // Filters out minutes that are out of the min/max bounds\n        const filteredMinutes = minuteValues.filter((minute) => {\n            if ((minParts === null || minParts === void 0 ? void 0 : minParts.minute) !== undefined &&\n                isSameDay(copyParts, minParts) &&\n                copyParts.hour === minParts.hour &&\n                minute < minParts.minute) {\n                return false;\n            }\n            if ((maxParts === null || maxParts === void 0 ? void 0 : maxParts.minute) !== undefined &&\n                isSameDay(copyParts, maxParts) &&\n                copyParts.hour === maxParts.hour &&\n                minute > maxParts.minute) {\n                return false;\n            }\n            return true;\n        });\n        copyParts.minute = findClosestValue(minute, filteredMinutes);\n    }\n    return copyParts;\n};\n/**\n * Finds the value in \"values\" that is\n * numerically closest to \"reference\".\n * This function assumes that \"values\" is\n * already sorted in ascending order.\n * @param reference The reference number to use\n * when finding the closest value\n * @param values The allowed values that will be\n * searched to find the closest value to \"reference\"\n */\nconst findClosestValue = (reference, values) => {\n    let closestValue = values[0];\n    let rank = Math.abs(closestValue - reference);\n    for (let i = 1; i < values.length; i++) {\n        const value = values[i];\n        /**\n         * This code prioritizes the first\n         * closest result. Given two values\n         * with the same distance from reference,\n         * this code will prioritize the smaller of\n         * the two values.\n         */\n        const valueRank = Math.abs(value - reference);\n        if (valueRank < rank) {\n            closestValue = value;\n            rank = valueRank;\n        }\n    }\n    return closestValue;\n};\n\nconst getFormattedDayPeriod = (dayPeriod) => {\n    if (dayPeriod === undefined) {\n        return '';\n    }\n    return dayPeriod.toUpperCase();\n};\n/**\n * Including time zone options may lead to the rendered text showing a\n * different time from what was selected in the Datetime, which could cause\n * confusion.\n */\nconst stripTimeZone = (formatOptions) => {\n    return Object.assign(Object.assign({}, formatOptions), { \n        /**\n         * Setting the time zone to UTC ensures that the value shown is always the\n         * same as what was selected and safeguards against older Safari bugs with\n         * Intl.DateTimeFormat.\n         */\n        timeZone: 'UTC', \n        /**\n         * We do not want to display the time zone name\n         */\n        timeZoneName: undefined });\n};\nconst getLocalizedTime = (locale, refParts, hourCycle, formatOptions = { hour: 'numeric', minute: 'numeric' }) => {\n    const timeParts = {\n        hour: refParts.hour,\n        minute: refParts.minute,\n    };\n    if (timeParts.hour === undefined || timeParts.minute === undefined) {\n        return 'Invalid Time';\n    }\n    return new Intl.DateTimeFormat(locale, Object.assign(Object.assign({}, stripTimeZone(formatOptions)), { \n        /**\n         * We use hourCycle here instead of hour12 due to:\n         * https://bugs.chromium.org/p/chromium/issues/detail?id=1347316&q=hour12&can=2\n         */\n        hourCycle })).format(new Date(convertDataToISO(Object.assign({ \n        /**\n         * JS uses a simplified ISO 8601 format which allows for\n         * date-only formats and date-time formats, but not\n         * time-only formats: https://tc39.es/ecma262/#sec-date-time-string-format\n         * As a result, developers who only pass a time will get\n         * an \"Invalid Date\" error. To account for this, we make sure that\n         * year/day/month values are set when passing to new Date().\n         * The Intl.DateTimeFormat call above only uses the hour/minute\n         * values, so passing these date values should have no impact\n         * on the time output.\n         */\n        year: 2023, day: 1, month: 1 }, timeParts)) + 'Z'));\n};\n/**\n * Adds padding to a time value so\n * that it is always 2 digits.\n */\nconst addTimePadding = (value) => {\n    const valueToString = value.toString();\n    if (valueToString.length > 1) {\n        return valueToString;\n    }\n    return `0${valueToString}`;\n};\n/**\n * Formats 24 hour times so that\n * it always has 2 digits. For\n * 12 hour times it ensures that\n * hour 0 is formatted as '12'.\n */\nconst getFormattedHour = (hour, hourCycle) => {\n    /**\n     * Midnight for h11 starts at 0:00am\n     * Midnight for h12 starts at 12:00am\n     * Midnight for h23 starts at 00:00\n     * Midnight for h24 starts at 24:00\n     */\n    if (hour === 0) {\n        switch (hourCycle) {\n            case 'h11':\n                return '0';\n            case 'h12':\n                return '12';\n            case 'h23':\n                return '00';\n            case 'h24':\n                return '24';\n            default:\n                throw new Error(`Invalid hour cycle \"${hourCycle}\"`);\n        }\n    }\n    const use24Hour = is24Hour(hourCycle);\n    /**\n     * h23 and h24 use 24 hour times.\n     */\n    if (use24Hour) {\n        return addTimePadding(hour);\n    }\n    return hour.toString();\n};\n/**\n * Generates an aria-label to be read by screen readers\n * given a local, a date, and whether or not that date is\n * today's date.\n */\nconst generateDayAriaLabel = (locale, today, refParts) => {\n    if (refParts.day === null) {\n        return null;\n    }\n    /**\n     * MM/DD/YYYY will return midnight in the user's timezone.\n     */\n    const date = getNormalizedDate(refParts);\n    const labelString = new Intl.DateTimeFormat(locale, {\n        weekday: 'long',\n        month: 'long',\n        day: 'numeric',\n        timeZone: 'UTC',\n    }).format(date);\n    /**\n     * If date is today, prepend \"Today\" so screen readers indicate\n     * that the date is today.\n     */\n    return today ? `Today, ${labelString}` : labelString;\n};\n/**\n * Given a locale and a date object,\n * return a formatted string that includes\n * the month name and full year.\n * Example: May 2021\n */\nconst getMonthAndYear = (locale, refParts) => {\n    const date = getNormalizedDate(refParts);\n    return new Intl.DateTimeFormat(locale, { month: 'long', year: 'numeric', timeZone: 'UTC' }).format(date);\n};\n/**\n * Given a locale and a date object,\n * return a formatted string that includes\n * the numeric day.\n * Note: Some languages will add literal characters\n * to the end. This function removes those literals.\n * Example: 29\n */\nconst getDay = (locale, refParts) => {\n    return getLocalizedDateTimeParts(locale, refParts, { day: 'numeric' }).find((obj) => obj.type === 'day').value;\n};\n/**\n * Given a locale and a date object,\n * return a formatted string that includes\n * the numeric year.\n * Example: 2022\n */\nconst getYear = (locale, refParts) => {\n    return getLocalizedDateTime(locale, refParts, { year: 'numeric' });\n};\n/**\n * Given reference parts, return a JS Date object\n * with a normalized time.\n */\nconst getNormalizedDate = (refParts) => {\n    var _a, _b, _c;\n    const timeString = refParts.hour !== undefined && refParts.minute !== undefined ? ` ${refParts.hour}:${refParts.minute}` : '';\n    /**\n     * We use / notation here for the date\n     * so we do not need to do extra work and pad values with zeroes.\n     * Values such as YYYY-MM are still valid, so\n     * we add fallback values so we still get\n     * a valid date otherwise we will pass in a string\n     * like \"//2023\". Some browsers, such as Chrome, will\n     * account for this and still return a valid date. However,\n     * this is not a consistent behavior across all browsers.\n     */\n    return new Date(`${(_a = refParts.month) !== null && _a !== void 0 ? _a : 1}/${(_b = refParts.day) !== null && _b !== void 0 ? _b : 1}/${(_c = refParts.year) !== null && _c !== void 0 ? _c : 2023}${timeString} GMT+0000`);\n};\n/**\n * Given a locale, DatetimeParts, and options\n * format the DatetimeParts according to the options\n * and locale combination. This returns a string. If\n * you want an array of the individual pieces\n * that make up the localized date string, use\n * getLocalizedDateTimeParts.\n */\nconst getLocalizedDateTime = (locale, refParts, options) => {\n    const date = getNormalizedDate(refParts);\n    return getDateTimeFormat(locale, stripTimeZone(options)).format(date);\n};\n/**\n * Given a locale, DatetimeParts, and options\n * format the DatetimeParts according to the options\n * and locale combination. This returns an array of\n * each piece of the date.\n */\nconst getLocalizedDateTimeParts = (locale, refParts, options) => {\n    const date = getNormalizedDate(refParts);\n    return getDateTimeFormat(locale, options).formatToParts(date);\n};\n/**\n * Wrapper function for Intl.DateTimeFormat.\n * Allows developers to apply an allowed format to DatetimeParts.\n * This function also has built in safeguards for older browser bugs\n * with Intl.DateTimeFormat.\n */\nconst getDateTimeFormat = (locale, options) => {\n    return new Intl.DateTimeFormat(locale, Object.assign(Object.assign({}, options), { timeZone: 'UTC' }));\n};\n/**\n * Gets a localized version of \"Today\"\n * Falls back to \"Today\" in English for\n * browsers that do not support RelativeTimeFormat.\n */\nconst getTodayLabel = (locale) => {\n    if ('RelativeTimeFormat' in Intl) {\n        const label = new Intl.RelativeTimeFormat(locale, { numeric: 'auto' }).format(0, 'day');\n        return label.charAt(0).toUpperCase() + label.slice(1);\n    }\n    else {\n        return 'Today';\n    }\n};\n/**\n * When calling toISOString(), the browser\n * will convert the date to UTC time by either adding\n * or subtracting the time zone offset.\n * To work around this, we need to either add\n * or subtract the time zone offset to the Date\n * object prior to calling toISOString().\n * This allows us to get an ISO string\n * that is in the user's time zone.\n *\n * Example:\n * Time zone offset is 240\n * Meaning: The browser needs to add 240 minutes\n * to the Date object to get UTC time.\n * What Ionic does: We subtract 240 minutes\n * from the Date object. The browser then adds\n * 240 minutes in toISOString(). The result\n * is a time that is in the user's time zone\n * and not UTC.\n *\n * Note: Some timezones include minute adjustments\n * such as 30 or 45 minutes. This is why we use setMinutes\n * instead of setHours.\n * Example: India Standard Time\n * Timezone offset: -330 = -5.5 hours.\n *\n * List of timezones with 30 and 45 minute timezones:\n * https://www.timeanddate.com/time/time-zones-interesting.html\n */\nconst removeDateTzOffset = (date) => {\n    const tzOffset = date.getTimezoneOffset();\n    date.setMinutes(date.getMinutes() - tzOffset);\n    return date;\n};\nconst DATE_AM = removeDateTzOffset(new Date('2022T01:00'));\nconst DATE_PM = removeDateTzOffset(new Date('2022T13:00'));\n/**\n * Formats the locale's string representation of the day period (am/pm) for a given\n * ref parts day period.\n *\n * @param locale The locale to format the day period in.\n * @param value The date string, in ISO format.\n * @returns The localized day period (am/pm) representation of the given value.\n */\nconst getLocalizedDayPeriod = (locale, dayPeriod) => {\n    const date = dayPeriod === 'am' ? DATE_AM : DATE_PM;\n    const localizedDayPeriod = new Intl.DateTimeFormat(locale, {\n        hour: 'numeric',\n        timeZone: 'UTC',\n    })\n        .formatToParts(date)\n        .find((part) => part.type === 'dayPeriod');\n    if (localizedDayPeriod) {\n        return localizedDayPeriod.value;\n    }\n    return getFormattedDayPeriod(dayPeriod);\n};\n/**\n * Formats the datetime's value to a string, for use in the native input.\n *\n * @param value The value to format, either an ISO string or an array thereof.\n */\nconst formatValue = (value) => {\n    return Array.isArray(value) ? value.join(',') : value;\n};\n\n/**\n * Returns the current date as\n * an ISO string in the user's\n * time zone.\n */\nconst getToday = () => {\n    /**\n     * ion-datetime intentionally does not\n     * parse time zones/do automatic time zone\n     * conversion when accepting user input.\n     * However when we get today's date string,\n     * we want it formatted relative to the user's\n     * time zone.\n     *\n     * When calling toISOString(), the browser\n     * will convert the date to UTC time by either adding\n     * or subtracting the time zone offset.\n     * To work around this, we need to either add\n     * or subtract the time zone offset to the Date\n     * object prior to calling toISOString().\n     * This allows us to get an ISO string\n     * that is in the user's time zone.\n     */\n    return removeDateTzOffset(new Date()).toISOString();\n};\nconst minutes = [\n    0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31,\n    32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59,\n];\n// h11 hour system uses 0-11. Midnight starts at 0:00am.\nconst hour11 = [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11];\n// h12 hour system uses 0-12. Midnight starts at 12:00am.\nconst hour12 = [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11];\n// h23 hour system uses 0-23. Midnight starts at 0:00.\nconst hour23 = [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23];\n// h24 hour system uses 1-24. Midnight starts at 24:00.\nconst hour24 = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 0];\n/**\n * Given a locale and a mode,\n * return an array with formatted days\n * of the week. iOS should display days\n * such as \"Mon\" or \"Tue\".\n * MD should display days such as \"M\"\n * or \"T\".\n */\nconst getDaysOfWeek = (locale, mode, firstDayOfWeek = 0) => {\n    /**\n     * Nov 1st, 2020 starts on a Sunday.\n     * ion-datetime assumes weeks start on Sunday,\n     * but is configurable via `firstDayOfWeek`.\n     */\n    const weekdayFormat = mode === 'ios' ? 'short' : 'narrow';\n    const intl = new Intl.DateTimeFormat(locale, { weekday: weekdayFormat });\n    const startDate = new Date('11/01/2020');\n    const daysOfWeek = [];\n    /**\n     * For each day of the week,\n     * get the day name.\n     */\n    for (let i = firstDayOfWeek; i < firstDayOfWeek + 7; i++) {\n        const currentDate = new Date(startDate);\n        currentDate.setDate(currentDate.getDate() + i);\n        daysOfWeek.push(intl.format(currentDate));\n    }\n    return daysOfWeek;\n};\n/**\n * Returns an array containing all of the\n * days in a month for a given year. Values are\n * aligned with a week calendar starting on\n * the firstDayOfWeek value (Sunday by default)\n * using null values.\n */\nconst getDaysOfMonth = (month, year, firstDayOfWeek) => {\n    const numDays = getNumDaysInMonth(month, year);\n    const firstOfMonth = new Date(`${month}/1/${year}`).getDay();\n    /**\n     * To get the first day of the month aligned on the correct\n     * day of the week, we need to determine how many \"filler\" days\n     * to generate. These filler days as empty/disabled buttons\n     * that fill the space of the days of the week before the first\n     * of the month.\n     *\n     * There are two cases here:\n     *\n     * 1. If firstOfMonth = 4, firstDayOfWeek = 0 then the offset\n     * is (4 - (0 + 1)) = 3. Since the offset loop goes from 0 to 3 inclusive,\n     * this will generate 4 filler days (0, 1, 2, 3), and then day of week 4 will have\n     * the first day of the month.\n     *\n     * 2. If firstOfMonth = 2, firstDayOfWeek = 4 then the offset\n     * is (6 - (4 - 2)) = 4. Since the offset loop goes from 0 to 4 inclusive,\n     * this will generate 5 filler days (0, 1, 2, 3, 4), and then day of week 5 will have\n     * the first day of the month.\n     */\n    const offset = firstOfMonth >= firstDayOfWeek ? firstOfMonth - (firstDayOfWeek + 1) : 6 - (firstDayOfWeek - firstOfMonth);\n    let days = [];\n    for (let i = 1; i <= numDays; i++) {\n        days.push({ day: i, dayOfWeek: (offset + i) % 7 });\n    }\n    for (let i = 0; i <= offset; i++) {\n        days = [{ day: null, dayOfWeek: null }, ...days];\n    }\n    return days;\n};\n/**\n * Returns an array of pre-defined hour\n * values based on the provided hourCycle.\n */\nconst getHourData = (hourCycle) => {\n    switch (hourCycle) {\n        case 'h11':\n            return hour11;\n        case 'h12':\n            return hour12;\n        case 'h23':\n            return hour23;\n        case 'h24':\n            return hour24;\n        default:\n            throw new Error(`Invalid hour cycle \"${hourCycle}\"`);\n    }\n};\n/**\n * Given a local, reference datetime parts and option\n * max/min bound datetime parts, calculate the acceptable\n * hour and minute values according to the bounds and locale.\n */\nconst generateTime = (locale, refParts, hourCycle = 'h12', minParts, maxParts, hourValues, minuteValues) => {\n    const computedHourCycle = getHourCycle(locale, hourCycle);\n    const use24Hour = is24Hour(computedHourCycle);\n    let processedHours = getHourData(computedHourCycle);\n    let processedMinutes = minutes;\n    let isAMAllowed = true;\n    let isPMAllowed = true;\n    if (hourValues) {\n        processedHours = processedHours.filter((hour) => hourValues.includes(hour));\n    }\n    if (minuteValues) {\n        processedMinutes = processedMinutes.filter((minute) => minuteValues.includes(minute));\n    }\n    if (minParts) {\n        /**\n         * If ref day is the same as the\n         * minimum allowed day, filter hour/minute\n         * values according to min hour and minute.\n         */\n        if (isSameDay(refParts, minParts)) {\n            /**\n             * Users may not always set the hour/minute for\n             * min value (i.e. 2021-06-02) so we should allow\n             * all hours/minutes in that case.\n             */\n            if (minParts.hour !== undefined) {\n                processedHours = processedHours.filter((hour) => {\n                    const convertedHour = refParts.ampm === 'pm' ? (hour + 12) % 24 : hour;\n                    return (use24Hour ? hour : convertedHour) >= minParts.hour;\n                });\n                isAMAllowed = minParts.hour < 13;\n            }\n            if (minParts.minute !== undefined) {\n                /**\n                 * The minimum minute range should not be enforced when\n                 * the hour is greater than the min hour.\n                 *\n                 * For example with a minimum range of 09:30, users\n                 * should be able to select 10:00-10:29 and beyond.\n                 */\n                let isPastMinHour = false;\n                if (minParts.hour !== undefined && refParts.hour !== undefined) {\n                    if (refParts.hour > minParts.hour) {\n                        isPastMinHour = true;\n                    }\n                }\n                processedMinutes = processedMinutes.filter((minute) => {\n                    if (isPastMinHour) {\n                        return true;\n                    }\n                    return minute >= minParts.minute;\n                });\n            }\n            /**\n             * If ref day is before minimum\n             * day do not render any hours/minute values\n             */\n        }\n        else if (isBefore(refParts, minParts)) {\n            processedHours = [];\n            processedMinutes = [];\n            isAMAllowed = isPMAllowed = false;\n        }\n    }\n    if (maxParts) {\n        /**\n         * If ref day is the same as the\n         * maximum allowed day, filter hour/minute\n         * values according to max hour and minute.\n         */\n        if (isSameDay(refParts, maxParts)) {\n            /**\n             * Users may not always set the hour/minute for\n             * max value (i.e. 2021-06-02) so we should allow\n             * all hours/minutes in that case.\n             */\n            if (maxParts.hour !== undefined) {\n                processedHours = processedHours.filter((hour) => {\n                    const convertedHour = refParts.ampm === 'pm' ? (hour + 12) % 24 : hour;\n                    return (use24Hour ? hour : convertedHour) <= maxParts.hour;\n                });\n                isPMAllowed = maxParts.hour >= 12;\n            }\n            if (maxParts.minute !== undefined && refParts.hour === maxParts.hour) {\n                // The available minutes should only be filtered when the hour is the same as the max hour.\n                // For example if the max hour is 10:30 and the current hour is 10:00,\n                // users should be able to select 00-30 minutes.\n                // If the current hour is 09:00, users should be able to select 00-60 minutes.\n                processedMinutes = processedMinutes.filter((minute) => minute <= maxParts.minute);\n            }\n            /**\n             * If ref day is after minimum\n             * day do not render any hours/minute values\n             */\n        }\n        else if (isAfter(refParts, maxParts)) {\n            processedHours = [];\n            processedMinutes = [];\n            isAMAllowed = isPMAllowed = false;\n        }\n    }\n    return {\n        hours: processedHours,\n        minutes: processedMinutes,\n        am: isAMAllowed,\n        pm: isPMAllowed,\n    };\n};\n/**\n * Given DatetimeParts, generate the previous,\n * current, and and next months.\n */\nconst generateMonths = (refParts, forcedDate) => {\n    const current = { month: refParts.month, year: refParts.year, day: refParts.day };\n    /**\n     * If we're forcing a month to appear, and it's different from the current month,\n     * ensure it appears by replacing the next or previous month as appropriate.\n     */\n    if (forcedDate !== undefined && (refParts.month !== forcedDate.month || refParts.year !== forcedDate.year)) {\n        const forced = { month: forcedDate.month, year: forcedDate.year, day: forcedDate.day };\n        const forcedMonthIsBefore = isBefore(forced, current);\n        return forcedMonthIsBefore\n            ? [forced, current, getNextMonth(refParts)]\n            : [getPreviousMonth(refParts), current, forced];\n    }\n    return [getPreviousMonth(refParts), current, getNextMonth(refParts)];\n};\nconst getMonthColumnData = (locale, refParts, minParts, maxParts, monthValues, formatOptions = {\n    month: 'long',\n}) => {\n    const { year } = refParts;\n    const months = [];\n    if (monthValues !== undefined) {\n        let processedMonths = monthValues;\n        if ((maxParts === null || maxParts === void 0 ? void 0 : maxParts.month) !== undefined) {\n            processedMonths = processedMonths.filter((month) => month <= maxParts.month);\n        }\n        if ((minParts === null || minParts === void 0 ? void 0 : minParts.month) !== undefined) {\n            processedMonths = processedMonths.filter((month) => month >= minParts.month);\n        }\n        processedMonths.forEach((processedMonth) => {\n            const date = new Date(`${processedMonth}/1/${year} GMT+0000`);\n            const monthString = new Intl.DateTimeFormat(locale, Object.assign(Object.assign({}, formatOptions), { timeZone: 'UTC' })).format(date);\n            months.push({ text: monthString, value: processedMonth });\n        });\n    }\n    else {\n        const maxMonth = maxParts && maxParts.year === year ? maxParts.month : 12;\n        const minMonth = minParts && minParts.year === year ? minParts.month : 1;\n        for (let i = minMonth; i <= maxMonth; i++) {\n            /**\n             *\n             * There is a bug on iOS 14 where\n             * Intl.DateTimeFormat takes into account\n             * the local timezone offset when formatting dates.\n             *\n             * Forcing the timezone to 'UTC' fixes the issue. However,\n             * we should keep this workaround as it is safer. In the event\n             * this breaks in another browser, we will not be impacted\n             * because all dates will be interpreted in UTC.\n             *\n             * Example:\n             * new Intl.DateTimeFormat('en-US', { month: 'long' }).format(new Date('Sat Apr 01 2006 00:00:00 GMT-0400 (EDT)')) // \"March\"\n             * new Intl.DateTimeFormat('en-US', { month: 'long', timeZone: 'UTC' }).format(new Date('Sat Apr 01 2006 00:00:00 GMT-0400 (EDT)')) // \"April\"\n             *\n             * In certain timezones, iOS 14 shows the wrong\n             * date for .toUTCString(). To combat this, we\n             * force all of the timezones to GMT+0000 (UTC).\n             *\n             * Example:\n             * Time Zone: Central European Standard Time\n             * new Date('1/1/1992').toUTCString() // \"Tue, 31 Dec 1991 23:00:00 GMT\"\n             * new Date('1/1/1992 GMT+0000').toUTCString() // \"Wed, 01 Jan 1992 00:00:00 GMT\"\n             */\n            const date = new Date(`${i}/1/${year} GMT+0000`);\n            const monthString = new Intl.DateTimeFormat(locale, Object.assign(Object.assign({}, formatOptions), { timeZone: 'UTC' })).format(date);\n            months.push({ text: monthString, value: i });\n        }\n    }\n    return months;\n};\n/**\n * Returns information regarding\n * selectable dates (i.e 1st, 2nd, 3rd, etc)\n * within a reference month.\n * @param locale The locale to format the date with\n * @param refParts The reference month/year to generate dates for\n * @param minParts The minimum bound on the date that can be returned\n * @param maxParts The maximum bound on the date that can be returned\n * @param dayValues The allowed date values\n * @returns Date data to be used in ion-picker-column-internal\n */\nconst getDayColumnData = (locale, refParts, minParts, maxParts, dayValues, formatOptions = {\n    day: 'numeric',\n}) => {\n    const { month, year } = refParts;\n    const days = [];\n    /**\n     * If we have max/min bounds that in the same\n     * month/year as the refParts, we should\n     * use the define day as the max/min day.\n     * Otherwise, fallback to the max/min days in a month.\n     */\n    const numDaysInMonth = getNumDaysInMonth(month, year);\n    const maxDay = (maxParts === null || maxParts === void 0 ? void 0 : maxParts.day) !== null && (maxParts === null || maxParts === void 0 ? void 0 : maxParts.day) !== undefined && maxParts.year === year && maxParts.month === month\n        ? maxParts.day\n        : numDaysInMonth;\n    const minDay = (minParts === null || minParts === void 0 ? void 0 : minParts.day) !== null && (minParts === null || minParts === void 0 ? void 0 : minParts.day) !== undefined && minParts.year === year && minParts.month === month\n        ? minParts.day\n        : 1;\n    if (dayValues !== undefined) {\n        let processedDays = dayValues;\n        processedDays = processedDays.filter((day) => day >= minDay && day <= maxDay);\n        processedDays.forEach((processedDay) => {\n            const date = new Date(`${month}/${processedDay}/${year} GMT+0000`);\n            const dayString = new Intl.DateTimeFormat(locale, Object.assign(Object.assign({}, formatOptions), { timeZone: 'UTC' })).format(date);\n            days.push({ text: dayString, value: processedDay });\n        });\n    }\n    else {\n        for (let i = minDay; i <= maxDay; i++) {\n            const date = new Date(`${month}/${i}/${year} GMT+0000`);\n            const dayString = new Intl.DateTimeFormat(locale, Object.assign(Object.assign({}, formatOptions), { timeZone: 'UTC' })).format(date);\n            days.push({ text: dayString, value: i });\n        }\n    }\n    return days;\n};\nconst getYearColumnData = (locale, refParts, minParts, maxParts, yearValues) => {\n    var _a, _b;\n    let processedYears = [];\n    if (yearValues !== undefined) {\n        processedYears = yearValues;\n        if ((maxParts === null || maxParts === void 0 ? void 0 : maxParts.year) !== undefined) {\n            processedYears = processedYears.filter((year) => year <= maxParts.year);\n        }\n        if ((minParts === null || minParts === void 0 ? void 0 : minParts.year) !== undefined) {\n            processedYears = processedYears.filter((year) => year >= minParts.year);\n        }\n    }\n    else {\n        const { year } = refParts;\n        const maxYear = (_a = maxParts === null || maxParts === void 0 ? void 0 : maxParts.year) !== null && _a !== void 0 ? _a : year;\n        const minYear = (_b = minParts === null || minParts === void 0 ? void 0 : minParts.year) !== null && _b !== void 0 ? _b : year - 100;\n        for (let i = minYear; i <= maxYear; i++) {\n            processedYears.push(i);\n        }\n    }\n    return processedYears.map((year) => ({\n        text: getYear(locale, { year, month: refParts.month, day: refParts.day }),\n        value: year,\n    }));\n};\n/**\n * Given a starting date and an upper bound,\n * this functions returns an array of all\n * month objects in that range.\n */\nconst getAllMonthsInRange = (currentParts, maxParts) => {\n    if (currentParts.month === maxParts.month && currentParts.year === maxParts.year) {\n        return [currentParts];\n    }\n    return [currentParts, ...getAllMonthsInRange(getNextMonth(currentParts), maxParts)];\n};\n/**\n * Creates and returns picker items\n * that represent the days in a month.\n * Example: \"Thu, Jun 2\"\n */\nconst getCombinedDateColumnData = (locale, todayParts, minParts, maxParts, dayValues, monthValues) => {\n    let items = [];\n    let parts = [];\n    /**\n     * Get all month objects from the min date\n     * to the max date. Note: Do not use getMonthColumnData\n     * as that function only generates dates within a\n     * single year.\n     */\n    let months = getAllMonthsInRange(minParts, maxParts);\n    /**\n     * Filter out any disallowed month values.\n     */\n    if (monthValues) {\n        months = months.filter(({ month }) => monthValues.includes(month));\n    }\n    /**\n     * Get all of the days in the month.\n     * From there, generate an array where\n     * each item has the month, date, and day\n     * of work as the text.\n     */\n    months.forEach((monthObject) => {\n        const referenceMonth = { month: monthObject.month, day: null, year: monthObject.year };\n        const monthDays = getDayColumnData(locale, referenceMonth, minParts, maxParts, dayValues, {\n            month: 'short',\n            day: 'numeric',\n            weekday: 'short',\n        });\n        const dateParts = [];\n        const dateColumnItems = [];\n        monthDays.forEach((dayObject) => {\n            const isToday = isSameDay(Object.assign(Object.assign({}, referenceMonth), { day: dayObject.value }), todayParts);\n            /**\n             * Today's date should read as \"Today\" (localized)\n             * not the actual date string\n             */\n            dateColumnItems.push({\n                text: isToday ? getTodayLabel(locale) : dayObject.text,\n                value: `${referenceMonth.year}-${referenceMonth.month}-${dayObject.value}`,\n            });\n            /**\n             * When selecting a date in the wheel picker\n             * we need access to the raw datetime parts data.\n             * The picker column only accepts values of\n             * type string or number, so we need to return\n             * two sets of data: A data set to be passed\n             * to the picker column, and a data set to\n             * be used to reference the raw data when\n             * updating the picker column value.\n             */\n            dateParts.push({\n                month: referenceMonth.month,\n                year: referenceMonth.year,\n                day: dayObject.value,\n            });\n        });\n        parts = [...parts, ...dateParts];\n        items = [...items, ...dateColumnItems];\n    });\n    return {\n        parts,\n        items,\n    };\n};\nconst getTimeColumnsData = (locale, refParts, hourCycle, minParts, maxParts, allowedHourValues, allowedMinuteValues) => {\n    const computedHourCycle = getHourCycle(locale, hourCycle);\n    const use24Hour = is24Hour(computedHourCycle);\n    const { hours, minutes, am, pm } = generateTime(locale, refParts, computedHourCycle, minParts, maxParts, allowedHourValues, allowedMinuteValues);\n    const hoursItems = hours.map((hour) => {\n        return {\n            text: getFormattedHour(hour, computedHourCycle),\n            value: getInternalHourValue(hour, use24Hour, refParts.ampm),\n        };\n    });\n    const minutesItems = minutes.map((minute) => {\n        return {\n            text: addTimePadding(minute),\n            value: minute,\n        };\n    });\n    const dayPeriodItems = [];\n    if (am && !use24Hour) {\n        dayPeriodItems.push({\n            text: getLocalizedDayPeriod(locale, 'am'),\n            value: 'am',\n        });\n    }\n    if (pm && !use24Hour) {\n        dayPeriodItems.push({\n            text: getLocalizedDayPeriod(locale, 'pm'),\n            value: 'pm',\n        });\n    }\n    return {\n        minutesData: minutesItems,\n        hoursData: hoursItems,\n        dayPeriodData: dayPeriodItems,\n    };\n};\n\nexport { getNumDaysInMonth as A, getCombinedDateColumnData as B, getMonthColumnData as C, getDayColumnData as D, getYearColumnData as E, isMonthFirstLocale as F, getTimeColumnsData as G, isLocaleDayPeriodRTL as H, getDaysOfWeek as I, getMonthAndYear as J, getDaysOfMonth as K, getHourCycle as L, getLocalizedTime as M, getLocalizedDateTime as N, formatValue as O, clampDate as P, parseAmPm as Q, calculateHourFromAMPM as R, getDay as a, isAfter as b, isSameDay as c, getPreviousMonth as d, getNextMonth as e, getPartsFromCalendarDay as f, generateDayAriaLabel as g, getNextYear as h, isBefore as i, getPreviousYear as j, getEndOfWeek as k, getStartOfWeek as l, getPreviousDay as m, getNextDay as n, getPreviousWeek as o, getNextWeek as p, parseMinParts as q, parseMaxParts as r, parseDate as s, convertToArrayOfNumbers as t, convertDataToISO as u, validateParts as v, warnIfValueOutOfBounds as w, getToday as x, getClosestValidDate as y, generateMonths as z };\n"], "names": ["p", "printIonWarning", "isSameDay", "baseParts", "compareParts", "month", "day", "year", "isBefore", "isAfter", "warnIfValueOutOfBounds", "value", "min", "max", "valueArray", "Array", "isArray", "val", "undefined", "JSON", "stringify", "isLeapYear", "getHourCycle", "locale", "hourCycle", "formatted", "Intl", "DateTimeFormat", "hour", "options", "resolvedOptions", "date", "Date", "parts", "formatToParts", "find", "type", "Error", "is24Hour", "getNumDaysInMonth", "isMonthFirstLocale", "formatOptions", "isLocaleDayPeriodRTL", "ISO_8601_REGEXP", "TIME_REGEXP", "convertToArrayOfNumbers", "input", "processedInput", "replace", "split", "values", "map", "num", "parseInt", "filter", "isFinite", "getPartsFromCalendarDay", "el", "getAttribute", "dayOfWeek", "parseDate", "parsed<PERSON><PERSON><PERSON>", "valStr", "parsedVal", "push", "parse", "exec", "unshift", "i", "minute", "ampm", "clampDate", "dateParts", "minParts", "maxParts", "parseAmPm", "parseMaxParts", "todayParts", "result", "yearValue", "monthValue", "parseMinParts", "twoDigit", "Math", "abs", "slice", "fourDigit", "convertDataToISO", "data", "rtn", "convert12HourTo24Hour", "getStartOfWeek", "refParts", "subtractDays", "getEndOfWeek", "addDays", "getNextDay", "getPreviousDay", "getPreviousWeek", "getNextWeek", "numDays", "workingParts", "daysInMonth", "getPrevious<PERSON><PERSON>h", "numDaysInMonth", "getNextMonth", "changeYear", "<PERSON><PERSON><PERSON><PERSON>", "getPreviousYear", "getNextYear", "getInternalHourValue", "use24Hour", "calculateHourFromAMPM", "currentParts", "newAMPM", "currentAMPM", "newHour", "validateParts", "partsCopy", "Object", "assign", "getClosestValidDate", "month<PERSON><PERSON><PERSON>", "dayV<PERSON><PERSON>", "yearValues", "hourValues", "minute<PERSON><PERSON><PERSON>", "copyParts", "filteredYears", "findClosestValue", "filteredMonths", "filteredDays", "filteredHours", "filteredMinutes", "reference", "closestValue", "rank", "length", "valueRank", "getFormattedDayPeriod", "<PERSON><PERSON><PERSON><PERSON>", "toUpperCase", "stripTimeZone", "timeZone", "timeZoneName", "getLocalizedTime", "timeParts", "format", "addTimePadding", "valueToString", "toString", "getFormattedHour", "generateDayAriaLabel", "today", "getNormalizedDate", "labelString", "weekday", "getMonthAndYear", "getDay", "getLocalizedDateTimeParts", "obj", "getYear", "getLocalizedDateTime", "_a", "_b", "_c", "timeString", "getDateTimeFormat", "getTodayLabel", "label", "RelativeTimeFormat", "numeric", "char<PERSON>t", "removeDateTzOffset", "tzOffset", "getTimezoneOffset", "setMinutes", "getMinutes", "DATE_AM", "DATE_PM", "getLocalizedDayPeriod", "localizedDayPeriod", "part", "formatValue", "join", "get<PERSON><PERSON>y", "toISOString", "minutes", "hour11", "hour12", "hour23", "hour24", "getDaysOfWeek", "mode", "firstDayOfWeek", "weekdayFormat", "intl", "startDate", "daysOfWeek", "currentDate", "setDate", "getDate", "getDaysOfMonth", "firstOfMonth", "offset", "days", "getHourData", "generateTime", "computedHourCycle", "processedHours", "processedMinutes", "isAMAllowed", "isPMAllowed", "includes", "convertedHour", "isPastMinHour", "hours", "am", "pm", "generateMonths", "forcedDate", "current", "forced", "forcedMonthIsBefore", "getMonthColumnData", "months", "processedMonths", "for<PERSON>ach", "processedMonth", "monthString", "text", "max<PERSON><PERSON><PERSON>", "minMonth", "getDayColumnData", "maxDay", "minDay", "processedDays", "processedDay", "dayString", "getYearColumnData", "processedYears", "maxYear", "minYear", "getAllMonthsInRange", "getCombinedDateColumnData", "items", "monthObject", "referenceMonth", "monthDays", "dateColumnItems", "dayObject", "isToday", "getTimeColumnsData", "allowedHourV<PERSON>ues", "allowed<PERSON><PERSON>ute<PERSON><PERSON><PERSON>", "hoursItems", "minutesItems", "dayPeriodItems", "minutesData", "hoursData", "dayPeriodData", "A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M", "N", "O", "P", "Q", "R", "a", "b", "c", "d", "e", "f", "g", "h", "j", "k", "l", "m", "n", "o", "q", "r", "s", "t", "u", "v", "w", "x", "y", "z"], "sourceRoot": "webpack:///", "x_google_ignoreList": [0]}