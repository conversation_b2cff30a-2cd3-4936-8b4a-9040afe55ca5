{"version": 3, "file": "node_modules_ionic_core_dist_esm_ion-progress-bar_entry_js.js", "mappings": ";;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AAC0E;AACvB;AACW;AACY;AAE1E,MAAMY,iBAAiB,GAAG,ugRAAugR;AACjiR,MAAMC,uBAAuB,GAAGD,iBAAiB;AAEjD,MAAME,gBAAgB,GAAG,ugRAAugR;AAChiR,MAAMC,sBAAsB,GAAGD,gBAAgB;AAE/C,MAAME,WAAW,GAAG,MAAM;EACtBC,WAAWA,CAACC,OAAO,EAAE;IACjBjB,qDAAgB,CAAC,IAAI,EAAEiB,OAAO,CAAC;IAC/B,IAAI,CAACC,IAAI,GAAG,aAAa;IACzB,IAAI,CAACC,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACC,KAAK,GAAG,CAAC;IACd,IAAI,CAACC,MAAM,GAAG,CAAC;IACf,IAAI,CAACC,KAAK,GAAGC,SAAS;EAC1B;EACAC,MAAMA,CAAA,EAAG;IACL,MAAM;MAAEF,KAAK;MAAEJ,IAAI;MAAEC,QAAQ;MAAEC,KAAK;MAAEC;IAAO,CAAC,GAAG,IAAI;IACrD,MAAMI,MAAM,GAAGjB,wDAAM,CAACkB,UAAU,CAAC,UAAU,CAAC;IAC5C,MAAMC,IAAI,GAAGjB,4DAAU,CAAC,IAAI,CAAC;IAC7B,OAAQT,qDAAC,CAACE,iDAAI,EAAE;MAAEyB,GAAG,EAAE,0CAA0C;MAAEC,IAAI,EAAE,aAAa;MAAE,eAAe,EAAEX,IAAI,KAAK,aAAa,GAAGE,KAAK,GAAG,IAAI;MAAE,eAAe,EAAE,GAAG;MAAE,eAAe,EAAE,GAAG;MAAEU,KAAK,EAAEvB,qDAAkB,CAACe,KAAK,EAAE;QACrN,CAACK,IAAI,GAAG,IAAI;QACZ,CAAC,gBAAgBT,IAAI,EAAE,GAAG,IAAI;QAC9B,iBAAiB,EAAEO,MAAM;QACzB,uBAAuB,EAAEM,QAAQ,CAACC,GAAG,KAAK,KAAK,GAAG,CAACb,QAAQ,GAAGA;MAClE,CAAC;IAAE,CAAC,EAAED,IAAI,KAAK,eAAe,GAAGe,mBAAmB,CAAC,CAAC,GAAGC,cAAc,CAACd,KAAK,EAAEC,MAAM,CAAC,CAAC;EAC/F;AACJ,CAAC;AACD,MAAMY,mBAAmB,GAAGA,CAAA,KAAM;EAC9B,OAAQhC,qDAAC,CAAC,KAAK,EAAE;IAAEkC,IAAI,EAAE,OAAO;IAAEL,KAAK,EAAE;EAAsB,CAAC,EAAE7B,qDAAC,CAAC,KAAK,EAAE;IAAE6B,KAAK,EAAE;EAA4B,CAAC,EAAE7B,qDAAC,CAAC,MAAM,EAAE;IAAEkC,IAAI,EAAE,UAAU;IAAEL,KAAK,EAAE;EAAyB,CAAC,CAAC,CAAC,EAAE7B,qDAAC,CAAC,KAAK,EAAE;IAAE6B,KAAK,EAAE;EAA8B,CAAC,EAAE7B,qDAAC,CAAC,MAAM,EAAE;IAAEkC,IAAI,EAAE,UAAU;IAAEL,KAAK,EAAE;EAAyB,CAAC,CAAC,CAAC,CAAC;AAC/S,CAAC;AACD,MAAMI,cAAc,GAAGA,CAACd,KAAK,EAAEC,MAAM,KAAK;EACtC,MAAMe,UAAU,GAAG/B,uDAAK,CAAC,CAAC,EAAEe,KAAK,EAAE,CAAC,CAAC;EACrC,MAAMiB,WAAW,GAAGhC,uDAAK,CAAC,CAAC,EAAEgB,MAAM,EAAE,CAAC,CAAC;EACvC,OAAO,CACHpB,qDAAC,CAAC,KAAK,EAAE;IAAEkC,IAAI,EAAE,UAAU;IAAEL,KAAK,EAAE,UAAU;IAAEQ,KAAK,EAAE;MAAEC,SAAS,EAAE,UAAUH,UAAU;IAAI;EAAE,CAAC,CAAC;EAChG;AACR;AACA;AACA;AACA;AACA;AACA;EACQnC,qDAAC,CAAC,KAAK,EAAE;IAAE6B,KAAK,EAAE;MAAE,0BAA0B,EAAE,IAAI;MAAE,UAAU,EAAEO,WAAW,KAAK;IAAE,CAAC;IAAEC,KAAK,EAAE;MAAEC,SAAS,EAAE,cAAcF,WAAW,GAAG,GAAG;IAAK;EAAE,CAAC,EAAEpC,qDAAC,CAAC,KAAK,EAAE;IAAE6B,KAAK,EAAE,0BAA0B;IAAEQ,KAAK,EAAE;MAAEC,SAAS,EAAE,eAAeF,WAAW,GAAG,GAAG;IAAK;EAAE,CAAC,EAAEpC,qDAAC,CAAC,KAAK,EAAE;IAAEkC,IAAI,EAAE,QAAQ;IAAEL,KAAK,EAAE;EAAiB,CAAC,CAAC,CAAC,CAAC,EACvT7B,qDAAC,CAAC,KAAK,EAAE;IAAEkC,IAAI,EAAE,OAAO;IAAEL,KAAK,EAAE,qBAAqB;IAAEQ,KAAK,EAAE;MAAEC,SAAS,EAAE,UAAUF,WAAW;IAAI;EAAE,CAAC,CAAC,CAC5G;AACL,CAAC;AACDtB,WAAW,CAACuB,KAAK,GAAG;EAChBE,GAAG,EAAE5B,uBAAuB;EAC5B6B,EAAE,EAAE3B;AACR,CAAC", "sources": ["./node_modules/@ionic/core/dist/esm/ion-progress-bar.entry.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, h, H as Host } from './index-a1a47f01.js';\nimport { l as clamp } from './helpers-be245865.js';\nimport { c as createColorClasses } from './theme-01f3f29c.js';\nimport { c as config, b as getIonMode } from './ionic-global-94f25d1b.js';\n\nconst progressBarIosCss = \":host{--background:rgba(var(--ion-color-primary-rgb, 56, 128, 255), 0.3);--progress-background:var(--ion-color-primary, #3880ff);--buffer-background:var(--background);display:block;position:relative;width:100%;contain:strict;direction:ltr;overflow:hidden}.progress,.progress-indeterminate,.indeterminate-bar-primary,.indeterminate-bar-secondary,.progress-buffer-bar{left:0;right:0;top:0;bottom:0;position:absolute;width:100%;height:100%}.buffer-circles-container,.buffer-circles{left:0;right:0;top:0;bottom:0;position:absolute}.buffer-circles{right:-10px;left:-10px;}.progress,.progress-buffer-bar,.buffer-circles-container{-webkit-transform-origin:left top;transform-origin:left top;-webkit-transition:-webkit-transform 150ms linear;transition:-webkit-transform 150ms linear;transition:transform 150ms linear;transition:transform 150ms linear, -webkit-transform 150ms linear}.progress,.progress-indeterminate{background:var(--progress-background);z-index:2}.progress-buffer-bar{background:var(--buffer-background);z-index:1}.buffer-circles-container{overflow:hidden}.indeterminate-bar-primary{top:0;right:0;bottom:0;left:-145.166611%;-webkit-animation:primary-indeterminate-translate 2s infinite linear;animation:primary-indeterminate-translate 2s infinite linear}.indeterminate-bar-primary .progress-indeterminate{-webkit-animation:primary-indeterminate-scale 2s infinite linear;animation:primary-indeterminate-scale 2s infinite linear;-webkit-animation-play-state:inherit;animation-play-state:inherit}.indeterminate-bar-secondary{top:0;right:0;bottom:0;left:-54.888891%;-webkit-animation:secondary-indeterminate-translate 2s infinite linear;animation:secondary-indeterminate-translate 2s infinite linear}.indeterminate-bar-secondary .progress-indeterminate{-webkit-animation:secondary-indeterminate-scale 2s infinite linear;animation:secondary-indeterminate-scale 2s infinite linear;-webkit-animation-play-state:inherit;animation-play-state:inherit}.buffer-circles{background-image:radial-gradient(ellipse at center, var(--buffer-background) 0%, var(--buffer-background) 30%, transparent 30%);background-repeat:repeat-x;background-position:5px center;background-size:10px 10px;z-index:0;-webkit-animation:buffering 450ms infinite linear;animation:buffering 450ms infinite linear}:host(.progress-bar-reversed){-webkit-transform:scaleX(-1);transform:scaleX(-1)}:host(.progress-paused) .indeterminate-bar-secondary,:host(.progress-paused) .indeterminate-bar-primary,:host(.progress-paused) .buffer-circles{-webkit-animation-play-state:paused;animation-play-state:paused}:host(.ion-color) .progress-buffer-bar{background:rgba(var(--ion-color-base-rgb), 0.3)}:host(.ion-color) .buffer-circles{background-image:radial-gradient(ellipse at center, rgba(var(--ion-color-base-rgb), 0.3) 0%, rgba(var(--ion-color-base-rgb), 0.3) 30%, transparent 30%)}:host(.ion-color) .progress,:host(.ion-color) .progress-indeterminate{background:var(--ion-color-base)}@-webkit-keyframes primary-indeterminate-translate{0%{-webkit-transform:translateX(0);transform:translateX(0)}20%{-webkit-animation-timing-function:cubic-bezier(0.5, 0, 0.701732, 0.495819);animation-timing-function:cubic-bezier(0.5, 0, 0.701732, 0.495819);-webkit-transform:translateX(0);transform:translateX(0)}59.15%{-webkit-animation-timing-function:cubic-bezier(0.302435, 0.381352, 0.55, 0.956352);animation-timing-function:cubic-bezier(0.302435, 0.381352, 0.55, 0.956352);-webkit-transform:translateX(83.67142%);transform:translateX(83.67142%)}100%{-webkit-transform:translateX(200.611057%);transform:translateX(200.611057%)}}@keyframes primary-indeterminate-translate{0%{-webkit-transform:translateX(0);transform:translateX(0)}20%{-webkit-animation-timing-function:cubic-bezier(0.5, 0, 0.701732, 0.495819);animation-timing-function:cubic-bezier(0.5, 0, 0.701732, 0.495819);-webkit-transform:translateX(0);transform:translateX(0)}59.15%{-webkit-animation-timing-function:cubic-bezier(0.302435, 0.381352, 0.55, 0.956352);animation-timing-function:cubic-bezier(0.302435, 0.381352, 0.55, 0.956352);-webkit-transform:translateX(83.67142%);transform:translateX(83.67142%)}100%{-webkit-transform:translateX(200.611057%);transform:translateX(200.611057%)}}@-webkit-keyframes primary-indeterminate-scale{0%{-webkit-transform:scaleX(0.08);transform:scaleX(0.08)}36.65%{-webkit-animation-timing-function:cubic-bezier(0.334731, 0.12482, 0.785844, 1);animation-timing-function:cubic-bezier(0.334731, 0.12482, 0.785844, 1);-webkit-transform:scaleX(0.08);transform:scaleX(0.08)}69.15%{-webkit-animation-timing-function:cubic-bezier(0.06, 0.11, 0.6, 1);animation-timing-function:cubic-bezier(0.06, 0.11, 0.6, 1);-webkit-transform:scaleX(0.661479);transform:scaleX(0.661479)}100%{-webkit-transform:scaleX(0.08);transform:scaleX(0.08)}}@keyframes primary-indeterminate-scale{0%{-webkit-transform:scaleX(0.08);transform:scaleX(0.08)}36.65%{-webkit-animation-timing-function:cubic-bezier(0.334731, 0.12482, 0.785844, 1);animation-timing-function:cubic-bezier(0.334731, 0.12482, 0.785844, 1);-webkit-transform:scaleX(0.08);transform:scaleX(0.08)}69.15%{-webkit-animation-timing-function:cubic-bezier(0.06, 0.11, 0.6, 1);animation-timing-function:cubic-bezier(0.06, 0.11, 0.6, 1);-webkit-transform:scaleX(0.661479);transform:scaleX(0.661479)}100%{-webkit-transform:scaleX(0.08);transform:scaleX(0.08)}}@-webkit-keyframes secondary-indeterminate-translate{0%{-webkit-animation-timing-function:cubic-bezier(0.15, 0, 0.515058, 0.409685);animation-timing-function:cubic-bezier(0.15, 0, 0.515058, 0.409685);-webkit-transform:translateX(0);transform:translateX(0)}25%{-webkit-animation-timing-function:cubic-bezier(0.31033, 0.284058, 0.8, 0.733712);animation-timing-function:cubic-bezier(0.31033, 0.284058, 0.8, 0.733712);-webkit-transform:translateX(37.651913%);transform:translateX(37.651913%)}48.35%{-webkit-animation-timing-function:cubic-bezier(0.4, 0.627035, 0.6, 0.902026);animation-timing-function:cubic-bezier(0.4, 0.627035, 0.6, 0.902026);-webkit-transform:translateX(84.386165%);transform:translateX(84.386165%)}100%{-webkit-transform:translateX(160.277782%);transform:translateX(160.277782%)}}@keyframes secondary-indeterminate-translate{0%{-webkit-animation-timing-function:cubic-bezier(0.15, 0, 0.515058, 0.409685);animation-timing-function:cubic-bezier(0.15, 0, 0.515058, 0.409685);-webkit-transform:translateX(0);transform:translateX(0)}25%{-webkit-animation-timing-function:cubic-bezier(0.31033, 0.284058, 0.8, 0.733712);animation-timing-function:cubic-bezier(0.31033, 0.284058, 0.8, 0.733712);-webkit-transform:translateX(37.651913%);transform:translateX(37.651913%)}48.35%{-webkit-animation-timing-function:cubic-bezier(0.4, 0.627035, 0.6, 0.902026);animation-timing-function:cubic-bezier(0.4, 0.627035, 0.6, 0.902026);-webkit-transform:translateX(84.386165%);transform:translateX(84.386165%)}100%{-webkit-transform:translateX(160.277782%);transform:translateX(160.277782%)}}@-webkit-keyframes secondary-indeterminate-scale{0%{-webkit-animation-timing-function:cubic-bezier(0.205028, 0.057051, 0.57661, 0.453971);animation-timing-function:cubic-bezier(0.205028, 0.057051, 0.57661, 0.453971);-webkit-transform:scaleX(0.08);transform:scaleX(0.08)}19.15%{-webkit-animation-timing-function:cubic-bezier(0.152313, 0.196432, 0.648374, 1.004315);animation-timing-function:cubic-bezier(0.152313, 0.196432, 0.648374, 1.004315);-webkit-transform:scaleX(0.457104);transform:scaleX(0.457104)}44.15%{-webkit-animation-timing-function:cubic-bezier(0.257759, -0.003163, 0.211762, 1.38179);animation-timing-function:cubic-bezier(0.257759, -0.003163, 0.211762, 1.38179);-webkit-transform:scaleX(0.72796);transform:scaleX(0.72796)}100%{-webkit-transform:scaleX(0.08);transform:scaleX(0.08)}}@keyframes secondary-indeterminate-scale{0%{-webkit-animation-timing-function:cubic-bezier(0.205028, 0.057051, 0.57661, 0.453971);animation-timing-function:cubic-bezier(0.205028, 0.057051, 0.57661, 0.453971);-webkit-transform:scaleX(0.08);transform:scaleX(0.08)}19.15%{-webkit-animation-timing-function:cubic-bezier(0.152313, 0.196432, 0.648374, 1.004315);animation-timing-function:cubic-bezier(0.152313, 0.196432, 0.648374, 1.004315);-webkit-transform:scaleX(0.457104);transform:scaleX(0.457104)}44.15%{-webkit-animation-timing-function:cubic-bezier(0.257759, -0.003163, 0.211762, 1.38179);animation-timing-function:cubic-bezier(0.257759, -0.003163, 0.211762, 1.38179);-webkit-transform:scaleX(0.72796);transform:scaleX(0.72796)}100%{-webkit-transform:scaleX(0.08);transform:scaleX(0.08)}}@-webkit-keyframes buffering{to{-webkit-transform:translateX(-10px);transform:translateX(-10px)}}@keyframes buffering{to{-webkit-transform:translateX(-10px);transform:translateX(-10px)}}:host{height:3px}\";\nconst IonProgressBarIosStyle0 = progressBarIosCss;\n\nconst progressBarMdCss = \":host{--background:rgba(var(--ion-color-primary-rgb, 56, 128, 255), 0.3);--progress-background:var(--ion-color-primary, #3880ff);--buffer-background:var(--background);display:block;position:relative;width:100%;contain:strict;direction:ltr;overflow:hidden}.progress,.progress-indeterminate,.indeterminate-bar-primary,.indeterminate-bar-secondary,.progress-buffer-bar{left:0;right:0;top:0;bottom:0;position:absolute;width:100%;height:100%}.buffer-circles-container,.buffer-circles{left:0;right:0;top:0;bottom:0;position:absolute}.buffer-circles{right:-10px;left:-10px;}.progress,.progress-buffer-bar,.buffer-circles-container{-webkit-transform-origin:left top;transform-origin:left top;-webkit-transition:-webkit-transform 150ms linear;transition:-webkit-transform 150ms linear;transition:transform 150ms linear;transition:transform 150ms linear, -webkit-transform 150ms linear}.progress,.progress-indeterminate{background:var(--progress-background);z-index:2}.progress-buffer-bar{background:var(--buffer-background);z-index:1}.buffer-circles-container{overflow:hidden}.indeterminate-bar-primary{top:0;right:0;bottom:0;left:-145.166611%;-webkit-animation:primary-indeterminate-translate 2s infinite linear;animation:primary-indeterminate-translate 2s infinite linear}.indeterminate-bar-primary .progress-indeterminate{-webkit-animation:primary-indeterminate-scale 2s infinite linear;animation:primary-indeterminate-scale 2s infinite linear;-webkit-animation-play-state:inherit;animation-play-state:inherit}.indeterminate-bar-secondary{top:0;right:0;bottom:0;left:-54.888891%;-webkit-animation:secondary-indeterminate-translate 2s infinite linear;animation:secondary-indeterminate-translate 2s infinite linear}.indeterminate-bar-secondary .progress-indeterminate{-webkit-animation:secondary-indeterminate-scale 2s infinite linear;animation:secondary-indeterminate-scale 2s infinite linear;-webkit-animation-play-state:inherit;animation-play-state:inherit}.buffer-circles{background-image:radial-gradient(ellipse at center, var(--buffer-background) 0%, var(--buffer-background) 30%, transparent 30%);background-repeat:repeat-x;background-position:5px center;background-size:10px 10px;z-index:0;-webkit-animation:buffering 450ms infinite linear;animation:buffering 450ms infinite linear}:host(.progress-bar-reversed){-webkit-transform:scaleX(-1);transform:scaleX(-1)}:host(.progress-paused) .indeterminate-bar-secondary,:host(.progress-paused) .indeterminate-bar-primary,:host(.progress-paused) .buffer-circles{-webkit-animation-play-state:paused;animation-play-state:paused}:host(.ion-color) .progress-buffer-bar{background:rgba(var(--ion-color-base-rgb), 0.3)}:host(.ion-color) .buffer-circles{background-image:radial-gradient(ellipse at center, rgba(var(--ion-color-base-rgb), 0.3) 0%, rgba(var(--ion-color-base-rgb), 0.3) 30%, transparent 30%)}:host(.ion-color) .progress,:host(.ion-color) .progress-indeterminate{background:var(--ion-color-base)}@-webkit-keyframes primary-indeterminate-translate{0%{-webkit-transform:translateX(0);transform:translateX(0)}20%{-webkit-animation-timing-function:cubic-bezier(0.5, 0, 0.701732, 0.495819);animation-timing-function:cubic-bezier(0.5, 0, 0.701732, 0.495819);-webkit-transform:translateX(0);transform:translateX(0)}59.15%{-webkit-animation-timing-function:cubic-bezier(0.302435, 0.381352, 0.55, 0.956352);animation-timing-function:cubic-bezier(0.302435, 0.381352, 0.55, 0.956352);-webkit-transform:translateX(83.67142%);transform:translateX(83.67142%)}100%{-webkit-transform:translateX(200.611057%);transform:translateX(200.611057%)}}@keyframes primary-indeterminate-translate{0%{-webkit-transform:translateX(0);transform:translateX(0)}20%{-webkit-animation-timing-function:cubic-bezier(0.5, 0, 0.701732, 0.495819);animation-timing-function:cubic-bezier(0.5, 0, 0.701732, 0.495819);-webkit-transform:translateX(0);transform:translateX(0)}59.15%{-webkit-animation-timing-function:cubic-bezier(0.302435, 0.381352, 0.55, 0.956352);animation-timing-function:cubic-bezier(0.302435, 0.381352, 0.55, 0.956352);-webkit-transform:translateX(83.67142%);transform:translateX(83.67142%)}100%{-webkit-transform:translateX(200.611057%);transform:translateX(200.611057%)}}@-webkit-keyframes primary-indeterminate-scale{0%{-webkit-transform:scaleX(0.08);transform:scaleX(0.08)}36.65%{-webkit-animation-timing-function:cubic-bezier(0.334731, 0.12482, 0.785844, 1);animation-timing-function:cubic-bezier(0.334731, 0.12482, 0.785844, 1);-webkit-transform:scaleX(0.08);transform:scaleX(0.08)}69.15%{-webkit-animation-timing-function:cubic-bezier(0.06, 0.11, 0.6, 1);animation-timing-function:cubic-bezier(0.06, 0.11, 0.6, 1);-webkit-transform:scaleX(0.661479);transform:scaleX(0.661479)}100%{-webkit-transform:scaleX(0.08);transform:scaleX(0.08)}}@keyframes primary-indeterminate-scale{0%{-webkit-transform:scaleX(0.08);transform:scaleX(0.08)}36.65%{-webkit-animation-timing-function:cubic-bezier(0.334731, 0.12482, 0.785844, 1);animation-timing-function:cubic-bezier(0.334731, 0.12482, 0.785844, 1);-webkit-transform:scaleX(0.08);transform:scaleX(0.08)}69.15%{-webkit-animation-timing-function:cubic-bezier(0.06, 0.11, 0.6, 1);animation-timing-function:cubic-bezier(0.06, 0.11, 0.6, 1);-webkit-transform:scaleX(0.661479);transform:scaleX(0.661479)}100%{-webkit-transform:scaleX(0.08);transform:scaleX(0.08)}}@-webkit-keyframes secondary-indeterminate-translate{0%{-webkit-animation-timing-function:cubic-bezier(0.15, 0, 0.515058, 0.409685);animation-timing-function:cubic-bezier(0.15, 0, 0.515058, 0.409685);-webkit-transform:translateX(0);transform:translateX(0)}25%{-webkit-animation-timing-function:cubic-bezier(0.31033, 0.284058, 0.8, 0.733712);animation-timing-function:cubic-bezier(0.31033, 0.284058, 0.8, 0.733712);-webkit-transform:translateX(37.651913%);transform:translateX(37.651913%)}48.35%{-webkit-animation-timing-function:cubic-bezier(0.4, 0.627035, 0.6, 0.902026);animation-timing-function:cubic-bezier(0.4, 0.627035, 0.6, 0.902026);-webkit-transform:translateX(84.386165%);transform:translateX(84.386165%)}100%{-webkit-transform:translateX(160.277782%);transform:translateX(160.277782%)}}@keyframes secondary-indeterminate-translate{0%{-webkit-animation-timing-function:cubic-bezier(0.15, 0, 0.515058, 0.409685);animation-timing-function:cubic-bezier(0.15, 0, 0.515058, 0.409685);-webkit-transform:translateX(0);transform:translateX(0)}25%{-webkit-animation-timing-function:cubic-bezier(0.31033, 0.284058, 0.8, 0.733712);animation-timing-function:cubic-bezier(0.31033, 0.284058, 0.8, 0.733712);-webkit-transform:translateX(37.651913%);transform:translateX(37.651913%)}48.35%{-webkit-animation-timing-function:cubic-bezier(0.4, 0.627035, 0.6, 0.902026);animation-timing-function:cubic-bezier(0.4, 0.627035, 0.6, 0.902026);-webkit-transform:translateX(84.386165%);transform:translateX(84.386165%)}100%{-webkit-transform:translateX(160.277782%);transform:translateX(160.277782%)}}@-webkit-keyframes secondary-indeterminate-scale{0%{-webkit-animation-timing-function:cubic-bezier(0.205028, 0.057051, 0.57661, 0.453971);animation-timing-function:cubic-bezier(0.205028, 0.057051, 0.57661, 0.453971);-webkit-transform:scaleX(0.08);transform:scaleX(0.08)}19.15%{-webkit-animation-timing-function:cubic-bezier(0.152313, 0.196432, 0.648374, 1.004315);animation-timing-function:cubic-bezier(0.152313, 0.196432, 0.648374, 1.004315);-webkit-transform:scaleX(0.457104);transform:scaleX(0.457104)}44.15%{-webkit-animation-timing-function:cubic-bezier(0.257759, -0.003163, 0.211762, 1.38179);animation-timing-function:cubic-bezier(0.257759, -0.003163, 0.211762, 1.38179);-webkit-transform:scaleX(0.72796);transform:scaleX(0.72796)}100%{-webkit-transform:scaleX(0.08);transform:scaleX(0.08)}}@keyframes secondary-indeterminate-scale{0%{-webkit-animation-timing-function:cubic-bezier(0.205028, 0.057051, 0.57661, 0.453971);animation-timing-function:cubic-bezier(0.205028, 0.057051, 0.57661, 0.453971);-webkit-transform:scaleX(0.08);transform:scaleX(0.08)}19.15%{-webkit-animation-timing-function:cubic-bezier(0.152313, 0.196432, 0.648374, 1.004315);animation-timing-function:cubic-bezier(0.152313, 0.196432, 0.648374, 1.004315);-webkit-transform:scaleX(0.457104);transform:scaleX(0.457104)}44.15%{-webkit-animation-timing-function:cubic-bezier(0.257759, -0.003163, 0.211762, 1.38179);animation-timing-function:cubic-bezier(0.257759, -0.003163, 0.211762, 1.38179);-webkit-transform:scaleX(0.72796);transform:scaleX(0.72796)}100%{-webkit-transform:scaleX(0.08);transform:scaleX(0.08)}}@-webkit-keyframes buffering{to{-webkit-transform:translateX(-10px);transform:translateX(-10px)}}@keyframes buffering{to{-webkit-transform:translateX(-10px);transform:translateX(-10px)}}:host{height:4px}\";\nconst IonProgressBarMdStyle0 = progressBarMdCss;\n\nconst ProgressBar = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.type = 'determinate';\n        this.reversed = false;\n        this.value = 0;\n        this.buffer = 1;\n        this.color = undefined;\n    }\n    render() {\n        const { color, type, reversed, value, buffer } = this;\n        const paused = config.getBoolean('_testing');\n        const mode = getIonMode(this);\n        return (h(Host, { key: '944b79ed6fce1b2c0ed48681cd8517a5abbddd80', role: \"progressbar\", \"aria-valuenow\": type === 'determinate' ? value : null, \"aria-valuemin\": \"0\", \"aria-valuemax\": \"1\", class: createColorClasses(color, {\n                [mode]: true,\n                [`progress-bar-${type}`]: true,\n                'progress-paused': paused,\n                'progress-bar-reversed': document.dir === 'rtl' ? !reversed : reversed,\n            }) }, type === 'indeterminate' ? renderIndeterminate() : renderProgress(value, buffer)));\n    }\n};\nconst renderIndeterminate = () => {\n    return (h(\"div\", { part: \"track\", class: \"progress-buffer-bar\" }, h(\"div\", { class: \"indeterminate-bar-primary\" }, h(\"span\", { part: \"progress\", class: \"progress-indeterminate\" })), h(\"div\", { class: \"indeterminate-bar-secondary\" }, h(\"span\", { part: \"progress\", class: \"progress-indeterminate\" }))));\n};\nconst renderProgress = (value, buffer) => {\n    const finalValue = clamp(0, value, 1);\n    const finalBuffer = clamp(0, buffer, 1);\n    return [\n        h(\"div\", { part: \"progress\", class: \"progress\", style: { transform: `scaleX(${finalValue})` } }),\n        /**\n         * Buffer circles with two container to move\n         * the circles behind the buffer progress\n         * with respecting the animation.\n         * When finalBuffer === 1, we use display: none\n         * instead of removing the element to avoid flickering.\n         */\n        h(\"div\", { class: { 'buffer-circles-container': true, 'ion-hide': finalBuffer === 1 }, style: { transform: `translateX(${finalBuffer * 100}%)` } }, h(\"div\", { class: \"buffer-circles-container\", style: { transform: `translateX(-${finalBuffer * 100}%)` } }, h(\"div\", { part: \"stream\", class: \"buffer-circles\" }))),\n        h(\"div\", { part: \"track\", class: \"progress-buffer-bar\", style: { transform: `scaleX(${finalBuffer})` } }),\n    ];\n};\nProgressBar.style = {\n    ios: IonProgressBarIosStyle0,\n    md: IonProgressBarMdStyle0\n};\n\nexport { ProgressBar as ion_progress_bar };\n"], "names": ["r", "registerInstance", "h", "H", "Host", "l", "clamp", "c", "createColorClasses", "config", "b", "getIonMode", "progressBarIosCss", "IonProgressBarIosStyle0", "progressBarMdCss", "IonProgressBarMdStyle0", "ProgressBar", "constructor", "hostRef", "type", "reversed", "value", "buffer", "color", "undefined", "render", "paused", "getBoolean", "mode", "key", "role", "class", "document", "dir", "renderIndeterminate", "renderProgress", "part", "finalValue", "finalBuffer", "style", "transform", "ios", "md", "ion_progress_bar"], "sourceRoot": "webpack:///", "x_google_ignoreList": [0]}