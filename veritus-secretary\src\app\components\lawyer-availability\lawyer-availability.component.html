<div class="availability-container">
  <!-- Header -->
  <div class="availability-header">
    <div class="header-info">
      <h3 class="lawyer-name">{{ selectedLawyer?.name || 'Select a Lawyer' }}</h3>
      <p class="selected-date">{{ selectedDate | date:'fullDate' }}</p>
    </div>
    
    <div class="header-actions" *ngIf="selectedLawyer">
      <ion-button 
        *ngIf="!isEditing" 
        fill="outline" 
        size="small" 
        (click)="startEditing()">
        <ion-icon name="create-outline" slot="start"></ion-icon>
        Edit
      </ion-button>
      
      <div *ngIf="isEditing" class="edit-actions">
        <ion-button 
          fill="clear" 
          size="small" 
          color="medium" 
          (click)="cancelEditing()">
          Cancel
        </ion-button>
        <ion-button 
          fill="solid" 
          size="small" 
          color="primary" 
          (click)="saveAvailability()"
          [disabled]="isLoading">
          <ion-icon name="checkmark" slot="start"></ion-icon>
          Save
        </ion-button>
      </div>
    </div>
  </div>

  <!-- Loading State -->
  <div *ngIf="isLoading" class="loading-container">
    <ion-spinner name="crescent"></ion-spinner>
    <p>Loading availability...</p>
  </div>

  <!-- No Lawyer Selected -->
  <div *ngIf="!selectedLawyer && !isLoading" class="no-lawyer-selected">
    <ion-icon name="person-outline" class="large-icon"></ion-icon>
    <h4>No Lawyer Selected</h4>
    <p>Please select a lawyer to view and manage their availability.</p>
  </div>

  <!-- Availability Content -->
  <div *ngIf="selectedLawyer && !isLoading" class="availability-content">
    
    <!-- Summary -->
    <div class="availability-summary">
      <div class="summary-card">
        <div class="summary-number">{{ availableSlotsCount }}</div>
        <div class="summary-label">Available Slots</div>
      </div>
      
      <div class="summary-card" *ngIf="availability">
        <div class="summary-number">{{ availability.timeSlots.length }}</div>
        <div class="summary-label">Total Slots</div>
      </div>
      
      <div class="summary-card" *ngIf="!availability">
        <div class="summary-number">0</div>
        <div class="summary-label">No Availability Set</div>
      </div>
    </div>

    <!-- Time Slots Grid -->
    <div class="time-slots-section">
      <div class="section-header">
        <h4>Time Slots</h4>
        <ion-button 
          *ngIf="isEditing" 
          fill="clear" 
          size="small" 
          (click)="addCustomTimeSlot()">
          <ion-icon name="add" slot="start"></ion-icon>
          Add Custom Time
        </ion-button>
      </div>

      <div class="time-slots-grid">
        <div 
          *ngFor="let timeSlot of timeSlots" 
          class="time-slot-item"
          [ngClass]="getTimeSlotClass(timeSlot)"
          (click)="toggleTimeSlot(timeSlot)"
          [class.disabled]="isTimeSlotDisabled(timeSlot)">
          
          <div class="time-display">{{ formatTime(timeSlot.time) }}</div>
          
          <div class="slot-status">
            <ion-icon 
              *ngIf="timeSlot.isBooked" 
              name="calendar" 
              class="status-icon booked">
            </ion-icon>
            <ion-icon 
              *ngIf="timeSlot.isAvailable && !timeSlot.isBooked" 
              name="checkmark-circle" 
              class="status-icon available">
            </ion-icon>
            <ion-icon 
              *ngIf="!timeSlot.isAvailable && !timeSlot.isBooked" 
              name="close-circle" 
              class="status-icon unavailable">
            </ion-icon>
          </div>
        </div>
      </div>

      <!-- Empty State -->
      <div *ngIf="timeSlots.length === 0" class="empty-time-slots">
        <ion-icon name="time-outline" class="large-icon"></ion-icon>
        <h4>No Time Slots</h4>
        <p>Click "Edit" to add available time slots for this date.</p>
      </div>
    </div>

    <!-- Legend -->
    <div class="legend">
      <div class="legend-item">
        <ion-icon name="checkmark-circle" class="legend-icon available"></ion-icon>
        <span>Available</span>
      </div>
      <div class="legend-item">
        <ion-icon name="calendar" class="legend-icon booked"></ion-icon>
        <span>Booked</span>
      </div>
      <div class="legend-item">
        <ion-icon name="close-circle" class="legend-icon unavailable"></ion-icon>
        <span>Unavailable</span>
      </div>
    </div>

    <!-- Actions -->
    <div class="availability-actions" *ngIf="availability && !isEditing">
      <ion-button 
        fill="outline" 
        color="danger" 
        size="small" 
        (click)="deleteAvailability()">
        <ion-icon name="trash-outline" slot="start"></ion-icon>
        Delete Availability
      </ion-button>
    </div>

    <!-- Help Text -->
    <div class="help-text" *ngIf="isEditing">
      <ion-icon name="information-circle-outline"></ion-icon>
      <span>Click on time slots to toggle availability. Green slots are available for booking.</span>
    </div>
  </div>
</div>
