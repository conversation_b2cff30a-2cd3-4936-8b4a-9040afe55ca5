{"version": 3, "file": "src_app_secretary-tabs_secretary-tabs_module_ts.js", "mappings": ";;;;;;;;;;;;;;;;AACuD;AAEG;;;AAE1D,MAAME,MAAM,GAAW,CACrB;EACEC,IAAI,EAAE,EAAE;EACRC,SAAS,EAAEH,mEAAiB;EAC5BI,QAAQ,EAAE,CACR;IACEF,IAAI,EAAE,WAAW;IACjBG,YAAY,EAAEA,CAAA,KAAM,qNAA2D,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,4BAA4B;GACzH,EACD;IACEN,IAAI,EAAE,UAAU;IAChBG,YAAY,EAAEA,CAAA,KAAM,iNAAyD,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACE,2BAA2B;GACtH,EACD;IACEP,IAAI,EAAE,OAAO;IACbG,YAAY,EAAEA,CAAA,KAAM,qMAAmD,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACG,wBAAwB;GAC7G,EACD;IACER,IAAI,EAAE,OAAO;IACbG,YAAY,EAAEA,CAAA,KAAM,qMAAmD,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACI,wBAAwB;GAC7G,EACD;IACET,IAAI,EAAE,SAAS;IACfG,YAAY,EAAEA,CAAA,KAAM,6MAAuD,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACK,0BAA0B;GACnH,EACD;IACEV,IAAI,EAAE,EAAE;IACRW,UAAU,EAAE,2BAA2B;IACvCC,SAAS,EAAE;GACZ;CAEJ,CACF;AAMK,MAAOC,8BAA8B;;;uBAA9BA,8BAA8B;IAAA;EAAA;;;YAA9BA;IAA8B;EAAA;;;gBAH/BhB,yDAAY,CAACiB,QAAQ,CAACf,MAAM,CAAC,EAC7BF,yDAAY;IAAA;EAAA;;;sHAEXgB,8BAA8B;IAAAE,OAAA,GAAAC,yDAAA;IAAAC,OAAA,GAF/BpB,yDAAY;EAAA;AAAA,K;;;;;;;;;;;;;;;;;;;;ACxCuB;AACF;AACA;AAEoC;AACvB;;AAWpD,MAAOwB,uBAAuB;;;uBAAvBA,uBAAuB;IAAA;EAAA;;;YAAvBA;IAAuB;EAAA;;;gBAPhCH,yDAAY,EACZC,uDAAW,EACXC,uDAAW,EACXP,0FAA8B;IAAA;EAAA;;;sHAIrBQ,uBAAuB;IAAAC,YAAA,GAFnBxB,mEAAiB;IAAAiB,OAAA,GAL9BG,yDAAY,EACZC,uDAAW,EACXC,uDAAW,EACXP,0FAA8B;EAAA;AAAA,K;;;;;;;;;;;;;;;;;;ACN5B,MAAOf,iBAAiB;EAE5ByB,YAAA,GAAgB;EAEhBC,QAAQA,CAAA,GACR;;;uBALW1B,iBAAiB;IAAA;EAAA;;;YAAjBA,iBAAiB;MAAA2B,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,2BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCP9BE,4DAAA,eAAU;UACRA,uDAAA,wBAAuC;UAGrCA,4DADF,qBAAmD,wBACjB;UAC9BA,uDAAA,kBAAiC;UACjCA,4DAAA,gBAAW;UAAAA,oDAAA,gBAAS;UACtBA,0DADsB,EAAY,EACjB;UAEjBA,4DAAA,wBAA+B;UAC7BA,uDAAA,kBAAqC;UACrCA,4DAAA,gBAAW;UAAAA,oDAAA,gBAAQ;UACrBA,0DADqB,EAAY,EAChB;UAEjBA,4DAAA,yBAA4B;UAC1BA,uDAAA,mBAAmC;UACnCA,4DAAA,iBAAW;UAAAA,oDAAA,aAAK;UAClBA,0DADkB,EAAY,EACb;UAEjBA,4DAAA,yBAA4B;UAC1BA,uDAAA,mBAAqC;UACrCA,4DAAA,iBAAW;UAAAA,oDAAA,aAAK;UAClBA,0DADkB,EAAY,EACb;UAEjBA,4DAAA,yBAA8B;UAC5BA,uDAAA,oBAAmC;UACnCA,4DAAA,iBAAW;UAAAA,oDAAA,eAAO;UAGxBA,0DAHwB,EAAY,EACf,EACL,EACL", "sources": ["./src/app/secretary-tabs/secretary-tabs-routing.module.ts", "./src/app/secretary-tabs/secretary-tabs.module.ts", "./src/app/secretary-tabs/secretary-tabs.page.ts", "./src/app/secretary-tabs/secretary-tabs.page.html"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { Routes, RouterModule } from '@angular/router';\r\n\r\nimport { SecretaryTabsPage } from './secretary-tabs.page';\r\n\r\nconst routes: Routes = [\r\n  {\r\n    path: '',\r\n    component: SecretaryTabsPage,\r\n    children: [\r\n      {\r\n        path: 'dashboard',\r\n        loadChildren: () => import('../secretary-dashboard/secretary-dashboard.module').then(m => m.SecretaryDashboardPageModule)\r\n      },\r\n      {\r\n        path: 'calendar',\r\n        loadChildren: () => import('../secretary-calendar/secretary-calendar.module').then(m => m.SecretaryCalendarPageModule)\r\n      },\r\n      {\r\n        path: 'cases',\r\n        loadChildren: () => import('../secretary-cases/secretary-cases.module').then(m => m.SecretaryCasesPageModule)\r\n      },\r\n      {\r\n        path: 'files',\r\n        loadChildren: () => import('../secretary-files/secretary-files.module').then(m => m.SecretaryFilesPageModule)\r\n      },\r\n      {\r\n        path: 'profile',\r\n        loadChildren: () => import('../secretary-profile/secretary-profile.module').then(m => m.SecretaryProfilePageModule)\r\n      },\r\n      {\r\n        path: '',\r\n        redirectTo: '/secretary-tabs/dashboard',\r\n        pathMatch: 'full'\r\n      }\r\n    ]\r\n  }\r\n];\r\n\r\n@NgModule({\r\n  imports: [RouterModule.forChild(routes)],\r\n  exports: [RouterModule],\r\n})\r\nexport class SecretaryTabsPageRoutingModule {}\r\n", "import { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { FormsModule } from '@angular/forms';\r\nimport { IonicModule } from '@ionic/angular';\r\n\r\nimport { SecretaryTabsPageRoutingModule } from './secretary-tabs-routing.module';\r\nimport { SecretaryTabsPage } from './secretary-tabs.page';\r\n\r\n@NgModule({\r\n  imports: [\r\n    CommonModule,\r\n    FormsModule,\r\n    IonicModule,\r\n    SecretaryTabsPageRoutingModule\r\n  ],\r\n  declarations: [SecretaryTabsPage]\r\n})\r\nexport class SecretaryTabsPageModule {}\r\n", "import { Component, OnInit } from '@angular/core';\r\n\r\n@Component({\r\n  selector: 'app-secretary-tabs',\r\n  templateUrl: './secretary-tabs.page.html',\r\n  styleUrls: ['./secretary-tabs.page.scss'],\r\n})\r\nexport class SecretaryTabsPage implements OnInit {\r\n\r\n  constructor() { }\r\n\r\n  ngOnInit() {\r\n  }\r\n\r\n}\r\n", "<ion-tabs>\r\n  <ion-router-outlet></ion-router-outlet>\r\n\r\n  <ion-tab-bar slot=\"bottom\" class=\"veritus-tab-bar\">\r\n    <ion-tab-button tab=\"dashboard\">\r\n      <ion-icon name=\"home\"></ion-icon>\r\n      <ion-label>Dashboard</ion-label>\r\n    </ion-tab-button>\r\n\r\n    <ion-tab-button tab=\"calendar\">\r\n      <ion-icon name=\"calendar\"></ion-icon>\r\n      <ion-label>Calendar</ion-label>\r\n    </ion-tab-button>\r\n\r\n    <ion-tab-button tab=\"cases\">\r\n      <ion-icon name=\"folder\"></ion-icon>\r\n      <ion-label>Cases</ion-label>\r\n    </ion-tab-button>\r\n\r\n    <ion-tab-button tab=\"files\">\r\n      <ion-icon name=\"document\"></ion-icon>\r\n      <ion-label>Files</ion-label>\r\n    </ion-tab-button>\r\n\r\n    <ion-tab-button tab=\"profile\">\r\n      <ion-icon name=\"person\"></ion-icon>\r\n      <ion-label>Profile</ion-label>\r\n    </ion-tab-button>\r\n  </ion-tab-bar>\r\n</ion-tabs>\r\n"], "names": ["RouterModule", "SecretaryTabsPage", "routes", "path", "component", "children", "loadChildren", "then", "m", "SecretaryDashboardPageModule", "SecretaryCalendarPageModule", "SecretaryCasesPageModule", "SecretaryFilesPageModule", "SecretaryProfilePageModule", "redirectTo", "pathMatch", "SecretaryTabsPageRoutingModule", "<PERSON><PERSON><PERSON><PERSON>", "imports", "i1", "exports", "CommonModule", "FormsModule", "IonicModule", "SecretaryTabsPageModule", "declarations", "constructor", "ngOnInit", "selectors", "decls", "vars", "consts", "template", "SecretaryTabsPage_Template", "rf", "ctx", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd"], "sourceRoot": "webpack:///", "x_google_ignoreList": []}