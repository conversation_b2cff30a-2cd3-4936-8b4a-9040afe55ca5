<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Veritus Admin</title>
</head>
<body>
  <div class="app-container">
    <!-- Show sidebar and header only when not on login page -->
    <div *ngIf="!isLoginPage()" class="admin-layout">
      <app-sidebar></app-sidebar>
      <div class="main-content">
        <app-header></app-header>
        <div class="content-area">
          <router-outlet></router-outlet>
        </div>
      </div>
    </div>
    
    <!-- Show only router outlet for login page -->
    <div *ngIf="isLoginPage()" class="login-layout">
      <router-outlet></router-outlet>
    </div>
  </div>
</body>
</html>
