.availability-container {
  padding: 20px;
  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
  border-radius: 12px;
  color: white;
  min-height: 400px;
}

.availability-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.header-info {
  .lawyer-name {
    margin: 0 0 4px 0;
    font-size: 1.4rem;
    font-weight: 600;
    color: #ffffff;
  }

  .selected-date {
    margin: 0;
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.7);
  }
}

.header-actions {
  .edit-actions {
    display: flex;
    gap: 8px;
  }
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  
  ion-spinner {
    margin-bottom: 16px;
  }
  
  p {
    color: rgba(255, 255, 255, 0.7);
    margin: 0;
  }
}

.no-lawyer-selected {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  text-align: center;
  
  .large-icon {
    font-size: 3rem;
    color: rgba(255, 255, 255, 0.3);
    margin-bottom: 16px;
  }
  
  h4 {
    margin: 0 0 8px 0;
    color: rgba(255, 255, 255, 0.8);
  }
  
  p {
    margin: 0;
    color: rgba(255, 255, 255, 0.6);
  }
}

.availability-content {
  .availability-summary {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 16px;
    margin-bottom: 24px;
    
    .summary-card {
      background: rgba(255, 255, 255, 0.1);
      border-radius: 8px;
      padding: 16px;
      text-align: center;
      border: 1px solid rgba(255, 255, 255, 0.1);
      
      .summary-number {
        font-size: 1.8rem;
        font-weight: bold;
        color: #4ade80;
        margin-bottom: 4px;
      }
      
      .summary-label {
        font-size: 0.8rem;
        color: rgba(255, 255, 255, 0.7);
        text-transform: uppercase;
        letter-spacing: 0.5px;
      }
    }
  }
}

.time-slots-section {
  margin-bottom: 24px;
  
  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    
    h4 {
      margin: 0;
      color: #ffffff;
      font-size: 1.1rem;
    }
  }
  
  .time-slots-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    gap: 12px;
    
    .time-slot-item {
      background: rgba(255, 255, 255, 0.05);
      border: 2px solid rgba(255, 255, 255, 0.1);
      border-radius: 8px;
      padding: 12px;
      text-align: center;
      cursor: pointer;
      transition: all 0.3s ease;
      position: relative;
      
      &:hover:not(.disabled) {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
      }
      
      &.available {
        border-color: #4ade80;
        background: rgba(74, 222, 128, 0.1);
        
        .time-display {
          color: #4ade80;
        }
      }
      
      &.booked {
        border-color: #f59e0b;
        background: rgba(245, 158, 11, 0.1);
        cursor: not-allowed;
        
        .time-display {
          color: #f59e0b;
        }
      }
      
      &.unavailable {
        border-color: rgba(255, 255, 255, 0.2);
        background: rgba(255, 255, 255, 0.05);
        
        .time-display {
          color: rgba(255, 255, 255, 0.5);
        }
      }
      
      &.disabled {
        cursor: not-allowed;
        opacity: 0.6;
      }
      
      .time-display {
        font-weight: 600;
        font-size: 0.9rem;
        margin-bottom: 4px;
      }
      
      .slot-status {
        .status-icon {
          font-size: 1.2rem;
          
          &.available {
            color: #4ade80;
          }
          
          &.booked {
            color: #f59e0b;
          }
          
          &.unavailable {
            color: rgba(255, 255, 255, 0.3);
          }
        }
      }
    }
  }
  
  .empty-time-slots {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px;
    text-align: center;
    
    .large-icon {
      font-size: 2.5rem;
      color: rgba(255, 255, 255, 0.3);
      margin-bottom: 16px;
    }
    
    h4 {
      margin: 0 0 8px 0;
      color: rgba(255, 255, 255, 0.8);
    }
    
    p {
      margin: 0;
      color: rgba(255, 255, 255, 0.6);
    }
  }
}

.legend {
  display: flex;
  justify-content: center;
  gap: 24px;
  margin-bottom: 24px;
  padding: 16px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  
  .legend-item {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.9rem;
    
    .legend-icon {
      font-size: 1.1rem;
      
      &.available {
        color: #4ade80;
      }
      
      &.booked {
        color: #f59e0b;
      }
      
      &.unavailable {
        color: rgba(255, 255, 255, 0.3);
      }
    }
    
    span {
      color: rgba(255, 255, 255, 0.8);
    }
  }
}

.availability-actions {
  display: flex;
  justify-content: center;
  margin-bottom: 16px;
}

.help-text {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px;
  background: rgba(59, 130, 246, 0.1);
  border: 1px solid rgba(59, 130, 246, 0.2);
  border-radius: 8px;
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.8);
  
  ion-icon {
    color: #3b82f6;
    font-size: 1.1rem;
  }
}

// Responsive design
@media (max-width: 768px) {
  .availability-container {
    padding: 16px;
  }
  
  .availability-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }
  
  .time-slots-grid {
    grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
    gap: 8px;
  }
  
  .legend {
    flex-direction: column;
    gap: 12px;
    align-items: center;
  }
}
