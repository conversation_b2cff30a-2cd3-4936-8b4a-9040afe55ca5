.lawyer-list-container {
  padding: 0;
  margin-left: 250px;
}

.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  
  h1 {
    margin: 0;
    font-size: 2rem;
    font-weight: 700;
    color: #333;
  }
  
  .header-actions {
    display: flex;
    gap: 15px;

    .search-container {
      position: relative;

      .search-input {
        width: 250px;
        padding: 10px 40px 10px 15px;
        border: 1px solid #e1e5e9;
        border-radius: 6px;
        font-size: 0.9rem;

        &:focus {
          outline: none;
          border-color: var(--admin-primary);
        }
      }

      .search-icon {
        position: absolute;
        right: 12px;
        top: 50%;
        transform: translateY(-50%);
        color: #666;
        pointer-events: none;
      }
    }

    .filter-select {
      padding: 10px 15px;
      border: 1px solid #e1e5e9;
      border-radius: 6px;
      font-size: 0.9rem;

      &:focus {
        outline: none;
        border-color: var(--admin-primary);
      }
    }
  }
}

.lawyers-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 20px;
  
  .lawyer-card {
    background: white;
    border: 1px solid #e1e5e9;
    border-radius: 12px;
    padding: 20px;
    transition: all 0.3s ease;
    
    &:hover {
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
      transform: translateY(-2px);
    }
    
    .lawyer-header {
      display: flex;
      align-items: flex-start;
      gap: 15px;
      margin-bottom: 15px;
      
      .lawyer-avatar {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        border: 2px solid #e1e5e9;
        overflow: hidden;
        position: relative;

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
          display: block;
        }

        .avatar-placeholder {
          width: 100%;
          height: 100%;
          background: linear-gradient(135deg, var(--admin-primary), #d4a574);
          display: flex;
          align-items: center;
          justify-content: center;
          color: white;
          font-weight: 600;
          font-size: 20px;
          text-transform: uppercase;
        }
      }
      
      .lawyer-info {
        flex: 1;
        
        h3 {
          margin: 0 0 5px 0;
          font-size: 1.2rem;
          font-weight: 600;
          color: #333;
        }
        
        .roll-number, .firm {
          margin: 0 0 3px 0;
          font-size: 0.9rem;
          color: #666;
        }
      }
      
      .status-badge {
        padding: 4px 8px;
        border-radius: 12px;
        font-size: 0.75rem;
        font-weight: 600;
        
        &.verified {
          background: rgba(40, 167, 69, 0.1);
          color: #28a745;
        }
        
        &.pending {
          background: rgba(255, 193, 7, 0.1);
          color: #ffc107;
        }
        
        &.suspended {
          background: rgba(220, 53, 69, 0.1);
          color: #dc3545;
        }
      }
    }
    
    .lawyer-details {
      margin-bottom: 15px;
      
      p {
        margin: 0 0 5px 0;
        font-size: 0.85rem;
        color: #666;
        
        strong {
          color: #333;
        }
      }
    }
    
    .lawyer-actions {
      display: flex;
      gap: 8px;
      
      .action-btn {
        padding: 6px 12px;
        border: none;
        border-radius: 4px;
        font-size: 0.8rem;
        cursor: pointer;
        transition: all 0.3s ease;
        
        &.view {
          background: #17a2b8;
          color: white;
          
          &:hover {
            background: #138496;
          }
        }
        
        &.edit {
          background: #6c757d;
          color: white;
          
          &:hover {
            background: #5a6268;
          }
        }
        
        &.verify {
          background: #28a745;
          color: white;
          
          &:hover {
            background: #218838;
          }
        }
        
        &.suspend {
          background: #dc3545;
          color: white;
          
          &:hover {
            background: #c82333;
          }
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .lawyer-list-container {
    margin-left: 0;
    padding: 15px;
  }
  
  .list-header {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
    
    .header-actions {
      flex-direction: column;

      .search-container .search-input {
        width: 100%;
      }
    }
  }
  
  .lawyers-grid {
    grid-template-columns: 1fr;
  }
}

// Loading, Error, and Empty States
.loading-container,
.error-container,
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
}

.loading-container {
  .loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid var(--admin-primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 20px;
  }

  p {
    color: #666;
    font-size: 1rem;
    margin: 0;
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-container {
  .error-message {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 15px;

    svg {
      color: #dc3545;
    }

    p {
      color: #dc3545;
      font-size: 1rem;
      margin: 0;
    }

    .retry-btn {
      background: var(--admin-primary);
      color: white;
      border: none;
      padding: 10px 20px;
      border-radius: 6px;
      cursor: pointer;
      font-size: 0.9rem;
      transition: background-color 0.3s ease;

      &:hover {
        background: var(--admin-primary-dark);
      }
    }
  }
}

.empty-container {
  .empty-message {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 15px;

    svg {
      color: #999;
    }

    h3 {
      color: #333;
      font-size: 1.2rem;
      margin: 0;
    }

    p {
      color: #666;
      font-size: 0.95rem;
      margin: 0;
      max-width: 400px;
    }
  }
}
