import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { FirebaseService, ClientProfile } from '../services/firebase.service';

@Component({
  selector: 'app-client-profile',
  templateUrl: './client-profile.page.html',
  styleUrls: ['./client-profile.page.scss'],
  standalone: false,
})
export class ClientProfilePage implements OnInit {
  clientProfile: ClientProfile | null = null;

  constructor(
    private router: Router,
    private firebaseService: FirebaseService
  ) { }

  ngOnInit() {
    this.loadClientProfile();
  }

  async loadClientProfile() {
    const currentUser = this.firebaseService.getCurrentUser();
    if (currentUser) {
      this.clientProfile = await this.firebaseService.getClientProfile(currentUser.uid);
    }
  }

  async onLogout() {
    try {
      await this.firebaseService.signOut();
      this.router.navigate(['/auth/signin']);
    } catch (error) {
      console.error('Error signing out:', error);
    }
  }
}
