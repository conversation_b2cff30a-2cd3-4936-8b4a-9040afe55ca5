import { Component, Input, Output, EventEmitter } from '@angular/core';

export interface Lawyer {
  id: string;
  name: string;
  firm: string;
  location: string;
  bio: string;
  avatar: string;
  phone: string;
  email: string;
  specialties: string[];
  rating: number;
}

@Component({
  selector: 'app-lawyer-card',
  templateUrl: './lawyer-card.component.html',
  styleUrls: ['./lawyer-card.component.scss'],
  standalone: false,
})
export class LawyerCardComponent {
  @Input() lawyer!: Lawyer;
  @Input() showActions: boolean = true;
  @Output() viewDetails = new EventEmitter<Lawyer>();
  @Output() requestRetainer = new EventEmitter<Lawyer>();

  onViewDetails() {
    this.viewDetails.emit(this.lawyer);
  }

  onRequestRetainer() {
    this.requestRetainer.emit(this.lawyer);
  }

  getStarArray(rating: number): boolean[] {
    const stars = [];
    for (let i = 1; i <= 5; i++) {
      stars.push(i <= Math.floor(rating));
    }
    return stars;
  }

  getInitials(name: string): string {
    return name.split(' ').map(n => n[0]).join('');
  }
}
