// Header Styling
.generate-toolbar {
  --background: #B88A42;
  --color: #ffffff;
}

.back-icon {
  font-size: 24px;
  color: #ffffff;
}

.generate-title {
  font-size: 18px;
  font-weight: 600;
  color: #ffffff;
}

// Content Styling
.generate-content {
  --background: #f8f9fa;
}

.generate-container {
  padding: 20px;
  min-height: 100vh;
}

// Header Section
.header-section {
  margin-bottom: 24px;
}

.description-text {
  font-size: 14px;
  color: #495057;
  line-height: 1.5;
  margin: 0;
  text-align: center;
}

// Document Types Section
.document-types-section {
  margin-bottom: 32px;
}

.section-title {
  font-size: 14px;
  font-weight: 600;
  color: #000000;
  margin: 0 0 16px 0;
}

.document-types-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
}

.document-type-card {
  background: #ffffff;
  border: 2px solid #e5e5e5;
  border-radius: 12px;
  padding: 16px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:hover {
    border-color: #B88A42;
    box-shadow: 0 2px 12px rgba(184, 138, 66, 0.1);
  }
  
  &.selected {
    border-color: #B88A42;
    background: rgba(184, 138, 66, 0.05);
    box-shadow: 0 2px 12px rgba(184, 138, 66, 0.15);
  }
}

.type-icon {
  width: 48px;
  height: 48px;
  background: rgba(184, 138, 66, 0.1);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 12px auto;
  
  ion-icon {
    font-size: 24px;
    color: #B88A42;
  }
}

.type-name {
  font-size: 14px;
  font-weight: 600;
  color: #000000;
  margin: 0 0 4px 0;
}

.type-description {
  font-size: 12px;
  color: #666666;
  margin: 0;
  line-height: 1.3;
}

// Template Details Section
.template-details-section {
  margin-bottom: 24px;
}

.document-form {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.form-field {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.field-label {
  font-size: 14px;
  font-weight: 500;
  color: #000000;
  
  .required-indicator {
    color: #dc3545;
    margin-left: 2px;
  }
}

.field-input,
.field-textarea,
.field-select,
.field-date {
  --background: #ffffff;
  --color: #000000;
  --border-color: #d4af37;
  --border-radius: 8px;
  --padding-start: 16px;
  --padding-end: 16px;
  border: 2px solid #d4af37;
  border-radius: 8px;
  
  &:focus {
    --border-color: #B88A42;
  }
}

.field-textarea {
  --padding-top: 12px;
  --padding-bottom: 12px;
}

.error-message {
  margin-top: 4px;
  
  small {
    font-size: 12px;
  }
}

// Download Section
.download-section {
  margin-bottom: 32px;
}

.download-card {
  background: #ffffff;
  border-radius: 12px;
  padding: 16px;
  display: flex;
  align-items: center;
  gap: 16px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);
  border: 1px solid #e5e5e5;
  cursor: pointer;
  transition: all 0.2s ease;
  
  &:hover {
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  }
  
  &:active {
    transform: scale(0.98);
  }
}

.download-icon {
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, #B88A42 0%, #D4AF37 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  
  ion-icon {
    font-size: 24px;
    color: #ffffff;
  }
}

.download-content {
  flex: 1;
}

.download-title {
  font-size: 16px;
  font-weight: 600;
  color: #000000;
  margin: 0 0 4px 0;
}

.download-subtitle {
  font-size: 12px;
  color: #666666;
  margin: 0;
}

.download-action {
  ion-icon {
    font-size: 20px;
    color: #888888;
  }
}

// Generate Button Section
.generate-section {
  text-align: center;
}

.generate-btn {
  background: #0A49FF;
  color: #ffffff;
  border: none;
  border-radius: 16px;
  padding: 16px 32px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  width: 100%;
  max-width: 300px;
  margin: 0 auto;
  
  &:hover {
    background: #0841e6;
  }
  
  &:active {
    transform: scale(0.95);
  }
  
  &:disabled {
    background: #e9ecef;
    color: #6c757d;
    cursor: not-allowed;
    transform: none;
  }
}

.btn-icon {
  font-size: 18px;
}

// Responsive Design
@media (max-width: 375px) {
  .generate-container {
    padding: 16px;
  }
  
  .document-types-grid {
    grid-template-columns: 1fr;
  }
  
  .type-name {
    font-size: 13px;
  }
  
  .type-description {
    font-size: 11px;
  }
}
