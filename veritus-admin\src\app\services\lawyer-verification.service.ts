import { Injectable } from '@angular/core';
import { AngularFirestore } from '@angular/fire/compat/firestore';
import { AngularFireStorage } from '@angular/fire/compat/storage';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import firebase from 'firebase/compat/app';

export interface VerificationAction {
  notes: string;
  ibpStatus?: string;
  reason?: string;
  actionBy?: string;
  actionAt?: Date;
}

export interface LawyerVerificationData {
  id: string;
  name: string;
  email: string;
  phone: string;
  rollNumber: string;
  firm: string;
  yearsOfPractice: number;
  specialization: string;
  status: 'pending' | 'approved' | 'rejected' | 'under_review';
  submittedAt: Date;
  verificationProgress: number;
  documents: any[];
  verificationHistory: VerificationAction[];
}

@Injectable({
  providedIn: 'root'
})
export class LawyerVerificationService {

  constructor(
    private firestore: AngularFirestore,
    private storage: AngularFireStorage
  ) { }

  // Get all pending lawyer verifications
  getPendingVerifications(): Observable<LawyerVerificationData[]> {
    return this.firestore
      .collection('lawyer_verifications', ref => 
        ref.where('status', '==', 'pending').orderBy('submittedAt', 'desc')
      )
      .snapshotChanges()
      .pipe(
        map(actions => actions.map(a => {
          const data = a.payload.doc.data() as LawyerVerificationData;
          const id = a.payload.doc.id;
          return { ...data, id };
        }))
      );
  }

  // Get all lawyer verifications with optional status filter
  getAllVerifications(status?: string): Observable<LawyerVerificationData[]> {
    let query = this.firestore.collection('lawyer_verifications', ref => 
      ref.orderBy('submittedAt', 'desc')
    );

    if (status) {
      query = this.firestore.collection('lawyer_verifications', ref => 
        ref.where('status', '==', status).orderBy('submittedAt', 'desc')
      );
    }

    return query.snapshotChanges().pipe(
      map(actions => actions.map(a => {
        const data = a.payload.doc.data() as LawyerVerificationData;
        const id = a.payload.doc.id;
        return { ...data, id };
      }))
    );
  }

  // Get specific lawyer verification by ID
  getVerificationById(id: string): Observable<LawyerVerificationData | null> {
    return this.firestore
      .collection('lawyer_verifications')
      .doc(id)
      .snapshotChanges()
      .pipe(
        map(action => {
          if (action.payload.exists) {
            const data = action.payload.data() as LawyerVerificationData;
            return { ...data, id: action.payload.id };
          }
          return null;
        })
      );
  }

  // Approve lawyer verification
  async approveLawyer(lawyerId: string, action: VerificationAction): Promise<boolean> {
    try {
      const verificationAction: VerificationAction = {
        ...action,
        actionBy: 'current-admin-id', // Replace with actual admin ID
        actionAt: new Date()
      };

      await this.firestore.collection('lawyer_verifications').doc(lawyerId).update({
        status: 'approved',
        verificationProgress: 100,
        approvedAt: new Date(),
        verificationHistory: firebase.firestore.FieldValue.arrayUnion(verificationAction)
      });

      // Update the lawyer's profile to mark as verified
      await this.updateLawyerProfile(lawyerId, { isVerified: true, verifiedAt: new Date() });

      // Send approval notification
      await this.sendVerificationNotification(lawyerId, 'approved', action.notes);

      return true;
    } catch (error) {
      console.error('Error approving lawyer:', error);
      return false;
    }
  }

  // Reject lawyer verification
  async rejectLawyer(lawyerId: string, action: VerificationAction): Promise<boolean> {
    try {
      const verificationAction: VerificationAction = {
        ...action,
        actionBy: 'current-admin-id', // Replace with actual admin ID
        actionAt: new Date()
      };

      await this.firestore.collection('lawyer_verifications').doc(lawyerId).update({
        status: 'rejected',
        rejectedAt: new Date(),
        rejectionReason: action.reason,
        verificationHistory: firebase.firestore.FieldValue.arrayUnion(verificationAction)
      });

      // Send rejection notification
      await this.sendVerificationNotification(lawyerId, 'rejected', action.notes);

      return true;
    } catch (error) {
      console.error('Error rejecting lawyer:', error);
      return false;
    }
  }

  // Request additional documents
  async requestAdditionalDocuments(lawyerId: string, message: string): Promise<boolean> {
    try {
      const verificationAction: VerificationAction = {
        notes: message,
        actionBy: 'current-admin-id', // Replace with actual admin ID
        actionAt: new Date()
      };

      await this.firestore.collection('lawyer_verifications').doc(lawyerId).update({
        status: 'pending',
        additionalDocsRequested: true,
        additionalDocsMessage: message,
        verificationHistory: firebase.firestore.FieldValue.arrayUnion(verificationAction)
      });

      // Send notification requesting additional documents
      await this.sendVerificationNotification(lawyerId, 'additional_docs_required', message);

      return true;
    } catch (error) {
      console.error('Error requesting additional documents:', error);
      return false;
    }
  }

  // Update verification status to under review
  async setUnderReview(lawyerId: string, progress: number = 50): Promise<boolean> {
    try {
      await this.firestore.collection('lawyer_verifications').doc(lawyerId).update({
        status: 'under_review',
        verificationProgress: progress,
        reviewStartedAt: new Date()
      });

      return true;
    } catch (error) {
      console.error('Error setting under review:', error);
      return false;
    }
  }

  // Verify with IBP (Integrated Bar of the Philippines)
  async verifyWithIBP(rollNumber: string): Promise<{ verified: boolean; details?: any }> {
    try {
      // Mock IBP verification - replace with actual IBP API integration
      // This would typically involve calling an external API
      
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Mock response based on roll number pattern
      const isValid = /^\d{6}$/.test(rollNumber);
      
      if (isValid) {
        return {
          verified: true,
          details: {
            rollNumber,
            status: 'Active',
            admissionDate: '2015-04-15',
            chapter: 'Manila Chapter'
          }
        };
      } else {
        return {
          verified: false,
          details: {
            error: 'Roll number not found in IBP database'
          }
        };
      }
    } catch (error) {
      console.error('Error verifying with IBP:', error);
      return { verified: false };
    }
  }

  // Get verification statistics
  async getVerificationStats(): Promise<any> {
    try {
      const collections = await Promise.all([
        this.firestore.collection('lawyer_verifications', ref => ref.where('status', '==', 'pending')).get().toPromise(),
        this.firestore.collection('lawyer_verifications', ref => ref.where('status', '==', 'approved')).get().toPromise(),
        this.firestore.collection('lawyer_verifications', ref => ref.where('status', '==', 'rejected')).get().toPromise(),
        this.firestore.collection('lawyer_verifications', ref => ref.where('status', '==', 'under_review')).get().toPromise()
      ]);

      return {
        pending: collections[0]?.size || 0,
        approved: collections[1]?.size || 0,
        rejected: collections[2]?.size || 0,
        underReview: collections[3]?.size || 0,
        total: (collections[0]?.size || 0) + (collections[1]?.size || 0) + (collections[2]?.size || 0) + (collections[3]?.size || 0)
      };
    } catch (error) {
      console.error('Error getting verification stats:', error);
      return { pending: 0, approved: 0, rejected: 0, underReview: 0, total: 0 };
    }
  }

  // Private helper methods
  private async updateLawyerProfile(lawyerId: string, updates: any): Promise<void> {
    try {
      await this.firestore.collection('lawyers').doc(lawyerId).update(updates);
    } catch (error) {
      console.error('Error updating lawyer profile:', error);
    }
  }

  private async sendVerificationNotification(lawyerId: string, type: string, message: string): Promise<void> {
    try {
      const notification = {
        recipientId: lawyerId,
        type: 'verification_update',
        status: type,
        message,
        createdAt: new Date(),
        read: false
      };

      await this.firestore.collection('notifications').add(notification);

      // Here you could also integrate with email/SMS services
      // await this.emailService.sendVerificationEmail(lawyerId, type, message);
    } catch (error) {
      console.error('Error sending notification:', error);
    }
  }

  // Document management
  async uploadVerificationDocument(file: File, lawyerId: string): Promise<string> {
    try {
      const filePath = `verification-documents/${lawyerId}/${Date.now()}_${file.name}`;
      const fileRef = this.storage.ref(filePath);
      const uploadTask = await fileRef.put(file);
      return await uploadTask.ref.getDownloadURL();
    } catch (error) {
      console.error('Error uploading document:', error);
      throw error;
    }
  }

  async deleteVerificationDocument(documentUrl: string): Promise<boolean> {
    try {
      const fileRef = this.storage.storage.refFromURL(documentUrl);
      await fileRef.delete();
      return true;
    } catch (error) {
      console.error('Error deleting document:', error);
      return false;
    }
  }
}
