.files-content {
  --background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
}

.files-container {
  padding: 20px;
  min-height: 100vh;
}

.filters-section {
  margin-bottom: 20px;
}

.filter-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15px;
  margin-bottom: 15px;
}

.filter-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.filter-label {
  font-weight: 500;
}

.lawyer-select, .type-select {
  --background: rgba(255, 255, 255, 0.1);
  --color: white;
  --border-radius: 8px;
  --padding-start: 12px;
  --padding-end: 12px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
}

.search-section {
  margin-top: 15px;
}

.custom-searchbar {
  --background: rgba(255, 255, 255, 0.1);
  --color: white;
  --placeholder-color: rgba(255, 255, 255, 0.6);
  --icon-color: rgba(255, 255, 255, 0.6);
  --border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
}

.action-section {
  margin-bottom: 25px;
}

.upload-btn {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.btn-icon {
  font-size: 18px;
}

.files-section {
  margin-bottom: 30px;
}

.section-title {
  margin-bottom: 15px;
}

.files-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.file-card {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 16px;
  display: grid;
  grid-template-columns: auto 1fr auto;
  gap: 15px;
  align-items: center;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
  
  &:hover {
    background: rgba(255, 255, 255, 0.15);
    transform: translateY(-2px);
  }
}

.file-icon {
  width: 48px;
  height: 48px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  
  ion-icon {
    font-size: 24px;
  }
}

.file-details {
  flex: 1;
}

.file-name {
  margin-bottom: 4px;
}

.file-info {
  margin-bottom: 4px;
}

.file-case {
  margin-bottom: 4px;
}

.file-date {
  opacity: 0.8;
}

.file-actions {
  display: flex;
  gap: 8px;
}

.action-btn {
  width: 36px;
  height: 36px;
  border-radius: 8px;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  
  &:hover {
    transform: scale(1.1);
  }
  
  ion-icon {
    font-size: 16px;
  }
}

.download-btn {
  background: rgba(76, 175, 80, 0.2);
  color: #4caf50;
  
  &:hover {
    background: rgba(76, 175, 80, 0.3);
  }
}

.delete-btn {
  background: rgba(244, 67, 54, 0.2);
  color: #f44336;
  
  &:hover {
    background: rgba(244, 67, 54, 0.3);
  }
}

.empty-state {
  text-align: center;
  padding: 40px 20px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.empty-icon {
  font-size: 48px;
  color: #d4af37;
  margin-bottom: 16px;
}

.empty-title {
  margin-bottom: 8px;
}

.empty-description {
  margin-bottom: 20px;
}

.stats-section {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 15px;
}

.stat-card {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 16px;
  text-align: center;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.stat-number {
  margin-bottom: 4px;
}

.stat-label {
  font-size: 11px;
}

@media (max-width: 768px) {
  .filter-row {
    grid-template-columns: 1fr;
  }
  
  .file-card {
    grid-template-columns: 1fr;
    text-align: center;
    gap: 10px;
  }
  
  .file-actions {
    justify-content: center;
  }
  
  .stats-section {
    grid-template-columns: 1fr;
  }
}
