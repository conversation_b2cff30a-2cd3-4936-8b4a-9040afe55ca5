<ion-content class="modern-auth-content">
  <div class="auth-wrapper">
    <!-- Background Elements -->
    <div class="background-elements">
      <div class="gold-accent-1"></div>
      <div class="gold-accent-2"></div>
      <div class="gold-accent-3"></div>
    </div>

    <!-- Main Content -->
    <div class="auth-main-container">
      <!-- Logo Section -->
      <div class="logo-section">
        <div class="logo-container">
          <div class="logo-icon">
            <ion-icon name="scale-outline" class="justice-scale-icon"></ion-icon>
          </div>
          <h1 class="brand-title">Veritus</h1>
          <p class="brand-subtitle">Legal Excellence Platform</p>
        </div>
      </div>

      <!-- Form Card -->
      <div class="form-card">
        <div class="form-header">
          <div class="header-with-back">
            <button type="button" class="back-btn" (click)="goBackToRoleSelection()">
              <ion-icon name="arrow-back-outline"></ion-icon>
            </button>
            <div class="header-content">
              <h2 class="form-title">Create Account</h2>
              <p class="form-subtitle">
                Register as
                <span class="role-badge">
                  <ion-icon
                    [name]="selectedRole === 'client' ? 'person-outline' : selectedRole === 'lawyer' ? 'briefcase-outline' : 'document-text-outline'"
                    class="role-badge-icon">
                  </ion-icon>
                  {{ selectedRole | titlecase }}
                </span>
              </p>
            </div>
          </div>
        </div>

        <!-- Registration Form -->
        <form [formGroup]="registerForm" (ngSubmit)="onRegister()" class="modern-form">
          <!-- Name Field -->
          <div class="input-group">
            <label class="input-label">Full Name</label>
            <div class="input-container">
              <ion-icon name="person-outline" class="input-icon"></ion-icon>
              <input
                type="text"
                formControlName="name"
                placeholder="Enter your full name"
                class="modern-input">
            </div>
          </div>

          <!-- Email Field -->
          <div class="input-group">
            <label class="input-label">Email Address</label>
            <div class="input-container">
              <ion-icon name="mail-outline" class="input-icon"></ion-icon>
              <input
                type="email"
                formControlName="email"
                placeholder="Enter your email"
                class="modern-input">
            </div>
          </div>

          <!-- Lawyer specific fields -->
          <div class="input-group" *ngIf="selectedRole === 'lawyer'">
            <label class="input-label">Roll Number</label>
            <div class="input-container">
              <ion-icon name="card-outline" class="input-icon"></ion-icon>
              <input
                type="text"
                formControlName="rollNumber"
                placeholder="Enter your roll number"
                class="modern-input">
            </div>
          </div>

          <div class="input-group" *ngIf="selectedRole === 'lawyer'">
            <label class="input-label">Bar ID</label>
            <div class="input-container">
              <ion-icon name="shield-outline" class="input-icon"></ion-icon>
              <input
                type="text"
                formControlName="barId"
                placeholder="Enter your bar ID"
                class="modern-input">
            </div>
          </div>



          <!-- Password Field -->
          <div class="input-group">
            <label class="input-label">Password</label>
            <div class="input-container">
              <ion-icon name="lock-closed-outline" class="input-icon"></ion-icon>
              <input
                [type]="showPassword ? 'text' : 'password'"
                formControlName="password"
                placeholder="Create a password"
                class="modern-input password-input">
              <button
                type="button"
                class="password-toggle-btn"
                (click)="togglePasswordVisibility()">
                <ion-icon
                  [name]="showPassword ? 'eye-off-outline' : 'eye-outline'"
                  class="password-toggle-icon">
                </ion-icon>
              </button>
            </div>
          </div>

          <!-- Confirm Password Field -->
          <div class="input-group">
            <label class="input-label">Confirm Password</label>
            <div class="input-container">
              <ion-icon name="lock-closed-outline" class="input-icon"></ion-icon>
              <input
                [type]="showConfirmPassword ? 'text' : 'password'"
                formControlName="confirmPassword"
                placeholder="Confirm your password"
                class="modern-input password-input">
              <button
                type="button"
                class="password-toggle-btn"
                (click)="toggleConfirmPasswordVisibility()">
                <ion-icon
                  [name]="showConfirmPassword ? 'eye-off-outline' : 'eye-outline'"
                  class="password-toggle-icon">
                </ion-icon>
              </button>
            </div>
          </div>

          <!-- Sign Up Button -->
          <button
            type="submit"
            class="modern-signup-btn"
            [disabled]="!isFormValid()">
            <span class="btn-text">Create Account</span>
            <ion-icon name="arrow-forward" class="btn-icon"></ion-icon>
          </button>
        </form>

        <!-- Divider -->
        <div class="divider-section">
          <div class="divider-line"></div>
          <span class="divider-text">OR</span>
          <div class="divider-line"></div>
        </div>

        <!-- Google Sign Up -->
        <button type="button" class="google-signup-btn" (click)="onGoogleRegister()">
          <div class="google-icon-container">
            <svg class="google-logo" viewBox="0 0 24 24" width="20" height="20">
              <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
              <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
              <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
              <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
            </svg>
          </div>
          <span class="google-text">Sign up with Google</span>
        </button>

        <!-- Sign In Link -->
        <div class="signin-section">
          <span class="signin-text">Already have an account? </span>
          <button type="button" class="signin-link" (click)="goToSignIn()">
            Sign In
          </button>
        </div>
      </div>
    </div>
  </div>
</ion-content>
