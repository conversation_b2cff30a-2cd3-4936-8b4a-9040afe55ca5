import { Injectable } from '@angular/core';
import { initializeApp, FirebaseApp } from 'firebase/app';
import { increment } from 'firebase/firestore';
// Import types from auth module for compatibility
// TODO: Migrate to use AuthService from modules/auth/services/auth.service
import { 
  getAuth, 
  Auth, 
  signInWithEmailAndPassword, 
  createUserWithEmailAndPassword,
  signOut,
  sendPasswordResetEmail,
  User,
  onAuthStateChanged
} from 'firebase/auth';
import {
  getFirestore,
  Firestore,
  doc,
  setDoc,
  getDoc,
  collection,
  addDoc,
  query,
  where,
  orderBy,
  getDocs,
  updateDoc,
  deleteDoc
} from 'firebase/firestore';
import {
  getStorage,
  ref,
  uploadBytes,
  uploadBytesResumable,
  getDownloadURL,
  deleteObject
} from 'firebase/storage';
import { environment } from '../../environments/environment';
import { BehaviorSubject, Observable } from 'rxjs';

export interface LawyerProfile {
  uid: string;
  email: string;
  name: string;
  rollNumber: string;
  barId: string;
  phone?: string;
  avatar?: string;
  firmName?: string; // Law firm name
  role: 'lawyer';
  secretaryCode?: string; // Generated code for secretary linking
  createdAt: Date;
  updatedAt: Date;
}

export interface SecretaryProfile {
  uid: string;
  email: string;
  name: string;
  phone?: string;
  avatar?: string;
  role: 'secretary';
  linkedLawyers: string[]; // Array of lawyer UIDs
  permissions: SecretaryPermissions;
  createdAt: Date;
  updatedAt: Date;
}

export interface SecretaryPermissions {
  canManageCalendar: boolean;
  canManageFiles: boolean;
  canManageCases: boolean;
  canManageRetainers: boolean;
  canViewFinances: boolean;
  canManageFinances: boolean;
}

export interface LawyerSecretaryLink {
  id?: string;
  lawyerId: string;
  secretaryId: string;
  lawyerName: string;
  secretaryName: string;
  status: 'pending' | 'approved' | 'rejected';
  permissions: SecretaryPermissions;
  requestedAt: Date;
  respondedAt?: Date;
  createdAt: Date;
}

export interface ClientProfile {
  uid: string;
  email: string;
  name: string;
  phone?: string;
  avatar?: string;
  role: 'client';
  createdAt: Date;
  updatedAt: Date;
}

export interface AuditLog {
  id?: string;
  userId: string;
  userRole: 'lawyer' | 'secretary' | 'client';
  userName: string;
  action: string;
  entityType: string;
  entityId: string;
  changes?: any;
  metadata?: any;
  timestamp: Date;
}

export interface Case {
  id?: string;
  lawyerId: string;
  clientName: string;
  title: string;
  status: 'ongoing' | 'closed' | 'cancelled';
  description?: string;
  fileCount: number;
  createdAt: Date;
  updatedAt: Date;
}

export interface Appointment {
  id?: string;
  lawyerId: string;
  clientName: string;
  type: string;
  status: 'new' | 'confirmed' | 'completed' | 'cancelled';
  date: string;
  time: string;
  notes?: string;
  createdBy?: string; 
  createdByRole?: 'lawyer' | 'secretary';
  createdAt: Date;
  updatedAt: Date;
}

@Injectable({
  providedIn: 'root'
})
export class FirebaseService {
  private app: FirebaseApp;
  private auth: Auth;
  private firestore: Firestore;
  private storage: any; 
  private currentUserSubject = new BehaviorSubject<User | null>(null);
  public currentUser$ = this.currentUserSubject.asObservable();

  constructor() {
    // Initialize Firebase
    this.app = initializeApp(environment.firebase);
    this.auth = getAuth(this.app);
    this.firestore = getFirestore(this.app);
    this.storage = getStorage(this.app);

    // Listen to auth state changes
    onAuthStateChanged(this.auth, (user) => {
      this.currentUserSubject.next(user);
    });
  }

  // Authentication Methods
  async signIn(email: string, password: string): Promise<User> {
    const userCredential = await signInWithEmailAndPassword(this.auth, email, password);
    return userCredential.user;
  }

  async signUp(email: string, password: string, userData: any): Promise<User> {
    const userCredential = await createUserWithEmailAndPassword(this.auth, email, password);
    const user = userCredential.user;

    // Create profile based on role
    if (userData.role === 'lawyer') {
      const lawyerProfile: LawyerProfile = {
        uid: user.uid,
        email: user.email!,
        name: userData.name || email.split('@')[0], // Use the provided name
        rollNumber: userData.rollNumber || '',
        barId: userData.barId || '',
        phone: userData.phone || '',
        role: 'lawyer',
        secretaryCode: this.generateSecretaryCode(),
        createdAt: new Date(),
        updatedAt: new Date()
      };
      await this.createLawyerProfile(lawyerProfile);
    } else if (userData.role === 'secretary') {
      const secretaryProfile: SecretaryProfile = {
        uid: user.uid,
        email: user.email!,
        name: userData.name || email.split('@')[0],
        phone: userData.phone || '',
        role: 'secretary',
        linkedLawyers: [],
        permissions: {
          canManageCalendar: true,
          canManageFiles: true,
          canManageCases: true,
          canManageRetainers: true,
          canViewFinances: false,
          canManageFinances: false
        },
        createdAt: new Date(),
        updatedAt: new Date()
      };
      await this.createSecretaryProfile(secretaryProfile);

      // If lawyer code provided, create link request
      if (userData.lawyerCode) {
        await this.requestLawyerLink(user.uid, userData.lawyerCode);
      }
    } else if (userData.role === 'client') {
      const clientProfile: ClientProfile = {
        uid: user.uid,
        email: user.email!,
        name: userData.name || email.split('@')[0],
        phone: userData.phone || '',
        role: 'client',
        createdAt: new Date(),
        updatedAt: new Date()
      };
      await this.createClientProfile(clientProfile);
    }

    return user;
  }

  async signOut(): Promise<void> {
    await signOut(this.auth);
  }

  async resetPassword(email: string): Promise<void> {
    await sendPasswordResetEmail(this.auth, email);
  }

  getCurrentUser(): User | null {
    return this.auth.currentUser;
  }

  // Lawyer Profile Methods
  async createLawyerProfile(profile: LawyerProfile): Promise<void> {
    const docRef = doc(this.firestore, 'lawyers', profile.uid);
    await setDoc(docRef, profile);
  }

  async getLawyerProfile(uid: string): Promise<LawyerProfile | null> {
    const docRef = doc(this.firestore, 'lawyers', uid);
    const docSnap = await getDoc(docRef);
    
    if (docSnap.exists()) {
      return docSnap.data() as LawyerProfile;
    }
    return null;
  }

  async updateLawyerProfile(uid: string, updates: Partial<LawyerProfile>): Promise<void> {
    const docRef = doc(this.firestore, 'lawyers', uid);
    await updateDoc(docRef, { ...updates, updatedAt: new Date() });
  }

  // Case Management Methods
  async createCase(caseData: Omit<Case, 'id'>): Promise<string> {
    const docRef = await addDoc(collection(this.firestore, 'cases'), caseData);
    return docRef.id;
  }

  async getCases(lawyerId: string, status?: string): Promise<Case[]> {
    let q = query(collection(this.firestore, 'cases'), where('lawyerId', '==', lawyerId));

    if (status) {
      q = query(q, where('status', '==', status));
    }

    const querySnapshot = await getDocs(q);
    return querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    } as Case));
  } 

  async updateCase(
    caseId: string,
    updates: Partial<Case>,
    updatedBy?: string,
    updatedByRole?: 'lawyer' | 'secretary'
  ): Promise<void> {
    const docRef = doc(this.firestore, 'cases', caseId);
    await updateDoc(docRef, { ...updates, updatedAt: new Date() });

    if (updatedBy && updatedByRole) {
      await this.logActivity(updatedBy, updatedByRole, 'User', 'UPDATE', 'case', caseId, updates);
    }
  }

  async deleteCase(caseId: string): Promise<void> {
    const docRef = doc(this.firestore, 'cases', caseId);
    await deleteDoc(docRef);
  }

  // Appointment Methods
  async createAppointment(appointmentData: Omit<Appointment, 'id'>): Promise<string> {
    const docRef = await addDoc(collection(this.firestore, 'appointments'), appointmentData);
    return docRef.id;
  }

  async getAppointments(lawyerId: string, status?: string): Promise<Appointment[]> {
    let q = query(collection(this.firestore, 'appointments'), where('lawyerId', '==', lawyerId));
    
    if (status) {
      q = query(q, where('status', '==', status));
    }

    const querySnapshot = await getDocs(q);
    return querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    } as Appointment));
  }

  async getLawyerAvailability() {
    const user = this.auth.currentUser;
    if (!user) return [];

    const ref = doc(this.firestore, 'lawyer_availability', user.uid);
    const snap = await getDoc(ref);
    return snap.exists() ? snap.data()['availability'] : [];
  }

  async saveLawyerAvailability(availability: any[]) {
    const user = this.auth.currentUser;
    if (!user) return;

    const ref = doc(this.firestore, 'lawyer_availability', user.uid);
    await setDoc(ref, { availability }, { merge: true });
  }
  async updateAppointment(
    appointmentId: string,
    updates: Partial<Appointment>,
    updatedBy?: string,
    updatedByRole?: 'lawyer' | 'secretary'
  ): Promise<void> {
    const docRef = doc(this.firestore, 'appointments', appointmentId);
    await updateDoc(docRef, { ...updates, updatedAt: new Date() });

    if (updatedBy && updatedByRole) {
      await this.logActivity(updatedBy, updatedByRole, 'User', 'UPDATE', 'appointment', appointmentId, updates);
    }
  }

  async deleteAppointment(appointmentId: string): Promise<void> {
    const docRef = doc(this.firestore, 'appointments', appointmentId);
    await deleteDoc(docRef);
  }

  // Statistics Methods
  async getDashboardStats(lawyerId: string): Promise<{ ongoingCases: number; completedCases: number }> {
    const ongoingCases = await this.getCases(lawyerId, 'ongoing');
    const completedCases = await this.getCases(lawyerId, 'closed');

    return {
      ongoingCases: ongoingCases.length,
      completedCases: completedCases.length
    };
  }

  // Secretary Dashboard Statistics
  async getSecretaryDashboardStats(secretaryId: string): Promise<any> {
    const secretary = await this.getSecretaryProfile(secretaryId);
    if (!secretary) return { linkedLawyers: 0, totalCases: 0, totalAppointments: 0 };

    let totalCases = 0;
    let totalAppointments = 0;

    for (const lawyerId of secretary.linkedLawyers) {
      const cases = await this.getCases(lawyerId);
      const appointments = await this.getAppointments(lawyerId);
      totalCases += cases.length;
      totalAppointments += appointments.length;
    }

    return {
      linkedLawyers: secretary.linkedLawyers.length,
      totalCases,
      totalAppointments
    };
  }

  // Enhanced Methods with Secretary Support
  async createCaseWithAudit(
    caseData: Omit<Case, 'id'>,
    createdBy: string,
    createdByRole: 'lawyer' | 'secretary'
  ): Promise<string> {
    const docRef = await addDoc(collection(this.firestore, 'cases'), caseData);
    await this.logActivity(
      createdBy,
      createdByRole,
      'User',
      'CREATE',
      'case',
      docRef.id,
      { title: caseData.title, clientName: caseData.clientName }
    );
    return docRef.id;
  }

  async createAppointmentWithAudit(
    appointmentData: Omit<Appointment, 'id'>,
    createdBy: string,
    createdByRole: 'lawyer' | 'secretary'
  ): Promise<string> {
    const enhancedData = {
      ...appointmentData,
      createdBy,
      createdByRole
    };

    const docRef = await addDoc(collection(this.firestore, 'appointments'), enhancedData);
    await this.logActivity(
      createdBy,
      createdByRole,
      'User',
      'CREATE',
      'appointment',
      docRef.id,
      { clientName: appointmentData.clientName, date: appointmentData.date, time: appointmentData.time }
    );
    return docRef.id;
  }

  // Secretary Profile Methods
  async createSecretaryProfile(profile: SecretaryProfile): Promise<void> {
    const docRef = doc(this.firestore, 'secretaries', profile.uid);
    await setDoc(docRef, profile);
    await this.logActivity(profile.uid, 'secretary', profile.name, 'CREATE', 'secretary_profile', profile.uid);
  }

  async getSecretaryProfile(uid: string): Promise<SecretaryProfile | null> {
    const docRef = doc(this.firestore, 'secretaries', uid);
    const docSnap = await getDoc(docRef);

    if (docSnap.exists()) {
      return docSnap.data() as SecretaryProfile;
    }
    return null;
  }

  async updateSecretaryProfile(uid: string, updates: Partial<SecretaryProfile>): Promise<void> {
    const docRef = doc(this.firestore, 'secretaries', uid);
    await updateDoc(docRef, { ...updates, updatedAt: new Date() });

    const secretary = await this.getSecretaryProfile(uid);
    if (secretary) {
      await this.logActivity(uid, 'secretary', secretary.name, 'UPDATE', 'secretary_profile', uid, updates);
    }
  }

  // Client Profile Methods
  async createClientProfile(profile: ClientProfile): Promise<void> {
    const docRef = doc(this.firestore, 'clients', profile.uid);
    await setDoc(docRef, profile);
  }

  async getClientProfile(uid: string): Promise<ClientProfile | null> {
    const docRef = doc(this.firestore, 'clients', uid);
    const docSnap = await getDoc(docRef);

    if (docSnap.exists()) {
      return docSnap.data() as ClientProfile;
    }
    return null;
  }

  // Lawyer-Secretary Linking Methods
  async requestLawyerLink(secretaryId: string, lawyerCode: string): Promise<void> {
    // Find lawyer by code
    const lawyersQuery = query(
      collection(this.firestore, 'lawyers'),
      where('secretaryCode', '==', lawyerCode)
    );
    const lawyersSnapshot = await getDocs(lawyersQuery);

    if (lawyersSnapshot.empty) {
      throw new Error('Invalid lawyer code');
    }

    const lawyerDoc = lawyersSnapshot.docs[0];
    const lawyer = lawyerDoc.data() as LawyerProfile;
    const secretary = await this.getSecretaryProfile(secretaryId);

    if (!secretary) {
      throw new Error('Secretary profile not found');
    }

    // Create link request
    const linkRequest: LawyerSecretaryLink = {
      lawyerId: lawyer.uid,
      secretaryId: secretaryId,
      lawyerName: lawyer.name,
      secretaryName: secretary.name,
      status: 'pending',
      permissions: secretary.permissions,
      requestedAt: new Date(),
      createdAt: new Date()
    };

    await addDoc(collection(this.firestore, 'lawyer_secretary_links'), linkRequest);
    await this.logActivity(secretaryId, 'secretary', secretary.name, 'REQUEST', 'lawyer_link', lawyer.uid);
  }

  async getSecretaryLinkedLawyers(secretaryId: string): Promise<LawyerProfile[]> {
    const secretary = await this.getSecretaryProfile(secretaryId);
    if (!secretary || secretary.linkedLawyers.length === 0) {
      return [];
    }

    const lawyers: LawyerProfile[] = [];
    for (const lawyerId of secretary.linkedLawyers) {
      const lawyer = await this.getLawyerProfile(lawyerId);
      if (lawyer) {
        lawyers.push(lawyer);
      }
    }
    return lawyers;
  }

  // Audit Logging Methods
  async logActivity(
    userId: string,
    userRole: 'lawyer' | 'secretary' | 'client',
    userName: string,
    action: string,
    entityType: string,
    entityId: string,
    changes?: any,
    metadata?: any
  ): Promise<void> {
    const auditLog: AuditLog = {
      userId,
      userRole,
      userName,
      action,
      entityType,
      entityId,
      changes,
      metadata,
      timestamp: new Date()
    };

    await addDoc(collection(this.firestore, 'audit_logs'), auditLog);
  }

  async uploadFileToStorage(path: string, blob: Blob): Promise<void> {
    const storage = getStorage();
    const storageRef = ref(storage, path);
    await uploadBytes(storageRef, blob);
  }
  
  async saveEncryptedFileMetadata(data: any): Promise<void> {
    const db = getFirestore();
    await addDoc(collection(db, 'encrypted_files'), data);
  }
  
  async getEncryptedFiles(caseId: string): Promise<any[]> {
    const db = getFirestore();
    const q = query(collection(db, 'encrypted_files'), where('caseId', '==', caseId));
    const snapshot = await getDocs(q);
    return snapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    }));
  }
  
  async downloadEncryptedBlob(path: string): Promise<Blob> {
    const storageRef = ref(this.storage, path);
    const url = await getDownloadURL(storageRef);
    const response = await fetch(url);
    return await response.blob();
  }
  
  async getDecryptionDetails(caseId: string): Promise<any[]> {
    const db = getFirestore();
    const q = query(collection(db, 'encrypted_files'), where('caseId', '==', caseId));
    const snap = await getDocs(q);
    return snap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
  }
  
  // Profile Image Methods
  async uploadProfileImage(file: File, userId: string): Promise<string> {
    try {
      // Create a unique filename with timestamp
      const timestamp = Date.now();
      const fileExtension = file.name.split('.').pop();
      const fileName = `profile_${userId}_${timestamp}.${fileExtension}`;

      // Create storage reference
      const storageRef = ref(this.storage, `profile-images/${fileName}`);

      // Upload file
      const snapshot = await uploadBytes(storageRef, file);

      // Get download URL
      const downloadURL = await getDownloadURL(snapshot.ref);

      // Update user profile with image URL
      await this.updateUserProfileImage(userId, downloadURL);

      return downloadURL;
    } catch (error) {
      console.error('Error uploading profile image:', error);
      throw error;
    }
  }

  async uploadProfileImageWithProgress(
    file: File,
    userId: string,
    progressCallback?: (progress: number) => void
  ): Promise<string> {
    return new Promise((resolve, reject) => {
      try {
        // Create a unique filename with timestamp
        const timestamp = Date.now();
        const fileExtension = file.name.split('.').pop();
        const fileName = `profile_${userId}_${timestamp}.${fileExtension}`;

        // Create storage reference
        const storageRef = ref(this.storage, `profile-images/${fileName}`);

        // Create upload task with progress tracking
        const uploadTask = uploadBytesResumable(storageRef, file);

        // Set timeout for upload (60 seconds for better reliability)
        const timeout = setTimeout(() => {
          uploadTask.cancel();
          reject(new Error('Upload timeout - please try again with a smaller image'));
        }, 60000);

        uploadTask.on('state_changed',
          (snapshot) => {
            // Progress tracking
            const progress = (snapshot.bytesTransferred / snapshot.totalBytes) * 100;
            if (progressCallback) {
              progressCallback(progress);
            }
            console.log(`Upload is ${progress.toFixed(1)}% done`);
          },
          (error) => {
            // Error handling
            clearTimeout(timeout);
            console.error('Upload error:', error);
            reject(error);
          },
          async () => {
            // Upload completed successfully
            clearTimeout(timeout);
            try {
              const downloadURL = await getDownloadURL(uploadTask.snapshot.ref);
              await this.updateUserProfileImage(userId, downloadURL);
              resolve(downloadURL);
            } catch (error) {
              reject(error);
            }
          }
        );
      } catch (error) {
        reject(error);
      }
    });
  }

  async updateUserProfileImage(userId: string, imageUrl: string): Promise<void> {
    try {
      // Update lawyer profile
      const lawyerRef = doc(this.firestore, 'lawyers', userId);
      const lawyerDoc = await getDoc(lawyerRef);

      if (lawyerDoc.exists()) {
        await updateDoc(lawyerRef, {
          profileImageUrl: imageUrl,
          updatedAt: new Date()
        });
        return;
      }

      // Update secretary profile
      const secretaryRef = doc(this.firestore, 'secretaries', userId);
      const secretaryDoc = await getDoc(secretaryRef);

      if (secretaryDoc.exists()) {
        await updateDoc(secretaryRef, {
          profileImageUrl: imageUrl,
          updatedAt: new Date()
        });
        return;
      }

      // Update client profile
      const clientRef = doc(this.firestore, 'clients', userId);
      const clientDoc = await getDoc(clientRef);

      if (clientDoc.exists()) {
        await updateDoc(clientRef, {
          profileImageUrl: imageUrl,
          updatedAt: new Date()
        });
        return;
      }

      throw new Error('User profile not found');
    } catch (error) {
      console.error('Error updating user profile image:', error);
      throw error;
    }
  }

  async getUserProfileImage(userId: string): Promise<string | null> {
    try {
      // Check lawyer profile
      const lawyerRef = doc(this.firestore, 'lawyers', userId);
      const lawyerDoc = await getDoc(lawyerRef);

      if (lawyerDoc.exists()) {
        const data = lawyerDoc.data();
        return data['profileImageUrl'] || null;
      }

      // Check secretary profile
      const secretaryRef = doc(this.firestore, 'secretaries', userId);
      const secretaryDoc = await getDoc(secretaryRef);

      if (secretaryDoc.exists()) {
        const data = secretaryDoc.data();
        return data['profileImageUrl'] || null;
      }

      // Check client profile
      const clientRef = doc(this.firestore, 'clients', userId);
      const clientDoc = await getDoc(clientRef);

      if (clientDoc.exists()) {
        const data = clientDoc.data();
        return data['profileImageUrl'] || null;
      }

      return null;
    } catch (error) {
      console.error('Error getting user profile image:', error);
      return null;
    }
  }

  async deleteProfileImage(userId: string, imageUrl: string): Promise<void> {
    try {
      // Delete from storage
      const imageRef = ref(this.storage, imageUrl);
      await deleteObject(imageRef);

      // Update user profile to remove image URL
      await this.updateUserProfileImage(userId, '');
    } catch (error) {
      console.error('Error deleting profile image:', error);
      throw error;
    }
  }

  // Case File Management Methods
  async uploadCaseFile(
    file: File,
    caseId: string,
    userId: string,
    progressCallback?: (progress: number) => void
  ): Promise<any> {
    return new Promise((resolve, reject) => {
      try {
        // Create unique filename
        const timestamp = Date.now();
        const fileExtension = file.name.split('.').pop();
        const fileName = `case_${caseId}_${timestamp}.${fileExtension}`;
        const storagePath = `case-files/${caseId}/${fileName}`;

        // Create storage reference
        const storageRef = ref(this.storage, storagePath);

        // Create upload task with progress tracking
        const uploadTask = uploadBytesResumable(storageRef, file);

        // Set timeout for upload (2 minutes for larger files)
        const timeout = setTimeout(() => {
          uploadTask.cancel();
          reject(new Error('Upload timeout - please try again'));
        }, 120000);

        uploadTask.on('state_changed',
          (snapshot) => {
            // Progress callback
            const progress = (snapshot.bytesTransferred / snapshot.totalBytes) * 100;
            if (progressCallback) {
              progressCallback(progress);
            }
          },
          (error) => {
            clearTimeout(timeout);
            console.error('Upload error:', error);
            reject(error);
          },
          async () => {
            clearTimeout(timeout);
            try {
              // Get download URL
              const downloadURL = await getDownloadURL(uploadTask.snapshot.ref);

              // Create file document in Firestore
              const fileDoc = {
                name: fileName,
                originalName: file.name,
                type: file.type,
                size: file.size,
                caseId: caseId,
                uploadedBy: userId,
                uploadedAt: new Date(),
                downloadUrl: downloadURL,
                storagePath: storagePath
              };

              const docRef = await addDoc(collection(this.firestore, 'case_files'), fileDoc);

              // Update case file count
              await this.updateCaseFileCount(caseId);

              resolve({
                id: docRef.id,
                ...fileDoc
              });
            } catch (error) {
              console.error('Error saving file metadata:', error);
              reject(error);
            }
          }
        );
      } catch (error) {
        console.error('Error starting upload:', error);
        reject(error);
      }
    });
  }

  async getCaseFiles(caseId: string): Promise<any[]> {
    try {
      const q = query(
        collection(this.firestore, 'case_files'),
        where('caseId', '==', caseId),
        orderBy('uploadedAt', 'desc')
      );

      const querySnapshot = await getDocs(q);
      return querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));
    } catch (error) {
      console.error('Error getting case files:', error);
      throw error;
    }
  }

  async deleteCaseFile(fileId: string, storagePath: string): Promise<void> {
    try {
      const storageRef = ref(this.storage, storagePath);
      await deleteObject(storageRef);
  
      const docRef = doc(this.firestore, 'encrypted_files', fileId); // 🔁 match your collection name
      await deleteDoc(docRef);
    } catch (error) {
      console.error('Error deleting file:', error);
      throw error;
    }
  }  

  async updateCaseFileCount(caseId: string): Promise<void> {
  try {
    const q = query(
      collection(this.firestore, 'encrypted_files'),
      where('caseId', '==', caseId)
    );
    const snap = await getDocs(q);
    const fileCount = snap.size;

    console.log(`[updateCaseFileCount] Found ${fileCount} files`);

    const caseRef = doc(this.firestore, 'cases', caseId);
    await updateDoc(caseRef, { fileCount });

    console.log(`[updateCaseFileCount] Updated case ${caseId} with count ${fileCount}`);
  } catch (error) {
    console.error('Error updating case file count:', error);
  }
}
   
async incrementFolderFileCount(folderId: string): Promise<void> {
  const folderRef = doc(this.firestore, 'folders', folderId);
  await updateDoc(folderRef, { fileCount: increment(1) });
}
  
async decrementFolderFileCount(folderId: string): Promise<void> {
  try {
    const folderRef = doc(this.firestore, 'case_folders', folderId);
    await updateDoc(folderRef, {
      fileCount: increment(-1)
    });
  } catch (error) {
    console.error('Error decrementing folder file count:', error);
    throw error;
  }
}
  // Utility Methods
  private generateSecretaryCode(): string {
    return Math.random().toString(36).substring(2, 8).toUpperCase();
  }
}
