<ion-header>
  <ion-toolbar class="veritus-toolbar">
    <ion-title class="veritus-text-white veritus-font-semibold">File Management</ion-title>
  </ion-toolbar>
</ion-header>

<ion-content class="files-content veritus-gradient-bg">
  <div class="files-container veritus-safe-area-top">
    
    <!-- Filters -->
    <div class="filters-section">
      <div class="filter-row">
        <div class="filter-item">
          <ion-label class="filter-label veritus-text-sm veritus-text-white">Lawyer</ion-label>
          <ion-select
            [(ngModel)]="selectedLawyer"
            (ionChange)="onLawyerChange()"
            interface="popover"
            class="lawyer-select">
            <ion-select-option value="all">All Lawyers</ion-select-option>
            <ion-select-option *ngFor="let lawyer of linkedLawyers" [value]="lawyer.uid">
              {{ lawyer.name }}
            </ion-select-option>
          </ion-select>
        </div>
        
        <div class="filter-item">
          <ion-label class="filter-label veritus-text-sm veritus-text-white">Type</ion-label>
          <ion-select
            [(ngModel)]="selectedType"
            (ionChange)="onTypeChange()"
            interface="popover"
            class="type-select">
            <ion-select-option value="all">All Types</ion-select-option>
            <ion-select-option value="pdf">PDF</ion-select-option>
            <ion-select-option value="docx">Word</ion-select-option>
            <ion-select-option value="xlsx">Excel</ion-select-option>
            <ion-select-option value="zip">Archive</ion-select-option>
          </ion-select>
        </div>
      </div>
      
      <div class="search-section">
        <ion-searchbar
          [(ngModel)]="searchTerm"
          (ionInput)="onSearchChange()"
          placeholder="Search files..."
          class="custom-searchbar">
        </ion-searchbar>
      </div>
    </div>

    <!-- Upload Button -->
    <div class="action-section">
      <button class="veritus-btn-primary upload-btn" (click)="onUploadFile()">
        <ion-icon name="cloud-upload" class="btn-icon"></ion-icon>
        Upload File
      </button>
    </div>

    <!-- Files List -->
    <div class="files-section">
      <h2 class="section-title veritus-text-lg veritus-font-semibold veritus-text-white">
        Files ({{ filteredFiles.length }})
      </h2>
      
      <div class="files-list" *ngIf="filteredFiles.length > 0">
        <div class="file-card" *ngFor="let file of filteredFiles">
          <div class="file-icon">
            <ion-icon [name]="getFileIcon(file.type)" [color]="getFileTypeColor(file.type)"></ion-icon>
          </div>
          
          <div class="file-details">
            <h3 class="file-name veritus-text-base veritus-font-semibold veritus-text-white">
              {{ file.name }}
            </h3>
            <p class="file-info veritus-text-sm veritus-text-gray">
              {{ formatFileSize(file.size) }} • {{ file.lawyerName }}
            </p>
            <p class="file-case veritus-text-sm veritus-text-gold" *ngIf="file.caseName">
              {{ file.caseName }}
            </p>
            <p class="file-date veritus-text-xs veritus-text-gray">
              Uploaded {{ file.uploadedAt | date:'short' }}
            </p>
          </div>
          
          <div class="file-actions">
            <button class="action-btn download-btn" (click)="onDownloadFile(file)">
              <ion-icon name="download-outline"></ion-icon>
            </button>
            <button class="action-btn delete-btn" (click)="onDeleteFile(file)">
              <ion-icon name="trash-outline"></ion-icon>
            </button>
          </div>
        </div>
      </div>
      
      <!-- Empty State -->
      <div class="empty-state" *ngIf="filteredFiles.length === 0">
        <ion-icon name="folder-outline" class="empty-icon"></ion-icon>
        <h3 class="empty-title veritus-text-lg veritus-font-semibold veritus-text-white">No Files Found</h3>
        <p class="empty-description veritus-text-sm veritus-text-gray">
          No files match your current filters.
        </p>
        <button class="veritus-btn-secondary" (click)="onUploadFile()">
          Upload First File
        </button>
      </div>
    </div>

    <!-- Quick Stats -->
    <div class="stats-section">
      <div class="stat-card">
        <div class="stat-number veritus-text-xl veritus-font-bold veritus-text-white">
          {{ getTotalFiles() }}
        </div>
        <div class="stat-label veritus-text-sm veritus-text-gray">Total Files</div>
      </div>
      
      <div class="stat-card">
        <div class="stat-number veritus-text-xl veritus-font-bold veritus-text-white">
          {{ linkedLawyers.length }}
        </div>
        <div class="stat-label veritus-text-sm veritus-text-gray">Linked Lawyers</div>
      </div>
      
      <div class="stat-card">
        <div class="stat-number veritus-text-xl veritus-font-bold veritus-text-white">
          {{ getTotalSize() }}
        </div>
        <div class="stat-label veritus-text-sm veritus-text-gray">Total Size</div>
      </div>
    </div>

  </div>
</ion-content>
