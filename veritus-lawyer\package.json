{"name": "veritus-lawyer", "version": "0.0.1", "author": "Veritus Team", "homepage": "https://veritus.com/", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test", "lint": "ng lint", "e2e": "ng e2e", "ionic:build": "ng build", "ionic:serve": "ng serve"}, "private": true, "dependencies": {"@angular/animations": "^17.0.0", "@angular/common": "^17.0.0", "@angular/compiler": "^17.0.0", "@angular/core": "^17.0.0", "@angular/fire": "^17.1.0", "@angular/forms": "^17.0.0", "@angular/platform-browser": "^17.0.0", "@angular/platform-browser-dynamic": "^17.0.0", "@angular/router": "^17.0.0", "@capacitor/app": "5.0.6", "@capacitor/core": "5.5.1", "@capacitor/haptics": "5.0.6", "@capacitor/keyboard": "5.0.6", "@capacitor/status-bar": "5.0.6", "@ionic/angular": "^7.5.0", "firebase": "^11.10.0", "ionicons": "^7.0.0", "rxjs": "~7.8.0", "tslib": "^2.3.0", "zone.js": "~0.14.0"}, "devDependencies": {"@angular-devkit/build-angular": "^17.0.0", "@angular-eslint/builder": "^17.0.0", "@angular-eslint/eslint-plugin": "^17.0.0", "@angular-eslint/eslint-plugin-template": "^17.0.0", "@angular-eslint/schematics": "^17.0.0", "@angular-eslint/template-parser": "^17.0.0", "@angular/cli": "^17.0.0", "@angular/compiler-cli": "^17.0.0", "@angular/language-service": "^17.0.0", "@capacitor/cli": "5.5.1", "@ionic/angular-toolkit": "^9.0.0", "@types/jasmine": "~5.1.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.57.0", "eslint-plugin-import": "2.29.1", "eslint-plugin-jsdoc": "46.9.0", "eslint-plugin-prefer-arrow": "1.2.3", "jasmine-core": "~5.1.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "typescript": "~5.2.0"}, "description": "Veritus Lawyer Mobile App"}