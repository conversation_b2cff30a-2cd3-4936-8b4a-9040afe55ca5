import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { FirebaseService } from '../../services/firebase.service';
import { LoadingController, AlertController } from '@ionic/angular';

@Component({
  selector: 'app-forgot-password',
  templateUrl: './forgot-password.page.html',
  styleUrls: ['./forgot-password.page.scss'],
  standalone: false,
})
export class ForgotPasswordPage implements OnInit {
  forgotPasswordForm: FormGroup;
  isSubmitted = false;

  constructor(
    private formBuilder: FormBuilder,
    private router: Router,
    private firebaseService: FirebaseService,
    private loadingController: LoadingController,
    private alertController: AlertController
  ) {
    this.forgotPasswordForm = this.formBuilder.group({
      email: ['', [Validators.required, Validators.email]]
    });
  }

  ngOnInit() {}

  async onSubmit() {
    if (this.forgotPasswordForm.valid) {
      const loading = await this.loadingController.create({
        message: 'Sending reset email...',
        spinner: 'crescent'
      });
      await loading.present();

      try {
        const email = this.forgotPasswordForm.get('email')?.value;
        await this.firebaseService.resetPassword(email);
        await loading.dismiss();
        this.isSubmitted = true;
      } catch (error: any) {
        await loading.dismiss();
        await this.showErrorAlert('Reset Failed', error.message || 'Please check your email and try again.');
      }
    }
  }

  goToSignIn() {
    this.router.navigate(['/auth/signin']);
  }

  private async showErrorAlert(header: string, message: string) {
    const alert = await this.alertController.create({
      header,
      message,
      buttons: ['OK']
    });
    await alert.present();
  }
}
