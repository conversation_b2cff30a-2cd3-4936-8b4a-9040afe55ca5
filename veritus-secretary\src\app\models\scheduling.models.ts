// Scheduling Management Models and Interfaces for Secretary Dashboard

export interface LawyerAvailability {
  id?: string;
  lawyerId: string;
  date: string; // Format: "YYYY-MM-DD"
  timeSlots: string[]; // Array of time slots like ["09:00", "14:00", "16:00"]
  isRecurring?: boolean;
  recurringPattern?: 'weekly' | 'monthly';
  createdAt: Date;
  updatedAt: Date;
  createdBy: string; // Secretary UID who created/modified
}

export interface TimeSlot {
  time: string; // Format: "HH:MM"
  isAvailable: boolean;
  isBooked: boolean;
  appointmentId?: string;
}

export interface EnhancedAppointment {
  id?: string;
  lawyerId: string;
  lawyerName: string;
  clientId?: string;
  clientName: string;
  date: string; // Format: "YYYY-MM-DD"
  time: string; // Format: "HH:MM"
  status: 'pending' | 'confirmed' | 'cancelled' | 'completed' | 'rescheduled';
  type: string; // e.g., "Consultation", "Case Review", "Court Hearing"
  createdBy: 'client' | 'secretary' | 'lawyer';
  remarks?: string;
  duration?: number; // Duration in minutes
  location?: string;
  isUrgent?: boolean;
  
  // Secretary-specific fields
  managedBy?: string; // Secretary U<PERSON> who manages this appointment
  lastModifiedBy?: string; // UID of last person who modified
  lastModifiedByRole?: 'secretary' | 'lawyer' | 'client';
  
  // Reschedule tracking
  originalDate?: string;
  originalTime?: string;
  rescheduleReason?: string;
  rescheduleRequestedBy?: string;
  rescheduleRequestedAt?: Date;
  
  // Timestamps
  createdAt: Date;
  updatedAt: Date;
}

export interface RescheduleRequest {
  id?: string;
  appointmentId: string;
  originalDate: string;
  originalTime: string;
  proposedDate: string;
  proposedTime: string;
  reason: string;
  requestedBy: string; // Secretary UID
  requestedByName: string;
  status: 'pending' | 'approved' | 'rejected';
  
  // Approval tracking
  clientApproval?: 'pending' | 'approved' | 'rejected';
  lawyerApproval?: 'pending' | 'approved' | 'rejected';
  clientResponse?: string;
  lawyerResponse?: string;
  
  // Timestamps
  createdAt: Date;
  respondedAt?: Date;
  expiresAt: Date; // Auto-expire after certain time
}

export interface AppointmentFilter {
  lawyerId?: string;
  status?: string[];
  dateFrom?: string;
  dateTo?: string;
  type?: string[];
  isUrgent?: boolean;
  createdBy?: string[];
}

export interface CalendarView {
  type: 'day' | 'week' | 'month';
  currentDate: Date;
  selectedDate?: Date;
  selectedLawyer?: string;
}

export interface AppointmentReminder {
  id?: string;
  appointmentId: string;
  type: '24h' | '1h' | 'custom';
  scheduledFor: Date;
  status: 'pending' | 'sent' | 'failed';
  recipients: {
    client: boolean;
    lawyer: boolean;
    secretary: boolean;
  };
  message?: string;
  createdAt: Date;
  sentAt?: Date;
}

export interface SecretaryCalendarSettings {
  defaultView: 'day' | 'week' | 'month';
  workingHours: {
    start: string; // "09:00"
    end: string;   // "17:00"
  };
  timeSlotDuration: number; // in minutes
  autoReminders: {
    enabled: boolean;
    times: string[]; // ["24h", "1h"]
  };
  notifications: {
    newAppointments: boolean;
    rescheduleRequests: boolean;
    cancellations: boolean;
  };
}

export interface LawyerCalendarSummary {
  lawyerId: string;
  lawyerName: string;
  totalAppointments: number;
  confirmedAppointments: number;
  pendingAppointments: number;
  availableSlots: number;
  nextAppointment?: {
    date: string;
    time: string;
    clientName: string;
  };
}

// Utility types for UI components
export interface CalendarDay {
  date: number;
  fullDate: Date;
  isCurrentMonth: boolean;
  isToday: boolean;
  isSelected: boolean;
  hasAppointments: boolean;
  appointmentCount: number;
  hasAvailability: boolean;
}

export interface WeekView {
  weekStart: Date;
  weekEnd: Date;
  days: CalendarDay[];
}

export interface MonthView {
  month: number;
  year: number;
  weeks: WeekView[];
}

// Status and priority enums
export enum AppointmentStatus {
  PENDING = 'pending',
  CONFIRMED = 'confirmed',
  CANCELLED = 'cancelled',
  COMPLETED = 'completed',
  RESCHEDULED = 'rescheduled'
}

export enum AppointmentType {
  CONSULTATION = 'Consultation',
  CASE_REVIEW = 'Case Review',
  COURT_HEARING = 'Court Hearing',
  DOCUMENT_SIGNING = 'Document Signing',
  FOLLOW_UP = 'Follow-up',
  EMERGENCY = 'Emergency'
}

export enum RescheduleStatus {
  PENDING = 'pending',
  APPROVED = 'approved',
  REJECTED = 'rejected'
}

// Event interfaces for real-time updates
export interface CalendarEvent {
  type: 'appointment_created' | 'appointment_updated' | 'appointment_cancelled' | 'availability_updated' | 'reschedule_requested';
  data: any;
  timestamp: Date;
  triggeredBy: string;
}

export interface NotificationEvent {
  id: string;
  type: 'reminder' | 'reschedule_request' | 'appointment_update';
  title: string;
  message: string;
  priority: 'low' | 'medium' | 'high';
  read: boolean;
  createdAt: Date;
  expiresAt?: Date;
}
