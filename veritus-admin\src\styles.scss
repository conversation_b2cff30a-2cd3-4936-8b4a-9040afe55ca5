/* Global Styles for Veritus Admin */

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  font-size: 14px;
  line-height: 1.6;
  color: var(--veritus-text);
  background: linear-gradient(135deg, #F8F9FA 0%, #ECF0F1 100%);
  overflow-x: hidden;
  min-height: 100vh;
}

// Veritus Theme Colors
:root {
  --veritus-primary: #C49A56;
  --veritus-primary-dark: #B8894A;
  --veritus-primary-light: #D4B373;
  --veritus-secondary: #2C3E50;
  --veritus-accent: #E8D5B7;
  --veritus-success: #27AE60;
  --veritus-danger: #E74C3C;
  --veritus-warning: #F39C12;
  --veritus-info: #3498DB;
  --veritus-light: #F8F9FA;
  --veritus-dark: #2C3E50;
  --veritus-white: #FFFFFF;
  --veritus-gray: #7F8C8D;
  --veritus-gray-light: #ECF0F1;
  --veritus-text: #2C3E50;
  --veritus-text-light: #7F8C8D;

  // Legacy admin variables for compatibility
  --admin-primary: var(--veritus-primary);
  --admin-primary-dark: var(--veritus-primary-dark);
  --admin-secondary: var(--veritus-light);
  --admin-success: var(--veritus-success);
  --admin-danger: var(--veritus-danger);
  --admin-warning: var(--veritus-warning);
  --admin-info: var(--veritus-info);
  --admin-light: var(--veritus-light);
  --admin-dark: var(--veritus-dark);
  --admin-white: var(--veritus-white);
  --admin-gray: var(--veritus-gray);
  --admin-gray-light: var(--veritus-gray-light);
}

// Typography
h1, h2, h3, h4, h5, h6 {
  font-weight: 600;
  line-height: 1.2;
  margin-bottom: 0.5rem;
}

p {
  margin-bottom: 1rem;
}

// Links
a {
  color: var(--admin-primary);
  text-decoration: none;
  transition: color 0.3s ease;
  
  &:hover {
    color: var(--admin-primary-dark);
  }
}

// Buttons
button {
  font-family: inherit;
  cursor: pointer;
  border: none;
  outline: none;
  transition: all 0.3s ease;
  
  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
}

// Form elements
input, textarea, select {
  font-family: inherit;
  font-size: inherit;
  border: 1px solid #e1e5e9;
  border-radius: 4px;
  padding: 8px 12px;
  transition: border-color 0.3s ease;
  
  &:focus {
    outline: none;
    border-color: var(--admin-primary);
    box-shadow: 0 0 0 2px rgba(196, 154, 86, 0.1);
  }
}

// Utility classes
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.d-flex { display: flex; }
.d-block { display: block; }
.d-inline-block { display: inline-block; }
.d-none { display: none; }

.justify-content-center { justify-content: center; }
.justify-content-between { justify-content: space-between; }
.justify-content-around { justify-content: space-around; }

.align-items-center { align-items: center; }
.align-items-start { align-items: flex-start; }
.align-items-end { align-items: flex-end; }

.flex-column { flex-direction: column; }
.flex-row { flex-direction: row; }

.w-100 { width: 100%; }
.h-100 { height: 100%; }

.m-0 { margin: 0; }
.m-1 { margin: 0.25rem; }
.m-2 { margin: 0.5rem; }
.m-3 { margin: 1rem; }
.m-4 { margin: 1.5rem; }
.m-5 { margin: 3rem; }

.p-0 { padding: 0; }
.p-1 { padding: 0.25rem; }
.p-2 { padding: 0.5rem; }
.p-3 { padding: 1rem; }
.p-4 { padding: 1.5rem; }
.p-5 { padding: 3rem; }

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: 0.25rem; }
.mb-2 { margin-bottom: 0.5rem; }
.mb-3 { margin-bottom: 1rem; }
.mb-4 { margin-bottom: 1.5rem; }
.mb-5 { margin-bottom: 3rem; }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: 0.25rem; }
.mt-2 { margin-top: 0.5rem; }
.mt-3 { margin-top: 1rem; }
.mt-4 { margin-top: 1.5rem; }
.mt-5 { margin-top: 3rem; }

// Color utilities
.text-primary { color: var(--admin-primary); }
.text-success { color: var(--admin-success); }
.text-danger { color: var(--admin-danger); }
.text-warning { color: var(--admin-warning); }
.text-info { color: var(--admin-info); }
.text-muted { color: var(--admin-gray); }

.bg-primary { background-color: var(--admin-primary); }
.bg-success { background-color: var(--admin-success); }
.bg-danger { background-color: var(--admin-danger); }
.bg-warning { background-color: var(--admin-warning); }
.bg-info { background-color: var(--admin-info); }
.bg-light { background-color: var(--admin-light); }
.bg-white { background-color: var(--admin-white); }

// Border utilities
.border { border: 1px solid #e1e5e9; }
.border-top { border-top: 1px solid #e1e5e9; }
.border-bottom { border-bottom: 1px solid #e1e5e9; }
.border-left { border-left: 1px solid #e1e5e9; }
.border-right { border-right: 1px solid #e1e5e9; }

.rounded { border-radius: 0.25rem; }
.rounded-lg { border-radius: 0.5rem; }
.rounded-xl { border-radius: 0.75rem; }
.rounded-circle { border-radius: 50%; }

// Shadow utilities
.shadow-sm { box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1); }
.shadow { box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1); }
.shadow-lg { box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15); }

// Animation utilities
.fade-in {
  animation: fadeIn 0.3s ease-in;
}

.slide-in {
  animation: slideIn 0.3s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideIn {
  from { transform: translateY(-10px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

// Scrollbar styling
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
  
  &:hover {
    background: #a8a8a8;
  }
}

// Responsive breakpoints
@media (max-width: 1200px) {
  .container-xl { max-width: 100%; }
}

@media (max-width: 992px) {
  .container-lg { max-width: 100%; }
}

@media (max-width: 768px) {
  .container-md { max-width: 100%; }
  
  // Hide sidebar margin on mobile
  .dashboard-container,
  .verification-container,
  .platform-control-container,
  .lawyer-list-container,
  .templates-container,
  .users-container,
  .settings-container {
    margin-left: 0 !important;
    padding: 15px !important;
  }
}

@media (max-width: 576px) {
  .container-sm { max-width: 100%; }
  
  body {
    font-size: 13px;
  }
}
