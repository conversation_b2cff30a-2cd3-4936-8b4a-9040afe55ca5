import { Injectable } from '@angular/core';
import { EncryptedStorageService, EncryptedFile } from './encrypted-storage.service';
import { Firestore, doc, setDoc, getDoc, collection, query, where, getDocs } from '@angular/fire/firestore';
import { Storage, ref, uploadBytes, getDownloadURL } from '@angular/fire/storage';

export interface CaseArchive {
  id: string;
  caseId: string;
  caseName: string;
  clientId: string;
  lawyerId: string;
  archivedBy: string;
  archivedAt: Date;
  fileCount: number;
  totalSize: number;
  archiveUrl: string;
  encryptionKey: string;
  status: 'creating' | 'completed' | 'failed';
  metadata: {
    caseStartDate?: Date;
    caseEndDate?: Date;
    caseType?: string;
    outcome?: string;
    notes?: string;
  };
}

@Injectable({
  providedIn: 'root'
})
export class ArchiveService {

  constructor(
    private encryptedStorage: EncryptedStorageService,
    private firestore: Firestore,
    private storage: Storage
  ) { }

  // Archive entire case with all files
  async archiveCase(
    caseId: string,
    caseName: string,
    clientId: string,
    lawyerId: string,
    archivedBy: string,
    metadata?: {
      caseStartDate?: Date;
      caseEndDate?: Date;
      caseType?: string;
      outcome?: string;
      notes?: string;
    }
  ): Promise<CaseArchive> {
    
    // Create archive record
    const archiveId = `archive_${caseId}_${Date.now()}`;
    const archive: CaseArchive = {
      id: archiveId,
      caseId,
      caseName,
      clientId,
      lawyerId,
      archivedBy,
      archivedAt: new Date(),
      fileCount: 0,
      totalSize: 0,
      archiveUrl: '',
      encryptionKey: this.encryptedStorage.generateEncryptionKey(),
      status: 'creating',
      metadata: metadata || {}
    };

    // Save initial archive record
    const docRef = doc(this.firestore, 'case-archives', archiveId);
    await setDoc(docRef, archive);

    try {
      // Get all files for the case
      const caseFiles = await this.encryptedStorage.getFilesByCase(caseId);
      
      // Create ZIP archive (placeholder implementation)
      const zipBlob = await this.createZipArchive(caseFiles, archive.encryptionKey);
      
      // Upload archive to long-term storage
      const archiveRef = ref(this.storage, `case-archives/${archiveId}.zip`);
      await uploadBytes(archiveRef, zipBlob);
      const archiveUrl = await getDownloadURL(archiveRef);

      // Update archive record
      const updatedArchive: Partial<CaseArchive> = {
        fileCount: caseFiles.length,
        totalSize: caseFiles.reduce((total, file) => total + file.size, 0),
        archiveUrl,
        status: 'completed'
      };

      await this.updateArchive(archiveId, updatedArchive);

      // Mark individual files as archived
      for (const file of caseFiles) {
        await this.encryptedStorage.archiveFile(file.id);
      }

      return { ...archive, ...updatedArchive } as CaseArchive;

    } catch (error) {
      // Mark archive as failed
      await this.updateArchive(archiveId, { status: 'failed' });
      throw error;
    }
  }

  // Create ZIP archive of files (placeholder implementation)
  private async createZipArchive(files: EncryptedFile[], encryptionKey: string): Promise<Blob> {
    // TODO: Implement actual ZIP creation with JSZip or similar
    // For now, create a simple blob with file metadata
    
    const archiveData = {
      files: files.map(file => ({
        id: file.id,
        originalName: file.originalName,
        size: file.size,
        uploadedAt: file.uploadedAt,
        tags: file.tags
      })),
      createdAt: new Date(),
      encryptionKey: encryptionKey
    };

    const jsonString = JSON.stringify(archiveData, null, 2);
    return new Blob([jsonString], { type: 'application/json' });
  }

  // Get archive by ID
  async getArchive(archiveId: string): Promise<CaseArchive | null> {
    const docRef = doc(this.firestore, 'case-archives', archiveId);
    const docSnap = await getDoc(docRef);
    
    return docSnap.exists() ? docSnap.data() as CaseArchive : null;
  }

  // Get archives by user
  async getArchivesByUser(userId: string): Promise<CaseArchive[]> {
    const q = query(
      collection(this.firestore, 'case-archives'),
      where('archivedBy', '==', userId)
    );
    
    const querySnapshot = await getDocs(q);
    return querySnapshot.docs.map(doc => doc.data() as CaseArchive);
  }

  // Get archives by lawyer
  async getArchivesByLawyer(lawyerId: string): Promise<CaseArchive[]> {
    const q = query(
      collection(this.firestore, 'case-archives'),
      where('lawyerId', '==', lawyerId)
    );
    
    const querySnapshot = await getDocs(q);
    return querySnapshot.docs.map(doc => doc.data() as CaseArchive);
  }

  // Get archives by client
  async getArchivesByClient(clientId: string): Promise<CaseArchive[]> {
    const q = query(
      collection(this.firestore, 'case-archives'),
      where('clientId', '==', clientId)
    );
    
    const querySnapshot = await getDocs(q);
    return querySnapshot.docs.map(doc => doc.data() as CaseArchive);
  }

  // Update archive
  async updateArchive(archiveId: string, updates: Partial<CaseArchive>): Promise<void> {
    const docRef = doc(this.firestore, 'case-archives', archiveId);
    await setDoc(docRef, updates, { merge: true });
  }

  // Download archive
  async downloadArchive(archiveId: string, userKey: string): Promise<Blob> {
    const archive = await this.getArchive(archiveId);
    if (!archive) {
      throw new Error('Archive not found');
    }

    // Download archive file
    const response = await fetch(archive.archiveUrl);
    const encryptedData = await response.arrayBuffer();
    
    // Decrypt archive
    const decryptedData = await this.encryptedStorage.decryptFile(encryptedData, archive.encryptionKey);
    
    return new Blob([decryptedData], { type: 'application/zip' });
  }

  // Restore files from archive
  async restoreFromArchive(archiveId: string): Promise<void> {
    // TODO: Implement archive restoration
    // This would involve:
    // 1. Download and decrypt the archive
    // 2. Extract individual files
    // 3. Re-upload files to active storage
    // 4. Update file metadata to mark as active
    
    throw new Error('Archive restoration not yet implemented');
  }

  // Delete archive (permanent)
  async deleteArchive(archiveId: string): Promise<void> {
    const archive = await this.getArchive(archiveId);
    if (!archive) {
      throw new Error('Archive not found');
    }

    // Delete archive file from storage
    const archiveRef = ref(this.storage, `case-archives/${archiveId}.zip`);
    // await deleteObject(archiveRef); // Commented out for safety

    // Delete archive metadata
    // const docRef = doc(this.firestore, 'case-archives', archiveId);
    // await deleteDoc(docRef); // Commented out for safety
    
    // TODO: Implement proper archive deletion with confirmation
    throw new Error('Archive deletion requires additional confirmation');
  }

  // Get archive statistics
  async getArchiveStats(userId: string): Promise<{
    totalArchives: number;
    totalSize: number;
    oldestArchive?: Date;
    newestArchive?: Date;
  }> {
    const archives = await this.getArchivesByUser(userId);
    
    const totalSize = archives.reduce((total, archive) => total + archive.totalSize, 0);
    const dates = archives.map(archive => archive.archivedAt).sort();
    
    return {
      totalArchives: archives.length,
      totalSize,
      oldestArchive: dates[0],
      newestArchive: dates[dates.length - 1]
    };
  }
}
