import { Injectable } from '@angular/core';
import { EncryptedStorageService, EncryptedFile } from './encrypted-storage.service';
import { AccessControlService } from './access-control.service';

export interface FileCategory {
  id: string;
  name: string;
  description: string;
  icon: string;
  color: string;
}

export interface FileOperation {
  id: string;
  type: 'upload' | 'download' | 'delete' | 'move' | 'copy';
  fileId: string;
  userId: string;
  timestamp: Date;
  status: 'pending' | 'completed' | 'failed';
  progress?: number;
  error?: string;
}

@Injectable({
  providedIn: 'root'
})
export class FileManagerService {

  private fileCategories: FileCategory[] = [
    {
      id: 'contracts',
      name: 'Contracts',
      description: 'Legal contracts and agreements',
      icon: 'document-text',
      color: '#0A49FF'
    },
    {
      id: 'evidence',
      name: 'Evidence',
      description: 'Case evidence and supporting documents',
      icon: 'camera',
      color: '#28a745'
    },
    {
      id: 'correspondence',
      name: 'Correspondence',
      description: 'Letters, emails, and communications',
      icon: 'mail',
      color: '#ffc107'
    },
    {
      id: 'court_documents',
      name: 'Court Documents',
      description: 'Filings, motions, and court papers',
      icon: 'library',
      color: '#dc3545'
    },
    {
      id: 'client_documents',
      name: 'Client Documents',
      description: 'Client-provided documents',
      icon: 'person',
      color: '#6f42c1'
    },
    {
      id: 'research',
      name: 'Research',
      description: 'Legal research and references',
      icon: 'search',
      color: '#20c997'
    }
  ];

  constructor(
    private encryptedStorage: EncryptedStorageService,
    private accessControl: AccessControlService
  ) { }

  // Get file categories
  getFileCategories(): FileCategory[] {
    return this.fileCategories;
  }

  // Upload file with category and metadata
  async uploadFile(
    file: File,
    category: string,
    metadata: {
      uploadedBy: string;
      caseId?: string;
      clientId?: string;
      lawyerId?: string;
      description?: string;
      tags?: string[];
      accessLevel?: 'public' | 'restricted' | 'confidential';
    }
  ): Promise<EncryptedFile> {
    
    // Check upload permissions
    const canUpload = await this.accessControl.canUploadFile(
      metadata.uploadedBy,
      metadata.caseId,
      metadata.clientId,
      metadata.lawyerId
    );

    if (!canUpload) {
      throw new Error('Insufficient permissions to upload file');
    }

    // Add category to tags
    const tags = [...(metadata.tags || []), category];

    // Upload encrypted file
    return this.encryptedStorage.uploadEncryptedFile(file, {
      ...metadata,
      tags
    });
  }

  // Get files by category
  async getFilesByCategory(
    category: string,
    userId: string,
    filters?: {
      caseId?: string;
      clientId?: string;
      lawyerId?: string;
    }
  ): Promise<EncryptedFile[]> {
    
    // Get user's accessible files
    const files = await this.getUserAccessibleFiles(userId, filters);
    
    // Filter by category
    return files.filter(file => file.tags.includes(category));
  }

  // Get user's accessible files based on role and permissions
  async getUserAccessibleFiles(
    userId: string,
    filters?: {
      caseId?: string;
      clientId?: string;
      lawyerId?: string;
    }
  ): Promise<EncryptedFile[]> {
    
    const userRole = await this.accessControl.getUserRole(userId);
    let files: EncryptedFile[] = [];

    switch (userRole) {
      case 'client':
        // Clients can only see their own files
        files = await this.encryptedStorage.getFilesByClient(userId);
        break;
        
      case 'lawyer':
        // Lawyers can see files for their cases
        if (filters?.caseId) {
          files = await this.encryptedStorage.getFilesByCase(filters.caseId);
        } else {
          files = await this.encryptedStorage.getFilesByLawyer(userId);
        }
        break;
        
      case 'secretary':
        // Secretaries can see files for linked lawyers' cases
        const linkedLawyers = await this.accessControl.getLinkedLawyers(userId);
        files = [];
        for (const lawyerId of linkedLawyers) {
          const lawyerFiles = await this.encryptedStorage.getFilesByLawyer(lawyerId);
          files.push(...lawyerFiles);
        }
        break;
        
      default:
        files = [];
    }

    // Apply additional filters
    if (filters?.caseId) {
      files = files.filter(file => file.caseId === filters.caseId);
    }
    
    if (filters?.clientId) {
      files = files.filter(file => file.clientId === filters.clientId);
    }
    
    if (filters?.lawyerId) {
      files = files.filter(file => file.lawyerId === filters.lawyerId);
    }

    return files;
  }

  // Download file with access control
  async downloadFile(fileId: string, userId: string): Promise<Blob> {
    // Check download permissions
    const canDownload = await this.accessControl.canDownloadFile(userId, fileId);
    
    if (!canDownload) {
      throw new Error('Insufficient permissions to download file');
    }

    // TODO: Get user's decryption key
    const userKey = 'placeholder-user-key';
    
    return this.encryptedStorage.downloadDecryptedFile(fileId, userKey);
  }

  // Delete file with access control
  async deleteFile(fileId: string, userId: string): Promise<void> {
    // Check delete permissions
    const canDelete = await this.accessControl.canDeleteFile(userId, fileId);
    
    if (!canDelete) {
      throw new Error('Insufficient permissions to delete file');
    }

    await this.encryptedStorage.deleteFile(fileId);
  }

  // Move file to different case/client
  async moveFile(
    fileId: string,
    userId: string,
    newLocation: {
      caseId?: string;
      clientId?: string;
      lawyerId?: string;
    }
  ): Promise<void> {
    
    // Check move permissions
    const canMove = await this.accessControl.canMoveFile(userId, fileId, newLocation);
    
    if (!canMove) {
      throw new Error('Insufficient permissions to move file');
    }

    await this.encryptedStorage.updateFileMetadata(fileId, newLocation);
  }

  // Search files with access control
  async searchFiles(
    searchTerm: string,
    userId: string,
    filters?: {
      category?: string;
      caseId?: string;
      clientId?: string;
      lawyerId?: string;
      tags?: string[];
      accessLevel?: string;
    }
  ): Promise<EncryptedFile[]> {
    
    // Get user's accessible files first
    const accessibleFiles = await this.getUserAccessibleFiles(userId, {
      caseId: filters?.caseId,
      clientId: filters?.clientId,
      lawyerId: filters?.lawyerId
    });

    // Apply search and filters
    let results = accessibleFiles;

    // Search by term
    if (searchTerm) {
      results = results.filter(file =>
        file.originalName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        file.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()))
      );
    }

    // Filter by category
    if (filters?.category) {
      results = results.filter(file => file.tags.includes(filters.category!));
    }

    // Filter by tags
    if (filters?.tags && filters.tags.length > 0) {
      results = results.filter(file =>
        filters.tags!.some(tag => file.tags.includes(tag))
      );
    }

    // Filter by access level
    if (filters?.accessLevel) {
      results = results.filter(file => file.accessLevel === filters.accessLevel);
    }

    return results;
  }

  // Get file statistics
  async getFileStatistics(userId: string): Promise<{
    totalFiles: number;
    totalSize: number;
    filesByCategory: { [category: string]: number };
    recentUploads: EncryptedFile[];
  }> {
    
    const files = await this.getUserAccessibleFiles(userId);
    
    const totalSize = files.reduce((total, file) => total + file.size, 0);
    
    const filesByCategory: { [category: string]: number } = {};
    this.fileCategories.forEach(category => {
      filesByCategory[category.id] = files.filter(file => 
        file.tags.includes(category.id)
      ).length;
    });

    const recentUploads = files
      .sort((a, b) => b.uploadedAt.getTime() - a.uploadedAt.getTime())
      .slice(0, 5);

    return {
      totalFiles: files.length,
      totalSize,
      filesByCategory,
      recentUploads
    };
  }

  // Bulk operations
  async bulkDeleteFiles(fileIds: string[], userId: string): Promise<void> {
    for (const fileId of fileIds) {
      try {
        await this.deleteFile(fileId, userId);
      } catch (error) {
        console.error(`Failed to delete file ${fileId}:`, error);
      }
    }
  }

  async bulkMoveFiles(
    fileIds: string[],
    userId: string,
    newLocation: {
      caseId?: string;
      clientId?: string;
      lawyerId?: string;
    }
  ): Promise<void> {
    for (const fileId of fileIds) {
      try {
        await this.moveFile(fileId, userId, newLocation);
      } catch (error) {
        console.error(`Failed to move file ${fileId}:`, error);
      }
    }
  }
}
