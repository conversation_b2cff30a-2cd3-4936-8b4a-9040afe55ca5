"use strict";(self.webpackChunkveritus_secretary=self.webpackChunkveritus_secretary||[]).push([[6348],{6348:(M,p,o)=>{o.r(p),o.d(p,{SecretaryDashboardPageModule:()=>w});var c=o(177),f=o(9417),d=o(2276),l=o(8498),g=o(467),t=o(4438),u=o(8287);function m(n,r){if(1&n){const e=t.RV6();t.j41(0,"div",29),t.bIt("click",function(){const i=t.eBV(e).$implicit,s=t.XpG(2);return t.Njj(s.onView<PERSON>awyer(i.id))}),t.j41(1,"div",30)(2,"h3",31),t.E<PERSON>(3),t.k0s(),t.j41(4,"div",32)(5,"span",33),t.<PERSON><PERSON>(6),t.k0s(),t.j41(7,"span",33),t.<PERSON><PERSON>(8),t.k0s()(),t.j41(9,"p",34),t.EFF(10),t.k0s()(),t.nrm(11,"ion-icon",35),t.k0s()}if(2&n){const e=r.$implicit;t.R7$(3),t.JRh(e.name),t.R7$(3),t.SpI("",e.caseCount," cases"),t.R7$(2),t.SpI("",e.appointmentCount," appointments"),t.R7$(2),t.SpI("Last activity: ",e.lastActivity,"")}}function y(n,r){if(1&n){const e=t.RV6();t.j41(0,"div",15)(1,"div",25)(2,"h2",16),t.EFF(3,"Linked Lawyers"),t.k0s(),t.j41(4,"button",26),t.bIt("click",function(){t.eBV(e);const i=t.XpG();return t.Njj(i.onLinkNewLawyer())}),t.EFF(5," + Link New "),t.k0s()(),t.j41(6,"div",27),t.DNE(7,m,12,4,"div",28),t.k0s()()}if(2&n){const e=t.XpG();t.R7$(7),t.Y8G("ngForOf",e.linkedLawyers)}}function v(n,r){if(1&n){const e=t.RV6();t.j41(0,"div",15)(1,"div",36),t.nrm(2,"ion-icon",37),t.j41(3,"h3",38),t.EFF(4,"No Lawyers Linked"),t.k0s(),t.j41(5,"p",39),t.EFF(6," Request access to a lawyer's workspace using their code "),t.k0s(),t.j41(7,"button",40),t.bIt("click",function(){t.eBV(e);const i=t.XpG();return t.Njj(i.onLinkNewLawyer())}),t.EFF(8," Link with Lawyer "),t.k0s()()()}}function b(n,r){if(1&n&&(t.j41(0,"div",43)(1,"div",44),t.nrm(2,"ion-icon",45),t.k0s(),t.j41(3,"div",46)(4,"p",47),t.EFF(5),t.k0s(),t.j41(6,"div",48)(7,"span",49),t.EFF(8),t.k0s(),t.j41(9,"span",50),t.EFF(10),t.k0s()()()()),2&n){const e=r.$implicit;t.R7$(2),t.Y8G("name","appointment"===e.type?"calendar":"folder"),t.R7$(3),t.JRh(e.description),t.R7$(3),t.JRh(e.time),t.R7$(2),t.JRh(e.lawyer)}}function k(n,r){if(1&n&&(t.j41(0,"div",15)(1,"h2",16),t.EFF(2,"Recent Activities"),t.k0s(),t.j41(3,"div",41),t.DNE(4,b,11,4,"div",42),t.k0s()()),2&n){const e=t.XpG();t.R7$(4),t.Y8G("ngForOf",e.recentActivities)}}function h(n,r){if(1&n&&(t.j41(0,"span",60),t.EFF(1),t.k0s()),2&n){const e=t.XpG().$implicit;t.R7$(),t.JRh(e.lawyer)}}function x(n,r){if(1&n&&(t.j41(0,"span",61),t.EFF(1),t.k0s()),2&n){const e=t.XpG().$implicit;t.R7$(),t.SpI("Due: ",e.dueTime,"")}}function _(n,r){if(1&n&&(t.j41(0,"div",53),t.nrm(1,"div",54),t.j41(2,"div",55)(3,"p",56),t.EFF(4),t.k0s(),t.j41(5,"div",57),t.DNE(6,h,2,1,"span",58)(7,x,2,1,"span",59),t.k0s()(),t.nrm(8,"ion-icon",35),t.k0s()),2&n){const e=r.$implicit;t.R7$(),t.HbH("priority-"+e.priority),t.R7$(3),t.JRh(e.description),t.R7$(2),t.Y8G("ngIf",e.lawyer),t.R7$(),t.Y8G("ngIf",e.dueTime)}}function P(n,r){if(1&n&&(t.j41(0,"div",15)(1,"h2",16),t.EFF(2,"Pending Tasks"),t.k0s(),t.j41(3,"div",51),t.DNE(4,_,9,5,"div",52),t.k0s()()),2&n){const e=t.XpG();t.R7$(4),t.Y8G("ngForOf",e.pendingTasks)}}const C=[{path:"",component:(()=>{class n{constructor(e,a){this.router=e,this.firebaseService=a,this.secretaryName="Secretary",this.secretaryProfile=null,this.stats={linkedLawyers:0,totalCases:0,totalAppointments:0},this.linkedLawyers=[],this.recentActivities=[],this.pendingTasks=[]}ngOnInit(){this.loadSecretaryProfile(),this.loadDashboardData()}loadSecretaryProfile(){var e=this;return(0,g.A)(function*(){const a=e.firebaseService.getCurrentUser();a&&(e.secretaryProfile=yield e.firebaseService.getSecretaryProfile(a.uid),e.secretaryProfile&&(e.secretaryName=e.secretaryProfile.name))})()}loadDashboardData(){var e=this;return(0,g.A)(function*(){const a=e.firebaseService.getCurrentUser();if(a)try{const i=yield e.firebaseService.getSecretaryLinkedLawyers(a.uid);e.stats.linkedLawyers=i.length,e.linkedLawyers=i.map(s=>({id:s.uid,name:s.name,caseCount:0,appointmentCount:0,lastActivity:"Today"})),e.recentActivities=[{type:"appointment",description:"Scheduled appointment for John Doe",time:"2 hours ago",lawyer:"Atty. Smith"},{type:"case",description:"Updated case progress for ABC Corp",time:"4 hours ago",lawyer:"Atty. Johnson"}],e.pendingTasks=[{type:"approval",description:"Client information update pending approval",priority:"high",lawyer:"Atty. Smith"},{type:"reminder",description:"Send appointment reminder to client",priority:"medium",dueTime:"30 minutes"}]}catch(i){console.error("Error loading dashboard data:",i)}})()}onViewLawyer(e){this.router.navigate(["/secretary/lawyer",e])}onManageCalendar(){this.router.navigate(["/secretary/calendar"])}onManageFiles(){this.router.navigate(["/secretary/files"])}onManageCases(){this.router.navigate(["/secretary/cases"])}onViewFinances(){this.router.navigate(["/secretary/finances"])}onViewAuditLog(){this.router.navigate(["/secretary/audit"])}onLinkNewLawyer(){this.router.navigate(["/secretary/link-lawyer"])}getGreeting(){const e=(new Date).getHours();return e<12?"Good morning":e<17?"Good afternoon":"Good evening"}static{this.\u0275fac=function(a){return new(a||n)(t.rXU(l.Ix),t.rXU(u.f))}}static{this.\u0275cmp=t.VBU({type:n,selectors:[["app-secretary-dashboard"]],decls:57,vars:9,consts:[[1,"dashboard-content","veritus-gradient-bg"],[1,"dashboard-container","veritus-safe-area-top"],[1,"dashboard-header"],[1,"greeting-section"],[1,"greeting","veritus-text-2xl","veritus-font-bold","veritus-text-white"],[1,"sub-greeting","veritus-text-base","veritus-text-white-70"],[1,"stats-grid"],[1,"stat-card"],[1,"stat-icon"],["name","people-outline"],[1,"stat-content"],[1,"stat-number","veritus-text-2xl","veritus-font-bold"],[1,"stat-label","veritus-text-sm","veritus-text-gray"],["name","briefcase-outline"],["name","calendar-outline"],[1,"section"],[1,"section-title","veritus-text-lg","veritus-font-semibold"],[1,"action-grid"],[1,"action-btn",3,"click"],["name","calendar",1,"action-icon"],[1,"action-label"],["name","folder",1,"action-icon"],["name","document",1,"action-icon"],["name","card",1,"action-icon"],["class","section",4,"ngIf"],[1,"section-header"],[1,"link-btn","veritus-text-sm","veritus-text-gold",3,"click"],[1,"lawyer-cards"],["class","lawyer-card",3,"click",4,"ngFor","ngForOf"],[1,"lawyer-card",3,"click"],[1,"lawyer-info"],[1,"lawyer-name","veritus-text-base","veritus-font-semibold"],[1,"lawyer-stats"],[1,"stat-item"],[1,"last-activity","veritus-text-xs","veritus-text-gray"],["name","chevron-forward",1,"chevron-icon"],[1,"empty-state"],["name","people-outline",1,"empty-icon"],[1,"empty-title","veritus-text-lg","veritus-font-semibold"],[1,"empty-description","veritus-text-sm","veritus-text-gray"],[1,"veritus-btn-primary",3,"click"],[1,"activity-list"],["class","activity-item",4,"ngFor","ngForOf"],[1,"activity-item"],[1,"activity-icon"],[3,"name"],[1,"activity-content"],[1,"activity-description","veritus-text-sm"],[1,"activity-meta"],[1,"activity-time","veritus-text-xs","veritus-text-gray"],[1,"activity-lawyer","veritus-text-xs","veritus-text-gold"],[1,"task-list"],["class","task-item",4,"ngFor","ngForOf"],[1,"task-item"],[1,"task-priority"],[1,"task-content"],[1,"task-description","veritus-text-sm"],[1,"task-meta"],["class","task-lawyer veritus-text-xs veritus-text-gold",4,"ngIf"],["class","task-due veritus-text-xs veritus-text-gray",4,"ngIf"],[1,"task-lawyer","veritus-text-xs","veritus-text-gold"],[1,"task-due","veritus-text-xs","veritus-text-gray"]],template:function(a,i){1&a&&(t.j41(0,"ion-content",0)(1,"div",1)(2,"div",2)(3,"div",3)(4,"h1",4),t.EFF(5),t.k0s(),t.j41(6,"p",5),t.EFF(7," Manage your lawyers' workspaces efficiently "),t.k0s()()(),t.j41(8,"div",6)(9,"div",7)(10,"div",8),t.nrm(11,"ion-icon",9),t.k0s(),t.j41(12,"div",10)(13,"h3",11),t.EFF(14),t.k0s(),t.j41(15,"p",12),t.EFF(16,"Linked Lawyers"),t.k0s()()(),t.j41(17,"div",7)(18,"div",8),t.nrm(19,"ion-icon",13),t.k0s(),t.j41(20,"div",10)(21,"h3",11),t.EFF(22),t.k0s(),t.j41(23,"p",12),t.EFF(24,"Total Cases"),t.k0s()()(),t.j41(25,"div",7)(26,"div",8),t.nrm(27,"ion-icon",14),t.k0s(),t.j41(28,"div",10)(29,"h3",11),t.EFF(30),t.k0s(),t.j41(31,"p",12),t.EFF(32,"Appointments"),t.k0s()()()(),t.j41(33,"div",15)(34,"h2",16),t.EFF(35,"Quick Actions"),t.k0s(),t.j41(36,"div",17)(37,"button",18),t.bIt("click",function(){return i.onManageCalendar()}),t.nrm(38,"ion-icon",19),t.j41(39,"span",20),t.EFF(40,"Calendar"),t.k0s()(),t.j41(41,"button",18),t.bIt("click",function(){return i.onManageCases()}),t.nrm(42,"ion-icon",21),t.j41(43,"span",20),t.EFF(44,"Cases"),t.k0s()(),t.j41(45,"button",18),t.bIt("click",function(){return i.onManageFiles()}),t.nrm(46,"ion-icon",22),t.j41(47,"span",20),t.EFF(48,"Files"),t.k0s()(),t.j41(49,"button",18),t.bIt("click",function(){return i.onViewFinances()}),t.nrm(50,"ion-icon",23),t.j41(51,"span",20),t.EFF(52,"Finances"),t.k0s()()()(),t.DNE(53,y,8,1,"div",24)(54,v,9,0,"div",24)(55,k,5,1,"div",24)(56,P,5,1,"div",24),t.k0s()()),2&a&&(t.R7$(5),t.Lme(" ",i.getGreeting(),", ",i.secretaryName,"! "),t.R7$(9),t.JRh(i.stats.linkedLawyers),t.R7$(8),t.JRh(i.stats.totalCases),t.R7$(8),t.JRh(i.stats.totalAppointments),t.R7$(23),t.Y8G("ngIf",i.linkedLawyers.length>0),t.R7$(),t.Y8G("ngIf",0===i.linkedLawyers.length),t.R7$(),t.Y8G("ngIf",i.recentActivities.length>0),t.R7$(),t.Y8G("ngIf",i.pendingTasks.length>0))},dependencies:[c.Sq,c.bT,d.W9,d.iq],styles:[".dashboard-content[_ngcontent-%COMP%]{--background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%)}.dashboard-container[_ngcontent-%COMP%]{padding:20px;min-height:100vh}.dashboard-header[_ngcontent-%COMP%]{margin-bottom:30px}.greeting-section[_ngcontent-%COMP%]{text-align:center}.greeting[_ngcontent-%COMP%]{margin-bottom:8px}.sub-greeting[_ngcontent-%COMP%]{opacity:.8}.stats-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fit,minmax(100px,1fr));gap:15px;margin-bottom:30px}.stat-card[_ngcontent-%COMP%]{background:#ffffff1a;border-radius:12px;padding:20px;text-align:center;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px);border:1px solid rgba(255,255,255,.1)}.stat-icon[_ngcontent-%COMP%]{margin-bottom:10px}.stat-icon[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:24px;color:#d4af37}.stat-number[_ngcontent-%COMP%]{color:#fff;margin-bottom:5px}.stat-label[_ngcontent-%COMP%]{color:#ffffffb3}.section[_ngcontent-%COMP%]{margin-bottom:30px}.section-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;margin-bottom:15px}.section-title[_ngcontent-%COMP%]{color:#fff}.link-btn[_ngcontent-%COMP%]{background:none;border:none;color:#d4af37;font-weight:500}.action-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(4,1fr);gap:15px}.action-btn[_ngcontent-%COMP%]{background:#ffffff1a;border:none;border-radius:12px;padding:20px 10px;text-align:center;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px);border:1px solid rgba(255,255,255,.1);transition:all .3s ease}.action-btn[_ngcontent-%COMP%]:hover{background:#ffffff26;transform:translateY(-2px)}.action-icon[_ngcontent-%COMP%]{font-size:24px;color:#d4af37;margin-bottom:8px;display:block}.action-label[_ngcontent-%COMP%]{color:#fff;font-size:12px;font-weight:500}.lawyer-cards[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:12px}.lawyer-card[_ngcontent-%COMP%]{background:#ffffff1a;border-radius:12px;padding:16px;display:flex;justify-content:space-between;align-items:center;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px);border:1px solid rgba(255,255,255,.1);cursor:pointer;transition:all .3s ease}.lawyer-card[_ngcontent-%COMP%]:hover{background:#ffffff26;transform:translate(5px)}.lawyer-info[_ngcontent-%COMP%]{flex:1}.lawyer-name[_ngcontent-%COMP%]{color:#fff;margin-bottom:8px}.lawyer-stats[_ngcontent-%COMP%]{display:flex;gap:15px;margin-bottom:5px}.stat-item[_ngcontent-%COMP%]{color:#d4af37;font-size:12px;font-weight:500}.last-activity[_ngcontent-%COMP%]{color:#fff9}.chevron-icon[_ngcontent-%COMP%]{color:#d4af37;font-size:18px}.empty-state[_ngcontent-%COMP%]{text-align:center;padding:40px 20px;background:#ffffff0d;border-radius:12px;border:1px solid rgba(255,255,255,.1)}.empty-icon[_ngcontent-%COMP%]{font-size:48px;color:#d4af37;margin-bottom:16px}.empty-title[_ngcontent-%COMP%]{color:#fff;margin-bottom:8px}.empty-description[_ngcontent-%COMP%]{color:#ffffffb3;margin-bottom:20px}.activity-list[_ngcontent-%COMP%], .task-list[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:12px}.activity-item[_ngcontent-%COMP%], .task-item[_ngcontent-%COMP%]{background:#ffffff1a;border-radius:12px;padding:16px;display:flex;align-items:center;gap:12px;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px);border:1px solid rgba(255,255,255,.1)}.activity-icon[_ngcontent-%COMP%]{width:32px;height:32px;background:#d4af3733;border-radius:8px;display:flex;align-items:center;justify-content:center}.activity-icon[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{color:#d4af37;font-size:16px}.activity-content[_ngcontent-%COMP%], .task-content[_ngcontent-%COMP%]{flex:1}.activity-description[_ngcontent-%COMP%], .task-description[_ngcontent-%COMP%]{color:#fff;margin-bottom:4px}.activity-meta[_ngcontent-%COMP%], .task-meta[_ngcontent-%COMP%]{display:flex;gap:10px}.activity-time[_ngcontent-%COMP%], .activity-lawyer[_ngcontent-%COMP%], .task-lawyer[_ngcontent-%COMP%], .task-due[_ngcontent-%COMP%]{font-size:11px}.task-priority[_ngcontent-%COMP%]{width:4px;height:100%;border-radius:2px}.task-priority.priority-high[_ngcontent-%COMP%]{background:#ff4757}.task-priority.priority-medium[_ngcontent-%COMP%]{background:#ffa502}.task-priority.priority-low[_ngcontent-%COMP%]{background:#2ed573}@media (max-width: 768px){.action-grid[_ngcontent-%COMP%]{grid-template-columns:repeat(2,1fr)}.stats-grid[_ngcontent-%COMP%]{grid-template-columns:repeat(3,1fr)}}"]})}}return n})()}];let F=(()=>{class n{static{this.\u0275fac=function(a){return new(a||n)}}static{this.\u0275mod=t.$C({type:n})}static{this.\u0275inj=t.G2t({imports:[l.iI.forChild(C),l.iI]})}}return n})(),w=(()=>{class n{static{this.\u0275fac=function(a){return new(a||n)}}static{this.\u0275mod=t.$C({type:n})}static{this.\u0275inj=t.G2t({imports:[c.MD,f.YN,d.bv,F]})}}return n})()}}]);