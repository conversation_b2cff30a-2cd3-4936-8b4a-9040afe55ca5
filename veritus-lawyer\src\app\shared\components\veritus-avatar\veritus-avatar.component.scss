.veritus-avatar {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: var(--veritus-gray-light);
  overflow: hidden;
  position: relative;
}

.veritus-avatar-sm {
  width: 32px;
  height: 32px;
}

.veritus-avatar-md {
  width: 48px;
  height: 48px;
}

.veritus-avatar-lg {
  width: 64px;
  height: 64px;
}

.veritus-avatar-xl {
  width: 96px;
  height: 96px;
}

.veritus-avatar-border {
  border: 3px solid var(--veritus-white);
  box-shadow: 0 0 0 1px var(--veritus-gray-light);
}

.veritus-avatar-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.veritus-avatar-initials {
  font-weight: var(--veritus-font-weight-semibold);
  color: var(--veritus-primary);
  font-size: calc(var(--veritus-font-size-base) * 0.8);
}

.veritus-avatar-placeholder {
  color: var(--veritus-gray-medium);
  
  ion-icon {
    font-size: calc(100% * 0.6);
  }
}
