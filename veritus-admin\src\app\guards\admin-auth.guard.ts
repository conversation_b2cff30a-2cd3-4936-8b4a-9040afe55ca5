import { Injectable } from '@angular/core';
import { CanActivate, ActivatedRouteSnapshot, RouterStateSnapshot, Router } from '@angular/router';
import { Observable, of } from 'rxjs';
import { map, take, switchMap, delay } from 'rxjs/operators';
import { AdminAuthService } from '../services/admin-auth.service';

@Injectable({
  providedIn: 'root'
})
export class AdminAuthGuard implements CanActivate {

  constructor(
    private adminAuthService: AdminAuthService,
    private router: Router
  ) {}

  canActivate(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): Observable<boolean> | Promise<boolean> | boolean {

    // First check if we have a valid session stored
    if (this.adminAuthService.isSessionValid()) {
      const currentAdmin = this.adminAuthService.getCurrentAdmin();
      if (currentAdmin && currentAdmin.isActive) {
        return true;
      }
    }

    return this.adminAuthService.currentAdmin$.pipe(
      take(1),
      map(admin => {
        if (admin && admin.isActive) {
          // Check for specific permissions if required
          const requiredPermission = route.data?.['permission'];
          if (requiredPermission && !this.adminAuthService.hasPermission(requiredPermission)) {
            this.router.navigate(['/admin/dashboard']);
            return false;
          }

          // Check for specific role if required
          const requiredRole = route.data?.['role'];
          if (requiredRole && !this.adminAuthService.hasRole(requiredRole)) {
            this.router.navigate(['/admin/dashboard']);
            return false;
          }

          return true;
        } else {
          this.router.navigate(['/login']);
          return false;
        }
      })
    );
  }
}
