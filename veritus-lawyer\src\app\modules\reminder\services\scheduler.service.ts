import { Injectable } from '@angular/core';
import { Reminder } from './reminder.service';

@Injectable({
  providedIn: 'root'
})
export class SchedulerService {

  private scheduledReminders = new Map<string, any>();

  constructor() { }

  // Schedule a reminder
  async scheduleReminder(reminder: Reminder): Promise<void> {
    const now = new Date();
    const reminderTime = new Date(reminder.reminderTime);
    
    if (reminderTime <= now) {
      // If reminder time is in the past or now, process immediately
      console.log('Processing immediate reminder:', reminder.id);
      return;
    }

    const delay = reminderTime.getTime() - now.getTime();
    
    // Schedule using setTimeout (for demo purposes)
    // In production, use a proper job scheduler like Bull Queue or similar
    const timeoutId = setTimeout(() => {
      this.processReminder(reminder);
    }, delay);

    this.scheduledReminders.set(reminder.id, timeoutId);
    
    console.log(`Scheduled reminder ${reminder.id} for ${reminderTime.toISOString()}`);
  }

  // Reschedule a reminder
  async rescheduleReminder(reminder: Reminder): Promise<void> {
    // Cancel existing schedule
    await this.cancelReminder(reminder.id);
    
    // Schedule with new time
    await this.scheduleReminder(reminder);
  }

  // Cancel a scheduled reminder
  async cancelReminder(reminderId: string): Promise<void> {
    const timeoutId = this.scheduledReminders.get(reminderId);
    
    if (timeoutId) {
      clearTimeout(timeoutId);
      this.scheduledReminders.delete(reminderId);
      console.log(`Cancelled reminder ${reminderId}`);
    }
  }

  // Process a reminder when it's due
  private async processReminder(reminder: Reminder): Promise<void> {
    try {
      console.log('Processing reminder:', reminder.id);
      
      // Remove from scheduled reminders
      this.scheduledReminders.delete(reminder.id);
      
      // TODO: Call ReminderService to process the reminder
      // This would typically involve:
      // 1. Sending the notification
      // 2. Updating the reminder status
      // 3. Creating recurring reminders if needed
      
    } catch (error) {
      console.error(`Failed to process reminder ${reminder.id}:`, error);
    }
  }

  // Get all scheduled reminders (for debugging)
  getScheduledReminders(): string[] {
    return Array.from(this.scheduledReminders.keys());
  }

  // Clear all scheduled reminders
  clearAllReminders(): void {
    this.scheduledReminders.forEach((timeoutId) => {
      clearTimeout(timeoutId);
    });
    this.scheduledReminders.clear();
  }
}
