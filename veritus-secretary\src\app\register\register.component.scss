// Global Fixes
html, body {
  height: 100%;
  margin: 0;
  padding: 0;
  overflow: hidden;
  font-family: 'Segoe UI', Tahoma, sans-serif;
}

:host {
  display: block;
  height: 100%;
  width: 100%;
  overflow: hidden;
}

// Main Page Wrapper
.modern-auth-content {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  background: #f8f9fa;
  overflow: auto;
  padding: 32px 16px;
  box-sizing: border-box;
}

// Layout Wrapper
.auth-wrapper {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center; // vertical center
  align-items: center;
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0;
}

// Main Container
.auth-main-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  max-width: 600px;
  gap: 32px;
}

// Branding Section
.logo-section {
  text-align: center;
}

.logo-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.logo-icon {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, #C49A56 0%, #D4AF6A 100%);
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8px 24px rgba(196, 154, 86, 0.3);
}

.secretary-icon {
  font-size: 36px;
  color: white;
}

.brand-title {
  font-size: 36px;
  font-weight: 700;
  color: #2c3e50;
  margin: 0;
  text-align: center;
}

.brand-subtitle {
  font-size: 18px;
  color: #6c757d;
  font-weight: 500;
  margin: 0;
  text-align: center;
}

// Form Card Styling
.form-card {
  background: #fff;
  border-radius: 16px;
  padding: 40px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid #e1e5e9;
  width: 100%;
  box-sizing: border-box;
  animation: fadeInUp 0.6s ease-out;
}

.form-header {
  text-align: center;
  margin-bottom: 32px;
}

.form-title {
  font-size: 32px;
  font-weight: 700;
  color: #2c3e50;
  margin-bottom: 12px;
}

.form-subtitle {
  font-size: 16px;
  color: #6c757d;
  font-weight: 400;
  line-height: 1.5;
}

// Form Structure
.modern-form {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.secretary-form {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.input-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.input-label {
  font-size: 15px;
  font-weight: 600;
  color: #2c3e50;
}

.form-input {
  width: 100%;
  padding: 16px 20px;
  border: 2px solid #e1e5e9;
  border-radius: 8px;
  font-size: 16px;
  background: #fff;
  color: #2c3e50;
  transition: all 0.3s ease;
}

.form-input:focus {
  outline: none;
  border-color: #C49A56;
  box-shadow: 0 0 0 3px rgba(196, 154, 86, 0.1);
}

.form-input:hover:not(:focus) {
  border-color: #ced4da;
}

.form-input::placeholder {
  color: #6c757d;
}

// Password Field
.password-input-container {
  position: relative;
  display: flex;
  align-items: center;
}

.password-input {
  padding-right: 45px !important;
}

.password-toggle {
  position: absolute;
  right: 16px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  cursor: pointer;
  width: 24px;
  height: 24px;
  color: #6b7280;
  z-index: 1;
}

.password-toggle:hover {
  color: #c49a56;
}

.toggle-icon {
  width: 20px;
  height: 20px;
  fill: currentColor;
}

.input-help {
  font-size: 12px;
  color: #6b7280;
  font-style: italic;
}

// Button
.modern-continue-btn {
  width: 100%;
  height: 50px;
  background: linear-gradient(135deg, #C49A56 0%, #D4AF6A 100%);
  border: none;
  border-radius: 8px;
  color: white;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  transition: all 0.3s ease;
  margin-top: 16px;
  box-shadow: 0 4px 12px rgba(196, 154, 86, 0.3);
}

.modern-continue-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #B8894A 0%, #C49A56 100%);
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(196, 154, 86, 0.4);
}

.modern-continue-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  background: #6c757d;
  box-shadow: none;
}

.btn-text {
  font-size: 16px;
  font-weight: 600;
}

.btn-icon {
  font-size: 20px;
}

// Sign In Link
.signin-section {
  text-align: center;
  margin-top: 32px;
  padding-top: 24px;
  border-top: 1px solid #e1e5e9;
}

.signin-link {
  background: none;
  border: none;
  font-size: 15px;
  color: #C49A56;
  font-weight: 500;
  cursor: pointer;
  padding: 12px 20px;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.signin-link:hover {
  background-color: rgba(196, 154, 86, 0.1);
  color: #B8894A;
  text-decoration: underline;
}

// Animations
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// Responsive
@media (max-width: 768px) {
  .modern-auth-content {
    padding: 24px 12px;
  }

  .form-card {
    padding: 32px 24px;
  }

  .form-title {
    font-size: 28px;
  }

  .form-input {
    padding: 14px 16px;
    font-size: 15px;
  }

  .modern-continue-btn {
    height: 48px;
  }
}

@media (max-width: 480px) {
  .form-card {
    padding: 24px 16px;
  }

  .form-title {
    font-size: 24px;
  }

  .secretary-form {
    gap: 20px;
  }
}
