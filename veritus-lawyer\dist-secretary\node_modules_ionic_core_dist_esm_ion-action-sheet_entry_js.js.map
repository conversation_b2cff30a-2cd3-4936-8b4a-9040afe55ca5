{"version": 3, "file": "node_modules_ionic_core_dist_esm_ion-action-sheet_entry_js.js", "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AAC4H;AAC/C;AAC5B;AACyB;AAC8J;AACjL;AACM;AACE;AACjC;AACG;AACJ;AACA;AACa;AACE;AACF;AACb;;AAE7B;AACA;AACA;AACA,MAAMmC,iBAAiB,GAAIC,MAAM,IAAK;EAClC,MAAMC,aAAa,GAAGH,yDAAe,CAAC,CAAC;EACvC,MAAMI,iBAAiB,GAAGJ,yDAAe,CAAC,CAAC;EAC3C,MAAMK,gBAAgB,GAAGL,yDAAe,CAAC,CAAC;EAC1CI,iBAAiB,CACZE,UAAU,CAACJ,MAAM,CAACK,aAAa,CAAC,cAAc,CAAC,CAAC,CAChDC,MAAM,CAAC,SAAS,EAAE,IAAI,EAAE,yBAAyB,CAAC,CAClDC,YAAY,CAAC;IACd,gBAAgB,EAAE;EACtB,CAAC,CAAC,CACGC,gBAAgB,CAAC,CAAC,gBAAgB,CAAC,CAAC;EACzCL,gBAAgB,CACXC,UAAU,CAACJ,MAAM,CAACK,aAAa,CAAC,uBAAuB,CAAC,CAAC,CACzDC,MAAM,CAAC,WAAW,EAAE,kBAAkB,EAAE,gBAAgB,CAAC;EAC9D,OAAOL,aAAa,CACfG,UAAU,CAACJ,MAAM,CAAC,CAClBS,MAAM,CAAC,6BAA6B,CAAC,CACrCC,QAAQ,CAAC,GAAG,CAAC,CACbC,YAAY,CAAC,CAACT,iBAAiB,EAAEC,gBAAgB,CAAC,CAAC;AAC5D,CAAC;;AAED;AACA;AACA;AACA,MAAMS,iBAAiB,GAAIZ,MAAM,IAAK;EAClC,MAAMC,aAAa,GAAGH,yDAAe,CAAC,CAAC;EACvC,MAAMI,iBAAiB,GAAGJ,yDAAe,CAAC,CAAC;EAC3C,MAAMK,gBAAgB,GAAGL,yDAAe,CAAC,CAAC;EAC1CI,iBAAiB,CAACE,UAAU,CAACJ,MAAM,CAACK,aAAa,CAAC,cAAc,CAAC,CAAC,CAACC,MAAM,CAAC,SAAS,EAAE,yBAAyB,EAAE,CAAC,CAAC;EAClHH,gBAAgB,CACXC,UAAU,CAACJ,MAAM,CAACK,aAAa,CAAC,uBAAuB,CAAC,CAAC,CACzDC,MAAM,CAAC,WAAW,EAAE,gBAAgB,EAAE,kBAAkB,CAAC;EAC9D,OAAOL,aAAa,CACfG,UAAU,CAACJ,MAAM,CAAC,CAClBS,MAAM,CAAC,6BAA6B,CAAC,CACrCC,QAAQ,CAAC,GAAG,CAAC,CACbC,YAAY,CAAC,CAACT,iBAAiB,EAAEC,gBAAgB,CAAC,CAAC;AAC5D,CAAC;;AAED;AACA;AACA;AACA,MAAMU,gBAAgB,GAAIb,MAAM,IAAK;EACjC,MAAMC,aAAa,GAAGH,yDAAe,CAAC,CAAC;EACvC,MAAMI,iBAAiB,GAAGJ,yDAAe,CAAC,CAAC;EAC3C,MAAMK,gBAAgB,GAAGL,yDAAe,CAAC,CAAC;EAC1CI,iBAAiB,CACZE,UAAU,CAACJ,MAAM,CAACK,aAAa,CAAC,cAAc,CAAC,CAAC,CAChDC,MAAM,CAAC,SAAS,EAAE,IAAI,EAAE,yBAAyB,CAAC,CAClDC,YAAY,CAAC;IACd,gBAAgB,EAAE;EACtB,CAAC,CAAC,CACGC,gBAAgB,CAAC,CAAC,gBAAgB,CAAC,CAAC;EACzCL,gBAAgB,CACXC,UAAU,CAACJ,MAAM,CAACK,aAAa,CAAC,uBAAuB,CAAC,CAAC,CACzDC,MAAM,CAAC,WAAW,EAAE,kBAAkB,EAAE,gBAAgB,CAAC;EAC9D,OAAOL,aAAa,CACfG,UAAU,CAACJ,MAAM,CAAC,CAClBS,MAAM,CAAC,6BAA6B,CAAC,CACrCC,QAAQ,CAAC,GAAG,CAAC,CACbC,YAAY,CAAC,CAACT,iBAAiB,EAAEC,gBAAgB,CAAC,CAAC;AAC5D,CAAC;;AAED;AACA;AACA;AACA,MAAMW,gBAAgB,GAAId,MAAM,IAAK;EACjC,MAAMC,aAAa,GAAGH,yDAAe,CAAC,CAAC;EACvC,MAAMI,iBAAiB,GAAGJ,yDAAe,CAAC,CAAC;EAC3C,MAAMK,gBAAgB,GAAGL,yDAAe,CAAC,CAAC;EAC1CI,iBAAiB,CAACE,UAAU,CAACJ,MAAM,CAACK,aAAa,CAAC,cAAc,CAAC,CAAC,CAACC,MAAM,CAAC,SAAS,EAAE,yBAAyB,EAAE,CAAC,CAAC;EAClHH,gBAAgB,CACXC,UAAU,CAACJ,MAAM,CAACK,aAAa,CAAC,uBAAuB,CAAC,CAAC,CACzDC,MAAM,CAAC,WAAW,EAAE,gBAAgB,EAAE,kBAAkB,CAAC;EAC9D,OAAOL,aAAa,CACfG,UAAU,CAACJ,MAAM,CAAC,CAClBS,MAAM,CAAC,6BAA6B,CAAC,CACrCC,QAAQ,CAAC,GAAG,CAAC,CACbC,YAAY,CAAC,CAACT,iBAAiB,EAAEC,gBAAgB,CAAC,CAAC;AAC5D,CAAC;AAED,MAAMY,iBAAiB,GAAG,w4RAAw4R;AACl6R,MAAMC,uBAAuB,GAAGD,iBAAiB;AAEjD,MAAME,gBAAgB,GAAG,+7KAA+7K;AACx9K,MAAMC,sBAAsB,GAAGD,gBAAgB;AAE/C,MAAME,WAAW,GAAG,MAAM;EACtBC,WAAWA,CAACC,OAAO,EAAE;IACjBxD,qDAAgB,CAAC,IAAI,EAAEwD,OAAO,CAAC;IAC/B,IAAI,CAACC,UAAU,GAAGvD,qDAAW,CAAC,IAAI,EAAE,0BAA0B,EAAE,CAAC,CAAC;IAClE,IAAI,CAACwD,WAAW,GAAGxD,qDAAW,CAAC,IAAI,EAAE,2BAA2B,EAAE,CAAC,CAAC;IACpE,IAAI,CAACyD,WAAW,GAAGzD,qDAAW,CAAC,IAAI,EAAE,2BAA2B,EAAE,CAAC,CAAC;IACpE,IAAI,CAAC0D,UAAU,GAAG1D,qDAAW,CAAC,IAAI,EAAE,0BAA0B,EAAE,CAAC,CAAC;IAClE,IAAI,CAAC2D,mBAAmB,GAAG3D,qDAAW,CAAC,IAAI,EAAE,YAAY,EAAE,CAAC,CAAC;IAC7D,IAAI,CAAC4D,oBAAoB,GAAG5D,qDAAW,CAAC,IAAI,EAAE,aAAa,EAAE,CAAC,CAAC;IAC/D,IAAI,CAAC6D,oBAAoB,GAAG7D,qDAAW,CAAC,IAAI,EAAE,aAAa,EAAE,CAAC,CAAC;IAC/D,IAAI,CAAC8D,mBAAmB,GAAG9D,qDAAW,CAAC,IAAI,EAAE,YAAY,EAAE,CAAC,CAAC;IAC7D,IAAI,CAAC+D,kBAAkB,GAAGnD,wDAAwB,CAAC,IAAI,CAAC;IACxD,IAAI,CAACoD,cAAc,GAAGrD,+DAAoB,CAAC,CAAC;IAC5C,IAAI,CAACsD,iBAAiB,GAAGpD,wDAAuB,CAAC,CAAC;IAClD,IAAI,CAACqD,SAAS,GAAG,KAAK;IACtB,IAAI,CAACC,aAAa,GAAG,MAAM;MACvB,IAAI,CAAC/C,OAAO,CAACgD,SAAS,EAAErD,oDAAQ,CAAC;IACrC,CAAC;IACD,IAAI,CAACsD,qBAAqB,GAAIC,EAAE,IAAK;MACjC,MAAMC,IAAI,GAAGD,EAAE,CAACE,MAAM,CAACD,IAAI;MAC3B,IAAItD,wDAAQ,CAACsD,IAAI,CAAC,EAAE;QAChB,MAAME,YAAY,GAAG,IAAI,CAACC,UAAU,CAAC,CAAC,CAACC,IAAI,CAAE9C,CAAC,IAAKA,CAAC,CAAC0C,IAAI,KAAK,QAAQ,CAAC;QACvE,IAAI,CAACK,iBAAiB,CAACH,YAAY,CAAC;MACxC;IACJ,CAAC;IACD,IAAI,CAACI,YAAY,GAAGT,SAAS;IAC7B,IAAI,CAACU,QAAQ,GAAGV,SAAS;IACzB,IAAI,CAACW,aAAa,GAAG,KAAK;IAC1B,IAAI,CAACC,aAAa,GAAG,IAAI;IACzB,IAAI,CAACC,cAAc,GAAGb,SAAS;IAC/B,IAAI,CAACc,cAAc,GAAGd,SAAS;IAC/B,IAAI,CAACe,OAAO,GAAG,EAAE;IACjB,IAAI,CAACC,QAAQ,GAAGhB,SAAS;IACzB,IAAI,CAACiB,eAAe,GAAG,IAAI;IAC3B,IAAI,CAACC,MAAM,GAAGlB,SAAS;IACvB,IAAI,CAACmB,SAAS,GAAGnB,SAAS;IAC1B,IAAI,CAACoB,WAAW,GAAG,KAAK;IACxB,IAAI,CAACC,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACC,cAAc,GAAGtB,SAAS;IAC/B,IAAI,CAACuB,MAAM,GAAG,KAAK;IACnB,IAAI,CAACC,OAAO,GAAGxB,SAAS;EAC5B;EACAyB,cAAcA,CAACC,QAAQ,EAAEC,QAAQ,EAAE;IAC/B,IAAID,QAAQ,KAAK,IAAI,IAAIC,QAAQ,KAAK,KAAK,EAAE;MACzC,IAAI,CAAC7E,OAAO,CAAC,CAAC;IAClB,CAAC,MACI,IAAI4E,QAAQ,KAAK,KAAK,IAAIC,QAAQ,KAAK,IAAI,EAAE;MAC9C,IAAI,CAAC3E,OAAO,CAAC,CAAC;IAClB;EACJ;EACA4E,cAAcA,CAAA,EAAG;IACb,MAAM;MAAEJ,OAAO;MAAEK,EAAE;MAAEhC;IAAkB,CAAC,GAAG,IAAI;IAC/C,IAAI2B,OAAO,EAAE;MACT3B,iBAAiB,CAACiC,gBAAgB,CAACD,EAAE,EAAEL,OAAO,CAAC;IACnD;EACJ;EACA;AACJ;AACA;EACU1E,OAAOA,CAAA,EAAG;IAAA,IAAAiF,KAAA;IAAA,OAAAC,6KAAA;MACZ,MAAMC,MAAM,SAASF,KAAI,CAACnC,cAAc,CAACsC,IAAI,CAAC,CAAC;MAC/C,MAAMH,KAAI,CAACpC,kBAAkB,CAACwC,eAAe,CAAC,CAAC;MAC/C,MAAMrF,wDAAO,CAACiF,KAAI,EAAE,kBAAkB,EAAEnE,iBAAiB,EAAEc,gBAAgB,CAAC;MAC5EuD,MAAM,CAAC,CAAC;IAAC;EACb;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACUjF,OAAOA,CAACoF,IAAI,EAAEjC,IAAI,EAAE;IAAA,IAAAkC,MAAA;IAAA,OAAAL,6KAAA;MACtB,MAAMC,MAAM,SAASI,MAAI,CAACzC,cAAc,CAACsC,IAAI,CAAC,CAAC;MAC/C,MAAMI,SAAS,SAAStF,wDAAO,CAACqF,MAAI,EAAED,IAAI,EAAEjC,IAAI,EAAE,kBAAkB,EAAE1B,iBAAiB,EAAEE,gBAAgB,CAAC;MAC1G,IAAI2D,SAAS,EAAE;QACXD,MAAI,CAAC1C,kBAAkB,CAAC4C,iBAAiB,CAAC,CAAC;MAC/C;MACAN,MAAM,CAAC,CAAC;MACR,OAAOK,SAAS;IAAC;EACrB;EACA;AACJ;AACA;EACIE,YAAYA,CAAA,EAAG;IACX,OAAOvF,wDAAW,CAAC,IAAI,CAAC4E,EAAE,EAAE,0BAA0B,CAAC;EAC3D;EACA;AACJ;AACA;AACA;EACIY,aAAaA,CAAA,EAAG;IACZ,OAAOxF,wDAAW,CAAC,IAAI,CAAC4E,EAAE,EAAE,2BAA2B,CAAC;EAC5D;EACMa,WAAWA,CAACC,MAAM,EAAE;IAAA,IAAAC,MAAA;IAAA,OAAAZ,6KAAA;MACtB,MAAM7B,IAAI,GAAGwC,MAAM,CAACxC,IAAI;MACxB,IAAItD,wDAAQ,CAACsD,IAAI,CAAC,EAAE;QAChB,OAAOyC,MAAI,CAAC5F,OAAO,CAAC2F,MAAM,CAACP,IAAI,EAAEjC,IAAI,CAAC;MAC1C;MACA,MAAM0C,aAAa,SAASD,MAAI,CAACpC,iBAAiB,CAACmC,MAAM,CAAC;MAC1D,IAAIE,aAAa,EAAE;QACf,OAAOD,MAAI,CAAC5F,OAAO,CAAC2F,MAAM,CAACP,IAAI,EAAEO,MAAM,CAACxC,IAAI,CAAC;MACjD;MACA,OAAO2C,OAAO,CAACC,OAAO,CAAC,CAAC;IAAC;EAC7B;EACMvC,iBAAiBA,CAACmC,MAAM,EAAE;IAAA,OAAAX,6KAAA;MAC5B,IAAIW,MAAM,EAAE;QACR;QACA;QACA,MAAMK,GAAG,SAAS7F,wDAAQ,CAACwF,MAAM,CAACM,OAAO,CAAC;QAC1C,IAAID,GAAG,KAAK,KAAK,EAAE;UACf;UACA,OAAO,KAAK;QAChB;MACJ;MACA,OAAO,IAAI;IAAC;EAChB;EACA1C,UAAUA,CAAA,EAAG;IACT,OAAO,IAAI,CAACS,OAAO,CAACmC,GAAG,CAAEzF,CAAC,IAAK;MAC3B,OAAO,OAAOA,CAAC,KAAK,QAAQ,GAAG;QAAE0F,IAAI,EAAE1F;MAAE,CAAC,GAAGA,CAAC;IAClD,CAAC,CAAC;EACN;EACA2F,iBAAiBA,CAAA,EAAG;IAChB/F,wDAAc,CAAC,IAAI,CAACwE,EAAE,CAAC;IACvB,IAAI,CAACD,cAAc,CAAC,CAAC;EACzB;EACAyB,oBAAoBA,CAAA,EAAG;IACnB,IAAI,IAAI,CAACC,OAAO,EAAE;MACd,IAAI,CAACA,OAAO,CAACC,OAAO,CAAC,CAAC;MACtB,IAAI,CAACD,OAAO,GAAGtD,SAAS;IAC5B;IACA,IAAI,CAACH,iBAAiB,CAAC2D,mBAAmB,CAAC,CAAC;EAChD;EACAC,iBAAiBA,CAAA,EAAG;IAChBlG,wDAAY,CAAC,IAAI,CAACsE,EAAE,CAAC;EACzB;EACA6B,gBAAgBA,CAAA,EAAG;IACf;AACR;AACA;AACA;AACA;AACA;AACA;IACQ,MAAM;MAAEC,OAAO;MAAEC;IAAU,CAAC,GAAG,IAAI;IACnC,IAAI,CAAC,IAAI,CAACN,OAAO,IAAI5F,4DAAU,CAAC,IAAI,CAAC,KAAK,KAAK,IAAIkG,SAAS,IAAID,OAAO,EAAE;MACrE7H,qDAAQ,CAAC,MAAM;QACX,MAAM+H,YAAY,GAAGF,OAAO,CAACG,YAAY,GAAGH,OAAO,CAACI,YAAY;QAChE,IAAI,CAACF,YAAY,EAAE;UACf,IAAI,CAACP,OAAO,GAAGjH,6DAAyB,CAACuH,SAAS,EAAGI,KAAK,IAAKA,KAAK,CAACC,SAAS,CAACC,QAAQ,CAAC,qBAAqB,CAAC,CAAC;UAC/G,IAAI,CAACZ,OAAO,CAACa,MAAM,CAAC,IAAI,CAAC;QAC7B;MACJ,CAAC,CAAC;IACN;IACA;AACR;AACA;AACA;IACQ,IAAI,IAAI,CAAC5C,MAAM,KAAK,IAAI,EAAE;MACtBjF,uDAAG,CAAC,MAAM,IAAI,CAACQ,OAAO,CAAC,CAAC,CAAC;IAC7B;IACA;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAAC8E,cAAc,CAAC,CAAC;EACzB;EACAwC,MAAMA,CAAA,EAAG;IACL,MAAM;MAAElD,MAAM;MAAEI,cAAc;MAAEb;IAAa,CAAC,GAAG,IAAI;IACrD,MAAM4D,IAAI,GAAG3G,4DAAU,CAAC,IAAI,CAAC;IAC7B,MAAM4G,UAAU,GAAG,IAAI,CAAChE,UAAU,CAAC,CAAC;IACpC,MAAMD,YAAY,GAAGiE,UAAU,CAAC/D,IAAI,CAAE9C,CAAC,IAAKA,CAAC,CAAC0C,IAAI,KAAK,QAAQ,CAAC;IAChE,MAAMY,OAAO,GAAGuD,UAAU,CAACC,MAAM,CAAE9G,CAAC,IAAKA,CAAC,CAAC0C,IAAI,KAAK,QAAQ,CAAC;IAC7D,MAAMqE,QAAQ,GAAG,gBAAgB/D,YAAY,SAAS;IACtD,OAAQ1E,qDAAC,CAACE,iDAAI,EAAEwI,MAAM,CAACC,MAAM,CAAC;MAAEC,GAAG,EAAE,0CAA0C;MAAExE,IAAI,EAAE,QAAQ;MAAE,YAAY,EAAE,MAAM;MAAE,iBAAiB,EAAEe,MAAM,KAAKlB,SAAS,GAAGwE,QAAQ,GAAG,IAAI;MAAEI,QAAQ,EAAE;IAAK,CAAC,EAAEtD,cAAc,EAAE;MAAEuD,KAAK,EAAE;QACrNC,MAAM,EAAE,GAAG,KAAK,GAAG,IAAI,CAACrE,YAAY;MACxC,CAAC;MAAEsE,KAAK,EAAEN,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC;QAAE,CAACL,IAAI,GAAG;MAAK,CAAC,EAAE7G,qDAAW,CAAC,IAAI,CAACwD,QAAQ,CAAC,CAAC,EAAE;QAAE,gBAAgB,EAAE,IAAI;QAAE,0BAA0B,EAAE,IAAI,CAACI;MAAY,CAAC,CAAC;MAAE4D,2BAA2B,EAAE,IAAI,CAAC/E,qBAAqB;MAAEgF,gBAAgB,EAAE,IAAI,CAAClF;IAAc,CAAC,CAAC,EAAEhE,qDAAC,CAAC,cAAc,EAAE;MAAE4I,GAAG,EAAE,0CAA0C;MAAEO,QAAQ,EAAE,IAAI,CAACjE;IAAgB,CAAC,CAAC,EAAElF,qDAAC,CAAC,KAAK,EAAE;MAAE4I,GAAG,EAAE,0CAA0C;MAAEC,QAAQ,EAAE;IAAI,CAAC,CAAC,EAAE7I,qDAAC,CAAC,KAAK,EAAE;MAAE4I,GAAG,EAAE,0CAA0C;MAAEI,KAAK,EAAE,0CAA0C;MAAEI,GAAG,EAAGtD,EAAE,IAAM,IAAI,CAAC+B,SAAS,GAAG/B;IAAI,CAAC,EAAE9F,qDAAC,CAAC,KAAK,EAAE;MAAE4I,GAAG,EAAE,0CAA0C;MAAEI,KAAK,EAAE;IAAyB,CAAC,EAAEhJ,qDAAC,CAAC,KAAK,EAAE;MAAE4I,GAAG,EAAE,0CAA0C;MAAEI,KAAK,EAAE,oBAAoB;MAAEI,GAAG,EAAGtD,EAAE,IAAM,IAAI,CAAC8B,OAAO,GAAG9B;IAAI,CAAC,EAAEX,MAAM,KAAKlB,SAAS,IAAKjE,qDAAC,CAAC,KAAK,EAAE;MAAE4I,GAAG,EAAE,0CAA0C;MAAES,EAAE,EAAEZ,QAAQ;MAAEO,KAAK,EAAE;QAC/4B,oBAAoB,EAAE,IAAI;QAC1B,4BAA4B,EAAE,IAAI,CAAC5D,SAAS,KAAKnB;MACrD;IAAE,CAAC,EAAEkB,MAAM,EAAE,IAAI,CAACC,SAAS,IAAIpF,qDAAC,CAAC,KAAK,EAAE;MAAE4I,GAAG,EAAE,0CAA0C;MAAEI,KAAK,EAAE;IAAyB,CAAC,EAAE,IAAI,CAAC5D,SAAS,CAAC,CAAE,EAAEJ,OAAO,CAACmC,GAAG,CAAEzF,CAAC,IAAM1B,qDAAC,CAAC,QAAQ,EAAE0I,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEjH,CAAC,CAAC6D,cAAc,EAAE;MAAE+D,IAAI,EAAE,QAAQ;MAAED,EAAE,EAAE3H,CAAC,CAAC2H,EAAE;MAAEL,KAAK,EAAEO,WAAW,CAAC7H,CAAC,CAAC;MAAE8H,OAAO,EAAEA,CAAA,KAAM,IAAI,CAAC7C,WAAW,CAACjF,CAAC;IAAE,CAAC,CAAC,EAAE1B,qDAAC,CAAC,MAAM,EAAE;MAAEgJ,KAAK,EAAE;IAA4B,CAAC,EAAEtH,CAAC,CAAC+H,IAAI,IAAIzJ,qDAAC,CAAC,UAAU,EAAE;MAAEyJ,IAAI,EAAE/H,CAAC,CAAC+H,IAAI;MAAE,aAAa,EAAE,MAAM;MAAEC,IAAI,EAAE,KAAK;MAAEV,KAAK,EAAE;IAAoB,CAAC,CAAC,EAAEtH,CAAC,CAAC0F,IAAI,CAAC,EAAEkB,IAAI,KAAK,IAAI,IAAItI,qDAAC,CAAC,mBAAmB,EAAE,IAAI,CAAC,CAAE,CAAC,CAAC,EAAEsE,YAAY,IAAKtE,qDAAC,CAAC,KAAK,EAAE;MAAE4I,GAAG,EAAE,0CAA0C;MAAEI,KAAK,EAAE;IAA+C,CAAC,EAAEhJ,qDAAC,CAAC,QAAQ,EAAE0I,MAAM,CAACC,MAAM,CAAC;MAAEC,GAAG,EAAE;IAA2C,CAAC,EAAEtE,YAAY,CAACiB,cAAc,EAAE;MAAE+D,IAAI,EAAE,QAAQ;MAAEN,KAAK,EAAEO,WAAW,CAACjF,YAAY,CAAC;MAAEkF,OAAO,EAAEA,CAAA,KAAM,IAAI,CAAC7C,WAAW,CAACrC,YAAY;IAAE,CAAC,CAAC,EAAEtE,qDAAC,CAAC,MAAM,EAAE;MAAE4I,GAAG,EAAE,0CAA0C;MAAEI,KAAK,EAAE;IAA4B,CAAC,EAAE1E,YAAY,CAACmF,IAAI,IAAKzJ,qDAAC,CAAC,UAAU,EAAE;MAAE4I,GAAG,EAAE,0CAA0C;MAAEa,IAAI,EAAEnF,YAAY,CAACmF,IAAI;MAAE,aAAa,EAAE,MAAM;MAAEC,IAAI,EAAE,KAAK;MAAEV,KAAK,EAAE;IAAoB,CAAC,CAAE,EAAE1E,YAAY,CAAC8C,IAAI,CAAC,EAAEkB,IAAI,KAAK,IAAI,IAAItI,qDAAC,CAAC,mBAAmB,EAAE;MAAE4I,GAAG,EAAE;IAA2C,CAAC,CAAC,CAAC,CAAE,CAAC,CAAC,EAAE5I,qDAAC,CAAC,KAAK,EAAE;MAAE4I,GAAG,EAAE,0CAA0C;MAAEC,QAAQ,EAAE;IAAI,CAAC,CAAC,CAAC;EACn0C;EACA,IAAI/C,EAAEA,CAAA,EAAG;IAAE,OAAO1F,qDAAU,CAAC,IAAI,CAAC;EAAE;EACpC,WAAWuJ,QAAQA,CAAA,EAAG;IAAE,OAAO;MAC3B,QAAQ,EAAE,CAAC,gBAAgB,CAAC;MAC5B,SAAS,EAAE,CAAC,gBAAgB;IAChC,CAAC;EAAE;AACP,CAAC;AACD,MAAMJ,WAAW,GAAI3C,MAAM,IAAK;EAC5B,OAAO8B,MAAM,CAACC,MAAM,CAAC;IAAE,qBAAqB,EAAE,IAAI;IAAE,iBAAiB,EAAE,IAAI;IAAE,eAAe,EAAE,IAAI;IAAE,CAAC,gBAAgB/B,MAAM,CAACxC,IAAI,EAAE,GAAGwC,MAAM,CAACxC,IAAI,KAAKH;EAAU,CAAC,EAAExC,qDAAW,CAACmF,MAAM,CAAC3B,QAAQ,CAAC,CAAC;AACnM,CAAC;AACDhC,WAAW,CAAC6F,KAAK,GAAG;EAChBc,GAAG,EAAE9G,uBAAuB;EAC5B+G,EAAE,EAAE7G;AACR,CAAC", "sources": ["./node_modules/@ionic/core/dist/esm/ion-action-sheet.entry.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, d as createEvent, e as readTask, h, H as Host, f as getElement } from './index-a1a47f01.js';\nimport { c as createButtonActiveGesture } from './button-active-414be235.js';\nimport { r as raf } from './helpers-be245865.js';\nimport { c as createLockController } from './lock-controller-316928be.js';\nimport { d as createDelegateController, e as createTriggerController, B as BACKDROP, i as isCancel, f as present, g as dismiss, h as eventMethod, s as safeCall, j as prepareOverlay, k as setOverlayId } from './overlays-b874c3c3.js';\nimport { g as getClassMap } from './theme-01f3f29c.js';\nimport { b as getIonMode } from './ionic-global-94f25d1b.js';\nimport { c as createAnimation } from './animation-6a0c5338.js';\nimport './haptic-554688a5.js';\nimport './capacitor-59395cbd.js';\nimport './index-a5d50daf.js';\nimport './index-2cf77112.js';\nimport './gesture-controller-1bf57181.js';\nimport './hardware-back-button-6107a37c.js';\nimport './framework-delegate-ed4ba327.js';\nimport './index-9b0d46f4.js';\n\n/**\n * iOS Action Sheet Enter Animation\n */\nconst iosEnterAnimation = (baseEl) => {\n    const baseAnimation = createAnimation();\n    const backdropAnimation = createAnimation();\n    const wrapperAnimation = createAnimation();\n    backdropAnimation\n        .addElement(baseEl.querySelector('ion-backdrop'))\n        .fromTo('opacity', 0.01, 'var(--backdrop-opacity)')\n        .beforeStyles({\n        'pointer-events': 'none',\n    })\n        .afterClearStyles(['pointer-events']);\n    wrapperAnimation\n        .addElement(baseEl.querySelector('.action-sheet-wrapper'))\n        .fromTo('transform', 'translateY(100%)', 'translateY(0%)');\n    return baseAnimation\n        .addElement(baseEl)\n        .easing('cubic-bezier(.36,.66,.04,1)')\n        .duration(400)\n        .addAnimation([backdropAnimation, wrapperAnimation]);\n};\n\n/**\n * iOS Action Sheet Leave Animation\n */\nconst iosLeaveAnimation = (baseEl) => {\n    const baseAnimation = createAnimation();\n    const backdropAnimation = createAnimation();\n    const wrapperAnimation = createAnimation();\n    backdropAnimation.addElement(baseEl.querySelector('ion-backdrop')).fromTo('opacity', 'var(--backdrop-opacity)', 0);\n    wrapperAnimation\n        .addElement(baseEl.querySelector('.action-sheet-wrapper'))\n        .fromTo('transform', 'translateY(0%)', 'translateY(100%)');\n    return baseAnimation\n        .addElement(baseEl)\n        .easing('cubic-bezier(.36,.66,.04,1)')\n        .duration(450)\n        .addAnimation([backdropAnimation, wrapperAnimation]);\n};\n\n/**\n * MD Action Sheet Enter Animation\n */\nconst mdEnterAnimation = (baseEl) => {\n    const baseAnimation = createAnimation();\n    const backdropAnimation = createAnimation();\n    const wrapperAnimation = createAnimation();\n    backdropAnimation\n        .addElement(baseEl.querySelector('ion-backdrop'))\n        .fromTo('opacity', 0.01, 'var(--backdrop-opacity)')\n        .beforeStyles({\n        'pointer-events': 'none',\n    })\n        .afterClearStyles(['pointer-events']);\n    wrapperAnimation\n        .addElement(baseEl.querySelector('.action-sheet-wrapper'))\n        .fromTo('transform', 'translateY(100%)', 'translateY(0%)');\n    return baseAnimation\n        .addElement(baseEl)\n        .easing('cubic-bezier(.36,.66,.04,1)')\n        .duration(400)\n        .addAnimation([backdropAnimation, wrapperAnimation]);\n};\n\n/**\n * MD Action Sheet Leave Animation\n */\nconst mdLeaveAnimation = (baseEl) => {\n    const baseAnimation = createAnimation();\n    const backdropAnimation = createAnimation();\n    const wrapperAnimation = createAnimation();\n    backdropAnimation.addElement(baseEl.querySelector('ion-backdrop')).fromTo('opacity', 'var(--backdrop-opacity)', 0);\n    wrapperAnimation\n        .addElement(baseEl.querySelector('.action-sheet-wrapper'))\n        .fromTo('transform', 'translateY(0%)', 'translateY(100%)');\n    return baseAnimation\n        .addElement(baseEl)\n        .easing('cubic-bezier(.36,.66,.04,1)')\n        .duration(450)\n        .addAnimation([backdropAnimation, wrapperAnimation]);\n};\n\nconst actionSheetIosCss = \".sc-ion-action-sheet-ios-h{--color:initial;--button-color-activated:var(--button-color);--button-color-focused:var(--button-color);--button-color-hover:var(--button-color);--button-color-selected:var(--button-color);--min-width:auto;--width:100%;--max-width:500px;--min-height:auto;--height:auto;--max-height:calc(100% - (var(--ion-safe-area-top) + var(--ion-safe-area-bottom)));-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;left:0;right:0;top:0;bottom:0;display:block;position:fixed;outline:none;font-family:var(--ion-font-family, inherit);-ms-touch-action:none;touch-action:none;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;z-index:1001}.overlay-hidden.sc-ion-action-sheet-ios-h{display:none}.action-sheet-wrapper.sc-ion-action-sheet-ios{left:0;right:0;bottom:0;-webkit-transform:translate3d(0,  100%,  0);transform:translate3d(0,  100%,  0);display:block;position:absolute;width:var(--width);min-width:var(--min-width);max-width:var(--max-width);height:var(--height);min-height:var(--min-height);max-height:var(--max-height);z-index:10;pointer-events:none}.action-sheet-button.sc-ion-action-sheet-ios{display:block;position:relative;width:100%;border:0;outline:none;background:var(--button-background);color:var(--button-color);font-family:inherit;overflow:hidden}.action-sheet-button-inner.sc-ion-action-sheet-ios{display:-ms-flexbox;display:flex;position:relative;-ms-flex-flow:row nowrap;flex-flow:row nowrap;-ms-flex-negative:0;flex-shrink:0;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;pointer-events:none;width:100%;height:100%;z-index:1}.action-sheet-container.sc-ion-action-sheet-ios{display:-ms-flexbox;display:flex;-ms-flex-flow:column;flex-flow:column;-ms-flex-pack:end;justify-content:flex-end;height:100%;max-height:calc(100vh - (var(--ion-safe-area-top, 0) + var(--ion-safe-area-bottom, 0)));max-height:calc(100dvh - (var(--ion-safe-area-top, 0) + var(--ion-safe-area-bottom, 0)))}.action-sheet-group.sc-ion-action-sheet-ios{-ms-flex-negative:2;flex-shrink:2;overscroll-behavior-y:contain;overflow-y:auto;-webkit-overflow-scrolling:touch;pointer-events:all;background:var(--background)}@media (any-pointer: coarse){.action-sheet-group.sc-ion-action-sheet-ios::-webkit-scrollbar{display:none}}.action-sheet-group-cancel.sc-ion-action-sheet-ios{-ms-flex-negative:0;flex-shrink:0;overflow:hidden}.action-sheet-button.sc-ion-action-sheet-ios::after{left:0;right:0;top:0;bottom:0;position:absolute;content:\\\"\\\";opacity:0}.action-sheet-selected.sc-ion-action-sheet-ios{color:var(--button-color-selected)}.action-sheet-selected.sc-ion-action-sheet-ios::after{background:var(--button-background-selected);opacity:var(--button-background-selected-opacity)}.action-sheet-button.ion-activated.sc-ion-action-sheet-ios{color:var(--button-color-activated)}.action-sheet-button.ion-activated.sc-ion-action-sheet-ios::after{background:var(--button-background-activated);opacity:var(--button-background-activated-opacity)}.action-sheet-button.ion-focused.sc-ion-action-sheet-ios{color:var(--button-color-focused)}.action-sheet-button.ion-focused.sc-ion-action-sheet-ios::after{background:var(--button-background-focused);opacity:var(--button-background-focused-opacity)}@media (any-hover: hover){.action-sheet-button.sc-ion-action-sheet-ios:hover{color:var(--button-color-hover)}.action-sheet-button.sc-ion-action-sheet-ios:hover::after{background:var(--button-background-hover);opacity:var(--button-background-hover-opacity)}}.sc-ion-action-sheet-ios-h{--background:var(--ion-overlay-background-color, var(--ion-color-step-100, #f9f9f9));--backdrop-opacity:var(--ion-backdrop-opacity, 0.4);--button-background:linear-gradient(0deg, rgba(var(--ion-text-color-rgb, 0, 0, 0), 0.08), rgba(var(--ion-text-color-rgb, 0, 0, 0), 0.08) 50%, transparent 50%) bottom/100% 1px no-repeat transparent;--button-background-activated:var(--ion-text-color, #000);--button-background-activated-opacity:.08;--button-background-hover:currentColor;--button-background-hover-opacity:.04;--button-background-focused:currentColor;--button-background-focused-opacity:.12;--button-background-selected:var(--ion-color-step-150, var(--ion-background-color, #fff));--button-background-selected-opacity:1;--button-color:var(--ion-color-primary, #3880ff);--color:var(--ion-color-step-400, #999999);text-align:center}.action-sheet-wrapper.sc-ion-action-sheet-ios{-webkit-margin-start:auto;margin-inline-start:auto;-webkit-margin-end:auto;margin-inline-end:auto;margin-top:var(--ion-safe-area-top, 0);padding-bottom:var(--ion-safe-area-bottom, 0);-webkit-box-sizing:content-box;box-sizing:content-box}.action-sheet-container.sc-ion-action-sheet-ios{-webkit-padding-start:8px;padding-inline-start:8px;-webkit-padding-end:8px;padding-inline-end:8px;padding-top:0;padding-bottom:0}.action-sheet-group.sc-ion-action-sheet-ios{border-radius:13px;margin-bottom:8px}.action-sheet-group.sc-ion-action-sheet-ios:first-child{margin-top:10px}.action-sheet-group.sc-ion-action-sheet-ios:last-child{margin-bottom:10px}@supports ((-webkit-backdrop-filter: blur(0)) or (backdrop-filter: blur(0))){.action-sheet-translucent.sc-ion-action-sheet-ios-h .action-sheet-group.sc-ion-action-sheet-ios{background-color:transparent;-webkit-backdrop-filter:saturate(280%) blur(20px);backdrop-filter:saturate(280%) blur(20px)}.action-sheet-translucent.sc-ion-action-sheet-ios-h .action-sheet-title.sc-ion-action-sheet-ios,.action-sheet-translucent.sc-ion-action-sheet-ios-h .action-sheet-button.sc-ion-action-sheet-ios{background-color:transparent;background-image:-webkit-gradient(linear, left bottom, left top, from(rgba(var(--ion-background-color-rgb, 255, 255, 255), 0.8)), to(rgba(var(--ion-background-color-rgb, 255, 255, 255), 0.8))), -webkit-gradient(linear, left bottom, left top, from(rgba(var(--ion-background-color-rgb, 255, 255, 255), 0.4)), color-stop(50%, rgba(var(--ion-background-color-rgb, 255, 255, 255), 0.4)), color-stop(50%, rgba(var(--ion-background-color-rgb, 255, 255, 255), 0.8)));background-image:linear-gradient(0deg, rgba(var(--ion-background-color-rgb, 255, 255, 255), 0.8), rgba(var(--ion-background-color-rgb, 255, 255, 255), 0.8) 100%), linear-gradient(0deg, rgba(var(--ion-background-color-rgb, 255, 255, 255), 0.4), rgba(var(--ion-background-color-rgb, 255, 255, 255), 0.4) 50%, rgba(var(--ion-background-color-rgb, 255, 255, 255), 0.8) 50%);background-repeat:no-repeat;background-position:top, bottom;background-size:100% calc(100% - 1px), 100% 1px;-webkit-backdrop-filter:saturate(120%);backdrop-filter:saturate(120%)}.action-sheet-translucent.sc-ion-action-sheet-ios-h .action-sheet-button.ion-activated.sc-ion-action-sheet-ios{background-color:rgba(var(--ion-background-color-rgb, 255, 255, 255), 0.7);background-image:none}.action-sheet-translucent.sc-ion-action-sheet-ios-h .action-sheet-cancel.sc-ion-action-sheet-ios{background:var(--button-background-selected)}}.action-sheet-title.sc-ion-action-sheet-ios{background:-webkit-gradient(linear, left bottom, left top, from(rgba(var(--ion-text-color-rgb, 0, 0, 0), 0.08)), color-stop(50%, rgba(var(--ion-text-color-rgb, 0, 0, 0), 0.08)), color-stop(50%, transparent)) bottom/100% 1px no-repeat transparent;background:linear-gradient(0deg, rgba(var(--ion-text-color-rgb, 0, 0, 0), 0.08), rgba(var(--ion-text-color-rgb, 0, 0, 0), 0.08) 50%, transparent 50%) bottom/100% 1px no-repeat transparent}.action-sheet-title.sc-ion-action-sheet-ios{-webkit-padding-start:10px;padding-inline-start:10px;-webkit-padding-end:10px;padding-inline-end:10px;padding-top:14px;padding-bottom:13px;color:var(--color, var(--ion-color-step-400, #999999));font-size:max(13px, 0.8125rem);font-weight:400;text-align:center}.action-sheet-title.action-sheet-has-sub-title.sc-ion-action-sheet-ios{font-weight:600}.action-sheet-sub-title.sc-ion-action-sheet-ios{padding-left:0;padding-right:0;padding-top:6px;padding-bottom:0;font-size:max(13px, 0.8125rem);font-weight:400}.action-sheet-button.sc-ion-action-sheet-ios{-webkit-padding-start:14px;padding-inline-start:14px;-webkit-padding-end:14px;padding-inline-end:14px;padding-top:14px;padding-bottom:14px;min-height:56px;font-size:max(20px, 1.25rem);contain:content}.action-sheet-button.sc-ion-action-sheet-ios .action-sheet-icon.sc-ion-action-sheet-ios{-webkit-margin-end:0.3em;margin-inline-end:0.3em;font-size:max(28px, 1.75rem);pointer-events:none}.action-sheet-button.sc-ion-action-sheet-ios:last-child{background-image:none}.action-sheet-selected.sc-ion-action-sheet-ios{font-weight:bold}.action-sheet-cancel.sc-ion-action-sheet-ios{font-weight:600}.action-sheet-cancel.sc-ion-action-sheet-ios::after{background:var(--button-background-selected);opacity:var(--button-background-selected-opacity)}.action-sheet-destructive.sc-ion-action-sheet-ios,.action-sheet-destructive.ion-activated.sc-ion-action-sheet-ios,.action-sheet-destructive.ion-focused.sc-ion-action-sheet-ios{color:var(--ion-color-danger, #eb445a)}@media (any-hover: hover){.action-sheet-destructive.sc-ion-action-sheet-ios:hover{color:var(--ion-color-danger, #eb445a)}}\";\nconst IonActionSheetIosStyle0 = actionSheetIosCss;\n\nconst actionSheetMdCss = \".sc-ion-action-sheet-md-h{--color:initial;--button-color-activated:var(--button-color);--button-color-focused:var(--button-color);--button-color-hover:var(--button-color);--button-color-selected:var(--button-color);--min-width:auto;--width:100%;--max-width:500px;--min-height:auto;--height:auto;--max-height:calc(100% - (var(--ion-safe-area-top) + var(--ion-safe-area-bottom)));-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;left:0;right:0;top:0;bottom:0;display:block;position:fixed;outline:none;font-family:var(--ion-font-family, inherit);-ms-touch-action:none;touch-action:none;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;z-index:1001}.overlay-hidden.sc-ion-action-sheet-md-h{display:none}.action-sheet-wrapper.sc-ion-action-sheet-md{left:0;right:0;bottom:0;-webkit-transform:translate3d(0,  100%,  0);transform:translate3d(0,  100%,  0);display:block;position:absolute;width:var(--width);min-width:var(--min-width);max-width:var(--max-width);height:var(--height);min-height:var(--min-height);max-height:var(--max-height);z-index:10;pointer-events:none}.action-sheet-button.sc-ion-action-sheet-md{display:block;position:relative;width:100%;border:0;outline:none;background:var(--button-background);color:var(--button-color);font-family:inherit;overflow:hidden}.action-sheet-button-inner.sc-ion-action-sheet-md{display:-ms-flexbox;display:flex;position:relative;-ms-flex-flow:row nowrap;flex-flow:row nowrap;-ms-flex-negative:0;flex-shrink:0;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;pointer-events:none;width:100%;height:100%;z-index:1}.action-sheet-container.sc-ion-action-sheet-md{display:-ms-flexbox;display:flex;-ms-flex-flow:column;flex-flow:column;-ms-flex-pack:end;justify-content:flex-end;height:100%;max-height:calc(100vh - (var(--ion-safe-area-top, 0) + var(--ion-safe-area-bottom, 0)));max-height:calc(100dvh - (var(--ion-safe-area-top, 0) + var(--ion-safe-area-bottom, 0)))}.action-sheet-group.sc-ion-action-sheet-md{-ms-flex-negative:2;flex-shrink:2;overscroll-behavior-y:contain;overflow-y:auto;-webkit-overflow-scrolling:touch;pointer-events:all;background:var(--background)}@media (any-pointer: coarse){.action-sheet-group.sc-ion-action-sheet-md::-webkit-scrollbar{display:none}}.action-sheet-group-cancel.sc-ion-action-sheet-md{-ms-flex-negative:0;flex-shrink:0;overflow:hidden}.action-sheet-button.sc-ion-action-sheet-md::after{left:0;right:0;top:0;bottom:0;position:absolute;content:\\\"\\\";opacity:0}.action-sheet-selected.sc-ion-action-sheet-md{color:var(--button-color-selected)}.action-sheet-selected.sc-ion-action-sheet-md::after{background:var(--button-background-selected);opacity:var(--button-background-selected-opacity)}.action-sheet-button.ion-activated.sc-ion-action-sheet-md{color:var(--button-color-activated)}.action-sheet-button.ion-activated.sc-ion-action-sheet-md::after{background:var(--button-background-activated);opacity:var(--button-background-activated-opacity)}.action-sheet-button.ion-focused.sc-ion-action-sheet-md{color:var(--button-color-focused)}.action-sheet-button.ion-focused.sc-ion-action-sheet-md::after{background:var(--button-background-focused);opacity:var(--button-background-focused-opacity)}@media (any-hover: hover){.action-sheet-button.sc-ion-action-sheet-md:hover{color:var(--button-color-hover)}.action-sheet-button.sc-ion-action-sheet-md:hover::after{background:var(--button-background-hover);opacity:var(--button-background-hover-opacity)}}.sc-ion-action-sheet-md-h{--background:var(--ion-overlay-background-color, var(--ion-background-color, #fff));--backdrop-opacity:var(--ion-backdrop-opacity, 0.32);--button-background:transparent;--button-background-selected:currentColor;--button-background-selected-opacity:0;--button-background-activated:transparent;--button-background-activated-opacity:0;--button-background-hover:currentColor;--button-background-hover-opacity:.04;--button-background-focused:currentColor;--button-background-focused-opacity:.12;--button-color:var(--ion-color-step-850, #262626);--color:rgba(var(--ion-text-color-rgb, 0, 0, 0), 0.54)}.action-sheet-wrapper.sc-ion-action-sheet-md{-webkit-margin-start:auto;margin-inline-start:auto;-webkit-margin-end:auto;margin-inline-end:auto;margin-top:var(--ion-safe-area-top, 0);margin-bottom:0}.action-sheet-title.sc-ion-action-sheet-md{-webkit-padding-start:16px;padding-inline-start:16px;-webkit-padding-end:16px;padding-inline-end:16px;padding-top:20px;padding-bottom:17px;min-height:60px;color:var(--color, rgba(var(--ion-text-color-rgb, 0, 0, 0), 0.54));font-size:1rem;text-align:start}.action-sheet-sub-title.sc-ion-action-sheet-md{padding-left:0;padding-right:0;padding-top:16px;padding-bottom:0;font-size:0.875rem}.action-sheet-group.sc-ion-action-sheet-md:first-child{padding-top:0}.action-sheet-group.sc-ion-action-sheet-md:last-child{padding-bottom:var(--ion-safe-area-bottom)}.action-sheet-button.sc-ion-action-sheet-md{-webkit-padding-start:16px;padding-inline-start:16px;-webkit-padding-end:16px;padding-inline-end:16px;padding-top:12px;padding-bottom:12px;position:relative;min-height:52px;font-size:1rem;text-align:start;contain:content;overflow:hidden}.action-sheet-icon.sc-ion-action-sheet-md{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:32px;margin-inline-end:32px;margin-top:0;margin-bottom:0;color:var(--color);font-size:1.5rem}.action-sheet-button-inner.sc-ion-action-sheet-md{-ms-flex-pack:start;justify-content:flex-start}.action-sheet-selected.sc-ion-action-sheet-md{font-weight:bold}\";\nconst IonActionSheetMdStyle0 = actionSheetMdCss;\n\nconst ActionSheet = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.didPresent = createEvent(this, \"ionActionSheetDidPresent\", 7);\n        this.willPresent = createEvent(this, \"ionActionSheetWillPresent\", 7);\n        this.willDismiss = createEvent(this, \"ionActionSheetWillDismiss\", 7);\n        this.didDismiss = createEvent(this, \"ionActionSheetDidDismiss\", 7);\n        this.didPresentShorthand = createEvent(this, \"didPresent\", 7);\n        this.willPresentShorthand = createEvent(this, \"willPresent\", 7);\n        this.willDismissShorthand = createEvent(this, \"willDismiss\", 7);\n        this.didDismissShorthand = createEvent(this, \"didDismiss\", 7);\n        this.delegateController = createDelegateController(this);\n        this.lockController = createLockController();\n        this.triggerController = createTriggerController();\n        this.presented = false;\n        this.onBackdropTap = () => {\n            this.dismiss(undefined, BACKDROP);\n        };\n        this.dispatchCancelHandler = (ev) => {\n            const role = ev.detail.role;\n            if (isCancel(role)) {\n                const cancelButton = this.getButtons().find((b) => b.role === 'cancel');\n                this.callButtonHandler(cancelButton);\n            }\n        };\n        this.overlayIndex = undefined;\n        this.delegate = undefined;\n        this.hasController = false;\n        this.keyboardClose = true;\n        this.enterAnimation = undefined;\n        this.leaveAnimation = undefined;\n        this.buttons = [];\n        this.cssClass = undefined;\n        this.backdropDismiss = true;\n        this.header = undefined;\n        this.subHeader = undefined;\n        this.translucent = false;\n        this.animated = true;\n        this.htmlAttributes = undefined;\n        this.isOpen = false;\n        this.trigger = undefined;\n    }\n    onIsOpenChange(newValue, oldValue) {\n        if (newValue === true && oldValue === false) {\n            this.present();\n        }\n        else if (newValue === false && oldValue === true) {\n            this.dismiss();\n        }\n    }\n    triggerChanged() {\n        const { trigger, el, triggerController } = this;\n        if (trigger) {\n            triggerController.addClickListener(el, trigger);\n        }\n    }\n    /**\n     * Present the action sheet overlay after it has been created.\n     */\n    async present() {\n        const unlock = await this.lockController.lock();\n        await this.delegateController.attachViewToDom();\n        await present(this, 'actionSheetEnter', iosEnterAnimation, mdEnterAnimation);\n        unlock();\n    }\n    /**\n     * Dismiss the action sheet overlay after it has been presented.\n     *\n     * @param data Any data to emit in the dismiss events.\n     * @param role The role of the element that is dismissing the action sheet.\n     * This can be useful in a button handler for determining which button was\n     * clicked to dismiss the action sheet.\n     * Some examples include: ``\"cancel\"`, `\"destructive\"`, \"selected\"`, and `\"backdrop\"`.\n     *\n     * This is a no-op if the overlay has not been presented yet. If you want\n     * to remove an overlay from the DOM that was never presented, use the\n     * [remove](https://developer.mozilla.org/en-US/docs/Web/API/Element/remove) method.\n     */\n    async dismiss(data, role) {\n        const unlock = await this.lockController.lock();\n        const dismissed = await dismiss(this, data, role, 'actionSheetLeave', iosLeaveAnimation, mdLeaveAnimation);\n        if (dismissed) {\n            this.delegateController.removeViewFromDom();\n        }\n        unlock();\n        return dismissed;\n    }\n    /**\n     * Returns a promise that resolves when the action sheet did dismiss.\n     */\n    onDidDismiss() {\n        return eventMethod(this.el, 'ionActionSheetDidDismiss');\n    }\n    /**\n     * Returns a promise that resolves when the action sheet will dismiss.\n     *\n     */\n    onWillDismiss() {\n        return eventMethod(this.el, 'ionActionSheetWillDismiss');\n    }\n    async buttonClick(button) {\n        const role = button.role;\n        if (isCancel(role)) {\n            return this.dismiss(button.data, role);\n        }\n        const shouldDismiss = await this.callButtonHandler(button);\n        if (shouldDismiss) {\n            return this.dismiss(button.data, button.role);\n        }\n        return Promise.resolve();\n    }\n    async callButtonHandler(button) {\n        if (button) {\n            // a handler has been provided, execute it\n            // pass the handler the values from the inputs\n            const rtn = await safeCall(button.handler);\n            if (rtn === false) {\n                // if the return value of the handler is false then do not dismiss\n                return false;\n            }\n        }\n        return true;\n    }\n    getButtons() {\n        return this.buttons.map((b) => {\n            return typeof b === 'string' ? { text: b } : b;\n        });\n    }\n    connectedCallback() {\n        prepareOverlay(this.el);\n        this.triggerChanged();\n    }\n    disconnectedCallback() {\n        if (this.gesture) {\n            this.gesture.destroy();\n            this.gesture = undefined;\n        }\n        this.triggerController.removeClickListener();\n    }\n    componentWillLoad() {\n        setOverlayId(this.el);\n    }\n    componentDidLoad() {\n        /**\n         * Only create gesture if:\n         * 1. A gesture does not already exist\n         * 2. App is running in iOS mode\n         * 3. A wrapper ref exists\n         * 4. A group ref exists\n         */\n        const { groupEl, wrapperEl } = this;\n        if (!this.gesture && getIonMode(this) === 'ios' && wrapperEl && groupEl) {\n            readTask(() => {\n                const isScrollable = groupEl.scrollHeight > groupEl.clientHeight;\n                if (!isScrollable) {\n                    this.gesture = createButtonActiveGesture(wrapperEl, (refEl) => refEl.classList.contains('action-sheet-button'));\n                    this.gesture.enable(true);\n                }\n            });\n        }\n        /**\n         * If action sheet was rendered with isOpen=\"true\"\n         * then we should open action sheet immediately.\n         */\n        if (this.isOpen === true) {\n            raf(() => this.present());\n        }\n        /**\n         * When binding values in frameworks such as Angular\n         * it is possible for the value to be set after the Web Component\n         * initializes but before the value watcher is set up in Stencil.\n         * As a result, the watcher callback may not be fired.\n         * We work around this by manually calling the watcher\n         * callback when the component has loaded and the watcher\n         * is configured.\n         */\n        this.triggerChanged();\n    }\n    render() {\n        const { header, htmlAttributes, overlayIndex } = this;\n        const mode = getIonMode(this);\n        const allButtons = this.getButtons();\n        const cancelButton = allButtons.find((b) => b.role === 'cancel');\n        const buttons = allButtons.filter((b) => b.role !== 'cancel');\n        const headerID = `action-sheet-${overlayIndex}-header`;\n        return (h(Host, Object.assign({ key: '49c8b5b3412b5688e44f3e3fa18abcc01c75a770', role: \"dialog\", \"aria-modal\": \"true\", \"aria-labelledby\": header !== undefined ? headerID : null, tabindex: \"-1\" }, htmlAttributes, { style: {\n                zIndex: `${20000 + this.overlayIndex}`,\n            }, class: Object.assign(Object.assign({ [mode]: true }, getClassMap(this.cssClass)), { 'overlay-hidden': true, 'action-sheet-translucent': this.translucent }), onIonActionSheetWillDismiss: this.dispatchCancelHandler, onIonBackdropTap: this.onBackdropTap }), h(\"ion-backdrop\", { key: '80b4c279fca194c6d65bbdb8128956641387bb05', tappable: this.backdropDismiss }), h(\"div\", { key: '245cde1873c07ef09267de8ab1a4d6ee51c0a83c', tabindex: \"0\" }), h(\"div\", { key: '045109bb2118decbe633f45aa3d71b824d37c0fd', class: \"action-sheet-wrapper ion-overlay-wrapper\", ref: (el) => (this.wrapperEl = el) }, h(\"div\", { key: 'b053f3a177b6ac7f2f76f5470f7023389f06cfd8', class: \"action-sheet-container\" }, h(\"div\", { key: '88287aa180c22389747c9fec702112e29f4ec039', class: \"action-sheet-group\", ref: (el) => (this.groupEl = el) }, header !== undefined && (h(\"div\", { key: '693e67af994a0018508a6deb867937916913eaa6', id: headerID, class: {\n                'action-sheet-title': true,\n                'action-sheet-has-sub-title': this.subHeader !== undefined,\n            } }, header, this.subHeader && h(\"div\", { key: '813cbb8d66e46d5a55a6c8bf52c5689882dc7002', class: \"action-sheet-sub-title\" }, this.subHeader))), buttons.map((b) => (h(\"button\", Object.assign({}, b.htmlAttributes, { type: \"button\", id: b.id, class: buttonClass(b), onClick: () => this.buttonClick(b) }), h(\"span\", { class: \"action-sheet-button-inner\" }, b.icon && h(\"ion-icon\", { icon: b.icon, \"aria-hidden\": \"true\", lazy: false, class: \"action-sheet-icon\" }), b.text), mode === 'md' && h(\"ion-ripple-effect\", null))))), cancelButton && (h(\"div\", { key: 'f99cd10e7d91d3014edac6109c3e6dc128737f7c', class: \"action-sheet-group action-sheet-group-cancel\" }, h(\"button\", Object.assign({ key: '595c6a39ba04185e80cc3b0705536f93b4f1ebf4' }, cancelButton.htmlAttributes, { type: \"button\", class: buttonClass(cancelButton), onClick: () => this.buttonClick(cancelButton) }), h(\"span\", { key: '1f40403b907c6e925405a8b405ede9f7f9885611', class: \"action-sheet-button-inner\" }, cancelButton.icon && (h(\"ion-icon\", { key: '75d5398d889fa70b514843b9cc73b2087a0bf1a0', icon: cancelButton.icon, \"aria-hidden\": \"true\", lazy: false, class: \"action-sheet-icon\" })), cancelButton.text), mode === 'md' && h(\"ion-ripple-effect\", { key: 'cda40def00755c69da9f6a67494eee4dc79550fc' })))))), h(\"div\", { key: '4d9432bae550ef618ba762857144f1558e3e29e7', tabindex: \"0\" })));\n    }\n    get el() { return getElement(this); }\n    static get watchers() { return {\n        \"isOpen\": [\"onIsOpenChange\"],\n        \"trigger\": [\"triggerChanged\"]\n    }; }\n};\nconst buttonClass = (button) => {\n    return Object.assign({ 'action-sheet-button': true, 'ion-activatable': true, 'ion-focusable': true, [`action-sheet-${button.role}`]: button.role !== undefined }, getClassMap(button.cssClass));\n};\nActionSheet.style = {\n    ios: IonActionSheetIosStyle0,\n    md: IonActionSheetMdStyle0\n};\n\nexport { ActionSheet as ion_action_sheet };\n"], "names": ["r", "registerInstance", "d", "createEvent", "e", "readTask", "h", "H", "Host", "f", "getElement", "c", "createButtonActiveGesture", "raf", "createLockController", "createDelegateController", "createTriggerController", "B", "BACKDROP", "i", "isCancel", "present", "g", "dismiss", "eventMethod", "s", "safeCall", "j", "prepareOverlay", "k", "setOverlayId", "getClassMap", "b", "getIonMode", "createAnimation", "iosEnterAnimation", "baseEl", "baseAnimation", "backdropAnimation", "wrapperAnimation", "addElement", "querySelector", "fromTo", "beforeStyles", "afterClearStyles", "easing", "duration", "addAnimation", "iosLeaveAnimation", "mdEnterAnimation", "mdLeaveAnimation", "actionSheetIosCss", "IonActionSheetIosStyle0", "actionSheetMdCss", "IonActionSheetMdStyle0", "ActionSheet", "constructor", "hostRef", "didPresent", "willPresent", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "didPresentShorthand", "willPresentShorthand", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "delegate<PERSON><PERSON>roller", "lockController", "triggerController", "presented", "onBackdropTap", "undefined", "dispatchCancelHandler", "ev", "role", "detail", "cancelButton", "getButtons", "find", "callButtonHandler", "overlayIndex", "delegate", "hasController", "keyboardClose", "enterAnimation", "leaveAnimation", "buttons", "cssClass", "<PERSON><PERSON><PERSON><PERSON>", "header", "subHeader", "translucent", "animated", "htmlAttributes", "isOpen", "trigger", "onIsOpenChange", "newValue", "oldValue", "triggerChanged", "el", "addClickListener", "_this", "_asyncToGenerator", "unlock", "lock", "attachViewToDom", "data", "_this2", "dismissed", "removeViewFromDom", "onDid<PERSON><PERSON><PERSON>", "on<PERSON>ill<PERSON><PERSON>iss", "buttonClick", "button", "_this3", "<PERSON><PERSON><PERSON><PERSON>", "Promise", "resolve", "rtn", "handler", "map", "text", "connectedCallback", "disconnectedCallback", "gesture", "destroy", "removeClickListener", "componentWillLoad", "componentDidLoad", "groupEl", "wrapperEl", "isScrollable", "scrollHeight", "clientHeight", "refEl", "classList", "contains", "enable", "render", "mode", "allButtons", "filter", "headerID", "Object", "assign", "key", "tabindex", "style", "zIndex", "class", "onIonActionSheetWillDismiss", "onIonBackdropTap", "tappable", "ref", "id", "type", "buttonClass", "onClick", "icon", "lazy", "watchers", "ios", "md", "ion_action_sheet"], "sourceRoot": "webpack:///", "x_google_ignoreList": [0]}