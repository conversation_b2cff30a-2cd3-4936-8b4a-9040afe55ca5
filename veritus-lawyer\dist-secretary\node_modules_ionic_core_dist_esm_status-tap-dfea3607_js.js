"use strict";
(self["webpackChunkveritus_secretary"] = self["webpackChunkveritus_secretary"] || []).push([["node_modules_ionic_core_dist_esm_status-tap-dfea3607_js"],{

/***/ 3441:
/*!******************************************************************!*\
  !*** ./node_modules/@ionic/core/dist/esm/status-tap-dfea3607.js ***!
  \******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   startStatusTap: () => (/* binding */ startStatusTap)
/* harmony export */ });
/* harmony import */ var C_Users_acer_Desktop_angularprojects_veritus_Veritus_veritus_secretary_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js */ 9204);
/* harmony import */ var _index_a1a47f01_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./index-a1a47f01.js */ 2856);
/* harmony import */ var _index_f3946ac1_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./index-f3946ac1.js */ 6734);
/* harmony import */ var _helpers_be245865_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./helpers-be245865.js */ 2641);
/* harmony import */ var _index_9b0d46f4_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./index-9b0d46f4.js */ 8974);

/*!
 * (C) Ionic http://ionicframework.com - MIT License
 */




const startStatusTap = () => {
  const win = window;
  win.addEventListener('statusTap', () => {
    (0,_index_a1a47f01_js__WEBPACK_IMPORTED_MODULE_1__.e)(() => {
      const width = win.innerWidth;
      const height = win.innerHeight;
      const el = document.elementFromPoint(width / 2, height / 2);
      if (!el) {
        return;
      }
      const contentEl = (0,_index_f3946ac1_js__WEBPACK_IMPORTED_MODULE_2__.f)(el);
      if (contentEl) {
        new Promise(resolve => (0,_helpers_be245865_js__WEBPACK_IMPORTED_MODULE_3__.c)(contentEl, resolve)).then(() => {
          (0,_index_a1a47f01_js__WEBPACK_IMPORTED_MODULE_1__.w)(/*#__PURE__*/(0,C_Users_acer_Desktop_angularprojects_veritus_Veritus_veritus_secretary_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__["default"])(function* () {
            /**
             * If scrolling and user taps status bar,
             * only calling scrollToTop is not enough
             * as engines like WebKit will jump the
             * scroll position back down and complete
             * any in-progress momentum scrolling.
             */
            contentEl.style.setProperty('--overflow', 'hidden');
            yield (0,_index_f3946ac1_js__WEBPACK_IMPORTED_MODULE_2__.s)(contentEl, 300);
            contentEl.style.removeProperty('--overflow');
          }));
        });
      }
    });
  });
};


/***/ })

}]);
//# sourceMappingURL=node_modules_ionic_core_dist_esm_status-tap-dfea3607_js.js.map