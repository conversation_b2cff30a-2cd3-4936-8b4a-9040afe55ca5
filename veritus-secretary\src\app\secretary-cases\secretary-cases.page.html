<ion-content class="cases-content">
  <div class="cases-container">

    <!-- Filters -->
    <div class="filters-section">
      <div class="filter-row">
        <div class="filter-item">
          <ion-label class="filter-label">Lawyer</ion-label>
          <ion-select
            [(ngModel)]="selectedLawyer"
            (ionChange)="onLawyerChange()"
            interface="popover"
            class="lawyer-select">
            <ion-select-option value="all">All Lawyers</ion-select-option>
            <ion-select-option *ngFor="let lawyer of linkedLawyers" [value]="lawyer.uid">
              {{ lawyer.name }}
            </ion-select-option>
          </ion-select>
        </div>

        <div class="filter-item">
          <ion-label class="filter-label">Status</ion-label>
          <ion-select
            [(ngModel)]="selectedStatus"
            (ionChange)="onStatusChange()"
            interface="popover"
            class="status-select">
            <ion-select-option value="all">All Status</ion-select-option>
            <ion-select-option value="ongoing">Ongoing</ion-select-option>
            <ion-select-option value="pending">Pending</ion-select-option>
            <ion-select-option value="closed">Closed</ion-select-option>
          </ion-select>
        </div>
      </div>

      <div class="search-section">
        <ion-searchbar
          [(ngModel)]="searchTerm"
          (ionInput)="onSearchChange()"
          placeholder="Search cases..."
          class="custom-searchbar">
        </ion-searchbar>
      </div>
    </div>

    <!-- Create Case Button -->
    <div class="action-section">
      <button class="create-btn" (click)="onCreateCase()">
        <ion-icon name="add" class="btn-icon"></ion-icon>
        Create Case
      </button>
    </div>

    <!-- Cases List -->
    <div class="cases-section">
      <h2 class="section-title">
        Cases ({{ filteredCases.length }})
      </h2>

      <div class="cases-list" *ngIf="filteredCases.length > 0">
        <div class="case-card" *ngFor="let case of filteredCases">
          <div class="case-status" [class]="'status-' + case.status">
            <ion-icon [name]="getStatusIcon(case.status)"></ion-icon>
          </div>

          <div class="case-details">
            <h3 class="case-title">
              {{ case.title }}
            </h3>
            <p class="case-client">{{ case.clientName }}</p>
            <p class="case-description">{{ case.description }}</p>
            <p class="case-lawyer">{{ case.lawyerName }}</p>

            <div class="case-meta">
              <span class="case-files">
                <ion-icon name="document-outline"></ion-icon>
                {{ case.fileCount }} files
              </span>
              <span class="case-date">
                <ion-icon name="time-outline"></ion-icon>
                Updated {{ case.updatedAt | date:'short' }}
              </span>
            </div>
          </div>

          <div class="case-actions">
            <button class="action-btn edit-btn" (click)="onUpdateCaseProgress(case)" title="Edit Case">
              <ion-icon name="create-outline"></ion-icon>
            </button>
            <button class="action-btn view-btn" (click)="onViewCase(case)" title="View Case">
              <ion-icon name="eye-outline"></ion-icon>
            </button>
          </div>
        </div>
      </div>

      <!-- Empty State -->
      <div class="empty-state" *ngIf="filteredCases.length === 0">
        <ion-icon name="briefcase-outline" class="empty-icon"></ion-icon>
        <h3 class="empty-title">No Cases Found</h3>
        <p class="empty-description">
          No cases match your current filters. Try adjusting your search criteria or create a new case.
        </p>
        <button class="create-btn" (click)="onCreateCase()">
          <ion-icon name="add" class="btn-icon"></ion-icon>
          Create First Case
        </button>
      </div>
    </div>

    <!-- Quick Stats -->
    <div class="stats-section">
      <div class="stat-card stat-ongoing">
        <div class="stat-number">
          {{ getOngoingCount() }}
        </div>
        <div class="stat-label">Ongoing Cases</div>
      </div>

      <div class="stat-card stat-pending">
        <div class="stat-number">
          {{ getPendingCount() }}
        </div>
        <div class="stat-label">Pending Cases</div>
      </div>

      <div class="stat-card stat-closed">
        <div class="stat-number">
          {{ getClosedCount() }}
        </div>
        <div class="stat-label">Closed Cases</div>
      </div>
    </div>

  </div>
</ion-content>
