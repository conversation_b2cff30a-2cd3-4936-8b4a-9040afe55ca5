.dashboard-container {
  display: flex;
  gap: 24px;
  padding: 24px;
  background: #f8f9fa;
  min-height: 100vh;
}

.left-column {
  flex: 1;
  max-width: 400px;
}

.right-column {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.scheduling-section,
.files-section,
.finance-section {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

h2 {
  font-size: 24px;
  font-weight: 600;
  margin: 0 0 20px 0;
  color: #333;
}

h3, h4 {
  font-size: 16px;
  font-weight: 600;
  margin: 0 0 12px 0;
  color: #333;
}

.calendar-widget {
  margin-bottom: 24px;
}

.calendar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.nav-btn {
  background: none;
  border: none;
  font-size: 20px;
  cursor: pointer;
  padding: 8px;
  border-radius: 4px;
}

.nav-btn:hover {
  background: #f0f0f0;
}

.calendar-grid {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 1px;
  background: #e0e0e0;
  border-radius: 8px;
  overflow: hidden;
}

.day-header {
  background: #f5f5f5;
  padding: 8px;
  text-align: center;
  font-size: 12px;
  font-weight: 600;
  color: #666;
}

.calendar-day {
  background: white;
  padding: 12px 8px;
  text-align: center;
  cursor: pointer;
  font-size: 14px;
  min-height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.calendar-day:hover {
  background: #f0f0f0;
}

.calendar-day.today {
  background: #1976d2;
  color: white;
}

.calendar-day.other-month {
  color: #ccc;
}

.calendar-day.has-appointment {
  background: #e3f2fd;
  color: #1976d2;
}

.booking-section {
  margin-bottom: 24px;
}

.appointment-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.appointment-type {
  font-weight: 600;
  color: #333;
}

.appointment-client {
  color: #666;
  font-size: 14px;
}

.appointment-time {
  font-weight: 600;
  color: #1976d2;
}

.new-booking-btn {
  width: 100%;
  background: #1976d2;
  color: white;
  border: none;
  padding: 12px;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  margin-top: 16px;
}

.new-booking-btn:hover {
  background: #1565c0;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.add-client-btn {
  background: none;
  border: 1px solid #ddd;
  padding: 6px 12px;
  border-radius: 6px;
  font-size: 12px;
  cursor: pointer;
  color: #666;
}

.client-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.client-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 0;
}

.client-status {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #4caf50;
}

.client-status.inactive {
  background: #f44336;
}

.search-box {
  position: relative;
  width: 250px;
}

.search-box input {
  width: 100%;
  padding: 8px 12px 8px 36px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 14px;
}

.search-icon {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: #666;
}

.file-categories {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
  margin-bottom: 24px;
}

.file-category {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  cursor: pointer;
}

.file-category:hover {
  background: #f5f5f5;
}

.folder-icon {
  font-size: 24px;
}

.document-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.doc-icon {
  font-size: 20px;
}

.doc-info {
  flex: 1;
}

.doc-name {
  font-weight: 500;
  color: #333;
}

.doc-date {
  font-size: 12px;
  color: #666;
}

.doc-action {
  background: none;
  border: none;
  font-size: 16px;
  color: #666;
  cursor: pointer;
}

.new-transaction-btn {
  background: #1976d2;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
}

.chart-container {
  margin-bottom: 24px;
}

.chart-bars {
  display: flex;
  align-items: flex-end;
  gap: 8px;
  height: 120px;
  margin-bottom: 8px;
}

.bar {
  flex: 1;
  min-height: 20px;
  border-radius: 4px 4px 0 0;
}

.chart-labels {
  display: flex;
  gap: 8px;
  font-size: 12px;
  color: #666;
}

.chart-labels span {
  flex: 1;
  text-align: center;
}

.chart-period {
  text-align: center;
  font-size: 12px;
  color: #666;
  margin-top: 8px;
}

.transaction-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.transaction-amount {
  font-weight: 600;
  color: #333;
}

.transaction-info {
  text-align: right;
}

.transaction-type {
  font-weight: 500;
  color: #333;
}

.transaction-date {
  font-size: 12px;
  color: #666;
}
