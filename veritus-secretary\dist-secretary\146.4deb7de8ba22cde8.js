"use strict";(self.webpackChunkveritus_secretary=self.webpackChunkveritus_secretary||[]).push([[146],{146:(f,l,o)=>{o.r(l),o.d(l,{SecretaryTabsPageModule:()=>h});var i=o(177),c=o(9417),a=o(2276),r=o(8498),t=o(4438);const d=[{path:"",component:(()=>{class n{constructor(){}ngOnInit(){}static{this.\u0275fac=function(e){return new(e||n)}}static{this.\u0275cmp=t.VBU({type:n,selectors:[["app-secretary-tabs"]],decls:23,vars:0,consts:[["slot","bottom",1,"veritus-tab-bar"],["tab","dashboard"],["name","home"],["tab","calendar"],["name","calendar"],["tab","cases"],["name","folder"],["tab","files"],["name","document"],["tab","profile"],["name","person"]],template:function(e,m){1&e&&(t.j41(0,"ion-tabs"),t.nrm(1,"ion-router-outlet"),t.j41(2,"ion-tab-bar",0)(3,"ion-tab-button",1),t.nrm(4,"ion-icon",2),t.j41(5,"ion-label"),t.EFF(6,"Dashboard"),t.k0s()(),t.j41(7,"ion-tab-button",3),t.nrm(8,"ion-icon",4),t.j41(9,"ion-label"),t.EFF(10,"Calendar"),t.k0s()(),t.j41(11,"ion-tab-button",5),t.nrm(12,"ion-icon",6),t.j41(13,"ion-label"),t.EFF(14,"Cases"),t.k0s()(),t.j41(15,"ion-tab-button",7),t.nrm(16,"ion-icon",8),t.j41(17,"ion-label"),t.EFF(18,"Files"),t.k0s()(),t.j41(19,"ion-tab-button",9),t.nrm(20,"ion-icon",10),t.j41(21,"ion-label"),t.EFF(22,"Profile"),t.k0s()()()())},dependencies:[a.iq,a.he,a.Jq,a.qW,a.p4,a.Rg],styles:[".veritus-tab-bar[_ngcontent-%COMP%]{--background: rgba(213, 208, 200, .38);--color: #666;--color-selected: #d4af37;border-top:1px solid rgba(255,255,255,.1);-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px)}ion-tab-button[_ngcontent-%COMP%]{--color: rgba(255, 255, 255, .6);--color-selected: #d4af37;--ripple-color: rgba(212, 175, 55, .2)}ion-tab-button.tab-selected[_ngcontent-%COMP%]{--color: #d4af37}ion-tab-button.tab-selected[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{color:#d4af37}ion-tab-button.tab-selected[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{color:#d4af37;font-weight:600}ion-icon[_ngcontent-%COMP%]{font-size:22px;margin-bottom:4px}ion-label[_ngcontent-%COMP%]{font-size:12px;font-weight:500}"]})}}return n})(),children:[{path:"dashboard",loadChildren:()=>o.e(6348).then(o.bind(o,6348)).then(n=>n.SecretaryDashboardPageModule)},{path:"calendar",loadChildren:()=>o.e(3710).then(o.bind(o,3710)).then(n=>n.SecretaryCalendarPageModule)},{path:"cases",loadChildren:()=>o.e(1711).then(o.bind(o,1711)).then(n=>n.SecretaryCasesPageModule)},{path:"files",loadChildren:()=>o.e(8154).then(o.bind(o,8154)).then(n=>n.SecretaryFilesPageModule)},{path:"profile",loadChildren:()=>o.e(3300).then(o.bind(o,3300)).then(n=>n.SecretaryProfilePageModule)},{path:"",redirectTo:"/secretary-tabs/dashboard",pathMatch:"full"}]}];let b=(()=>{class n{static{this.\u0275fac=function(e){return new(e||n)}}static{this.\u0275mod=t.$C({type:n})}static{this.\u0275inj=t.G2t({imports:[r.iI.forChild(d),r.iI]})}}return n})(),h=(()=>{class n{static{this.\u0275fac=function(e){return new(e||n)}}static{this.\u0275mod=t.$C({type:n})}static{this.\u0275inj=t.G2t({imports:[i.MD,c.YN,a.bv,b]})}}return n})()}}]);