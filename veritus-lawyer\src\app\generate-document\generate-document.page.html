<ion-header>
  <ion-toolbar class="generate-toolbar">
    <ion-buttons slot="start">
      <ion-button (click)="onBack()">
        <ion-icon name="arrow-back" class="back-icon"></ion-icon>
      </ion-button>
    </ion-buttons>
    <ion-title class="generate-title">Generate Document</ion-title>
  </ion-toolbar>
</ion-header>

<ion-content class="generate-content">
  <div class="generate-container">
    
    <!-- Header Description -->
    <div class="header-section">
      <p class="description-text">
        Choose a document type and fill in the required information to generate your legal document.
      </p>
    </div>

    <!-- Document Type Selector -->
    <div class="document-types-section">
      <h3 class="section-title">Select Document Type</h3>
      <div class="document-types-grid">
        <div 
          class="document-type-card" 
          *ngFor="let type of documentTypes"
          [class.selected]="selectedDocumentType === type.id"
          (click)="selectDocumentType(type)">
          
          <div class="type-icon">
            <ion-icon [name]="type.icon"></ion-icon>
          </div>
          <h4 class="type-name">{{ type.name }}</h4>
          <p class="type-description">{{ type.description }}</p>
        </div>
      </div>
    </div>

    <!-- Template Details Form -->
    <div class="template-details-section" *ngIf="currentTemplate">
      <h3 class="section-title">Template Details</h3>
      
      <form [formGroup]="documentForm" class="document-form">
        <div class="form-field" *ngFor="let field of currentTemplate.fields">
          <ion-label class="field-label">
            {{ field.label }}
            <span class="required-indicator" *ngIf="field.required">*</span>
          </ion-label>
          
          <!-- Text Input -->
          <ion-input
            *ngIf="field.type === 'text'"
            [formControlName]="field.id"
            [placeholder]="field.placeholder"
            class="field-input"
            fill="outline">
          </ion-input>
          
          <!-- Textarea -->
          <ion-textarea
            *ngIf="field.type === 'textarea'"
            [formControlName]="field.id"
            [placeholder]="field.placeholder"
            class="field-textarea"
            fill="outline"
            rows="4">
          </ion-textarea>
          
          <!-- Date Input -->
          <ion-datetime
            *ngIf="field.type === 'date'"
            [formControlName]="field.id"
            presentation="date"
            class="field-date">
          </ion-datetime>
          
          <!-- Select Input -->
          <ion-select
            *ngIf="field.type === 'select'"
            [formControlName]="field.id"
            interface="popover"
            class="field-select"
            fill="outline">
            <ion-select-option *ngFor="let option of field.options" [value]="option">
              {{ option }}
            </ion-select-option>
          </ion-select>
          
          <!-- Number Input -->
          <ion-input
            *ngIf="field.type === 'number'"
            [formControlName]="field.id"
            [placeholder]="field.placeholder"
            type="number"
            class="field-input"
            fill="outline">
          </ion-input>
          
          <!-- Error Message -->
          <div class="error-message" *ngIf="documentForm.get(field.id)?.invalid && documentForm.get(field.id)?.touched">
            <ion-text color="danger">
              <small>{{ field.label }} is required</small>
            </ion-text>
          </div>
        </div>
      </form>
    </div>

    <!-- Download Template Link -->
    <div class="download-section">
      <div class="download-card" (click)="downloadTemplate()">
        <div class="download-icon">
          <ion-icon name="folder-open"></ion-icon>
        </div>
        <div class="download-content">
          <h4 class="download-title">Download documents as PDF files</h4>
          <p class="download-subtitle">Get template files for offline use</p>
        </div>
        <div class="download-action">
          <ion-icon name="chevron-forward"></ion-icon>
        </div>
      </div>
    </div>

    <!-- Generate Button -->
    <div class="generate-section">
      <button 
        class="generate-btn"
        [disabled]="!documentForm.valid"
        (click)="onGenerateDocument()">
        <ion-icon name="document-text" class="btn-icon"></ion-icon>
        Generate Document
      </button>
    </div>

  </div>
</ion-content>
