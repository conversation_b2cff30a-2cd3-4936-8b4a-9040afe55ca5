<div 
  class="veritus-avatar"
  [ngClass]="{
    'veritus-avatar-sm': size === 'sm',
    'veritus-avatar-md': size === 'md',
    'veritus-avatar-lg': size === 'lg',
    'veritus-avatar-xl': size === 'xl',
    'veritus-avatar-border': showBorder
  }">
  <img 
    *ngIf="src" 
    [src]="src" 
    [alt]="alt"
    class="veritus-avatar-image">
  <div 
    *ngIf="!src && initials" 
    class="veritus-avatar-initials">
    {{ initials }}
  </div>
  <div 
    *ngIf="!src && !initials" 
    class="veritus-avatar-placeholder">
    <ion-icon name="person"></ion-icon>
  </div>
</div>
