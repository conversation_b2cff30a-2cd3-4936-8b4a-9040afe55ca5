import { Injectable } from '@angular/core';
import { Auth, signInWithEmailAndPassword, createUserWithEmailAndPassword, signOut, User, GoogleAuthProvider, signInWithPopup } from '@angular/fire/auth';
import { Firestore, doc, setDoc, getDoc, updateDoc } from '@angular/fire/firestore';
import { BehaviorSubject, Observable } from 'rxjs';

export interface UserProfile {
  uid: string;
  email: string;
  name: string;
  role: 'client' | 'lawyer' | 'secretary';
  phone?: string;
  avatar?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface LawyerProfile extends UserProfile {
  role: 'lawyer';
  rollNumber?: string;
  barId?: string;
  specialization?: string[];
  firmName?: string;
  photoIdUrl?: string;
  secretaryCode?: string;
  isVerified: boolean;
}

export interface SecretaryProfile extends UserProfile {
  role: 'secretary';
  linkedLawyers: string[];
  lawyerCode?: string;
  accessLevel: 'basic' | 'advanced' | 'full';
  isApproved: boolean;
  permissions: {
    canManageCalendar: boolean;
    canManageFiles: boolean;
    canManageCases: boolean;
    canManageRetainers: boolean;
    canViewFinances: boolean;
    canManageFinances: boolean;
  };
}

export interface ClientProfile extends UserProfile {
  role: 'client';
  preferredLawyers?: string[];
  caseHistory?: string[];
}

@Injectable({
  providedIn: 'root'
})
export class AuthService {
  private currentUserSubject = new BehaviorSubject<User | null>(null);
  public currentUser$ = this.currentUserSubject.asObservable();

  private userProfileSubject = new BehaviorSubject<UserProfile | null>(null);
  public userProfile$ = this.userProfileSubject.asObservable();

  constructor(
    private auth: Auth,
    private firestore: Firestore
  ) {
    // Listen to auth state changes
    this.auth.onAuthStateChanged(async (user) => {
      this.currentUserSubject.next(user);
      if (user) {
        await this.loadUserProfile(user.uid);
      } else {
        this.userProfileSubject.next(null);
      }
    });
  }

  // Email/Password Authentication
  async signInWithEmail(email: string, password: string): Promise<User> {
    const userCredential = await signInWithEmailAndPassword(this.auth, email, password);
    return userCredential.user;
  }

  async signUpWithEmail(email: string, password: string, role: 'client' | 'lawyer' | 'secretary'): Promise<User> {
    const userCredential = await createUserWithEmailAndPassword(this.auth, email, password);
    const user = userCredential.user;

    // Create basic profile
    const baseProfile: UserProfile = {
      uid: user.uid,
      email: user.email!,
      name: email.split('@')[0], // Default name from email
      role: role,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    await this.createUserProfile(baseProfile);
    return user;
  }

  // Google OAuth
  async signInWithGoogle(): Promise<User> {
    const provider = new GoogleAuthProvider();
    const userCredential = await signInWithPopup(this.auth, provider);
    const user = userCredential.user;

    // Check if profile exists, if not create one
    const profileExists = await this.checkProfileExists(user.uid);
    if (!profileExists) {
      const baseProfile: UserProfile = {
        uid: user.uid,
        email: user.email!,
        name: user.displayName || user.email!.split('@')[0],
        role: 'client', // Default role for Google sign-in
        avatar: user.photoURL || undefined,
        createdAt: new Date(),
        updatedAt: new Date()
      };
      await this.createUserProfile(baseProfile);
    }

    return user;
  }

  // Sign Out
  async signOut(): Promise<void> {
    await signOut(this.auth);
  }

  // Profile Management
  async createUserProfile(profile: UserProfile): Promise<void> {
    const docRef = doc(this.firestore, 'users', profile.uid);
    await setDoc(docRef, profile);
    this.userProfileSubject.next(profile);
  }

  async loadUserProfile(uid: string): Promise<UserProfile | null> {
    const docRef = doc(this.firestore, 'users', uid);
    const docSnap = await getDoc(docRef);

    if (docSnap.exists()) {
      const profile = docSnap.data() as UserProfile;
      this.userProfileSubject.next(profile);
      return profile;
    }
    return null;
  }

  async updateUserProfile(uid: string, updates: Partial<UserProfile>): Promise<void> {
    const docRef = doc(this.firestore, 'users', uid);
    await updateDoc(docRef, { ...updates, updatedAt: new Date() });
    
    // Reload profile
    await this.loadUserProfile(uid);
  }

  async checkProfileExists(uid: string): Promise<boolean> {
    const docRef = doc(this.firestore, 'users', uid);
    const docSnap = await getDoc(docRef);
    return docSnap.exists();
  }

  // Utility Methods
  getCurrentUser(): User | null {
    return this.currentUserSubject.value;
  }

  getCurrentUserProfile(): UserProfile | null {
    return this.userProfileSubject.value;
  }

  isAuthenticated(): boolean {
    return !!this.currentUserSubject.value;
  }

  getUserRole(): string | null {
    const profile = this.userProfileSubject.value;
    return profile ? profile.role : null;
  }

  // Generate Secretary Code for Lawyers
  generateSecretaryCode(): string {
    return Math.random().toString(36).substring(2, 8).toUpperCase();
  }
}
