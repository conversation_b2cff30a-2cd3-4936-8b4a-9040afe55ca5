import { NgModule, APP_INITIALIZER } from '@angular/core';
import { BrowserModule } from '@angular/platform-browser';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { HttpClientModule } from '@angular/common/http';

import { AppRoutingModule } from './app-routing.module';
import { AppComponent } from './app.component';

// Firebase
import { AngularFireModule } from '@angular/fire/compat';
import { AngularFireAuthModule } from '@angular/fire/compat/auth';
import { AngularFirestoreModule } from '@angular/fire/compat/firestore';
import { AngularFireStorageModule } from '@angular/fire/compat/storage';

// Components
import { LoginComponent } from './auth/login/login.component';
import { DashboardComponent } from './dashboard/dashboard.component';
import { SidebarComponent } from './shared/sidebar/sidebar.component';
import { HeaderComponent } from './shared/header/header.component';
import { LawyerVerificationComponent } from './lawyer-verification/lawyer-verification.component';
import { PlatformControlComponent } from './platform-control/platform-control.component';
import { LawyerListComponent } from './lawyer-list/lawyer-list.component';
import { TemplatesComponent } from './templates/templates.component';
import { UsersComponent } from './users/users.component';
import { SettingsComponent } from './settings/settings.component';
import { AdminSetupComponent } from './setup/admin-setup.component';

// Services
import { AdminAuthService } from './services/admin-auth.service';
import { LawyerVerificationService } from './services/lawyer-verification.service';
import { PlatformControlService } from './services/platform-control.service';
import { FirebaseService } from './services/firebase.service';

// Guards
import { AdminAuthGuard } from './guards/admin-auth.guard';

// Environment
import { environment } from '../environments/environment';

// App initializer function
export function initializeApp(adminAuthService: AdminAuthService): () => Promise<void> {
  return () => {
    return new Promise<void>((resolve) => {
      // Give Firebase a moment to restore auth state
      setTimeout(() => {
        resolve();
      }, 100);
    });
  };
}

@NgModule({
  declarations: [
    AppComponent,
    LoginComponent,
    DashboardComponent,
    SidebarComponent,
    HeaderComponent,
    LawyerVerificationComponent,
    PlatformControlComponent,
    LawyerListComponent,
    TemplatesComponent,
    UsersComponent,
    SettingsComponent,
    AdminSetupComponent
  ],
  imports: [
    BrowserModule,
    AppRoutingModule,
    FormsModule,
    ReactiveFormsModule,
    HttpClientModule,
    AngularFireModule.initializeApp(environment.firebase),
    AngularFireAuthModule,
    AngularFirestoreModule,
    AngularFireStorageModule
  ],
  providers: [
    AdminAuthService,
    LawyerVerificationService,
    PlatformControlService,
    FirebaseService,
    AdminAuthGuard,
    {
      provide: APP_INITIALIZER,
      useFactory: initializeApp,
      deps: [AdminAuthService],
      multi: true
    }
  ],
  bootstrap: [AppComponent]
})
export class AppModule { }
