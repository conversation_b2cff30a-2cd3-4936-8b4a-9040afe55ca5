<div class="role-selection-container">
  <h2 class="selection-title">Choose Your Role</h2>
  <p class="selection-subtitle">Select how you'll be using Veritus</p>
  
  <div class="roles-grid">
    <div 
      class="role-card" 
      *ngFor="let role of roles"
      [class.selected]="selectedRole === role.id"
      (click)="selectRole(role.id)">
      
      <div class="role-icon">
        <ion-icon [name]="role.icon"></ion-icon>
      </div>
      
      <h3 class="role-title">{{ role.title }}</h3>
      <p class="role-description">{{ role.description }}</p>
      
      <div class="selection-indicator" *ngIf="selectedRole === role.id">
        <ion-icon name="checkmark-circle"></ion-icon>
      </div>
    </div>
  </div>
</div>
