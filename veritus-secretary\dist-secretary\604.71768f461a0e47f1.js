"use strict";(self.webpackChunkveritus_secretary=self.webpackChunkveritus_secretary||[]).push([[604],{604:(u,n,e)=>{e.r(n),e.d(n,{startStatusTap:()=>d});var a=e(467),r=e(4363),o=e(2885),i=e(5638);const d=()=>{const s=window;s.addEventListener("statusTap",()=>{(0,r.e)(()=>{const _=document.elementFromPoint(s.innerWidth/2,s.innerHeight/2);if(!_)return;const t=(0,o.f)(_);t&&new Promise(E=>(0,i.c)(t,E)).then(()=>{(0,r.w)((0,a.A)(function*(){t.style.setProperty("--overflow","hidden"),yield(0,o.s)(t,300),t.style.removeProperty("--overflow")}))})})})}}}]);