import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { IonicModule } from '@ionic/angular';

// Services
import { AuthService } from './services/auth.service';
import { ProfileService } from './services/profile.service';
import { OnboardingService } from './services/onboarding.service';

// Components
import { RoleSelectionComponent } from './components/role-selection/role-selection.component';
import { LawyerOnboardingComponent } from './components/lawyer-onboarding/lawyer-onboarding.component';
import { SecretaryOnboardingComponent } from './components/secretary-onboarding/secretary-onboarding.component';
import { ProfileEditComponent } from './components/profile-edit/profile-edit.component';

@NgModule({
  declarations: [
    RoleSelectionComponent,
    LawyerOnboardingComponent,
    SecretaryOnboardingComponent,
    ProfileEditComponent
  ],
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    IonicModule
  ],
  providers: [
    AuthService,
    ProfileService,
    OnboardingService
  ],
  exports: [
    RoleSelectionComponent,
    LawyerOnboardingComponent,
    SecretaryOnboardingComponent,
    ProfileEditComponent
  ]
})
export class AuthModule { }
