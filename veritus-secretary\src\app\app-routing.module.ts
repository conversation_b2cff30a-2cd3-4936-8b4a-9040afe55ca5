import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';

// Existing components
import { DashboardComponent } from './dashboard/dashboard.component';
import { LoginComponent } from './login/login.component';
import { RegisterComponent } from './register/register.component';
import { LinkLawyerComponent } from './link-lawyer/link-lawyer.component';
import { CalendarComponent } from './calendar/calendar.component';
import { FilesComponent } from './files/files.component';
import { FinanceComponent } from './finance/finance.component';
import { ClientsComponent } from './clients/clients.component';
import { ActivityLogComponent } from './activity-log/activity-log.component';

const routes: Routes = [
  { path: '', redirectTo: '/secretary-dashboard', pathMatch: 'full' },
  { path: 'login', component: LoginComponent },
  { path: 'register', component: RegisterComponent },

  // Secretary Components (Direct Routes)
  {
    path: 'secretary-dashboard',
    loadChildren: () => import('./secretary-dashboard/secretary-dashboard.module').then(m => m.SecretaryDashboardPageModule)
  },
  {
    path: 'secretary-calendar',
    loadChildren: () => import('./secretary-calendar/secretary-calendar.module').then(m => m.SecretaryCalendarPageModule)
  },
  {
    path: 'secretary-cases',
    loadChildren: () => import('./secretary-cases/secretary-cases.module').then(m => m.SecretaryCasesPageModule)
  },
  {
    path: 'secretary-files',
    loadChildren: () => import('./secretary-files/secretary-files.module').then(m => m.SecretaryFilesPageModule)
  },
  {
    path: 'secretary-profile',
    loadChildren: () => import('./secretary-profile/secretary-profile.module').then(m => m.SecretaryProfilePageModule)
  },

  // Secretary Tab Navigation (keeping for backward compatibility)
  {
    path: 'secretary-tabs',
    loadChildren: () => import('./secretary-tabs/secretary-tabs.module').then(m => m.SecretaryTabsPageModule)
  },

  // Legacy routes (keeping for backward compatibility)
  { path: 'dashboard', component: DashboardComponent },
  { path: 'lawyer-list', component: LinkLawyerComponent },
  { path: 'calendar', component: CalendarComponent },
  { path: 'clients', component: ClientsComponent },
  { path: 'finance', component: FinanceComponent },
  { path: 'activity-log', component: ActivityLogComponent },
  { path: 'link-lawyer', component: LinkLawyerComponent },
  { path: 'files', component: FilesComponent },

  // Fallback
  { path: '**', redirectTo: '/secretary-dashboard' }
];

@NgModule({
  imports: [RouterModule.forRoot(routes)],
  exports: [RouterModule]
})
export class AppRoutingModule { }
