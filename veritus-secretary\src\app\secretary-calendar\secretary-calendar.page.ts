import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { IonicModule } from '@ionic/angular';
import { RouterModule } from '@angular/router';
import { FirebaseService, LawyerProfile } from '../services/firebase.service';
import { EnhancedAppointment } from '../models/scheduling.models';
import { ToastController } from '@ionic/angular';

@Component({
  selector: 'app-secretary-calendar',
  templateUrl: './secretary-calendar.page.html',
  styleUrls: ['./secretary-calendar.page.scss'],
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    RouterModule
  ]
})
export class SecretaryCalendarPage implements OnInit {
  selectedDate: string = new Date().toISOString().split('T')[0];
  linkedLawyers: LawyerProfile[] = [];
  selectedLawyer: LawyerProfile | null = null;
  appointments: EnhancedAppointment[] = [];

  // Tab management
  selectedTab: 'calendar' | 'appointments' | 'availability' = 'calendar';

  // Loading states
  isLoading = false;

  constructor(
    private firebaseService: FirebaseService,
    private toastController: ToastController
  ) { }

  ngOnInit() {
    console.log('SecretaryCalendarPage ngOnInit called');
    this.testFirebaseConnection();
    this.loadLinkedLawyers();
    this.loadAppointments();
  }

  async testFirebaseConnection() {
    console.log('Testing Firebase connection...');
    const currentUser = this.firebaseService.getCurrentUser();
    console.log('Current user:', currentUser);

    if (currentUser) {
      try {
        const profile = await this.firebaseService.getSecretaryProfile(currentUser.uid);
        console.log('Secretary profile:', profile);
      } catch (error) {
        console.error('Error getting secretary profile:', error);
      }
    }
  }

  async loadLinkedLawyers() {
    console.log('Loading linked lawyers...');
    const currentUser = this.firebaseService.getCurrentUser();
    console.log('Current user for linked lawyers:', currentUser);

    if (currentUser) {
      try {
        this.linkedLawyers = await this.firebaseService.getSecretaryLinkedLawyers(currentUser.uid);
        console.log('Linked lawyers loaded:', this.linkedLawyers);
      } catch (error) {
        console.error('Error loading linked lawyers:', error);
        // Add mock data for testing
        this.linkedLawyers = [
          {
            uid: 'lawyer1',
            email: '<EMAIL>',
            name: 'John Smith',
            rollNumber: 'BAR123456',
            barId: 'BAR123456',
            phone: '+1234567890',
            role: 'lawyer',
            createdAt: new Date(),
            updatedAt: new Date()
          },
          {
            uid: 'lawyer2',
            email: '<EMAIL>',
            name: 'Jane Doe',
            rollNumber: 'BAR789012',
            barId: 'BAR789012',
            phone: '+0987654321',
            role: 'lawyer',
            createdAt: new Date(),
            updatedAt: new Date()
          }
        ];
        console.log('Using mock linked lawyers:', this.linkedLawyers);
      }
    }
  }

  async loadAppointments() {
    console.log('Loading appointments...');
    const currentUser = this.firebaseService.getCurrentUser();
    if (!currentUser) {
      console.log('No current user found');
      return;
    }

    this.isLoading = true;
    try {
      this.appointments = await this.firebaseService.getAppointmentsForSecretary(currentUser.uid);
      console.log('Appointments loaded:', this.appointments);
    } catch (error) {
      console.error('Error loading appointments:', error);
      // Add mock data for testing
      this.appointments = [
        {
          id: 'apt1',
          lawyerId: 'lawyer1',
          lawyerName: 'John Smith',
          clientName: 'Alice Johnson',
          date: new Date().toISOString().split('T')[0],
          time: '09:00',
          status: 'pending',
          type: 'Consultation',
          createdBy: 'client',
          createdAt: new Date(),
          updatedAt: new Date()
        },
        {
          id: 'apt2',
          lawyerId: 'lawyer2',
          lawyerName: 'Jane Doe',
          clientName: 'Bob Wilson',
          date: new Date().toISOString().split('T')[0],
          time: '14:00',
          status: 'confirmed',
          type: 'Case Review',
          createdBy: 'client',
          createdAt: new Date(),
          updatedAt: new Date()
        }
      ];
      console.log('Using mock appointments:', this.appointments);
      this.showToast('Using mock data for testing', 'warning');
    } finally {
      this.isLoading = false;
    }
  }

  onLawyerSelected(lawyer: LawyerProfile) {
    this.selectedLawyer = lawyer;
  }

  onDateSelected(date: string) {
    this.selectedDate = date;
  }

  onAvailabilityChanged() {
    // Refresh data when availability changes
    this.loadAppointments();
  }

  refreshData() {
    console.log('Refreshing data...');
    this.loadLinkedLawyers();
    this.loadAppointments();
  }

  onTabChange(event: any) {
    console.log('Tab change event:', event);
    const tab = event.detail.value;
    console.log('Selected tab:', tab);
    if (tab === 'calendar' || tab === 'appointments' || tab === 'availability') {
      this.selectedTab = tab;
      console.log('Tab changed to:', this.selectedTab);
    }
  }

  setSelectedTab(tab: 'calendar' | 'appointments' | 'availability') {
    this.selectedTab = tab;
  }

  async showToast(message: string, color: string) {
    const toast = await this.toastController.create({
      message,
      duration: 3000,
      color,
      position: 'top'
    });
    await toast.present();
  }

  getConfirmedCount(): number {
    return this.appointments.filter(a => a.status === 'confirmed').length;
  }

  getTotalAppointments(): number {
    return this.appointments.length;
  }

  getPendingCount(): number {
    return this.appointments.filter(a => a.status === 'pending').length;
  }

  getUpcomingCount(): number {
    const now = new Date();
    return this.appointments.filter(apt => {
      const appointmentDateTime = new Date(apt.date + ' ' + apt.time);
      return appointmentDateTime > now;
    }).length;
  }

  getStatusColor(status: string): string {
    switch (status) {
      case 'confirmed': return 'success';
      case 'completed': return 'medium';
      case 'cancelled': return 'danger';
      default: return 'warning';
    }
  }
}
