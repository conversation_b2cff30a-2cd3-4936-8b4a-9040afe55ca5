import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { AlertController, LoadingController } from '@ionic/angular';
import { FirebaseService, LawyerProfile } from '../services/firebase.service';

interface ProfileData {
  name: string;
  lawFirm: string;
  yearsOfExperience: number | null;
  barRollNumber: string;
}

@Component({
  selector: 'app-profile-information',
  templateUrl: './profile-information.page.html',
  styleUrls: ['./profile-information.page.scss'],
})
export class ProfileInformationPage implements OnInit {

  profileData: ProfileData = {
    name: '',
    lawFirm: '',
    yearsOfExperience: null,
    barRollNumber: ''
  };

  profileImageUrl: string | null = null;
  private currentUser: any = null;
  private lawyerProfile: LawyerProfile | null = null;

  constructor(
    private router: Router,
    private alertController: AlertController,
    private loadingController: LoadingController,
    private firebaseService: FirebaseService
  ) { }

  ngOnInit() {
    this.loadProfileData();
    this.loadProfileImage();
  }

  async loadProfileData() {
    // Show loading
    const loading = await this.loadingController.create({
      message: 'Loading profile...',
      spinner: 'crescent'
    });
    await loading.present();

    try {
      // Get current user
      this.currentUser = this.firebaseService.getCurrentUser();

      if (!this.currentUser) {
        await loading.dismiss();
        await this.showAlert('Error', 'Please sign in to view your profile.');
        this.router.navigate(['/auth/signin']);
        return;
      }

      // Load lawyer profile from Firebase
      this.lawyerProfile = await this.firebaseService.getLawyerProfile(this.currentUser.uid);

      if (this.lawyerProfile) {
        // Map Firebase data to form data
        this.profileData = {
          name: this.lawyerProfile.name || '',
          lawFirm: '', // Always start blank for user to fill
          yearsOfExperience: null, // Always start blank for user to fill
          barRollNumber: this.lawyerProfile.rollNumber || ''
        };
      } else {
        // Set default values if no profile exists
        this.profileData = {
          name: this.currentUser.email?.split('@')[0] || '',
          lawFirm: '',
          yearsOfExperience: null,
          barRollNumber: ''
        };
      }

      await loading.dismiss();
    } catch (error) {
      await loading.dismiss();
      console.error('Error loading profile:', error);
      await this.showAlert('Error', 'Failed to load profile data. Please try again.');
    }
  }



  async loadProfileImage() {
    try {
      // Try to get from Firebase first
      const currentUser = this.firebaseService.getCurrentUser();
      if (currentUser) {
        const imageUrl = await this.firebaseService.getUserProfileImage(currentUser.uid);
        if (imageUrl) {
          this.profileImageUrl = imageUrl;
          return;
        }
      }

      // Fallback to localStorage for mock users
      const savedImage = localStorage.getItem('profileImage');
      if (savedImage) {
        this.profileImageUrl = savedImage;
      }
    } catch (error) {
      console.error('Error loading profile image:', error);
      // Fallback to localStorage
      const savedImage = localStorage.getItem('profileImage');
      if (savedImage) {
        this.profileImageUrl = savedImage;
      }
    }
  }

  onBack() {
    this.router.navigate(['/tabs/profile']);
  }

  async onSave() {
    // Validate form data
    if (!this.profileData.name.trim()) {
      await this.showAlert('Validation Error', 'Please enter your name.');
      return;
    }

    if (!this.profileData.lawFirm.trim()) {
      await this.showAlert('Validation Error', 'Please enter your law firm name.');
      return;
    }

    if (!this.profileData.barRollNumber.trim()) {
      await this.showAlert('Validation Error', 'Please enter your bar roll number.');
      return;
    }

    if (this.profileData.yearsOfExperience !== null && this.profileData.yearsOfExperience < 0) {
      await this.showAlert('Validation Error', 'Years of experience cannot be negative.');
      return;
    }

    if (!this.currentUser) {
      await this.showAlert('Error', 'Please sign in to save your profile.');
      return;
    }

    // Show loading
    const loading = await this.loadingController.create({
      message: 'Saving profile...',
      spinner: 'crescent'
    });
    await loading.present();

    try {
      // Prepare profile updates
      const profileUpdates: Partial<LawyerProfile> = {
        name: this.profileData.name.trim(),
        firmName: this.profileData.lawFirm.trim(),
        rollNumber: this.profileData.barRollNumber.trim(),
        updatedAt: new Date()
      };

      if (this.lawyerProfile) {
        // Update existing lawyer profile
        await this.firebaseService.updateLawyerProfile(this.currentUser.uid, profileUpdates);
      } else {
        // Create new lawyer profile
        const newLawyerProfile: LawyerProfile = {
          uid: this.currentUser.uid,
          email: this.currentUser.email || '',
          name: this.profileData.name.trim(),
          role: 'lawyer',
          firmName: this.profileData.lawFirm.trim(),
          rollNumber: this.profileData.barRollNumber.trim(),
          barId: '', // Can be added later
          secretaryCode: '',
          createdAt: new Date(),
          updatedAt: new Date()
        };

        await this.firebaseService.createLawyerProfile(newLawyerProfile);
      }

      // Also update the lawyer profile
      await this.firebaseService.updateLawyerProfile(this.currentUser.uid, {
        name: this.profileData.name.trim(),
        firmName: this.profileData.lawFirm.trim(),
        rollNumber: this.profileData.barRollNumber.trim()
      });

      await loading.dismiss();
      await this.showAlert('Success', 'Profile information saved successfully!');

      // Navigate back to profile page
      this.router.navigate(['/tabs/profile']);

    } catch (error) {
      await loading.dismiss();
      await this.showAlert('Error', 'Failed to save profile information. Please try again.');
      console.error('Save error:', error);
    }
  }

  private async showAlert(header: string, message: string) {
    const alert = await this.alertController.create({
      header,
      message,
      buttons: ['OK']
    });
    await alert.present();
  }

  async onLocationClick() {
    const alert = await this.alertController.create({
      header: 'Location Services',
      message: 'Would you like to add your law firm location?',
      buttons: [
        {
          text: 'Cancel',
          role: 'cancel'
        },
        {
          text: 'Add Location',
          handler: () => {
            this.openLocationPicker();
          }
        }
      ]
    });
    await alert.present();
  }

  private async openLocationPicker() {
    // For now, show a simple input dialog for location
    // In a real app, you would integrate with Google Maps or similar
    const alert = await this.alertController.create({
      header: 'Add Law Firm Location',
      inputs: [
        {
          name: 'address',
          type: 'text',
          placeholder: 'Enter law firm address',
          value: ''
        },
        {
          name: 'city',
          type: 'text',
          placeholder: 'City',
          value: ''
        },
        {
          name: 'state',
          type: 'text',
          placeholder: 'State/Province',
          value: ''
        }
      ],
      buttons: [
        {
          text: 'Cancel',
          role: 'cancel'
        },
        {
          text: 'Save Location',
          handler: (data) => {
            if (data.address && data.city) {
              const fullAddress = `${data.address}, ${data.city}${data.state ? ', ' + data.state : ''}`;
              this.profileData.lawFirm = `${this.profileData.lawFirm} (${fullAddress})`.trim();
              this.showLocationSavedAlert();
            }
          }
        }
      ]
    });
    await alert.present();
  }

  private async showLocationSavedAlert() {
    const alert = await this.alertController.create({
      header: 'Location Added',
      message: 'Law firm location has been added to your profile.',
      buttons: ['OK']
    });
    await alert.present();
  }
}
