.cases-content {
  --background: #FFFFFF;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
}

.cases-container {
  padding: 24px;
  min-height: 100vh;
  max-width: 1200px;
  margin: 0 auto;
}

.filters-section {
  background: #FFFFFF;
  border-radius: 16px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid #e2e8f0;
}

.filter-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin-bottom: 20px;
}

.filter-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.filter-label {
  font-weight: 600;
  color: #374151;
  font-size: 14px;
  margin-bottom: 4px;
}

.lawyer-select, .status-select {
  --background: #FFFFFF;
  --color: #374151;
  --border-radius: 12px;
  --padding-start: 16px;
  --padding-end: 16px;
  --padding-top: 12px;
  --padding-bottom: 12px;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  font-size: 14px;
  transition: all 0.3s ease;

  &:focus {
    border-color: #C49A56;
    box-shadow: 0 0 0 3px rgba(196, 154, 86, 0.1);
  }
}

.search-section {
  margin-top: 20px;
}

.custom-searchbar {
  --background: #FFFFFF;
  --color: #374151;
  --placeholder-color: #9ca3af;
  --icon-color: #6b7280;
  --border-radius: 12px;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  transition: all 0.3s ease;

  &:focus-within {
    border-color: #C49A56;
    box-shadow: 0 0 0 3px rgba(196, 154, 86, 0.1);
  }
}

.action-section {
  margin-bottom: 32px;
}

.create-btn {
  width: 100%;
  height: 56px;
  background: linear-gradient(135deg, #C49A56 0%, #D8B67E 100%);
  color: #FFFFFF;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  font-weight: 600;
  font-size: 16px;
  border: none;
  box-shadow: 0 8px 24px rgba(196, 154, 86, 0.3);
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 32px rgba(196, 154, 86, 0.4);
  }

  &:active {
    transform: translateY(0);
  }
}

.btn-icon {
  font-size: 20px;
}

.cases-section {
  margin-bottom: 32px;
}

.section-title {
  color: #374151;
  font-size: 24px;
  font-weight: 700;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  gap: 12px;

  &::before {
    content: '';
    width: 4px;
    height: 24px;
    background: linear-gradient(135deg, #C49A56 0%, #D8B67E 100%);
    border-radius: 2px;
  }
}

.cases-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.case-card {
  background: #FFFFFF;
  border-radius: 16px;
  padding: 24px;
  display: grid;
  grid-template-columns: auto 1fr auto;
  gap: 20px;
  align-items: center;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid #e2e8f0;
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 32px rgba(0, 0, 0, 0.12);
    border-color: #C49A56;
  }
}

.case-status {
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, #C49A56 0%, #D8B67E 100%);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 12px rgba(196, 154, 86, 0.3);

  ion-icon {
    font-size: 24px;
    color: #FFFFFF;
  }

  &.status-ongoing {
    background: linear-gradient(135deg, #10b981 0%, #34d399 100%);
    box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
  }

  &.status-pending {
    background: linear-gradient(135deg, #f59e0b 0%, #fbbf24 100%);
    box-shadow: 0 4px 12px rgba(245, 158, 11, 0.3);
  }

  &.status-closed {
    background: linear-gradient(135deg, #6b7280 0%, #9ca3af 100%);
    box-shadow: 0 4px 12px rgba(107, 114, 128, 0.3);
  }
}

.case-details {
  flex: 1;
}

.case-title {
  color: #111827;
  font-size: 18px;
  font-weight: 700;
  margin-bottom: 8px;
  line-height: 1.4;
}

.case-client {
  color: #C49A56;
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 6px;
}

.case-description {
  color: #6b7280;
  font-size: 14px;
  margin-bottom: 6px;
  line-height: 1.5;
}

.case-lawyer {
  color: #1B3A9E;
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 12px;
}

.case-meta {
  display: flex;
  gap: 20px;
  align-items: center;
}

.case-files, .case-date {
  display: flex;
  align-items: center;
  gap: 6px;
  color: #6b7280;
  font-size: 12px;

  ion-icon {
    font-size: 14px;
    color: #9ca3af;
  }
}

.case-actions {
  display: flex;
  gap: 12px;
}

.action-btn {
  width: 44px;
  height: 44px;
  border-radius: 12px;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  cursor: pointer;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.15);
  }

  ion-icon {
    font-size: 18px;
  }
}

.edit-btn {
  background: linear-gradient(135deg, #1B3A9E 0%, #3B5AAD 100%);
  color: #FFFFFF;
  box-shadow: 0 4px 12px rgba(27, 58, 158, 0.3);

  &:hover {
    box-shadow: 0 8px 20px rgba(27, 58, 158, 0.4);
  }
}

.view-btn {
  background: linear-gradient(135deg, #10b981 0%, #34d399 100%);
  color: #FFFFFF;
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);

  &:hover {
    box-shadow: 0 8px 20px rgba(16, 185, 129, 0.4);
  }
}

.empty-state {
  text-align: center;
  padding: 60px 40px;
  background: #FFFFFF;
  border-radius: 16px;
  border: 2px dashed #e2e8f0;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.empty-icon {
  font-size: 64px;
  color: #C49A56;
  margin-bottom: 24px;
}

.empty-title {
  color: #374151;
  font-size: 20px;
  font-weight: 700;
  margin-bottom: 12px;
}

.empty-description {
  color: #6b7280;
  font-size: 16px;
  margin-bottom: 32px;
  line-height: 1.6;
}

.stats-section {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;
  margin-bottom: 32px;
}

.stat-card {
  background: #FFFFFF;
  border-radius: 16px;
  padding: 24px;
  text-align: center;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid #e2e8f0;
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 32px rgba(0, 0, 0, 0.12);
  }

  &.stat-ongoing {
    border-left: 4px solid #10b981;
  }

  &.stat-pending {
    border-left: 4px solid #f59e0b;
  }

  &.stat-closed {
    border-left: 4px solid #6b7280;
  }
}

.stat-number {
  color: #111827;
  font-size: 32px;
  font-weight: 800;
  margin-bottom: 8px;
  line-height: 1;
}

.stat-label {
  color: #6b7280;
  font-size: 14px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

// Responsive Design
@media (max-width: 768px) {
  .cases-container {
    padding: 16px;
  }

  .filters-section {
    padding: 20px;
  }

  .filter-row {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .case-card {
    grid-template-columns: 1fr;
    text-align: center;
    gap: 16px;
    padding: 20px;
  }

  .case-actions {
    justify-content: center;
  }

  .stats-section {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .section-title {
    font-size: 20px;
  }

  .case-title {
    font-size: 16px;
  }
}

@media (max-width: 480px) {
  .cases-container {
    padding: 12px;
  }

  .filters-section {
    padding: 16px;
  }

  .case-card {
    padding: 16px;
  }

  .empty-state {
    padding: 40px 20px;
  }

  .empty-icon {
    font-size: 48px;
  }
}
