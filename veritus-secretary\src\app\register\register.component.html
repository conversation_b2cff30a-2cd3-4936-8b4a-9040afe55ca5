<div class="modern-auth-content">
  <div class="auth-wrapper">

    <!-- Main Content -->
    <div class="auth-main-container">
      <!-- Logo Section -->
      <div class="logo-section">
        <div class="logo-container">
          <div class="logo-icon">
            <ion-icon name="briefcase-outline" class="secretary-icon"></ion-icon>
          </div>
          <h1 class="brand-title">Veritus</h1>
          <p class="brand-subtitle">Legal Excellence Platform</p>
        </div>
      </div>

      <!-- Form Card -->
      <div class="form-card">
        <div class="form-header">
          <h2 class="form-title">Secretary Registration</h2>
          <p class="form-subtitle">Create your secretary account and link to your lawyer</p>
        </div>

        <form class="modern-form" (ngSubmit)="onRegister()">
          <!-- Secretary Registration Form -->
          <div class="secretary-form">
            <!-- Full Name -->
            <div class="input-group">
              <label class="input-label">Full Name</label>
              <input
                type="text"
                class="form-input"
                placeholder="Enter your full name"
                [(ngModel)]="fullName"
                name="fullName"
                required>
            </div>

            <!-- Email -->
            <div class="input-group">
              <label class="input-label">Email Address</label>
              <input
                type="email"
                class="form-input"
                placeholder="Enter your email"
                [(ngModel)]="email"
                name="email"
                required>
            </div>

            <!-- Password -->
            <div class="input-group">
              <label class="input-label">Password</label>
              <div class="password-input-container">
                <input
                  [type]="showPassword ? 'text' : 'password'"
                  class="form-input password-input"
                  placeholder="Create a password"
                  [(ngModel)]="password"
                  name="password"
                  required>
                <button
                  type="button"
                  class="password-toggle"
                  (click)="togglePasswordVisibility()">
                  <svg class="toggle-icon" viewBox="0 0 512 512" *ngIf="!showPassword">
                    <path d="M256 105c-101.8 0-188.4 62.4-224 151 35.6 88.6 122.2 151 224 151s188.4-62.4 224-151c-35.6-88.6-122.2-151-224-151zM256 347c-50.6 0-91.7-41.1-91.7-91.7s41.1-91.7 91.7-91.7 91.7 41.1 91.7 91.7-41.1 91.7-91.7 91.7zM256 203c-28.7 0-52 23.3-52 52s23.3 52 52 52 52-23.3 52-52-23.3-52-52-52z"/>
                  </svg>
                  <svg class="toggle-icon" viewBox="0 0 512 512" *ngIf="showPassword">
                    <path d="M256 105c-101.8 0-188.4 62.4-224 151 35.6 88.6 122.2 151 224 151s188.4-62.4 224-151c-35.6-88.6-122.2-151-224-151zM256 347c-50.6 0-91.7-41.1-91.7-91.7s41.1-91.7 91.7-91.7 91.7 41.1 91.7 91.7-41.1 91.7-91.7 91.7zM256 203c-28.7 0-52 23.3-52 52s23.3 52 52 52 52-23.3 52-52-23.3-52-52-52z"/>
                    <line x1="50" y1="50" x2="462" y2="462" stroke="currentColor" stroke-width="32"/>
                  </svg>
                </button>
              </div>
            </div>

            <!-- Confirm Password -->
            <div class="input-group">
              <label class="input-label">Confirm Password</label>
              <div class="password-input-container">
                <input
                  [type]="showConfirmPassword ? 'text' : 'password'"
                  class="form-input password-input"
                  placeholder="Confirm your password"
                  [(ngModel)]="confirmPassword"
                  name="confirmPassword"
                  required>
                <button
                  type="button"
                  class="password-toggle"
                  (click)="toggleConfirmPasswordVisibility()">
                  <svg class="toggle-icon" viewBox="0 0 512 512" *ngIf="!showConfirmPassword">
                    <path d="M256 105c-101.8 0-188.4 62.4-224 151 35.6 88.6 122.2 151 224 151s188.4-62.4 224-151c-35.6-88.6-122.2-151-224-151zM256 347c-50.6 0-91.7-41.1-91.7-91.7s41.1-91.7 91.7-91.7 91.7 41.1 91.7 91.7-41.1 91.7-91.7 91.7zM256 203c-28.7 0-52 23.3-52 52s23.3 52 52 52 52-23.3 52-52-23.3-52-52-52z"/>
                  </svg>
                  <svg class="toggle-icon" viewBox="0 0 512 512" *ngIf="showConfirmPassword">
                    <path d="M256 105c-101.8 0-188.4 62.4-224 151 35.6 88.6 122.2 151 224 151s188.4-62.4 224-151c-35.6-88.6-122.2-151-224-151zM256 347c-50.6 0-91.7-41.1-91.7-91.7s41.1-91.7 91.7-91.7 91.7 41.1 91.7 91.7-41.1 91.7-91.7 91.7zM256 203c-28.7 0-52 23.3-52 52s23.3 52 52 52 52-23.3 52-52-23.3-52-52-52z"/>
                    <line x1="50" y1="50" x2="462" y2="462" stroke="currentColor" stroke-width="32"/>
                  </svg>
                </button>
              </div>
            </div>

            <!-- Lawyer Code -->
            <div class="input-group">
              <label class="input-label">Lawyer Code</label>
              <input
                type="text"
                class="form-input"
                placeholder="Enter the lawyer code provided by your lawyer"
                [(ngModel)]="lawyerCode"
                name="lawyerCode"
                required>
              <small class="input-help">This code links you to your lawyer's account</small>
            </div>


          </div>

          <!-- Register Button -->
          <button
            type="submit"
            class="modern-continue-btn"
            [disabled]="!isFormValid()">
            <span class="btn-text">Create Account</span>
            <ion-icon name="person-add-outline" class="btn-icon"></ion-icon>
          </button>
        </form>

        <!-- Sign In Link -->
        <div class="signin-section">
          <button type="button" class="signin-link" (click)="goToSignIn()">
            Already have an account?
          </button>
        </div>
      </div>
    </div>
  </div>
</div>
