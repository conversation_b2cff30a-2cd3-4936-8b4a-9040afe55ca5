<div class="setup-container">
  <div class="setup-card">
    <div class="setup-header">
      <div class="back-button">
        <button type="button" class="back-btn" (click)="goToLogin()">
          ← Back to Login
        </button>
      </div>
      <div class="logo">
        <h1>Veritus</h1>
        <p>Admin Account Setup</p>
        <div class="logo-subtitle">Legal Excellence Management</div>
      </div>
    </div>

    <form [formGroup]="setupForm" (ngSubmit)="createAdminAccount()" class="setup-form">
      <div class="form-group">
        <label for="email">Admin Email</label>
        <input 
          type="email" 
          id="email" 
          formControlName="email" 
          class="form-control"
          placeholder="<EMAIL>">
        <div class="error-message" *ngIf="setupForm.get('email')?.invalid && setupForm.get('email')?.touched">
          <span *ngIf="setupForm.get('email')?.errors?.['required']">Email is required</span>
          <span *ngIf="setupForm.get('email')?.errors?.['email']">Please enter a valid email</span>
        </div>
      </div>

      <div class="form-group">
        <label for="password">Password</label>
        <div class="password-input-container">
          <input
            [type]="showPassword ? 'text' : 'password'"
            id="password"
            formControlName="password"
            class="form-control"
            placeholder="Enter secure password">
          <button
            type="button"
            class="password-toggle"
            (click)="togglePasswordVisibility()">
            {{ showPassword ? '👁' : '👁‍🗨' }}
          </button>
        </div>
        <div class="error-message" *ngIf="setupForm.get('password')?.invalid && setupForm.get('password')?.touched">
          <span *ngIf="setupForm.get('password')?.errors?.['required']">Password is required</span>
          <span *ngIf="setupForm.get('password')?.errors?.['minlength']">Password must be at least 6 characters</span>
        </div>
      </div>

      <div class="form-group">
        <label for="displayName">Display Name</label>
        <input 
          type="text" 
          id="displayName" 
          formControlName="displayName" 
          class="form-control"
          placeholder="Admin Name">
        <div class="error-message" *ngIf="setupForm.get('displayName')?.invalid && setupForm.get('displayName')?.touched">
          <span *ngIf="setupForm.get('displayName')?.errors?.['required']">Display name is required</span>
        </div>
      </div>


      <button
        type="submit"
        class="setup-btn"
        [disabled]="setupForm.invalid || isCreating">
        <span *ngIf="isCreating" class="spinner"></span>
        {{ isCreating ? 'Creating Admin Account...' : 'Create Admin Account' }}
      </button>

      <div class="success-message" *ngIf="successMessage">
        <div class="success-icon">🎉</div>
        <h3>Admin Account Created Successfully!</h3>
        <p>{{ successMessage }}</p>
        <button class="login-btn" (click)="goToLogin()">Go to Login</button>
      </div>

      <div class="error-message" *ngIf="errorMessage">
        {{ errorMessage }}
      </div>
    </form>

   
  </div>
</div>
