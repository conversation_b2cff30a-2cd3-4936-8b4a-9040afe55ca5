<ion-header>
  <ion-toolbar class="case-progress-toolbar">
    <ion-buttons slot="start">
      <ion-button (click)="onBack()">
        <ion-icon name="arrow-back" class="back-icon"></ion-icon>
      </ion-button>
    </ion-buttons>
    <ion-title class="case-progress-title">Case Progress</ion-title>
  </ion-toolbar>
</ion-header>

<ion-content class="case-progress-content">
  <div class="case-progress-container" *ngIf="!isLoading && currentCase">
    
    <!-- Profile Section -->
    <div class="profile-section">
      <div class="profile-avatar">
        <div class="avatar-placeholder">
          <ion-icon name="briefcase" class="avatar-icon"></ion-icon>
        </div>
      </div>
      
      <div class="profile-details">
        <h2 class="case-title">{{ currentCase.title }}</h2>
        <p class="case-info">
          <span class="case-number">Case #{{ currentCase.id }}</span> • 
          <span class="case-date">{{ formatDate(currentCase.startDate) }}</span>
        </p>
        <p class="lawyer-name">{{ currentCase.lawyerName }}</p>
      </div>
    </div>

    <!-- Progress Bar -->
    <div class="progress-section">
      <div class="progress-header">
        <span class="progress-label">Case Progress</span>
        <span class="progress-percentage">{{ currentCase.progress }}%</span>
      </div>
      <div class="progress-bar">
        <div class="progress-fill" [style.width.%]="currentCase.progress"></div>
      </div>
    </div>

    <!-- Timeline Section -->
    <div class="timeline-section">
      <div class="timeline-header-section">
        <h3 class="timeline-title">Case Timeline</h3>
        <!-- Lawyer-only: Add Milestone Button -->
        <ion-button *ngIf="userRole === 'lawyer'"
                    fill="clear"
                    size="small"
                    color="primary"
                    (click)="onAddMilestone()">
          <ion-icon name="add" slot="start"></ion-icon>
          Add Milestone
        </ion-button>
      </div>
      
      <div class="timeline">
        <div 
          class="timeline-item" 
          *ngFor="let event of currentCase.timeline; let last = last"
          [class.completed]="event.isCompleted"
          [class.last]="last">
          
          <div class="timeline-marker">
            <div class="timeline-dot" [class.completed]="event.isCompleted">
              <ion-icon [name]="getTimelineIcon(event.type)" class="timeline-icon"></ion-icon>
            </div>
            <div class="timeline-line" *ngIf="!last"></div>
          </div>
          
          <div class="timeline-content">
            <div class="timeline-header">
              <h4 class="timeline-event-title">{{ event.title }}</h4>
              <span class="timeline-date">{{ formatDate(event.date) }}</span>
            </div>
            <p class="timeline-description">{{ event.description }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Case Details -->
    <div class="details-section">
      <h3 class="details-title">Case Details</h3>
      <div class="details-card">
        <div class="detail-item">
          <span class="detail-label">Type:</span>
          <span class="detail-value">{{ currentCase.type }}</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">Status:</span>
          <span class="detail-value status" [class]="currentCase.status">{{ currentCase.status | titlecase }}</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">Last Update:</span>
          <span class="detail-value">{{ formatDate(currentCase.lastUpdate) }}</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">Description:</span>
          <span class="detail-value">{{ currentCase.description }}</span>
        </div>
      </div>
    </div>



  </div>

  <!-- Loading State -->
  <div class="loading-state" *ngIf="isLoading">
    <ion-spinner name="crescent"></ion-spinner>
    <p>Loading case details...</p>
  </div>

  <!-- Empty State -->
  <div class="empty-state" *ngIf="!isLoading && !currentCase">
    <ion-icon name="briefcase-outline" class="empty-icon"></ion-icon>
    <h3 class="empty-title">Case Not Found</h3>
    <p class="empty-description">The requested case could not be found.</p>
    <button class="back-btn" (click)="onBack()">Go Back</button>
  </div>

</ion-content>
