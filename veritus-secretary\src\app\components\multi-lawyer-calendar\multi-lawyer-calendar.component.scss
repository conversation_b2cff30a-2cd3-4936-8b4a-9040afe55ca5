.multi-lawyer-calendar-container {
  padding: 20px;
  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
  border-radius: 12px;
  color: white;
  min-height: 500px;
}

.calendar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  
  .header-left {
    h2 {
      margin: 0 0 4px 0;
      font-size: 1.6rem;
      font-weight: 600;
      color: #ffffff;
    }
    
    .subtitle {
      margin: 0;
      color: rgba(255, 255, 255, 0.7);
      font-size: 0.9rem;
    }
  }
  
  .header-actions {
    display: flex;
    gap: 8px;
  }
}

.no-lawyers-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px;
  text-align: center;
  
  .large-icon {
    font-size: 4rem;
    color: rgba(255, 255, 255, 0.3);
    margin-bottom: 24px;
  }
  
  h3 {
    margin: 0 0 12px 0;
    color: rgba(255, 255, 255, 0.8);
    font-size: 1.4rem;
  }
  
  p {
    margin: 0;
    color: rgba(255, 255, 255, 0.6);
    font-size: 1rem;
  }
}

.lawyer-selection-panel {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 24px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  
  .panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    
    h3 {
      margin: 0;
      color: #ffffff;
      font-size: 1.2rem;
    }
  }
  
  .lawyers-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 16px;
    
    .lawyer-card {
      background: rgba(255, 255, 255, 0.08);
      border-radius: 12px;
      padding: 16px;
      border: 2px solid rgba(255, 255, 255, 0.1);
      cursor: pointer;
      transition: all 0.3s ease;
      
      &:hover {
        background: rgba(255, 255, 255, 0.12);
        transform: translateY(-2px);
        box-shadow: 0 8px 24px rgba(0, 0, 0, 0.3);
      }
      
      &.selected {
        border-color: #4ade80;
        background: rgba(74, 222, 128, 0.1);
      }
      
      &.loading {
        opacity: 0.7;
        cursor: not-allowed;
      }
      
      .lawyer-info {
        display: flex;
        align-items: center;
        gap: 12px;
        margin-bottom: 12px;
        
        .lawyer-avatar {
          width: 40px;
          height: 40px;
          border-radius: 50%;
          overflow: hidden;
          background: rgba(255, 255, 255, 0.1);
          display: flex;
          align-items: center;
          justify-content: center;
          
          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }
          
          ion-icon {
            font-size: 1.5rem;
            color: rgba(255, 255, 255, 0.6);
          }
        }
        
        .lawyer-details {
          flex: 1;
          
          h4 {
            margin: 0 0 4px 0;
            color: #ffffff;
            font-size: 1rem;
            font-weight: 600;
          }
          
          .lawyer-role {
            margin: 0;
            color: rgba(255, 255, 255, 0.6);
            font-size: 0.8rem;
          }
        }
      }
      
      .lawyer-status {
        margin-bottom: 12px;
        
        .status-chip {
          font-size: 0.8rem;
        }
      }
      
      .lawyer-stats {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 8px;
        
        .stat-item {
          text-align: center;
          
          .stat-number {
            display: block;
            font-size: 1.2rem;
            font-weight: bold;
            color: #4ade80;
            margin-bottom: 2px;
          }
          
          .stat-label {
            font-size: 0.7rem;
            color: rgba(255, 255, 255, 0.6);
            text-transform: uppercase;
            letter-spacing: 0.5px;
          }
        }
      }
      
      .loading-stats {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 8px;
      }
    }
  }
}

.calendar-navigation {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 24px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  
  .nav-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    
    .current-period {
      text-align: center;
      
      h3 {
        margin: 0 0 4px 0;
        color: #ffffff;
        font-size: 1.3rem;
        font-weight: 600;
      }
      
      p {
        margin: 0;
        color: rgba(255, 255, 255, 0.7);
        font-size: 0.9rem;
      }
    }
  }
  
  .view-controls {
    ion-segment {
      background: rgba(255, 255, 255, 0.1);
      border-radius: 8px;
      
      ion-segment-button {
        --color: rgba(255, 255, 255, 0.7);
        --color-checked: #ffffff;
        --background-checked: rgba(255, 255, 255, 0.2);
      }
    }
  }
}

.week-calendar {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 24px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  
  .week-header {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 8px;
    margin-bottom: 16px;
    
    .day-header {
      text-align: center;
      padding: 12px 8px;
      border-radius: 8px;
      cursor: pointer;
      transition: all 0.3s ease;
      
      &:hover {
        background: rgba(255, 255, 255, 0.1);
      }
      
      &.today {
        background: rgba(74, 222, 128, 0.2);
        border: 1px solid #4ade80;
      }
      
      &.selected {
        background: rgba(59, 130, 246, 0.2);
        border: 1px solid #3b82f6;
      }
      
      &.weekend {
        opacity: 0.7;
      }
      
      .day-name {
        font-size: 0.8rem;
        color: rgba(255, 255, 255, 0.7);
        margin-bottom: 4px;
        text-transform: uppercase;
        letter-spacing: 0.5px;
      }
      
      .day-number {
        font-size: 1.2rem;
        font-weight: bold;
        color: #ffffff;
        margin-bottom: 8px;
      }
      
      .day-indicators {
        .appointments-count,
        .available-count {
          font-size: 0.7rem;
          padding: 2px 6px;
          border-radius: 4px;
          margin: 2px 0;
        }
        
        .appointments-count {
          background: rgba(245, 158, 11, 0.2);
          color: #f59e0b;
        }
        
        .available-count {
          background: rgba(74, 222, 128, 0.2);
          color: #4ade80;
        }
      }
    }
  }
  
  .week-body {
    .time-slots {
      .time-slot {
        display: flex;
        align-items: center;
        padding: 8px 0;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        
        .time-label {
          width: 60px;
          font-size: 0.8rem;
          color: rgba(255, 255, 255, 0.6);
          text-align: right;
          margin-right: 16px;
        }
        
        .slot-content {
          flex: 1;
          min-height: 30px;
          background: rgba(255, 255, 255, 0.02);
          border-radius: 4px;
        }
      }
    }
  }
}

.day-calendar,
.month-calendar {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 24px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  
  .day-header,
  .month-header {
    margin-bottom: 20px;
    
    h3 {
      margin: 0 0 8px 0;
      color: #ffffff;
      font-size: 1.4rem;
      font-weight: 600;
    }
    
    .day-stats {
      display: flex;
      gap: 16px;
      
      span {
        color: rgba(255, 255, 255, 0.7);
        font-size: 0.9rem;
      }
    }
  }
  
  .placeholder-text {
    color: rgba(255, 255, 255, 0.6);
    font-style: italic;
    text-align: center;
    padding: 40px;
  }
}

.quick-stats {
  .stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 16px;
    
    .stat-card {
      background: rgba(255, 255, 255, 0.08);
      border-radius: 12px;
      padding: 20px;
      text-align: center;
      border: 1px solid rgba(255, 255, 255, 0.1);
      
      .stat-number {
        font-size: 2rem;
        font-weight: bold;
        color: #4ade80;
        margin-bottom: 8px;
      }
      
      .stat-label {
        font-size: 0.9rem;
        color: rgba(255, 255, 255, 0.7);
        text-transform: uppercase;
        letter-spacing: 0.5px;
      }
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .multi-lawyer-calendar-container {
    padding: 16px;
  }
  
  .calendar-header {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }
  
  .lawyers-grid {
    grid-template-columns: 1fr;
  }
  
  .nav-controls {
    flex-direction: column;
    gap: 16px;
  }
  
  .week-header {
    grid-template-columns: repeat(7, 1fr);
    gap: 4px;
    
    .day-header {
      padding: 8px 4px;
      
      .day-name {
        font-size: 0.7rem;
      }
      
      .day-number {
        font-size: 1rem;
      }
    }
  }
  
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}
