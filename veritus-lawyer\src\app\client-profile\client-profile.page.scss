.client-toolbar {
  --background: #ffffff;
  --color: #000000;
  --border-color: #e5e5e5;
}

.client-title {
  font-size: 18px;
  font-weight: 600;
  color: #000000;
}

.client-profile-content {
  --background: #f8f9fa;
}

.profile-container {
  padding: 20px;
  min-height: 100vh;
}

// Profile Header
.profile-header {
  text-align: center;
  margin-bottom: 40px;
  background: #ffffff;
  border-radius: 12px;
  padding: 30px 20px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);
}

.avatar-section {
  margin-bottom: 20px;
}

.avatar-placeholder {
  width: 100px;
  height: 100px;
  border-radius: 50%;
  background: linear-gradient(135deg, #0A49FF 0%, #4A90E2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
  box-shadow: 0 4px 20px rgba(10, 73, 255, 0.3);
}

.avatar-icon {
  font-size: 48px;
  color: #ffffff;
}

.user-details {
  margin-bottom: 20px;
}

.user-name {
  font-size: 24px;
  font-weight: 700;
  color: #000000;
  margin: 0 0 8px 0;
}

.user-email {
  font-size: 16px;
  color: #666666;
  margin: 0 0 4px 0;
}

.user-role {
  font-size: 14px;
  color: #0A49FF;
  font-weight: 600;
  margin: 0;
}

// Profile Options
.profile-options {
  background: #ffffff;
  border-radius: 12px;
  overflow: hidden;
  margin-bottom: 30px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);
}

.option-item {
  display: flex;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  transition: all 0.2s ease;
  
  &:last-child {
    border-bottom: none;
  }
  
  &:hover {
    background: #f8f9fa;
  }
  
  &:active {
    background: #e9ecef;
    transform: scale(0.98);
  }
}

.option-icon {
  width: 44px;
  height: 44px;
  background: #f0f0f0;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  
  ion-icon {
    font-size: 20px;
    color: #0A49FF;
  }
}

.option-content {
  flex: 1;
}

.option-title {
  font-size: 16px;
  font-weight: 600;
  color: #000000;
  margin: 0 0 4px 0;
}

.option-description {
  font-size: 14px;
  color: #666666;
  margin: 0;
}

.option-arrow {
  font-size: 20px;
  color: #888888;
}

// Logout Section
.logout-section {
  text-align: center;
}

.logout-btn {
  background: #dc3545;
  color: #ffffff;
  border: none;
  border-radius: 12px;
  padding: 16px 32px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  width: 100%;
  max-width: 200px;
  margin: 0 auto;
  
  &:hover {
    background: #c82333;
  }
  
  &:active {
    transform: scale(0.95);
  }
}

.logout-icon {
  font-size: 18px;
}

// Responsive Design
@media (max-width: 375px) {
  .profile-container {
    padding: 16px;
  }
  
  .profile-header {
    padding: 24px 16px;
  }
  
  .user-name {
    font-size: 20px;
  }
  
  .option-item {
    padding: 14px 16px;
  }
}
