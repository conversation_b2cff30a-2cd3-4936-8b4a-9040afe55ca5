import { Component, OnInit, Input, Output, EventEmitter } from '@angular/core';
import { FirebaseService, LawyerProfile } from '../../services/firebase.service';
import { LawyerAvailability, TimeSlot } from '../../models/scheduling.models';
import { <PERSON><PERSON><PERSON>ontroller, ToastController, ModalController } from '@ionic/angular';

@Component({
  selector: 'app-lawyer-availability',
  templateUrl: './lawyer-availability.component.html',
  styleUrls: ['./lawyer-availability.component.scss']
})
export class LawyerAvailabilityComponent implements OnInit {
  @Input() selectedLawyer: LawyerProfile | null = null;
  @Input() selectedDate: string = new Date().toISOString().split('T')[0];
  @Output() availabilityChanged = new EventEmitter<void>();

  availability: LawyerAvailability | null = null;
  timeSlots: TimeSlot[] = [];
  isLoading = false;
  isEditing = false;

  // Time slot options (9 AM to 6 PM in 30-minute intervals)
  availableTimeOptions = [
    '09:00', '09:30', '10:00', '10:30', '11:00', '11:30',
    '12:00', '12:30', '13:00', '13:30', '14:00', '14:30',
    '15:00', '15:30', '16:00', '16:30', '17:00', '17:30'
  ];

  constructor(
    private firebaseService: FirebaseService,
    private alertController: AlertController,
    private toastController: ToastController,
    private modalController: ModalController
  ) { }

  ngOnInit() {
    this.loadAvailability();
  }

  ngOnChanges() {
    if (this.selectedLawyer && this.selectedDate) {
      this.loadAvailability();
    }
  }

  async loadAvailability() {
    if (!this.selectedLawyer) return;

    this.isLoading = true;
    try {
      const availabilities = await this.firebaseService.getLawyerAvailability(
        this.selectedLawyer.uid,
        this.selectedDate
      );

      this.availability = availabilities.length > 0 ? availabilities[0] : null;
      this.generateTimeSlots();
    } catch (error) {
      console.error('Error loading availability:', error);
      // Create mock availability for testing
      this.availability = {
        lawyerId: this.selectedLawyer.uid,
        date: this.selectedDate,
        timeSlots: ['09:00', '10:00', '14:00', '15:00', '16:00'],
        createdBy: 'mock',
        createdAt: new Date(),
        updatedAt: new Date()
      };
      this.generateTimeSlots();
      this.showToast('Using mock availability data', 'warning');
    } finally {
      this.isLoading = false;
    }
  }

  generateTimeSlots() {
    this.timeSlots = this.availableTimeOptions.map(time => ({
      time,
      isAvailable: this.availability?.timeSlots.includes(time) || false,
      isBooked: false, // Will be updated when we load appointments
      appointmentId: undefined
    }));
  }

  toggleTimeSlot(timeSlot: TimeSlot) {
    if (!this.isEditing) return;
    
    timeSlot.isAvailable = !timeSlot.isAvailable;
  }

  async startEditing() {
    if (!this.selectedLawyer) return;

    // Check permission
    const currentUser = this.firebaseService.getCurrentUser();
    if (!currentUser) return;

    const hasPermission = await this.firebaseService.checkSecretaryPermission(
      currentUser.uid,
      this.selectedLawyer.uid,
      'canManageCalendar'
    );

    if (!hasPermission) {
      this.showToast('You do not have permission to manage this lawyer\'s calendar', 'warning');
      return;
    }

    this.isEditing = true;
  }

  async saveAvailability() {
    if (!this.selectedLawyer || !this.isEditing) return;

    const currentUser = this.firebaseService.getCurrentUser();
    if (!currentUser) return;

    this.isLoading = true;
    try {
      const selectedTimeSlots = this.timeSlots
        .filter(slot => slot.isAvailable)
        .map(slot => slot.time);

      if (this.availability) {
        // Update existing availability
        await this.firebaseService.updateLawyerAvailability(
          this.availability.id!,
          { timeSlots: selectedTimeSlots },
          currentUser.uid
        );
      } else {
        // Create new availability
        await this.firebaseService.createLawyerAvailability({
          lawyerId: this.selectedLawyer.uid,
          date: this.selectedDate,
          timeSlots: selectedTimeSlots,
          createdBy: currentUser.uid,
          createdAt: new Date(),
          updatedAt: new Date()
        });
      }

      this.isEditing = false;
      this.availabilityChanged.emit();
      this.showToast('Availability updated successfully', 'success');
      await this.loadAvailability();
    } catch (error) {
      console.error('Error saving availability:', error);
      this.showToast('Error saving availability', 'danger');
    } finally {
      this.isLoading = false;
    }
  }

  cancelEditing() {
    this.isEditing = false;
    this.generateTimeSlots(); // Reset to original state
  }

  async deleteAvailability() {
    if (!this.availability || !this.selectedLawyer) return;

    const alert = await this.alertController.create({
      header: 'Delete Availability',
      message: 'Are you sure you want to delete the availability for this date?',
      buttons: [
        {
          text: 'Cancel',
          role: 'cancel'
        },
        {
          text: 'Delete',
          role: 'destructive',
          handler: async () => {
            await this.performDelete();
          }
        }
      ]
    });

    await alert.present();
  }

  private async performDelete() {
    if (!this.availability) return;

    const currentUser = this.firebaseService.getCurrentUser();
    if (!currentUser) return;

    this.isLoading = true;
    try {
      await this.firebaseService.deleteLawyerAvailability(
        this.availability.id!,
        currentUser.uid
      );

      this.availability = null;
      this.generateTimeSlots();
      this.availabilityChanged.emit();
      this.showToast('Availability deleted successfully', 'success');
    } catch (error) {
      console.error('Error deleting availability:', error);
      this.showToast('Error deleting availability', 'danger');
    } finally {
      this.isLoading = false;
    }
  }

  async addCustomTimeSlot() {
    const alert = await this.alertController.create({
      header: 'Add Custom Time Slot',
      inputs: [
        {
          name: 'time',
          type: 'time',
          placeholder: 'Select time'
        }
      ],
      buttons: [
        {
          text: 'Cancel',
          role: 'cancel'
        },
        {
          text: 'Add',
          handler: (data) => {
            if (data.time) {
              this.addTimeSlot(data.time);
            }
          }
        }
      ]
    });

    await alert.present();
  }

  private addTimeSlot(time: string) {
    // Convert time format if needed (e.g., from "14:30" to "14:30")
    const formattedTime = time.substring(0, 5); // Ensure HH:MM format

    // Check if time slot already exists
    const existingSlot = this.timeSlots.find(slot => slot.time === formattedTime);
    if (existingSlot) {
      existingSlot.isAvailable = true;
      return;
    }

    // Add new time slot and sort
    this.timeSlots.push({
      time: formattedTime,
      isAvailable: true,
      isBooked: false,
      appointmentId: undefined
    });

    // Sort time slots
    this.timeSlots.sort((a, b) => a.time.localeCompare(b.time));
  }

  private async showToast(message: string, color: string) {
    const toast = await this.toastController.create({
      message,
      duration: 3000,
      color,
      position: 'top'
    });
    await toast.present();
  }

  getTimeSlotClass(timeSlot: TimeSlot): string {
    if (timeSlot.isBooked) return 'time-slot booked';
    if (timeSlot.isAvailable) return 'time-slot available';
    return 'time-slot unavailable';
  }

  isTimeSlotDisabled(timeSlot: TimeSlot): boolean {
    return timeSlot.isBooked || !this.isEditing;
  }

  formatTime(time: string): string {
    const [hours, minutes] = time.split(':');
    const hour = parseInt(hours);
    const ampm = hour >= 12 ? 'PM' : 'AM';
    const displayHour = hour > 12 ? hour - 12 : hour === 0 ? 12 : hour;
    return `${displayHour}:${minutes} ${ampm}`;
  }

  get hasAvailableSlots(): boolean {
    return this.timeSlots.some(slot => slot.isAvailable);
  }

  get availableSlotsCount(): number {
    return this.timeSlots.filter(slot => slot.isAvailable).length;
  }
}
