<ion-content class="auth-content veritus-gradient-auth">
  <div class="auth-container veritus-safe-area-top">
    <!-- Logo -->
    <div class="auth-logo">
      <h1 class="veritus-text-2xl veritus-font-bold veritus-text-white">Veritus</h1>
    </div>

    <div *ngIf="!isSubmitted">
      <!-- Forgot Password Form -->
      <form [formGroup]="forgotPasswordForm" (ngSubmit)="onSubmit()" class="auth-form">
        <div class="form-description">
          <p class="veritus-text-sm veritus-text-white">
            Enter your email address and we'll send you a link to reset your password.
          </p>
        </div>

        <div class="form-group">
          <input 
            type="email" 
            formControlName="email"
            placeholder="Email"
            class="veritus-input-auth">
        </div>

        <button 
          type="submit"
          class="veritus-btn-primary auth-submit-btn"
          [disabled]="!forgotPasswordForm.valid">
          Send Reset Link
        </button>
      </form>
    </div>

    <div *ngIf="isSubmitted" class="success-message">
      <ion-icon name="checkmark-circle" class="success-icon"></ion-icon>
      <h2 class="success-title veritus-text-xl veritus-font-semibold veritus-text-white">
        Check your email
      </h2>
      <p class="success-text veritus-text-sm veritus-text-white">
        We've sent a password reset link to your email address.
      </p>
    </div>

    <!-- Back to Sign In -->
    <div class="auth-footer">
      <button type="button" class="link-button veritus-text-sm veritus-text-gold" (click)="goToSignIn()">
        Back to Sign In
      </button>
    </div>
  </div>
</ion-content>
