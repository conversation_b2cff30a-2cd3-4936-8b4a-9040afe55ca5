.lawyer-card {
  margin-bottom: var(--veritus-spacing-md);
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  }
}

.lawyer-header {
  display: flex;
  align-items: flex-start;
  margin-bottom: var(--veritus-spacing-md);
  gap: var(--veritus-spacing-md);
}

.lawyer-info {
  flex: 1;
}

.lawyer-name {
  margin: 0 0 var(--veritus-spacing-xs) 0;
  color: var(--veritus-gray-dark);
}

.lawyer-firm {
  margin: 0 0 var(--veritus-spacing-xs) 0;
  font-weight: var(--veritus-font-weight-medium);
}

.lawyer-location {
  margin: 0;
  display: flex;
  align-items: center;
  gap: 4px;

  ion-icon {
    font-size: 14px;
  }
}

.lawyer-rating {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 4px;
}

.rating-stars {
  display: flex;
  gap: 2px;

  ion-icon {
    font-size: 16px;
    color: var(--veritus-gray-light);

    &.filled {
      color: var(--veritus-gold);
    }
  }
}

.rating-value {
  font-weight: var(--veritus-font-weight-semibold);
  color: var(--veritus-gray-dark);
}

.lawyer-specialties {
  display: flex;
  flex-wrap: wrap;
  gap: var(--veritus-spacing-xs);
  margin-bottom: var(--veritus-spacing-md);
}

.specialty-tag {
  background: var(--veritus-primary-light);
  color: var(--veritus-primary);
  padding: 4px 8px;
  border-radius: var(--veritus-border-radius-sm);
  font-weight: var(--veritus-font-weight-medium);
}

.lawyer-bio {
  margin: 0 0 var(--veritus-spacing-md) 0;
  line-height: 1.5;
}

.lawyer-actions {
  display: flex;
  gap: var(--veritus-spacing-sm);
  justify-content: flex-end;
}
