<ion-content class="client-home-content">
  <div class="home-container">
    
    <!-- Header Section -->
    <div class="header-section">
      <div class="greeting-row">
        <div class="greeting-text">
          <h1 class="greeting-title">Hello, {{ userName }}!</h1>
        </div>
        <div class="notification-bell">
          <ion-icon name="notifications-outline"></ion-icon>
        </div>
      </div>
    </div>

    <!-- Hero Card -->
    <div class="hero-card">
      <div class="hero-image">
        <div class="hero-placeholder">
          <ion-icon name="briefcase" class="hero-icon"></ion-icon>
        </div>
      </div>
      <div class="hero-content">
        <h2 class="hero-title">Legal Services at Your Fingertips</h2>
        <p class="hero-subtitle">Connect with qualified lawyers and manage your legal needs</p>
      </div>
    </div>

    <!-- Search Section -->
    <div class="search-section">
      <div class="search-input" (click)="onSearchLawyers()">
        <ion-icon name="search" class="search-icon"></ion-icon>
        <span class="search-placeholder">Search for lawyers</span>
      </div>
    </div>

    <!-- My Lawyer Section -->
    <div class="section" *ngIf="myLawyer">
      <h3 class="section-title">My Lawyer</h3>
      <div class="lawyer-card" (click)="onViewLawyerDetails()">
        <div class="lawyer-avatar">
          <div class="avatar-placeholder">
            <ion-icon name="person" class="avatar-icon"></ion-icon>
          </div>
        </div>
        <div class="lawyer-info">
          <h4 class="lawyer-name">{{ myLawyer.name }}</h4>
          <p class="lawyer-firm">{{ myLawyer.firm }}</p>
          <p class="next-appointment" *ngIf="myLawyer.nextAppointment">
            Next: {{ myLawyer.nextAppointment }}
          </p>
        </div>
        <div class="lawyer-action">
          <span class="view-details">View Details</span>
          <ion-icon name="chevron-forward"></ion-icon>
        </div>
      </div>
    </div>

    <!-- Track Your Case Section -->
    <div class="section" *ngIf="recentCase">
      <h3 class="section-title">Track Your Case</h3>
      <div class="case-card" (click)="onTrackCase()">
        <div class="case-info">
          <h4 class="case-type">{{ recentCase.type }}</h4>
          <p class="case-lawyer">{{ recentCase.lawyerName }}</p>
          <div class="case-status">
            <span class="status-badge ongoing">{{ recentCase.status | titlecase }}</span>
          </div>
        </div>
        <div class="case-action">
          <span class="track-progress">Track Progress</span>
          <ion-icon name="chevron-forward"></ion-icon>
        </div>
      </div>
    </div>

    <!-- Legal Document Templates -->
    <div class="section">
      <div class="section-header">
        <h3 class="section-title">Legal Document Templates</h3>
        <span class="view-all" (click)="onViewAllDocuments()">View All</span>
      </div>
      
      <div class="templates-grid">
        <div class="template-card" *ngFor="let template of documentTemplates" (click)="onGenerateDocument(template)">
          <div class="template-icon">
            <ion-icon [name]="template.icon"></ion-icon>
          </div>
          <div class="template-content">
            <h4 class="template-title">{{ template.title }}</h4>
            <p class="template-description">{{ template.description }}</p>
          </div>
          <div class="template-action">
            <span class="get-started">Get Started</span>
            <ion-icon name="chevron-forward"></ion-icon>
          </div>
        </div>
      </div>
    </div>

    <!-- Quick Actions -->
    <div class="section">
      <h3 class="section-title">Quick Actions</h3>
      <div class="quick-actions">
        <div class="action-item" (click)="onSearchLawyers()">
          <div class="action-icon">
            <ion-icon name="search"></ion-icon>
          </div>
          <span class="action-label">Find Lawyer</span>
        </div>
        
        <div class="action-item" routerLink="/track-case">
          <div class="action-icon">
            <ion-icon name="briefcase"></ion-icon>
          </div>
          <span class="action-label">Track Case</span>
        </div>
        
        <div class="action-item" routerLink="/schedule-appointment">
          <div class="action-icon">
            <ion-icon name="calendar"></ion-icon>
          </div>
          <span class="action-label">Schedule</span>
        </div>
        
        <div class="action-item" (click)="onViewAllDocuments()">
          <div class="action-icon">
            <ion-icon name="document"></ion-icon>
          </div>
          <span class="action-label">Documents</span>
        </div>
      </div>
    </div>

    <!-- Download Documents Section -->
    <div class="section">
      <div class="download-documents-card" (click)="onDownloadDocuments()">
        <div class="download-icon">
          <ion-icon name="cloud-download"></ion-icon>
        </div>
        <div class="download-content">
          <h4 class="download-title">Download Documents</h4>
          <p class="download-subtitle">Access your case files and documents</p>
        </div>
        <div class="download-action">
          <ion-icon name="chevron-forward"></ion-icon>
        </div>
      </div>
    </div>

  </div>
</ion-content>
