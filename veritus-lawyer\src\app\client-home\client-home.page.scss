.client-home-content {
  --background: #f8f9fa;
}

.home-container {
  padding: 0 20px 20px 20px;
  padding-top: env(safe-area-inset-top, 20px);
  min-height: 100vh;
}

// Header Section
.header-section {
  margin-bottom: 24px;
}

.greeting-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 20px;
}

.greeting-title {
  font-size: 32px;
  font-weight: 700;
  color: #000000;
  margin: 0;
  line-height: 1.2;
}

.notification-bell {
  width: 44px;
  height: 44px;
  background: #ffffff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e5e5;
  
  ion-icon {
    font-size: 24px;
    color: #333333;
  }
}

// Hero Card
.hero-card {
  background: #ffffff;
  border-radius: 24px;
  overflow: hidden;
  margin-bottom: 24px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.hero-image {
  height: 160px;
  overflow: hidden;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

.hero-placeholder {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #0A49FF 0%, #4A90E2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
}

.hero-icon {
  font-size: 48px;
  color: #ffffff;
}

.hero-content {
  padding: 20px;
  
  .hero-title {
    font-size: 20px;
    font-weight: 600;
    color: #000000;
    margin: 0 0 8px 0;
  }
  
  .hero-subtitle {
    font-size: 14px;
    color: #666666;
    margin: 0;
    line-height: 1.4;
  }
}

// Search Section
.search-section {
  margin-bottom: 32px;
}

.search-input {
  background: #ffffff;
  border-radius: 8px;
  padding: 16px;
  display: flex;
  align-items: center;
  gap: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border: 1px solid #e5e5e5;
  cursor: pointer;
  transition: all 0.2s ease;
  
  &:active {
    transform: scale(0.98);
  }
}

.search-icon {
  font-size: 20px;
  color: #888888;
}

.search-placeholder {
  font-size: 16px;
  color: #888888;
  flex: 1;
}

// Section Styling
.section {
  margin-bottom: 32px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.section-title {
  font-size: 18px;
  font-weight: 600;
  color: #000000;
  margin: 0;
}

.view-all {
  font-size: 14px;
  color: #0A49FF;
  font-weight: 500;
  cursor: pointer;
}

// Case Card
.case-card {
  background: #ffffff;
  border-radius: 12px;
  padding: 16px;
  display: flex;
  align-items: center;
  gap: 16px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);
  border: 1px solid #e5e5e5;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-bottom: 16px;

  &:active {
    transform: scale(0.98);
  }
}

.case-info {
  flex: 1;
}

.case-type {
  font-size: 16px;
  font-weight: 600;
  color: #000000;
  margin: 0 0 4px 0;
}

.case-lawyer {
  font-size: 14px;
  color: #666666;
  margin: 0 0 8px 0;
}

.case-status {
  display: flex;
  align-items: center;
}

.status-badge {
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;

  &.ongoing {
    background: #d4edda;
    color: #155724;
  }
}

.case-action {
  display: flex;
  align-items: center;
  gap: 4px;
}

.track-progress {
  font-size: 14px;
  color: #0A49FF;
  font-weight: 500;
}

// Lawyer Card
.lawyer-card {
  background: #ffffff;
  border-radius: 12px;
  padding: 16px;
  display: flex;
  align-items: center;
  gap: 16px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);
  border: 1px solid #e5e5e5;
  cursor: pointer;
  transition: all 0.2s ease;

  &:active {
    transform: scale(0.98);
  }
}

.lawyer-avatar {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  overflow: hidden;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .avatar-placeholder {
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #B88A42 0%, #D4AF37 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;

    .avatar-icon {
      font-size: 24px;
      color: #ffffff;
    }
  }
}

.lawyer-info {
  flex: 1;
}

.lawyer-name {
  font-size: 18px;
  font-weight: 600;
  color: #000000;
  margin: 0 0 4px 0;
}

.lawyer-firm {
  font-size: 14px;
  color: #666666;
  margin: 0 0 4px 0;
}

.next-appointment {
  font-size: 14px;
  color: #888888;
  margin: 0;
}

.lawyer-action {
  display: flex;
  align-items: center;
  gap: 4px;
}

.view-details {
  font-size: 14px;
  color: #0A49FF;
  font-weight: 500;
}

// Templates Grid
.templates-grid {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.template-card {
  background: #ffffff;
  border-radius: 12px;
  padding: 16px;
  display: flex;
  align-items: center;
  gap: 16px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);
  border: 1px solid #e5e5e5;
  cursor: pointer;
  transition: all 0.2s ease;
  
  &:active {
    transform: scale(0.98);
  }
}

.template-icon {
  width: 44px;
  height: 44px;
  background: linear-gradient(135deg, #B88A42 0%, #D4AF37 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  
  ion-icon {
    font-size: 20px;
    color: #ffffff;
  }
}

.template-content {
  flex: 1;
}

.template-title {
  font-size: 16px;
  font-weight: 600;
  color: #000000;
  margin: 0 0 4px 0;
}

.template-description {
  font-size: 12px;
  color: #666666;
  margin: 0;
}

.template-action {
  display: flex;
  align-items: center;
  gap: 4px;
}

.get-started {
  font-size: 14px;
  color: #888888;
  font-weight: 500;
}

// Quick Actions
.quick-actions {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16px;
}

.action-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  padding: 16px 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  
  &:active {
    transform: scale(0.95);
  }
}

.action-icon {
  width: 48px;
  height: 48px;
  background: #f0f0f0;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  
  ion-icon {
    font-size: 24px;
    color: #0A49FF;
  }
}

.action-label {
  font-size: 12px;
  color: #333333;
  font-weight: 500;
  text-align: center;
}

// Download Documents Card
.download-documents-card {
  background: #ffffff;
  border-radius: 12px;
  padding: 16px;
  display: flex;
  align-items: center;
  gap: 16px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);
  border: 1px solid #e5e5e5;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  }

  &:active {
    transform: scale(0.98);
  }
}

.download-icon {
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, #0A49FF 0%, #4A90E2 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;

  ion-icon {
    font-size: 24px;
    color: #ffffff;
  }
}

.download-content {
  flex: 1;
}

.download-title {
  font-size: 16px;
  font-weight: 600;
  color: #000000;
  margin: 0 0 4px 0;
}

.download-subtitle {
  font-size: 12px;
  color: #666666;
  margin: 0;
}

.download-action {
  ion-icon {
    font-size: 20px;
    color: #888888;
  }
}

// Responsive Design
@media (max-width: 375px) {
  .home-container {
    padding: 0 16px 16px 16px;
  }
  
  .greeting-title {
    font-size: 28px;
  }
  
  .quick-actions {
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
  }
}
