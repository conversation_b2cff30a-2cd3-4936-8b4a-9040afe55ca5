.appointment-management-container {
  padding: 20px;
  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
  min-height: 100vh;
  color: white;
}

.management-header {
  margin-bottom: 32px;
  
  .header-title {
    margin-bottom: 24px;
    
    h2 {
      margin: 0 0 8px 0;
      font-size: 1.8rem;
      font-weight: 600;
      color: #ffffff;
    }
    
    p {
      margin: 0;
      color: rgba(255, 255, 255, 0.7);
      font-size: 1rem;
    }
  }
  
  .stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 16px;
    
    .stat-card {
      background: rgba(255, 255, 255, 0.1);
      border-radius: 12px;
      padding: 20px;
      text-align: center;
      border: 1px solid rgba(255, 255, 255, 0.1);
      
      .stat-number {
        font-size: 2rem;
        font-weight: bold;
        color: #4ade80;
        margin-bottom: 8px;
      }
      
      .stat-label {
        font-size: 0.9rem;
        color: rgba(255, 255, 255, 0.7);
        text-transform: uppercase;
        letter-spacing: 0.5px;
      }
    }
  }
}

.filters-section {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 24px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  
  .filters-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    
    h3 {
      margin: 0;
      color: #ffffff;
      font-size: 1.3rem;
    }
  }
  
  .filter-group {
    margin-bottom: 24px;
    
    &:last-child {
      margin-bottom: 0;
    }
    
    h4 {
      margin: 0 0 12px 0;
      color: rgba(255, 255, 255, 0.9);
      font-size: 1rem;
      font-weight: 500;
    }
    
    .date-range-inputs {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 16px;
      
      .date-input-group {
        ion-label {
          color: rgba(255, 255, 255, 0.8);
          font-size: 0.9rem;
          margin-bottom: 8px;
          display: block;
        }
        
        .date-filter {
          background: rgba(255, 255, 255, 0.1);
          border-radius: 8px;
          border: 1px solid rgba(255, 255, 255, 0.2);
          padding: 12px;
          color: white;
        }
      }
    }
    
    .lawyer-filter {
      background: rgba(255, 255, 255, 0.1);
      border-radius: 8px;
      border: 1px solid rgba(255, 255, 255, 0.2);
      padding: 12px;
      color: white;
      width: 100%;
    }
    
    .status-chips,
    .type-chips {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;
      
      .filter-chip {
        cursor: pointer;
        transition: all 0.3s ease;
        
        &:hover {
          transform: translateY(-1px);
        }
      }
    }
  }
}

.view-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding: 16px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  
  .view-mode-toggle {
    ion-segment {
      background: rgba(255, 255, 255, 0.1);
      border-radius: 8px;
    }
  }
  
  .sort-controls {
    display: flex;
    align-items: center;
    gap: 8px;
    
    .sort-select {
      background: rgba(255, 255, 255, 0.1);
      border-radius: 8px;
      padding: 8px 12px;
      color: white;
      min-width: 120px;
    }
    
    .sort-order-btn {
      --color: rgba(255, 255, 255, 0.8);
    }
  }
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px;
  
  ion-spinner {
    margin-bottom: 16px;
  }
  
  p {
    color: rgba(255, 255, 255, 0.7);
    margin: 0;
  }
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px;
  text-align: center;
  
  .empty-icon {
    font-size: 4rem;
    color: rgba(255, 255, 255, 0.3);
    margin-bottom: 24px;
  }
  
  h3 {
    margin: 0 0 12px 0;
    color: rgba(255, 255, 255, 0.8);
    font-size: 1.4rem;
  }
  
  p {
    margin: 0;
    color: rgba(255, 255, 255, 0.6);
    font-size: 1rem;
  }
}

.appointments-list {
  .appointment-item {
    background: rgba(255, 255, 255, 0.08);
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 16px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
    
    &:hover {
      background: rgba(255, 255, 255, 0.12);
      transform: translateY(-2px);
      box-shadow: 0 8px 24px rgba(0, 0, 0, 0.3);
    }
    
    &.urgent {
      border-left: 4px solid #ef4444;
    }
    
    &.past {
      opacity: 0.7;
    }
    
    .appointment-main {
      display: grid;
      grid-template-columns: 2fr 1fr 1fr;
      gap: 20px;
      align-items: center;
      margin-bottom: 16px;
      
      .appointment-info {
        .client-name {
          font-size: 1.2rem;
          font-weight: 600;
          color: #ffffff;
          margin-bottom: 4px;
        }
        
        .appointment-type {
          color: #4ade80;
          font-size: 0.9rem;
          margin-bottom: 4px;
        }
        
        .lawyer-name {
          color: rgba(255, 255, 255, 0.7);
          font-size: 0.9rem;
        }
      }
      
      .appointment-datetime {
        text-align: center;
        
        .date {
          color: #ffffff;
          font-weight: 500;
          margin-bottom: 4px;
        }
        
        .time-until {
          color: rgba(255, 255, 255, 0.6);
          font-size: 0.8rem;
        }
      }
      
      .appointment-status {
        text-align: right;
        
        .status-chip {
          margin-bottom: 8px;
        }
        
        .urgent-chip {
          font-size: 0.8rem;
        }
      }
    }
    
    .appointment-actions {
      display: flex;
      gap: 8px;
      flex-wrap: wrap;
    }
    
    .appointment-remarks {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-top: 12px;
      padding: 12px;
      background: rgba(255, 255, 255, 0.05);
      border-radius: 8px;
      color: rgba(255, 255, 255, 0.8);
      font-size: 0.9rem;
      
      ion-icon {
        color: #3b82f6;
      }
    }
  }
}

.appointments-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 20px;
  
  .appointment-card {
    background: rgba(255, 255, 255, 0.08);
    border-radius: 12px;
    padding: 20px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
    
    &:hover {
      background: rgba(255, 255, 255, 0.12);
      transform: translateY(-4px);
      box-shadow: 0 12px 32px rgba(0, 0, 0, 0.3);
    }
    
    &.urgent {
      border-left: 4px solid #ef4444;
    }
    
    &.past {
      opacity: 0.7;
    }
    
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: 16px;
      
      .client-name {
        font-size: 1.1rem;
        font-weight: 600;
        color: #ffffff;
      }
    }
    
    .card-content {
      margin-bottom: 16px;
      
      .appointment-type {
        color: #4ade80;
        font-size: 0.9rem;
        margin-bottom: 8px;
      }
      
      .lawyer-name {
        color: rgba(255, 255, 255, 0.7);
        font-size: 0.9rem;
        margin-bottom: 8px;
      }
      
      .appointment-datetime {
        color: #ffffff;
        font-weight: 500;
      }
    }
    
    .card-actions {
      display: flex;
      justify-content: flex-end;
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .appointment-management-container {
    padding: 16px;
  }
  
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .date-range-inputs {
    grid-template-columns: 1fr;
  }
  
  .view-controls {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
  
  .appointment-main {
    grid-template-columns: 1fr;
    gap: 12px;
    text-align: left;
    
    .appointment-datetime,
    .appointment-status {
      text-align: left;
    }
  }
  
  .appointments-grid {
    grid-template-columns: 1fr;
  }
}
