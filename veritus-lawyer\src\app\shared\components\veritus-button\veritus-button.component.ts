import { Component, Input, Output, EventEmitter } from '@angular/core';

@Component({
  selector: 'app-veritus-button',
  templateUrl: './veritus-button.component.html',
  styleUrls: ['./veritus-button.component.scss'],
  standalone: false,
})
export class VeritusButtonComponent {
  @Input() variant: 'primary' | 'outline-gold' | 'text' = 'primary';
  @Input() size: 'sm' | 'md' | 'lg' = 'md';
  @Input() disabled: boolean = false;
  @Input() fullWidth: boolean = false;
  @Output() clicked = new EventEmitter<void>();

  onClick() {
    if (!this.disabled) {
      this.clicked.emit();
    }
  }
}
