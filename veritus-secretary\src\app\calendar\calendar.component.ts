import { Component, OnInit } from '@angular/core';
import { ModalController, ToastController, AlertController } from '@ionic/angular';
import { FirebaseService, LawyerProfile } from '../services/firebase.service';
import { EnhancedAppointment } from '../models/scheduling.models';

interface Appointment {
  id: string;
  clientName: string;
  type: string;
  time: string;
  date: Date;
  status: 'confirmed' | 'pending' | 'cancelled';
}

interface CalendarDay {
  date: number;
  isCurrentMonth: boolean;
  isToday: boolean;
  isSelected: boolean;
  fullDate: Date;
}

@Component({
  selector: 'app-calendar',
  template: `
    <div class="calendar-container">
      <div class="calendar-header">
        <div class="header-left">
          <h1>Scheduling</h1>
          <div class="lawyer-selector" *ngIf="linkedLawyers.length > 0">
            <label>Managing appointments for:</label>
            <select [(ngModel)]="selectedLawyer" (change)="onLawyerChange()" class="lawyer-select">
              <option *ngFor="let lawyer of linkedLawyers" [ngValue]="lawyer">
                {{ lawyer.name }} ({{ lawyer.rollNumber }})
              </option>
            </select>
          </div>
        </div>
        <div class="header-right">
          <div class="workflow-info">
            <small>📋 Secretary creates → 🔄 Lawyer approves → ✅ Confirmed</small>
          </div>
          <button mat-raised-button color="primary" (click)="openNewBookingDialog()" class="header-new-booking-btn" [disabled]="isLoading">
            <mat-icon>add</mat-icon>
            {{ isLoading ? 'Creating...' : 'New Booking Request' }}
          </button>
        </div>
      </div>

      <div class="calendar-content">
        <!-- Calendar Widget -->
        <div class="calendar-widget">
          <div class="calendar-nav">
            <button mat-icon-button (click)="previousMonth()">
              <mat-icon>chevron_left</mat-icon>
            </button>
            <h3>{{ currentMonth }}</h3>
            <button mat-icon-button (click)="nextMonth()">
              <mat-icon>chevron_right</mat-icon>
            </button>
          </div>

          <div class="calendar-grid">
            <div class="calendar-header-row">
              <div class="day-header" *ngFor="let day of weekDays">{{ day }}</div>
            </div>
            <div class="calendar-body">
              <div class="calendar-row" *ngFor="let week of calendarWeeks">
                <div 
                  class="calendar-day" 
                  *ngFor="let day of week"
                  [class.other-month]="!day.isCurrentMonth"
                  [class.today]="day.isToday"
                  [class.selected]="day.isSelected"
                  (click)="selectDate(day)">
                  {{ day.date }}
                </div>
              </div>
            </div>
          </div>

          <!-- Selected Date Appointments -->
          <div class="selected-date-section" *ngIf="selectedDate">
            <h4>Booking on {{ selectedDate | date:'fullDate' }}</h4>
            <div class="appointments-list">
              <div 
                class="appointment-item" 
                *ngFor="let appointment of getAppointmentsForDate(selectedDate)">
                <div class="appointment-time">{{ appointment.time }}</div>
                <div class="appointment-details">
                  <div class="appointment-type">{{ appointment.type }}</div>
                  <div class="appointment-client">{{ appointment.clientName }}</div>
                </div>
                <div class="appointment-status" [class]="appointment.status">
                  {{ appointment.status }}
                </div>
              </div>
            </div>
            <button mat-raised-button color="primary" class="new-booking-btn" (click)="openNewBookingDialog()">
              + New Booking
            </button>
          </div>
        </div>

        <!-- Upcoming Appointments -->
        <div class="upcoming-appointments">
          <h3>Upcoming Appointments</h3>
          <div class="appointment-card" *ngFor="let appointment of upcomingAppointments">
            <div class="appointment-date">
              <div class="date-day">{{ appointment.date | date:'dd' }}</div>
              <div class="date-month">{{ appointment.date | date:'MMM' }}</div>
            </div>
            <div class="appointment-info">
              <div class="appointment-title">{{ appointment.type }}</div>
              <div class="appointment-client">{{ appointment.clientName }}</div>
              <div class="appointment-time">{{ appointment.time }}</div>
            </div>
            <div class="appointment-actions">
              <button mat-icon-button (click)="editAppointment(appointment)">
                <mat-icon>edit</mat-icon>
              </button>
              <button mat-icon-button color="warn" (click)="deleteAppointment(appointment)">
                <mat-icon>delete</mat-icon>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .calendar-container {
      padding: 32px;
      background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
      min-height: calc(100vh - 64px);
      position: relative;
    }

    .calendar-container::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background:
        radial-gradient(circle at 30% 30%, rgba(196, 154, 86, 0.08) 0%, transparent 50%),
        radial-gradient(circle at 70% 70%, rgba(30, 41, 59, 0.03) 0%, transparent 50%);
      pointer-events: none;
    }

    .calendar-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: 32px;
      position: relative;
      z-index: 1;
      gap: 20px;
    }

    .header-left {
      flex: 1;
    }

    .header-right {
      display: flex;
      flex-direction: column;
      align-items: flex-end;
      gap: 8px;
    }

    .lawyer-selector {
      margin-top: 12px;
      display: flex;
      flex-direction: column;
      gap: 8px;
    }

    .lawyer-selector label {
      font-size: 14px;
      color: #64748b;
      font-weight: 500;
    }

    .lawyer-select {
      padding: 8px 12px;
      border: 2px solid #e2e8f0;
      border-radius: 8px;
      background: white;
      font-size: 14px;
      color: #1e293b;
      min-width: 250px;
      transition: all 0.3s ease;
    }

    .lawyer-select:focus {
      outline: none;
      border-color: #C49A56;
      box-shadow: 0 0 0 3px rgba(196, 154, 86, 0.1);
    }

    .workflow-info {
      font-size: 12px;
      color: #64748b;
      background: rgba(255, 255, 255, 0.8);
      padding: 6px 12px;
      border-radius: 6px;
      border: 1px solid #e2e8f0;
      white-space: nowrap;
    }

    .calendar-header h1 {
      margin: 0;
      font-size: 36px;
      font-weight: 700;
      color: #1e293b;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      position: relative;
    }

    .calendar-header h1::after {
      content: '';
      position: absolute;
      bottom: -8px;
      left: 0;
      width: 80px;
      height: 4px;
      background: linear-gradient(90deg, #C49A56 0%, #D4AF6A 100%);
      border-radius: 2px;
    }

    .calendar-content {
      display: grid;
      grid-template-columns: 1fr 300px;
      gap: 24px;
    }

    .calendar-widget {
      background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
      border-radius: 24px;
      padding: 32px;
      box-shadow:
        0 20px 60px rgba(0, 0, 0, 0.1),
        0 8px 24px rgba(0, 0, 0, 0.05);
      border: 1px solid rgba(226, 232, 240, 0.8);
      position: relative;
      overflow: hidden;
      transition: all 0.3s ease;
    }

    .calendar-widget::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 6px;
      background: linear-gradient(90deg, #C49A56 0%, #D4AF6A 50%, #C49A56 100%);
    }

    .calendar-widget:hover {
      transform: translateY(-4px);
      box-shadow:
        0 30px 80px rgba(0, 0, 0, 0.15),
        0 12px 32px rgba(0, 0, 0, 0.08);
    }

    .calendar-nav {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
    }

    .calendar-nav h3 {
      margin: 0;
      font-size: 18px;
      font-weight: 600;
    }

    .calendar-grid {
      border: 1px solid #e0e0e0;
      border-radius: 8px;
      overflow: hidden;
    }

    .calendar-header-row {
      display: grid;
      grid-template-columns: repeat(7, 1fr);
      background: #f8f9fa;
    }

    .day-header {
      padding: 12px;
      text-align: center;
      font-weight: 600;
      font-size: 12px;
      color: #666;
      border-right: 1px solid #e0e0e0;
    }

    .calendar-body {
      background: white;
    }

    .calendar-row {
      display: grid;
      grid-template-columns: repeat(7, 1fr);
      border-bottom: 1px solid #e0e0e0;
    }

    .calendar-day {
      padding: 12px;
      text-align: center;
      cursor: pointer;
      border-right: 1px solid #e0e0e0;
      transition: all 0.2s ease;
      min-height: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .calendar-day:hover {
      background: #f0f0f0;
    }

    .calendar-day.other-month {
      color: #ccc;
    }

    .calendar-day.today {
      background: #e3f2fd;
      color: #1976d2;
      font-weight: 600;
    }

    .calendar-day.selected {
      background: #1976d2;
      color: white;
    }

    .selected-date-section {
      margin-top: 24px;
      padding-top: 24px;
      border-top: 1px solid #e0e0e0;
    }

    .selected-date-section h4 {
      margin: 0 0 16px 0;
      font-size: 16px;
      font-weight: 600;
    }

    .appointment-item {
      display: flex;
      align-items: center;
      gap: 12px;
      padding: 12px;
      background: #f8f9fa;
      border-radius: 8px;
      margin-bottom: 8px;
    }

    .appointment-time {
      font-weight: 600;
      color: #1976d2;
      min-width: 60px;
    }

    .appointment-details {
      flex: 1;
    }

    .appointment-type {
      font-weight: 500;
      margin-bottom: 2px;
    }

    .appointment-client {
      font-size: 12px;
      color: #666;
    }

    .appointment-status {
      padding: 4px 8px;
      border-radius: 4px;
      font-size: 11px;
      font-weight: 500;
      text-transform: uppercase;
    }

    .appointment-status.confirmed {
      background: #e8f5e8;
      color: #2e7d32;
    }

    .appointment-status.pending {
      background: #fff3e0;
      color: #f57c00;
    }

    .new-booking-btn {
      width: 100%;
      margin-top: 16px;
      background: linear-gradient(45deg, #C49A56, #D4AF6A) !important;
      color: white !important;
      font-weight: 600;
      transition: all 0.3s ease;
    }

    .new-booking-btn:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(196, 154, 86, 0.3);
    }

    .header-new-booking-btn {
      background: linear-gradient(45deg, #C49A56, #D4AF6A) !important;
      color: white !important;
      font-weight: 600;
      padding: 12px 24px !important;
      border-radius: 8px !important;
      transition: all 0.3s ease;
      box-shadow: 0 4px 12px rgba(196, 154, 86, 0.2);
    }

    .header-new-booking-btn:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 20px rgba(196, 154, 86, 0.3);
    }

    .upcoming-appointments {
      background: white;
      border-radius: 12px;
      padding: 24px;
      box-shadow: 0 2px 8px rgba(0,0,0,0.1);
      height: fit-content;
    }

    .upcoming-appointments h3 {
      margin: 0 0 20px 0;
      font-size: 18px;
      font-weight: 600;
    }

    .appointment-card {
      display: flex;
      align-items: center;
      gap: 16px;
      padding: 16px;
      border: 1px solid #e0e0e0;
      border-radius: 8px;
      margin-bottom: 12px;
      transition: all 0.2s ease;
    }

    .appointment-card:hover {
      box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }

    .appointment-date {
      text-align: center;
      min-width: 50px;
    }

    .date-day {
      font-size: 20px;
      font-weight: 600;
      color: #1976d2;
    }

    .date-month {
      font-size: 12px;
      color: #666;
      text-transform: uppercase;
    }

    .appointment-info {
      flex: 1;
    }

    .appointment-title {
      font-weight: 500;
      margin-bottom: 4px;
    }

    .appointment-actions {
      display: flex;
      gap: 4px;
    }

    @media (max-width: 768px) {
      .calendar-content {
        grid-template-columns: 1fr;
      }
    }
  `]
})
export class CalendarComponent implements OnInit {
  currentMonth = 'July 2025';
  selectedDate: Date | null = null;
  weekDays = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
  calendarWeeks: CalendarDay[][] = [];

  // Firebase integration
  linkedLawyers: LawyerProfile[] = [];
  selectedLawyer: LawyerProfile | null = null;
  isLoading = false;

  constructor(
    private modalController: ModalController,
    private toastController: ToastController,
    private alertController: AlertController,
    private firebaseService: FirebaseService
  ) {}

  appointments: Appointment[] = [
    {
      id: '1',
      clientName: 'Kobe Bryant',
      type: 'Consultation',
      time: '10:00',
      date: new Date(2025, 6, 1), // July 1, 2025
      status: 'confirmed'
    },
    {
      id: '2',
      clientName: 'LeBron James',
      type: 'Consultation',
      time: '11:00',
      date: new Date(2025, 6, 1), // July 1, 2025
      status: 'confirmed'
    },
    {
      id: '3',
      clientName: 'Michael Jordan',
      type: 'Case Review',
      time: '14:00',
      date: new Date(2025, 6, 2), // July 2, 2025
      status: 'pending'
    }
  ];

  upcomingAppointments: Appointment[] = [];

  async ngOnInit() {
    console.log('Calendar component initialized');
    this.generateCalendar();
    await this.loadLinkedLawyers();
    await this.loadAppointments();
    this.upcomingAppointments = this.appointments.slice(0, 5);
    console.log('Initial appointments:', this.appointments);
    console.log('Upcoming appointments:', this.upcomingAppointments);
  }

  async loadLinkedLawyers() {
    const currentUser = this.firebaseService.getCurrentUser();
    if (currentUser) {
      try {
        this.linkedLawyers = await this.firebaseService.getSecretaryLinkedLawyers(currentUser.uid);
        if (this.linkedLawyers.length > 0) {
          this.selectedLawyer = this.linkedLawyers[0]; // Select first lawyer by default
        }
        console.log('Linked lawyers loaded:', this.linkedLawyers);
      } catch (error) {
        console.error('Error loading linked lawyers:', error);
        this.showToast('Error loading linked lawyers', 'danger');
      }
    }
  }

  async loadAppointments() {
    const currentUser = this.firebaseService.getCurrentUser();
    if (!currentUser) return;

    this.isLoading = true;
    try {
      const firebaseAppointments = await this.firebaseService.getAppointmentsForSecretary(currentUser.uid);

      // Convert Firebase appointments to local format
      this.appointments = firebaseAppointments.map(apt => ({
        id: apt.id || '',
        clientName: apt.clientName,
        type: apt.type,
        time: apt.time,
        date: new Date(apt.date),
        status: apt.status as 'confirmed' | 'pending' | 'cancelled'
      }));

      console.log('Firebase appointments loaded:', this.appointments);
    } catch (error) {
      console.error('Error loading appointments from Firebase:', error);
      this.showToast('Error loading appointments', 'danger');
    } finally {
      this.isLoading = false;
    }
  }

  onLawyerChange() {
    console.log('Selected lawyer changed to:', this.selectedLawyer);
    if (this.selectedLawyer) {
      this.showToast(`Now managing appointments for ${this.selectedLawyer.name}`, 'primary');
      // Optionally reload appointments for the selected lawyer
      this.loadAppointments();
    }
  }

  generateCalendar() {
    // Generate calendar grid for July 2025
    const year = 2025;
    const month = 6; // July (0-indexed)
    
    const firstDay = new Date(year, month, 1);
    const lastDay = new Date(year, month + 1, 0);
    const startDate = new Date(firstDay);
    startDate.setDate(startDate.getDate() - firstDay.getDay() + 1); // Start from Monday
    
    this.calendarWeeks = [];
    let currentDate = new Date(startDate);
    
    for (let week = 0; week < 6; week++) {
      const weekDays = [];
      for (let day = 0; day < 7; day++) {
        weekDays.push({
          date: currentDate.getDate(),
          isCurrentMonth: currentDate.getMonth() === month,
          isToday: this.isToday(currentDate),
          isSelected: false,
          fullDate: new Date(currentDate)
        });
        currentDate.setDate(currentDate.getDate() + 1);
      }
      this.calendarWeeks.push(weekDays);
    }
  }

  isToday(date: Date): boolean {
    const today = new Date();
    return date.toDateString() === today.toDateString();
  }

  selectDate(day: any) {
    console.log('Date selected:', day);

    // Clear previous selection
    this.calendarWeeks.forEach(week => {
      week.forEach(d => d.isSelected = false);
    });

    // Set new selection
    day.isSelected = true;
    this.selectedDate = day.fullDate;

    // Show feedback
    this.showToast(`Selected ${this.selectedDate?.toDateString()}`, 'primary');
    console.log('Selected date:', this.selectedDate);
  }

  getAppointmentsForDate(date: Date): Appointment[] {
    return this.appointments.filter(apt => 
      apt.date.toDateString() === date.toDateString()
    );
  }

  previousMonth() {
    console.log('Previous month clicked');
    this.showToast('Previous month navigation - Coming soon!', 'primary');
  }

  nextMonth() {
    console.log('Next month clicked');
    this.showToast('Next month navigation - Coming soon!', 'primary');
  }

  async openNewBookingDialog() {
    console.log('New booking dialog opened');

    if (this.linkedLawyers.length === 0) {
      this.showToast('No linked lawyers found. Please link a lawyer first.', 'warning');
      return;
    }

    const alert = await this.alertController.create({
      header: 'New Booking Request',
      message: 'Create a new appointment booking (requires lawyer approval)',
      inputs: [
        {
          name: 'lawyer',
          type: 'radio',
          label: this.selectedLawyer?.name || 'Select Lawyer',
          value: this.selectedLawyer?.uid || '',
          checked: true
        },
        ...this.linkedLawyers.slice(1).map(lawyer => ({
          name: 'lawyer',
          type: 'radio' as const,
          label: lawyer.name,
          value: lawyer.uid,
          checked: false
        })),
        {
          name: 'clientName',
          type: 'text',
          placeholder: 'Client Name'
        },
        {
          name: 'appointmentType',
          type: 'text',
          placeholder: 'Appointment Type (e.g., Consultation)'
        },
        {
          name: 'time',
          type: 'time',
          placeholder: 'Time'
        },
        {
          name: 'remarks',
          type: 'textarea',
          placeholder: 'Additional remarks (optional)'
        }
      ],
      buttons: [
        {
          text: 'Cancel',
          role: 'cancel'
        },
        {
          text: 'Create Booking Request',
          handler: (data) => {
            this.createNewBooking(data);
          }
        }
      ]
    });

    await alert.present();
  }

  async createNewBooking(data: any) {
    if (!this.selectedDate) {
      this.showToast('Please select a date first', 'warning');
      return;
    }

    if (!data.clientName || !data.appointmentType || !data.time || !data.lawyer) {
      this.showToast('Please fill in all required fields', 'warning');
      return;
    }

    const currentUser = this.firebaseService.getCurrentUser();
    if (!currentUser) {
      this.showToast('Please log in to create appointments', 'danger');
      return;
    }

    const selectedLawyer = this.linkedLawyers.find(l => l.uid === data.lawyer);
    if (!selectedLawyer) {
      this.showToast('Selected lawyer not found', 'danger');
      return;
    }

    this.isLoading = true;
    try {
      const appointmentData: Omit<EnhancedAppointment, 'id'> = {
        lawyerId: selectedLawyer.uid,
        lawyerName: selectedLawyer.name,
        clientName: data.clientName,
        date: this.selectedDate.toISOString().split('T')[0], // Format: YYYY-MM-DD
        time: data.time,
        status: 'pending', // Always starts as pending for secretary-created appointments
        type: data.appointmentType,
        createdBy: 'secretary',
        managedBy: currentUser.uid,
        lastModifiedBy: currentUser.uid,
        lastModifiedByRole: 'secretary',
        remarks: data.remarks || '',
        isUrgent: false,
        createdAt: new Date(),
        updatedAt: new Date()
      };

      const appointmentId = await this.firebaseService.createAppointment(appointmentData);

      // Add to local array for immediate UI update
      const newAppointment: Appointment = {
        id: appointmentId,
        clientName: data.clientName,
        type: data.appointmentType,
        time: data.time,
        date: new Date(this.selectedDate),
        status: 'pending'
      };

      this.appointments.push(newAppointment);
      this.upcomingAppointments = this.appointments.slice(0, 5);

      this.showToast(
        `Appointment request created for ${data.clientName} with ${selectedLawyer.name}. Awaiting lawyer approval.`,
        'success'
      );
      console.log('New appointment created in Firebase:', appointmentData);

    } catch (error) {
      console.error('Error creating appointment:', error);
      this.showToast('Error creating appointment. Please try again.', 'danger');
    } finally {
      this.isLoading = false;
    }
  }

  async editAppointment(appointment: Appointment) {
    console.log('Edit appointment:', appointment);

    const alert = await this.alertController.create({
      header: 'Edit Appointment',
      message: 'Update appointment details',
      inputs: [
        {
          name: 'clientName',
          type: 'text',
          value: appointment.clientName,
          placeholder: 'Client Name'
        },
        {
          name: 'appointmentType',
          type: 'text',
          value: appointment.type,
          placeholder: 'Appointment Type'
        },
        {
          name: 'time',
          type: 'time',
          value: appointment.time,
          placeholder: 'Time'
        }
      ],
      buttons: [
        {
          text: 'Cancel',
          role: 'cancel'
        },
        {
          text: 'Update',
          handler: (data) => {
            this.updateAppointment(appointment.id, data);
          }
        }
      ]
    });

    await alert.present();
  }

  async updateAppointment(appointmentId: string, data: any) {
    const currentUser = this.firebaseService.getCurrentUser();
    if (!currentUser) {
      this.showToast('Please log in to update appointments', 'danger');
      return;
    }

    this.isLoading = true;
    try {
      const updates: Partial<EnhancedAppointment> = {
        clientName: data.clientName,
        type: data.appointmentType,
        time: data.time,
        lastModifiedBy: currentUser.uid,
        lastModifiedByRole: 'secretary',
        updatedAt: new Date()
      };

      await this.firebaseService.updateAppointment(appointmentId, updates);

      // Update local array
      const index = this.appointments.findIndex(apt => apt.id === appointmentId);
      if (index !== -1) {
        this.appointments[index] = {
          ...this.appointments[index],
          clientName: data.clientName,
          type: data.appointmentType,
          time: data.time
        };
        this.upcomingAppointments = this.appointments.slice(0, 5);
      }

      this.showToast('Appointment updated successfully', 'success');
    } catch (error) {
      console.error('Error updating appointment:', error);
      this.showToast('Error updating appointment. Please try again.', 'danger');
    } finally {
      this.isLoading = false;
    }
  }

  async deleteAppointment(appointment: Appointment) {
    console.log('Delete appointment:', appointment);

    const alert = await this.alertController.create({
      header: 'Delete Appointment',
      message: `Are you sure you want to delete the appointment with ${appointment.clientName}?`,
      buttons: [
        {
          text: 'Cancel',
          role: 'cancel'
        },
        {
          text: 'Delete',
          role: 'destructive',
          handler: () => {
            this.confirmDeleteAppointment(appointment.id);
          }
        }
      ]
    });

    await alert.present();
  }

  async confirmDeleteAppointment(appointmentId: string) {
    const currentUser = this.firebaseService.getCurrentUser();
    if (!currentUser) {
      this.showToast('Please log in to delete appointments', 'danger');
      return;
    }

    this.isLoading = true;
    try {
      await this.firebaseService.deleteAppointment(appointmentId, currentUser.uid, 'secretary');

      // Remove from local array
      const index = this.appointments.findIndex(apt => apt.id === appointmentId);
      if (index !== -1) {
        const deletedAppointment = this.appointments.splice(index, 1)[0];
        this.upcomingAppointments = this.appointments.slice(0, 5);
        this.showToast(`Appointment with ${deletedAppointment.clientName} deleted`, 'success');
      }
    } catch (error) {
      console.error('Error deleting appointment:', error);
      this.showToast('Error deleting appointment. Please try again.', 'danger');
    } finally {
      this.isLoading = false;
    }
  }

  async showToast(message: string, color: string = 'primary') {
    const toast = await this.toastController.create({
      message: message,
      duration: 3000,
      color: color,
      position: 'bottom'
    });
    toast.present();
  }
}
