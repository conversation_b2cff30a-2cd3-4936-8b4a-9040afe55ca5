import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';

import { LoginComponent } from './auth/login/login.component';
import { DashboardComponent } from './dashboard/dashboard.component';
import { LawyerVerificationComponent } from './lawyer-verification/lawyer-verification.component';
import { PlatformControlComponent } from './platform-control/platform-control.component';
import { LawyerListComponent } from './lawyer-list/lawyer-list.component';
import { TemplatesComponent } from './templates/templates.component';
import { UsersComponent } from './users/users.component';
import { SettingsComponent } from './settings/settings.component';
import { AdminSetupComponent } from './setup/admin-setup.component';

import { AdminAuthGuard } from './guards/admin-auth.guard';

const routes: Routes = [
  { path: '', redirectTo: '/login', pathMatch: 'full' },
  { path: 'login', component: LoginComponent },
  { path: 'setup', component: AdminSetupComponent },
  { 
    path: 'admin', 
    canActivate: [AdminAuthGuard],
    children: [
      { path: '', redirectTo: 'dashboard', pathMatch: 'full' },
      { path: 'dashboard', component: DashboardComponent },
      { path: 'lawyer-verification', component: LawyerVerificationComponent },
      { path: 'lawyer-list', component: LawyerListComponent },
      { path: 'platform-control', component: PlatformControlComponent },
      { path: 'templates', component: TemplatesComponent },
      { path: 'users', component: UsersComponent },
      { path: 'settings', component: SettingsComponent }
    ]
  },
  { path: '**', redirectTo: '/login' }
];

@NgModule({
  imports: [RouterModule.forRoot(routes)],
  exports: [RouterModule]
})
export class AppRoutingModule { }
