.dashboard-container {
  padding: 0;
  margin-left: 250px; // Account for sidebar
}

.dashboard-header {
  margin-bottom: 30px;
  padding: 25px;
  background: linear-gradient(135deg, var(--veritus-white) 0%, var(--veritus-accent) 100%);
  border-radius: 12px;
  border: 1px solid var(--veritus-primary-light);

  h1 {
    margin: 0 0 8px 0;
    font-size: 2.2rem;
    font-weight: 700;
    color: var(--veritus-primary);
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  }

  p {
    margin: 0;
    color: var(--veritus-text-light);
    font-size: 1.1rem;
    font-weight: 400;
  }
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.stat-card {
  background: linear-gradient(135deg, var(--veritus-white) 0%, var(--veritus-light) 100%);
  border-radius: 16px;
  padding: 30px;
  box-shadow: 0 4px 15px rgba(44, 62, 80, 0.08);
  border: 1px solid var(--veritus-primary-light);
  display: flex;
  align-items: center;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: linear-gradient(180deg, var(--veritus-primary) 0%, var(--veritus-primary-dark) 100%);
  }

  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(196, 154, 86, 0.15);
  }
  
  .stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    margin-right: 20px;
    
    &.pending {
      background: rgba(255, 193, 7, 0.1);
      color: #ffc107;
    }
    
    &.verified {
      background: rgba(40, 167, 69, 0.1);
      color: #28a745;
    }
    
    &.users {
      background: rgba(23, 162, 184, 0.1);
      color: #17a2b8;
    }
    
    &.templates {
      background: rgba(196, 154, 86, 0.1);
      color: var(--admin-primary);
    }
  }
  
  .stat-content {
    flex: 1;
    
    h3 {
      margin: 0 0 5px 0;
      font-size: 2rem;
      font-weight: 700;
      color: #333;
    }
    
    p {
      margin: 0 0 8px 0;
      color: #666;
      font-size: 0.9rem;
      font-weight: 500;
    }
    
    .stat-change {
      font-size: 0.8rem;
      font-weight: 600;
      
      &.positive {
        color: #28a745;
      }
      
      &.negative {
        color: #dc3545;
      }
      
      &.neutral {
        color: #6c757d;
      }
    }
  }
}

.dashboard-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 20px;
}

.dashboard-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  border: 1px solid #e1e5e9;
  overflow: hidden;
  
  .card-header {
    padding: 20px 25px;
    border-bottom: 1px solid #e1e5e9;
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    h2 {
      margin: 0;
      font-size: 1.2rem;
      font-weight: 600;
      color: #333;
    }
    
    .view-all-link {
      color: var(--admin-primary);
      text-decoration: none;
      font-size: 0.9rem;
      font-weight: 500;
      
      &:hover {
        text-decoration: underline;
      }
    }
    
    .time-filter {
      padding: 6px 12px;
      border: 1px solid #e1e5e9;
      border-radius: 6px;
      font-size: 0.85rem;
      background: white;
      
      &:focus {
        outline: none;
        border-color: var(--admin-primary);
      }
    }
  }
  
  .card-content {
    padding: 25px;
  }
}

.verification-list {
  .verification-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 0;
    border-bottom: 1px solid #f1f3f4;
    
    &:last-child {
      border-bottom: none;
    }
    
    .lawyer-info {
      display: flex;
      align-items: center;
      
      .lawyer-avatar {
        width: 45px;
        height: 45px;
        border-radius: 50%;
        overflow: hidden;
        margin-right: 15px;
        position: relative;

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
          display: block;
        }

        .avatar-placeholder {
          width: 100%;
          height: 100%;
          background: linear-gradient(135deg, var(--admin-primary), #d4a574);
          display: flex;
          align-items: center;
          justify-content: center;
          color: white;
          font-weight: 600;
          font-size: 16px;
          text-transform: uppercase;
        }
      }
      
      .lawyer-details {
        h4 {
          margin: 0 0 4px 0;
          font-size: 0.95rem;
          font-weight: 600;
          color: #333;
        }
        
        p {
          margin: 0;
          font-size: 0.8rem;
          color: #666;
          line-height: 1.3;
        }
      }
    }
    
    .verification-status {
      text-align: right;
      
      .status-badge {
        display: inline-block;
        padding: 4px 12px;
        border-radius: 20px;
        font-size: 0.75rem;
        font-weight: 600;
        margin-bottom: 4px;
        
        &.pending {
          background: rgba(255, 193, 7, 0.1);
          color: #ffc107;
        }
        
        &.approved {
          background: rgba(40, 167, 69, 0.1);
          color: #28a745;
        }
        
        &.rejected {
          background: rgba(220, 53, 69, 0.1);
          color: #dc3545;
        }
      }
      
      .verification-time {
        display: block;
        font-size: 0.75rem;
        color: #999;
      }
    }
  }
}

.activity-list {
  .activity-item {
    display: flex;
    align-items: flex-start;
    padding: 12px 0;
    border-bottom: 1px solid #f1f3f4;
    
    &:last-child {
      border-bottom: none;
    }
    
    .activity-icon {
      width: 35px;
      height: 35px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 15px;
      font-size: 16px;
      
      &.verification {
        background: rgba(40, 167, 69, 0.1);
        color: #28a745;
      }
      
      &.user {
        background: rgba(23, 162, 184, 0.1);
        color: #17a2b8;
      }
      
      &.system {
        background: rgba(108, 117, 125, 0.1);
        color: #6c757d;
      }
      
      &.template {
        background: rgba(196, 154, 86, 0.1);
        color: var(--admin-primary);
      }
    }
    
    .activity-content {
      flex: 1;
      
      p {
        margin: 0 0 4px 0;
        font-size: 0.9rem;
        color: #333;
        line-height: 1.4;
      }
      
      .activity-time {
        font-size: 0.75rem;
        color: #999;
      }
    }
  }
}



.health-metrics {
  .metric {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid #f1f3f4;
    
    &:last-child {
      border-bottom: none;
    }
    
    .metric-label {
      font-size: 0.9rem;
      color: #666;
      font-weight: 500;
    }
    
    .metric-value {
      display: flex;
      align-items: center;
      font-size: 0.85rem;
      font-weight: 600;
      
      .status-dot {
        width: 8px;
        height: 8px;
        border-radius: 50%;
        margin-right: 8px;
      }
      
      &.healthy {
        color: #28a745;
        
        .status-dot {
          background: #28a745;
        }
      }
      
      &.warning {
        color: #ffc107;
        
        .status-dot {
          background: #ffc107;
        }
      }
      
      &.error {
        color: #dc3545;
        
        .status-dot {
          background: #dc3545;
        }
      }
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .dashboard-container {
    margin-left: 0;
    padding: 15px;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
    gap: 15px;
  }
  
  .dashboard-grid {
    grid-template-columns: 1fr;
    gap: 15px;
  }
  
  .stat-card {
    padding: 20px;
    
    .stat-icon {
      width: 50px;
      height: 50px;
      font-size: 20px;
      margin-right: 15px;
    }
    
    .stat-content h3 {
      font-size: 1.5rem;
    }
  }
  
  .dashboard-card .card-content {
    padding: 20px;
  }

}
