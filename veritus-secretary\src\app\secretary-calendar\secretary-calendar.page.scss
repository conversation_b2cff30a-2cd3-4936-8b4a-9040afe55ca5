.calendar-content {
  --background: #FFFFFF;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
}

.calendar-container {
  padding: 24px;
  min-height: 100vh;
  max-width: 1200px;
  margin: 0 auto;
}

.tab-navigation {
  margin-bottom: 32px;
  background: #FFFFFF;
  border-radius: 16px;
  padding: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid #e2e8f0;

  ion-segment {
    background: transparent;
    border-radius: 12px;
    padding: 4px;

    ion-segment-button {
      --color: #6b7280;
      --color-checked: #FFFFFF;
      --background-checked: linear-gradient(135deg, #C49A56 0%, #D8B67E 100%);
      --border-radius: 12px;
      margin: 0 4px;
      transition: all 0.3s ease;
      font-weight: 500;

      ion-icon {
        margin-bottom: 6px;
        font-size: 20px;
      }

      ion-label {
        font-size: 14px;
        font-weight: 600;
      }

      &:hover {
        --color: #374151;
      }

      &.segment-button-checked {
        box-shadow: 0 4px 12px rgba(196, 154, 86, 0.3);
      }
    }
  }
}

.tab-content {
  margin-bottom: 32px;
  min-height: 400px;

  // Add visual feedback for tab content
  &:empty::before {
    content: "Loading tab content...";
    display: block;
    text-align: center;
    padding: 60px;
    color: #6b7280;
    font-style: italic;
    font-size: 16px;
  }
}

.filters-section {
  background: #FFFFFF;
  border-radius: 16px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid #e2e8f0;
}

.filter-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.filter-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.filter-label {
  font-weight: 600;
  color: #374151;
  font-size: 14px;
  margin-bottom: 4px;
}

.date-input, .lawyer-select {
  --background: #FFFFFF;
  --color: #374151;
  --border-radius: 12px;
  --padding-start: 16px;
  --padding-end: 16px;
  --padding-top: 12px;
  --padding-bottom: 12px;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  font-size: 14px;
  transition: all 0.3s ease;

  &:focus {
    border-color: #C49A56;
    box-shadow: 0 0 0 3px rgba(196, 154, 86, 0.1);
  }
}

.action-section {
  margin-bottom: 32px;
}

.create-btn {
  width: 100%;
  height: 56px;
  background: linear-gradient(135deg, #C49A56 0%, #D8B67E 100%);
  color: #FFFFFF;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  font-weight: 600;
  font-size: 16px;
  border: none;
  box-shadow: 0 8px 24px rgba(196, 154, 86, 0.3);
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 32px rgba(196, 154, 86, 0.4);
  }

  &:active {
    transform: translateY(0);
  }
}

.btn-icon {
  font-size: 20px;
}

.appointments-section {
  margin-bottom: 32px;
}

.section-title {
  color: #374151;
  font-size: 24px;
  font-weight: 700;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  gap: 12px;

  &::before {
    content: '';
    width: 4px;
    height: 24px;
    background: linear-gradient(135deg, #C49A56 0%, #D8B67E 100%);
    border-radius: 2px;
  }
}

.appointments-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.appointment-card {
  background: #FFFFFF;
  border-radius: 16px;
  padding: 24px;
  display: grid;
  grid-template-columns: auto 1fr auto;
  gap: 20px;
  align-items: center;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid #e2e8f0;
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 32px rgba(0, 0, 0, 0.12);
    border-color: #C49A56;
  }
}

.appointment-time {
  text-align: center;
  min-width: 80px;
  background: linear-gradient(135deg, #C49A56 0%, #D8B67E 100%);
  color: #FFFFFF;
  border-radius: 12px;
  padding: 12px 8px;
  font-weight: 600;
  box-shadow: 0 4px 12px rgba(196, 154, 86, 0.3);
}

.time {
  display: block;
  font-size: 14px;
  line-height: 1.2;
}

.appointment-details {
  flex: 1;
}

.appointment-title {
  color: #111827;
  font-size: 18px;
  font-weight: 700;
  margin-bottom: 8px;
  line-height: 1.4;
}

.appointment-type {
  color: #C49A56;
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 6px;
}

.appointment-lawyer {
  color: #6b7280;
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 12px;
}

.appointment-status {
  display: flex;
  align-items: center;
}

.status-chip {
  --background: linear-gradient(135deg, #10b981 0%, #34d399 100%);
  --color: #FFFFFF;
  height: 32px;
  border-radius: 16px;
  padding: 0 12px;
  font-weight: 600;
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);

  &.status-pending {
    --background: linear-gradient(135deg, #f59e0b 0%, #fbbf24 100%);
    box-shadow: 0 4px 12px rgba(245, 158, 11, 0.3);
  }

  &.status-cancelled {
    --background: linear-gradient(135deg, #ef4444 0%, #f87171 100%);
    box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
  }

  ion-label {
    font-size: 12px;
    font-weight: 600;
    color: #FFFFFF;
  }
}

.appointment-actions {
  display: flex;
  gap: 12px;
}

.action-btn {
  width: 44px;
  height: 44px;
  border-radius: 12px;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  cursor: pointer;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.15);
  }

  &.edit-btn {
    background: linear-gradient(135deg, #1B3A9E 0%, #3B5AAD 100%);
    color: #FFFFFF;
    box-shadow: 0 4px 12px rgba(27, 58, 158, 0.3);

    &:hover {
      box-shadow: 0 8px 20px rgba(27, 58, 158, 0.4);
    }
  }

  &.delete-btn {
    background: linear-gradient(135deg, #ef4444 0%, #f87171 100%);
    color: #FFFFFF;
    box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);

    &:hover {
      box-shadow: 0 8px 20px rgba(239, 68, 68, 0.4);
    }
  }

  ion-icon {
    font-size: 18px;
  }
}

.empty-state {
  text-align: center;
  padding: 60px 40px;
  background: #FFFFFF;
  border-radius: 16px;
  border: 2px dashed #e2e8f0;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.empty-icon {
  font-size: 64px;
  color: #C49A56;
  margin-bottom: 24px;
}

.empty-title {
  color: #374151;
  font-size: 20px;
  font-weight: 700;
  margin-bottom: 12px;
}

.empty-description {
  color: #6b7280;
  font-size: 16px;
  margin-bottom: 32px;
  line-height: 1.6;
}

.stats-section {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;
  margin-bottom: 32px;
}

.stat-card {
  background: #FFFFFF;
  border-radius: 16px;
  padding: 24px;
  text-align: center;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid #e2e8f0;
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 32px rgba(0, 0, 0, 0.12);
  }

  &.stat-confirmed {
    border-left: 4px solid #10b981;
  }

  &.stat-pending {
    border-left: 4px solid #f59e0b;
  }

  &.stat-total {
    border-left: 4px solid #C49A56;
  }
}

.stat-number {
  color: #111827;
  font-size: 32px;
  font-weight: 800;
  margin-bottom: 8px;
  line-height: 1;
}

.stat-label {
  color: #6b7280;
  font-size: 14px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

// Placeholder content styling
.placeholder-content {
  ion-card {
    background: #FFFFFF;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid #e2e8f0;
    margin: 0;

    ion-card-header {
      padding: 24px 24px 16px 24px;

      ion-card-title {
        color: #111827;
        font-size: 20px;
        font-weight: 700;
        margin-bottom: 8px;
      }

      ion-card-subtitle {
        color: #6b7280;
        font-size: 14px;
        font-weight: 500;
      }
    }

    ion-card-content {
      padding: 0 24px 24px 24px;

      p {
        color: #6b7280;
        font-size: 14px;
        line-height: 1.6;
        margin-bottom: 12px;
      }

      ion-button {
        --background: linear-gradient(135deg, #C49A56 0%, #D8B67E 100%);
        --color: #FFFFFF;
        --border-radius: 12px;
        --box-shadow: 0 4px 12px rgba(196, 154, 86, 0.3);
        font-weight: 600;
        margin-top: 16px;

        &:hover {
          --box-shadow: 0 8px 20px rgba(196, 154, 86, 0.4);
        }
      }
    }
  }
}

// Responsive Design
@media (max-width: 768px) {
  .calendar-container {
    padding: 16px;
  }

  .tab-navigation {
    padding: 6px;
    margin-bottom: 24px;
  }

  .filters-section {
    padding: 20px;
  }

  .filter-row {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .appointment-card {
    grid-template-columns: 1fr;
    text-align: center;
    gap: 16px;
    padding: 20px;
  }

  .appointment-actions {
    justify-content: center;
  }

  .stats-section {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .section-title {
    font-size: 20px;
  }
}
