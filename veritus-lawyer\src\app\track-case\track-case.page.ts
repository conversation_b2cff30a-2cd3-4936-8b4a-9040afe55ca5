import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { MockDataService, MockCase, MockTimelineEvent } from '../services/mock-data.service';

@Component({
  selector: 'app-track-case',
  templateUrl: './track-case.page.html',
  styleUrls: ['./track-case.page.scss'],
  standalone: false,
})
export class TrackCasePage implements OnInit {
  caseId: string = '';
  currentCase: MockCase | null = null;
  isLoading = true;
  userRole: 'client' | 'lawyer' = 'lawyer'; // Default to lawyer for this phase

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private mockDataService: MockDataService
  ) { }

  ngOnInit() {
    this.caseId = this.route.snapshot.paramMap.get('id') || '1';
    this.determineUserRole();
    this.loadCaseDetails();
  }

  loadCaseDetails() {
    this.isLoading = true;
    this.mockDataService.getCaseById(this.caseId).subscribe(case_ => {
      this.currentCase = case_ || null;
      this.isLoading = false;
    });
  }

  onBack() {
    // Navigate back based on user role
    if (this.userRole === 'lawyer') {
      this.router.navigate(['/tabs/cases']);
    } else {
      this.router.navigate(['/client-tabs/home']);
    }
  }

  getTimelineIcon(type: string): string {
    switch (type) {
      case 'filing': return 'document-text';
      case 'meeting': return 'people';
      case 'court': return 'business';
      case 'document': return 'folder';
      case 'milestone': return 'flag';
      case 'hearing': return 'hammer';
      case 'appeal': return 'arrow-up-circle';
      case 'resolution': return 'checkmark-done';
      case 'execution': return 'shield-checkmark';
      default: return 'checkmark-circle';
    }
  }

  formatDate(date: Date): string {
    return new Intl.DateTimeFormat('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    }).format(new Date(date));
  }

  getTimelineIconColor(type: string): string {
    switch (type) {
      case 'filing': return '#2196F3';
      case 'meeting': return '#FF9800';
      case 'court': return '#9C27B0';
      case 'document': return '#4CAF50';
      case 'milestone': return '#F44336';
      case 'hearing': return '#E91E63';
      case 'appeal': return '#FF5722';
      case 'resolution': return '#00BCD4';
      case 'execution': return '#795548';
      default: return '#67EF77';
    }
  }

  getPriorityColor(priority?: string): string {
    switch (priority) {
      case 'critical': return '#F44336';
      case 'high': return '#FF9800';
      case 'medium': return '#2196F3';
      case 'low': return '#4CAF50';
      default: return '#9E9E9E';
    }
  }

  getPriorityLabel(priority?: string): string {
    switch (priority) {
      case 'critical': return 'CRITICAL';
      case 'high': return 'HIGH';
      case 'medium': return 'MEDIUM';
      case 'low': return 'LOW';
      default: return '';
    }
  }

  determineUserRole() {
    // Check current route to determine if this is client or lawyer view
    const currentUrl = this.router.url;
    if (currentUrl.includes('/client-tabs/') || currentUrl.includes('/client-home')) {
      this.userRole = 'client';
    } else {
      this.userRole = 'lawyer';
    }
  }



  onAddMilestone() {
    // Lawyer-only feature to add new milestones
    console.log('Adding new milestone');
  }

  onEditMilestone(milestone: any) {
    // Lawyer-only feature to edit milestones
    console.log('Editing milestone:', milestone.title);
  }
}
