import { Component, OnInit } from '@angular/core';
import { LawyerVerificationService } from '../services/lawyer-verification.service';

interface LawyerDocument {
  id: string;
  name: string;
  type: string;
  url: string;
  uploadedAt: Date;
}

interface LawyerVerification {
  id: string;
  name: string;
  email: string;
  phone: string;
  rollNumber: string;
  firm: string;
  yearsOfPractice: number;
  specialization: string;
  avatar?: string;
  status: 'pending' | 'approved' | 'rejected' | 'under_review';
  submittedAt: Date;
  verificationProgress: number;
  documents: LawyerDocument[];
  verificationNotes?: string;
  ibpVerificationStatus?: string;
}

@Component({
  selector: 'app-lawyer-verification',
  templateUrl: './lawyer-verification.component.html',
  styleUrls: ['./lawyer-verification.component.scss']
})
export class LawyerVerificationComponent implements OnInit {
  lawyers: LawyerVerification[] = [];
  filteredLawyers: LawyerVerification[] = [];
  selectedLawyer: LawyerVerification | null = null;
  
  searchQuery = '';
  statusFilter = '';
  currentPage = 1;
  itemsPerPage = 10;
  totalPages = 1;
  
  verificationNotes = '';
  ibpVerificationStatus = '';

  constructor(private lawyerVerificationService: LawyerVerificationService) { }

  ngOnInit(): void {
    this.loadLawyers();
  }

  loadLawyers(): void {
    // Mock data - replace with actual service call
    this.lawyers = [
      {
        id: '1',
        name: 'John Doe',
        email: '<EMAIL>',
        phone: '+63 ************',
        rollNumber: '123456',
        firm: 'ABC Legal Services',
        yearsOfPractice: 5,
        specialization: 'Corporate Law',
        status: 'pending',
        submittedAt: new Date(Date.now() - 2 * 60 * 60 * 1000),
        verificationProgress: 0,
        documents: [
          {
            id: '1',
            name: 'Bar Exam Certificate',
            type: 'PDF',
            url: '/documents/bar-cert-1.pdf',
            uploadedAt: new Date()
          },
          {
            id: '2',
            name: 'Government ID',
            type: 'JPG',
            url: '/documents/id-1.jpg',
            uploadedAt: new Date()
          }
        ]
      },
      {
        id: '2',
        name: 'Jane Cruz',
        email: '<EMAIL>',
        phone: '+63 ************',
        rollNumber: '783012',
        firm: 'XYZ Law Group',
        yearsOfPractice: 8,
        specialization: 'Criminal Law',
        status: 'pending',
        submittedAt: new Date(Date.now() - 4 * 60 * 60 * 1000),
        verificationProgress: 0,
        documents: [
          {
            id: '3',
            name: 'Bar Exam Certificate',
            type: 'PDF',
            url: '/documents/bar-cert-2.pdf',
            uploadedAt: new Date()
          },
          {
            id: '4',
            name: 'Government ID',
            type: 'JPG',
            url: '/documents/id-2.jpg',
            uploadedAt: new Date()
          }
        ]
      },
      {
        id: '3',
        name: 'Michael Santos',
        email: '<EMAIL>',
        phone: '+63 ************',
        rollNumber: '456789',
        firm: 'Santos & Associates',
        yearsOfPractice: 12,
        specialization: 'Family Law',
        status: 'approved',
        submittedAt: new Date(Date.now() - 6 * 60 * 60 * 1000),
        verificationProgress: 100,
        documents: [
          {
            id: '5',
            name: 'Bar Exam Certificate',
            type: 'PDF',
            url: '/documents/bar-cert-3.pdf',
            uploadedAt: new Date()
          }
        ]
      },
      {
        id: '4',
        name: 'Maria Garcia',
        email: '<EMAIL>',
        phone: '+63 ************',
        rollNumber: '987654',
        firm: 'Garcia Law Firm',
        yearsOfPractice: 3,
        specialization: 'Labor Law',
        status: 'under_review',
        submittedAt: new Date(Date.now() - 8 * 60 * 60 * 1000),
        verificationProgress: 65,
        documents: [
          {
            id: '6',
            name: 'Bar Exam Certificate',
            type: 'PDF',
            url: '/documents/bar-cert-4.pdf',
            uploadedAt: new Date()
          }
        ]
      }
    ];

    this.filterLawyers();
  }

  filterLawyers(): void {
    let filtered = this.lawyers;

    // Filter by search query
    if (this.searchQuery.trim()) {
      const query = this.searchQuery.toLowerCase();
      filtered = filtered.filter(lawyer => 
        lawyer.name.toLowerCase().includes(query) ||
        lawyer.rollNumber.toLowerCase().includes(query) ||
        lawyer.firm.toLowerCase().includes(query) ||
        lawyer.email.toLowerCase().includes(query)
      );
    }

    // Filter by status
    if (this.statusFilter) {
      filtered = filtered.filter(lawyer => lawyer.status === this.statusFilter);
    }

    this.filteredLawyers = filtered;
    this.totalPages = Math.ceil(this.filteredLawyers.length / this.itemsPerPage);
    this.currentPage = 1;
  }

  getFilteredCount(status: string): number {
    return this.lawyers.filter(lawyer => lawyer.status === status).length;
  }

  viewLawyerDetails(lawyer: LawyerVerification): void {
    this.selectedLawyer = { ...lawyer };
    this.verificationNotes = lawyer.verificationNotes || '';
    this.ibpVerificationStatus = lawyer.ibpVerificationStatus || '';
  }

  closeModal(): void {
    this.selectedLawyer = null;
    this.verificationNotes = '';
    this.ibpVerificationStatus = '';
  }

  async approveLawyer(lawyer: LawyerVerification): Promise<void> {
    try {
      const success = await this.lawyerVerificationService.approveLawyer(lawyer.id, {
        notes: 'Approved via quick action',
        ibpStatus: 'verified'
      });

      if (success) {
        lawyer.status = 'approved';
        lawyer.verificationProgress = 100;
        this.filterLawyers();
      }
    } catch (error) {
      console.error('Error approving lawyer:', error);
    }
  }

  async rejectLawyer(lawyer: LawyerVerification): Promise<void> {
    try {
      const success = await this.lawyerVerificationService.rejectLawyer(lawyer.id, {
        notes: 'Rejected via quick action',
        reason: 'Documents require review'
      });

      if (success) {
        lawyer.status = 'rejected';
        this.filterLawyers();
      }
    } catch (error) {
      console.error('Error rejecting lawyer:', error);
    }
  }

  async approveFromModal(): Promise<void> {
    if (!this.selectedLawyer) return;

    try {
      const success = await this.lawyerVerificationService.approveLawyer(this.selectedLawyer.id, {
        notes: this.verificationNotes,
        ibpStatus: this.ibpVerificationStatus
      });

      if (success) {
        const lawyer = this.lawyers.find(l => l.id === this.selectedLawyer!.id);
        if (lawyer) {
          lawyer.status = 'approved';
          lawyer.verificationProgress = 100;
          lawyer.verificationNotes = this.verificationNotes;
          lawyer.ibpVerificationStatus = this.ibpVerificationStatus;
        }
        this.filterLawyers();
        this.closeModal();
      }
    } catch (error) {
      console.error('Error approving lawyer:', error);
    }
  }

  async rejectFromModal(): Promise<void> {
    if (!this.selectedLawyer) return;

    try {
      const success = await this.lawyerVerificationService.rejectLawyer(this.selectedLawyer.id, {
        notes: this.verificationNotes,
        reason: 'Failed verification process'
      });

      if (success) {
        const lawyer = this.lawyers.find(l => l.id === this.selectedLawyer!.id);
        if (lawyer) {
          lawyer.status = 'rejected';
          lawyer.verificationNotes = this.verificationNotes;
        }
        this.filterLawyers();
        this.closeModal();
      }
    } catch (error) {
      console.error('Error rejecting lawyer:', error);
    }
  }

  async requestAdditionalDocs(): Promise<void> {
    if (!this.selectedLawyer) return;

    try {
      const success = await this.lawyerVerificationService.requestAdditionalDocuments(
        this.selectedLawyer.id,
        this.verificationNotes
      );

      if (success) {
        const lawyer = this.lawyers.find(l => l.id === this.selectedLawyer!.id);
        if (lawyer) {
          lawyer.status = 'pending';
          lawyer.verificationNotes = this.verificationNotes;
        }
        this.filterLawyers();
        this.closeModal();
      }
    } catch (error) {
      console.error('Error requesting additional documents:', error);
    }
  }

  viewDocument(document: LawyerDocument): void {
    // Open document in new tab/window
    window.open(document.url, '_blank');
  }

  downloadDocument(doc: LawyerDocument): void {
    // Create download link
    const link = document.createElement('a');
    link.href = doc.url;
    link.download = doc.name;
    link.click();
  }

  goToPage(page: number): void {
    if (page >= 1 && page <= this.totalPages) {
      this.currentPage = page;
    }
  }

  getInitials(name: string): string {
    if (!name) return '?';
    return name
      .split(' ')
      .map(word => word.charAt(0).toUpperCase())
      .slice(0, 2)
      .join('');
  }

  onImageError(event: any, lawyer: any): void {
    // Hide the broken image and show placeholder instead
    event.target.style.display = 'none';
    lawyer.avatar = undefined;
  }

  openIBPLawyersList(): void {
    window.open('https://sc.judiciary.gov.ph/lawyers-list-2/', '_blank');
  }
}
