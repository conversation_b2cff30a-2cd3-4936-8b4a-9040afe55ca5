# Veritus Capstone Project - Comprehensive .gitignore

# ===== GENERAL =====
# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~
*.sw[mnpcod]

# Logs
*.log
log.txt
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# ===== NODE.JS =====
# Dependencies - CRITICAL: node_modules is too large for GitHub
node_modules/
*/node_modules/
**/node_modules/

# Optional npm cache directory
.npm

# Yarn Integrity file
.yarn-integrity

# Output of 'npm pack'
*.tgz

# ===== ANGULAR/IONIC =====
# Build outputs
dist/
*/dist/
**/dist/
build/
*/build/
**/build/

# Angular cache files - IMPORTANT: These are too large for GitHub
.angular/
*/.angular/
**/.angular/
.angular/cache/
*/.angular/cache/
**/.angular/cache/

# TypeScript
*.tsbuildinfo

# ===== CAPACITOR =====
# Native platforms (can be regenerated)
android/
ios/
*/android/
*/ios/
**/android/
**/ios/

# ===== FIREBASE =====
# Firebase cache
.firebase/
*/.firebase/
**/.firebase/

# Firebase debug logs
firebase-debug.log
firebase-debug.*.log

# ===== ENVIRONMENT FILES =====
.env
.env.local
.env.production
.env.*.local

# ===== TEMPORARY FILES =====
.tmp
*.tmp
*.tmp.*
tmp/
temp/

# ===== TESTING =====
coverage/
.nyc_output/

# ===== MISC =====
# Sublime Text
*.sublime-project
*.sublime-workspace

# Parcel cache
.cache
.parcel-cache

# Next.js build output
.next

# Nuxt.js build / generate output
.nuxt

# Gatsby files
.cache/
public

# Storybook build outputs
.out
.storybook-out

# Optional eslint cache
.eslintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# ===== PROJECT SPECIFIC =====
# Test documents
test-document.txt
*/test-document.txt

# Backup files
*.backup
*.bak

# Archive files
*.zip
*.tar.gz
*.rar
