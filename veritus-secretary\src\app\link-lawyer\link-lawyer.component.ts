import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import {
  FirebaseService,
  AssistantCode,
  LawyerSecretaryLink
} from '../services/firebase.service';

@Component({
  selector: 'app-link-lawyer',
  template: `
    <div class="link-lawyer-container">
      <div class="header-section">
        <h1>Link to Lawyer</h1>
        <p>Enter the assistant code provided by your lawyer</p>
      </div>
      
      <div class="form-section">
        <mat-card class="link-card">
          <mat-card-header>
            <mat-card-title>Assistant Code</mat-card-title>
            <mat-card-subtitle>8-character code from lawyer</mat-card-subtitle>
          </mat-card-header>
          
          <mat-card-content>
            <mat-form-field appearance="outline" class="full-width">
              <mat-label>Enter Code</mat-label>
              <input matInput 
                     [(ngModel)]="assistantCode" 
                     placeholder="XXXXXXXX"
                     maxlength="8"
                     (input)="onCodeInput($event)"
                     class="code-input">
            </mat-form-field>
            
            <div class="code-info">
              <mat-icon>info</mat-icon>
              <span>Codes are 8 characters long and expire after 24 hours</span>
            </div>
          </mat-card-content>
          
          <mat-card-actions>
            <button mat-raised-button 
                    color="primary" 
                    [disabled]="!isValidCode()"
                    (click)="linkToLawyer()"
                    class="full-width">
              <mat-icon>link</mat-icon>
              Link to Lawyer
            </button>
          </mat-card-actions>
        </mat-card>
      </div>
      
      <div class="linked-lawyers-section" *ngIf="linkedLawyers.length > 0">
        <h2>Linked Lawyers</h2>
        
        <div class="lawyers-grid">
          <mat-card *ngFor="let lawyer of linkedLawyers" class="lawyer-card">
            <mat-card-header>
              <mat-card-title>{{ lawyer.lawyerName }}</mat-card-title>
              <mat-card-subtitle>Linked on {{ formatDate(lawyer.createdAt) }}</mat-card-subtitle>
            </mat-card-header>
            
            <mat-card-content>
              <div class="permissions">
                <h4>Your Permissions:</h4>
                <div class="permission-chips">
                  <mat-chip *ngIf="lawyer.permissions.canManageCalendar" color="primary" selected>
                    Calendar
                  </mat-chip>
                  <mat-chip *ngIf="lawyer.permissions.canManageFiles" color="primary" selected>
                    Files
                  </mat-chip>
                  <mat-chip *ngIf="lawyer.permissions.canManageCases" color="primary" selected>
                    Cases
                  </mat-chip>
                </div>
              </div>
            </mat-card-content>
            
            <mat-card-actions>
              <button mat-button color="primary">Access Dashboard</button>
              <button mat-button>View Details</button>
            </mat-card-actions>
          </mat-card>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .link-lawyer-container {
      padding: 24px;
      max-width: 800px;
      margin: 0 auto;
    }
    
    .header-section {
      text-align: center;
      margin-bottom: 32px;
    }
    
    .header-section h1 {
      font-size: 2.5rem;
      color: #1976d2;
      margin-bottom: 8px;
    }
    
    .form-section {
      margin-bottom: 48px;
    }
    
    .link-card {
      max-width: 500px;
      margin: 0 auto;
    }
    
    .full-width {
      width: 100%;
    }
    
    .code-input {
      font-family: monospace;
      font-size: 18px;
      font-weight: bold;
      letter-spacing: 2px;
      text-transform: uppercase;
      text-align: center;
    }
    
    .code-info {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-top: 16px;
      color: #666;
      font-size: 14px;
    }
    
    .lawyers-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 24px;
    }
    
    .lawyer-card {
      height: fit-content;
    }
    
    .permissions h4 {
      margin: 0 0 12px 0;
      color: #666;
    }
  `]
})
export class LinkLawyerComponent implements OnInit {
  linkedLawyers: LawyerSecretaryLink[] = [];
  assistantCode = '';
  codePreview: AssistantCode | null = null;
  isLinking = false;
  isLoading = false;

  constructor(
    private router: Router,
    private firebaseService: FirebaseService
  ) { }

  ngOnInit() {
    this.loadLinkedLawyers();
  }

  async loadLinkedLawyers() {
    this.isLoading = true;
    const currentUser = this.firebaseService.getCurrentUser();

    if (currentUser) {
      try {
        this.linkedLawyers = await this.firebaseService.getLinkedLawyers(currentUser.uid);
      } catch (error) {
        console.error('Error loading linked lawyers:', error);
      }
    }

    this.isLoading = false;
  }

  onCodeInput(event: any) {
    const value = event.target.value.toUpperCase();
    this.assistantCode = value;

    if (this.codePreview) {
      this.codePreview = null;
    }
  }

  isValidCode(): boolean {
    return this.assistantCode.length === 8;
  }

  async linkToLawyer() {
    if (!this.isValidCode()) return;

    const currentUser = this.firebaseService.getCurrentUser();
    if (!currentUser) return;

    this.isLinking = true;

    try {
      const codeData = await this.firebaseService.validateAssistantCode(this.assistantCode);

      if (!codeData) {
        alert('Invalid or expired code');
        this.isLinking = false;
        return;
      }

      const alreadyLinked = this.linkedLawyers.some(link =>
        link.lawyerId === codeData.lawyerId
      );

      if (alreadyLinked) {
        alert('You are already linked to this lawyer');
        this.isLinking = false;
        return;
      }

      this.codePreview = codeData;
      this.isLinking = false;

    } catch (error) {
      console.error('Error validating code:', error);
      alert('Error validating code');
      this.isLinking = false;
    }
  }

  async confirmLink() {
    if (!this.codePreview) return;

    const currentUser = this.firebaseService.getCurrentUser();
    if (!currentUser) return;

    this.isLinking = true;

    try {
      const link = await this.firebaseService.useAssistantCode(
        this.assistantCode,
        currentUser.uid
      );

      this.linkedLawyers.unshift(link);
      this.assistantCode = '';
      this.codePreview = null;

      alert(`Successfully linked to ${link.lawyerName}`);

    } catch (error) {
      console.error('Error linking to lawyer:', error);
      alert('Failed to link to lawyer');
    } finally {
      this.isLinking = false;
    }
  }

  cancelPreview() {
    this.codePreview = null;
    this.assistantCode = '';
  }

  switchToLawyer(lawyer: LawyerSecretaryLink) {
    localStorage.setItem('selectedLawyerContext', JSON.stringify({
      lawyerId: lawyer.lawyerId,
      lawyerName: lawyer.lawyerName,
      permissions: lawyer.permissions
    }));

    this.router.navigate(['/dashboard']);
  }

  formatDate(date: any): string {
    return new Date(date).toLocaleDateString();
  }
}
