.setup-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, var(--veritus-primary) 0%, var(--veritus-primary-dark) 50%, var(--veritus-secondary) 100%);
  padding: 20px;
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
    pointer-events: none;
  }
}

.setup-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  width: 100%;
  max-width: 600px;
  overflow: hidden;
}

.setup-header {
  background: linear-gradient(135deg, var(--veritus-primary) 0%, var(--veritus-primary-dark) 100%);
  color: white;
  padding: 45px 30px;
  text-align: center;
  position: relative;

  .back-button {
    position: absolute;
    top: 20px;
    left: 20px;

    .back-btn {
      background: rgba(255, 255, 255, 0.2);
      color: white;
      border: 1px solid rgba(255, 255, 255, 0.3);
      padding: 8px 16px;
      border-radius: 6px;
      font-size: 0.9rem;
      cursor: pointer;
      transition: all 0.3s ease;
      backdrop-filter: blur(10px);

      &:hover {
        background: rgba(255, 255, 255, 0.3);
        border-color: rgba(255, 255, 255, 0.5);
        transform: translateX(-2px);
      }
    }
  }

  &::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--veritus-accent) 0%, var(--veritus-primary-light) 100%);
  }

  .logo h1 {
    margin: 0;
    font-size: 2.8rem;
    font-weight: 700;
    letter-spacing: -1px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .logo p {
    margin: 8px 0 5px 0;
    font-size: 1.1rem;
    opacity: 0.95;
    font-weight: 400;
  }

  .logo-subtitle {
    margin: 0;
    font-size: 0.85rem;
    opacity: 0.8;
    font-weight: 300;
    letter-spacing: 0.5px;
    text-transform: uppercase;
  }
}

.setup-form {
  padding: 40px 30px;
}

.form-group {
  margin-bottom: 25px;
  
  label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #333;
    font-size: 0.9rem;
  }
  
  .form-control {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #e1e5e9;
    border-radius: 8px;
    font-size: 1rem;
    transition: all 0.3s ease;
    box-sizing: border-box;
    
    &:focus {
      outline: none;
      border-color: var(--admin-primary);
      box-shadow: 0 0 0 3px rgba(196, 154, 86, 0.1);
    }
    
    &::placeholder {
      color: #999;
    }
  }

  .password-input-container {
    position: relative;

    .form-control {
      padding-right: 50px;
    }

    .password-toggle {
      position: absolute;
      right: 12px;
      top: 50%;
      transform: translateY(-50%);
      background: none;
      border: none;
      font-size: 1.2rem;
      cursor: pointer;
      color: #666;
      padding: 4px;
      border-radius: 4px;
      transition: all 0.3s ease;

      &:hover {
        background: rgba(0, 0, 0, 0.05);
        color: var(--admin-primary);
      }
    }
  }
}

.admin-info {
  margin-bottom: 25px;

  .info-card {
    background: linear-gradient(135deg, var(--veritus-light) 0%, var(--veritus-accent) 100%);
    border: 1px solid var(--veritus-primary-light);
    border-radius: 12px;
    padding: 25px;

    h4 {
      margin: 0 0 15px 0;
      color: var(--veritus-primary);
      font-size: 1.1rem;
      font-weight: 600;
    }

    p {
      margin: 0 0 15px 0;
      color: var(--veritus-text);
      font-weight: 500;
    }

    .permissions-list {
      list-style: none;
      margin: 0;
      padding: 0;

      li {
        padding: 8px 0;
        color: var(--veritus-success);
        font-size: 0.9rem;
        font-weight: 500;
        display: flex;
        align-items: center;

        &::before {
          content: '';
          width: 6px;
          height: 6px;
          background: var(--veritus-success);
          border-radius: 50%;
          margin-right: 12px;
        }
      }
    }
  }
}

.setup-btn {
  width: 100%;
  background: var(--admin-primary);
  color: white;
  border: none;
  padding: 14px 20px;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 20px;
  
  &:hover:not(:disabled) {
    background: var(--admin-primary-dark);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(196, 154, 86, 0.3);
  }
  
  &:disabled {
    opacity: 0.7;
    cursor: not-allowed;
  }
  
  .spinner {
    width: 20px;
    height: 20px;
    border: 2px solid transparent;
    border-top: 2px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-right: 10px;
  }
}

.success-message {
  text-align: center;
  padding: 30px;
  background: rgba(40, 167, 69, 0.1);
  border: 1px solid rgba(40, 167, 69, 0.3);
  border-radius: 8px;
  margin-bottom: 20px;
  
  .success-icon {
    font-size: 3rem;
    margin-bottom: 15px;
  }
  
  h3 {
    margin: 0 0 10px 0;
    color: #28a745;
    font-size: 1.3rem;
  }
  
  p {
    margin: 0 0 20px 0;
    color: #666;
  }
  
  .login-btn {
    background: #28a745;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 6px;
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.3s ease;
    
    &:hover {
      background: #218838;
    }
  }
}

.error-message {
  color: var(--admin-danger);
  font-size: 0.85rem;
  margin-top: 8px;
  padding: 10px;
  background: rgba(220, 53, 69, 0.1);
  border: 1px solid rgba(220, 53, 69, 0.3);
  border-radius: 6px;
}

.setup-info {
  background: #f8f9fa;
  padding: 30px;
  border-top: 1px solid #e1e5e9;
  
  h4 {
    margin: 0 0 15px 0;
    color: #333;
    font-size: 1.1rem;
  }
  
  ol {
    margin: 0 0 25px 0;
    padding-left: 20px;
    
    li {
      margin-bottom: 8px;
      color: #666;
      line-height: 1.4;
    }
  }
  
  .role-info {
    .role-item {
      margin-bottom: 10px;
      padding: 10px;
      background: white;
      border-radius: 6px;
      border: 1px solid #e1e5e9;
      
      strong {
        color: var(--admin-primary);
      }
    }
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@media (max-width: 768px) {
  .setup-container {
    padding: 10px;
  }
  
  .setup-header {
    padding: 30px 20px;
    
    h1 {
      font-size: 1.5rem;
    }
  }
  
  .setup-form {
    padding: 30px 20px;
  }
  
  .setup-info {
    padding: 20px;
  }
}
