{"version": 3, "file": "node_modules_ionic_core_dist_esm_ion-route_4_entry_js.js", "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AAC6G;AAChC;AACD;AACf;AAE7D,MAAMkB,KAAK,GAAG,MAAM;EAChBC,WAAWA,CAACC,OAAO,EAAE;IACjBnB,qDAAgB,CAAC,IAAI,EAAEmB,OAAO,CAAC;IAC/B,IAAI,CAACC,mBAAmB,GAAGlB,qDAAW,CAAC,IAAI,EAAE,qBAAqB,EAAE,CAAC,CAAC;IACtE,IAAI,CAACmB,GAAG,GAAG,EAAE;IACb,IAAI,CAACC,SAAS,GAAGC,SAAS;IAC1B,IAAI,CAACC,cAAc,GAAGD,SAAS;IAC/B,IAAI,CAACE,WAAW,GAAGF,SAAS;IAC5B,IAAI,CAACG,WAAW,GAAGH,SAAS;EAChC;EACAI,QAAQA,CAACC,QAAQ,EAAE;IACf,IAAI,CAACR,mBAAmB,CAACS,IAAI,CAACD,QAAQ,CAAC;EAC3C;EACAE,gBAAgBA,CAACF,QAAQ,EAAEG,QAAQ,EAAE;IACjC,IAAIH,QAAQ,KAAKG,QAAQ,EAAE;MACvB;IACJ;IACA,MAAMC,KAAK,GAAGJ,QAAQ,GAAGK,MAAM,CAACC,IAAI,CAACN,QAAQ,CAAC,GAAG,EAAE;IACnD,MAAMO,KAAK,GAAGJ,QAAQ,GAAGE,MAAM,CAACC,IAAI,CAACH,QAAQ,CAAC,GAAG,EAAE;IACnD,IAAIC,KAAK,CAACI,MAAM,KAAKD,KAAK,CAACC,MAAM,EAAE;MAC/B,IAAI,CAACT,QAAQ,CAACC,QAAQ,CAAC;MACvB;IACJ;IACA,KAAK,MAAMS,GAAG,IAAIL,KAAK,EAAE;MACrB,IAAIJ,QAAQ,CAACS,GAAG,CAAC,KAAKN,QAAQ,CAACM,GAAG,CAAC,EAAE;QACjC,IAAI,CAACV,QAAQ,CAACC,QAAQ,CAAC;QACvB;MACJ;IACJ;EACJ;EACAU,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAAClB,mBAAmB,CAACS,IAAI,CAAC,CAAC;EACnC;EACA,WAAWU,QAAQA,CAAA,EAAG;IAAE,OAAO;MAC3B,KAAK,EAAE,CAAC,UAAU,CAAC;MACnB,WAAW,EAAE,CAAC,UAAU,CAAC;MACzB,gBAAgB,EAAE,CAAC,kBAAkB;IACzC,CAAC;EAAE;AACP,CAAC;AAED,MAAMC,aAAa,GAAG,MAAM;EACxBtB,WAAWA,CAACC,OAAO,EAAE;IACjBnB,qDAAgB,CAAC,IAAI,EAAEmB,OAAO,CAAC;IAC/B,IAAI,CAACsB,uBAAuB,GAAGvC,qDAAW,CAAC,IAAI,EAAE,yBAAyB,EAAE,CAAC,CAAC;IAC9E,IAAI,CAACwC,IAAI,GAAGnB,SAAS;IACrB,IAAI,CAACoB,EAAE,GAAGpB,SAAS;EACvB;EACAqB,aAAaA,CAAA,EAAG;IACZ,IAAI,CAACH,uBAAuB,CAACZ,IAAI,CAAC,CAAC;EACvC;EACAS,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAACG,uBAAuB,CAACZ,IAAI,CAAC,CAAC;EACvC;EACA,WAAWU,QAAQA,CAAA,EAAG;IAAE,OAAO;MAC3B,MAAM,EAAE,CAAC,eAAe,CAAC;MACzB,IAAI,EAAE,CAAC,eAAe;IAC1B,CAAC;EAAE;AACP,CAAC;AAED,MAAMM,kBAAkB,GAAG,MAAM;AACjC,MAAMC,qBAAqB,GAAG,SAAS;AACvC,MAAMC,kBAAkB,GAAG,MAAM;;AAEjC;AACA,MAAMC,YAAY,GAAIC,QAAQ,IAAK;EAC/B,MAAMC,IAAI,GAAGD,QAAQ,CAACE,MAAM,CAAEC,CAAC,IAAKA,CAAC,CAAChB,MAAM,GAAG,CAAC,CAAC,CAACiB,IAAI,CAAC,GAAG,CAAC;EAC3D,OAAO,GAAG,GAAGH,IAAI;AACrB,CAAC;AACD,MAAMI,WAAW,GAAGA,CAACL,QAAQ,EAAEM,OAAO,EAAEC,WAAW,KAAK;EACpD,IAAInC,GAAG,GAAG2B,YAAY,CAACC,QAAQ,CAAC;EAChC,IAAIM,OAAO,EAAE;IACTlC,GAAG,GAAG,GAAG,GAAGA,GAAG;EACnB;EACA,IAAImC,WAAW,KAAKjC,SAAS,EAAE;IAC3BF,GAAG,IAAI,GAAG,GAAGmC,WAAW;EAC5B;EACA,OAAOnC,GAAG;AACd,CAAC;AACD,MAAMoC,aAAa,GAAGA,CAACC,OAAO,EAAEC,IAAI,EAAEJ,OAAO,EAAEN,QAAQ,EAAEW,SAAS,EAAEC,KAAK,EAAEL,WAAW,KAAK;EACvF,MAAMnC,GAAG,GAAGiC,WAAW,CAAC,CAAC,GAAGQ,SAAS,CAACH,IAAI,CAAC,CAACV,QAAQ,EAAE,GAAGA,QAAQ,CAAC,EAAEM,OAAO,EAAEC,WAAW,CAAC;EACzF,IAAII,SAAS,KAAKd,qBAAqB,EAAE;IACrCY,OAAO,CAACK,SAAS,CAACF,KAAK,EAAE,EAAE,EAAExC,GAAG,CAAC;EACrC,CAAC,MACI;IACDqC,OAAO,CAACM,YAAY,CAACH,KAAK,EAAE,EAAE,EAAExC,GAAG,CAAC;EACxC;AACJ,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM4C,eAAe,GAAIC,KAAK,IAAK;EAC/B,MAAMjB,QAAQ,GAAG,EAAE;EACnB,KAAK,MAAMkB,KAAK,IAAID,KAAK,EAAE;IACvB,KAAK,MAAME,OAAO,IAAID,KAAK,CAAClB,QAAQ,EAAE;MAClC,IAAImB,OAAO,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;QACpB;QACA,MAAMC,KAAK,GAAGF,KAAK,CAACG,MAAM,IAAIH,KAAK,CAACG,MAAM,CAACF,OAAO,CAACG,KAAK,CAAC,CAAC,CAAC,CAAC;QAC5D,IAAI,CAACF,KAAK,EAAE;UACR,OAAO,IAAI;QACf;QACApB,QAAQ,CAACuB,IAAI,CAACH,KAAK,CAAC;MACxB,CAAC,MACI,IAAID,OAAO,KAAK,EAAE,EAAE;QACrBnB,QAAQ,CAACuB,IAAI,CAACJ,OAAO,CAAC;MAC1B;IACJ;EACJ;EACA,OAAOnB,QAAQ;AACnB,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMwB,YAAY,GAAGA,CAACC,MAAM,EAAEzB,QAAQ,KAAK;EACvC,IAAIyB,MAAM,CAACtC,MAAM,GAAGa,QAAQ,CAACb,MAAM,EAAE;IACjC,OAAO,IAAI;EACf;EACA,IAAIsC,MAAM,CAACtC,MAAM,IAAI,CAAC,IAAIsC,MAAM,CAAC,CAAC,CAAC,KAAK,EAAE,EAAE;IACxC,OAAOzB,QAAQ;EACnB;EACA,KAAK,IAAI0B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,MAAM,CAACtC,MAAM,EAAEuC,CAAC,EAAE,EAAE;IACpC,IAAID,MAAM,CAACC,CAAC,CAAC,KAAK1B,QAAQ,CAAC0B,CAAC,CAAC,EAAE;MAC3B,OAAO,IAAI;IACf;EACJ;EACA,IAAI1B,QAAQ,CAACb,MAAM,KAAKsC,MAAM,CAACtC,MAAM,EAAE;IACnC,OAAO,CAAC,EAAE,CAAC;EACf;EACA,OAAOa,QAAQ,CAACsB,KAAK,CAACG,MAAM,CAACtC,MAAM,CAAC;AACxC,CAAC;AACD,MAAMwC,YAAY,GAAGA,CAACC,GAAG,EAAElB,IAAI,EAAEJ,OAAO,KAAK;EACzC,MAAMmB,MAAM,GAAGZ,SAAS,CAACH,IAAI,CAAC,CAACV,QAAQ;EACvC,MAAM6B,QAAQ,GAAGvB,OAAO,GAAGsB,GAAG,CAACE,IAAI,CAACR,KAAK,CAAC,CAAC,CAAC,GAAGM,GAAG,CAACC,QAAQ;EAC3D,MAAM7B,QAAQ,GAAGa,SAAS,CAACgB,QAAQ,CAAC,CAAC7B,QAAQ;EAC7C,OAAOwB,YAAY,CAACC,MAAM,EAAEzB,QAAQ,CAAC;AACzC,CAAC;AACD;AACA;AACA;AACA;AACA;AACA,MAAMa,SAAS,GAAIZ,IAAI,IAAK;EACxB,IAAID,QAAQ,GAAG,CAAC,EAAE,CAAC;EACnB,IAAIO,WAAW;EACf,IAAIN,IAAI,IAAI,IAAI,EAAE;IACd,MAAM8B,OAAO,GAAG9B,IAAI,CAAC+B,OAAO,CAAC,GAAG,CAAC;IACjC,IAAID,OAAO,GAAG,CAAC,CAAC,EAAE;MACdxB,WAAW,GAAGN,IAAI,CAACgC,SAAS,CAACF,OAAO,GAAG,CAAC,CAAC;MACzC9B,IAAI,GAAGA,IAAI,CAACgC,SAAS,CAAC,CAAC,EAAEF,OAAO,CAAC;IACrC;IACA/B,QAAQ,GAAGC,IAAI,CACViC,KAAK,CAAC,GAAG,CAAC,CACVC,GAAG,CAAEhC,CAAC,IAAKA,CAAC,CAACiC,IAAI,CAAC,CAAC,CAAC,CACpBlC,MAAM,CAAEC,CAAC,IAAKA,CAAC,CAAChB,MAAM,GAAG,CAAC,CAAC;IAChC,IAAIa,QAAQ,CAACb,MAAM,KAAK,CAAC,EAAE;MACvBa,QAAQ,GAAG,CAAC,EAAE,CAAC;IACnB;EACJ;EACA,OAAO;IAAEA,QAAQ;IAAEO;EAAY,CAAC;AACpC,CAAC;AAED,MAAM8B,WAAW,GAAIC,MAAM,IAAK;EAC5BC,OAAO,CAACC,KAAK,CAAC,qBAAqBF,MAAM,CAACnD,MAAM,GAAG,CAAC;EACpD,KAAK,MAAM8B,KAAK,IAAIqB,MAAM,EAAE;IACxB,MAAMtC,QAAQ,GAAG,EAAE;IACnBiB,KAAK,CAACwB,OAAO,CAAE3F,CAAC,IAAKkD,QAAQ,CAACuB,IAAI,CAAC,GAAGzE,CAAC,CAACkD,QAAQ,CAAC,CAAC;IAClD,MAAM0C,GAAG,GAAGzB,KAAK,CAACkB,GAAG,CAAErF,CAAC,IAAKA,CAAC,CAAC6F,EAAE,CAAC;IAClCJ,OAAO,CAACK,KAAK,CAAC,MAAM7C,YAAY,CAACC,QAAQ,CAAC,EAAE,EAAE,uCAAuC,EAAE,MAAM,EAAE,IAAI0C,GAAG,CAACtC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC;EACzH;EACAmC,OAAO,CAACM,QAAQ,CAAC,CAAC;AACtB,CAAC;AACD,MAAMC,cAAc,GAAIC,SAAS,IAAK;EAClCR,OAAO,CAACC,KAAK,CAAC,wBAAwBO,SAAS,CAAC5D,MAAM,GAAG,CAAC;EAC1D,KAAK,MAAM6D,QAAQ,IAAID,SAAS,EAAE;IAC9B,IAAIC,QAAQ,CAACtD,EAAE,EAAE;MACb6C,OAAO,CAACK,KAAK,CAAC,QAAQ,EAAE,MAAM7C,YAAY,CAACiD,QAAQ,CAACvD,IAAI,CAAC,EAAE,EAAE,mBAAmB,EAAE,OAAO,EAAE,MAAMM,YAAY,CAACiD,QAAQ,CAACtD,EAAE,CAACM,QAAQ,CAAC,EAAE,EAAE,mBAAmB,CAAC;IAC/J;EACJ;EACAuC,OAAO,CAACM,QAAQ,CAAC,CAAC;AACtB,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMI,cAAa;EAAA,IAAAC,IAAA,GAAAC,6KAAA,CAAG,WAAOzC,IAAI,EAAEO,KAAK,EAAEN,SAAS,EAAEyC,KAAK,EAAEC,OAAO,GAAG,KAAK,EAAEC,SAAS,EAAK;IACvF,IAAI;MACA;MACA,MAAMC,MAAM,GAAGC,aAAa,CAAC9C,IAAI,CAAC;MAClC;MACA,IAAI0C,KAAK,IAAInC,KAAK,CAAC9B,MAAM,IAAI,CAACoE,MAAM,EAAE;QAClC,OAAOF,OAAO;MAClB;MACA,MAAM,IAAII,OAAO,CAAEC,OAAO,IAAKlG,uDAAgB,CAAC+F,MAAM,EAAEG,OAAO,CAAC,CAAC;MACjE,MAAMxC,KAAK,GAAGD,KAAK,CAACmC,KAAK,CAAC;MAC1B,MAAMO,MAAM,SAASJ,MAAM,CAACK,UAAU,CAAC1C,KAAK,CAACyB,EAAE,EAAEzB,KAAK,CAACG,MAAM,EAAEV,SAAS,EAAE2C,SAAS,CAAC;MACpF;MACA;MACA,IAAIK,MAAM,CAACN,OAAO,EAAE;QAChB1C,SAAS,GAAGf,kBAAkB;QAC9ByD,OAAO,GAAG,IAAI;MAClB;MACA;MACAA,OAAO,SAASJ,cAAa,CAACU,MAAM,CAACE,OAAO,EAAE5C,KAAK,EAAEN,SAAS,EAAEyC,KAAK,GAAG,CAAC,EAAEC,OAAO,EAAEC,SAAS,CAAC;MAC9F;MACA;MACA,IAAIK,MAAM,CAACG,WAAW,EAAE;QACpB,MAAMH,MAAM,CAACG,WAAW,CAAC,CAAC;MAC9B;MACA,OAAOT,OAAO;IAClB,CAAC,CACD,OAAOU,CAAC,EAAE;MACNxB,OAAO,CAACyB,KAAK,CAACD,CAAC,CAAC;MAChB,OAAO,KAAK;IAChB;EACJ,CAAC;EAAA,gBA9BKd,aAAaA,CAAAgB,EAAA,EAAAC,GAAA,EAAAC,GAAA,EAAAC,GAAA;IAAA,OAAAlB,IAAA,CAAAmB,KAAA,OAAAC,SAAA;EAAA;AAAA,GA8BlB;AACD;AACA;AACA;AACA;AACA;AACA,MAAMC,YAAY;EAAA,IAAAC,KAAA,GAAArB,6KAAA,CAAG,WAAOzC,IAAI,EAAK;IACjC,MAAMgC,GAAG,GAAG,EAAE;IACd,IAAIa,MAAM;IACV,IAAIkB,IAAI,GAAG/D,IAAI;IACf;IACA,OAAQ6C,MAAM,GAAGC,aAAa,CAACiB,IAAI,CAAC,EAAG;MACnC,MAAM9B,EAAE,SAASY,MAAM,CAACmB,UAAU,CAAC,CAAC;MACpC,IAAI/B,EAAE,EAAE;QACJ8B,IAAI,GAAG9B,EAAE,CAACkB,OAAO;QACjBlB,EAAE,CAACkB,OAAO,GAAGvF,SAAS;QACtBoE,GAAG,CAACnB,IAAI,CAACoB,EAAE,CAAC;MAChB,CAAC,MACI;QACD;MACJ;IACJ;IACA,OAAO;MAAED,GAAG;MAAEa;IAAO,CAAC;EAC1B,CAAC;EAAA,gBAjBKgB,YAAYA,CAAAI,GAAA;IAAA,OAAAH,KAAA,CAAAH,KAAA,OAAAC,SAAA;EAAA;AAAA,GAiBjB;AACD,MAAMM,gBAAgB,GAAGA,CAAA,KAAM;EAC3B,IAAIpB,aAAa,CAACqB,QAAQ,CAACC,IAAI,CAAC,EAAE;IAC9B,OAAOrB,OAAO,CAACC,OAAO,CAAC,CAAC;EAC5B;EACA,OAAO,IAAID,OAAO,CAAEC,OAAO,IAAK;IAC5BqB,MAAM,CAACC,gBAAgB,CAAC,gBAAgB,EAAE,MAAMtB,OAAO,CAAC,CAAC,EAAE;MAAEuB,IAAI,EAAE;IAAK,CAAC,CAAC;EAC9E,CAAC,CAAC;AACN,CAAC;AACD;AACA,MAAMC,eAAe,GAAG,4FAA4F;AACpH,MAAM1B,aAAa,GAAI9C,IAAI,IAAK;EAC5B,IAAI,CAACA,IAAI,EAAE;IACP,OAAOpC,SAAS;EACpB;EACA,IAAIoC,IAAI,CAACyE,OAAO,CAACD,eAAe,CAAC,EAAE;IAC/B,OAAOxE,IAAI;EACf;EACA,MAAM6C,MAAM,GAAG7C,IAAI,CAAC0E,aAAa,CAACF,eAAe,CAAC;EAClD,OAAO3B,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAGA,MAAM,GAAGjF,SAAS;AACpE,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM+G,eAAe,GAAGA,CAACrF,QAAQ,EAAEgD,QAAQ,KAAK;EAC5C,MAAM;IAAEvD,IAAI;IAAEC;EAAG,CAAC,GAAGsD,QAAQ;EAC7B,IAAItD,EAAE,KAAKpB,SAAS,EAAE;IAClB,OAAO,KAAK;EAChB;EACA,IAAImB,IAAI,CAACN,MAAM,GAAGa,QAAQ,CAACb,MAAM,EAAE;IAC/B,OAAO,KAAK;EAChB;EACA,KAAK,IAAIuC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGjC,IAAI,CAACN,MAAM,EAAEuC,CAAC,EAAE,EAAE;IAClC,MAAM4D,QAAQ,GAAG7F,IAAI,CAACiC,CAAC,CAAC;IACxB,IAAI4D,QAAQ,KAAK,GAAG,EAAE;MAClB,OAAO,IAAI;IACf;IACA,IAAIA,QAAQ,KAAKtF,QAAQ,CAAC0B,CAAC,CAAC,EAAE;MAC1B,OAAO,KAAK;IAChB;EACJ;EACA,OAAOjC,IAAI,CAACN,MAAM,KAAKa,QAAQ,CAACb,MAAM;AAC1C,CAAC;AACD;AACA,MAAMoG,iBAAiB,GAAGA,CAACvF,QAAQ,EAAE+C,SAAS,KAAK;EAC/C,OAAOA,SAAS,CAACyC,IAAI,CAAExC,QAAQ,IAAKqC,eAAe,CAACrF,QAAQ,EAAEgD,QAAQ,CAAC,CAAC;AAC5E,CAAC;AACD,MAAMyC,UAAU,GAAGA,CAAC/C,GAAG,EAAEzB,KAAK,KAAK;EAC/B,MAAMyE,GAAG,GAAGC,IAAI,CAACC,GAAG,CAAClD,GAAG,CAACvD,MAAM,EAAE8B,KAAK,CAAC9B,MAAM,CAAC;EAC9C,IAAI0G,KAAK,GAAG,CAAC;EACb,KAAK,IAAInE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGgE,GAAG,EAAEhE,CAAC,EAAE,EAAE;IAC1B,MAAMoE,OAAO,GAAGpD,GAAG,CAAChB,CAAC,CAAC;IACtB,MAAMqE,UAAU,GAAG9E,KAAK,CAACS,CAAC,CAAC;IAC3B;IACA,IAAIoE,OAAO,CAACnD,EAAE,CAACqD,WAAW,CAAC,CAAC,KAAKD,UAAU,CAACpD,EAAE,EAAE;MAC5C;IACJ;IACA,IAAImD,OAAO,CAACzE,MAAM,EAAE;MAChB,MAAM4E,aAAa,GAAGjH,MAAM,CAACC,IAAI,CAAC6G,OAAO,CAACzE,MAAM,CAAC;MACjD;MACA,IAAI4E,aAAa,CAAC9G,MAAM,KAAK4G,UAAU,CAAC/F,QAAQ,CAACb,MAAM,EAAE;QACrD;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA,MAAM+G,cAAc,GAAGD,aAAa,CAAC9D,GAAG,CAAE/C,GAAG,IAAK,IAAIA,GAAG,EAAE,CAAC;QAC5D,KAAK,IAAI+G,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,cAAc,CAAC/G,MAAM,EAAEgH,CAAC,EAAE,EAAE;UAC5C;UACA,IAAID,cAAc,CAACC,CAAC,CAAC,CAACH,WAAW,CAAC,CAAC,KAAKD,UAAU,CAAC/F,QAAQ,CAACmG,CAAC,CAAC,EAAE;YAC5D;UACJ;UACA;UACAN,KAAK,EAAE;QACX;MACJ;IACJ;IACA;IACAA,KAAK,EAAE;EACX;EACA,OAAOA,KAAK;AAChB,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMO,eAAe,GAAGA,CAACpG,QAAQ,EAAEiB,KAAK,KAAK;EACzC,MAAMoF,aAAa,GAAG,IAAIC,cAAc,CAACtG,QAAQ,CAAC;EAClD,IAAIuG,cAAc,GAAG,KAAK;EAC1B,IAAIC,SAAS;EACb,KAAK,IAAI9E,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGT,KAAK,CAAC9B,MAAM,EAAEuC,CAAC,EAAE,EAAE;IACnC,MAAM+E,aAAa,GAAGxF,KAAK,CAACS,CAAC,CAAC,CAAC1B,QAAQ;IACvC,IAAIyG,aAAa,CAAC,CAAC,CAAC,KAAK,EAAE,EAAE;MACzBF,cAAc,GAAG,IAAI;IACzB,CAAC,MACI;MACD,KAAK,MAAMpF,OAAO,IAAIsF,aAAa,EAAE;QACjC,MAAMC,IAAI,GAAGL,aAAa,CAACM,IAAI,CAAC,CAAC;QACjC;QACA,IAAIxF,OAAO,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;UACpB,IAAIuF,IAAI,KAAK,EAAE,EAAE;YACb,OAAO,IAAI;UACf;UACAF,SAAS,GAAGA,SAAS,IAAI,EAAE;UAC3B,MAAMnF,MAAM,GAAGmF,SAAS,CAAC9E,CAAC,CAAC,KAAK8E,SAAS,CAAC9E,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;UAClDL,MAAM,CAACF,OAAO,CAACG,KAAK,CAAC,CAAC,CAAC,CAAC,GAAGoF,IAAI;QACnC,CAAC,MACI,IAAIA,IAAI,KAAKvF,OAAO,EAAE;UACvB,OAAO,IAAI;QACf;MACJ;MACAoF,cAAc,GAAG,KAAK;IAC1B;EACJ;EACA,MAAMpB,OAAO,GAAGoB,cAAc,GAAGA,cAAc,MAAMF,aAAa,CAACM,IAAI,CAAC,CAAC,KAAK,EAAE,CAAC,GAAG,IAAI;EACxF,IAAI,CAACxB,OAAO,EAAE;IACV,OAAO,IAAI;EACf;EACA,IAAIqB,SAAS,EAAE;IACX,OAAOvF,KAAK,CAACkB,GAAG,CAAC,CAACjB,KAAK,EAAEQ,CAAC,MAAM;MAC5BiB,EAAE,EAAEzB,KAAK,CAACyB,EAAE;MACZ3C,QAAQ,EAAEkB,KAAK,CAAClB,QAAQ;MACxBqB,MAAM,EAAEuF,WAAW,CAAC1F,KAAK,CAACG,MAAM,EAAEmF,SAAS,CAAC9E,CAAC,CAAC,CAAC;MAC/CjD,WAAW,EAAEyC,KAAK,CAACzC,WAAW;MAC9BD,WAAW,EAAE0C,KAAK,CAAC1C;IACvB,CAAC,CAAC,CAAC;EACP;EACA,OAAOyC,KAAK;AAChB,CAAC;AACD;AACA;AACA;AACA;AACA,MAAM2F,WAAW,GAAGA,CAACC,CAAC,EAAE/I,CAAC,KAAK;EAC1B,OAAO+I,CAAC,IAAI/I,CAAC,GAAGkB,MAAM,CAAC8H,MAAM,CAAC9H,MAAM,CAAC8H,MAAM,CAAC,CAAC,CAAC,EAAED,CAAC,CAAC,EAAE/I,CAAC,CAAC,GAAGQ,SAAS;AACtE,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMyI,eAAe,GAAGA,CAACrE,GAAG,EAAEsE,MAAM,KAAK;EACrC,IAAIC,KAAK,GAAG,IAAI;EAChB,IAAIC,UAAU,GAAG,CAAC;EAClB,KAAK,MAAMjG,KAAK,IAAI+F,MAAM,EAAE;IACxB,MAAMnB,KAAK,GAAGJ,UAAU,CAAC/C,GAAG,EAAEzB,KAAK,CAAC;IACpC,IAAI4E,KAAK,GAAGqB,UAAU,EAAE;MACpBD,KAAK,GAAGhG,KAAK;MACbiG,UAAU,GAAGrB,KAAK;IACtB;EACJ;EACA,IAAIoB,KAAK,EAAE;IACP,OAAOA,KAAK,CAAC9E,GAAG,CAAC,CAACjB,KAAK,EAAEQ,CAAC,KAAK;MAC3B,IAAIyF,EAAE;MACN,OAAQ;QACJxE,EAAE,EAAEzB,KAAK,CAACyB,EAAE;QACZ3C,QAAQ,EAAEkB,KAAK,CAAClB,QAAQ;QACxBqB,MAAM,EAAEuF,WAAW,CAAC1F,KAAK,CAACG,MAAM,EAAE,CAAC8F,EAAE,GAAGzE,GAAG,CAAChB,CAAC,CAAC,MAAM,IAAI,IAAIyF,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC9F,MAAM;MAClG,CAAC;IACL,CAAC,CAAC;EACN;EACA,OAAO,IAAI;AACf,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM+F,oBAAoB,GAAGA,CAACpH,QAAQ,EAAEgH,MAAM,KAAK;EAC/C,IAAIC,KAAK,GAAG,IAAI;EAChB,IAAII,SAAS,GAAG,CAAC;EACjB,KAAK,MAAMpG,KAAK,IAAI+F,MAAM,EAAE;IACxB,MAAMM,YAAY,GAAGlB,eAAe,CAACpG,QAAQ,EAAEiB,KAAK,CAAC;IACrD,IAAIqG,YAAY,KAAK,IAAI,EAAE;MACvB,MAAMzB,KAAK,GAAG0B,eAAe,CAACD,YAAY,CAAC;MAC3C,IAAIzB,KAAK,GAAGwB,SAAS,EAAE;QACnBA,SAAS,GAAGxB,KAAK;QACjBoB,KAAK,GAAGK,YAAY;MACxB;IACJ;EACJ;EACA,OAAOL,KAAK;AAChB,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMM,eAAe,GAAItG,KAAK,IAAK;EAC/B,IAAI4E,KAAK,GAAG,CAAC;EACb,IAAI2B,KAAK,GAAG,CAAC;EACb,KAAK,MAAMtG,KAAK,IAAID,KAAK,EAAE;IACvB,KAAK,MAAME,OAAO,IAAID,KAAK,CAAClB,QAAQ,EAAE;MAClC,IAAImB,OAAO,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;QACpB0E,KAAK,IAAIF,IAAI,CAAC8B,GAAG,CAAC,CAAC,EAAED,KAAK,CAAC;MAC/B,CAAC,MACI,IAAIrG,OAAO,KAAK,EAAE,EAAE;QACrB0E,KAAK,IAAIF,IAAI,CAAC8B,GAAG,CAAC,CAAC,EAAED,KAAK,CAAC;MAC/B;MACAA,KAAK,EAAE;IACX;EACJ;EACA,OAAO3B,KAAK;AAChB,CAAC;AACD,MAAMS,cAAc,CAAC;EACjBrI,WAAWA,CAAC+B,QAAQ,EAAE;IAClB,IAAI,CAACA,QAAQ,GAAGA,QAAQ,CAACsB,KAAK,CAAC,CAAC;EACpC;EACAqF,IAAIA,CAAA,EAAG;IACH,IAAI,IAAI,CAAC3G,QAAQ,CAACb,MAAM,GAAG,CAAC,EAAE;MAC1B,OAAO,IAAI,CAACa,QAAQ,CAAC0H,KAAK,CAAC,CAAC;IAChC;IACA,OAAO,EAAE;EACb;AACJ;AAEA,MAAMC,QAAQ,GAAGA,CAACC,EAAE,EAAEC,IAAI,KAAK;EAC3B,IAAIA,IAAI,IAAID,EAAE,EAAE;IACZ,OAAOA,EAAE,CAACC,IAAI,CAAC;EACnB;EACA,IAAID,EAAE,CAACE,YAAY,CAACD,IAAI,CAAC,EAAE;IACvB,OAAOD,EAAE,CAACG,YAAY,CAACF,IAAI,CAAC;EAChC;EACA,OAAO,IAAI;AACf,CAAC;AACD;AACA;AACA;AACA;AACA;AACA,MAAMG,aAAa,GAAItH,IAAI,IAAK;EAC5B,OAAOuH,KAAK,CAACxI,IAAI,CAACiB,IAAI,CAACwH,QAAQ,CAAC,CAC3BhI,MAAM,CAAE0H,EAAE,IAAKA,EAAE,CAACO,OAAO,KAAK,oBAAoB,CAAC,CACnDhG,GAAG,CAAEyF,EAAE,IAAK;IACb,MAAMlI,EAAE,GAAGiI,QAAQ,CAACC,EAAE,EAAE,IAAI,CAAC;IAC7B,OAAO;MACHnI,IAAI,EAAEoB,SAAS,CAAC8G,QAAQ,CAACC,EAAE,EAAE,MAAM,CAAC,CAAC,CAAC5H,QAAQ;MAC9CN,EAAE,EAAEA,EAAE,IAAI,IAAI,GAAGpB,SAAS,GAAGuC,SAAS,CAACnB,EAAE;IAC7C,CAAC;EACL,CAAC,CAAC;AACN,CAAC;AACD;AACA;AACA;AACA;AACA;AACA,MAAM0I,UAAU,GAAI1H,IAAI,IAAK;EACzB,OAAO2H,iBAAiB,CAACC,cAAc,CAAC5H,IAAI,CAAC,CAAC;AAClD,CAAC;AACD;AACA;AACA;AACA;AACA;AACA,MAAM4H,cAAc,GAAI7D,IAAI,IAAK;EAC7B,OAAOwD,KAAK,CAACxI,IAAI,CAACgF,IAAI,CAACyD,QAAQ,CAAC,CAC3BhI,MAAM,CAAE0H,EAAE,IAAKA,EAAE,CAACO,OAAO,KAAK,WAAW,IAAIP,EAAE,CAACvJ,SAAS,CAAC,CAC1D8D,GAAG,CAAEyF,EAAE,IAAK;IACb,MAAMvJ,SAAS,GAAGsJ,QAAQ,CAACC,EAAE,EAAE,WAAW,CAAC;IAC3C,OAAO;MACH5H,QAAQ,EAAEa,SAAS,CAAC8G,QAAQ,CAACC,EAAE,EAAE,KAAK,CAAC,CAAC,CAAC5H,QAAQ;MACjD2C,EAAE,EAAEtE,SAAS,CAAC2H,WAAW,CAAC,CAAC;MAC3B3E,MAAM,EAAEuG,EAAE,CAACrJ,cAAc;MACzBC,WAAW,EAAEoJ,EAAE,CAACpJ,WAAW;MAC3BC,WAAW,EAAEmJ,EAAE,CAACnJ,WAAW;MAC3ByJ,QAAQ,EAAEI,cAAc,CAACV,EAAE;IAC/B,CAAC;EACL,CAAC,CAAC;AACN,CAAC;AACD;AACA;AACA;AACA;AACA;AACA,MAAMS,iBAAiB,GAAIE,KAAK,IAAK;EACjC,MAAMvB,MAAM,GAAG,EAAE;EACjB,KAAK,MAAMvC,IAAI,IAAI8D,KAAK,EAAE;IACtBC,WAAW,CAAC,EAAE,EAAExB,MAAM,EAAEvC,IAAI,CAAC;EACjC;EACA,OAAOuC,MAAM;AACjB,CAAC;AACD;AACA,MAAMwB,WAAW,GAAGA,CAACvH,KAAK,EAAE+F,MAAM,EAAEvC,IAAI,KAAK;EACzCxD,KAAK,GAAG,CACJ,GAAGA,KAAK,EACR;IACI0B,EAAE,EAAE8B,IAAI,CAAC9B,EAAE;IACX3C,QAAQ,EAAEyE,IAAI,CAACzE,QAAQ;IACvBqB,MAAM,EAAEoD,IAAI,CAACpD,MAAM;IACnB7C,WAAW,EAAEiG,IAAI,CAACjG,WAAW;IAC7BC,WAAW,EAAEgG,IAAI,CAAChG;EACtB,CAAC,CACJ;EACD,IAAIgG,IAAI,CAACyD,QAAQ,CAAC/I,MAAM,KAAK,CAAC,EAAE;IAC5B6H,MAAM,CAACzF,IAAI,CAACN,KAAK,CAAC;IAClB;EACJ;EACA,KAAK,MAAMwH,KAAK,IAAIhE,IAAI,CAACyD,QAAQ,EAAE;IAC/BM,WAAW,CAACvH,KAAK,EAAE+F,MAAM,EAAEyB,KAAK,CAAC;EACrC;AACJ,CAAC;AAED,MAAMC,MAAM,GAAG,MAAM;EACjBzK,WAAWA,CAACC,OAAO,EAAE;IACjBnB,qDAAgB,CAAC,IAAI,EAAEmB,OAAO,CAAC;IAC/B,IAAI,CAACyK,kBAAkB,GAAG1L,qDAAW,CAAC,IAAI,EAAE,oBAAoB,EAAE,CAAC,CAAC;IACpE,IAAI,CAAC2L,iBAAiB,GAAG3L,qDAAW,CAAC,IAAI,EAAE,mBAAmB,EAAE,CAAC,CAAC;IAClE,IAAI,CAAC4L,YAAY,GAAG,IAAI;IACxB,IAAI,CAACC,IAAI,GAAG,KAAK;IACjB,IAAI,CAAClI,KAAK,GAAG,CAAC;IACd,IAAI,CAACmI,SAAS,GAAG,CAAC;IAClB,IAAI,CAACrI,IAAI,GAAG,GAAG;IACf,IAAI,CAACJ,OAAO,GAAG,IAAI;EACvB;EACM0I,iBAAiBA,CAAA,EAAG;IAAA,IAAAC,KAAA;IAAA,OAAA9F,6KAAA;MACtB,MAAMyB,gBAAgB,CAAC,CAAC;MACxB,MAAMsE,UAAU,SAASD,KAAI,CAACE,SAAS,CAACF,KAAI,CAACG,WAAW,CAAC,CAAC,CAAC;MAC3D,IAAIF,UAAU,KAAK,IAAI,EAAE;QACrB,IAAI,OAAOA,UAAU,KAAK,QAAQ,EAAE;UAChC,MAAM;YAAElG;UAAS,CAAC,GAAGkG,UAAU;UAC/B,MAAMjJ,IAAI,GAAGY,SAAS,CAACmC,QAAQ,CAAC;UAChCiG,KAAI,CAACI,WAAW,CAACpJ,IAAI,CAACD,QAAQ,EAAEJ,kBAAkB,EAAEK,IAAI,CAACM,WAAW,CAAC;UACrE,MAAM0I,KAAI,CAACK,iBAAiB,CAACrJ,IAAI,CAACD,QAAQ,EAAEJ,kBAAkB,CAAC;QACnE;MACJ,CAAC,MACI;QACD,MAAMqJ,KAAI,CAACM,eAAe,CAAC,CAAC;MAChC;IAAC;EACL;EACAC,gBAAgBA,CAAA,EAAG;IACfzE,MAAM,CAACC,gBAAgB,CAAC,yBAAyB,EAAEtH,uDAAQ,CAAC,IAAI,CAAC+L,iBAAiB,CAACC,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC,CAAC;IACnG3E,MAAM,CAACC,gBAAgB,CAAC,qBAAqB,EAAEtH,uDAAQ,CAAC,IAAI,CAAC6L,eAAe,CAACG,IAAI,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC;EAClG;EACMC,UAAUA,CAAA,EAAG;IAAA,IAAAC,MAAA;IAAA,OAAAzG,6KAAA;MACf,MAAMxC,SAAS,GAAGiJ,MAAI,CAACC,gBAAgB,CAAC,CAAC;MACzC,IAAI7J,QAAQ,GAAG4J,MAAI,CAACR,WAAW,CAAC,CAAC;MACjC,MAAMF,UAAU,SAASU,MAAI,CAACT,SAAS,CAACnJ,QAAQ,CAAC;MACjD,IAAIkJ,UAAU,KAAK,IAAI,EAAE;QACrB,IAAI,OAAOA,UAAU,KAAK,QAAQ,EAAE;UAChClJ,QAAQ,GAAGa,SAAS,CAACqI,UAAU,CAAClG,QAAQ,CAAC,CAAChD,QAAQ;QACtD,CAAC,MACI;UACD,OAAO,KAAK;QAChB;MACJ;MACA,OAAO4J,MAAI,CAACN,iBAAiB,CAACtJ,QAAQ,EAAEW,SAAS,CAAC;IAAC;EACvD;EACAmJ,YAAYA,CAACC,EAAE,EAAE;IACbA,EAAE,CAACC,MAAM,CAACC,QAAQ,CAAC,CAAC,EAAGC,kBAAkB,IAAK;MAC1C,IAAI,CAACC,IAAI,CAAC,CAAC;MACXD,kBAAkB,CAAC,CAAC;IACxB,CAAC,CAAC;EACN;EACA;EACME,aAAaA,CAAA,EAAG;IAAA,IAAAC,MAAA;IAAA,OAAAlH,6KAAA;MAClB,MAAM+F,UAAU,SAASmB,MAAI,CAAClB,SAAS,CAAC,CAAC;MACzC,IAAID,UAAU,KAAK,IAAI,EAAE;QACrB,IAAI,OAAOA,UAAU,KAAK,QAAQ,EAAE;UAChC,OAAOA,UAAU,CAAClG,QAAQ;QAC9B,CAAC,MACI;UACD,OAAO,KAAK;QAChB;MACJ;MACA,OAAO,IAAI;IAAC;EAChB;EACA;AACJ;AACA;AACA;AACA;AACA;EACUzB,IAAIA,CAAA+I,GAAA,EAAyC;IAAA,IAAAC,MAAA;IAAA,OAAApH,6KAAA,YAAxClD,IAAI,EAAEU,SAAS,GAAG,SAAS,EAAE2C,SAAS;MAC7C,IAAI6D,EAAE;MACN,IAAIlH,IAAI,CAACuK,UAAU,CAAC,GAAG,CAAC,EAAE;QACtB,MAAMC,WAAW,GAAG,CAACtD,EAAE,GAAGoD,MAAI,CAAC1B,YAAY,MAAM,IAAI,IAAI1B,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,GAAG;QACjF;QACA,MAAM/I,GAAG,GAAG,IAAIsM,GAAG,CAACzK,IAAI,EAAE,gBAAgBwK,WAAW,EAAE,CAAC;QACxDxK,IAAI,GAAG7B,GAAG,CAACyD,QAAQ,GAAGzD,GAAG,CAACuM,MAAM;MACpC;MACA,IAAIC,UAAU,GAAG/J,SAAS,CAACZ,IAAI,CAAC;MAChC,MAAMiJ,UAAU,SAASqB,MAAI,CAACpB,SAAS,CAACyB,UAAU,CAAC5K,QAAQ,CAAC;MAC5D,IAAIkJ,UAAU,KAAK,IAAI,EAAE;QACrB,IAAI,OAAOA,UAAU,KAAK,QAAQ,EAAE;UAChC0B,UAAU,GAAG/J,SAAS,CAACqI,UAAU,CAAClG,QAAQ,CAAC;QAC/C,CAAC,MACI;UACD,OAAO,KAAK;QAChB;MACJ;MACAuH,MAAI,CAAClB,WAAW,CAACuB,UAAU,CAAC5K,QAAQ,EAAEW,SAAS,EAAEiK,UAAU,CAACrK,WAAW,CAAC;MACxE,OAAOgK,MAAI,CAACjB,iBAAiB,CAACsB,UAAU,CAAC5K,QAAQ,EAAEW,SAAS,EAAE2C,SAAS,CAAC;IAAC,GAAAe,KAAA,OAAAC,SAAA;EAC7E;EACA;EACA6F,IAAIA,CAAA,EAAG;IACHpF,MAAM,CAACtE,OAAO,CAAC0J,IAAI,CAAC,CAAC;IACrB,OAAO1G,OAAO,CAACC,OAAO,CAAC,IAAI,CAACmH,WAAW,CAAC;EAC5C;EACA;EACMC,UAAUA,CAAA,EAAG;IAAA,IAAAC,MAAA;IAAA,OAAA5H,6KAAA;MACfd,WAAW,CAAC+F,UAAU,CAAC2C,MAAI,CAACnD,EAAE,CAAC,CAAC;MAChC9E,cAAc,CAACkF,aAAa,CAAC+C,MAAI,CAACnD,EAAE,CAAC,CAAC;IAAC;EAC3C;EACA;EACMoD,UAAUA,CAACrK,SAAS,EAAE;IAAA,IAAAsK,MAAA;IAAA,OAAA9H,6KAAA;MACxB,IAAI8H,MAAI,CAACnC,IAAI,EAAE;QACXvG,OAAO,CAAC2I,IAAI,CAAC,uDAAuD,CAAC;QACrE,OAAO,KAAK;MAChB;MACA,MAAM;QAAExI,GAAG;QAAEa;MAAO,CAAC,SAASgB,YAAY,CAACQ,MAAM,CAACF,QAAQ,CAACC,IAAI,CAAC;MAChE,MAAMxC,MAAM,GAAG8F,UAAU,CAAC6C,MAAI,CAACrD,EAAE,CAAC;MAClC,MAAM3G,KAAK,GAAG8F,eAAe,CAACrE,GAAG,EAAEJ,MAAM,CAAC;MAC1C,IAAI,CAACrB,KAAK,EAAE;QACRsB,OAAO,CAAC2I,IAAI,CAAC,mCAAmC,EAAExI,GAAG,CAACP,GAAG,CAAET,CAAC,IAAKA,CAAC,CAACiB,EAAE,CAAC,CAAC;QACvE,OAAO,KAAK;MAChB;MACA,MAAM3C,QAAQ,GAAGgB,eAAe,CAACC,KAAK,CAAC;MACvC,IAAI,CAACjB,QAAQ,EAAE;QACXuC,OAAO,CAAC2I,IAAI,CAAC,iFAAiF,CAAC;QAC/F,OAAO,KAAK;MAChB;MACAD,MAAI,CAAC5B,WAAW,CAACrJ,QAAQ,EAAEW,SAAS,CAAC;MACrC,MAAMsK,MAAI,CAACE,iBAAiB,CAAC5H,MAAM,EAAEtC,KAAK,EAAErB,kBAAkB,EAAEI,QAAQ,EAAE,IAAI,EAAE0C,GAAG,CAACvD,MAAM,CAAC;MAC3F,OAAO,IAAI;IAAC;EAChB;EACA;EACAsK,iBAAiBA,CAAA,EAAG;IAChB,MAAMzJ,QAAQ,GAAG,IAAI,CAACoJ,WAAW,CAAC,CAAC;IACnC,IAAIpJ,QAAQ,IAAIuF,iBAAiB,CAACvF,QAAQ,EAAEgI,aAAa,CAAC,IAAI,CAACJ,EAAE,CAAC,CAAC,EAAE;MACjE,IAAI,CAAC0B,iBAAiB,CAACtJ,QAAQ,EAAEJ,kBAAkB,CAAC;IACxD;EACJ;EACA;EACA2J,eAAeA,CAAA,EAAG;IACd,OAAO,IAAI,CAACD,iBAAiB,CAAC,IAAI,CAACF,WAAW,CAAC,CAAC,EAAExJ,kBAAkB,CAAC;EACzE;EACAiK,gBAAgBA,CAAA,EAAG;IACf,IAAI1C,EAAE;IACN,MAAMiE,GAAG,GAAGrG,MAAM;IAClB,IAAIqG,GAAG,CAAC3K,OAAO,CAACG,KAAK,KAAK,IAAI,EAAE;MAC5B,IAAI,CAACA,KAAK,EAAE;MACZwK,GAAG,CAAC3K,OAAO,CAACM,YAAY,CAAC,IAAI,CAACH,KAAK,EAAEwK,GAAG,CAACvG,QAAQ,CAACwG,KAAK,EAAE,CAAClE,EAAE,GAAGiE,GAAG,CAACvG,QAAQ,CAACyG,QAAQ,MAAM,IAAI,IAAInE,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACoE,IAAI,CAAC;IACvI;IACA,MAAM3K,KAAK,GAAGwK,GAAG,CAAC3K,OAAO,CAACG,KAAK;IAC/B,MAAMmI,SAAS,GAAG,IAAI,CAACA,SAAS;IAChC,IAAI,CAACA,SAAS,GAAGnI,KAAK;IACtB,IAAIA,KAAK,GAAGmI,SAAS,IAAKnI,KAAK,IAAImI,SAAS,IAAIA,SAAS,GAAG,CAAE,EAAE;MAC5D,OAAOlJ,qBAAqB;IAChC;IACA,IAAIe,KAAK,GAAGmI,SAAS,EAAE;MACnB,OAAOjJ,kBAAkB;IAC7B;IACA,OAAOF,kBAAkB;EAC7B;EACM0J,iBAAiBA,CAACtJ,QAAQ,EAAEW,SAAS,EAAE2C,SAAS,EAAE;IAAA,IAAAkI,MAAA;IAAA,OAAArI,6KAAA;MACpD,IAAI,CAACnD,QAAQ,EAAE;QACXuC,OAAO,CAACyB,KAAK,CAAC,iDAAiD,CAAC;QAChE,OAAO,KAAK;MAChB;MACA;MACA,MAAMjB,SAAS,GAAGiF,aAAa,CAACwD,MAAI,CAAC5D,EAAE,CAAC;MACxC,MAAM5E,QAAQ,GAAGuC,iBAAiB,CAACvF,QAAQ,EAAE+C,SAAS,CAAC;MACvD,IAAI0I,YAAY,GAAG,IAAI;MACvB,IAAIzI,QAAQ,EAAE;QACV,MAAM;UAAEhD,QAAQ,EAAE0L,UAAU;UAAEnL;QAAY,CAAC,GAAGyC,QAAQ,CAACtD,EAAE;QACzD8L,MAAI,CAACnC,WAAW,CAACqC,UAAU,EAAE/K,SAAS,EAAEJ,WAAW,CAAC;QACpDkL,YAAY,GAAGzI,QAAQ,CAACvD,IAAI;QAC5BO,QAAQ,GAAG0L,UAAU;MACzB;MACA;MACA,MAAMpJ,MAAM,GAAG8F,UAAU,CAACoD,MAAI,CAAC5D,EAAE,CAAC;MAClC,MAAM3G,KAAK,GAAGmG,oBAAoB,CAACpH,QAAQ,EAAEsC,MAAM,CAAC;MACpD,IAAI,CAACrB,KAAK,EAAE;QACRsB,OAAO,CAACyB,KAAK,CAAC,gDAAgD,CAAC;QAC/D,OAAO,KAAK;MAChB;MACA;MACA,OAAOwH,MAAI,CAACL,iBAAiB,CAACtG,QAAQ,CAACC,IAAI,EAAE7D,KAAK,EAAEN,SAAS,EAAEX,QAAQ,EAAEyL,YAAY,EAAE,CAAC,EAAEnI,SAAS,CAAC;IAAC;EACzG;EACM6H,iBAAiBA,CAAAQ,GAAA,EAAAC,GAAA,EAAAC,GAAA,EAAAC,GAAA,EAAAC,GAAA,EAAuE;IAAA,IAAAC,MAAA;IAAA,OAAA7I,6KAAA,YAAtEsB,IAAI,EAAExD,KAAK,EAAEN,SAAS,EAAEX,QAAQ,EAAEyL,YAAY,EAAErI,KAAK,GAAG,CAAC,EAAEE,SAAS;MACxF,MAAM2I,MAAM,SAASD,MAAI,CAACE,IAAI,CAAC,CAAC;MAChC,IAAI7I,OAAO,GAAG,KAAK;MACnB,IAAI;QACAA,OAAO,SAAS2I,MAAI,CAAC/I,aAAa,CAACwB,IAAI,EAAExD,KAAK,EAAEN,SAAS,EAAEX,QAAQ,EAAEyL,YAAY,EAAErI,KAAK,EAAEE,SAAS,CAAC;MACxG,CAAC,CACD,OAAOS,CAAC,EAAE;QACNxB,OAAO,CAACyB,KAAK,CAACD,CAAC,CAAC;MACpB;MACAkI,MAAM,CAAC,CAAC;MACR,OAAO5I,OAAO;IAAC,GAAAgB,KAAA,OAAAC,SAAA;EACnB;EACM4H,IAAIA,CAAA,EAAG;IAAA,IAAAC,MAAA;IAAA,OAAAhJ,6KAAA;MACT,MAAMiJ,CAAC,GAAGD,MAAI,CAACtB,WAAW;MAC1B,IAAInH,OAAO;MACXyI,MAAI,CAACtB,WAAW,GAAG,IAAIpH,OAAO,CAAE3G,CAAC,IAAM4G,OAAO,GAAG5G,CAAE,CAAC;MACpD,IAAIsP,CAAC,KAAK9N,SAAS,EAAE;QACjB,MAAM8N,CAAC;MACX;MACA,OAAO1I,OAAO;IAAC;EACnB;EACA;AACJ;AACA;AACA;AACA;AACA;EACUyF,SAASA,CAAA,EAAgC;IAAA,IAAAkD,MAAA;IAAA,OAAAlJ,6KAAA,YAA/BzD,EAAE,GAAG2M,MAAI,CAACjD,WAAW,CAAC,CAAC,EAAE3J,IAAI;MACzC,IAAIA,IAAI,KAAKnB,SAAS,EAAE;QACpBmB,IAAI,GAAGoB,SAAS,CAACwL,MAAI,CAACxD,YAAY,CAAC,CAAC7I,QAAQ;MAChD;MACA,IAAI,CAACN,EAAE,IAAI,CAACD,IAAI,EAAE;QACd,OAAO,IAAI;MACf;MACA,MAAM6C,MAAM,GAAG8F,UAAU,CAACiE,MAAI,CAACzE,EAAE,CAAC;MAClC,MAAM0E,SAAS,GAAGlF,oBAAoB,CAAC3H,IAAI,EAAE6C,MAAM,CAAC;MACpD;MACA,MAAMiK,eAAe,GAAGD,SAAS,IAAIA,SAAS,CAACA,SAAS,CAACnN,MAAM,GAAG,CAAC,CAAC,CAACX,WAAW;MAChF,MAAMgO,QAAQ,GAAGD,eAAe,SAASA,eAAe,CAAC,CAAC,GAAG,IAAI;MACjE,IAAIC,QAAQ,KAAK,KAAK,IAAI,OAAOA,QAAQ,KAAK,QAAQ,EAAE;QACpD,OAAOA,QAAQ;MACnB;MACA,MAAMC,OAAO,GAAGrF,oBAAoB,CAAC1H,EAAE,EAAE4C,MAAM,CAAC;MAChD;MACA,MAAMoK,eAAe,GAAGD,OAAO,IAAIA,OAAO,CAACA,OAAO,CAACtN,MAAM,GAAG,CAAC,CAAC,CAACV,WAAW;MAC1E,OAAOiO,eAAe,GAAGA,eAAe,CAAC,CAAC,GAAG,IAAI;IAAC,GAAArI,KAAA,OAAAC,SAAA;EACtD;EACMrB,aAAaA,CAAA0J,IAAA,EAAAC,IAAA,EAAAC,IAAA,EAAAC,IAAA,EAAAC,IAAA,EAAuE;IAAA,IAAAC,MAAA;IAAA,OAAA7J,6KAAA,YAAtEsB,IAAI,EAAExD,KAAK,EAAEN,SAAS,EAAEX,QAAQ,EAAEyL,YAAY,EAAErI,KAAK,GAAG,CAAC,EAAEE,SAAS;MACpF,IAAI0J,MAAI,CAAClE,IAAI,EAAE;QACXvG,OAAO,CAAC2I,IAAI,CAAC,uDAAuD,CAAC;QACrE,OAAO,KAAK;MAChB;MACA8B,MAAI,CAAClE,IAAI,GAAG,IAAI;MAChB;MACA,MAAMmE,UAAU,GAAGD,MAAI,CAACE,gBAAgB,CAAClN,QAAQ,EAAEyL,YAAY,CAAC;MAChE,IAAIwB,UAAU,EAAE;QACZD,MAAI,CAACrE,kBAAkB,CAAC/J,IAAI,CAACqO,UAAU,CAAC;MAC5C;MACA,MAAM5J,OAAO,SAASJ,cAAa,CAACwB,IAAI,EAAExD,KAAK,EAAEN,SAAS,EAAEyC,KAAK,EAAE,KAAK,EAAEE,SAAS,CAAC;MACpF0J,MAAI,CAAClE,IAAI,GAAG,KAAK;MACjB;MACA,IAAImE,UAAU,EAAE;QACZD,MAAI,CAACpE,iBAAiB,CAAChK,IAAI,CAACqO,UAAU,CAAC;MAC3C;MACA,OAAO5J,OAAO;IAAC,GAAAgB,KAAA,OAAAC,SAAA;EACnB;EACA+E,WAAWA,CAACrJ,QAAQ,EAAEW,SAAS,EAAEJ,WAAW,EAAE;IAC1C,IAAI,CAACK,KAAK,EAAE;IACZJ,aAAa,CAACuE,MAAM,CAACtE,OAAO,EAAE,IAAI,CAACC,IAAI,EAAE,IAAI,CAACJ,OAAO,EAAEN,QAAQ,EAAEW,SAAS,EAAE,IAAI,CAACC,KAAK,EAAEL,WAAW,CAAC;EACxG;EACA6I,WAAWA,CAAA,EAAG;IACV,OAAOzH,YAAY,CAACoD,MAAM,CAACuG,QAAQ,EAAE,IAAI,CAAC5K,IAAI,EAAE,IAAI,CAACJ,OAAO,CAAC;EACjE;EACA4M,gBAAgBA,CAACxB,UAAU,EAAEyB,oBAAoB,EAAE;IAC/C,MAAM1N,IAAI,GAAG,IAAI,CAACoJ,YAAY;IAC9B,MAAMnJ,EAAE,GAAGK,YAAY,CAAC2L,UAAU,CAAC;IACnC,IAAI,CAAC7C,YAAY,GAAGnJ,EAAE;IACtB,IAAIA,EAAE,KAAKD,IAAI,EAAE;MACb,OAAO,IAAI;IACf;IACA,MAAM2N,cAAc,GAAGD,oBAAoB,GAAGpN,YAAY,CAACoN,oBAAoB,CAAC,GAAG,IAAI;IACvF,OAAO;MACH1N,IAAI;MACJ2N,cAAc;MACd1N;IACJ,CAAC;EACL;EACA,IAAIkI,EAAEA,CAAA,EAAG;IAAE,OAAOzK,qDAAU,CAAC,IAAI,CAAC;EAAE;AACxC,CAAC;AAED,MAAMkQ,aAAa,GAAG,6ZAA6Z;AACnb,MAAMC,mBAAmB,GAAGD,aAAa;AAEzC,MAAME,UAAU,GAAG,MAAM;EACrBtP,WAAWA,CAACC,OAAO,EAAE;IACjBnB,qDAAgB,CAAC,IAAI,EAAEmB,OAAO,CAAC;IAC/B,IAAI,CAACsP,OAAO,GAAIzD,EAAE,IAAK;MACnBnM,qDAAO,CAAC,IAAI,CAAC2N,IAAI,EAAExB,EAAE,EAAE,IAAI,CAAC0D,eAAe,EAAE,IAAI,CAACC,eAAe,CAAC;IACtE,CAAC;IACD,IAAI,CAACC,KAAK,GAAGrP,SAAS;IACtB,IAAI,CAACiN,IAAI,GAAGjN,SAAS;IACrB,IAAI,CAACsP,GAAG,GAAGtP,SAAS;IACpB,IAAI,CAACmP,eAAe,GAAG,SAAS;IAChC,IAAI,CAACC,eAAe,GAAGpP,SAAS;IAChC,IAAI,CAACuP,MAAM,GAAGvP,SAAS;EAC3B;EACAwP,MAAMA,CAAA,EAAG;IACL,MAAMC,IAAI,GAAGhQ,4DAAU,CAAC,IAAI,CAAC;IAC7B,MAAMiQ,KAAK,GAAG;MACVzC,IAAI,EAAE,IAAI,CAACA,IAAI;MACfqC,GAAG,EAAE,IAAI,CAACA,GAAG;MACbC,MAAM,EAAE,IAAI,CAACA;IACjB,CAAC;IACD,OAAQzQ,qDAAC,CAACE,iDAAI,EAAE;MAAE8B,GAAG,EAAE,0CAA0C;MAAEoO,OAAO,EAAE,IAAI,CAACA,OAAO;MAAES,KAAK,EAAEpQ,qDAAkB,CAAC,IAAI,CAAC8P,KAAK,EAAE;QACxH,CAACI,IAAI,GAAG,IAAI;QACZ,iBAAiB,EAAE;MACvB,CAAC;IAAE,CAAC,EAAE3Q,qDAAC,CAAC,GAAG,EAAE4B,MAAM,CAAC8H,MAAM,CAAC;MAAE1H,GAAG,EAAE;IAA2C,CAAC,EAAE4O,KAAK,CAAC,EAAE5Q,qDAAC,CAAC,MAAM,EAAE;MAAEgC,GAAG,EAAE;IAA2C,CAAC,CAAC,CAAC,CAAC;EAChK;AACJ,CAAC;AACDmO,UAAU,CAACW,KAAK,GAAGZ,mBAAmB", "sources": ["./node_modules/@ionic/core/dist/esm/ion-route_4.entry.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, d as createEvent, f as getElement, h, H as Host } from './index-a1a47f01.js';\nimport { c as componentOnReady, q as debounce } from './helpers-be245865.js';\nimport { o as openURL, c as createColorClasses } from './theme-01f3f29c.js';\nimport { b as getIonMode } from './ionic-global-94f25d1b.js';\n\nconst Route = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.ionRouteDataChanged = createEvent(this, \"ionRouteDataChanged\", 7);\n        this.url = '';\n        this.component = undefined;\n        this.componentProps = undefined;\n        this.beforeLeave = undefined;\n        this.beforeEnter = undefined;\n    }\n    onUpdate(newValue) {\n        this.ionRouteDataChanged.emit(newValue);\n    }\n    onComponentProps(newValue, oldValue) {\n        if (newValue === oldValue) {\n            return;\n        }\n        const keys1 = newValue ? Object.keys(newValue) : [];\n        const keys2 = oldValue ? Object.keys(oldValue) : [];\n        if (keys1.length !== keys2.length) {\n            this.onUpdate(newValue);\n            return;\n        }\n        for (const key of keys1) {\n            if (newValue[key] !== oldValue[key]) {\n                this.onUpdate(newValue);\n                return;\n            }\n        }\n    }\n    connectedCallback() {\n        this.ionRouteDataChanged.emit();\n    }\n    static get watchers() { return {\n        \"url\": [\"onUpdate\"],\n        \"component\": [\"onUpdate\"],\n        \"componentProps\": [\"onComponentProps\"]\n    }; }\n};\n\nconst RouteRedirect = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.ionRouteRedirectChanged = createEvent(this, \"ionRouteRedirectChanged\", 7);\n        this.from = undefined;\n        this.to = undefined;\n    }\n    propDidChange() {\n        this.ionRouteRedirectChanged.emit();\n    }\n    connectedCallback() {\n        this.ionRouteRedirectChanged.emit();\n    }\n    static get watchers() { return {\n        \"from\": [\"propDidChange\"],\n        \"to\": [\"propDidChange\"]\n    }; }\n};\n\nconst ROUTER_INTENT_NONE = 'root';\nconst ROUTER_INTENT_FORWARD = 'forward';\nconst ROUTER_INTENT_BACK = 'back';\n\n/** Join the non empty segments with \"/\". */\nconst generatePath = (segments) => {\n    const path = segments.filter((s) => s.length > 0).join('/');\n    return '/' + path;\n};\nconst generateUrl = (segments, useHash, queryString) => {\n    let url = generatePath(segments);\n    if (useHash) {\n        url = '#' + url;\n    }\n    if (queryString !== undefined) {\n        url += '?' + queryString;\n    }\n    return url;\n};\nconst writeSegments = (history, root, useHash, segments, direction, state, queryString) => {\n    const url = generateUrl([...parsePath(root).segments, ...segments], useHash, queryString);\n    if (direction === ROUTER_INTENT_FORWARD) {\n        history.pushState(state, '', url);\n    }\n    else {\n        history.replaceState(state, '', url);\n    }\n};\n/**\n * Transforms a chain to a list of segments.\n *\n * Notes:\n * - parameter segments of the form :param are replaced with their value,\n * - null is returned when a value is missing for any parameter segment.\n */\nconst chainToSegments = (chain) => {\n    const segments = [];\n    for (const route of chain) {\n        for (const segment of route.segments) {\n            if (segment[0] === ':') {\n                // eslint-disable-next-line @typescript-eslint/prefer-optional-chain\n                const param = route.params && route.params[segment.slice(1)];\n                if (!param) {\n                    return null;\n                }\n                segments.push(param);\n            }\n            else if (segment !== '') {\n                segments.push(segment);\n            }\n        }\n    }\n    return segments;\n};\n/**\n * Removes the prefix segments from the path segments.\n *\n * Return:\n * - null when the path segments do not start with the passed prefix,\n * - the path segments after the prefix otherwise.\n */\nconst removePrefix = (prefix, segments) => {\n    if (prefix.length > segments.length) {\n        return null;\n    }\n    if (prefix.length <= 1 && prefix[0] === '') {\n        return segments;\n    }\n    for (let i = 0; i < prefix.length; i++) {\n        if (prefix[i] !== segments[i]) {\n            return null;\n        }\n    }\n    if (segments.length === prefix.length) {\n        return [''];\n    }\n    return segments.slice(prefix.length);\n};\nconst readSegments = (loc, root, useHash) => {\n    const prefix = parsePath(root).segments;\n    const pathname = useHash ? loc.hash.slice(1) : loc.pathname;\n    const segments = parsePath(pathname).segments;\n    return removePrefix(prefix, segments);\n};\n/**\n * Parses the path to:\n * - segments an array of '/' separated parts,\n * - queryString (undefined when no query string).\n */\nconst parsePath = (path) => {\n    let segments = [''];\n    let queryString;\n    if (path != null) {\n        const qsStart = path.indexOf('?');\n        if (qsStart > -1) {\n            queryString = path.substring(qsStart + 1);\n            path = path.substring(0, qsStart);\n        }\n        segments = path\n            .split('/')\n            .map((s) => s.trim())\n            .filter((s) => s.length > 0);\n        if (segments.length === 0) {\n            segments = [''];\n        }\n    }\n    return { segments, queryString };\n};\n\nconst printRoutes = (routes) => {\n    console.group(`[ion-core] ROUTES[${routes.length}]`);\n    for (const chain of routes) {\n        const segments = [];\n        chain.forEach((r) => segments.push(...r.segments));\n        const ids = chain.map((r) => r.id);\n        console.debug(`%c ${generatePath(segments)}`, 'font-weight: bold; padding-left: 20px', '=>\\t', `(${ids.join(', ')})`);\n    }\n    console.groupEnd();\n};\nconst printRedirects = (redirects) => {\n    console.group(`[ion-core] REDIRECTS[${redirects.length}]`);\n    for (const redirect of redirects) {\n        if (redirect.to) {\n            console.debug('FROM: ', `$c ${generatePath(redirect.from)}`, 'font-weight: bold', ' TO: ', `$c ${generatePath(redirect.to.segments)}`, 'font-weight: bold');\n        }\n    }\n    console.groupEnd();\n};\n\n/**\n * Activates the passed route chain.\n *\n * There must be exactly one outlet per route entry in the chain.\n *\n * The methods calls setRouteId on each of the outlet with the corresponding route entry in the chain.\n * setRouteId will create or select the view in the outlet.\n */\nconst writeNavState = async (root, chain, direction, index, changed = false, animation) => {\n    try {\n        // find next navigation outlet in the DOM\n        const outlet = searchNavNode(root);\n        // make sure we can continue interacting the DOM, otherwise abort\n        if (index >= chain.length || !outlet) {\n            return changed;\n        }\n        await new Promise((resolve) => componentOnReady(outlet, resolve));\n        const route = chain[index];\n        const result = await outlet.setRouteId(route.id, route.params, direction, animation);\n        // if the outlet changed the page, reset navigation to neutral (no direction)\n        // this means nested outlets will not animate\n        if (result.changed) {\n            direction = ROUTER_INTENT_NONE;\n            changed = true;\n        }\n        // recursively set nested outlets\n        changed = await writeNavState(result.element, chain, direction, index + 1, changed, animation);\n        // once all nested outlets are visible let's make the parent visible too,\n        // using markVisible prevents flickering\n        if (result.markVisible) {\n            await result.markVisible();\n        }\n        return changed;\n    }\n    catch (e) {\n        console.error(e);\n        return false;\n    }\n};\n/**\n * Recursively walks the outlet in the DOM.\n *\n * The function returns a list of RouteID corresponding to each of the outlet and the last outlet without a RouteID.\n */\nconst readNavState = async (root) => {\n    const ids = [];\n    let outlet;\n    let node = root;\n    // eslint-disable-next-line no-cond-assign\n    while ((outlet = searchNavNode(node))) {\n        const id = await outlet.getRouteId();\n        if (id) {\n            node = id.element;\n            id.element = undefined;\n            ids.push(id);\n        }\n        else {\n            break;\n        }\n    }\n    return { ids, outlet };\n};\nconst waitUntilNavNode = () => {\n    if (searchNavNode(document.body)) {\n        return Promise.resolve();\n    }\n    return new Promise((resolve) => {\n        window.addEventListener('ionNavWillLoad', () => resolve(), { once: true });\n    });\n};\n/** Selector for all the outlets supported by the router. */\nconst OUTLET_SELECTOR = ':not([no-router]) ion-nav, :not([no-router]) ion-tabs, :not([no-router]) ion-router-outlet';\nconst searchNavNode = (root) => {\n    if (!root) {\n        return undefined;\n    }\n    if (root.matches(OUTLET_SELECTOR)) {\n        return root;\n    }\n    const outlet = root.querySelector(OUTLET_SELECTOR);\n    return outlet !== null && outlet !== void 0 ? outlet : undefined;\n};\n\n/**\n * Returns whether the given redirect matches the given path segments.\n *\n * A redirect matches when the segments of the path and redirect.from are equal.\n * Note that segments are only checked until redirect.from contains a '*' which matches any path segment.\n * The path ['some', 'path', 'to', 'page'] matches both ['some', 'path', 'to', 'page'] and ['some', 'path', '*'].\n */\nconst matchesRedirect = (segments, redirect) => {\n    const { from, to } = redirect;\n    if (to === undefined) {\n        return false;\n    }\n    if (from.length > segments.length) {\n        return false;\n    }\n    for (let i = 0; i < from.length; i++) {\n        const expected = from[i];\n        if (expected === '*') {\n            return true;\n        }\n        if (expected !== segments[i]) {\n            return false;\n        }\n    }\n    return from.length === segments.length;\n};\n/** Returns the first redirect matching the path segments or undefined when no match found. */\nconst findRouteRedirect = (segments, redirects) => {\n    return redirects.find((redirect) => matchesRedirect(segments, redirect));\n};\nconst matchesIDs = (ids, chain) => {\n    const len = Math.min(ids.length, chain.length);\n    let score = 0;\n    for (let i = 0; i < len; i++) {\n        const routeId = ids[i];\n        const routeChain = chain[i];\n        // Skip results where the route id does not match the chain at the same index\n        if (routeId.id.toLowerCase() !== routeChain.id) {\n            break;\n        }\n        if (routeId.params) {\n            const routeIdParams = Object.keys(routeId.params);\n            // Only compare routes with the chain that have the same number of parameters.\n            if (routeIdParams.length === routeChain.segments.length) {\n                // Maps the route's params into a path based on the path variable names,\n                // to compare against the route chain format.\n                //\n                // Before:\n                // ```ts\n                // {\n                //  params: {\n                //    s1: 'a',\n                //    s2: 'b'\n                //  }\n                // }\n                // ```\n                //\n                // After:\n                // ```ts\n                // [':s1',':s2']\n                // ```\n                //\n                const pathWithParams = routeIdParams.map((key) => `:${key}`);\n                for (let j = 0; j < pathWithParams.length; j++) {\n                    // Skip results where the path variable is not a match\n                    if (pathWithParams[j].toLowerCase() !== routeChain.segments[j]) {\n                        break;\n                    }\n                    // Weight path matches for the same index higher.\n                    score++;\n                }\n            }\n        }\n        // Weight id matches\n        score++;\n    }\n    return score;\n};\n/**\n * Matches the segments against the chain.\n *\n * Returns:\n * - null when there is no match,\n * - a chain with the params properties updated with the parameter segments on match.\n */\nconst matchesSegments = (segments, chain) => {\n    const inputSegments = new RouterSegments(segments);\n    let matchesDefault = false;\n    let allparams;\n    for (let i = 0; i < chain.length; i++) {\n        const chainSegments = chain[i].segments;\n        if (chainSegments[0] === '') {\n            matchesDefault = true;\n        }\n        else {\n            for (const segment of chainSegments) {\n                const data = inputSegments.next();\n                // data param\n                if (segment[0] === ':') {\n                    if (data === '') {\n                        return null;\n                    }\n                    allparams = allparams || [];\n                    const params = allparams[i] || (allparams[i] = {});\n                    params[segment.slice(1)] = data;\n                }\n                else if (data !== segment) {\n                    return null;\n                }\n            }\n            matchesDefault = false;\n        }\n    }\n    const matches = matchesDefault ? matchesDefault === (inputSegments.next() === '') : true;\n    if (!matches) {\n        return null;\n    }\n    if (allparams) {\n        return chain.map((route, i) => ({\n            id: route.id,\n            segments: route.segments,\n            params: mergeParams(route.params, allparams[i]),\n            beforeEnter: route.beforeEnter,\n            beforeLeave: route.beforeLeave,\n        }));\n    }\n    return chain;\n};\n/**\n * Merges the route parameter objects.\n * Returns undefined when both parameters are undefined.\n */\nconst mergeParams = (a, b) => {\n    return a || b ? Object.assign(Object.assign({}, a), b) : undefined;\n};\n/**\n * Finds the best match for the ids in the chains.\n *\n * Returns the best match or null when no match is found.\n * When a chain is returned the parameters are updated from the RouteIDs.\n * That is they contain both the componentProps of the <ion-route> and the parameter segment.\n */\nconst findChainForIDs = (ids, chains) => {\n    let match = null;\n    let maxMatches = 0;\n    for (const chain of chains) {\n        const score = matchesIDs(ids, chain);\n        if (score > maxMatches) {\n            match = chain;\n            maxMatches = score;\n        }\n    }\n    if (match) {\n        return match.map((route, i) => {\n            var _a;\n            return ({\n                id: route.id,\n                segments: route.segments,\n                params: mergeParams(route.params, (_a = ids[i]) === null || _a === void 0 ? void 0 : _a.params),\n            });\n        });\n    }\n    return null;\n};\n/**\n * Finds the best match for the segments in the chains.\n *\n * Returns the best match or null when no match is found.\n * When a chain is returned the parameters are updated from the segments.\n * That is they contain both the componentProps of the <ion-route> and the parameter segments.\n */\nconst findChainForSegments = (segments, chains) => {\n    let match = null;\n    let bestScore = 0;\n    for (const chain of chains) {\n        const matchedChain = matchesSegments(segments, chain);\n        if (matchedChain !== null) {\n            const score = computePriority(matchedChain);\n            if (score > bestScore) {\n                bestScore = score;\n                match = matchedChain;\n            }\n        }\n    }\n    return match;\n};\n/**\n * Computes the priority of a chain.\n *\n * Parameter segments are given a lower priority over fixed segments.\n *\n * Considering the following 2 chains matching the path /path/to/page:\n * - /path/to/:where\n * - /path/to/page\n *\n * The second one will be given a higher priority because \"page\" is a fixed segment (vs \":where\", a parameter segment).\n */\nconst computePriority = (chain) => {\n    let score = 1;\n    let level = 1;\n    for (const route of chain) {\n        for (const segment of route.segments) {\n            if (segment[0] === ':') {\n                score += Math.pow(1, level);\n            }\n            else if (segment !== '') {\n                score += Math.pow(2, level);\n            }\n            level++;\n        }\n    }\n    return score;\n};\nclass RouterSegments {\n    constructor(segments) {\n        this.segments = segments.slice();\n    }\n    next() {\n        if (this.segments.length > 0) {\n            return this.segments.shift();\n        }\n        return '';\n    }\n}\n\nconst readProp = (el, prop) => {\n    if (prop in el) {\n        return el[prop];\n    }\n    if (el.hasAttribute(prop)) {\n        return el.getAttribute(prop);\n    }\n    return null;\n};\n/**\n * Extracts the redirects (that is <ion-route-redirect> elements inside the root).\n *\n * The redirects are returned as a list of RouteRedirect.\n */\nconst readRedirects = (root) => {\n    return Array.from(root.children)\n        .filter((el) => el.tagName === 'ION-ROUTE-REDIRECT')\n        .map((el) => {\n        const to = readProp(el, 'to');\n        return {\n            from: parsePath(readProp(el, 'from')).segments,\n            to: to == null ? undefined : parsePath(to),\n        };\n    });\n};\n/**\n * Extracts all the routes (that is <ion-route> elements inside the root).\n *\n * The routes are returned as a list of chains - the flattened tree.\n */\nconst readRoutes = (root) => {\n    return flattenRouterTree(readRouteNodes(root));\n};\n/**\n * Reads the route nodes as a tree modeled after the DOM tree of <ion-route> elements.\n *\n * Note: routes without a component are ignored together with their children.\n */\nconst readRouteNodes = (node) => {\n    return Array.from(node.children)\n        .filter((el) => el.tagName === 'ION-ROUTE' && el.component)\n        .map((el) => {\n        const component = readProp(el, 'component');\n        return {\n            segments: parsePath(readProp(el, 'url')).segments,\n            id: component.toLowerCase(),\n            params: el.componentProps,\n            beforeLeave: el.beforeLeave,\n            beforeEnter: el.beforeEnter,\n            children: readRouteNodes(el),\n        };\n    });\n};\n/**\n * Flattens a RouterTree in a list of chains.\n *\n * Each chain represents a path from the root node to a terminal node.\n */\nconst flattenRouterTree = (nodes) => {\n    const chains = [];\n    for (const node of nodes) {\n        flattenNode([], chains, node);\n    }\n    return chains;\n};\n/** Flattens a route node recursively and push each branch to the chains list. */\nconst flattenNode = (chain, chains, node) => {\n    chain = [\n        ...chain,\n        {\n            id: node.id,\n            segments: node.segments,\n            params: node.params,\n            beforeLeave: node.beforeLeave,\n            beforeEnter: node.beforeEnter,\n        },\n    ];\n    if (node.children.length === 0) {\n        chains.push(chain);\n        return;\n    }\n    for (const child of node.children) {\n        flattenNode(chain, chains, child);\n    }\n};\n\nconst Router = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.ionRouteWillChange = createEvent(this, \"ionRouteWillChange\", 7);\n        this.ionRouteDidChange = createEvent(this, \"ionRouteDidChange\", 7);\n        this.previousPath = null;\n        this.busy = false;\n        this.state = 0;\n        this.lastState = 0;\n        this.root = '/';\n        this.useHash = true;\n    }\n    async componentWillLoad() {\n        await waitUntilNavNode();\n        const canProceed = await this.runGuards(this.getSegments());\n        if (canProceed !== true) {\n            if (typeof canProceed === 'object') {\n                const { redirect } = canProceed;\n                const path = parsePath(redirect);\n                this.setSegments(path.segments, ROUTER_INTENT_NONE, path.queryString);\n                await this.writeNavStateRoot(path.segments, ROUTER_INTENT_NONE);\n            }\n        }\n        else {\n            await this.onRoutesChanged();\n        }\n    }\n    componentDidLoad() {\n        window.addEventListener('ionRouteRedirectChanged', debounce(this.onRedirectChanged.bind(this), 10));\n        window.addEventListener('ionRouteDataChanged', debounce(this.onRoutesChanged.bind(this), 100));\n    }\n    async onPopState() {\n        const direction = this.historyDirection();\n        let segments = this.getSegments();\n        const canProceed = await this.runGuards(segments);\n        if (canProceed !== true) {\n            if (typeof canProceed === 'object') {\n                segments = parsePath(canProceed.redirect).segments;\n            }\n            else {\n                return false;\n            }\n        }\n        return this.writeNavStateRoot(segments, direction);\n    }\n    onBackButton(ev) {\n        ev.detail.register(0, (processNextHandler) => {\n            this.back();\n            processNextHandler();\n        });\n    }\n    /** @internal */\n    async canTransition() {\n        const canProceed = await this.runGuards();\n        if (canProceed !== true) {\n            if (typeof canProceed === 'object') {\n                return canProceed.redirect;\n            }\n            else {\n                return false;\n            }\n        }\n        return true;\n    }\n    /**\n     * Navigate to the specified path.\n     *\n     * @param path The path to navigate to.\n     * @param direction The direction of the animation. Defaults to `\"forward\"`.\n     */\n    async push(path, direction = 'forward', animation) {\n        var _a;\n        if (path.startsWith('.')) {\n            const currentPath = (_a = this.previousPath) !== null && _a !== void 0 ? _a : '/';\n            // Convert currentPath to an URL by pre-pending a protocol and a host to resolve the relative path.\n            const url = new URL(path, `https://host/${currentPath}`);\n            path = url.pathname + url.search;\n        }\n        let parsedPath = parsePath(path);\n        const canProceed = await this.runGuards(parsedPath.segments);\n        if (canProceed !== true) {\n            if (typeof canProceed === 'object') {\n                parsedPath = parsePath(canProceed.redirect);\n            }\n            else {\n                return false;\n            }\n        }\n        this.setSegments(parsedPath.segments, direction, parsedPath.queryString);\n        return this.writeNavStateRoot(parsedPath.segments, direction, animation);\n    }\n    /** Go back to previous page in the window.history. */\n    back() {\n        window.history.back();\n        return Promise.resolve(this.waitPromise);\n    }\n    /** @internal */\n    async printDebug() {\n        printRoutes(readRoutes(this.el));\n        printRedirects(readRedirects(this.el));\n    }\n    /** @internal */\n    async navChanged(direction) {\n        if (this.busy) {\n            console.warn('[ion-router] router is busy, navChanged was cancelled');\n            return false;\n        }\n        const { ids, outlet } = await readNavState(window.document.body);\n        const routes = readRoutes(this.el);\n        const chain = findChainForIDs(ids, routes);\n        if (!chain) {\n            console.warn('[ion-router] no matching URL for ', ids.map((i) => i.id));\n            return false;\n        }\n        const segments = chainToSegments(chain);\n        if (!segments) {\n            console.warn('[ion-router] router could not match path because some required param is missing');\n            return false;\n        }\n        this.setSegments(segments, direction);\n        await this.safeWriteNavState(outlet, chain, ROUTER_INTENT_NONE, segments, null, ids.length);\n        return true;\n    }\n    /** This handler gets called when a `ion-route-redirect` component is added to the DOM or if the from or to property of such node changes. */\n    onRedirectChanged() {\n        const segments = this.getSegments();\n        if (segments && findRouteRedirect(segments, readRedirects(this.el))) {\n            this.writeNavStateRoot(segments, ROUTER_INTENT_NONE);\n        }\n    }\n    /** This handler gets called when a `ion-route` component is added to the DOM or if the from or to property of such node changes. */\n    onRoutesChanged() {\n        return this.writeNavStateRoot(this.getSegments(), ROUTER_INTENT_NONE);\n    }\n    historyDirection() {\n        var _a;\n        const win = window;\n        if (win.history.state === null) {\n            this.state++;\n            win.history.replaceState(this.state, win.document.title, (_a = win.document.location) === null || _a === void 0 ? void 0 : _a.href);\n        }\n        const state = win.history.state;\n        const lastState = this.lastState;\n        this.lastState = state;\n        if (state > lastState || (state >= lastState && lastState > 0)) {\n            return ROUTER_INTENT_FORWARD;\n        }\n        if (state < lastState) {\n            return ROUTER_INTENT_BACK;\n        }\n        return ROUTER_INTENT_NONE;\n    }\n    async writeNavStateRoot(segments, direction, animation) {\n        if (!segments) {\n            console.error('[ion-router] URL is not part of the routing set');\n            return false;\n        }\n        // lookup redirect rule\n        const redirects = readRedirects(this.el);\n        const redirect = findRouteRedirect(segments, redirects);\n        let redirectFrom = null;\n        if (redirect) {\n            const { segments: toSegments, queryString } = redirect.to;\n            this.setSegments(toSegments, direction, queryString);\n            redirectFrom = redirect.from;\n            segments = toSegments;\n        }\n        // lookup route chain\n        const routes = readRoutes(this.el);\n        const chain = findChainForSegments(segments, routes);\n        if (!chain) {\n            console.error('[ion-router] the path does not match any route');\n            return false;\n        }\n        // write DOM give\n        return this.safeWriteNavState(document.body, chain, direction, segments, redirectFrom, 0, animation);\n    }\n    async safeWriteNavState(node, chain, direction, segments, redirectFrom, index = 0, animation) {\n        const unlock = await this.lock();\n        let changed = false;\n        try {\n            changed = await this.writeNavState(node, chain, direction, segments, redirectFrom, index, animation);\n        }\n        catch (e) {\n            console.error(e);\n        }\n        unlock();\n        return changed;\n    }\n    async lock() {\n        const p = this.waitPromise;\n        let resolve;\n        this.waitPromise = new Promise((r) => (resolve = r));\n        if (p !== undefined) {\n            await p;\n        }\n        return resolve;\n    }\n    /**\n     * Executes the beforeLeave hook of the source route and the beforeEnter hook of the target route if they exist.\n     *\n     * When the beforeLeave hook does not return true (to allow navigating) then that value is returned early and the beforeEnter is executed.\n     * Otherwise the beforeEnterHook hook of the target route is executed.\n     */\n    async runGuards(to = this.getSegments(), from) {\n        if (from === undefined) {\n            from = parsePath(this.previousPath).segments;\n        }\n        if (!to || !from) {\n            return true;\n        }\n        const routes = readRoutes(this.el);\n        const fromChain = findChainForSegments(from, routes);\n        // eslint-disable-next-line @typescript-eslint/prefer-optional-chain\n        const beforeLeaveHook = fromChain && fromChain[fromChain.length - 1].beforeLeave;\n        const canLeave = beforeLeaveHook ? await beforeLeaveHook() : true;\n        if (canLeave === false || typeof canLeave === 'object') {\n            return canLeave;\n        }\n        const toChain = findChainForSegments(to, routes);\n        // eslint-disable-next-line @typescript-eslint/prefer-optional-chain\n        const beforeEnterHook = toChain && toChain[toChain.length - 1].beforeEnter;\n        return beforeEnterHook ? beforeEnterHook() : true;\n    }\n    async writeNavState(node, chain, direction, segments, redirectFrom, index = 0, animation) {\n        if (this.busy) {\n            console.warn('[ion-router] router is busy, transition was cancelled');\n            return false;\n        }\n        this.busy = true;\n        // generate route event and emit will change\n        const routeEvent = this.routeChangeEvent(segments, redirectFrom);\n        if (routeEvent) {\n            this.ionRouteWillChange.emit(routeEvent);\n        }\n        const changed = await writeNavState(node, chain, direction, index, false, animation);\n        this.busy = false;\n        // emit did change\n        if (routeEvent) {\n            this.ionRouteDidChange.emit(routeEvent);\n        }\n        return changed;\n    }\n    setSegments(segments, direction, queryString) {\n        this.state++;\n        writeSegments(window.history, this.root, this.useHash, segments, direction, this.state, queryString);\n    }\n    getSegments() {\n        return readSegments(window.location, this.root, this.useHash);\n    }\n    routeChangeEvent(toSegments, redirectFromSegments) {\n        const from = this.previousPath;\n        const to = generatePath(toSegments);\n        this.previousPath = to;\n        if (to === from) {\n            return null;\n        }\n        const redirectedFrom = redirectFromSegments ? generatePath(redirectFromSegments) : null;\n        return {\n            from,\n            redirectedFrom,\n            to,\n        };\n    }\n    get el() { return getElement(this); }\n};\n\nconst routerLinkCss = \":host{--background:transparent;--color:var(--ion-color-primary, #3880ff);background:var(--background);color:var(--color)}:host(.ion-color){color:var(--ion-color-base)}a{font-family:inherit;font-size:inherit;font-style:inherit;font-weight:inherit;letter-spacing:inherit;text-decoration:inherit;text-indent:inherit;text-overflow:inherit;text-transform:inherit;text-align:inherit;white-space:inherit;color:inherit}\";\nconst IonRouterLinkStyle0 = routerLinkCss;\n\nconst RouterLink = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.onClick = (ev) => {\n            openURL(this.href, ev, this.routerDirection, this.routerAnimation);\n        };\n        this.color = undefined;\n        this.href = undefined;\n        this.rel = undefined;\n        this.routerDirection = 'forward';\n        this.routerAnimation = undefined;\n        this.target = undefined;\n    }\n    render() {\n        const mode = getIonMode(this);\n        const attrs = {\n            href: this.href,\n            rel: this.rel,\n            target: this.target,\n        };\n        return (h(Host, { key: 'e69892f4c6a6baf040a20c429afdec0e4db2dc0e', onClick: this.onClick, class: createColorClasses(this.color, {\n                [mode]: true,\n                'ion-activatable': true,\n            }) }, h(\"a\", Object.assign({ key: '648cb22526f2933abe0865c86da6f30eac3ccb87' }, attrs), h(\"slot\", { key: 'd6bc2e28b7c5cc228caedffebe653bde35934b67' }))));\n    }\n};\nRouterLink.style = IonRouterLinkStyle0;\n\nexport { Route as ion_route, RouteRedirect as ion_route_redirect, Router as ion_router, RouterLink as ion_router_link };\n"], "names": ["r", "registerInstance", "d", "createEvent", "f", "getElement", "h", "H", "Host", "c", "componentOnReady", "q", "debounce", "o", "openURL", "createColorClasses", "b", "getIonMode", "Route", "constructor", "hostRef", "ionRouteDataChanged", "url", "component", "undefined", "componentProps", "beforeLeave", "beforeEnter", "onUpdate", "newValue", "emit", "onComponentProps", "oldValue", "keys1", "Object", "keys", "keys2", "length", "key", "connectedCallback", "watchers", "RouteRedirect", "ionRouteRedirectChanged", "from", "to", "propDidChange", "ROUTER_INTENT_NONE", "ROUTER_INTENT_FORWARD", "ROUTER_INTENT_BACK", "generatePath", "segments", "path", "filter", "s", "join", "generateUrl", "useHash", "queryString", "writeSegments", "history", "root", "direction", "state", "parsePath", "pushState", "replaceState", "chainToSegments", "chain", "route", "segment", "param", "params", "slice", "push", "removePrefix", "prefix", "i", "readSegments", "loc", "pathname", "hash", "qsStart", "indexOf", "substring", "split", "map", "trim", "printRoutes", "routes", "console", "group", "for<PERSON>ach", "ids", "id", "debug", "groupEnd", "printRedirects", "redirects", "redirect", "writeNavState", "_ref", "_asyncToGenerator", "index", "changed", "animation", "outlet", "searchNavNode", "Promise", "resolve", "result", "setRouteId", "element", "markVisible", "e", "error", "_x", "_x2", "_x3", "_x4", "apply", "arguments", "readNavState", "_ref2", "node", "getRouteId", "_x5", "waitUntilNavNode", "document", "body", "window", "addEventListener", "once", "OUTLET_SELECTOR", "matches", "querySelector", "matchesRedirect", "expected", "findRouteRedirect", "find", "matchesIDs", "len", "Math", "min", "score", "routeId", "routeChain", "toLowerCase", "routeIdParams", "pathWithParams", "j", "matchesSegments", "inputSegments", "RouterSegments", "matchesDefault", "allparams", "chainSegments", "data", "next", "mergeParams", "a", "assign", "findChainForIDs", "chains", "match", "max<PERSON><PERSON><PERSON>", "_a", "findChainForSegments", "bestScore", "<PERSON><PERSON><PERSON><PERSON>", "computePriority", "level", "pow", "shift", "readProp", "el", "prop", "hasAttribute", "getAttribute", "readRedirects", "Array", "children", "tagName", "readRoutes", "flattenRouterTree", "readRouteNodes", "nodes", "flattenNode", "child", "Router", "ionRouteWillChange", "ionRouteDidChange", "previousPath", "busy", "lastState", "componentWillLoad", "_this", "canProceed", "runGuards", "getSegments", "setSegments", "writeNavStateRoot", "onRoutesChanged", "componentDidLoad", "onRedirectChanged", "bind", "onPopState", "_this2", "historyDirection", "onBackButton", "ev", "detail", "register", "processNextHandler", "back", "canTransition", "_this3", "_x6", "_this4", "startsWith", "currentPath", "URL", "search", "parsed<PERSON><PERSON>", "waitPromise", "printDebug", "_this5", "navChanged", "_this6", "warn", "safeWriteNavState", "win", "title", "location", "href", "_this7", "redirectFrom", "toSegments", "_x7", "_x8", "_x9", "_x0", "_x1", "_this8", "unlock", "lock", "_this9", "p", "_this0", "fromChain", "beforeLeaveHook", "canLeave", "<PERSON><PERSON><PERSON><PERSON>", "beforeEnterHook", "_x10", "_x11", "_x12", "_x13", "_x14", "_this1", "routeEvent", "routeChangeEvent", "redirectFromSegments", "redirectedFrom", "routerLinkCss", "IonRouterLinkStyle0", "RouterLink", "onClick", "routerDirection", "routerAnimation", "color", "rel", "target", "render", "mode", "attrs", "class", "style", "ion_route", "ion_route_redirect", "ion_router", "ion_router_link"], "sourceRoot": "webpack:///", "x_google_ignoreList": [0]}