import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { AngularFireAuth } from '@angular/fire/compat/auth';
import { AngularFirestore } from '@angular/fire/compat/firestore';

@Component({
  selector: 'app-admin-setup',
  templateUrl: './admin-setup.component.html',
  styleUrls: ['./admin-setup.component.scss']
})
export class AdminSetupComponent implements OnInit {
  setupForm: FormGroup;
  isCreating = false;
  successMessage = '';
  errorMessage = '';
  showPassword = false;

  constructor(
    private fb: FormBuilder,
    private afAuth: AngularFireAuth,
    private firestore: AngularFirestore,
    private router: Router
  ) {
    this.setupForm = this.fb.group({
      email: ['', [Validators.required, Validators.email]],
      password: ['', [Validators.required, Validators.minLength(6)]],
      displayName: ['', [Validators.required]]
    });
  }

  ngOnInit(): void {
    // Component initialization
  }

  async createAdminAccount(): Promise<void> {
    if (this.setupForm.invalid) {
      this.markFormGroupTouched();
      return;
    }

    this.isCreating = true;
    this.errorMessage = '';
    this.successMessage = '';

    try {
      const { email, password, displayName } = this.setupForm.value;
      console.log('Creating admin account with:', { email, displayName });

      // Create user in Firebase Auth
      console.log('Attempting to create user in Firebase Auth...');
      const userCredential = await this.afAuth.createUserWithEmailAndPassword(email, password);
      console.log('User created successfully:', userCredential.user?.uid);
      
      if (userCredential.user) {
        // Update user profile
        console.log('Updating user profile...');
        await userCredential.user.updateProfile({
          displayName: displayName
        });
        console.log('User profile updated successfully');

        // Create admin document in Firestore
        const adminData = {
          uid: userCredential.user.uid,
          email: email,
          displayName: displayName,
          role: 'admin',
          permissions: [
            'manage_lawyers',
            'manage_users',
            'manage_templates',
            'view_analytics',
            'manage_announcements',
            'manage_settings',
            'system_control'
          ],
          isActive: true,
          createdAt: new Date(),
          lastLogin: null,
          avatar: null
        };

        console.log('Creating admin document in Firestore...', adminData);
        await this.firestore.collection('admins').doc(userCredential.user.uid).set(adminData);
        console.log('Admin document created successfully');

        this.successMessage = `Admin account created successfully! You can now login with email: ${email}`;
        this.setupForm.reset();

        // Auto-redirect to login after 3 seconds
        setTimeout(() => {
          this.goToLogin();
        }, 3000);

      }
    } catch (error: any) {
      console.error('Error creating admin account:', error);
      this.errorMessage = this.getErrorMessage(error);
    } finally {
      this.isCreating = false;
    }
  }

  // Removed role-based permissions - single admin type with full access

  goToLogin(): void {
    this.router.navigate(['/login']);
  }

  togglePasswordVisibility(): void {
    this.showPassword = !this.showPassword;
  }

  private markFormGroupTouched(): void {
    Object.keys(this.setupForm.controls).forEach(key => {
      const control = this.setupForm.get(key);
      control?.markAsTouched();
    });
  }

  private getErrorMessage(error: any): string {
    switch (error.code) {
      case 'auth/email-already-in-use':
        return 'An account with this email already exists.';
      case 'auth/invalid-email':
        return 'Please enter a valid email address.';
      case 'auth/weak-password':
        return 'Password should be at least 6 characters long.';
      case 'auth/network-request-failed':
        return 'Network error. Please check your internet connection.';
      default:
        return error.message || 'An unexpected error occurred. Please try again.';
    }
  }
}
