{"version": 3, "file": "main.js", "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IA6KoBA,4DADF,eAAoD,eACxC;IAAAA,oDAAA,eAAQ;IAAAA,0DAAA,EAAW;IAC7BA,oDAAA,GACF;IAAAA,0DAAA,EAAO;;;;IADLA,uDAAA,GACF;IADEA,gEAAA,MAAAM,WAAA,CAAAC,SAAA,MACF;;;;;;IAvBJP,4DAFJ,cAAoG,cACxC,eAC9C;IAAAA,oDAAA,GAAsC;IAClDA,0DADkD,EAAW,EACvD;IAIFA,4DAFJ,cAA8B,cACC,cACC;IAAAA,oDAAA,GAA0B;IAAAA,0DAAA,EAAM;IAE1DA,4DADF,cAA2B,eACG;IAAAA,oDAAA,IAAuD;;IAAAA,0DAAA,EAAO;IAC1FA,4DAAA,gBAA4D;IAAAA,oDAAA,IAAmC;;IAAAA,0DAAA,EAAO;IACtGA,4DAAA,gBAA4D;IAAAA,oDAAA,IAAmC;;IAEnGA,0DAFmG,EAAO,EAClG,EACF;IAGJA,4DADF,eAA8B,SACzB;IAAAA,oDAAA,IAAsB;IAAAA,0DAAA,EAAI;IAGzBA,4DAFJ,eAAgC,gBACN,gBACZ;IAAAA,oDAAA,cAAM;IAAAA,0DAAA,EAAW;IAC3BA,oDAAA,IACF;IAAAA,0DAAA,EAAO;IACPA,wDAAA,KAAAS,6CAAA,mBAAoD;IAM1DT,0DAFI,EAAM,EACF,EACF;IAGJA,4DADF,eAA8B,iBACoC;IAAxCA,wDAAA,mBAAAW,+DAAA;MAAA,MAAAL,WAAA,GAAAN,2DAAA,CAAAa,GAAA,EAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAf,2DAAA;MAAA,OAAAA,yDAAA,CAASe,MAAA,CAAAG,mBAAA,CAAAZ,WAAA,CAA6B;IAAA,EAAC;IAC7DN,4DAAA,gBAAU;IAAAA,oDAAA,kBAAU;IACtBA,0DADsB,EAAW,EACxB;IAEPA,4DADF,kBAA2D,gBAC/C;IAAAA,oDAAA,iBAAS;IACrBA,0DADqB,EAAW,EACvB;IAEPA,4DADF,yBAAkC,kBAC+B;IAAzCA,wDAAA,mBAAAmB,+DAAA;MAAA,MAAAb,WAAA,GAAAN,2DAAA,CAAAa,GAAA,EAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAf,2DAAA;MAAA,OAAAA,yDAAA,CAASe,MAAA,CAAAK,oBAAA,CAAAd,WAAA,CAA8B;IAAA,EAAC;IAC5DN,4DAAA,gBAAU;IAAAA,oDAAA,gBAAQ;IAAAA,0DAAA,EAAW;IAC7BA,4DAAA,YAAM;IAAAA,oDAAA,oBAAY;IACpBA,0DADoB,EAAO,EAClB;IACTA,4DAAA,kBAAuD;IAAjCA,wDAAA,mBAAAqB,+DAAA;MAAA,MAAAf,WAAA,GAAAN,2DAAA,CAAAa,GAAA,EAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAf,2DAAA;MAAA,OAAAA,yDAAA,CAASe,MAAA,CAAAO,YAAA,CAAAhB,WAAA,CAAsB;IAAA,EAAC;IACpDN,4DAAA,gBAAU;IAAAA,oDAAA,YAAI;IAAAA,0DAAA,EAAW;IACzBA,4DAAA,YAAM;IAAAA,oDAAA,uBAAe;IAI7BA,0DAJ6B,EAAO,EACrB,EACA,EACP,EACF;;;;;;IAhDkEA,wDAAA,CAAAM,WAAA,CAAAkB,QAAA,CAA2B;IACjExB,uDAAA,EAAyB;IAAzBA,wDAAA,CAAAM,WAAA,CAAAmB,MAAA,CAAyB;IAC7CzB,uDAAA,GAAsC;IAAtCA,+DAAA,CAAAe,MAAA,CAAAY,eAAA,CAAArB,WAAA,CAAAsB,MAAA,EAAsC;IAKlB5B,uDAAA,GAA0B;IAA1BA,+DAAA,CAAAM,WAAA,CAAAuB,WAAA,CAA0B;IAExB7B,uDAAA,GAAuD;IAAvDA,+DAAA,CAAAA,yDAAA,SAAAM,WAAA,CAAAyB,SAAA,2BAAuD;IACnD/B,uDAAA,GAA2B;IAA3BA,wDAAA,CAAAM,WAAA,CAAA0B,QAAA,CAA2B;IAAChC,uDAAA,EAAmC;IAAnCA,+DAAA,CAAAA,yDAAA,SAAAM,WAAA,CAAA0B,QAAA,EAAmC;IAC/DhC,uDAAA,GAA2B;IAA3BA,wDAAA,CAAAM,WAAA,CAAAkB,QAAA,CAA2B;IAACxB,uDAAA,EAAmC;IAAnCA,+DAAA,CAAAA,yDAAA,SAAAM,WAAA,CAAAkB,QAAA,EAAmC;IAK9FxB,uDAAA,GAAsB;IAAtBA,+DAAA,CAAAM,WAAA,CAAA4B,OAAA,CAAsB;IAIrBlC,uDAAA,GACF;IADEA,gEAAA,MAAAM,WAAA,CAAA6B,IAAA,MACF;IAC0BnC,uDAAA,EAAwB;IAAxBA,wDAAA,SAAAM,WAAA,CAAAC,SAAA,CAAwB;IAY9BP,uDAAA,GAAkC;IAAlCA,wDAAA,sBAAAqC,eAAA,CAAkC;;;;;;IAmB9DrC,4DADF,cAA6E,wBAK3C;IAA9BA,wDAAA,kBAAAsC,oEAAAC,MAAA;MAAAvC,2DAAA,CAAAwC,GAAA;MAAA,MAAAzB,MAAA,GAAAf,2DAAA;MAAA,OAAAA,yDAAA,CAAQe,MAAA,CAAA0B,YAAA,CAAAF,MAAA,CAAoB;IAAA,EAAC;IAEjCvC,0DADE,EAAgB,EACZ;;;;IALFA,uDAAA,EAAoC;IAEpCA,wDAFA,WAAAe,MAAA,CAAA2B,kBAAA,CAAAC,MAAA,CAAoC,aAAA5B,MAAA,CAAA6B,QAAA,CACf,oBAAA5C,6DAAA,IAAA8C,GAAA,EACgB;;;AAse3C,MAAOC,oBAAoB;EApqBjCC,YAAA;IAqqBE,KAAAC,UAAU,GAAG,EAAE;IACf,KAAAC,iBAAiB,GAAG,OAAO;IAC3B,KAAAC,gBAAgB,GAAG,EAAE;IACrB,KAAAC,cAAc,GAAG,EAAE;IACnB,KAAAC,gBAAgB,GAAG,EAAE;IACrB,KAAAT,QAAQ,GAAG,EAAE;IACb,KAAAU,WAAW,GAAG,CAAC;IAEf,KAAAC,UAAU,GAAuB,CAC/B;MACEC,EAAE,EAAE,GAAG;MACPzB,SAAS,EAAE,IAAI0B,IAAI,CAAC,IAAI,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;MAC3C7B,MAAM,EAAE,QAAQ;MAChBI,QAAQ,EAAE,QAAQ;MAClBH,WAAW,EAAE,4BAA4B;MACzCK,OAAO,EAAE,6GAA6G;MACtHC,IAAI,EAAE,sBAAsB;MAC5B5B,SAAS,EAAE,eAAe;MAC1BmD,SAAS,EAAE,8DAA8D;MACzElC,QAAQ,EAAE,QAAQ;MAClBC,MAAM,EAAE;KACT,EACD;MACE+B,EAAE,EAAE,GAAG;MACPzB,SAAS,EAAE,IAAI0B,IAAI,CAAC,IAAI,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;MAC3C7B,MAAM,EAAE,QAAQ;MAChBI,QAAQ,EAAE,MAAM;MAChBH,WAAW,EAAE,yBAAyB;MACtCK,OAAO,EAAE,6HAA6H;MACtIC,IAAI,EAAE,sBAAsB;MAC5B5B,SAAS,EAAE,eAAe;MAC1BiB,QAAQ,EAAE,KAAK;MACfC,MAAM,EAAE;KACT,EACD;MACE+B,EAAE,EAAE,GAAG;MACPzB,SAAS,EAAE,IAAI0B,IAAI,CAAC,IAAI,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;MAC3C7B,MAAM,EAAE,QAAQ;MAChBI,QAAQ,EAAE,aAAa;MACvBH,WAAW,EAAE,+BAA+B;MAC5CK,OAAO,EAAE,2HAA2H;MACpIC,IAAI,EAAE,sBAAsB;MAC5B5B,SAAS,EAAE,eAAe;MAC1BiB,QAAQ,EAAE,QAAQ;MAClBC,MAAM,EAAE;KACT,EACD;MACE+B,EAAE,EAAE,GAAG;MACPzB,SAAS,EAAE,IAAI0B,IAAI,CAAC,IAAI,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;MAC3C7B,MAAM,EAAE,QAAQ;MAChBI,QAAQ,EAAE,aAAa;MACvBH,WAAW,EAAE,+BAA+B;MAC5CK,OAAO,EAAE,sHAAsH;MAC/HC,IAAI,EAAE,sBAAsB;MAC5B5B,SAAS,EAAE,eAAe;MAC1BiB,QAAQ,EAAE,MAAM;MAChBC,MAAM,EAAE;KACT,EACD;MACE+B,EAAE,EAAE,GAAG;MACPzB,SAAS,EAAE,IAAI0B,IAAI,CAAC,IAAI,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;MAC3C7B,MAAM,EAAE,UAAU;MAClBI,QAAQ,EAAE,MAAM;MAChBH,WAAW,EAAE,4BAA4B;MACzCK,OAAO,EAAE,kFAAkF;MAC3FC,IAAI,EAAE,sBAAsB;MAC5B5B,SAAS,EAAE,eAAe;MAC1BiB,QAAQ,EAAE,KAAK;MACfC,MAAM,EAAE;KACT,EACD;MACE+B,EAAE,EAAE,GAAG;MACPzB,SAAS,EAAE,IAAI0B,IAAI,CAAC,IAAI,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;MAC1C7B,MAAM,EAAE,OAAO;MACfI,QAAQ,EAAE,UAAU;MACpBH,WAAW,EAAE,uBAAuB;MACpCK,OAAO,EAAE,oGAAoG;MAC7GC,IAAI,EAAE,sBAAsB;MAC5B5B,SAAS,EAAE,eAAe;MAC1BiB,QAAQ,EAAE,KAAK;MACfC,MAAM,EAAE;KACT,EACD;MACE+B,EAAE,EAAE,GAAG;MACPzB,SAAS,EAAE,IAAI0B,IAAI,CAAC,IAAI,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;MAC3C7B,MAAM,EAAE,QAAQ;MAChBI,QAAQ,EAAE,UAAU;MACpBH,WAAW,EAAE,aAAa;MAC1BK,OAAO,EAAE,sGAAsG;MAC/GC,IAAI,EAAE,sBAAsB;MAC5B5B,SAAS,EAAE,eAAe;MAC1BiB,QAAQ,EAAE,KAAK;MACfC,MAAM,EAAE;KACT,EACD;MACE+B,EAAE,EAAE,GAAG;MACPzB,SAAS,EAAE,IAAI0B,IAAI,CAAC,IAAI,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;MAC3C7B,MAAM,EAAE,QAAQ;MAChBI,QAAQ,EAAE,QAAQ;MAClBH,WAAW,EAAE,oCAAoC;MACjDK,OAAO,EAAE,4GAA4G;MACrHC,IAAI,EAAE,sBAAsB;MAC5B5B,SAAS,EAAE,eAAe;MAC1BiB,QAAQ,EAAE,QAAQ;MAClBC,MAAM,EAAE;KACT,EACD;MACE+B,EAAE,EAAE,GAAG;MACPzB,SAAS,EAAE,IAAI0B,IAAI,CAAC,IAAI,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;MAC3C7B,MAAM,EAAE,MAAM;MACdI,QAAQ,EAAE,MAAM;MAChBH,WAAW,EAAE,iCAAiC;MAC9CK,OAAO,EAAE,oGAAoG;MAC7GC,IAAI,EAAE,sBAAsB;MAC5B5B,SAAS,EAAE,eAAe;MAC1BiB,QAAQ,EAAE,MAAM;MAChBC,MAAM,EAAE;KACT,EACD;MACE+B,EAAE,EAAE,IAAI;MACRzB,SAAS,EAAE,IAAI0B,IAAI,CAAC,IAAI,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;MAC3C7B,MAAM,EAAE,QAAQ;MAChBI,QAAQ,EAAE,MAAM;MAChBH,WAAW,EAAE,wBAAwB;MACrCK,OAAO,EAAE,2HAA2H;MACpIC,IAAI,EAAE,sBAAsB;MAC5B5B,SAAS,EAAE,eAAe;MAC1BiB,QAAQ,EAAE,QAAQ;MAClBC,MAAM,EAAE;KACT,EACD;MACE+B,EAAE,EAAE,IAAI;MACRzB,SAAS,EAAE,IAAI0B,IAAI,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;MAC1C7B,MAAM,EAAE,QAAQ;MAChBI,QAAQ,EAAE,aAAa;MACvBH,WAAW,EAAE,2BAA2B;MACxCK,OAAO,EAAE,wHAAwH;MACjIC,IAAI,EAAE,sBAAsB;MAC5B5B,SAAS,EAAE,eAAe;MAC1BiB,QAAQ,EAAE,QAAQ;MAClBC,MAAM,EAAE;KACT,EACD;MACE+B,EAAE,EAAE,IAAI;MACRzB,SAAS,EAAE,IAAI0B,IAAI,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;MAC1C7B,MAAM,EAAE,QAAQ;MAChBI,QAAQ,EAAE,QAAQ;MAClBH,WAAW,EAAE,yBAAyB;MACtCK,OAAO,EAAE,6HAA6H;MACtIC,IAAI,EAAE,QAAQ;MACd5B,SAAS,EAAE,UAAU;MACrBiB,QAAQ,EAAE,KAAK;MACfC,MAAM,EAAE;KACT,EACD;MACE+B,EAAE,EAAE,IAAI;MACRzB,SAAS,EAAE,IAAI0B,IAAI,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;MAC1C7B,MAAM,EAAE,MAAM;MACdI,QAAQ,EAAE,UAAU;MACpBH,WAAW,EAAE,+BAA+B;MAC5CK,OAAO,EAAE,iIAAiI;MAC1IC,IAAI,EAAE,iBAAiB;MACvB5B,SAAS,EAAE,cAAc;MACzBiB,QAAQ,EAAE,UAAU;MACpBC,MAAM,EAAE;KACT,CACF;IAED,KAAAiB,kBAAkB,GAAuB,EAAE;;EAE3CiB,QAAQA,CAAA;IACN,IAAI,CAACjB,kBAAkB,GAAG,CAAC,GAAG,IAAI,CAACa,UAAU,CAAC;EAChD;EAEAK,gBAAgBA,CAAA;IACd,IAAIC,QAAQ,GAAG,CAAC,GAAG,IAAI,CAACN,UAAU,CAAC;IAEnC;IACA,IAAI,IAAI,CAACN,UAAU,EAAE;MACnB,MAAMa,IAAI,GAAG,IAAI,CAACb,UAAU,CAACc,WAAW,EAAE;MAC1CF,QAAQ,GAAGA,QAAQ,CAACG,MAAM,CAACC,QAAQ,IACjCA,QAAQ,CAACpC,WAAW,CAACkC,WAAW,EAAE,CAACG,QAAQ,CAACJ,IAAI,CAAC,IACjDG,QAAQ,CAAC/B,OAAO,CAAC6B,WAAW,EAAE,CAACG,QAAQ,CAACJ,IAAI,CAAC,IAC7CG,QAAQ,CAAC9B,IAAI,CAAC4B,WAAW,EAAE,CAACG,QAAQ,CAACJ,IAAI,CAAC,CAC3C;;IAGH;IACA,IAAI,IAAI,CAACX,gBAAgB,EAAE;MACzBU,QAAQ,GAAGA,QAAQ,CAACG,MAAM,CAACC,QAAQ,IAAIA,QAAQ,CAACjC,QAAQ,KAAK,IAAI,CAACmB,gBAAgB,CAAC;;IAGrF;IACA,IAAI,IAAI,CAACC,cAAc,EAAE;MACvBS,QAAQ,GAAGA,QAAQ,CAACG,MAAM,CAACC,QAAQ,IAAIA,QAAQ,CAACrC,MAAM,KAAK,IAAI,CAACwB,cAAc,CAAC;;IAGjF;IACA,IAAI,IAAI,CAACC,gBAAgB,EAAE;MACzBQ,QAAQ,GAAGA,QAAQ,CAACG,MAAM,CAACC,QAAQ,IAAIA,QAAQ,CAACzC,QAAQ,KAAK,IAAI,CAAC6B,gBAAgB,CAAC;;IAGrF;IACA,IAAI,IAAI,CAACH,iBAAiB,KAAK,KAAK,EAAE;MACpC,MAAMiB,GAAG,GAAG,IAAIV,IAAI,EAAE;MACtB,IAAIW,SAAS,GAAG,IAAIX,IAAI,EAAE;MAE1B,QAAQ,IAAI,CAACP,iBAAiB;QAC5B,KAAK,OAAO;UACVkB,SAAS,CAACC,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;UAC9B;QACF,KAAK,MAAM;UACTD,SAAS,CAACE,OAAO,CAACH,GAAG,CAACI,OAAO,EAAE,GAAG,CAAC,CAAC;UACpC;QACF,KAAK,OAAO;UACVH,SAAS,CAACI,QAAQ,CAACL,GAAG,CAACM,QAAQ,EAAE,GAAG,CAAC,CAAC;UACtC;QACF,KAAK,SAAS;UACZL,SAAS,CAACI,QAAQ,CAACL,GAAG,CAACM,QAAQ,EAAE,GAAG,CAAC,CAAC;UACtC;QACF,KAAK,MAAM;UACTL,SAAS,CAACM,WAAW,CAACP,GAAG,CAACQ,WAAW,EAAE,GAAG,CAAC,CAAC;UAC5C;;MAGJd,QAAQ,GAAGA,QAAQ,CAACG,MAAM,CAACC,QAAQ,IAAIA,QAAQ,CAAClC,SAAS,IAAIqC,SAAS,CAAC;;IAGzE,IAAI,CAAC1B,kBAAkB,GAAGmB,QAAQ;EACpC;EAEAe,gBAAgBA,CAACnD,MAAc;IAC7B,OAAO,IAAI,CAACiB,kBAAkB,CAACsB,MAAM,CAACC,QAAQ,IAAIA,QAAQ,CAACxC,MAAM,KAAKA,MAAM,CAAC,CAACkB,MAAM;EACtF;EAEAkC,kBAAkBA,CAAA;IAChB,OAAO,IAAI,CAACnC,kBAAkB,CAACC,MAAM;EACvC;EAEAhB,eAAeA,CAACC,MAAc;IAC5B,QAAQA,MAAM;MACZ,KAAK,QAAQ;QAAE,OAAO,YAAY;MAClC,KAAK,QAAQ;QAAE,OAAO,MAAM;MAC5B,KAAK,QAAQ;QAAE,OAAO,QAAQ;MAC9B,KAAK,MAAM;QAAE,OAAO,YAAY;MAChC,KAAK,UAAU;QAAE,OAAO,UAAU;MAClC,KAAK,QAAQ;QAAE,OAAO,QAAQ;MAC9B,KAAK,OAAO;QAAE,OAAO,OAAO;MAC5B,KAAK,QAAQ;QAAE,OAAO,QAAQ;MAC9B;QAAS,OAAO,MAAM;;EAE1B;EAEAkD,SAASA,CAAA;IACP;IACAC,OAAO,CAACC,GAAG,CAAC,2BAA2B,CAAC;EAC1C;EAEAC,UAAUA,CAAA;IACR;IACA,IAAI,CAACrB,gBAAgB,EAAE;IACvBmB,OAAO,CAACC,GAAG,CAAC,wBAAwB,CAAC;EACvC;EAEA9D,mBAAmBA,CAAC+C,QAA0B;IAC5C;IACAc,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEf,QAAQ,CAAC;EACpD;EAEA7C,oBAAoBA,CAAC6C,QAA0B;IAC7C;IACAc,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEf,QAAQ,CAAC;EAC9C;EAEA3C,YAAYA,CAAC2C,QAA0B;IACrC;IACAc,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAEf,QAAQ,CAAC;EACxD;EAEAxB,YAAYA,CAACyC,KAAU;IACrB,IAAI,CAAC5B,WAAW,GAAG4B,KAAK,CAACC,SAAS;IAClC,IAAI,CAACvC,QAAQ,GAAGsC,KAAK,CAACtC,QAAQ;EAChC;;;uBA3RWG,oBAAoB;IAAA;EAAA;;;YAApBA,oBAAoB;MAAAqC,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,8BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UA/pBzB1F,4DAFJ,aAAoC,aACD,SAC3B;UAAAA,oDAAA,mBAAY;UAAAA,0DAAA,EAAK;UAEnBA,4DADF,aAA4B,gBACsC;UAAtBA,wDAAA,mBAAA4F,sDAAA;YAAA,OAASD,GAAA,CAAAb,SAAA,EAAW;UAAA,EAAC;UAC7D9E,4DAAA,eAAU;UAAAA,oDAAA,eAAQ;UAAAA,0DAAA,EAAW;UAC7BA,oDAAA,mBACF;UAAAA,0DAAA,EAAS;UACTA,4DAAA,gBAA+C;UAAvBA,wDAAA,mBAAA6F,sDAAA;YAAA,OAASF,GAAA,CAAAV,UAAA,EAAY;UAAA,EAAC;UAC5CjF,4DAAA,gBAAU;UAAAA,oDAAA,eAAO;UAGvBA,0DAHuB,EAAW,EACrB,EACL,EACF;UAMAA,4DAHN,cAA6B,cACH,yBACoC,iBAC7C;UAAAA,oDAAA,kBAAU;UAAAA,0DAAA,EAAY;UACjCA,4DAAA,qBAAiF;UAArEA,8DAAA,yBAAA+F,iEAAAxD,MAAA;YAAAvC,gEAAA,CAAA2F,GAAA,CAAAzC,iBAAA,EAAAX,MAAA,MAAAoD,GAAA,CAAAzC,iBAAA,GAAAX,MAAA;YAAA,OAAAA,MAAA;UAAA,EAA6B;UAACvC,wDAAA,6BAAAiG,qEAAA;YAAA,OAAmBN,GAAA,CAAA/B,gBAAA,EAAkB;UAAA,EAAC;UAC9E5D,4DAAA,sBAA0B;UAAAA,oDAAA,aAAK;UAAAA,0DAAA,EAAa;UAC5CA,4DAAA,sBAAyB;UAAAA,oDAAA,iBAAS;UAAAA,0DAAA,EAAa;UAC/CA,4DAAA,sBAA0B;UAAAA,oDAAA,kBAAU;UAAAA,0DAAA,EAAa;UACjDA,4DAAA,sBAA4B;UAAAA,oDAAA,oBAAY;UAAAA,0DAAA,EAAa;UACrDA,4DAAA,sBAAyB;UAAAA,oDAAA,iBAAS;UAAAA,0DAAA,EAAa;UAC/CA,4DAAA,sBAAwB;UAAAA,oDAAA,gBAAQ;UAEpCA,0DAFoC,EAAa,EAClC,EACE;UAGfA,4DADF,yBAA0D,iBAC7C;UAAAA,oDAAA,gBAAQ;UAAAA,0DAAA,EAAY;UAC/BA,4DAAA,qBAAgF;UAApEA,8DAAA,yBAAAkG,iEAAA3D,MAAA;YAAAvC,gEAAA,CAAA2F,GAAA,CAAAxC,gBAAA,EAAAZ,MAAA,MAAAoD,GAAA,CAAAxC,gBAAA,GAAAZ,MAAA;YAAA,OAAAA,MAAA;UAAA,EAA4B;UAACvC,wDAAA,6BAAAmG,qEAAA;YAAA,OAAmBR,GAAA,CAAA/B,gBAAA,EAAkB;UAAA,EAAC;UAC7E5D,4DAAA,sBAAqB;UAAAA,oDAAA,sBAAc;UAAAA,0DAAA,EAAa;UAChDA,4DAAA,sBAA2B;UAAAA,oDAAA,yBAAiB;UAAAA,0DAAA,EAAa;UACzDA,4DAAA,sBAAyB;UAAAA,oDAAA,uBAAe;UAAAA,0DAAA,EAAa;UACrDA,4DAAA,sBAAgC;UAAAA,oDAAA,oBAAY;UAAAA,0DAAA,EAAa;UACzDA,4DAAA,sBAAgC;UAAAA,oDAAA,oBAAY;UAAAA,0DAAA,EAAa;UACzDA,4DAAA,sBAA2B;UAAAA,oDAAA,cAAM;UAAAA,0DAAA,EAAa;UAC9CA,4DAAA,sBAA6B;UAAAA,oDAAA,gBAAQ;UAEzCA,0DAFyC,EAAa,EACvC,EACE;UAGfA,4DADF,yBAA0D,iBAC7C;UAAAA,oDAAA,mBAAW;UAAAA,0DAAA,EAAY;UAClCA,4DAAA,qBAA8E;UAAlEA,8DAAA,yBAAAoG,iEAAA7D,MAAA;YAAAvC,gEAAA,CAAA2F,GAAA,CAAAvC,cAAA,EAAAb,MAAA,MAAAoD,GAAA,CAAAvC,cAAA,GAAAb,MAAA;YAAA,OAAAA,MAAA;UAAA,EAA0B;UAACvC,wDAAA,6BAAAqG,qEAAA;YAAA,OAAmBV,GAAA,CAAA/B,gBAAA,EAAkB;UAAA,EAAC;UAC3E5D,4DAAA,sBAAqB;UAAAA,oDAAA,mBAAW;UAAAA,0DAAA,EAAa;UAC7CA,4DAAA,sBAA2B;UAAAA,oDAAA,cAAM;UAAAA,0DAAA,EAAa;UAC9CA,4DAAA,sBAA2B;UAAAA,oDAAA,cAAM;UAAAA,0DAAA,EAAa;UAC9CA,4DAAA,sBAA2B;UAAAA,oDAAA,cAAM;UAAAA,0DAAA,EAAa;UAC9CA,4DAAA,sBAAyB;UAAAA,oDAAA,YAAI;UAAAA,0DAAA,EAAa;UAC1CA,4DAAA,sBAA6B;UAAAA,oDAAA,gBAAQ;UAAAA,0DAAA,EAAa;UAClDA,4DAAA,sBAA2B;UAAAA,oDAAA,cAAM;UAAAA,0DAAA,EAAa;UAC9CA,4DAAA,sBAA0B;UAAAA,oDAAA,aAAK;UAAAA,0DAAA,EAAa;UAC5CA,4DAAA,sBAA2B;UAAAA,oDAAA,cAAM;UAErCA,0DAFqC,EAAa,EACnC,EACE;UAGfA,4DADF,yBAA0D,iBAC7C;UAAAA,oDAAA,gBAAQ;UAAAA,0DAAA,EAAY;UAC/BA,4DAAA,qBAAgF;UAApEA,8DAAA,yBAAAsG,iEAAA/D,MAAA;YAAAvC,gEAAA,CAAA2F,GAAA,CAAAtC,gBAAA,EAAAd,MAAA,MAAAoD,GAAA,CAAAtC,gBAAA,GAAAd,MAAA;YAAA,OAAAA,MAAA;UAAA,EAA4B;UAACvC,wDAAA,6BAAAuG,qEAAA;YAAA,OAAmBZ,GAAA,CAAA/B,gBAAA,EAAkB;UAAA,EAAC;UAC7E5D,4DAAA,sBAAqB;UAAAA,oDAAA,kBAAU;UAAAA,0DAAA,EAAa;UAC5CA,4DAAA,sBAAwB;UAAAA,oDAAA,WAAG;UAAAA,0DAAA,EAAa;UACxCA,4DAAA,sBAA2B;UAAAA,oDAAA,cAAM;UAAAA,0DAAA,EAAa;UAC9CA,4DAAA,sBAAyB;UAAAA,oDAAA,YAAI;UAAAA,0DAAA,EAAa;UAC1CA,4DAAA,sBAA6B;UAAAA,oDAAA,gBAAQ;UAG3CA,0DAH2C,EAAa,EACvC,EACE,EACb;UAIFA,4DAFJ,eAAwB,0BACoC,oBACpC;UAAAA,oDAAA,cAAM;UAAAA,0DAAA,EAAW;UACrCA,4DAAA,iBAAW;UAAAA,oDAAA,4BAAoB;UAAAA,0DAAA,EAAY;UAC3CA,4DAAA,iBAA4H;UAA5GA,8DAAA,2BAAAwG,8DAAAjE,MAAA;YAAAvC,gEAAA,CAAA2F,GAAA,CAAA1C,UAAA,EAAAV,MAAA,MAAAoD,GAAA,CAAA1C,UAAA,GAAAV,MAAA;YAAA,OAAAA,MAAA;UAAA,EAAwB;UAACvC,wDAAA,mBAAAyG,sDAAA;YAAA,OAASd,GAAA,CAAA/B,gBAAA,EAAkB;UAAA,EAAC;UAG3E5D,0DAHM,EAA4H,EAC7G,EACb,EACF;UAMAA,4DAHN,eAA2B,eACM,eACN,gBACX;UAAAA,oDAAA,oBAAY;UACxBA,0DADwB,EAAW,EAC7B;UAEJA,4DADF,eAA0B,eACA;UAAAA,oDAAA,IAAiC;UAAAA,0DAAA,EAAM;UAC/DA,4DAAA,eAAwB;UAAAA,oDAAA,2BAAkB;UAE9CA,0DAF8C,EAAM,EAC5C,EACF;UAIFA,4DAFJ,gBAA+B,gBACN,iBACX;UAAAA,oDAAA,gBAAO;UACnBA,0DADmB,EAAW,EACxB;UAEJA,4DADF,gBAA0B,gBACA;UAAAA,oDAAA,KAAiC;UAAAA,0DAAA,EAAM;UAC/DA,4DAAA,gBAAwB;UAAAA,oDAAA,iBAAQ;UAEpCA,0DAFoC,EAAM,EAClC,EACF;UAIFA,4DAFJ,gBAA6B,gBACJ,iBACX;UAAAA,oDAAA,cAAK;UACjBA,0DADiB,EAAW,EACtB;UAEJA,4DADF,gBAA0B,gBACA;UAAAA,oDAAA,KAAgC;UAAAA,0DAAA,EAAM;UAC9DA,4DAAA,gBAAwB;UAAAA,oDAAA,uBAAc;UAE1CA,0DAF0C,EAAM,EACxC,EACF;UAIFA,4DAFJ,gBAA4B,gBACH,iBACX;UAAAA,oDAAA,aAAI;UAChBA,0DADgB,EAAW,EACrB;UAEJA,4DADF,gBAA0B,gBACA;UAAAA,oDAAA,KAA0B;UAAAA,0DAAA,EAAM;UACxDA,4DAAA,gBAAwB;UAAAA,oDAAA,yBAAgB;UAG9CA,0DAH8C,EAAM,EAC1C,EACF,EACF;UAKFA,4DAFJ,gBAA+B,gBACH,WACpB;UAAAA,oDAAA,0BAAiB;UAAAA,0DAAA,EAAK;UAExBA,4DADF,gBAA4B,iBACE;UAAAA,oDAAA,KAAgD;UAEhFA,0DAFgF,EAAO,EAC/E,EACF;UAENA,4DAAA,gBAA+B;UAC7BA,wDAAA,MAAA0G,qCAAA,oBAAoG;UAiDtG1G,0DAAA,EAAM;UAGNA,wDAAA,MAAA2G,qCAAA,kBAA6E;UASjF3G,0DADE,EAAM,EACF;;;UA7KcA,uDAAA,IAA6B;UAA7BA,8DAAA,UAAA2F,GAAA,CAAAzC,iBAAA,CAA6B;UAY7BlD,uDAAA,IAA4B;UAA5BA,8DAAA,UAAA2F,GAAA,CAAAxC,gBAAA,CAA4B;UAa5BnD,uDAAA,IAA0B;UAA1BA,8DAAA,UAAA2F,GAAA,CAAAvC,cAAA,CAA0B;UAe1BpD,uDAAA,IAA4B;UAA5BA,8DAAA,UAAA2F,GAAA,CAAAtC,gBAAA,CAA4B;UAcxBrD,uDAAA,IAAwB;UAAxBA,8DAAA,YAAA2F,GAAA,CAAA1C,UAAA,CAAwB;UAYhBjD,uDAAA,GAAiC;UAAjCA,+DAAA,CAAA2F,GAAA,CAAAf,gBAAA,YAAiC;UAUjC5E,uDAAA,GAAiC;UAAjCA,+DAAA,CAAA2F,GAAA,CAAAf,gBAAA,YAAiC;UAUjC5E,uDAAA,GAAgC;UAAhCA,+DAAA,CAAA2F,GAAA,CAAAf,gBAAA,WAAgC;UAUhC5E,uDAAA,GAA0B;UAA1BA,+DAAA,CAAA2F,GAAA,CAAAd,kBAAA,GAA0B;UAWtB7E,uDAAA,GAAgD;UAAhDA,gEAAA,KAAA2F,GAAA,CAAAjD,kBAAA,CAAAC,MAAA,sBAAgD;UAK7B3C,uDAAA,GAAqB;UAArBA,wDAAA,YAAA2F,GAAA,CAAAjD,kBAAA,CAAqB;UAoDvC1C,uDAAA,EAA0C;UAA1CA,wDAAA,SAAA2F,GAAA,CAAAjD,kBAAA,CAAAC,MAAA,GAAAgD,GAAA,CAAA/C,QAAA,CAA0C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACzM5B;AAEvD;AACqE;AACZ;AACiB;AACR;AACT;AACM;AACA;AACc;;;AAE7E,MAAMyE,MAAM,GAAW,CACrB;EAAEC,IAAI,EAAE,EAAE;EAAEC,UAAU,EAAE,iBAAiB;EAAEC,SAAS,EAAE;AAAM,CAAE,EAC9D;EAAEF,IAAI,EAAE,OAAO;EAAEG,SAAS,EAAEV,kEAAcA;AAAA,CAAE;AAE5C;AACA;EACEO,IAAI,EAAE,gBAAgB;EACtBI,YAAY,EAAEA,CAAA,KAAM,gMAAgD,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,uBAAuB;CACzG;AAED;AACA;EACEP,IAAI,EAAE,qBAAqB;EAC3BI,YAAY,EAAEA,CAAA,KAAM,oNAA0D,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACE,4BAA4B;CACxH,EACD;EACER,IAAI,EAAE,oBAAoB;EAC1BI,YAAY,EAAEA,CAAA,KAAM,gNAAwD,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACG,2BAA2B;CACrH,EACD;EACET,IAAI,EAAE,iBAAiB;EACvBI,YAAY,EAAEA,CAAA,KAAM,oMAAkD,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACI,wBAAwB;CAC5G,EACD;EACEV,IAAI,EAAE,iBAAiB;EACvBI,YAAY,EAAEA,CAAA,KAAM,oMAAkD,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACK,wBAAwB;CAC5G,EACD;EACEX,IAAI,EAAE,mBAAmB;EACzBI,YAAY,EAAEA,CAAA,KAAM,4MAAsD,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACM,0BAA0B;CAClH;AAED;AACA;EAAEZ,IAAI,EAAE,WAAW;EAAEG,SAAS,EAAEX,8EAAkBA;AAAA,CAAE,EACpD;EAAEQ,IAAI,EAAE,aAAa;EAAEG,SAAS,EAAET,mFAAmBA;AAAA,CAAE,EACvD;EAAEM,IAAI,EAAE,UAAU;EAAEG,SAAS,EAAER,2EAAiBA;AAAA,CAAE,EAClD;EAAEK,IAAI,EAAE,SAAS;EAAEG,SAAS,EAAEL,wEAAgBA;AAAA,CAAE,EAChD;EAAEE,IAAI,EAAE,SAAS;EAAEG,SAAS,EAAEN,wEAAgBA;AAAA,CAAE,EAChD;EAAEG,IAAI,EAAE,cAAc;EAAEG,SAAS,EAAE1E,sFAAoBA;AAAA,CAAE,EACzD;EAAEuE,IAAI,EAAE,aAAa;EAAEG,SAAS,EAAET,mFAAmBA;AAAA,CAAE,EACvD;EAAEM,IAAI,EAAE,OAAO;EAAEG,SAAS,EAAEP,kEAAcA;AAAA,CAAE;AAE5C;AACA;EAAEI,IAAI,EAAE,IAAI;EAAEC,UAAU,EAAE;AAAiB,CAAE,CAC9C;AAMK,MAAOY,gBAAgB;;;uBAAhBA,gBAAgB;IAAA;EAAA;;;YAAhBA;IAAgB;EAAA;;;gBAHjBtB,yDAAY,CAACuB,OAAO,CAACf,MAAM,CAAC,EAC5BR,yDAAY;IAAA;EAAA;;;sHAEXsB,gBAAgB;IAAAE,OAAA,GAAAC,yDAAA;IAAAC,OAAA,GAFjB1B,yDAAY;EAAA;AAAA,K;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC5D8C;AAEpB;AACM;AAEhB;;;;;;;;;;;;;;;IAMpC7G,4DAAA,aAA8C;IAC5CA,uDAAA,oBAA+B;IACjCA,0DAAA,EAAM;;;;;;IAqEAA,4DAAA,iBAK6B;IAD3BA,wDAAA,mBAAA6I,gFAAA;MAAA7I,2DAAA,CAAA8I,GAAA;MAAA9I,2DAAA;MAAA,MAAA+I,SAAA,GAAA/I,yDAAA;MAAA,OAAAA,yDAAA,CAAS+I,SAAA,CAAAE,MAAA,EAAe;IAAA,EAAC;IAEzBjJ,4DAAA,mBAA4C;IAAAA,oDAAA,WAAI;IAClDA,0DADkD,EAAW,EACpD;;;;;;IAxEbA,4DADF,+BAAsE,wBAItB;;;;IAKxCA,4DAFJ,aAA4B,aACE,kBACE;IAAAA,oDAAA,cAAO;IAAAA,0DAAA,EAAW;IAC9CA,4DAAA,gBAAwB;IAAAA,oDAAA,eAAO;IAEnCA,0DAFmC,EAAO,EAClC,EACF;IAKFA,4DAFJ,wBAA+B,aAC+C,oBAChD;IAAAA,oDAAA,iBAAS;IAAAA,0DAAA,EAAW;IAC9CA,4DAAA,gBAAuB;IAAAA,oDAAA,iBAAS;IAClCA,0DADkC,EAAO,EACrC;IAEFA,4DADF,aAA8E,oBAClD;IAAAA,oDAAA,cAAM;IAAAA,0DAAA,EAAW;IAC3CA,4DAAA,gBAAuB;IAAAA,oDAAA,mBAAW;IACpCA,0DADoC,EAAO,EACvC;IAEFA,4DADF,aAA2E,oBAC/C;IAAAA,oDAAA,aAAK;IAAAA,0DAAA,EAAW;IAC1CA,4DAAA,gBAAuB;IAAAA,oDAAA,gBAAQ;IACjCA,0DADiC,EAAO,EACpC;IAEFA,4DADF,aAAwE,oBAC5C;IAAAA,oDAAA,cAAM;IAAAA,0DAAA,EAAW;IAC3CA,4DAAA,gBAAuB;IAAAA,oDAAA,aAAK;IAC9BA,0DAD8B,EAAO,EACjC;IAEFA,4DADF,aAA0E,oBAC9C;IAAAA,oDAAA,cAAM;IAAAA,0DAAA,EAAW;IAC3CA,4DAAA,gBAAuB;IAAAA,oDAAA,eAAO;IAChCA,0DADgC,EAAO,EACnC;IAEFA,4DADF,aAA0E,oBAC9C;IAAAA,oDAAA,uBAAe;IAAAA,0DAAA,EAAW;IACpDA,4DAAA,gBAAuB;IAAAA,oDAAA,eAAO;IAChCA,0DADgC,EAAO,EACnC;IAEFA,4DADF,aAA+E,oBACnD;IAAAA,oDAAA,eAAO;IAAAA,0DAAA,EAAW;IAC5CA,4DAAA,gBAAuB;IAAAA,oDAAA,oBAAY;IACrCA,0DADqC,EAAO,EACxC;IAEFA,4DADF,aAA4E,oBAChD;IAAAA,oDAAA,eAAO;IAAAA,0DAAA,EAAW;IAC5CA,4DAAA,gBAAuB;IAAAA,oDAAA,iBAAS;IAClCA,0DADkC,EAAO,EACrC;IAEFA,4DADF,aAA2E,oBAC/C;IAAAA,oDAAA,gBAAQ;IAAAA,0DAAA,EAAW;IAC7CA,4DAAA,gBAAuB;IAAAA,oDAAA,gBAAQ;IAEnCA,0DAFmC,EAAO,EACpC,EACS;IAIbA,4DADF,eAA4B,kBACkC;IAAtCA,wDAAA,mBAAAkJ,uEAAA;MAAAlJ,2DAAA,CAAAa,GAAA;MAAA,MAAAsI,MAAA,GAAAnJ,2DAAA;MAAA,OAAAA,yDAAA,CAASmJ,MAAA,CAAAC,MAAA,EAAQ;IAAA,EAAC;IACtCpJ,4DAAA,oBAA0B;IAAAA,oDAAA,cAAM;IAAAA,0DAAA,EAAW;IAC3CA,4DAAA,gBAAuB;IAAAA,oDAAA,eAAO;IAGpCA,0DAHoC,EAAO,EAC9B,EACL,EACM;IAIZA,4DAFF,2BAAqB,uBAEa;IAC9BA,wDAAA,KAAAqJ,uDAAA,qBAK6B;;IAI7BrJ,uDAAA,gBAA4B;IAI1BA,4DADF,eAA0B,gBACI;IAAAA,oDAAA,IAAyC;IAAAA,0DAAA,EAAO;IAC5EA,4DAAA,kBAA2E;IACzEA,uDAAA,eAC0C;IAC5CA,0DAAA,EAAS;IAGLA,4DAFJ,yBAA8B,kBACgB,gBAChC;IAAAA,oDAAA,cAAM;IAAAA,0DAAA,EAAW;IAC3BA,4DAAA,YAAM;IAAAA,oDAAA,eAAO;IACfA,0DADe,EAAO,EACb;IACTA,4DAAA,kBAAyC;IAAnBA,wDAAA,mBAAAsJ,uEAAA;MAAAtJ,2DAAA,CAAAa,GAAA;MAAA,MAAAsI,MAAA,GAAAnJ,2DAAA;MAAA,OAAAA,yDAAA,CAASmJ,MAAA,CAAAC,MAAA,EAAQ;IAAA,EAAC;IACtCpJ,4DAAA,gBAAU;IAAAA,oDAAA,cAAM;IAAAA,0DAAA,EAAW;IAC3BA,4DAAA,YAAM;IAAAA,oDAAA,cAAM;IAIpBA,0DAJoB,EAAO,EACZ,EACA,EACP,EACM;IAEdA,uDAAA,qBAA+B;IAEnCA,0DADE,EAAsB,EACA;;;;;IAhGlBA,uDAAA,EAA+C;IAC/CA,wDADA,SAAAA,yDAAA,OAAAmJ,MAAA,CAAAI,UAAA,oBAA+C,WAAAvJ,yDAAA,OAAAmJ,MAAA,CAAAI,UAAA,YACN;;IAmEtCvJ,uDAAA,IAAwB;IAAxBA,wDAAA,SAAAA,yDAAA,SAAAmJ,MAAA,CAAAI,UAAA,EAAwB;IAQGvJ,uDAAA,GAAyC;IAAzCA,gEAAA,UAAAmJ,MAAA,CAAAK,WAAA,kBAAAL,MAAA,CAAAK,WAAA,CAAAC,IAAA,oBAAyC;IAC7CzJ,uDAAA,EAA8B;IAA9BA,wDAAA,sBAAA0J,WAAA,CAA8B;IAC/C1J,uDAAA,EAA4D;IAA5DA,wDAAA,SAAAmJ,MAAA,CAAAK,WAAA,kBAAAL,MAAA,CAAAK,WAAA,CAAAG,QAAA,kCAAA3J,2DAAA,CAA4D;;;AA+MzE,MAAO6J,YAAY;EAevB7G,YACU8G,kBAAsC,EACtCC,MAAc,EACdC,eAAgC;IAFhC,KAAAF,kBAAkB,GAAlBA,kBAAkB;IAClB,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,eAAe,GAAfA,eAAe;IAjBzB,KAAAC,KAAK,GAAG,mBAAmB;IAC3B,KAAAT,WAAW,GAAQ;MACjBC,IAAI,EAAE,UAAU;MAChBE,QAAQ,EAAE;KACX;IAED,KAAAO,WAAW,GAAG,KAAK;IAEnB,KAAAX,UAAU,GAAwB,IAAI,CAACO,kBAAkB,CAACK,OAAO,CAAC3B,4DAAW,CAAC4B,OAAO,CAAC,CACnFC,IAAI,CACH5B,mDAAG,CAAC6B,MAAM,IAAIA,MAAM,CAACC,OAAO,CAAC,EAC7B7B,2DAAW,EAAE,CACd;IAOD;IACA,IAAI,CAACqB,MAAM,CAACS,MAAM,CAACH,IAAI,CACrBrG,sDAAM,CAACkB,KAAK,IAAIA,KAAK,YAAYyD,0DAAa,CAAC,CAChD,CAAC8B,SAAS,CAAEvF,KAAK,IAAI;MACpB,MAAMwF,eAAe,GAAGxF,KAAsB;MAC9C,IAAI,CAACgF,WAAW,GAAGQ,eAAe,CAACC,GAAG,KAAK,QAAQ,IAAID,eAAe,CAACC,GAAG,CAACC,UAAU,CAAC,QAAQ,CAAC;MAC/F7F,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAE0F,eAAe,CAACC,GAAG,EAAE,cAAc,EAAE,IAAI,CAACT,WAAW,CAAC;IACtF,CAAC,CAAC;IAEF;IACA,IAAI,CAACA,WAAW,GAAG,IAAI,CAACH,MAAM,CAACY,GAAG,KAAK,QAAQ,IAAI,IAAI,CAACZ,MAAM,CAACY,GAAG,CAACC,UAAU,CAAC,QAAQ,CAAC;EACzF;EAEMxB,MAAMA,CAAA;IAAA,IAAAyB,KAAA;IAAA,OAAAC,6KAAA;MACV,IAAI;QACF/F,OAAO,CAACC,GAAG,CAAC,gBAAgB,CAAC;QAE7B;QACA,MAAM+F,SAAS,GAAGC,OAAO,CAAC,kCAAkC,CAAC;QAC7D,IAAI,CAACD,SAAS,EAAE;UACd;;QAGFhG,OAAO,CAACC,GAAG,CAAC,4BAA4B,CAAC;QAEzC;QACA,MAAM6F,KAAI,CAACb,eAAe,CAACiB,OAAO,EAAE;QACpClG,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAC;QAE1C;QACAkG,YAAY,CAACC,KAAK,EAAE;QACpBpG,OAAO,CAACC,GAAG,CAAC,uBAAuB,CAAC;QAEpC;QACA,MAAM6F,KAAI,CAACd,MAAM,CAACqB,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;QACtCrG,OAAO,CAACC,GAAG,CAAC,iDAAiD,CAAC;OAE/D,CAAC,OAAOqG,KAAK,EAAE;QACdtG,OAAO,CAACsG,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;QAE5C;QACAH,YAAY,CAACC,KAAK,EAAE;QACpBpG,OAAO,CAACC,GAAG,CAAC,kDAAkD,CAAC;QAE/D;QACAsG,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,QAAQ;;IAChC;EACH;;;uBAnEW3B,YAAY,EAAA7J,+DAAA,CAAAsI,mEAAA,GAAAtI,+DAAA,CAAA2L,mDAAA,GAAA3L,+DAAA,CAAA6L,uEAAA;IAAA;EAAA;;;YAAZhC,YAAY;MAAAzE,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAuG,sBAAArG,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAhSrB1F,wDALA,IAAAgM,2BAAA,iBAA8C,IAAAC,6CAAA,qCAKwB;;;UALhEjM,wDAAA,SAAA2F,GAAA,CAAAuE,WAAA,CAAiB;UAKClK,uDAAA,EAAkB;UAAlBA,wDAAA,UAAA2F,GAAA,CAAAuE,WAAA,CAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AChBY;AACqB;AACb;AACV;AAExD;AAC6C;AAE7C;AAC6D;AACF;AACJ;AACE;AACS;AACX;AACM;AACN;AACS;AACT;AACE;AACQ;AACV;AACI;AACK;AACc;AACrB;AACE;AACQ;AACN;AACV;AAEnD;AACsE;AACZ;AACe;AACA;AACN;AAEX;AACT;AACW;AAE1D;AACqE;AACZ;AACiB;AAER;AACT;AACM;AACA;AACc;;;AA2DvE,MAAOsE,SAAS;;;uBAATA,SAAS;IAAA;EAAA;;;YAATA,SAAS;MAAAC,SAAA,GAFR5E,wDAAY;IAAA;EAAA;;;iBAPb,CACTiE,sEAAkB,CAAC,MAAMD,iEAAa,CAACU,kEAAW,CAACG,QAAQ,CAAC,CAAC,EAC7DX,gEAAW,CAAC,MAAMC,4DAAO,EAAE,CAAC,EAC5BC,0EAAgB,CAAC,MAAMC,sEAAY,EAAE,CAAC,EACtCC,0EAAgB,CAAC,MAAMC,sEAAY,EAAE,CAAC,EACtCC,sEAAc,CAAC,MAAMC,kEAAU,EAAE,CAAC,CACnC;MAAAjG,OAAA,GAxCC6D,qEAAa,EACb/D,iEAAgB,EAChBgE,0FAAuB,EACvBC,wDAAW,EACXC,gEAAmB,EACnBC,mEAAgB;MAEhB;MACAC,wDAAW,CAACnE,OAAO,EAAE;MAErB;MACAoE,wEAAgB,EAChBC,sEAAe,EACfC,kEAAa,EACbC,oEAAc,EACdC,6EAAkB,EAClBC,kEAAa,EACbC,wEAAgB,EAChBC,kEAAa,EACbC,2EAAiB,EACjBC,kEAAa,EACbC,oEAAc,EACdC,4EAAkB,EAClBC,kEAAa,EACbC,sEAAe,EACfC,2EAAiB,EACjBC,yFAAwB,EACxBC,oEAAc,EACdC,sEAAe,EACfC,8EAAmB,EACnBC,wEAAmB,EACnBC,8DAAY;IAAA;EAAA;;;uHAYHY,SAAS;IAAAG,YAAA,GAvDlB9E,wDAAY,EACZ/C,8EAAkB,EAClBC,kEAAc,EACdC,mFAAmB,EAEnBC,2EAAiB,EACjBC,kEAAc,EACdC,wEAAgB,EAChBC,wEAAgB,EAChBrE,uFAAoB;IAAAsF,OAAA,GAGpB6D,qEAAa,EACb/D,iEAAgB,EAChBgE,0FAAuB,EACvBC,wDAAW,EACXC,gEAAmB,EACnBC,mEAAgB,EAAAhE,wDAAA;IAKhB;IACAkE,wEAAgB,EAChBC,sEAAe,EACfC,kEAAa,EACbC,oEAAc,EACdC,6EAAkB,EAClBC,kEAAa,EACbC,wEAAgB,EAChBC,kEAAa,EACbC,2EAAiB,EACjBC,kEAAa,EACbC,oEAAc,EACdC,4EAAkB,EAClBC,kEAAa,EACbC,sEAAe,EACfC,2EAAiB,EACjBC,yFAAwB,EACxBC,oEAAc,EACdC,sEAAe,EACfC,8EAAmB,EACnBC,wEAAmB,EACnBC,8DAAY;EAAA;AAAA,K;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ICnEF5N,4DAAA,iBAAgE;IAC9DA,oDAAA,GACF;IAAAA,0DAAA,EAAS;;;;IAFoCA,wDAAA,YAAA4O,SAAA,CAAkB;IAC7D5O,uDAAA,EACF;IADEA,gEAAA,MAAA4O,SAAA,CAAAnF,IAAA,QAAAmF,SAAA,CAAAE,UAAA,OACF;;;;;;IAJF9O,4DADF,cAA8D,YACrD;IAAAA,oDAAA,iCAA0B;IAAAA,0DAAA,EAAQ;IACzCA,4DAAA,iBAAuF;IAA/EA,8DAAA,2BAAA+O,iEAAAxM,MAAA;MAAAvC,2DAAA,CAAAa,GAAA;MAAA,MAAAsI,MAAA,GAAAnJ,2DAAA;MAAAA,gEAAA,CAAAmJ,MAAA,CAAA6F,cAAA,EAAAzM,MAAA,MAAA4G,MAAA,CAAA6F,cAAA,GAAAzM,MAAA;MAAA,OAAAvC,yDAAA,CAAAuC,MAAA;IAAA,EAA4B;IAACvC,wDAAA,oBAAAiP,0DAAA;MAAAjP,2DAAA,CAAAa,GAAA;MAAA,MAAAsI,MAAA,GAAAnJ,2DAAA;MAAA,OAAAA,yDAAA,CAAUmJ,MAAA,CAAA+F,cAAA,EAAgB;IAAA,EAAC;IAC9DlP,wDAAA,IAAAmP,yCAAA,qBAAgE;IAIpEnP,0DADE,EAAS,EACL;;;;IALIA,uDAAA,GAA4B;IAA5BA,8DAAA,YAAAmJ,MAAA,CAAA6F,cAAA,CAA4B;IACPhP,uDAAA,EAAgB;IAAhBA,wDAAA,YAAAmJ,MAAA,CAAAiG,aAAA,CAAgB;;;;;IAgC3CpP,4DAAA,cAAqD;IAAAA,oDAAA,GAAS;IAAAA,0DAAA,EAAM;;;;IAAfA,uDAAA,EAAS;IAATA,+DAAA,CAAAqP,MAAA,CAAS;;;;;;IAI5DrP,4DAAA,cAM4B;IAA1BA,wDAAA,mBAAAsP,6DAAA;MAAA,MAAAC,MAAA,GAAAvP,2DAAA,CAAAwC,GAAA,EAAA1B,SAAA;MAAA,MAAAqI,MAAA,GAAAnJ,2DAAA;MAAA,OAAAA,yDAAA,CAASmJ,MAAA,CAAAqG,UAAA,CAAAD,MAAA,CAAe;IAAA,EAAC;IACzBvP,oDAAA,GACF;IAAAA,0DAAA,EAAM;;;;IAHJA,yDAFA,iBAAAuP,MAAA,CAAAG,cAAA,CAAyC,UAAAH,MAAA,CAAAI,OAAA,CACd,aAAAJ,MAAA,CAAAK,UAAA,CACM;IAEjC5P,uDAAA,EACF;IADEA,gEAAA,MAAAuP,MAAA,CAAAM,IAAA,MACF;;;;;IATF7P,4DAAA,cAA6D;IAC3DA,wDAAA,IAAA8P,uCAAA,kBAM4B;IAG9B9P,0DAAA,EAAM;;;;IAPcA,uDAAA,EAAO;IAAPA,wDAAA,YAAA+P,OAAA,CAAO;;;;;IAkBzB/P,4DAHF,cAEmE,cACnC;IAAAA,oDAAA,GAAsB;IAAAA,0DAAA,EAAM;IAExDA,4DADF,cAAiC,cACD;IAAAA,oDAAA,GAAsB;IAAAA,0DAAA,EAAM;IAC1DA,4DAAA,cAAgC;IAAAA,oDAAA,GAA4B;IAC9DA,0DAD8D,EAAM,EAC9D;IACNA,4DAAA,cAA6D;IAC3DA,oDAAA,GACF;IACFA,0DADE,EAAM,EACF;;;;IAR0BA,uDAAA,GAAsB;IAAtBA,+DAAA,CAAAgQ,cAAA,CAAAC,IAAA,CAAsB;IAEpBjQ,uDAAA,GAAsB;IAAtBA,+DAAA,CAAAgQ,cAAA,CAAAE,IAAA,CAAsB;IACpBlQ,uDAAA,GAA4B;IAA5BA,+DAAA,CAAAgQ,cAAA,CAAAG,UAAA,CAA4B;IAE9BnQ,uDAAA,EAA4B;IAA5BA,wDAAA,CAAAgQ,cAAA,CAAAvO,MAAA,CAA4B;IAC1DzB,uDAAA,EACF;IADEA,gEAAA,MAAAgQ,cAAA,CAAAvO,MAAA,MACF;;;;;;IAZJzB,4DADF,cAAwD,SAClD;IAAAA,oDAAA,GAA+C;;IAAAA,0DAAA,EAAK;IACxDA,4DAAA,cAA+B;IAC7BA,wDAAA,IAAAoQ,uCAAA,mBAEmE;IAUrEpQ,0DAAA,EAAM;IACNA,4DAAA,iBAAmG;IAAjCA,wDAAA,mBAAAqQ,0DAAA;MAAArQ,2DAAA,CAAAsQ,GAAA;MAAA,MAAAnH,MAAA,GAAAnJ,2DAAA;MAAA,OAAAA,yDAAA,CAASmJ,MAAA,CAAAoH,oBAAA,EAAsB;IAAA,EAAC;IAChGvQ,oDAAA,sBACF;IACFA,0DADE,EAAS,EACL;;;;IAlBAA,uDAAA,GAA+C;IAA/CA,gEAAA,gBAAAA,yDAAA,OAAAmJ,MAAA,CAAAqH,YAAA,kBAA+C;IAIvBxQ,uDAAA,GAAuC;IAAvCA,wDAAA,YAAAmJ,MAAA,CAAAsH,sBAAA,CAAAtH,MAAA,CAAAqH,YAAA,EAAuC;;;;;;IAsBjExQ,4DAFJ,cAA+E,cAC/C,cACN;IAAAA,oDAAA,GAAkC;;IAAAA,0DAAA,EAAM;IAC9DA,4DAAA,cAAwB;IAAAA,oDAAA,GAAmC;;IAC7DA,0DAD6D,EAAM,EAC7D;IAEJA,4DADF,cAA8B,cACG;IAAAA,oDAAA,IAAsB;IAAAA,0DAAA,EAAM;IAC3DA,4DAAA,eAAgC;IAAAA,oDAAA,IAA4B;IAAAA,0DAAA,EAAM;IAClEA,4DAAA,eAA8B;IAAAA,oDAAA,IAAsB;IACtDA,0DADsD,EAAM,EACtD;IAEJA,4DADF,eAAiC,kBACgC;IAAvCA,wDAAA,mBAAA0Q,2DAAA;MAAA,MAAAC,eAAA,GAAA3Q,2DAAA,CAAA4Q,IAAA,EAAA9P,SAAA;MAAA,MAAAqI,MAAA,GAAAnJ,2DAAA;MAAA,OAAAA,yDAAA,CAASmJ,MAAA,CAAA0H,eAAA,CAAAF,eAAA,CAA4B;IAAA,EAAC;IAC5D3Q,4DAAA,gBAAU;IAAAA,oDAAA,YAAI;IAChBA,0DADgB,EAAW,EAClB;IACTA,4DAAA,kBAA8E;IAAzCA,wDAAA,mBAAA8Q,2DAAA;MAAA,MAAAH,eAAA,GAAA3Q,2DAAA,CAAA4Q,IAAA,EAAA9P,SAAA;MAAA,MAAAqI,MAAA,GAAAnJ,2DAAA;MAAA,OAAAA,yDAAA,CAASmJ,MAAA,CAAA4H,iBAAA,CAAAJ,eAAA,CAA8B;IAAA,EAAC;IAC3E3Q,4DAAA,gBAAU;IAAAA,oDAAA,cAAM;IAGtBA,0DAHsB,EAAW,EACpB,EACL,EACF;;;;IAhBoBA,uDAAA,GAAkC;IAAlCA,+DAAA,CAAAA,yDAAA,OAAA2Q,eAAA,CAAAd,IAAA,QAAkC;IAChC7P,uDAAA,GAAmC;IAAnCA,+DAAA,CAAAA,yDAAA,OAAA2Q,eAAA,CAAAd,IAAA,SAAmC;IAG5B7P,uDAAA,GAAsB;IAAtBA,+DAAA,CAAA2Q,eAAA,CAAAT,IAAA,CAAsB;IACrBlQ,uDAAA,GAA4B;IAA5BA,+DAAA,CAAA2Q,eAAA,CAAAR,UAAA,CAA4B;IAC9BnQ,uDAAA,GAAsB;IAAtBA,+DAAA,CAAA2Q,eAAA,CAAAV,IAAA,CAAsB;;;AAgY5D,MAAOhJ,iBAAiB;EAW5BjE,YACUgO,eAAgC,EAChCC,eAAgC,EAChCC,eAAgC,EAChClH,eAAgC;IAHhC,KAAAgH,eAAe,GAAfA,eAAe;IACf,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAlH,eAAe,GAAfA,eAAe;IAdzB,KAAAmH,YAAY,GAAG,WAAW;IAC1B,KAAAX,YAAY,GAAgB,IAAI;IAChC,KAAAY,QAAQ,GAAG,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;IAC5D,KAAAC,aAAa,GAAoB,EAAE;IAEnC;IACA,KAAAjC,aAAa,GAAoB,EAAE;IACnC,KAAAJ,cAAc,GAAyB,IAAI;IAC3C,KAAAsC,SAAS,GAAG,KAAK;IASjB,KAAAC,YAAY,GAAkB,CAC5B;MACE/N,EAAE,EAAE,GAAG;MACP2M,UAAU,EAAE,aAAa;MACzBD,IAAI,EAAE,cAAc;MACpBD,IAAI,EAAE,OAAO;MACbJ,IAAI,EAAE,IAAIpM,IAAI,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC;MAC1BhC,MAAM,EAAE;KACT,EACD;MACE+B,EAAE,EAAE,GAAG;MACP2M,UAAU,EAAE,cAAc;MAC1BD,IAAI,EAAE,cAAc;MACpBD,IAAI,EAAE,OAAO;MACbJ,IAAI,EAAE,IAAIpM,IAAI,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC;MAC1BhC,MAAM,EAAE;KACT,EACD;MACE+B,EAAE,EAAE,GAAG;MACP2M,UAAU,EAAE,gBAAgB;MAC5BD,IAAI,EAAE,aAAa;MACnBD,IAAI,EAAE,OAAO;MACbJ,IAAI,EAAE,IAAIpM,IAAI,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC;MAC1BhC,MAAM,EAAE;KACT,CACF;IAED,KAAA+P,oBAAoB,GAAkB,EAAE;EA7BrC;EA+BG7N,QAAQA,CAAA;IAAA,IAAAkH,KAAA;IAAA,OAAAC,6KAAA;MACZ/F,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC;MAC7C6F,KAAI,CAAC4G,gBAAgB,EAAE;MACvB,MAAM5G,KAAI,CAAC6G,iBAAiB,EAAE;MAC9B,MAAM7G,KAAI,CAAC8G,gBAAgB,EAAE;MAC7B9G,KAAI,CAAC2G,oBAAoB,GAAG3G,KAAI,CAAC0G,YAAY,CAACK,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;MACzD7M,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAE6F,KAAI,CAAC0G,YAAY,CAAC;MACvDxM,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAE6F,KAAI,CAAC2G,oBAAoB,CAAC;IAAC;EACnE;EAEME,iBAAiBA,CAAA;IAAA,IAAAG,MAAA;IAAA,OAAA/G,6KAAA;MACrB,MAAMtB,WAAW,GAAGqI,MAAI,CAAC7H,eAAe,CAAC8H,cAAc,EAAE;MACzD,IAAItI,WAAW,EAAE;QACf,IAAI;UACFqI,MAAI,CAACzC,aAAa,SAASyC,MAAI,CAAC7H,eAAe,CAAC+H,yBAAyB,CAACvI,WAAW,CAACwI,GAAG,CAAC;UAC1F,IAAIH,MAAI,CAACzC,aAAa,CAACzM,MAAM,GAAG,CAAC,EAAE;YACjCkP,MAAI,CAAC7C,cAAc,GAAG6C,MAAI,CAACzC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC;;UAE/CrK,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAE6M,MAAI,CAACzC,aAAa,CAAC;SAC1D,CAAC,OAAO/D,KAAK,EAAE;UACdtG,OAAO,CAACsG,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;UACrDwG,MAAI,CAACI,SAAS,CAAC,8BAA8B,EAAE,QAAQ,CAAC;;;IAE3D;EACH;EAEMN,gBAAgBA,CAAA;IAAA,IAAAO,MAAA;IAAA,OAAApH,6KAAA;MACpB,MAAMtB,WAAW,GAAG0I,MAAI,CAAClI,eAAe,CAAC8H,cAAc,EAAE;MACzD,IAAI,CAACtI,WAAW,EAAE;MAElB0I,MAAI,CAACZ,SAAS,GAAG,IAAI;MACrB,IAAI;QACF,MAAMa,oBAAoB,SAASD,MAAI,CAAClI,eAAe,CAACoI,2BAA2B,CAAC5I,WAAW,CAACwI,GAAG,CAAC;QAEpG;QACAE,MAAI,CAACX,YAAY,GAAGY,oBAAoB,CAAC1J,GAAG,CAAC4J,GAAG,KAAK;UACnD7O,EAAE,EAAE6O,GAAG,CAAC7O,EAAE,IAAI,EAAE;UAChB2M,UAAU,EAAEkC,GAAG,CAAClC,UAAU;UAC1BD,IAAI,EAAEmC,GAAG,CAACnC,IAAI;UACdD,IAAI,EAAEoC,GAAG,CAACpC,IAAI;UACdJ,IAAI,EAAE,IAAIpM,IAAI,CAAC4O,GAAG,CAACxC,IAAI,CAAC;UACxBpO,MAAM,EAAE4Q,GAAG,CAAC5Q;SACb,CAAC,CAAC;QAEHsD,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAEkN,MAAI,CAACX,YAAY,CAAC;OAChE,CAAC,OAAOlG,KAAK,EAAE;QACdtG,OAAO,CAACsG,KAAK,CAAC,2CAA2C,EAAEA,KAAK,CAAC;QACjE6G,MAAI,CAACD,SAAS,CAAC,4BAA4B,EAAE,QAAQ,CAAC;OACvD,SAAS;QACRC,MAAI,CAACZ,SAAS,GAAG,KAAK;;IACvB;EACH;EAEApC,cAAcA,CAAA;IACZnK,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAE,IAAI,CAACgK,cAAc,CAAC;IAC/D,IAAI,IAAI,CAACA,cAAc,EAAE;MACvB,IAAI,CAACiD,SAAS,CAAC,iCAAiC,IAAI,CAACjD,cAAc,CAACvF,IAAI,EAAE,EAAE,SAAS,CAAC;MACtF;MACA,IAAI,CAACkI,gBAAgB,EAAE;;EAE3B;EAEAF,gBAAgBA,CAAA;IACd;IACA,MAAMa,IAAI,GAAG,IAAI;IACjB,MAAMC,KAAK,GAAG,CAAC,CAAC,CAAC;IAEjB,MAAMC,QAAQ,GAAG,IAAI/O,IAAI,CAAC6O,IAAI,EAAEC,KAAK,EAAE,CAAC,CAAC;IACzC,MAAME,OAAO,GAAG,IAAIhP,IAAI,CAAC6O,IAAI,EAAEC,KAAK,GAAG,CAAC,EAAE,CAAC,CAAC;IAC5C,MAAMnO,SAAS,GAAG,IAAIX,IAAI,CAAC+O,QAAQ,CAAC;IACpCpO,SAAS,CAACE,OAAO,CAACF,SAAS,CAACG,OAAO,EAAE,GAAGiO,QAAQ,CAACE,MAAM,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC;IAEhE,IAAI,CAACrB,aAAa,GAAG,EAAE;IACvB,IAAIsB,WAAW,GAAG,IAAIlP,IAAI,CAACW,SAAS,CAAC;IAErC,KAAK,IAAIwO,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAG,CAAC,EAAEA,IAAI,EAAE,EAAE;MACnC,MAAMxB,QAAQ,GAAG,EAAE;MACnB,KAAK,IAAIyB,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAG,CAAC,EAAEA,GAAG,EAAE,EAAE;QAChCzB,QAAQ,CAAC0B,IAAI,CAAC;UACZjD,IAAI,EAAE8C,WAAW,CAACpO,OAAO,EAAE;UAC3BmL,cAAc,EAAEiD,WAAW,CAAClO,QAAQ,EAAE,KAAK8N,KAAK;UAChD5C,OAAO,EAAE,IAAI,CAACA,OAAO,CAACgD,WAAW,CAAC;UAClC/C,UAAU,EAAE,KAAK;UACjBmD,QAAQ,EAAE,IAAItP,IAAI,CAACkP,WAAW;SAC/B,CAAC;QACFA,WAAW,CAACrO,OAAO,CAACqO,WAAW,CAACpO,OAAO,EAAE,GAAG,CAAC,CAAC;;MAEhD,IAAI,CAAC8M,aAAa,CAACyB,IAAI,CAAC1B,QAAQ,CAAC;;EAErC;EAEAzB,OAAOA,CAACE,IAAU;IAChB,MAAMmD,KAAK,GAAG,IAAIvP,IAAI,EAAE;IACxB,OAAOoM,IAAI,CAACoD,YAAY,EAAE,KAAKD,KAAK,CAACC,YAAY,EAAE;EACrD;EAEAzD,UAAUA,CAACqD,GAAQ;IACjB9N,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAE6N,GAAG,CAAC;IAElC;IACA,IAAI,CAACxB,aAAa,CAAC6B,OAAO,CAACN,IAAI,IAAG;MAChCA,IAAI,CAACM,OAAO,CAACC,CAAC,IAAIA,CAAC,CAACvD,UAAU,GAAG,KAAK,CAAC;IACzC,CAAC,CAAC;IAEF;IACAiD,GAAG,CAACjD,UAAU,GAAG,IAAI;IACrB,IAAI,CAACY,YAAY,GAAGqC,GAAG,CAACE,QAAQ;IAEhC;IACA,IAAI,CAACd,SAAS,CAAC,YAAY,IAAI,CAACzB,YAAY,EAAEyC,YAAY,EAAE,EAAE,EAAE,SAAS,CAAC;IAC1ElO,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAE,IAAI,CAACwL,YAAY,CAAC;EAClD;EAEAC,sBAAsBA,CAACZ,IAAU;IAC/B,OAAO,IAAI,CAAC0B,YAAY,CAACvN,MAAM,CAACqO,GAAG,IACjCA,GAAG,CAACxC,IAAI,CAACoD,YAAY,EAAE,KAAKpD,IAAI,CAACoD,YAAY,EAAE,CAChD;EACH;EAEAG,aAAaA,CAAA;IACXrO,OAAO,CAACC,GAAG,CAAC,wBAAwB,CAAC;IACrC,IAAI,CAACiN,SAAS,CAAC,0CAA0C,EAAE,SAAS,CAAC;EACvE;EAEAoB,SAASA,CAAA;IACPtO,OAAO,CAACC,GAAG,CAAC,oBAAoB,CAAC;IACjC,IAAI,CAACiN,SAAS,CAAC,sCAAsC,EAAE,SAAS,CAAC;EACnE;EAEM1B,oBAAoBA,CAAA;IAAA,IAAA+C,MAAA;IAAA,OAAAxI,6KAAA;MACxB/F,OAAO,CAACC,GAAG,CAAC,2BAA2B,CAAC;MAExC,IAAIsO,MAAI,CAAClE,aAAa,CAACzM,MAAM,KAAK,CAAC,EAAE;QACnC2Q,MAAI,CAACrB,SAAS,CAAC,sDAAsD,EAAE,SAAS,CAAC;QACjF;;MAGF,MAAMsB,KAAK,SAASD,MAAI,CAACpC,eAAe,CAACsC,MAAM,CAAC;QAC9CC,MAAM,EAAE,qBAAqB;QAC7BC,OAAO,EAAE,6DAA6D;QACtEC,MAAM,EAAE,CACN;UACElK,IAAI,EAAE,QAAQ;UACdyG,IAAI,EAAE,OAAO;UACb0D,KAAK,EAAEN,MAAI,CAACtE,cAAc,EAAEvF,IAAI,IAAI,eAAe;UACnDoK,KAAK,EAAEP,MAAI,CAACtE,cAAc,EAAEgD,GAAG,IAAI,EAAE;UACrC8B,OAAO,EAAE;SACV,EACD,GAAGR,MAAI,CAAClE,aAAa,CAACwC,KAAK,CAAC,CAAC,CAAC,CAACnJ,GAAG,CAACsL,MAAM,KAAK;UAC5CtK,IAAI,EAAE,QAAQ;UACdyG,IAAI,EAAE,OAAgB;UACtB0D,KAAK,EAAEG,MAAM,CAACtK,IAAI;UAClBoK,KAAK,EAAEE,MAAM,CAAC/B,GAAG;UACjB8B,OAAO,EAAE;SACV,CAAC,CAAC,EACH;UACErK,IAAI,EAAE,YAAY;UAClByG,IAAI,EAAE,MAAM;UACZ8D,WAAW,EAAE;SACd,EACD;UACEvK,IAAI,EAAE,iBAAiB;UACvByG,IAAI,EAAE,MAAM;UACZ8D,WAAW,EAAE;SACd,EACD;UACEvK,IAAI,EAAE,MAAM;UACZyG,IAAI,EAAE,MAAM;UACZ8D,WAAW,EAAE;SACd,EACD;UACEvK,IAAI,EAAE,SAAS;UACfyG,IAAI,EAAE,UAAU;UAChB8D,WAAW,EAAE;SACd,CACF;QACDC,OAAO,EAAE,CACP;UACEC,IAAI,EAAE,QAAQ;UACdC,IAAI,EAAE;SACP,EACD;UACED,IAAI,EAAE,wBAAwB;UAC9BE,OAAO,EAAGC,IAAI,IAAI;YAChBf,MAAI,CAACgB,gBAAgB,CAACD,IAAI,CAAC;UAC7B;SACD;OAEJ,CAAC;MAEF,MAAMd,KAAK,CAACgB,OAAO,EAAE;IAAC;EACxB;EAEMD,gBAAgBA,CAACD,IAAS;IAAA,IAAAG,MAAA;IAAA,OAAA1J,6KAAA;MAC9B,IAAI,CAAC0J,MAAI,CAAChE,YAAY,EAAE;QACtBgE,MAAI,CAACvC,SAAS,CAAC,4BAA4B,EAAE,SAAS,CAAC;QACvD;;MAGF,IAAI,CAACoC,IAAI,CAAClE,UAAU,IAAI,CAACkE,IAAI,CAACI,eAAe,IAAI,CAACJ,IAAI,CAACpE,IAAI,IAAI,CAACoE,IAAI,CAACN,MAAM,EAAE;QAC3ES,MAAI,CAACvC,SAAS,CAAC,oCAAoC,EAAE,SAAS,CAAC;QAC/D;;MAGF,MAAMzI,WAAW,GAAGgL,MAAI,CAACxK,eAAe,CAAC8H,cAAc,EAAE;MACzD,IAAI,CAACtI,WAAW,EAAE;QAChBgL,MAAI,CAACvC,SAAS,CAAC,sCAAsC,EAAE,QAAQ,CAAC;QAChE;;MAGF,MAAMjD,cAAc,GAAGwF,MAAI,CAACpF,aAAa,CAACsF,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC3C,GAAG,KAAKqC,IAAI,CAACN,MAAM,CAAC;MAC1E,IAAI,CAAC/E,cAAc,EAAE;QACnBwF,MAAI,CAACvC,SAAS,CAAC,2BAA2B,EAAE,QAAQ,CAAC;QACrD;;MAGFuC,MAAI,CAAClD,SAAS,GAAG,IAAI;MACrB,IAAI;QACF,MAAMsD,eAAe,GAAoC;UACvDC,QAAQ,EAAE7F,cAAc,CAACgD,GAAG;UAC5B8C,UAAU,EAAE9F,cAAc,CAACvF,IAAI;UAC/B0G,UAAU,EAAEkE,IAAI,CAAClE,UAAU;UAC3BN,IAAI,EAAE2E,MAAI,CAAChE,YAAY,CAACuE,WAAW,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;UACnD/E,IAAI,EAAEoE,IAAI,CAACpE,IAAI;UACfxO,MAAM,EAAE,SAAS;UACjByO,IAAI,EAAEmE,IAAI,CAACI,eAAe;UAC1BQ,SAAS,EAAE,WAAW;UACtBC,SAAS,EAAE1L,WAAW,CAACwI,GAAG;UAC1BmD,cAAc,EAAE3L,WAAW,CAACwI,GAAG;UAC/BoD,kBAAkB,EAAE,WAAW;UAC/BC,OAAO,EAAEhB,IAAI,CAACgB,OAAO,IAAI,EAAE;UAC3BC,QAAQ,EAAE,KAAK;UACfC,SAAS,EAAE,IAAI9R,IAAI,EAAE;UACrB+R,SAAS,EAAE,IAAI/R,IAAI;SACpB;QAED,MAAMgS,aAAa,SAASjB,MAAI,CAACxK,eAAe,CAAC0L,iBAAiB,CAACd,eAAe,CAAC;QAEnF;QACA,MAAMe,cAAc,GAAgB;UAClCnS,EAAE,EAAEiS,aAAa;UACjBtF,UAAU,EAAEkE,IAAI,CAAClE,UAAU;UAC3BD,IAAI,EAAEmE,IAAI,CAACI,eAAe;UAC1BxE,IAAI,EAAEoE,IAAI,CAACpE,IAAI;UACfJ,IAAI,EAAE,IAAIpM,IAAI,CAAC+Q,MAAI,CAAChE,YAAY,CAAC;UACjC/O,MAAM,EAAE;SACT;QAED+S,MAAI,CAACjD,YAAY,CAACuB,IAAI,CAAC6C,cAAc,CAAC;QACtCnB,MAAI,CAAChD,oBAAoB,GAAGgD,MAAI,CAACjD,YAAY,CAACK,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;QAEzD4C,MAAI,CAACvC,SAAS,CACZ,mCAAmCoC,IAAI,CAAClE,UAAU,SAASnB,cAAc,CAACvF,IAAI,6BAA6B,EAC3G,SAAS,CACV;QACD1E,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAE4P,eAAe,CAAC;OAErE,CAAC,OAAOvJ,KAAK,EAAE;QACdtG,OAAO,CAACsG,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;QACnDmJ,MAAI,CAACvC,SAAS,CAAC,+CAA+C,EAAE,QAAQ,CAAC;OAC1E,SAAS;QACRuC,MAAI,CAAClD,SAAS,GAAG,KAAK;;IACvB;EACH;EAEMT,eAAeA,CAAC+E,WAAwB;IAAA,IAAAC,MAAA;IAAA,OAAA/K,6KAAA;MAC5C/F,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAE4Q,WAAW,CAAC;MAE7C,MAAMrC,KAAK,SAASsC,MAAI,CAAC3E,eAAe,CAACsC,MAAM,CAAC;QAC9CC,MAAM,EAAE,kBAAkB;QAC1BC,OAAO,EAAE,4BAA4B;QACrCC,MAAM,EAAE,CACN;UACElK,IAAI,EAAE,YAAY;UAClByG,IAAI,EAAE,MAAM;UACZ2D,KAAK,EAAE+B,WAAW,CAACzF,UAAU;UAC7B6D,WAAW,EAAE;SACd,EACD;UACEvK,IAAI,EAAE,iBAAiB;UACvByG,IAAI,EAAE,MAAM;UACZ2D,KAAK,EAAE+B,WAAW,CAAC1F,IAAI;UACvB8D,WAAW,EAAE;SACd,EACD;UACEvK,IAAI,EAAE,MAAM;UACZyG,IAAI,EAAE,MAAM;UACZ2D,KAAK,EAAE+B,WAAW,CAAC3F,IAAI;UACvB+D,WAAW,EAAE;SACd,CACF;QACDC,OAAO,EAAE,CACP;UACEC,IAAI,EAAE,QAAQ;UACdC,IAAI,EAAE;SACP,EACD;UACED,IAAI,EAAE,QAAQ;UACdE,OAAO,EAAGC,IAAI,IAAI;YAChBwB,MAAI,CAACC,iBAAiB,CAACF,WAAW,CAACpS,EAAE,EAAE6Q,IAAI,CAAC;UAC9C;SACD;OAEJ,CAAC;MAEF,MAAMd,KAAK,CAACgB,OAAO,EAAE;IAAC;EACxB;EAEMuB,iBAAiBA,CAACL,aAAqB,EAAEpB,IAAS;IAAA,IAAA0B,MAAA;IAAA,OAAAjL,6KAAA;MACtD,MAAMtB,WAAW,GAAGuM,MAAI,CAAC/L,eAAe,CAAC8H,cAAc,EAAE;MACzD,IAAI,CAACtI,WAAW,EAAE;QAChBuM,MAAI,CAAC9D,SAAS,CAAC,sCAAsC,EAAE,QAAQ,CAAC;QAChE;;MAGF8D,MAAI,CAACzE,SAAS,GAAG,IAAI;MACrB,IAAI;QACF,MAAM0E,OAAO,GAAiC;UAC5C7F,UAAU,EAAEkE,IAAI,CAAClE,UAAU;UAC3BD,IAAI,EAAEmE,IAAI,CAACI,eAAe;UAC1BxE,IAAI,EAAEoE,IAAI,CAACpE,IAAI;UACfkF,cAAc,EAAE3L,WAAW,CAACwI,GAAG;UAC/BoD,kBAAkB,EAAE,WAAW;UAC/BI,SAAS,EAAE,IAAI/R,IAAI;SACpB;QAED,MAAMsS,MAAI,CAAC/L,eAAe,CAAC8L,iBAAiB,CAACL,aAAa,EAAEO,OAAO,CAAC;QAEpE;QACA,MAAMC,KAAK,GAAGF,MAAI,CAACxE,YAAY,CAAC2E,SAAS,CAAC7D,GAAG,IAAIA,GAAG,CAAC7O,EAAE,KAAKiS,aAAa,CAAC;QAC1E,IAAIQ,KAAK,KAAK,CAAC,CAAC,EAAE;UAChBF,MAAI,CAACxE,YAAY,CAAC0E,KAAK,CAAC,GAAG;YACzB,GAAGF,MAAI,CAACxE,YAAY,CAAC0E,KAAK,CAAC;YAC3B9F,UAAU,EAAEkE,IAAI,CAAClE,UAAU;YAC3BD,IAAI,EAAEmE,IAAI,CAACI,eAAe;YAC1BxE,IAAI,EAAEoE,IAAI,CAACpE;WACZ;UACD8F,MAAI,CAACvE,oBAAoB,GAAGuE,MAAI,CAACxE,YAAY,CAACK,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;;QAG3DmE,MAAI,CAAC9D,SAAS,CAAC,kCAAkC,EAAE,SAAS,CAAC;OAC9D,CAAC,OAAO5G,KAAK,EAAE;QACdtG,OAAO,CAACsG,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;QACnD0K,MAAI,CAAC9D,SAAS,CAAC,+CAA+C,EAAE,QAAQ,CAAC;OAC1E,SAAS;QACR8D,MAAI,CAACzE,SAAS,GAAG,KAAK;;IACvB;EACH;EAEMP,iBAAiBA,CAAC6E,WAAwB;IAAA,IAAAO,MAAA;IAAA,OAAArL,6KAAA;MAC9C/F,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAE4Q,WAAW,CAAC;MAE/C,MAAMrC,KAAK,SAAS4C,MAAI,CAACjF,eAAe,CAACsC,MAAM,CAAC;QAC9CC,MAAM,EAAE,oBAAoB;QAC5BC,OAAO,EAAE,wDAAwDkC,WAAW,CAACzF,UAAU,GAAG;QAC1F8D,OAAO,EAAE,CACP;UACEC,IAAI,EAAE,QAAQ;UACdC,IAAI,EAAE;SACP,EACD;UACED,IAAI,EAAE,QAAQ;UACdC,IAAI,EAAE,aAAa;UACnBC,OAAO,EAAEA,CAAA,KAAK;YACZ+B,MAAI,CAACC,wBAAwB,CAACR,WAAW,CAACpS,EAAE,CAAC;UAC/C;SACD;OAEJ,CAAC;MAEF,MAAM+P,KAAK,CAACgB,OAAO,EAAE;IAAC;EACxB;EAEM6B,wBAAwBA,CAACX,aAAqB;IAAA,IAAAY,MAAA;IAAA,OAAAvL,6KAAA;MAClD,MAAMtB,WAAW,GAAG6M,MAAI,CAACrM,eAAe,CAAC8H,cAAc,EAAE;MACzD,IAAI,CAACtI,WAAW,EAAE;QAChB6M,MAAI,CAACpE,SAAS,CAAC,sCAAsC,EAAE,QAAQ,CAAC;QAChE;;MAGFoE,MAAI,CAAC/E,SAAS,GAAG,IAAI;MACrB,IAAI;QACF,MAAM+E,MAAI,CAACrM,eAAe,CAAC+G,iBAAiB,CAAC0E,aAAa,EAAEjM,WAAW,CAACwI,GAAG,EAAE,WAAW,CAAC;QAEzF;QACA,MAAMiE,KAAK,GAAGI,MAAI,CAAC9E,YAAY,CAAC2E,SAAS,CAAC7D,GAAG,IAAIA,GAAG,CAAC7O,EAAE,KAAKiS,aAAa,CAAC;QAC1E,IAAIQ,KAAK,KAAK,CAAC,CAAC,EAAE;UAChB,MAAMK,kBAAkB,GAAGD,MAAI,CAAC9E,YAAY,CAACgF,MAAM,CAACN,KAAK,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;UAChEI,MAAI,CAAC7E,oBAAoB,GAAG6E,MAAI,CAAC9E,YAAY,CAACK,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;UACzDyE,MAAI,CAACpE,SAAS,CAAC,oBAAoBqE,kBAAkB,CAACnG,UAAU,UAAU,EAAE,SAAS,CAAC;;OAEzF,CAAC,OAAO9E,KAAK,EAAE;QACdtG,OAAO,CAACsG,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;QACnDgL,MAAI,CAACpE,SAAS,CAAC,+CAA+C,EAAE,QAAQ,CAAC;OAC1E,SAAS;QACRoE,MAAI,CAAC/E,SAAS,GAAG,KAAK;;IACvB;EACH;EAEMW,SAASA,CAAAuE,EAAA,EAA2C;IAAA,IAAAC,MAAA;IAAA,OAAA3L,6KAAA,YAA1C4I,OAAe,EAAEgD,KAAA,GAAgB,SAAS;MACxD,MAAMC,KAAK,SAASF,MAAI,CAACxF,eAAe,CAACuC,MAAM,CAAC;QAC9CE,OAAO,EAAEA,OAAO;QAChBkD,QAAQ,EAAE,IAAI;QACdF,KAAK,EAAEA,KAAK;QACZG,QAAQ,EAAE;OACX,CAAC;MACFF,KAAK,CAACpC,OAAO,EAAE;IAAC,GAAAuC,KAAA,OAAAC,SAAA;EAClB;;;uBAtcW9P,iBAAiB,EAAAjH,+DAAA,CAAAsI,2DAAA,GAAAtI,+DAAA,CAAAsI,2DAAA,GAAAtI,+DAAA,CAAAsI,2DAAA,GAAAtI,+DAAA,CAAA2L,uEAAA;IAAA;EAAA;;;YAAjB1E,iBAAiB;MAAA7B,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAA2R,2BAAAzR,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAvdpB1F,4DAHN,aAAgC,aACD,aACF,SACnB;UAAAA,oDAAA,iBAAU;UAAAA,0DAAA,EAAK;UACnBA,wDAAA,IAAAoX,gCAAA,iBAA8D;UAQhEpX,0DAAA,EAAM;UAGFA,4DAFJ,aAA0B,aACG,YAClB;UAAAA,oDAAA,iGAAuD;UAChEA,0DADgE,EAAQ,EAClE;UACNA,4DAAA,iBAAiI;UAAvFA,wDAAA,mBAAAqX,oDAAA;YAAA,OAAS1R,GAAA,CAAA4K,oBAAA,EAAsB;UAAA,EAAC;UACxEvQ,4DAAA,gBAAU;UAAAA,oDAAA,WAAG;UAAAA,0DAAA,EAAW;UACxBA,oDAAA,IACF;UAEJA,0DAFI,EAAS,EACL,EACF;UAMAA,4DAJN,cAA8B,cAEC,cACD,kBAC0B;UAA1BA,wDAAA,mBAAAsX,oDAAA;YAAA,OAAS3R,GAAA,CAAAyN,aAAA,EAAe;UAAA,EAAC;UAC/CpT,4DAAA,gBAAU;UAAAA,oDAAA,oBAAY;UACxBA,0DADwB,EAAW,EAC1B;UACTA,4DAAA,UAAI;UAAAA,oDAAA,IAAkB;UAAAA,0DAAA,EAAK;UAC3BA,4DAAA,kBAA8C;UAAtBA,wDAAA,mBAAAuX,oDAAA;YAAA,OAAS5R,GAAA,CAAA0N,SAAA,EAAW;UAAA,EAAC;UAC3CrT,4DAAA,gBAAU;UAAAA,oDAAA,qBAAa;UAE3BA,0DAF2B,EAAW,EAC3B,EACL;UAGJA,4DADF,eAA2B,eACQ;UAC/BA,wDAAA,KAAAwX,iCAAA,kBAAqD;UACvDxX,0DAAA,EAAM;UACNA,4DAAA,eAA2B;UACzBA,wDAAA,KAAAyX,iCAAA,kBAA6D;UAYjEzX,0DADE,EAAM,EACF;UAGNA,wDAAA,KAAA0X,iCAAA,kBAAwD;UAoB1D1X,0DAAA,EAAM;UAIJA,4DADF,eAAmC,UAC7B;UAAAA,oDAAA,6BAAqB;UAAAA,0DAAA,EAAK;UAC9BA,wDAAA,KAAA2X,iCAAA,oBAA+E;UAqBrF3X,0DAFI,EAAM,EACF,EACF;;;UAnG8BA,uDAAA,GAA8B;UAA9BA,wDAAA,SAAA2F,GAAA,CAAAyJ,aAAA,CAAAzM,MAAA,KAA8B;UAa8C3C,uDAAA,GAAsB;UAAtBA,wDAAA,aAAA2F,GAAA,CAAA2L,SAAA,CAAsB;UAE9HtR,uDAAA,GACF;UADEA,gEAAA,MAAA2F,GAAA,CAAA2L,SAAA,8CACF;UAWMtR,uDAAA,GAAkB;UAAlBA,+DAAA,CAAA2F,GAAA,CAAAwL,YAAA,CAAkB;UAQoBnR,uDAAA,GAAW;UAAXA,wDAAA,YAAA2F,GAAA,CAAAyL,QAAA,CAAW;UAGRpR,uDAAA,GAAgB;UAAhBA,wDAAA,YAAA2F,GAAA,CAAA0L,aAAA,CAAgB;UAe3BrR,uDAAA,EAAkB;UAAlBA,wDAAA,SAAA2F,GAAA,CAAA6K,YAAA,CAAkB;UAyBAxQ,uDAAA,GAAuB;UAAvBA,wDAAA,YAAA2F,GAAA,CAAA6L,oBAAA,CAAuB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ICvBrExR,4DAHN,cAAgE,cACnC,cACE,eACf;IAAAA,oDAAA,aAAM;IAClBA,0DADkB,EAAW,EACvB;IACNA,4DAAA,cAAmD;IACjDA,uDAAA,cAA8B;IAC9BA,4DAAA,WAAM;IAAAA,oDAAA,GAA+B;;IAEzCA,0DAFyC,EAAO,EACxC,EACF;IAGJA,4DADF,eAAyB,cACC;IAAAA,oDAAA,IAAiB;IAAAA,0DAAA,EAAK;IAG1CA,4DAFJ,eAA4B,eACA,gBACd;IAAAA,oDAAA,aAAK;IAAAA,0DAAA,EAAW;IAC1BA,4DAAA,YAAM;IAAAA,oDAAA,IAAkB;IAC1BA,0DAD0B,EAAO,EAC3B;IAEJA,4DADF,eAA0B,gBACd;IAAAA,oDAAA,aAAK;IAAAA,0DAAA,EAAW;IAC1BA,4DAAA,YAAM;IAAAA,oDAAA,IAAkB;IAG9BA,0DAH8B,EAAO,EAC3B,EACF,EACF;IAIFA,4DAFJ,eAA4B,eACF,gBACK;IAAAA,oDAAA,iBAAS;IAAAA,0DAAA,EAAO;IAC3CA,4DAAA,gBAA2B;IAAAA,oDAAA,IAAqC;;IAClEA,0DADkE,EAAO,EACnE;IAEJA,4DADF,eAAwB,gBACK;IAAAA,oDAAA,cAAM;IAAAA,0DAAA,EAAO;IACxCA,4DAAA,gBAA2B;IAAAA,oDAAA,IAAsB;IACnDA,0DADmD,EAAO,EACpD;IAEJA,4DADF,eAAwB,gBACK;IAAAA,oDAAA,eAAO;IAAAA,0DAAA,EAAO;IACzCA,4DAAA,gBAA2B;IAAAA,oDAAA,IAAuC;;IACpEA,0DADoE,EAAO,EACrE;IAEJA,4DADF,eAAwB,gBACK;IAAAA,oDAAA,qBAAa;IAAAA,0DAAA,EAAO;IAC/CA,4DAAA,gBAA2B;IAAAA,oDAAA,IAAoC;IAEnEA,0DAFmE,EAAO,EAClE,EACF;IAGJA,4DADF,eAA4B,kBAC2B;IAA7BA,wDAAA,mBAAA4X,0DAAA;MAAA,MAAAC,SAAA,GAAA7X,2DAAA,CAAAa,GAAA,EAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAf,2DAAA;MAAA,OAAAA,yDAAA,CAASe,MAAA,CAAA+W,UAAA,CAAAD,SAAA,CAAkB;IAAA,EAAC;IAClD7X,4DAAA,gBAAU;IAAAA,oDAAA,kBAAU;IACtBA,0DADsB,EAAW,EACxB;IACTA,4DAAA,kBAAqD;IAA7BA,wDAAA,mBAAA+X,0DAAA;MAAA,MAAAF,SAAA,GAAA7X,2DAAA,CAAAa,GAAA,EAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAf,2DAAA;MAAA,OAAAA,yDAAA,CAASe,MAAA,CAAAiX,UAAA,CAAAH,SAAA,CAAkB;IAAA,EAAC;IAClD7X,4DAAA,gBAAU;IAAAA,oDAAA,YAAI;IAChBA,0DADgB,EAAW,EAClB;IACTA,4DAAA,kBAAwD;IAAhCA,wDAAA,mBAAAiY,0DAAA;MAAA,MAAAJ,SAAA,GAAA7X,2DAAA,CAAAa,GAAA,EAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAf,2DAAA;MAAA,OAAAA,yDAAA,CAASe,MAAA,CAAAmX,aAAA,CAAAL,SAAA,CAAqB;IAAA,EAAC;IACrD7X,4DAAA,gBAAU;IAAAA,oDAAA,eAAO;IACnBA,0DADmB,EAAW,EACrB;IAEPA,4DADF,kBAAyD,gBAC7C;IAAAA,oDAAA,iBAAS;IACrBA,0DADqB,EAAW,EACvB;IAEPA,4DADF,yBAAgC,kBACoB;IAA5BA,wDAAA,mBAAAmY,0DAAA;MAAA,MAAAN,SAAA,GAAA7X,2DAAA,CAAAa,GAAA,EAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAf,2DAAA;MAAA,OAAAA,yDAAA,CAASe,MAAA,CAAAqX,SAAA,CAAAP,SAAA,CAAiB;IAAA,EAAC;IAC/C7X,4DAAA,gBAAU;IAAAA,oDAAA,cAAM;IAAAA,0DAAA,EAAW;IAC3BA,4DAAA,YAAM;IAAAA,oDAAA,kBAAU;IAClBA,0DADkB,EAAO,EAChB;IACTA,4DAAA,kBAAyD;IAAnCA,wDAAA,mBAAAqY,0DAAA;MAAA,MAAAR,SAAA,GAAA7X,2DAAA,CAAAa,GAAA,EAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAf,2DAAA;MAAA,OAAAA,yDAAA,CAASe,MAAA,CAAAuX,gBAAA,CAAAT,SAAA,CAAwB;IAAA,EAAC;IACtD7X,4DAAA,gBAAU;IAAAA,oDAAA,eAAO;IAAAA,0DAAA,EAAW;IAC5BA,4DAAA,YAAM;IAAAA,oDAAA,yBAAiB;IACzBA,0DADyB,EAAO,EACvB;IACTA,4DAAA,kBAAyD;IAAnCA,wDAAA,mBAAAuY,0DAAA;MAAA,MAAAV,SAAA,GAAA7X,2DAAA,CAAAa,GAAA,EAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAf,2DAAA;MAAA,OAAAA,yDAAA,CAASe,MAAA,CAAAyX,gBAAA,CAAAX,SAAA,CAAwB;IAAA,EAAC;IACtD7X,4DAAA,gBAAU;IAAAA,oDAAA,aAAK;IAAAA,0DAAA,EAAW;IAC1BA,4DAAA,YAAM;IAAAA,oDAAA,kBAAU;IAIxBA,0DAJwB,EAAO,EAChB,EACA,EACP,EACF;;;;;;IAnEyBA,uDAAA,GAAuB;IAAvBA,wDAAA,CAAA6X,SAAA,CAAApW,MAAA,CAAuB;IAE1CzB,uDAAA,GAA+B;IAA/BA,+DAAA,CAAAA,yDAAA,QAAA6X,SAAA,CAAApW,MAAA,EAA+B;IAKfzB,uDAAA,GAAiB;IAAjBA,+DAAA,CAAA6X,SAAA,CAAApO,IAAA,CAAiB;IAI/BzJ,uDAAA,GAAkB;IAAlBA,+DAAA,CAAA6X,SAAA,CAAAY,KAAA,CAAkB;IAIlBzY,uDAAA,GAAkB;IAAlBA,+DAAA,CAAA6X,SAAA,CAAAa,KAAA,CAAkB;IAQC1Y,uDAAA,GAAqC;IAArCA,gEAAA,WAAAA,yDAAA,SAAA6X,SAAA,CAAAc,cAAA,MAAqC;IAIrC3Y,uDAAA,GAAsB;IAAtBA,+DAAA,CAAA6X,SAAA,CAAAe,SAAA,CAAsB;IAItB5Y,uDAAA,GAAuC;IAAvCA,+DAAA,CAAAA,yDAAA,SAAA6X,SAAA,CAAAgB,QAAA,cAAuC;IAIvC7Y,uDAAA,GAAoC;IAApCA,+DAAA,CAAAe,MAAA,CAAA+X,UAAA,CAAAjB,SAAA,CAAAkB,WAAA,EAAoC;IAczC/Y,uDAAA,IAAgC;IAAhCA,wDAAA,sBAAAgZ,aAAA,CAAgC;;;AA4WlE,MAAO5R,gBAAgB;EAre7BpE,YAAA;IAseE,KAAAC,UAAU,GAAG,EAAE;IACf,KAAAgW,YAAY,GAAG,EAAE;IAEjB,KAAAC,OAAO,GAAa,CAClB;MACE1V,EAAE,EAAE,GAAG;MACPiG,IAAI,EAAE,YAAY;MAClBgP,KAAK,EAAE,sBAAsB;MAC7BC,KAAK,EAAE,kBAAkB;MACzBjX,MAAM,EAAE,QAAQ;MAChBkX,cAAc,EAAE,KAAK;MACrBE,QAAQ,EAAE,IAAIpV,IAAI,CAAC,IAAI,EAAE,CAAC,EAAE,EAAE,CAAC;MAC/BsV,WAAW,EAAE,IAAItV,IAAI,CAAC,IAAI,EAAE,CAAC,EAAE,EAAE,CAAC;MAClCmV,SAAS,EAAE;KACZ,EACD;MACEpV,EAAE,EAAE,GAAG;MACPiG,IAAI,EAAE,eAAe;MACrBgP,KAAK,EAAE,yBAAyB;MAChCC,KAAK,EAAE,kBAAkB;MACzBjX,MAAM,EAAE,QAAQ;MAChBkX,cAAc,EAAE,KAAK;MACrBE,QAAQ,EAAE,IAAIpV,IAAI,CAAC,IAAI,EAAE,EAAE,EAAE,EAAE,CAAC;MAChCsV,WAAW,EAAE,IAAItV,IAAI,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC;MACjCmV,SAAS,EAAE;KACZ,EACD;MACEpV,EAAE,EAAE,GAAG;MACPiG,IAAI,EAAE,aAAa;MACnBgP,KAAK,EAAE,uBAAuB;MAC9BC,KAAK,EAAE,kBAAkB;MACzBjX,MAAM,EAAE,QAAQ;MAChBkX,cAAc,EAAE,MAAM;MACtBE,QAAQ,EAAE,IAAIpV,IAAI,CAAC,IAAI,EAAE,EAAE,EAAE,CAAC,CAAC;MAC/BsV,WAAW,EAAE,IAAItV,IAAI,CAAC,IAAI,EAAE,CAAC,EAAE,EAAE,CAAC;MAClCmV,SAAS,EAAE;KACZ,EACD;MACEpV,EAAE,EAAE,GAAG;MACPiG,IAAI,EAAE,cAAc;MACpBgP,KAAK,EAAE,wBAAwB;MAC/BC,KAAK,EAAE,kBAAkB;MACzBjX,MAAM,EAAE,SAAS;MACjBkX,cAAc,EAAE,KAAK;MACrBE,QAAQ,EAAE,IAAIpV,IAAI,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC;MAC9BsV,WAAW,EAAE,IAAItV,IAAI,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC;MACjCmV,SAAS,EAAE;KACZ,CACF;IAED,KAAAO,eAAe,GAAa,EAAE;;EAE9BxV,QAAQA,CAAA;IACN,IAAI,CAACwV,eAAe,GAAG,CAAC,GAAG,IAAI,CAACD,OAAO,CAAC;EAC1C;EAEAE,cAAcA,CAAC3X,MAAc;IAC3B,OAAO,IAAI,CAACyX,OAAO,CAAClV,MAAM,CAACqV,MAAM,IAAIA,MAAM,CAAC5X,MAAM,KAAKA,MAAM,CAAC,CAACkB,MAAM;EACvE;EAEA2W,gBAAgBA,CAAA;IACd,OAAO,IAAI,CAACJ,OAAO,CAChBlV,MAAM,CAACqV,MAAM,IAAIA,MAAM,CAAC5X,MAAM,KAAK,QAAQ,CAAC,CAC5C8X,MAAM,CAAC,CAACC,KAAK,EAAEH,MAAM,KAAKG,KAAK,GAAGH,MAAM,CAACV,cAAc,EAAE,CAAC,CAAC;EAChE;EAEAc,aAAaA,CAAA;IACX,IAAI5V,QAAQ,GAAG,CAAC,GAAG,IAAI,CAACqV,OAAO,CAAC;IAEhC;IACA,IAAI,IAAI,CAACjW,UAAU,EAAE;MACnBY,QAAQ,GAAGA,QAAQ,CAACG,MAAM,CAACqV,MAAM,IAC/BA,MAAM,CAAC5P,IAAI,CAAC1F,WAAW,EAAE,CAACG,QAAQ,CAAC,IAAI,CAACjB,UAAU,CAACc,WAAW,EAAE,CAAC,IACjEsV,MAAM,CAACZ,KAAK,CAAC1U,WAAW,EAAE,CAACG,QAAQ,CAAC,IAAI,CAACjB,UAAU,CAACc,WAAW,EAAE,CAAC,CACnE;;IAGH;IACA,IAAI,IAAI,CAACkV,YAAY,EAAE;MACrBpV,QAAQ,GAAGA,QAAQ,CAACG,MAAM,CAACqV,MAAM,IAAIA,MAAM,CAAC5X,MAAM,KAAK,IAAI,CAACwX,YAAY,CAAC;;IAG3E,IAAI,CAACE,eAAe,GAAGtV,QAAQ;EACjC;EAEAiV,UAAUA,CAACjJ,IAAU;IACnB,MAAM1L,GAAG,GAAG,IAAIV,IAAI,EAAE;IACtB,MAAMiW,QAAQ,GAAGC,IAAI,CAACC,GAAG,CAACzV,GAAG,CAAC0V,OAAO,EAAE,GAAGhK,IAAI,CAACgK,OAAO,EAAE,CAAC;IACzD,MAAMC,QAAQ,GAAGH,IAAI,CAACI,IAAI,CAACL,QAAQ,IAAI,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;IAE5D,IAAII,QAAQ,KAAK,CAAC,EAAE;MAClB,OAAO,OAAO;KACf,MAAM,IAAIA,QAAQ,IAAI,CAAC,EAAE;MACxB,OAAO,GAAGA,QAAQ,WAAW;KAC9B,MAAM;MACL,OAAOjK,IAAI,CAACmK,kBAAkB,EAAE;;EAEpC;EAEAC,mBAAmBA,CAAA;IACjB;EAAA;EAGFnC,UAAUA,CAACuB,MAAc;IACvB;EAAA;EAGFrB,UAAUA,CAACqB,MAAc;IACvB;EAAA;EAGFnB,aAAaA,CAACmB,MAAc;IAC1B;EAAA;EAGFjB,SAASA,CAACiB,MAAc;IACtB;EAAA;EAGFf,gBAAgBA,CAACe,MAAc;IAC7B;EAAA;EAGFb,gBAAgBA,CAACa,MAAc;IAC7B;EAAA;;;uBA7HSjS,gBAAgB;IAAA;EAAA;;;YAAhBA,gBAAgB;MAAAhC,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAA0U,0BAAAxU,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAherB1F,4DAFJ,aAA+B,aACD,SACtB;UAAAA,oDAAA,uBAAgB;UAAAA,0DAAA,EAAK;UACzBA,4DAAA,gBAA0E;UAAhCA,wDAAA,mBAAAma,kDAAA;YAAA,OAASxU,GAAA,CAAAsU,mBAAA,EAAqB;UAAA,EAAC;UACvEja,4DAAA,eAAU;UAAAA,oDAAA,UAAG;UAAAA,0DAAA,EAAW;UACxBA,oDAAA,mBACF;UACFA,0DADE,EAAS,EACL;UAOEA,4DALR,aAA6B,aAEA,cACF,cACS,gBAClB;UAAAA,oDAAA,cAAM;UAClBA,0DADkB,EAAW,EACvB;UAEJA,4DADF,cAA0B,cACA;UAAAA,oDAAA,IAA8B;UAAAA,0DAAA,EAAM;UAC5DA,4DAAA,eAAwB;UAAAA,oDAAA,sBAAc;UAE1CA,0DAF0C,EAAM,EACxC,EACF;UAIFA,4DAFJ,cAAuB,eACU,gBACnB;UAAAA,oDAAA,gBAAQ;UACpBA,0DADoB,EAAW,EACzB;UAEJA,4DADF,cAA0B,cACA;UAAAA,oDAAA,IAA+B;UAAAA,0DAAA,EAAM;UAC7DA,4DAAA,eAAwB;UAAAA,oDAAA,uBAAe;UAE3CA,0DAF2C,EAAM,EACzC,EACF;UAIFA,4DAFJ,cAAuB,eACU,gBACnB;UAAAA,oDAAA,8BAAsB;UAClCA,0DADkC,EAAW,EACvC;UAEJA,4DADF,cAA0B,cACA;UAAAA,oDAAA,IAAkC;;UAAAA,0DAAA,EAAM;UAChEA,4DAAA,eAAwB;UAAAA,oDAAA,sBAAc;UAG5CA,0DAH4C,EAAM,EACxC,EACF,EACF;UAKFA,4DAFJ,eAAkC,eACP,UACnB;UAAAA,oDAAA,mBAAW;UAAAA,0DAAA,EAAK;UAGhBA,4DAFJ,eAA2B,0BACiC,oBACpC;UAAAA,oDAAA,cAAM;UAAAA,0DAAA,EAAW;UACrCA,4DAAA,iBAAgG;UAAnDA,8DAAA,2BAAAoa,0DAAA7X,MAAA;YAAAvC,gEAAA,CAAA2F,GAAA,CAAA1C,UAAA,EAAAV,MAAA,MAAAoD,GAAA,CAAA1C,UAAA,GAAAV,MAAA;YAAA,OAAAA,MAAA;UAAA,EAAwB;UAACvC,wDAAA,mBAAAqa,kDAAA;YAAA,OAAS1U,GAAA,CAAA8T,aAAA,EAAe;UAAA,EAAC;UACjGzZ,0DADE,EAAgG,EACjF;UAEfA,4DADF,0BAA0D,sBACiB;UAA7DA,8DAAA,yBAAAsa,6DAAA/X,MAAA;YAAAvC,gEAAA,CAAA2F,GAAA,CAAAsT,YAAA,EAAA1W,MAAA,MAAAoD,GAAA,CAAAsT,YAAA,GAAA1W,MAAA;YAAA,OAAAA,MAAA;UAAA,EAAwB;UAACvC,wDAAA,6BAAAua,iEAAA;YAAA,OAAmB5U,GAAA,CAAA8T,aAAA,EAAe;UAAA,EAAC;UACtEzZ,4DAAA,sBAAqB;UAAAA,oDAAA,kBAAU;UAAAA,0DAAA,EAAa;UAC5CA,4DAAA,sBAA2B;UAAAA,oDAAA,cAAM;UAAAA,0DAAA,EAAa;UAC9CA,4DAAA,sBAA4B;UAAAA,oDAAA,eAAO;UAAAA,0DAAA,EAAa;UAChDA,4DAAA,sBAA6B;UAAAA,oDAAA,gBAAQ;UAI7CA,0DAJ6C,EAAa,EACvC,EACE,EACb,EACF;UAENA,4DAAA,eAA0B;UACxBA,wDAAA,KAAAwa,gCAAA,oBAAgE;UA4ExExa,0DAHM,EAAM,EACF,EACF,EACF;;;UA3H4BA,uDAAA,IAA8B;UAA9BA,+DAAA,CAAA2F,GAAA,CAAAyT,cAAA,WAA8B;UAU9BpZ,uDAAA,GAA+B;UAA/BA,+DAAA,CAAA2F,GAAA,CAAAyT,cAAA,YAA+B;UAU/BpZ,uDAAA,GAAkC;UAAlCA,gEAAA,WAAAA,yDAAA,QAAA2F,GAAA,CAAA2T,gBAAA,QAAkC;UAaXtZ,uDAAA,IAAwB;UAAxBA,8DAAA,YAAA2F,GAAA,CAAA1C,UAAA,CAAwB;UAGzDjD,uDAAA,GAAwB;UAAxBA,8DAAA,UAAA2F,GAAA,CAAAsT,YAAA,CAAwB;UAWIjZ,uDAAA,IAAkB;UAAlBA,wDAAA,YAAA2F,GAAA,CAAAwT,eAAA,CAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IC7D5DnZ,4DAAA,cAAuD;IAAAA,oDAAA,GAAS;IAAAA,0DAAA,EAAM;;;;IAAfA,uDAAA,EAAS;IAATA,+DAAA,CAAAya,MAAA,CAAS;;;;;;IAChEza,4DAAA,cAM4B;IAA1BA,wDAAA,mBAAA0a,wDAAA;MAAA,MAAAC,MAAA,GAAA3a,2DAAA,CAAA4a,GAAA,EAAA9Z,SAAA;MAAA,MAAA+Z,MAAA,GAAA7a,2DAAA;MAAA,OAAAA,yDAAA,CAAS6a,MAAA,CAAArL,UAAA,CAAAmL,MAAA,CAAe;IAAA,EAAC;IACzB3a,oDAAA,GACF;IAAAA,0DAAA,EAAM;;;;IAHJA,yDAFA,gBAAA2a,MAAA,CAAAG,UAAA,CAAoC,UAAAH,MAAA,CAAAhL,OAAA,CACT,oBAAAgL,MAAA,CAAAI,cAAA,CACiB;IAE5C/a,uDAAA,EACF;IADEA,gEAAA,MAAA2a,MAAA,CAAA9K,IAAA,MACF;;;;;IAUI7P,4DAFJ,cAA4E,cAC5C,cACE;IAAAA,oDAAA,GAAsB;IAAAA,0DAAA,EAAM;IAC1DA,4DAAA,cAAgC;IAAAA,oDAAA,GAAwB;IAC1DA,0DAD0D,EAAM,EAC1D;IACNA,4DAAA,cAA8B;IAAAA,oDAAA,GAAsB;IACtDA,0DADsD,EAAM,EACtD;;;;IAJ4BA,uDAAA,GAAsB;IAAtBA,+DAAA,CAAAgb,cAAA,CAAA9K,IAAA,CAAsB;IACpBlQ,uDAAA,GAAwB;IAAxBA,+DAAA,CAAAgb,cAAA,CAAA3B,MAAA,CAAwB;IAE5BrZ,uDAAA,GAAsB;IAAtBA,+DAAA,CAAAgb,cAAA,CAAA/K,IAAA,CAAsB;;;;;IAatDjQ,4DAAA,cAAgE;IAC9DA,uDAAA,cAAyD;IACzDA,4DAAA,WAAM;IAAAA,oDAAA,GAAiB;IACzBA,0DADyB,EAAO,EAC1B;;;;IAFuBA,uDAAA,EAAuB;IAAvBA,wDAAA,CAAAib,SAAA,CAAAxZ,MAAA,CAAuB;IAC5CzB,uDAAA,GAAiB;IAAjBA,+DAAA,CAAAib,SAAA,CAAAxR,IAAA,CAAiB;;;;;IAyCzBzJ,4DADF,cAA+D,cACvC;IAAAA,oDAAA,mBAAE;IAAAA,0DAAA,EAAM;IAE5BA,4DADF,cAAsB,cACE;IAAAA,oDAAA,GAAc;IAAAA,0DAAA,EAAM;IAC1CA,4DAAA,cAAsB;IAAAA,oDAAA,GAAc;IACtCA,0DADsC,EAAM,EACtC;IACNA,4DAAA,iBAA2B;IAAAA,oDAAA,aAAC;IAC9BA,0DAD8B,EAAS,EACjC;;;;IAJoBA,uDAAA,GAAc;IAAdA,+DAAA,CAAAkb,MAAA,CAAAzR,IAAA,CAAc;IACdzJ,uDAAA,GAAc;IAAdA,+DAAA,CAAAkb,MAAA,CAAArL,IAAA,CAAc;;;;;IAgBtC7P,uDAAA,cAAkH;;;;IAArCA,yDAA9B,WAAAob,MAAA,CAAAC,MAAA,MAA6B,eAAAD,MAAA,CAAA1E,KAAA,CAA+B;;;;;IAG3G1W,4DAAA,WAAwC;IAAAA,oDAAA,GAAW;IAAAA,0DAAA,EAAO;;;;IAAlBA,uDAAA,EAAW;IAAXA,+DAAA,CAAAsb,QAAA,CAAW;;;;;IAQnDtb,4DADF,cAA6E,cAC3C;IAAAA,oDAAA,GAAwB;IAAAA,0DAAA,EAAM;IAE5DA,4DADF,cAA8B,cACE;IAAAA,oDAAA,GAAsB;IAAAA,0DAAA,EAAM;IAC1DA,4DAAA,cAA8B;IAAAA,oDAAA,GAAsB;IAExDA,0DAFwD,EAAM,EACtD,EACF;;;;IAL4BA,uDAAA,GAAwB;IAAxBA,+DAAA,CAAAub,eAAA,CAAAC,MAAA,CAAwB;IAExBxb,uDAAA,GAAsB;IAAtBA,+DAAA,CAAAub,eAAA,CAAArL,IAAA,CAAsB;IACtBlQ,uDAAA,GAAsB;IAAtBA,+DAAA,CAAAub,eAAA,CAAA1L,IAAA,CAAsB;;;AA2W9D,MAAO/I,kBAAkB;EA5e/B9D,YAAA;IA6eE,KAAAmO,YAAY,GAAG,MAAM;IACrB,KAAAsK,WAAW,GAAG,IAAI;IAClB,KAAAC,kBAAkB,GAAG,cAAc;IACnC,KAAAzY,UAAU,GAAG,EAAE;IAEf,KAAA0Y,UAAU,GAAG,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;IAE9D,KAAAC,YAAY,GAAG,CACb;MAAE/L,IAAI,EAAE,EAAE;MAAEiL,UAAU,EAAE,IAAI;MAAEnL,OAAO,EAAE,KAAK;MAAEoL,cAAc,EAAE;IAAK,CAAE,EACrE;MAAElL,IAAI,EAAE,CAAC;MAAEiL,UAAU,EAAE,KAAK;MAAEnL,OAAO,EAAE,IAAI;MAAEoL,cAAc,EAAE;IAAI,CAAE,EACnE;MAAElL,IAAI,EAAE,CAAC;MAAEiL,UAAU,EAAE,KAAK;MAAEnL,OAAO,EAAE,KAAK;MAAEoL,cAAc,EAAE;IAAK,CAAE,EACrE;MAAElL,IAAI,EAAE,CAAC;MAAEiL,UAAU,EAAE,KAAK;MAAEnL,OAAO,EAAE,KAAK;MAAEoL,cAAc,EAAE;IAAK,CAAE,EACrE;MAAElL,IAAI,EAAE,CAAC;MAAEiL,UAAU,EAAE,KAAK;MAAEnL,OAAO,EAAE,KAAK;MAAEoL,cAAc,EAAE;IAAK,CAAE,EACrE;MAAElL,IAAI,EAAE,CAAC;MAAEiL,UAAU,EAAE,KAAK;MAAEnL,OAAO,EAAE,KAAK;MAAEoL,cAAc,EAAE;IAAK,CAAE,EACrE;MAAElL,IAAI,EAAE,CAAC;MAAEiL,UAAU,EAAE,KAAK;MAAEnL,OAAO,EAAE,KAAK;MAAEoL,cAAc,EAAE;IAAK,CAAE,EACrE;MAAElL,IAAI,EAAE,CAAC;MAAEiL,UAAU,EAAE,KAAK;MAAEnL,OAAO,EAAE,KAAK;MAAEoL,cAAc,EAAE;IAAK,CAAE,EACrE;MAAElL,IAAI,EAAE,CAAC;MAAEiL,UAAU,EAAE,KAAK;MAAEnL,OAAO,EAAE,KAAK;MAAEoL,cAAc,EAAE;IAAK,CAAE,EACrE;MAAElL,IAAI,EAAE,CAAC;MAAEiL,UAAU,EAAE,KAAK;MAAEnL,OAAO,EAAE,KAAK;MAAEoL,cAAc,EAAE;IAAK,CAAE,EACrE;MAAElL,IAAI,EAAE,EAAE;MAAEiL,UAAU,EAAE,KAAK;MAAEnL,OAAO,EAAE,KAAK;MAAEoL,cAAc,EAAE;IAAK,CAAE,EACtE;MAAElL,IAAI,EAAE,EAAE;MAAEiL,UAAU,EAAE,KAAK;MAAEnL,OAAO,EAAE,KAAK;MAAEoL,cAAc,EAAE;IAAK,CAAE,EACtE;MAAElL,IAAI,EAAE,EAAE;MAAEiL,UAAU,EAAE,KAAK;MAAEnL,OAAO,EAAE,KAAK;MAAEoL,cAAc,EAAE;IAAI,CAAE,EACrE;MAAElL,IAAI,EAAE,EAAE;MAAEiL,UAAU,EAAE,KAAK;MAAEnL,OAAO,EAAE,KAAK;MAAEoL,cAAc,EAAE;IAAK,CAAE,EACtE;MAAElL,IAAI,EAAE,EAAE;MAAEiL,UAAU,EAAE,KAAK;MAAEnL,OAAO,EAAE,KAAK;MAAEoL,cAAc,EAAE;IAAK,CAAE,EACtE;MAAElL,IAAI,EAAE,EAAE;MAAEiL,UAAU,EAAE,KAAK;MAAEnL,OAAO,EAAE,KAAK;MAAEoL,cAAc,EAAE;IAAK,CAAE,EACtE;MAAElL,IAAI,EAAE,EAAE;MAAEiL,UAAU,EAAE,KAAK;MAAEnL,OAAO,EAAE,KAAK;MAAEoL,cAAc,EAAE;IAAK,CAAE,EACtE;MAAElL,IAAI,EAAE,EAAE;MAAEiL,UAAU,EAAE,KAAK;MAAEnL,OAAO,EAAE,KAAK;MAAEoL,cAAc,EAAE;IAAK,CAAE,EACtE;MAAElL,IAAI,EAAE,EAAE;MAAEiL,UAAU,EAAE,KAAK;MAAEnL,OAAO,EAAE,KAAK;MAAEoL,cAAc,EAAE;IAAK,CAAE,EACtE;MAAElL,IAAI,EAAE,EAAE;MAAEiL,UAAU,EAAE,KAAK;MAAEnL,OAAO,EAAE,KAAK;MAAEoL,cAAc,EAAE;IAAK,CAAE,EACtE;MAAElL,IAAI,EAAE,EAAE;MAAEiL,UAAU,EAAE,KAAK;MAAEnL,OAAO,EAAE,KAAK;MAAEoL,cAAc,EAAE;IAAK,CAAE,EACtE;MAAElL,IAAI,EAAE,EAAE;MAAEiL,UAAU,EAAE,KAAK;MAAEnL,OAAO,EAAE,KAAK;MAAEoL,cAAc,EAAE;IAAK,CAAE,EACtE;MAAElL,IAAI,EAAE,EAAE;MAAEiL,UAAU,EAAE,KAAK;MAAEnL,OAAO,EAAE,KAAK;MAAEoL,cAAc,EAAE;IAAK,CAAE,EACtE;MAAElL,IAAI,EAAE,EAAE;MAAEiL,UAAU,EAAE,KAAK;MAAEnL,OAAO,EAAE,KAAK;MAAEoL,cAAc,EAAE;IAAK,CAAE,EACtE;MAAElL,IAAI,EAAE,EAAE;MAAEiL,UAAU,EAAE,KAAK;MAAEnL,OAAO,EAAE,KAAK;MAAEoL,cAAc,EAAE;IAAK,CAAE,EACtE;MAAElL,IAAI,EAAE,EAAE;MAAEiL,UAAU,EAAE,KAAK;MAAEnL,OAAO,EAAE,KAAK;MAAEoL,cAAc,EAAE;IAAK,CAAE,EACtE;MAAElL,IAAI,EAAE,EAAE;MAAEiL,UAAU,EAAE,KAAK;MAAEnL,OAAO,EAAE,KAAK;MAAEoL,cAAc,EAAE;IAAK,CAAE,EACtE;MAAElL,IAAI,EAAE,EAAE;MAAEiL,UAAU,EAAE,KAAK;MAAEnL,OAAO,EAAE,KAAK;MAAEoL,cAAc,EAAE;IAAK,CAAE,EACtE;MAAElL,IAAI,EAAE,EAAE;MAAEiL,UAAU,EAAE,KAAK;MAAEnL,OAAO,EAAE,KAAK;MAAEoL,cAAc,EAAE;IAAK,CAAE,EACtE;MAAElL,IAAI,EAAE,EAAE;MAAEiL,UAAU,EAAE,KAAK;MAAEnL,OAAO,EAAE,KAAK;MAAEoL,cAAc,EAAE;IAAK,CAAE,EACtE;MAAElL,IAAI,EAAE,EAAE;MAAEiL,UAAU,EAAE,KAAK;MAAEnL,OAAO,EAAE,KAAK;MAAEoL,cAAc,EAAE;IAAK,CAAE,EACtE;MAAElL,IAAI,EAAE,EAAE;MAAEiL,UAAU,EAAE,KAAK;MAAEnL,OAAO,EAAE,KAAK;MAAEoL,cAAc,EAAE;IAAK,CAAE,EACtE;MAAElL,IAAI,EAAE,CAAC;MAAEiL,UAAU,EAAE,IAAI;MAAEnL,OAAO,EAAE,KAAK;MAAEoL,cAAc,EAAE;IAAK,CAAE,EACpE;MAAElL,IAAI,EAAE,CAAC;MAAEiL,UAAU,EAAE,IAAI;MAAEnL,OAAO,EAAE,KAAK;MAAEoL,cAAc,EAAE;IAAK,CAAE,EACpE;MAAElL,IAAI,EAAE,CAAC;MAAEiL,UAAU,EAAE,IAAI;MAAEnL,OAAO,EAAE,KAAK;MAAEoL,cAAc,EAAE;IAAK,CAAE,CACrE;IAED,KAAAc,iBAAiB,GAAG,CAClB;MAAE3L,IAAI,EAAE,cAAc;MAAEmJ,MAAM,EAAE,aAAa;MAAEpJ,IAAI,EAAE;IAAO,CAAE,EAC9D;MAAEC,IAAI,EAAE,cAAc;MAAEmJ,MAAM,EAAE,cAAc;MAAEpJ,IAAI,EAAE;IAAO,CAAE,CAChE;IAED,KAAA6L,eAAe,GAAG,CAChB;MAAErS,IAAI,EAAE,YAAY;MAAEhI,MAAM,EAAE;IAAQ,CAAE,EACxC;MAAEgI,IAAI,EAAE,eAAe;MAAEhI,MAAM,EAAE;IAAU,CAAE,EAC7C;MAAEgI,IAAI,EAAE,aAAa;MAAEhI,MAAM,EAAE;IAAQ,CAAE,CAC1C;IAED,KAAAsa,eAAe,GAAG,CAChB;MAAEtS,IAAI,EAAE,oBAAoB;MAAEoG,IAAI,EAAE;IAAe,CAAE,EACrD;MAAEpG,IAAI,EAAE,sBAAsB;MAAEoG,IAAI,EAAE;IAAY,CAAE,CACrD;IAED,KAAAmM,SAAS,GAAG,CACV;MAAEX,MAAM,EAAE,EAAE;MAAE3E,KAAK,EAAE;IAAS,CAAE,EAChC;MAAE2E,MAAM,EAAE,EAAE;MAAE3E,KAAK,EAAE;IAAS,CAAE,EAChC;MAAE2E,MAAM,EAAE,EAAE;MAAE3E,KAAK,EAAE;IAAS,CAAE,EAChC;MAAE2E,MAAM,EAAE,EAAE;MAAE3E,KAAK,EAAE;IAAS,CAAE,EAChC;MAAE2E,MAAM,EAAE,EAAE;MAAE3E,KAAK,EAAE;IAAS,CAAE,EAChC;MAAE2E,MAAM,EAAE,EAAE;MAAE3E,KAAK,EAAE;IAAS,CAAE,EAChC;MAAE2E,MAAM,EAAE,EAAE;MAAE3E,KAAK,EAAE;IAAS,CAAE,EAChC;MAAE2E,MAAM,EAAE,EAAE;MAAE3E,KAAK,EAAE;IAAS,CAAE,EAChC;MAAE2E,MAAM,EAAE,EAAE;MAAE3E,KAAK,EAAE;IAAS,CAAE,CACjC;IAED,KAAAuF,WAAW,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;IAE3D,KAAAC,kBAAkB,GAAG,CACnB;MAAEV,MAAM,EAAE,SAAS;MAAEtL,IAAI,EAAE,cAAc;MAAEL,IAAI,EAAE;IAAe,CAAE,EAClE;MAAE2L,MAAM,EAAE,QAAQ;MAAEtL,IAAI,EAAE,UAAU;MAAEL,IAAI,EAAE;IAAe,CAAE,CAC9D;;EAEDlM,QAAQA,CAAA;IACN;EAAA;EAGFyP,aAAaA,CAAA;IACX;EAAA;EAGFC,SAASA,CAAA;IACP;EAAA;EAGF7D,UAAUA,CAACqD,GAAQ;IACjB;EAAA;;;uBA9FS/L,kBAAkB;IAAA;EAAA;;;YAAlBA,kBAAkB;MAAA1B,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAA2W,4BAAAzW,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UArerB1F,4DAJN,aAAiC,aAEN,aACS,SAC1B;UAAAA,oDAAA,iBAAU;UAAAA,0DAAA,EAAK;UAKfA,4DAFJ,aAA6B,aACE,gBACuB;UAA1BA,wDAAA,mBAAAoc,oDAAA;YAAA,OAASzW,GAAA,CAAAyN,aAAA,EAAe;UAAA,EAAC;UAACpT,oDAAA,aAAC;UAAAA,0DAAA,EAAS;UAC5DA,4DAAA,SAAI;UAAAA,oDAAA,IAAkB;UAAAA,0DAAA,EAAK;UAC3BA,4DAAA,iBAA8C;UAAtBA,wDAAA,mBAAAqc,qDAAA;YAAA,OAAS1W,GAAA,CAAA0N,SAAA,EAAW;UAAA,EAAC;UAACrT,oDAAA,cAAC;UACjDA,0DADiD,EAAS,EACpD;UAENA,4DAAA,cAA2B;UAEzBA,wDADA,KAAAsc,kCAAA,iBAAuD,KAAAC,kCAAA,iBAO3B;UAIhCvc,0DADE,EAAM,EACF;UAIJA,4DADF,cAA6B,UACvB;UAAAA,oDAAA,IAAmC;UAAAA,0DAAA,EAAK;UAC5CA,4DAAA,eAA8B;UAC5BA,wDAAA,KAAAwc,kCAAA,kBAA4E;UAO9Exc,0DAAA,EAAM;UACNA,4DAAA,kBAAgC;UAAAA,oDAAA,qBAAa;UAC/CA,0DAD+C,EAAS,EAClD;UAKFA,4DAFJ,eAA8B,eACA,UACtB;UAAAA,oDAAA,wBAAgB;UAAAA,0DAAA,EAAK;UACzBA,4DAAA,kBAA+B;UAAAA,oDAAA,oBAAY;UAC7CA,0DAD6C,EAAS,EAChD;UACNA,4DAAA,eAAyB;UACvBA,wDAAA,KAAAyc,kCAAA,kBAAgE;UAOxEzc,0DAHM,EAAM,EACF,EACF,EACF;UAOAA,4DAJN,eAA0B,eAEG,eACG,UACtB;UAAAA,oDAAA,aAAK;UAAAA,0DAAA,EAAK;UAEZA,4DADF,eAAwB,iBACsD;UAAzBA,8DAAA,2BAAA0c,4DAAAna,MAAA;YAAAvC,gEAAA,CAAA2F,GAAA,CAAA1C,UAAA,EAAAV,MAAA,MAAAoD,GAAA,CAAA1C,UAAA,GAAAV,MAAA;YAAA,OAAAA,MAAA;UAAA,EAAwB;UAA3EvC,0DAAA,EAA4E;UAC5EA,4DAAA,gBAA0B;UAAAA,oDAAA,oBAAE;UAEhCA,0DAFgC,EAAO,EAC/B,EACF;UAIFA,4DAFJ,eAA6B,eACA,eACA;UAAAA,oDAAA,oBAAE;UAAAA,0DAAA,EAAM;UACjCA,4DAAA,YAAM;UAAAA,oDAAA,mBAAW;UACnBA,0DADmB,EAAO,EACpB;UAEJA,4DADF,eAA2B,eACA;UAAAA,oDAAA,oBAAE;UAAAA,0DAAA,EAAM;UACjCA,4DAAA,YAAM;UAAAA,oDAAA,gBAAQ;UAChBA,0DADgB,EAAO,EACjB;UAEJA,4DADF,eAA2B,eACA;UAAAA,oDAAA,oBAAE;UAAAA,0DAAA,EAAM;UACjCA,4DAAA,YAAM;UAAAA,oDAAA,gBAAQ;UAChBA,0DADgB,EAAO,EACjB;UAEJA,4DADF,eAA2B,eACA;UAAAA,oDAAA,oBAAE;UAAAA,0DAAA,EAAM;UACjCA,4DAAA,YAAM;UAAAA,oDAAA,iBAAS;UAEnBA,0DAFmB,EAAO,EAClB,EACF;UAGJA,4DADF,eAA8B,UACxB;UAAAA,oDAAA,wBAAgB;UAAAA,0DAAA,EAAK;UACzBA,wDAAA,KAAA2c,kCAAA,mBAA+D;UASnE3c,0DADE,EAAM,EACF;UAKFA,4DAFJ,eAA6B,eACC,UACtB;UAAAA,oDAAA,eAAO;UAAAA,0DAAA,EAAK;UAChBA,4DAAA,kBAAoC;UAAAA,oDAAA,yBAAiB;UACvDA,0DADuD,EAAS,EAC1D;UAGJA,4DADF,eAA6B,eACH;UACtBA,wDAAA,KAAA4c,kCAAA,kBAA4G;UAC9G5c,0DAAA,EAAM;UACNA,4DAAA,eAA0B;UACxBA,wDAAA,KAAA6c,mCAAA,mBAAwC;UAC1C7c,0DAAA,EAAM;UACNA,4DAAA,eAA0B;UAAAA,oDAAA,iBAAS;UACrCA,0DADqC,EAAM,EACrC;UAGJA,4DADF,eAAiC,UAC3B;UAAAA,oDAAA,2BAAmB;UAAAA,0DAAA,EAAK;UAC5BA,wDAAA,KAAA8c,kCAAA,kBAA6E;UAUrF9c,0DAHM,EAAM,EACF,EACF,EACF;;;UA1HQA,uDAAA,IAAkB;UAAlBA,+DAAA,CAAA2F,GAAA,CAAAwL,YAAA,CAAkB;UAKkBnR,uDAAA,GAAa;UAAbA,wDAAA,YAAA2F,GAAA,CAAAgW,UAAA,CAAa;UAGnC3b,uDAAA,EAAe;UAAfA,wDAAA,YAAA2F,GAAA,CAAAiW,YAAA,CAAe;UAY/B5b,uDAAA,GAAmC;UAAnCA,gEAAA,gBAAA2F,GAAA,CAAA+V,kBAAA,KAAmC;UAEiB1b,uDAAA,GAAoB;UAApBA,wDAAA,YAAA2F,GAAA,CAAAkW,iBAAA,CAAoB;UAkB9B7b,uDAAA,IAAkB;UAAlBA,wDAAA,YAAA2F,GAAA,CAAAmW,eAAA,CAAkB;UAgBX9b,uDAAA,GAAwB;UAAxBA,8DAAA,YAAA2F,GAAA,CAAA1C,UAAA,CAAwB;UA0BlCjD,uDAAA,IAAkB;UAAlBA,wDAAA,YAAA2F,GAAA,CAAAoW,eAAA,CAAkB;UAoB1B/b,uDAAA,GAAY;UAAZA,wDAAA,YAAA2F,GAAA,CAAAqW,SAAA,CAAY;UAGrBhc,uDAAA,GAAc;UAAdA,wDAAA,YAAA2F,GAAA,CAAAsW,WAAA,CAAc;UAOcjc,uDAAA,GAAqB;UAArBA,wDAAA,YAAA2F,GAAA,CAAAuW,kBAAA,CAAqB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ICnDvElc,4DAFJ,cAAiE,cACpC,eACf;IAAAA,oDAAA,GAA+B;IAC3CA,0DAD2C,EAAW,EAChD;IAEJA,4DADF,cAA2B,cACE;IAAAA,oDAAA,GAAc;IAAAA,0DAAA,EAAM;IAE7CA,4DADF,cAA2B,eACG;IAAAA,oDAAA,GAAc;IAAAA,0DAAA,EAAO;IACjDA,4DAAA,gBAA4B;IAAAA,oDAAA,IAAgC;IAEhEA,0DAFgE,EAAO,EAC/D,EACF;IAGFA,4DAFJ,eAA8B,kBACJ,gBACZ;IAAAA,oDAAA,gBAAQ;IACpBA,0DADoB,EAAW,EACtB;IAEPA,4DADF,kBAAwB,gBACZ;IAAAA,oDAAA,iBAAS;IAGzBA,0DAHyB,EAAW,EACvB,EACL,EACF;;;;;IAjBQA,uDAAA,GAA+B;IAA/BA,+DAAA,CAAAmJ,MAAA,CAAA4T,eAAA,CAAAC,MAAA,CAAA9M,IAAA,EAA+B;IAGdlQ,uDAAA,GAAc;IAAdA,+DAAA,CAAAgd,MAAA,CAAAvT,IAAA,CAAc;IAEXzJ,uDAAA,GAAc;IAAdA,+DAAA,CAAAgd,MAAA,CAAAC,IAAA,CAAc;IACdjd,uDAAA,GAAgC;IAAhCA,+DAAA,CAAAmJ,MAAA,CAAA2P,UAAA,CAAAkE,MAAA,CAAAE,UAAA,EAAgC;;;AAmQxE,MAAOhW,cAAc;EA1U3BlE,YAAA;IA2UE,KAAAC,UAAU,GAAG,EAAE;IACf,KAAAE,gBAAgB,GAAkB,IAAI;IAEtC,KAAAga,SAAS,GAAe,CACtB;MACE3Z,EAAE,EAAE,GAAG;MACPiG,IAAI,EAAE,oBAAoB;MAC1BzH,QAAQ,EAAE,WAAW;MACrBkb,UAAU,EAAE,IAAIzZ,IAAI,EAAE;MACtBwZ,IAAI,EAAE,QAAQ;MACd/M,IAAI,EAAE;KACP,EACD;MACE1M,EAAE,EAAE,GAAG;MACPiG,IAAI,EAAE,sBAAsB;MAC5BzH,QAAQ,EAAE,WAAW;MACrBkb,UAAU,EAAE,IAAIzZ,IAAI,CAACA,IAAI,CAACU,GAAG,EAAE,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;MAC1D8Y,IAAI,EAAE,QAAQ;MACd/M,IAAI,EAAE;KACP,EACD;MACE1M,EAAE,EAAE,GAAG;MACPiG,IAAI,EAAE,qBAAqB;MAC3BzH,QAAQ,EAAE,UAAU;MACpBkb,UAAU,EAAE,IAAIzZ,IAAI,CAACA,IAAI,CAACU,GAAG,EAAE,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;MAC1D8Y,IAAI,EAAE,SAAS;MACf/M,IAAI,EAAE;KACP,EACD;MACE1M,EAAE,EAAE,GAAG;MACPiG,IAAI,EAAE,2BAA2B;MACjCzH,QAAQ,EAAE,aAAa;MACvBkb,UAAU,EAAE,IAAIzZ,IAAI,CAACA,IAAI,CAACU,GAAG,EAAE,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;MAC1D8Y,IAAI,EAAE,QAAQ;MACd/M,IAAI,EAAE;KACP,EACD;MACE1M,EAAE,EAAE,GAAG;MACPiG,IAAI,EAAE,iBAAiB;MACvBzH,QAAQ,EAAE,UAAU;MACpBkb,UAAU,EAAE,IAAIzZ,IAAI,CAACA,IAAI,CAACU,GAAG,EAAE,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;MAC1D8Y,IAAI,EAAE,QAAQ;MACd/M,IAAI,EAAE;KACP,CACF;IAED,KAAAkN,iBAAiB,GAAe,EAAE;;EAElCzZ,QAAQA,CAAA;IACN,IAAI,CAACyZ,iBAAiB,GAAG,CAAC,GAAG,IAAI,CAACD,SAAS,CAAC;EAC9C;EAEAE,gBAAgBA,CAACrb,QAAgB;IAC/B,OAAO,IAAI,CAACmb,SAAS,CAACnZ,MAAM,CAACsZ,GAAG,IAAIA,GAAG,CAACtb,QAAQ,KAAKA,QAAQ,CAAC,CAACW,MAAM;EACvE;EAEA4a,cAAcA,CAACvb,QAAgB;IAC7B,IAAI,CAACmB,gBAAgB,GAAGnB,QAAQ;IAChC,IAAI,CAACwb,eAAe,EAAE;EACxB;EAEAA,eAAeA,CAAA;IACb,IAAI3Z,QAAQ,GAAG,CAAC,GAAG,IAAI,CAACsZ,SAAS,CAAC;IAElC;IACA,IAAI,IAAI,CAACha,gBAAgB,EAAE;MACzBU,QAAQ,GAAGA,QAAQ,CAACG,MAAM,CAACsZ,GAAG,IAAIA,GAAG,CAACtb,QAAQ,KAAK,IAAI,CAACmB,gBAAgB,CAAC;;IAG3E;IACA,IAAI,IAAI,CAACF,UAAU,EAAE;MACnBY,QAAQ,GAAGA,QAAQ,CAACG,MAAM,CAACsZ,GAAG,IAC5BA,GAAG,CAAC7T,IAAI,CAAC1F,WAAW,EAAE,CAACG,QAAQ,CAAC,IAAI,CAACjB,UAAU,CAACc,WAAW,EAAE,CAAC,CAC/D;;IAGH,IAAI,CAACqZ,iBAAiB,GAAGvZ,QAAQ;EACnC;EAEAkZ,eAAeA,CAAC7M,IAAY;IAC1B,QAAQA,IAAI;MACV,KAAK,KAAK;QAAE,OAAO,gBAAgB;MACnC,KAAK,MAAM;QAAE,OAAO,aAAa;MACjC,KAAK,KAAK;QAAE,OAAO,SAAS;MAC5B,KAAK,KAAK;MACV,KAAK,KAAK;QAAE,OAAO,OAAO;MAC1B;QAAS,OAAO,mBAAmB;;EAEvC;EAEA4I,UAAUA,CAACjJ,IAAU;IACnB,MAAM1L,GAAG,GAAG,IAAIV,IAAI,EAAE;IACtB,MAAMiW,QAAQ,GAAGC,IAAI,CAACC,GAAG,CAACzV,GAAG,CAAC0V,OAAO,EAAE,GAAGhK,IAAI,CAACgK,OAAO,EAAE,CAAC;IACzD,MAAMC,QAAQ,GAAGH,IAAI,CAACI,IAAI,CAACL,QAAQ,IAAI,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;IAE5D,IAAII,QAAQ,KAAK,CAAC,EAAE;MAClB,OAAO,eAAe;KACvB,MAAM;MACL,OAAO,GAAGA,QAAQ,WAAW;;EAEjC;;;uBArGW5S,cAAc;IAAA;EAAA;;;YAAdA,cAAc;MAAA9B,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAiY,wBAAA/X,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UArUnB1F,4DAFJ,aAA6B,aACD,SACpB;UAAAA,oDAAA,YAAK;UAAAA,0DAAA,EAAK;UAGVA,4DAFJ,aAA8B,wBAC8B,kBACpC;UAAAA,oDAAA,aAAM;UAAAA,0DAAA,EAAW;UACrCA,4DAAA,eAAqG;UAArDA,8DAAA,2BAAA0d,uDAAAnb,MAAA;YAAAvC,gEAAA,CAAA2F,GAAA,CAAA1C,UAAA,EAAAV,MAAA,MAAAoD,GAAA,CAAA1C,UAAA,GAAAV,MAAA;YAAA,OAAAA,MAAA;UAAA,EAAwB;UAACvC,wDAAA,mBAAA2d,+CAAA;YAAA,OAAShY,GAAA,CAAA6X,eAAA,EAAiB;UAAA,EAAC;UAG1Gxd,0DAHM,EAAqG,EACtF,EACb,EACF;UAMAA,4DAJN,aAA2B,cAEQ,cACJ,cAC0C;UAAxCA,wDAAA,mBAAA4d,8CAAA;YAAA,OAASjY,GAAA,CAAA4X,cAAA,CAAe,aAAa,CAAC;UAAA,EAAC;UAE9Dvd,4DADF,eAA2B,gBACf;UAAAA,oDAAA,cAAM;UAClBA,0DADkB,EAAW,EACvB;UACNA,4DAAA,eAA2B;UAAAA,oDAAA,mBAAW;UAAAA,0DAAA,EAAM;UAC5CA,4DAAA,eAA4B;UAAAA,oDAAA,IAA2C;UACzEA,0DADyE,EAAM,EACzE;UAENA,4DAAA,cAAgE;UAArCA,wDAAA,mBAAA6d,8CAAA;YAAA,OAASlY,GAAA,CAAA4X,cAAA,CAAe,UAAU,CAAC;UAAA,EAAC;UAE3Dvd,4DADF,eAA2B,gBACf;UAAAA,oDAAA,cAAM;UAClBA,0DADkB,EAAW,EACvB;UACNA,4DAAA,eAA2B;UAAAA,oDAAA,gBAAQ;UAAAA,0DAAA,EAAM;UACzCA,4DAAA,eAA4B;UAAAA,oDAAA,IAAwC;UACtEA,0DADsE,EAAM,EACtE;UAENA,4DAAA,cAAgE;UAArCA,wDAAA,mBAAA8d,8CAAA;YAAA,OAASnY,GAAA,CAAA4X,cAAA,CAAe,UAAU,CAAC;UAAA,EAAC;UAE3Dvd,4DADF,eAA2B,gBACf;UAAAA,oDAAA,cAAM;UAClBA,0DADkB,EAAW,EACvB;UACNA,4DAAA,eAA2B;UAAAA,oDAAA,gBAAQ;UAAAA,0DAAA,EAAM;UACzCA,4DAAA,eAA4B;UAAAA,oDAAA,IAAwC;UACtEA,0DADsE,EAAM,EACtE;UAENA,4DAAA,cAAiE;UAAtCA,wDAAA,mBAAA+d,8CAAA;YAAA,OAASpY,GAAA,CAAA4X,cAAA,CAAe,WAAW,CAAC;UAAA,EAAC;UAE5Dvd,4DADF,eAA2B,gBACf;UAAAA,oDAAA,cAAM;UAClBA,0DADkB,EAAW,EACvB;UACNA,4DAAA,eAA2B;UAAAA,oDAAA,iBAAS;UAAAA,0DAAA,EAAM;UAC1CA,4DAAA,eAA4B;UAAAA,oDAAA,IAAyC;UAG3EA,0DAH2E,EAAM,EACvE,EACF,EACF;UAKFA,4DAFJ,eAA8B,eACA,UACtB;UAAAA,oDAAA,wBAAgB;UAAAA,0DAAA,EAAK;UAEvBA,4DADF,kBAA0C,gBAC9B;UAAAA,oDAAA,cAAM;UAAAA,0DAAA,EAAW;UAC3BA,oDAAA,yBACF;UACFA,0DADE,EAAS,EACL;UAENA,4DAAA,eAA4B;UAC1BA,wDAAA,KAAAge,8BAAA,mBAAiE;UAuBzEhe,0DAHM,EAAM,EACF,EACF,EACF;;;UA7EkDA,uDAAA,GAAwB;UAAxBA,8DAAA,YAAA2F,GAAA,CAAA1C,UAAA,CAAwB;UAc1CjD,uDAAA,IAA2C;UAA3CA,gEAAA,KAAA2F,GAAA,CAAA0X,gBAAA,0BAA2C;UAQ3Crd,uDAAA,GAAwC;UAAxCA,gEAAA,KAAA2F,GAAA,CAAA0X,gBAAA,uBAAwC;UAQxCrd,uDAAA,GAAwC;UAAxCA,gEAAA,KAAA2F,GAAA,CAAA0X,gBAAA,uBAAwC;UAQxCrd,uDAAA,GAAyC;UAAzCA,gEAAA,KAAA2F,GAAA,CAAA0X,gBAAA,wBAAyC;UAgB5Brd,uDAAA,IAAoB;UAApBA,wDAAA,YAAA2F,GAAA,CAAAyX,iBAAA,CAAoB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ICnCzDpd,4DAAA,cAAuD;IAAAA,oDAAA,GAAW;IAAAA,0DAAA,EAAM;;;;IAAjBA,uDAAA,EAAW;IAAXA,+DAAA,CAAAie,QAAA,CAAW;;;;;IAGlEje,4DAAA,cAAsE;IAEpEA,uDADA,cAAwF,cACR;IAChFA,4DAAA,cAAyB;IAAAA,oDAAA,GAAiB;IAC5CA,0DAD4C,EAAM,EAC5C;;;;;IAH0BA,uDAAA,EAAmD;IAAnDA,yDAAA,WAAAe,MAAA,CAAAmd,YAAA,CAAAC,QAAA,CAAAC,YAAA,OAAmD;IACvDpe,uDAAA,EAA+C;IAA/CA,yDAAA,WAAAe,MAAA,CAAAmd,YAAA,CAAAC,QAAA,CAAAE,QAAA,OAA+C;IAChDre,uDAAA,GAAiB;IAAjBA,+DAAA,CAAAme,QAAA,CAAA5L,KAAA,CAAiB;;;;;IAsC5CvS,4DAAA,eAA4D;IAAAA,oDAAA,GAA0B;IAAAA,0DAAA,EAAO;;;;IAAjCA,uDAAA,EAA0B;IAA1BA,gEAAA,YAAAse,cAAA,CAAAjF,MAAA,KAA0B;;;;;IANxFrZ,4DAFJ,cAA6E,cAClB,eAC7C;IAAAA,oDAAA,GAA0C;IACtDA,0DADsD,EAAW,EAC3D;IAEJA,4DADF,cAA8B,cACS;IAAAA,oDAAA,GAA6B;IAAAA,0DAAA,EAAM;IAEtEA,4DADF,cAA8B,eACG;IAAAA,oDAAA,GAA4C;;IAAAA,0DAAA,EAAO;IAClFA,wDAAA,KAAAue,wCAAA,mBAA4D;IAEhEve,0DADE,EAAM,EACF;IACNA,4DAAA,eAA2D;IACzDA,oDAAA,IACF;IACFA,0DADE,EAAM,EACF;;;;;IAb0BA,uDAAA,EAA0B;IAA1BA,wDAAA,CAAAse,cAAA,CAAApO,IAAA,CAA0B;IAC5ClQ,uDAAA,GAA0C;IAA1CA,+DAAA,CAAAe,MAAA,CAAAyd,kBAAA,CAAAF,cAAA,CAAApO,IAAA,EAA0C;IAGflQ,uDAAA,GAA6B;IAA7BA,+DAAA,CAAAse,cAAA,CAAAzc,WAAA,CAA6B;IAEjC7B,uDAAA,GAA4C;IAA5CA,+DAAA,CAAAA,yDAAA,QAAAse,cAAA,CAAAzO,IAAA,kBAA4C;IACzC7P,uDAAA,GAAwB;IAAxBA,wDAAA,SAAAse,cAAA,CAAAjF,MAAA,CAAwB;IAG9BrZ,uDAAA,EAA0B;IAA1BA,wDAAA,CAAAse,cAAA,CAAApO,IAAA,CAA0B;IACxDlQ,uDAAA,EACF;IADEA,gEAAA,MAAAe,MAAA,CAAA0d,YAAA,CAAAH,cAAA,CAAA9C,MAAA,OACF;;;AAucR,MAAOrU,gBAAgB;EAphB7BnE,YAAA;IAqhBE,KAAA0b,cAAc,GAAG,OAAO;IACxB,KAAAC,YAAY,GAAG,MAAM;IACrB,KAAAC,aAAa,GAAG,KAAK;IACrB,KAAAC,SAAS,GAAG,KAAK;IAEjB,KAAAC,WAAW,GAAG,CAAC,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,CAAC;IAE5C,KAAA9C,SAAS,GAAG,CACV;MAAEzJ,KAAK,EAAE,GAAG;MAAE6L,YAAY,EAAE,EAAE;MAAEC,QAAQ,EAAE;IAAE,CAAE,EAC9C;MAAE9L,KAAK,EAAE,GAAG;MAAE6L,YAAY,EAAE,EAAE;MAAEC,QAAQ,EAAE;IAAE,CAAE,EAC9C;MAAE9L,KAAK,EAAE,GAAG;MAAE6L,YAAY,EAAE,EAAE;MAAEC,QAAQ,EAAE;IAAE,CAAE,EAC9C;MAAE9L,KAAK,EAAE,GAAG;MAAE6L,YAAY,EAAE,EAAE;MAAEC,QAAQ,EAAE;IAAE,CAAE,EAC9C;MAAE9L,KAAK,EAAE,GAAG;MAAE6L,YAAY,EAAE,EAAE;MAAEC,QAAQ,EAAE;IAAE,CAAE,EAC9C;MAAE9L,KAAK,EAAE,GAAG;MAAE6L,YAAY,EAAE,EAAE;MAAEC,QAAQ,EAAE;IAAE,CAAE,EAC9C;MAAE9L,KAAK,EAAE,GAAG;MAAE6L,YAAY,EAAE,EAAE;MAAEC,QAAQ,EAAE;IAAE,CAAE,EAC9C;MAAE9L,KAAK,EAAE,GAAG;MAAE6L,YAAY,EAAE,EAAE;MAAEC,QAAQ,EAAE;IAAE,CAAE,EAC9C;MAAE9L,KAAK,EAAE,GAAG;MAAE6L,YAAY,EAAE,EAAE;MAAEC,QAAQ,EAAE;IAAE,CAAE,CAC/C;IAED,KAAAnC,kBAAkB,GAAkB,CAClC;MACE1Y,EAAE,EAAE,GAAG;MACPgY,MAAM,EAAE,KAAK;MACbtL,IAAI,EAAE,cAAc;MACpBrO,WAAW,EAAE,cAAc;MAC3BgO,IAAI,EAAE,IAAIpM,IAAI,CAAC,IAAI,EAAE,CAAC,EAAE,EAAE,CAAC;MAC3B4V,MAAM,EAAE;KACT,EACD;MACE7V,EAAE,EAAE,GAAG;MACPgY,MAAM,EAAE,IAAI;MACZtL,IAAI,EAAE,UAAU;MAChBrO,WAAW,EAAE,UAAU;MACvBgO,IAAI,EAAE,IAAIpM,IAAI,CAAC,IAAI,EAAE,CAAC,EAAE,EAAE,CAAC;MAC3B4V,MAAM,EAAE;KACT,EACD;MACE7V,EAAE,EAAE,GAAG;MACPgY,MAAM,EAAE,IAAI;MACZtL,IAAI,EAAE,SAAS;MACfrO,WAAW,EAAE,iBAAiB;MAC9BgO,IAAI,EAAE,IAAIpM,IAAI,CAAC,IAAI,EAAE,CAAC,EAAE,EAAE,CAAC,CAAE;KAC9B,CACF;;EAEDE,QAAQA,CAAA;IACN;EAAA;EAGFob,YAAYA,CAACC,MAAc;IACzB,IAAI,CAACN,cAAc,GAAGM,MAAM;IAC5B;EACF;EAEAd,YAAYA,CAACrK,KAAa;IACxB,OAAQA,KAAK,GAAG,GAAG,GAAI,GAAG,CAAC,CAAC;EAC9B;EAEA2K,kBAAkBA,CAACtO,IAAY;IAC7B,QAAQA,IAAI;MACV,KAAK,cAAc;QAAE,OAAO,QAAQ;MACpC,KAAK,UAAU;QAAE,OAAO,wBAAwB;MAChD,KAAK,SAAS;QAAE,OAAO,eAAe;MACtC,KAAK,SAAS;QAAE,OAAO,SAAS;MAChC;QAAS,OAAO,cAAc;;EAElC;EAEAuO,YAAYA,CAACjD,MAAc;IACzB,MAAMyD,MAAM,GAAGzD,MAAM,IAAI,CAAC,GAAG,GAAG,GAAG,EAAE;IACrC,OAAO,GAAGyD,MAAM,IAAIzD,MAAM,CAAC0D,cAAc,EAAE,EAAE;EAC/C;EAEAC,YAAYA,CAACC,GAAW;IACtB,OAAOA,GAAG,CAACF,cAAc,EAAE;EAC7B;EAEAG,wBAAwBA,CAAA;IACtB;EAAA;;;uBA/ESlY,gBAAgB;IAAA;EAAA;;;YAAhBA,gBAAgB;MAAA/B,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAA8Z,0BAAA5Z,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UA/gBrB1F,4DAFJ,aAA+B,aACD,SACtB;UAAAA,oDAAA,cAAO;UAAAA,0DAAA,EAAK;UAChBA,4DAAA,gBAA+E;UAArCA,wDAAA,mBAAAuf,kDAAA;YAAA,OAAS5Z,GAAA,CAAA0Z,wBAAA,EAA0B;UAAA,EAAC;UAC5Erf,4DAAA,eAAU;UAAAA,oDAAA,UAAG;UAAAA,0DAAA,EAAW;UACxBA,oDAAA,wBACF;UACFA,0DADE,EAAS,EACL;UAMAA,4DAJN,aAA6B,aAEA,cACC,UACpB;UAAAA,oDAAA,uBAAe;UAAAA,0DAAA,EAAK;UAEtBA,4DADF,cAA4B,iBACqE;UAAhCA,wDAAA,mBAAAwf,mDAAA;YAAA,OAAS7Z,GAAA,CAAAoZ,YAAA,CAAa,OAAO,CAAC;UAAA,EAAC;UAAC/e,oDAAA,kBAAU;UAAAA,0DAAA,EAAS;UAClHA,4DAAA,iBAA6F;UAA/BA,wDAAA,mBAAAyf,mDAAA;YAAA,OAAS9Z,GAAA,CAAAoZ,YAAA,CAAa,MAAM,CAAC;UAAA,EAAC;UAAC/e,oDAAA,iBAAS;UAE1GA,0DAF0G,EAAS,EAC3G,EACF;UAMAA,4DAJN,cAA6B,cACD,eAEA,eACI;UACxBA,wDAAA,KAAA0f,gCAAA,kBAAuD;UACzD1f,0DAAA,EAAM;UACNA,4DAAA,eAA4B;UAC1BA,wDAAA,KAAA2f,gCAAA,kBAAsE;UAM1E3f,0DADE,EAAM,EACF;UAIJA,4DADF,eAA0B,eACC;UACvBA,uDAAA,eAA6C;UAC7CA,4DAAA,YAAM;UAAAA,oDAAA,oBAAY;UACpBA,0DADoB,EAAO,EACrB;UACNA,4DAAA,eAAyB;UACvBA,uDAAA,eAAyC;UACzCA,4DAAA,YAAM;UAAAA,oDAAA,gBAAQ;UAKxBA,0DALwB,EAAO,EACjB,EACF,EACF,EACF,EACF;UAKFA,4DAFJ,eAAkC,eACJ,UACtB;UAAAA,oDAAA,2BAAmB;UAAAA,0DAAA,EAAK;UAE1BA,4DADF,kBAAwB,gBACZ;UAAAA,oDAAA,iBAAS;UAEvBA,0DAFuB,EAAW,EACvB,EACL;UAENA,4DAAA,eAA+B;UAC7BA,wDAAA,KAAA4f,gCAAA,oBAA6E;UAe/E5f,0DAAA,EAAM;UAENA,4DAAA,kBAAwC;UAAAA,oDAAA,6BAAqB;UAC/DA,0DAD+D,EAAS,EAClE;UAMAA,4DAHN,eAA2B,eACC,eACO,gBACnB;UAAAA,oDAAA,mBAAW;UACvBA,0DADuB,EAAW,EAC5B;UAEJA,4DADF,eAA0B,eACA;UAAAA,oDAAA,qBAAa;UAAAA,0DAAA,EAAM;UAC3CA,4DAAA,eAAwB;UAAAA,oDAAA,IAAiC;UAAAA,0DAAA,EAAM;UAC/DA,4DAAA,eAAkC;UAAAA,oDAAA,8BAAsB;UAE5DA,0DAF4D,EAAM,EAC1D,EACF;UAIFA,4DAFJ,eAA0B,eACQ,gBACpB;UAAAA,oDAAA,qBAAa;UACzBA,0DADyB,EAAW,EAC9B;UAEJA,4DADF,eAA0B,eACA;UAAAA,oDAAA,sBAAc;UAAAA,0DAAA,EAAM;UAC5CA,4DAAA,eAAwB;UAAAA,oDAAA,IAAkC;UAAAA,0DAAA,EAAM;UAChEA,4DAAA,eAAkC;UAAAA,oDAAA,6BAAqB;UAE3DA,0DAF2D,EAAM,EACzD,EACF;UAIFA,4DAFJ,eAA0B,eACM,gBAClB;UAAAA,oDAAA,uBAAe;UAC3BA,0DAD2B,EAAW,EAChC;UAEJA,4DADF,eAA0B,eACA;UAAAA,oDAAA,kBAAU;UAAAA,0DAAA,EAAM;UACxCA,4DAAA,eAAwB;UAAAA,oDAAA,IAA8B;UAAAA,0DAAA,EAAM;UAC5DA,4DAAA,eAAkC;UAAAA,oDAAA,8BAAsB;UAKlEA,0DALkE,EAAM,EAC1D,EACF,EACF,EACF,EACF;;;UAtGuBA,uDAAA,IAA2C;UAA3CA,yDAAA,WAAA2F,GAAA,CAAA+Y,cAAA,aAA2C;UAC3C1e,uDAAA,GAA0C;UAA1CA,yDAAA,WAAA2F,GAAA,CAAA+Y,cAAA,YAA0C;UASlB1e,uDAAA,GAAc;UAAdA,wDAAA,YAAA2F,GAAA,CAAAmZ,WAAA,CAAc;UAGZ9e,uDAAA,GAAc;UAAdA,wDAAA,YAAA2F,GAAA,CAAAqW,SAAA,CAAc;UAiCPhc,uDAAA,IAAqB;UAArBA,wDAAA,YAAA2F,GAAA,CAAAuW,kBAAA,CAAqB;UA4BjDlc,uDAAA,IAAiC;UAAjCA,gEAAA,WAAA2F,GAAA,CAAAwZ,YAAA,CAAAxZ,GAAA,CAAAgZ,YAAA,MAAiC;UAWjC3e,uDAAA,IAAkC;UAAlCA,gEAAA,WAAA2F,GAAA,CAAAwZ,YAAA,CAAAxZ,GAAA,CAAAiZ,aAAA,MAAkC;UAWlC5e,uDAAA,IAA8B;UAA9BA,gEAAA,WAAA2F,GAAA,CAAAwZ,YAAA,CAAAxZ,GAAA,CAAAkZ,SAAA,MAA8B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ICzDlD7e,4DAAA,mBAAgF;IAC9EA,oDAAA,iBACF;IAAAA,0DAAA,EAAW;;;;;IACXA,4DAAA,mBAA6E;IAC3EA,oDAAA,cACF;IAAAA,0DAAA,EAAW;;;;;IACXA,4DAAA,mBAA6E;IAC3EA,oDAAA,cACF;IAAAA,0DAAA,EAAW;;;;;IAhBfA,4DAFJ,mBAAmE,sBAChD,qBACC;IAAAA,oDAAA,GAAuB;IAAAA,0DAAA,EAAiB;IACxDA,4DAAA,wBAAmB;IAAAA,oDAAA,GAA4C;IACjEA,0DADiE,EAAoB,EACnE;IAIdA,4DAFJ,uBAAkB,cACS,SACnB;IAAAA,oDAAA,wBAAiB;IAAAA,0DAAA,EAAK;IAC1BA,4DAAA,eAA8B;IAO5BA,wDANA,KAAA6f,0DAAA,uBAAgF,KAAAC,0DAAA,uBAGH,KAAAC,0DAAA,uBAGA;IAKnF/f,0DAFI,EAAM,EACF,EACW;IAGjBA,4DADF,wBAAkB,kBACmB;IAAAA,oDAAA,wBAAgB;IAAAA,0DAAA,EAAS;IAC5DA,4DAAA,kBAAmB;IAAAA,oDAAA,oBAAY;IAEnCA,0DAFmC,EAAS,EACvB,EACV;;;;;IAzBSA,uDAAA,GAAuB;IAAvBA,+DAAA,CAAAggB,SAAA,CAAAlL,UAAA,CAAuB;IACpB9U,uDAAA,GAA4C;IAA5CA,gEAAA,eAAAmJ,MAAA,CAAA8W,UAAA,CAAAD,SAAA,CAAAzK,SAAA,MAA4C;IAOhDvV,uDAAA,GAA0C;IAA1CA,wDAAA,SAAAggB,SAAA,CAAAE,WAAA,CAAAC,iBAAA,CAA0C;IAG1CngB,uDAAA,EAAuC;IAAvCA,wDAAA,SAAAggB,SAAA,CAAAE,WAAA,CAAAE,cAAA,CAAuC;IAGvCpgB,uDAAA,EAAuC;IAAvCA,wDAAA,SAAAggB,SAAA,CAAAE,WAAA,CAAAG,cAAA,CAAuC;;;;;IAnB5DrgB,4DADF,aAAqE,SAC/D;IAAAA,oDAAA,qBAAc;IAAAA,0DAAA,EAAK;IAEvBA,4DAAA,cAA0B;IACxBA,wDAAA,IAAAsgB,8CAAA,wBAAmE;IA6BvEtgB,0DADE,EAAM,EACF;;;;IA7B2BA,uDAAA,GAAgB;IAAhBA,wDAAA,YAAAmJ,MAAA,CAAAiG,aAAA,CAAgB;;;AAiGjD,MAAOpI,mBAAmB;EAO9BhE,YACU+G,MAAc,EACdC,eAAgC;IADhC,KAAAD,MAAM,GAANA,MAAM;IACN,KAAAC,eAAe,GAAfA,eAAe;IARzB,KAAAoF,aAAa,GAA0B,EAAE;IACzC,KAAAmR,aAAa,GAAG,EAAE;IAClB,KAAAC,WAAW,GAAyB,IAAI;IACxC,KAAAC,SAAS,GAAG,KAAK;IACjB,KAAAnP,SAAS,GAAG,KAAK;EAKb;EAEJ3N,QAAQA,CAAA;IACN,IAAI,CAAC+N,iBAAiB,EAAE;EAC1B;EAEMA,iBAAiBA,CAAA;IAAA,IAAA7G,KAAA;IAAA,OAAAC,6KAAA;MACrBD,KAAI,CAACyG,SAAS,GAAG,IAAI;MACrB,MAAM9H,WAAW,GAAGqB,KAAI,CAACb,eAAe,CAAC8H,cAAc,EAAE;MAEzD,IAAItI,WAAW,EAAE;QACf,IAAI;UACFqB,KAAI,CAACuE,aAAa,SAASvE,KAAI,CAACb,eAAe,CAAC0W,gBAAgB,CAAClX,WAAW,CAACwI,GAAG,CAAC;SAClF,CAAC,OAAO3G,KAAK,EAAE;UACdtG,OAAO,CAACsG,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;;;MAIzDR,KAAI,CAACyG,SAAS,GAAG,KAAK;IAAC;EACzB;EAEAqP,WAAWA,CAACzb,KAAU;IACpB,MAAM2O,KAAK,GAAG3O,KAAK,CAAC0b,MAAM,CAAC/M,KAAK,CAACgN,WAAW,EAAE;IAC9C,IAAI,CAACN,aAAa,GAAG1M,KAAK;IAE1B,IAAI,IAAI,CAAC2M,WAAW,EAAE;MACpB,IAAI,CAACA,WAAW,GAAG,IAAI;;EAE3B;EAEAM,WAAWA,CAAA;IACT,OAAO,IAAI,CAACP,aAAa,CAAC5d,MAAM,KAAK,CAAC;EACxC;EAEMoe,YAAYA,CAAA;IAAA,IAAAlP,MAAA;IAAA,OAAA/G,6KAAA;MAChB,IAAI,CAAC+G,MAAI,CAACiP,WAAW,EAAE,EAAE;MAEzB,MAAMtX,WAAW,GAAGqI,MAAI,CAAC7H,eAAe,CAAC8H,cAAc,EAAE;MACzD,IAAI,CAACtI,WAAW,EAAE;MAElBqI,MAAI,CAAC4O,SAAS,GAAG,IAAI;MAErB,IAAI;QACF,MAAMO,QAAQ,SAASnP,MAAI,CAAC7H,eAAe,CAACiX,qBAAqB,CAACpP,MAAI,CAAC0O,aAAa,CAAC;QAErF,IAAI,CAACS,QAAQ,EAAE;UACbzN,KAAK,CAAC,yBAAyB,CAAC;UAChC1B,MAAI,CAAC4O,SAAS,GAAG,KAAK;UACtB;;QAGF,MAAMS,aAAa,GAAGrP,MAAI,CAACzC,aAAa,CAAC+R,IAAI,CAACC,IAAI,IAChDA,IAAI,CAACvM,QAAQ,KAAKmM,QAAQ,CAACnM,QAAQ,CACpC;QAED,IAAIqM,aAAa,EAAE;UACjB3N,KAAK,CAAC,uCAAuC,CAAC;UAC9C1B,MAAI,CAAC4O,SAAS,GAAG,KAAK;UACtB;;QAGF5O,MAAI,CAAC2O,WAAW,GAAGQ,QAAQ;QAC3BnP,MAAI,CAAC4O,SAAS,GAAG,KAAK;OAEvB,CAAC,OAAOpV,KAAK,EAAE;QACdtG,OAAO,CAACsG,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;QAC9CkI,KAAK,CAAC,uBAAuB,CAAC;QAC9B1B,MAAI,CAAC4O,SAAS,GAAG,KAAK;;IACvB;EACH;EAEMY,WAAWA,CAAA;IAAA,IAAAnP,MAAA;IAAA,OAAApH,6KAAA;MACf,IAAI,CAACoH,MAAI,CAACsO,WAAW,EAAE;MAEvB,MAAMhX,WAAW,GAAG0I,MAAI,CAAClI,eAAe,CAAC8H,cAAc,EAAE;MACzD,IAAI,CAACtI,WAAW,EAAE;MAElB0I,MAAI,CAACuO,SAAS,GAAG,IAAI;MAErB,IAAI;QACF,MAAMW,IAAI,SAASlP,MAAI,CAAClI,eAAe,CAACsX,gBAAgB,CACtDpP,MAAI,CAACqO,aAAa,EAClB/W,WAAW,CAACwI,GAAG,CAChB;QAEDE,MAAI,CAAC9C,aAAa,CAACmS,OAAO,CAACH,IAAI,CAAC;QAChClP,MAAI,CAACqO,aAAa,GAAG,EAAE;QACvBrO,MAAI,CAACsO,WAAW,GAAG,IAAI;QAEvBjN,KAAK,CAAC,0BAA0B6N,IAAI,CAACtM,UAAU,EAAE,CAAC;OAEnD,CAAC,OAAOzJ,KAAK,EAAE;QACdtG,OAAO,CAACsG,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;QAChDkI,KAAK,CAAC,0BAA0B,CAAC;OAClC,SAAS;QACRrB,MAAI,CAACuO,SAAS,GAAG,KAAK;;IACvB;EACH;EAEAe,aAAaA,CAAA;IACX,IAAI,CAAChB,WAAW,GAAG,IAAI;IACvB,IAAI,CAACD,aAAa,GAAG,EAAE;EACzB;EAEAkB,cAAcA,CAAC1N,MAA2B;IACxC7I,YAAY,CAACwW,OAAO,CAAC,uBAAuB,EAAEC,IAAI,CAACC,SAAS,CAAC;MAC3D/M,QAAQ,EAAEd,MAAM,CAACc,QAAQ;MACzBC,UAAU,EAAEf,MAAM,CAACe,UAAU;MAC7BoL,WAAW,EAAEnM,MAAM,CAACmM;KACrB,CAAC,CAAC;IAEH,IAAI,CAACnW,MAAM,CAACqB,QAAQ,CAAC,CAAC,YAAY,CAAC,CAAC;EACtC;EAEA6U,UAAUA,CAACpQ,IAAS;IAClB,OAAO,IAAIpM,IAAI,CAACoM,IAAI,CAAC,CAACmK,kBAAkB,EAAE;EAC5C;;;uBA9HWhT,mBAAmB,EAAAhH,+DAAA,CAAAsI,mDAAA,GAAAtI,+DAAA,CAAA2L,uEAAA;IAAA;EAAA;;;YAAnB3E,mBAAmB;MAAA5B,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAqc,6BAAAnc,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UA9IxB1F,4DAFJ,aAAmC,aACL,SACtB;UAAAA,oDAAA,qBAAc;UAAAA,0DAAA,EAAK;UACvBA,4DAAA,QAAG;UAAAA,oDAAA,uDAAgD;UACrDA,0DADqD,EAAI,EACnD;UAKAA,4DAHN,aAA0B,kBACI,sBACT,qBACC;UAAAA,oDAAA,sBAAc;UAAAA,0DAAA,EAAiB;UAC/CA,4DAAA,yBAAmB;UAAAA,oDAAA,oCAA4B;UACjDA,0DADiD,EAAoB,EACnD;UAIdA,4DAFJ,wBAAkB,yBACwC,iBAC3C;UAAAA,oDAAA,kBAAU;UAAAA,0DAAA,EAAY;UACjCA,4DAAA,gBAK0B;UAJnBA,8DAAA,2BAAA8hB,6DAAAvf,MAAA;YAAAvC,gEAAA,CAAA2F,GAAA,CAAA4a,aAAA,EAAAhe,MAAA,MAAAoD,GAAA,CAAA4a,aAAA,GAAAhe,MAAA;YAAA,OAAAA,MAAA;UAAA,EAA2B;UAG3BvC,wDAAA,mBAAA+hB,qDAAAxf,MAAA;YAAA,OAASoD,GAAA,CAAAgb,WAAA,CAAApe,MAAA,CAAmB;UAAA,EAAC;UAEtCvC,0DANE,EAK0B,EACX;UAGfA,4DADF,cAAuB,gBACX;UAAAA,oDAAA,YAAI;UAAAA,0DAAA,EAAW;UACzBA,4DAAA,YAAM;UAAAA,oDAAA,6DAAqD;UAE/DA,0DAF+D,EAAO,EAC9D,EACW;UAGjBA,4DADF,wBAAkB,iBAKW;UADnBA,wDAAA,mBAAAgiB,sDAAA;YAAA,OAASrc,GAAA,CAAAob,YAAA,EAAc;UAAA,EAAC;UAE9B/gB,4DAAA,gBAAU;UAAAA,oDAAA,YAAI;UAAAA,0DAAA,EAAW;UACzBA,oDAAA,wBACF;UAGNA,0DAHM,EAAS,EACQ,EACV,EACP;UAENA,wDAAA,KAAAiiB,mCAAA,iBAAqE;UAkCvEjiB,0DAAA,EAAM;;;UA5DWA,uDAAA,IAA2B;UAA3BA,8DAAA,YAAA2F,GAAA,CAAA4a,aAAA,CAA2B;UAgB5BvgB,uDAAA,GAA2B;UAA3BA,wDAAA,cAAA2F,GAAA,CAAAmb,WAAA,GAA2B;UAUJ9gB,uDAAA,GAA8B;UAA9BA,wDAAA,SAAA2F,GAAA,CAAAyJ,aAAA,CAAAzM,MAAA,KAA8B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ICd3D3C,4DAAA,WAAyB;IAAAA,oDAAA,cAAO;IAAAA,0DAAA,EAAO;;;;;IACvCA,4DAAA,WAAwB;IAAAA,oDAAA,oBAAa;IAAAA,0DAAA,EAAO;;;;;IAG9CA,4DAAA,cAAgD;IAC9CA,oDAAA,GACF;IAAAA,0DAAA,EAAM;;;;IADJA,uDAAA,EACF;IADEA,gEAAA,MAAAkiB,MAAA,CAAAC,YAAA,MACF;;;AAwEN,MAAOpb,cAAc;EAOzB/D,YACU+G,MAAc,EACdC,eAAgC;IADhC,KAAAD,MAAM,GAANA,MAAM;IACN,KAAAC,eAAe,GAAfA,eAAe;IARzB,KAAAyO,KAAK,GAAG,EAAE;IACV,KAAA2J,QAAQ,GAAG,EAAE;IACb,KAAAC,YAAY,GAAG,IAAI;IACnB,KAAA/Q,SAAS,GAAG,KAAK;IACjB,KAAA6Q,YAAY,GAAG,EAAE;EAKb;EAEEG,KAAKA,CAAA;IAAA,IAAAzX,KAAA;IAAA,OAAAC,6KAAA;MACT,IAAI,CAACD,KAAI,CAAC4N,KAAK,IAAI,CAAC5N,KAAI,CAACuX,QAAQ,EAAE;QACjCvX,KAAI,CAACsX,YAAY,GAAG,sCAAsC;QAC1D;;MAGFtX,KAAI,CAACyG,SAAS,GAAG,IAAI;MACrBzG,KAAI,CAACsX,YAAY,GAAG,EAAE;MAEtB,IAAI;QACFpd,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAE6F,KAAI,CAAC4N,KAAK,CAAC;QAEhD;QACA,MAAMtW,IAAI,SAAS0I,KAAI,CAACb,eAAe,CAACuY,MAAM,CAAC1X,KAAI,CAAC4N,KAAK,EAAE5N,KAAI,CAACuX,QAAQ,CAAC;QACzErd,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAE7C,IAAI,CAAC;QAEtC;QACA,MAAMqgB,gBAAgB,SAAS3X,KAAI,CAACb,eAAe,CAACyY,mBAAmB,CAACtgB,IAAI,CAAC6P,GAAG,CAAC;QAEjF,IAAI,CAACwQ,gBAAgB,EAAE;UACrB,MAAM,IAAIE,KAAK,CAAC,gEAAgE,CAAC;;QAGnF3d,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEwd,gBAAgB,CAAC;QAEzD;QACA3X,KAAI,CAACd,MAAM,CAACqB,QAAQ,CAAC,CAAC,iBAAiB,CAAC,CAAC,CAACzD,IAAI,CAAC,MAAK;UAClD5C,OAAO,CAACC,GAAG,CAAC,8CAA8C,CAAC;QAC7D,CAAC,CAAC;OAEH,CAAC,OAAOqG,KAAU,EAAE;QACnBtG,OAAO,CAACsG,KAAK,CAAC,cAAc,EAAEA,KAAK,CAAC;QAEpC;QACA,IAAIA,KAAK,CAACsX,IAAI,KAAK,qBAAqB,EAAE;UACxC9X,KAAI,CAACsX,YAAY,GAAG,0CAA0C;SAC/D,MAAM,IAAI9W,KAAK,CAACsX,IAAI,KAAK,qBAAqB,EAAE;UAC/C9X,KAAI,CAACsX,YAAY,GAAG,oBAAoB;SACzC,MAAM,IAAI9W,KAAK,CAACsX,IAAI,KAAK,oBAAoB,EAAE;UAC9C9X,KAAI,CAACsX,YAAY,GAAG,uBAAuB;SAC5C,MAAM,IAAI9W,KAAK,CAACsX,IAAI,KAAK,wBAAwB,EAAE;UAClD9X,KAAI,CAACsX,YAAY,GAAG,kDAAkD;SACvE,MAAM,IAAI9W,KAAK,CAACqI,OAAO,EAAE;UACxB7I,KAAI,CAACsX,YAAY,GAAG9W,KAAK,CAACqI,OAAO;SAClC,MAAM;UACL7I,KAAI,CAACsX,YAAY,GAAG,gCAAgC;;OAEvD,SAAS;QACRtX,KAAI,CAACyG,SAAS,GAAG,KAAK;;IACvB;EACH;EAEMsR,cAAcA,CAAA;IAAA,IAAA/Q,MAAA;IAAA,OAAA/G,6KAAA;MAClB,IAAI,CAAC+G,MAAI,CAAC4G,KAAK,EAAE;QACf5G,MAAI,CAACsQ,YAAY,GAAG,uCAAuC;QAC3D;;MAGF,IAAI;QACF,MAAMtQ,MAAI,CAAC7H,eAAe,CAAC6Y,aAAa,CAAChR,MAAI,CAAC4G,KAAK,CAAC;QACpD5G,MAAI,CAACsQ,YAAY,GAAG,EAAE;QACtB5O,KAAK,CAAC,8CAA8C,CAAC;OACtD,CAAC,OAAOlI,KAAU,EAAE;QACnBtG,OAAO,CAACsG,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;QAC7CwG,MAAI,CAACsQ,YAAY,GAAG,qCAAqC;;IAC1D;EACH;EAEMW,iBAAiBA,CAAA;IAAA,IAAA5Q,MAAA;IAAA,OAAApH,6KAAA;MACrBoH,MAAI,CAACZ,SAAS,GAAG,IAAI;MACrBY,MAAI,CAACiQ,YAAY,GAAG,EAAE;MAEtB,IAAI;QACFpd,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC;QAEjD;QACA,MAAM+d,cAAc,SAAS7Q,MAAI,CAAClI,eAAe,CAACgZ,MAAM,CACtD,uBAAuB,EACvB,cAAc,EACd;UACEvZ,IAAI,EAAE,gBAAgB;UACtB0K,IAAI,EAAE;SACP,CACF;QAEDpP,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAE+d,cAAc,CAAC;QAEjD;QACA,MAAMP,gBAAgB,GAAG;UACvBxQ,GAAG,EAAE+Q,cAAc,CAAC/Q,GAAG;UACvByG,KAAK,EAAE,uBAAuB;UAC9BhP,IAAI,EAAE,gBAAgB;UACtBiP,KAAK,EAAE,aAAa;UACpBvE,IAAI,EAAE,WAAoB;UAC1B/E,aAAa,EAAE,EAAE;UACjB8Q,WAAW,EAAE;YACXC,iBAAiB,EAAE,IAAI;YACvBC,cAAc,EAAE,IAAI;YACpBC,cAAc,EAAE,IAAI;YACpB4C,kBAAkB,EAAE,KAAK;YACzBC,eAAe,EAAE,IAAI;YACrBC,iBAAiB,EAAE;WACpB;UACD5N,SAAS,EAAE,IAAI9R,IAAI,EAAE;UACrB+R,SAAS,EAAE,IAAI/R,IAAI;SACpB;QAED,MAAMyO,MAAI,CAAClI,eAAe,CAACoZ,sBAAsB,CAACZ,gBAAgB,CAAC;QAEnEjP,KAAK,CAAC,kHAAkH,CAAC;QAEzH;QACArB,MAAI,CAACuG,KAAK,GAAG,uBAAuB;QACpCvG,MAAI,CAACkQ,QAAQ,GAAG,cAAc;OAE/B,CAAC,OAAO/W,KAAU,EAAE;QACnBtG,OAAO,CAACsG,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;QAEpD,IAAIA,KAAK,CAACsX,IAAI,KAAK,2BAA2B,EAAE;UAC9CzQ,MAAI,CAACiQ,YAAY,GAAG,2EAA2E;UAC/F;UACAjQ,MAAI,CAACuG,KAAK,GAAG,uBAAuB;UACpCvG,MAAI,CAACkQ,QAAQ,GAAG,cAAc;SAC/B,MAAM;UACLlQ,MAAI,CAACiQ,YAAY,GAAG,iCAAiC,IAAI9W,KAAK,CAACqI,OAAO,IAAI,eAAe,CAAC;;OAE7F,SAAS;QACRxB,MAAI,CAACZ,SAAS,GAAG,KAAK;;IACvB;EACH;;;uBA7IWvK,cAAc,EAAA/G,+DAAA,CAAAsI,mDAAA,GAAAtI,+DAAA,CAAA2L,uEAAA;IAAA;EAAA;;;YAAd5E,cAAc;MAAA3B,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAA6d,wBAAA3d,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UA5GjB1F,4DAHN,aAA6B,kBACE,sBACV,qBACC;UAAAA,oDAAA,6BAAsB;UAAAA,0DAAA,EAAiB;UACvDA,4DAAA,wBAAmB;UAAAA,oDAAA,8CAAuC;UAC5DA,0DAD4D,EAAoB,EAC9D;UAKZA,4DAHN,uBAAkB,cACS,wBACiC,iBAC3C;UAAAA,oDAAA,aAAK;UAAAA,0DAAA,EAAY;UAC5BA,4DAAA,gBAAkG;UAArEA,8DAAA,2BAAAsjB,wDAAA/gB,MAAA;YAAAvC,gEAAA,CAAA2F,GAAA,CAAA8S,KAAA,EAAAlW,MAAA,MAAAoD,GAAA,CAAA8S,KAAA,GAAAlW,MAAA;YAAA,OAAAA,MAAA;UAAA,EAAmB;UAAhDvC,0DAAA,EAAkG;UAClGA,4DAAA,mBAAoB;UAAAA,oDAAA,aAAK;UAC3BA,0DAD2B,EAAW,EACrB;UAGfA,4DADF,yBAAwD,iBAC3C;UAAAA,oDAAA,gBAAQ;UAAAA,0DAAA,EAAY;UAC/BA,4DAAA,gBAAqI;UAAzEA,8DAAA,2BAAAujB,wDAAAhhB,MAAA;YAAAvC,gEAAA,CAAA2F,GAAA,CAAAyc,QAAA,EAAA7f,MAAA,MAAAoD,GAAA,CAAAyc,QAAA,GAAA7f,MAAA;YAAA,OAAAA,MAAA;UAAA,EAAsB;UAAlFvC,0DAAA,EAAqI;UACrIA,4DAAA,iBAAuF;UAArDA,wDAAA,mBAAAwjB,iDAAA;YAAA,OAAA7d,GAAA,CAAA0c,YAAA,IAAA1c,GAAA,CAAA0c,YAAA;UAAA,EAAsC;UACtEriB,4DAAA,gBAAU;UAAAA,oDAAA,IAAkD;UAEhEA,0DAFgE,EAAW,EAChE,EACM;UAKbA,4DAFJ,cAAuB,aACd,cACG;UAAAA,oDAAA,yBAAiB;UAAAA,0DAAA,EAAS;UAAAA,uDAAA,UAAI;UACtCA,oDAAA,qCAAgC;UAAAA,uDAAA,UAAI;UACpCA,oDAAA,gCACF;UACFA,0DADE,EAAQ,EACJ;UAENA,4DAAA,iBAAmH;UAAzCA,wDAAA,mBAAAyjB,iDAAA;YAAA,OAAS9d,GAAA,CAAA2c,KAAA,EAAO;UAAA,EAAC;UAEzFtiB,wDADA,KAAA0jB,+BAAA,mBAAyB,KAAAC,+BAAA,mBACD;UAC1B3jB,0DAAA,EAAS;UAETA,wDAAA,KAAA4jB,8BAAA,kBAAgD;UAIpD5jB,0DADE,EAAO,EACU;UAGjBA,4DADF,wBAAkB,kBACoE;UAAlDA,wDAAA,mBAAA6jB,iDAAA;YAAA,OAASle,GAAA,CAAAid,cAAA,EAAgB;UAAA,EAAC;UAC1D5iB,oDAAA,0BACF;UAAAA,0DAAA,EAAS;UACTA,4DAAA,kBAAwF;UAArDA,wDAAA,mBAAA8jB,iDAAA;YAAA,OAASne,GAAA,CAAAmd,iBAAA,EAAmB;UAAA,EAAC;UAC9D9iB,oDAAA,6BACF;UAGNA,0DAHM,EAAS,EACQ,EACV,EACP;;;UAzCiCA,uDAAA,IAAmB;UAAnBA,8DAAA,YAAA2F,GAAA,CAAA8S,KAAA,CAAmB;UAMhCzY,uDAAA,GAA2C;UAA3CA,wDAAA,SAAA2F,GAAA,CAAA0c,YAAA,uBAA2C;UAACriB,8DAAA,YAAA2F,GAAA,CAAAyc,QAAA,CAAsB;UAEtEpiB,uDAAA,GAAkD;UAAlDA,+DAAA,CAAA2F,GAAA,CAAA0c,YAAA,mCAAkD;UAa4BriB,uDAAA,GAAsB;UAAtBA,wDAAA,aAAA2F,GAAA,CAAA2L,SAAA,CAAsB;UACzGtR,uDAAA,EAAgB;UAAhBA,wDAAA,UAAA2F,GAAA,CAAA2L,SAAA,CAAgB;UAChBtR,uDAAA,EAAe;UAAfA,wDAAA,SAAA2F,GAAA,CAAA2L,SAAA,CAAe;UAGlBtR,uDAAA,EAAkB;UAAlBA,wDAAA,SAAA2F,GAAA,CAAAwc,YAAA,CAAkB;UAOmCniB,uDAAA,GAAsB;UAAtBA,wDAAA,aAAA2F,GAAA,CAAA2L,SAAA,CAAsB;UAGlBtR,uDAAA,GAAsB;UAAtBA,wDAAA,aAAA2F,GAAA,CAAA2L,SAAA,CAAsB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACrDpD;AAStB;AAgBK;AACqC;AACJ;;AAyFvD,MAAOxF,eAAe;EAM1B9I,YAAA;IALQ,KAAA6hB,GAAG,GAAGhX,2DAAa,CAACU,kEAAW,CAACG,QAAQ,CAAC;IACzC,KAAAoW,IAAI,GAAS9W,sDAAO,CAAC,IAAI,CAAC6W,GAAG,CAAC;IAC9B,KAAAE,SAAS,GAAc7W,gEAAY,CAAC,IAAI,CAAC2W,GAAG,CAAC;IAC7C,KAAAG,SAAS,GAAG5W,gEAAY,CAAC,IAAI,CAACyW,GAAG,CAAC;EAE1B;EAEhB;EACMtC,MAAMA,CAAC9J,KAAa,EAAE2J,QAAgB;IAAA,IAAAvX,KAAA;IAAA,OAAAC,6KAAA;MAC1C,MAAMiY,cAAc,SAASgB,yEAA0B,CAAClZ,KAAI,CAACia,IAAI,EAAErM,KAAK,EAAE2J,QAAQ,CAAC;MACnF,OAAOW,cAAc,CAAC5gB,IAAI;IAAC;EAC7B;EAEM6gB,MAAMA,CAACvK,KAAa,EAAE2J,QAAgB,EAAE6C,QAAa;IAAA,IAAApT,MAAA;IAAA,OAAA/G,6KAAA;MACzD,MAAMiY,cAAc,SAASiB,6EAA8B,CAACnS,MAAI,CAACiT,IAAI,EAAErM,KAAK,EAAE2J,QAAQ,CAAC;MACvF,OAAOW,cAAc,CAAC5gB,IAAI;IAAC;EAC7B;EAEM8I,OAAOA,CAAA;IAAA,IAAAiH,MAAA;IAAA,OAAApH,6KAAA;MACX,MAAMG,sDAAO,CAACiH,MAAI,CAAC4S,IAAI,CAAC;IAAC;EAC3B;EAEMjC,aAAaA,CAACpK,KAAa;IAAA,IAAAnF,MAAA;IAAA,OAAAxI,6KAAA;MAC/B,MAAMmZ,qEAAsB,CAAC3Q,MAAI,CAACwR,IAAI,EAAErM,KAAK,CAAC;IAAC;EACjD;EAEA3G,cAAcA,CAAA;IACZ,OAAO,IAAI,CAACgT,IAAI,CAACtb,WAAW;EAC9B;EAEA;EACM4Z,sBAAsBA,CAAC8B,OAAyB;IAAA,IAAA1Q,MAAA;IAAA,OAAA1J,6KAAA;MACpD,MAAMqa,MAAM,GAAG7H,uDAAG,CAAC9I,MAAI,CAACuQ,SAAS,EAAE,aAAa,EAAEG,OAAO,CAAClT,GAAG,CAAC;MAC9D,MAAMkS,0DAAM,CAACiB,MAAM,EAAED,OAAO,CAAC;MAC7B,MAAM1Q,MAAI,CAAC4Q,WAAW,CAACF,OAAO,CAAClT,GAAG,EAAE,WAAW,EAAEkT,OAAO,CAACzb,IAAI,EAAE,QAAQ,EAAE,mBAAmB,EAAEyb,OAAO,CAAClT,GAAG,CAAC;IAAC;EAC7G;EAEMyQ,mBAAmBA,CAACzQ,GAAW;IAAA,IAAA6D,MAAA;IAAA,OAAA/K,6KAAA;MACnC,MAAMqa,MAAM,GAAG7H,uDAAG,CAACzH,MAAI,CAACkP,SAAS,EAAE,aAAa,EAAE/S,GAAG,CAAC;MACtD,MAAMqT,OAAO,SAASlB,0DAAM,CAACgB,MAAM,CAAC;MAEpC,IAAIE,OAAO,CAACC,MAAM,EAAE,EAAE;QACpB,OAAOD,OAAO,CAAChR,IAAI,EAAsB;;MAE3C,OAAO,IAAI;IAAC;EACd;EAEMkR,sBAAsBA,CAACvT,GAAW,EAAEgE,OAAkC;IAAA,IAAAD,MAAA;IAAA,OAAAjL,6KAAA;MAC1E,MAAMqa,MAAM,GAAG7H,uDAAG,CAACvH,MAAI,CAACgP,SAAS,EAAE,aAAa,EAAE/S,GAAG,CAAC;MACtD,MAAMoS,6DAAS,CAACe,MAAM,EAAE;QAAE,GAAGnP,OAAO;QAAER,SAAS,EAAE,IAAI/R,IAAI;MAAE,CAAE,CAAC;MAE9D,MAAM+hB,SAAS,SAASzP,MAAI,CAAC0M,mBAAmB,CAACzQ,GAAG,CAAC;MACrD,IAAIwT,SAAS,EAAE;QACb,MAAMzP,MAAI,CAACqP,WAAW,CAACpT,GAAG,EAAE,WAAW,EAAEwT,SAAS,CAAC/b,IAAI,EAAE,QAAQ,EAAE,mBAAmB,EAAEuI,GAAG,EAAEgE,OAAO,CAAC;;IACtG;EACH;EAEA;EACMyP,gBAAgBA,CAACzT,GAAW;IAAA,IAAAmE,MAAA;IAAA,OAAArL,6KAAA;MAChC,MAAMqa,MAAM,GAAG7H,uDAAG,CAACnH,MAAI,CAAC4O,SAAS,EAAE,SAAS,EAAE/S,GAAG,CAAC;MAClD,MAAMqT,OAAO,SAASlB,0DAAM,CAACgB,MAAM,CAAC;MAEpC,IAAIE,OAAO,CAACC,MAAM,EAAE,EAAE;QACpB,OAAOD,OAAO,CAAChR,IAAI,EAAmB;;MAExC,OAAO,IAAI;IAAC;EACd;EAEMtC,yBAAyBA,CAAC2T,WAAmB;IAAA,IAAArP,MAAA;IAAA,OAAAvL,6KAAA;MACjD,MAAM0a,SAAS,SAASnP,MAAI,CAACoM,mBAAmB,CAACiD,WAAW,CAAC;MAC7D,IAAI,CAACF,SAAS,IAAIA,SAAS,CAACpW,aAAa,CAACzM,MAAM,KAAK,CAAC,EAAE;QACtD,OAAO,EAAE;;MAGX,MAAMgjB,OAAO,GAAoB,EAAE;MACnC,KAAK,MAAM9Q,QAAQ,IAAI2Q,SAAS,CAACpW,aAAa,EAAE;QAC9C,MAAM2E,MAAM,SAASsC,MAAI,CAACoP,gBAAgB,CAAC5Q,QAAQ,CAAC;QACpD,IAAId,MAAM,EAAE;UACV4R,OAAO,CAAC7S,IAAI,CAACiB,MAAM,CAAC;;;MAGxB,OAAO4R,OAAO;IAAC;EACjB;EAEA;EACM1E,qBAAqBA,CAAC0B,IAAY;IAAA,IAAAlM,MAAA;IAAA,OAAA3L,6KAAA;MACtC,MAAM8a,CAAC,GAAGpB,yDAAK,CACbF,8DAAU,CAAC7N,MAAI,CAACsO,SAAS,EAAE,iBAAiB,CAAC,EAC7CN,yDAAK,CAAC,MAAM,EAAE,IAAI,EAAE9B,IAAI,CAAC,EACzB8B,yDAAK,CAAC,QAAQ,EAAE,IAAI,EAAE,KAAK,CAAC,CAC7B;MAED,MAAMoB,aAAa,SAASlB,2DAAO,CAACiB,CAAC,CAAC;MAEtC,IAAIC,aAAa,CAACC,KAAK,EAAE;QACvB,OAAO,IAAI;;MAGb,MAAMxI,GAAG,GAAGuI,aAAa,CAACE,IAAI,CAAC,CAAC,CAAC;MACjC,MAAMxF,aAAa,GAAG;QAAE/c,EAAE,EAAE8Z,GAAG,CAAC9Z,EAAE;QAAE,GAAG8Z,GAAG,CAACjJ,IAAI;MAAE,CAAmB;MAEpE;MACA,MAAM2R,UAAU,GAAGzF,aAAa,CAAC0F,SAAS,YAAYxiB,IAAI,GACtD8c,aAAa,CAAC0F,SAAS,GACtB1F,aAAa,CAAC0F,SAAiB,CAACC,MAAM,EAAE;MAE7C,IAAI,IAAIziB,IAAI,EAAE,GAAGuiB,UAAU,EAAE;QAC3B,OAAO,IAAI;;MAGb,OAAOzF,aAAa;IAAC;EACvB;EAEMe,gBAAgBA,CACpBqB,IAAY,EACZ+C,WAAmB;IAAA,IAAAS,MAAA;IAAA,OAAArb,6KAAA;MAEnB,IAAI;QACF,MAAMsb,aAAa,GAAGxB,iEAAa,CAACuB,MAAI,CAACnB,SAAS,EAAE,eAAe,CAAC;QACpE,MAAM1a,MAAM,SAAS8b,aAAa,CAAC;UAAEzD;QAAI,CAAE,CAAC;QAE5C,IAAIrY,MAAM,CAAC+J,IAAI,IAAK/J,MAAM,CAAC+J,IAAY,CAACgS,OAAO,EAAE;UAC/C,OAAQ/b,MAAM,CAAC+J,IAAY,CAAC+M,IAAI;SACjC,MAAM;UACL,MAAM,IAAIsB,KAAK,CAAC,0BAA0B,CAAC;;OAE9C,CAAC,OAAOrX,KAAK,EAAE;QACdtG,OAAO,CAACsG,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;QAC7D,MAAMA,KAAK;;IACZ;EACH;EAEMqV,gBAAgBA,CAACgF,WAAmB;IAAA,IAAAY,OAAA;IAAA,OAAAxb,6KAAA;MACxC,MAAM8a,CAAC,GAAGpB,yDAAK,CACbF,8DAAU,CAACgC,OAAI,CAACvB,SAAS,EAAE,wBAAwB,CAAC,EACpDN,yDAAK,CAAC,aAAa,EAAE,IAAI,EAAEiB,WAAW,CAAC,EACvCjB,yDAAK,CAAC,QAAQ,EAAE,IAAI,EAAE,UAAU,CAAC,CAClC;MAED,MAAMoB,aAAa,SAASlB,2DAAO,CAACiB,CAAC,CAAC;MACtC,OAAOC,aAAa,CAACE,IAAI,CAACtd,GAAG,CAAC6U,GAAG,KAAK;QACpC9Z,EAAE,EAAE8Z,GAAG,CAAC9Z,EAAE;QACV,GAAG8Z,GAAG,CAACjJ,IAAI;OACY,EAAC;IAAC;EAC7B;EAEMkS,wBAAwBA,CAC5Bb,WAAmB,EACnB7Q,QAAgB,EAChB2R,UAAsC;IAAA,IAAAC,OAAA;IAAA,OAAA3b,6KAAA;MAEtC,MAAM8a,CAAC,GAAGpB,yDAAK,CACbF,8DAAU,CAACmC,OAAI,CAAC1B,SAAS,EAAE,wBAAwB,CAAC,EACpDN,yDAAK,CAAC,aAAa,EAAE,IAAI,EAAEiB,WAAW,CAAC,EACvCjB,yDAAK,CAAC,UAAU,EAAE,IAAI,EAAE5P,QAAQ,CAAC,EACjC4P,yDAAK,CAAC,QAAQ,EAAE,IAAI,EAAE,UAAU,CAAC,CAClC;MAED,MAAMoB,aAAa,SAASlB,2DAAO,CAACiB,CAAC,CAAC;MAEtC,IAAIC,aAAa,CAACC,KAAK,EAAE;QACvB,OAAO,KAAK;;MAGd,MAAM1E,IAAI,GAAGyE,aAAa,CAACE,IAAI,CAAC,CAAC,CAAC,CAAC1R,IAAI,EAAyB;MAChE,OAAO+M,IAAI,CAAClB,WAAW,CAACsG,UAAU,CAAC,IAAI,KAAK;IAAC;EAC/C;EAEME,YAAYA,CAAAlQ,EAAA,EAAAmQ,GAAA,EAAAC,GAAA,EAIO;IAAA,IAAAC,OAAA;IAAA,OAAA/b,6KAAA,YAHvBgc,UAAmB,EACnBC,QAAiB,EACjBC,MAAe,EACfC,UAAA,GAAqB,EAAE;MAEvB,IAAIrB,CAAC,GAAGpB,yDAAK,CACXF,8DAAU,CAACuC,OAAI,CAAC9B,SAAS,EAAE,YAAY,CAAC,EACxCL,2DAAO,CAAC,WAAW,EAAE,MAAM,CAAC,CAC7B;MAED,IAAIoC,UAAU,EAAE;QACdlB,CAAC,GAAGpB,yDAAK,CAACoB,CAAC,EAAEnB,yDAAK,CAAC,YAAY,EAAE,IAAI,EAAEqC,UAAU,CAAC,CAAC;;MAGrD,IAAIC,QAAQ,EAAE;QACZnB,CAAC,GAAGpB,yDAAK,CAACoB,CAAC,EAAEnB,yDAAK,CAAC,UAAU,EAAE,IAAI,EAAEsC,QAAQ,CAAC,CAAC;;MAGjD,IAAIC,MAAM,EAAE;QACVpB,CAAC,GAAGpB,yDAAK,CAACoB,CAAC,EAAEnB,yDAAK,CAAC,QAAQ,EAAE,IAAI,EAAEuC,MAAM,CAAC,CAAC;;MAG7C,MAAMnB,aAAa,SAASlB,2DAAO,CAACiB,CAAC,CAAC;MACtC,OAAOC,aAAa,CAACE,IAAI,CAACnU,KAAK,CAAC,CAAC,EAAEqV,UAAU,CAAC,CAACxe,GAAG,CAAC6U,GAAG,KAAK;QACzD9Z,EAAE,EAAE8Z,GAAG,CAAC9Z,EAAE;QACV,GAAG8Z,GAAG,CAACjJ,IAAI;OACC,EAAC;IAAC,GAAAyC,KAAA,OAAAC,SAAA;EAClB;EAEA;EACMmQ,qBAAqBA,CAACrS,QAAgB,EAAEhF,IAAa;IAAA,IAAAsX,OAAA;IAAA,OAAArc,6KAAA;MACzD,IAAI8a,CAAC,GAAGpB,yDAAK,CACXF,8DAAU,CAAC6C,OAAI,CAACpC,SAAS,EAAE,cAAc,CAAC,EAC1CN,yDAAK,CAAC,UAAU,EAAE,IAAI,EAAE5P,QAAQ,CAAC,CAClC;MAED,IAAIhF,IAAI,EAAE;QACR+V,CAAC,GAAGpB,yDAAK,CAACoB,CAAC,EAAEnB,yDAAK,CAAC,MAAM,EAAE,IAAI,EAAE5U,IAAI,CAAC,CAAC;;MAGzC+V,CAAC,GAAGpB,yDAAK,CAACoB,CAAC,EAAElB,2DAAO,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;MAEpC,MAAMmB,aAAa,SAASlB,2DAAO,CAACiB,CAAC,CAAC;MACtC,OAAOC,aAAa,CAACE,IAAI,CAACtd,GAAG,CAAC6U,GAAG,KAAK;QACpC9Z,EAAE,EAAE8Z,GAAG,CAAC9Z,EAAE;QACV,GAAG8Z,GAAG,CAACjJ,IAAI;OACW,EAAC;IAAC;EAC5B;EAEM+S,wBAAwBA,CAACC,YAA4C;IAAA,IAAAC,OAAA;IAAA,OAAAxc,6KAAA;MACzE,MAAMqa,MAAM,SAASZ,0DAAM,CAACD,8DAAU,CAACgD,OAAI,CAACvC,SAAS,EAAE,cAAc,CAAC,EAAE;QACtE,GAAGsC,YAAY;QACf9R,SAAS,EAAE,IAAI9R,IAAI,EAAE;QACrB+R,SAAS,EAAE,IAAI/R,IAAI;OACpB,CAAC;MAEF;MACA,MAAM+hB,SAAS,SAAS8B,OAAI,CAAC7E,mBAAmB,CAAC4E,YAAY,CAACpS,SAAS,CAAC;MACxE,IAAIuQ,SAAS,EAAE;QACb,MAAM8B,OAAI,CAAClC,WAAW,CACpBiC,YAAY,CAACpS,SAAS,EACtB,WAAW,EACXuQ,SAAS,CAAC/b,IAAI,EACd,QAAQ,EACR,cAAc,EACd0b,MAAM,CAAC3hB,EAAE,EACT;UAAEqR,QAAQ,EAAEwS,YAAY,CAACxS,QAAQ;UAAEhF,IAAI,EAAEwX,YAAY,CAACxX,IAAI;UAAE0X,SAAS,EAAEF,YAAY,CAACE;QAAS,CAAE,CAChG;;MAGH,OAAOpC,MAAM,CAAC3hB,EAAE;IAAC;EACnB;EAEMgkB,wBAAwBA,CAC5BC,cAAsB,EACtBzR,OAAoC,EACpC0R,SAAiB;IAAA,IAAAC,OAAA;IAAA,OAAA7c,6KAAA;MAEjB,MAAMqa,MAAM,GAAG7H,uDAAG,CAACqK,OAAI,CAAC5C,SAAS,EAAE,cAAc,EAAE0C,cAAc,CAAC;MAClE,MAAMrD,6DAAS,CAACe,MAAM,EAAE;QAAE,GAAGnP,OAAO;QAAER,SAAS,EAAE,IAAI/R,IAAI;MAAE,CAAE,CAAC;MAE9D;MACA,MAAM+hB,SAAS,SAASmC,OAAI,CAAClF,mBAAmB,CAACiF,SAAS,CAAC;MAC3D,IAAIlC,SAAS,EAAE;QACb,MAAMmC,OAAI,CAACvC,WAAW,CACpBsC,SAAS,EACT,WAAW,EACXlC,SAAS,CAAC/b,IAAI,EACd,QAAQ,EACR,cAAc,EACdge,cAAc,EACdzR,OAAO,CACR;;IACF;EACH;EAEM4R,wBAAwBA,CAACH,cAAsB,EAAEI,SAAiB;IAAA,IAAAC,OAAA;IAAA,OAAAhd,6KAAA;MACtE,MAAMqa,MAAM,GAAG7H,uDAAG,CAACwK,OAAI,CAAC/C,SAAS,EAAE,cAAc,EAAE0C,cAAc,CAAC;MAClE,MAAMpD,6DAAS,CAACc,MAAM,CAAC;MAEvB;MACA,MAAMK,SAAS,SAASsC,OAAI,CAACrF,mBAAmB,CAACoF,SAAS,CAAC;MAC3D,IAAIrC,SAAS,EAAE;QACb,MAAMsC,OAAI,CAAC1C,WAAW,CACpByC,SAAS,EACT,WAAW,EACXrC,SAAS,CAAC/b,IAAI,EACd,QAAQ,EACR,cAAc,EACdge,cAAc,CACf;;IACF;EACH;EAEA;EACM/R,iBAAiBA,CAACd,eAAgD;IAAA,IAAAmT,OAAA;IAAA,OAAAjd,6KAAA;MACtE,MAAMqa,MAAM,SAASZ,0DAAM,CAACD,8DAAU,CAACyD,OAAI,CAAChD,SAAS,EAAE,cAAc,CAAC,EAAE;QACtE,GAAGnQ,eAAe;QAClBW,SAAS,EAAE,IAAI9R,IAAI,EAAE;QACrB+R,SAAS,EAAE,IAAI/R,IAAI;OACpB,CAAC;MAEF;MACA,MAAMskB,OAAI,CAAC3C,WAAW,CACpBxQ,eAAe,CAACM,SAAS,IAAIN,eAAe,CAACC,QAAQ,EACrDD,eAAe,CAACK,SAAS,EACzBL,eAAe,CAACzE,UAAU,EAC1B,QAAQ,EACR,aAAa,EACbgV,MAAM,CAAC3hB,EAAE,EACT;QACEsR,UAAU,EAAEF,eAAe,CAACE,UAAU;QACtCjF,IAAI,EAAE+E,eAAe,CAAC/E,IAAI;QAC1BI,IAAI,EAAE2E,eAAe,CAAC3E,IAAI;QAC1BC,IAAI,EAAE0E,eAAe,CAAC1E;OACvB,CACF;MAED,OAAOiV,MAAM,CAAC3hB,EAAE;IAAC;EACnB;EAEMsS,iBAAiBA,CAACL,aAAqB,EAAEO,OAAqC;IAAA,IAAAgS,OAAA;IAAA,OAAAld,6KAAA;MAClF,MAAMqa,MAAM,GAAG7H,uDAAG,CAAC0K,OAAI,CAACjD,SAAS,EAAE,cAAc,EAAEtP,aAAa,CAAC;MACjE,MAAM2O,6DAAS,CAACe,MAAM,EAAE;QACtB,GAAGnP,OAAO;QACVR,SAAS,EAAE,IAAI/R,IAAI;OACpB,CAAC;MAEF;MACA,IAAIuS,OAAO,CAACb,cAAc,EAAE;QAC1B,MAAM6S,OAAI,CAAC5C,WAAW,CACpBpP,OAAO,CAACb,cAAc,EACtBa,OAAO,CAACZ,kBAAkB,IAAI,WAAW,EACzCY,OAAO,CAAC7F,UAAU,IAAI,SAAS,EAC/B,QAAQ,EACR,aAAa,EACbsF,aAAa,EACbO,OAAO,CACR;;IACF;EACH;EAEMjF,iBAAiBA,CAAC0E,aAAqB,EAAEoS,SAAiB,EAAEI,aAAgD;IAAA,IAAAC,OAAA;IAAA,OAAApd,6KAAA;MAChH,MAAMqa,MAAM,GAAG7H,uDAAG,CAAC4K,OAAI,CAACnD,SAAS,EAAE,cAAc,EAAEtP,aAAa,CAAC;MAEjE;MACA,MAAM4P,OAAO,SAASlB,0DAAM,CAACgB,MAAM,CAAC;MACpC,MAAMvP,WAAW,GAAGyP,OAAO,CAAChR,IAAI,EAAyB;MAEzD,MAAMgQ,6DAAS,CAACc,MAAM,CAAC;MAEvB;MACA,MAAM+C,OAAI,CAAC9C,WAAW,CACpByC,SAAS,EACTI,aAAa,EACbrS,WAAW,CAACzF,UAAU,EACtB,QAAQ,EACR,aAAa,EACbsF,aAAa,EACb;QACEX,UAAU,EAAEc,WAAW,CAACd,UAAU;QAClCjF,IAAI,EAAE+F,WAAW,CAAC/F,IAAI;QACtBI,IAAI,EAAE2F,WAAW,CAAC3F,IAAI;QACtBC,IAAI,EAAE0F,WAAW,CAAC1F;OACnB,CACF;IAAC;EACJ;EAEMkC,2BAA2BA,CAC/BsT,WAAmB,EACnB1hB,MAA0B;IAAA,IAAAmkB,OAAA;IAAA,OAAArd,6KAAA;MAE1B,MAAM0a,SAAS,SAAS2C,OAAI,CAAC1F,mBAAmB,CAACiD,WAAW,CAAC;MAC7D,IAAI,CAACF,SAAS,IAAIA,SAAS,CAACpW,aAAa,CAACzM,MAAM,KAAK,CAAC,EAAE;QACtD,OAAO,EAAE;;MAGX,IAAIijB,CAAC,GAAGpB,yDAAK,CACXF,8DAAU,CAAC6D,OAAI,CAACpD,SAAS,EAAE,cAAc,CAAC,EAC1CN,yDAAK,CAAC,UAAU,EAAE,IAAI,EAAEe,SAAS,CAACpW,aAAa,CAAC,CACjD;MAED,IAAIpL,MAAM,EAAEvC,MAAM,IAAIuC,MAAM,CAACvC,MAAM,CAACkB,MAAM,GAAG,CAAC,EAAE;QAC9CijB,CAAC,GAAGpB,yDAAK,CAACoB,CAAC,EAAEnB,yDAAK,CAAC,QAAQ,EAAE,IAAI,EAAEzgB,MAAM,CAACvC,MAAM,CAAC,CAAC;;MAGpD,IAAIuC,MAAM,EAAEokB,QAAQ,EAAE;QACpBxC,CAAC,GAAGpB,yDAAK,CAACoB,CAAC,EAAEnB,yDAAK,CAAC,MAAM,EAAE,IAAI,EAAEzgB,MAAM,CAACokB,QAAQ,CAAC,CAAC;;MAGpD,IAAIpkB,MAAM,EAAEqkB,MAAM,EAAE;QAClBzC,CAAC,GAAGpB,yDAAK,CAACoB,CAAC,EAAEnB,yDAAK,CAAC,MAAM,EAAE,IAAI,EAAEzgB,MAAM,CAACqkB,MAAM,CAAC,CAAC;;MAGlDzC,CAAC,GAAGpB,yDAAK,CAACoB,CAAC,EAAElB,2DAAO,CAAC,MAAM,EAAE,KAAK,CAAC,EAAEA,2DAAO,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;MAE5D,MAAMmB,aAAa,SAASlB,2DAAO,CAACiB,CAAC,CAAC;MACtC,IAAIrU,YAAY,GAAGsU,aAAa,CAACE,IAAI,CAACtd,GAAG,CAAC6U,GAAG,KAAK;QAChD9Z,EAAE,EAAE8Z,GAAG,CAAC9Z,EAAE;QACV,GAAG8Z,GAAG,CAACjJ,IAAI;OACY,EAAC;MAE1B;MACA,IAAIrQ,MAAM,EAAEkM,IAAI,IAAIlM,MAAM,CAACkM,IAAI,CAACvN,MAAM,GAAG,CAAC,EAAE;QAC1C4O,YAAY,GAAGA,YAAY,CAACvN,MAAM,CAACqO,GAAG,IAAIrO,MAAM,CAACkM,IAAK,CAAChM,QAAQ,CAACmO,GAAG,CAACnC,IAAI,CAAC,CAAC;;MAG5E,IAAIlM,MAAM,EAAEsR,QAAQ,KAAKgT,SAAS,EAAE;QAClC/W,YAAY,GAAGA,YAAY,CAACvN,MAAM,CAACqO,GAAG,IAAIA,GAAG,CAACiD,QAAQ,KAAKtR,MAAM,CAACsR,QAAQ,CAAC;;MAG7E,IAAItR,MAAM,EAAEiR,SAAS,IAAIjR,MAAM,CAACiR,SAAS,CAACtS,MAAM,GAAG,CAAC,EAAE;QACpD4O,YAAY,GAAGA,YAAY,CAACvN,MAAM,CAACqO,GAAG,IAAIrO,MAAM,CAACiR,SAAU,CAAC/Q,QAAQ,CAACmO,GAAG,CAAC4C,SAAS,CAAC,CAAC;;MAGtF,OAAO1D,YAAY;IAAC;EACtB;EAEMgX,uBAAuBA,CAAC1T,QAAgB,EAAEuT,QAAiB,EAAEC,MAAe;IAAA,IAAAG,OAAA;IAAA,OAAA1d,6KAAA;MAChF,IAAI8a,CAAC,GAAGpB,yDAAK,CACXF,8DAAU,CAACkE,OAAI,CAACzD,SAAS,EAAE,cAAc,CAAC,EAC1CN,yDAAK,CAAC,UAAU,EAAE,IAAI,EAAE5P,QAAQ,CAAC,CAClC;MAED,IAAIuT,QAAQ,EAAE;QACZxC,CAAC,GAAGpB,yDAAK,CAACoB,CAAC,EAAEnB,yDAAK,CAAC,MAAM,EAAE,IAAI,EAAE2D,QAAQ,CAAC,CAAC;;MAG7C,IAAIC,MAAM,EAAE;QACVzC,CAAC,GAAGpB,yDAAK,CAACoB,CAAC,EAAEnB,yDAAK,CAAC,MAAM,EAAE,IAAI,EAAE4D,MAAM,CAAC,CAAC;;MAG3CzC,CAAC,GAAGpB,yDAAK,CAACoB,CAAC,EAAElB,2DAAO,CAAC,MAAM,EAAE,KAAK,CAAC,EAAEA,2DAAO,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;MAE5D,MAAMmB,aAAa,SAASlB,2DAAO,CAACiB,CAAC,CAAC;MACtC,OAAOC,aAAa,CAACE,IAAI,CAACtd,GAAG,CAAC6U,GAAG,KAAK;QACpC9Z,EAAE,EAAE8Z,GAAG,CAAC9Z,EAAE;QACV,GAAG8Z,GAAG,CAACjJ,IAAI;OACY,EAAC;IAAC;EAC7B;EAEMoU,uBAAuBA,CAC3BhT,aAAqB,EACrBhU,MAAc,EACdimB,SAAiB,EACjBgB,MAAe;IAAA,IAAAC,OAAA;IAAA,OAAA7d,6KAAA;MAEf,MAAMkL,OAAO,GAAQ;QACnBvU,MAAM;QACN0T,cAAc,EAAEuS,SAAS;QACzBtS,kBAAkB,EAAE,WAAW;QAC/BI,SAAS,EAAE,IAAI/R,IAAI;OACpB;MAED,IAAIilB,MAAM,EAAE;QACV1S,OAAO,CAACX,OAAO,GAAGqT,MAAM;;MAG1B,MAAMvD,MAAM,GAAG7H,uDAAG,CAACqL,OAAI,CAAC5D,SAAS,EAAE,cAAc,EAAEtP,aAAa,CAAC;MACjE,MAAM2O,6DAAS,CAACe,MAAM,EAAEnP,OAAO,CAAC;MAEhC;MACA,MAAMwP,SAAS,SAASmD,OAAI,CAAClG,mBAAmB,CAACiF,SAAS,CAAC;MAC3D,IAAIlC,SAAS,EAAE;QACb,MAAMmD,OAAI,CAACvD,WAAW,CACpBsC,SAAS,EACT,WAAW,EACXlC,SAAS,CAAC/b,IAAI,EACd,QAAQ,EACR,aAAa,EACbgM,aAAa,EACb;UAAEhU,MAAM;UAAEinB;QAAM,CAAE,CACnB;;IACF;EACH;EAEA;EACME,uBAAuBA,CAACC,OAAsC;IAAA,IAAAC,OAAA;IAAA,OAAAhe,6KAAA;MAClE,MAAMqa,MAAM,SAASZ,0DAAM,CAACD,8DAAU,CAACwE,OAAI,CAAC/D,SAAS,EAAE,qBAAqB,CAAC,EAAE;QAC7E,GAAG8D,OAAO;QACVtT,SAAS,EAAE,IAAI9R,IAAI,EAAE;QACrBwiB,SAAS,EAAE,IAAIxiB,IAAI,CAACA,IAAI,CAACU,GAAG,EAAE,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;OAC3D,CAAC;MAEF;MACA,MAAM2kB,OAAI,CAAC1D,WAAW,CACpByD,OAAO,CAACE,WAAW,EACnB,WAAW,EACXF,OAAO,CAACG,eAAe,EACvB,QAAQ,EACR,oBAAoB,EACpB7D,MAAM,CAAC3hB,EAAE,EACT;QACEiS,aAAa,EAAEoT,OAAO,CAACpT,aAAa;QACpCwT,YAAY,EAAEJ,OAAO,CAACI,YAAY;QAClCC,YAAY,EAAEL,OAAO,CAACK,YAAY;QAClCR,MAAM,EAAEG,OAAO,CAACH;OACjB,CACF;MAED,OAAOvD,MAAM,CAAC3hB,EAAE;IAAC;EACnB;EAEM2lB,qBAAqBA,CAAC1T,aAAsB;IAAA,IAAA2T,OAAA;IAAA,OAAAte,6KAAA;MAChD,IAAI8a,CAAC,GAAGpB,yDAAK,CAACF,8DAAU,CAAC8E,OAAI,CAACrE,SAAS,EAAE,qBAAqB,CAAC,CAAC;MAEhE,IAAItP,aAAa,EAAE;QACjBmQ,CAAC,GAAGpB,yDAAK,CAACoB,CAAC,EAAEnB,yDAAK,CAAC,eAAe,EAAE,IAAI,EAAEhP,aAAa,CAAC,CAAC;;MAG3DmQ,CAAC,GAAGpB,yDAAK,CAACoB,CAAC,EAAElB,2DAAO,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC;MAE1C,MAAMmB,aAAa,SAASlB,2DAAO,CAACiB,CAAC,CAAC;MACtC,OAAOC,aAAa,CAACE,IAAI,CAACtd,GAAG,CAAC6U,GAAG,KAAK;QACpC9Z,EAAE,EAAE8Z,GAAG,CAAC9Z,EAAE;QACV,GAAG8Z,GAAG,CAACjJ,IAAI;OACU,EAAC;IAAC;EAC3B;EAEMgV,6BAA6BA,CACjCC,SAAiB,EACjB7nB,MAA+B,EAC/B8nB,WAAmB,EACnBC,QAAiB;IAAA,IAAAC,OAAA;IAAA,OAAA3e,6KAAA;MAEjB,MAAMkL,OAAO,GAAQ;QACnBvU,MAAM;QACNioB,WAAW,EAAE,IAAIjmB,IAAI;OACtB;MAED,IAAI+lB,QAAQ,EAAE;QACZxT,OAAO,CAAC2T,cAAc,GAAGH,QAAQ;;MAGnC,MAAMrE,MAAM,GAAG7H,uDAAG,CAACmM,OAAI,CAAC1E,SAAS,EAAE,qBAAqB,EAAEuE,SAAS,CAAC;MACpE,MAAMlF,6DAAS,CAACe,MAAM,EAAEnP,OAAO,CAAC;MAEhC;MACA,IAAIvU,MAAM,KAAK,UAAU,EAAE;QACzB,MAAMonB,OAAO,SAASY,OAAI,CAACG,oBAAoB,CAACN,SAAS,CAAC;QAC1D,IAAIT,OAAO,EAAE;UACX,MAAMY,OAAI,CAACI,+BAA+B,CAAChB,OAAO,CAAC;;;MAIvD;MACA,MAAMY,OAAI,CAACrE,WAAW,CACpBmE,WAAW,EACX,WAAW,EACX,WAAW,EACX,QAAQ,EACR,oBAAoB,EACpBD,SAAS,EACT;QAAE7nB,MAAM;QAAE+nB;MAAQ,CAAE,CACrB;IAAC;EACJ;EAEcI,oBAAoBA,CAACN,SAAiB;IAAA,IAAAQ,OAAA;IAAA,OAAAhf,6KAAA;MAClD,MAAMqa,MAAM,GAAG7H,uDAAG,CAACwM,OAAI,CAAC/E,SAAS,EAAE,qBAAqB,EAAEuE,SAAS,CAAC;MACpE,MAAMjE,OAAO,SAASlB,0DAAM,CAACgB,MAAM,CAAC;MAEpC,IAAIE,OAAO,CAACC,MAAM,EAAE,EAAE;QACpB,OAAO;UAAE9hB,EAAE,EAAE6hB,OAAO,CAAC7hB,EAAE;UAAE,GAAG6hB,OAAO,CAAChR,IAAI;QAAE,CAAuB;;MAEnE,OAAO,IAAI;IAAC;EACd;EAEcwV,+BAA+BA,CAAChB,OAA0B;IAAA,IAAAkB,OAAA;IAAA,OAAAjf,6KAAA;MACtE,MAAMkL,OAAO,GAAG;QACdiT,YAAY,EAAEJ,OAAO,CAACI,YAAY;QAClCe,YAAY,EAAEnB,OAAO,CAACmB,YAAY;QAClCna,IAAI,EAAEgZ,OAAO,CAACK,YAAY;QAC1BjZ,IAAI,EAAE4Y,OAAO,CAACoB,YAAY;QAC1BxoB,MAAM,EAAE,aAAa;QACrByoB,gBAAgB,EAAErB,OAAO,CAACH,MAAM;QAChCyB,qBAAqB,EAAEtB,OAAO,CAACE,WAAW;QAC1CqB,qBAAqB,EAAEvB,OAAO,CAACtT,SAAS;QACxCC,SAAS,EAAE,IAAI/R,IAAI;OACpB;MAED,MAAM0hB,MAAM,GAAG7H,uDAAG,CAACyM,OAAI,CAAChF,SAAS,EAAE,cAAc,EAAE8D,OAAO,CAACpT,aAAa,CAAC;MACzE,MAAM2O,6DAAS,CAACe,MAAM,EAAEnP,OAAO,CAAC;IAAC;EACnC;EAEA;EACMqU,wBAAwBA,CAACxV,QAAgB,EAAEhF,IAAY;IAAA,IAAAya,OAAA;IAAA,OAAAxf,6KAAA;MAC3D,MAAMiJ,MAAM,SAASuW,OAAI,CAAC7E,gBAAgB,CAAC5Q,QAAQ,CAAC;MACpD,MAAMtD,YAAY,SAAS+Y,OAAI,CAAC/B,uBAAuB,CAAC1T,QAAQ,EAAEhF,IAAI,EAAEA,IAAI,CAAC;MAC7E,MAAMwX,YAAY,SAASiD,OAAI,CAACpD,qBAAqB,CAACrS,QAAQ,EAAEhF,IAAI,CAAC;MAErE,MAAM0a,iBAAiB,GAAGhZ,YAAY,CAAC5O,MAAM;MAC7C,MAAM6nB,qBAAqB,GAAGjZ,YAAY,CAACvN,MAAM,CAACqO,GAAG,IAAIA,GAAG,CAAC5Q,MAAM,KAAK,WAAW,CAAC,CAACkB,MAAM;MAC3F,MAAM8nB,mBAAmB,GAAGlZ,YAAY,CAACvN,MAAM,CAACqO,GAAG,IAAIA,GAAG,CAAC5Q,MAAM,KAAK,SAAS,CAAC,CAACkB,MAAM;MAEvF;MACA,IAAI+nB,cAAc,GAAG,CAAC;MACtBrD,YAAY,CAACnU,OAAO,CAACyX,KAAK,IAAG;QAC3B,MAAMC,WAAW,GAAGrZ,YAAY,CAACvN,MAAM,CAACqO,GAAG,IACzCA,GAAG,CAACxC,IAAI,KAAK8a,KAAK,CAAC9a,IAAI,IAAI8a,KAAK,CAACpD,SAAS,CAACrjB,QAAQ,CAACmO,GAAG,CAACpC,IAAI,CAAC,CAC9D,CAACtN,MAAM;QACR+nB,cAAc,IAAI/Q,IAAI,CAACkR,GAAG,CAAC,CAAC,EAAEF,KAAK,CAACpD,SAAS,CAAC5kB,MAAM,GAAGioB,WAAW,CAAC;MACrE,CAAC,CAAC;MAEF;MACA,MAAME,kBAAkB,GAAGvZ,YAAY,CAACvN,MAAM,CAACqO,GAAG,IAChD,IAAI5O,IAAI,CAAC4O,GAAG,CAACxC,IAAI,GAAG,GAAG,GAAGwC,GAAG,CAACpC,IAAI,CAAC,GAAG,IAAIxM,IAAI,EAAE,CACjD,CAACsnB,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KACV,IAAIxnB,IAAI,CAACunB,CAAC,CAACnb,IAAI,GAAG,GAAG,GAAGmb,CAAC,CAAC/a,IAAI,CAAC,CAAC4J,OAAO,EAAE,GAAG,IAAIpW,IAAI,CAACwnB,CAAC,CAACpb,IAAI,GAAG,GAAG,GAAGob,CAAC,CAAChb,IAAI,CAAC,CAAC4J,OAAO,EAAE,CACtF;MAED,MAAMqR,eAAe,GAAGJ,kBAAkB,CAACnoB,MAAM,GAAG,CAAC,GAAG;QACtDkN,IAAI,EAAEib,kBAAkB,CAAC,CAAC,CAAC,CAACjb,IAAI;QAChCI,IAAI,EAAE6a,kBAAkB,CAAC,CAAC,CAAC,CAAC7a,IAAI;QAChCE,UAAU,EAAE2a,kBAAkB,CAAC,CAAC,CAAC,CAAC3a;OACnC,GAAGmY,SAAS;MAEb,OAAO;QACLzT,QAAQ;QACRC,UAAU,EAAEf,MAAM,EAAEtK,IAAI,IAAI,SAAS;QACrC8gB,iBAAiB;QACjBC,qBAAqB;QACrBC,mBAAmB;QACnBC,cAAc;QACdQ;OACD;IAAC;EACJ;EAEA;EACMC,yBAAyBA,CAACC,QAAyC;IAAA,IAAAC,OAAA;IAAA,OAAAvgB,6KAAA;MACvE,MAAMqa,MAAM,SAASZ,0DAAM,CAACD,8DAAU,CAAC+G,OAAI,CAACtG,SAAS,EAAE,uBAAuB,CAAC,EAAE;QAC/E,GAAGqG,QAAQ;QACX7V,SAAS,EAAE,IAAI9R,IAAI;OACpB,CAAC;MACF,OAAO0hB,MAAM,CAAC3hB,EAAE;IAAC;EACnB;EAEM8nB,mBAAmBA,CAAA;IAAA,IAAAC,OAAA;IAAA,OAAAzgB,6KAAA;MACvB,MAAM8a,CAAC,GAAGpB,yDAAK,CACbF,8DAAU,CAACiH,OAAI,CAACxG,SAAS,EAAE,uBAAuB,CAAC,EACnDN,yDAAK,CAAC,QAAQ,EAAE,IAAI,EAAE,SAAS,CAAC,EAChCA,yDAAK,CAAC,cAAc,EAAE,IAAI,EAAE,IAAIhhB,IAAI,EAAE,CAAC,CACxC;MAED,MAAMoiB,aAAa,SAASlB,2DAAO,CAACiB,CAAC,CAAC;MACtC,OAAOC,aAAa,CAACE,IAAI,CAACtd,GAAG,CAAC6U,GAAG,KAAK;QACpC9Z,EAAE,EAAE8Z,GAAG,CAAC9Z,EAAE;QACV,GAAG8Z,GAAG,CAACjJ,IAAI;OACY,EAAC;IAAC;EAC7B;EAEMmX,kBAAkBA,CAACC,UAAkB;IAAA,IAAAC,OAAA;IAAA,OAAA5gB,6KAAA;MACzC,MAAMqa,MAAM,GAAG7H,uDAAG,CAACoO,OAAI,CAAC3G,SAAS,EAAE,uBAAuB,EAAE0G,UAAU,CAAC;MACvE,MAAMrH,6DAAS,CAACe,MAAM,EAAE;QACtB1jB,MAAM,EAAE,MAAM;QACdkqB,MAAM,EAAE,IAAIloB,IAAI;OACjB,CAAC;IAAC;EACL;EAEA;EACM2hB,WAAWA,CACf4B,MAAc,EACd4E,QAA2C,EAC3CC,QAAgB,EAChBjqB,MAAc,EACdklB,UAAkB,EAClBC,QAAgB,EAChB+E,OAAa,EACbC,QAAc;IAAA,IAAAC,OAAA;IAAA,OAAAlhB,6KAAA;MAEd,MAAMmhB,QAAQ,GAAa;QACzBjF,MAAM;QACN4E,QAAQ;QACRC,QAAQ;QACRjqB,MAAM;QACNklB,UAAU;QACVC,QAAQ;QACR+E,OAAO;QACPC,QAAQ;QACRhqB,SAAS,EAAE,IAAI0B,IAAI;OACpB;MAED,MAAM8gB,0DAAM,CAACD,8DAAU,CAAC0H,OAAI,CAACjH,SAAS,EAAE,YAAY,CAAC,EAAEkH,QAAQ,CAAC;IAAC;EACnE;;;uBAhqBWngB,eAAe;IAAA;EAAA;;;aAAfA,eAAe;MAAAogB,OAAA,EAAfpgB,eAAe,CAAAqgB,IAAA;MAAAC,UAAA,EAFd;IAAM;EAAA;;;;;;;;;;;;;;;;ACnHb,MAAM7d,WAAW,GAAG;EACzB8d,UAAU,EAAE,KAAK;EACjB3d,QAAQ,EAAE;IACR4d,MAAM,EAAE,yCAAyC;IACjDC,UAAU,EAAE,+BAA+B;IAC3CC,SAAS,EAAE,eAAe;IAC1BC,aAAa,EAAE,mCAAmC;IAClDC,iBAAiB,EAAE,cAAc;IACjCC,KAAK,EAAE;;CAEV,C;;;;;;;;;;;;;;;ACT4C;AAE7CC,sEAAA,EAAwB,CAACE,eAAe,CAACte,sDAAS,CAAC,CAChDue,KAAK,CAACC,GAAG,IAAIjoB,OAAO,CAACsG,KAAK,CAAC2hB,GAAG,CAAC,CAAC,C;;;;;;;;;;ACJnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;;AAEA;AACA;AACA;AACA,EAAE;AACF;AACA;AACA;AACA,qC;;;;;;;;;;AC1OA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE;AACF;AACA;AACA;AACA;AACA,0C", "sources": ["./src/app/activity-log/activity-log.component.ts", "./src/app/app-routing.module.ts", "./src/app/app.component.ts", "./src/app/app.module.ts", "./src/app/calendar/calendar.component.ts", "./src/app/clients/clients.component.ts", "./src/app/dashboard/dashboard.component.ts", "./src/app/files/files.component.ts", "./src/app/finance/finance.component.ts", "./src/app/link-lawyer/link-lawyer.component.ts", "./src/app/login/login.component.ts", "./src/app/services/firebase.service.ts", "./src/environments/environment.ts", "./src/main.ts", "./node_modules/@ionic/core/dist/esm/ lazy ^\\.\\/.*\\.entry\\.js$ include: \\.entry\\.js$ exclude: \\.system\\.entry\\.js$ namespace object", "./node_modules/@stencil/core/internal/client/ lazy ^\\.\\/.*\\.entry\\.js.*$ include: \\.entry\\.js$ exclude: \\.system\\.entry\\.js$ strict namespace object"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\n\r\ninterface ActivityLogEntry {\r\n  id: string;\r\n  timestamp: Date;\r\n  action: 'create' | 'update' | 'delete' | 'view' | 'download' | 'upload' | 'login' | 'logout';\r\n  category: 'client' | 'file' | 'appointment' | 'transaction' | 'system' | 'security';\r\n  description: string;\r\n  details: string;\r\n  user: string;\r\n  ipAddress?: string;\r\n  userAgent?: string;\r\n  severity: 'low' | 'medium' | 'high' | 'critical';\r\n  status: 'success' | 'failed' | 'warning';\r\n}\r\n\r\n@Component({\r\n  selector: 'app-activity-log',\r\n  template: `\r\n    <div class=\"activity-log-container\">\r\n      <div class=\"activity-log-header\">\r\n        <h1>Activity Log</h1>\r\n        <div class=\"header-actions\">\r\n          <button mat-raised-button color=\"primary\" (click)=\"exportLog()\">\r\n            <mat-icon>download</mat-icon>\r\n            Export Log\r\n          </button>\r\n          <button mat-icon-button (click)=\"refreshLog()\">\r\n            <mat-icon>refresh</mat-icon>\r\n          </button>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- Filters Section -->\r\n      <div class=\"filters-section\">\r\n        <div class=\"filter-row\">\r\n          <mat-form-field appearance=\"outline\" class=\"filter-field\">\r\n            <mat-label>Date Range</mat-label>\r\n            <mat-select [(value)]=\"selectedDateRange\" (selectionChange)=\"filterActivities()\">\r\n              <mat-option value=\"today\">Today</mat-option>\r\n              <mat-option value=\"week\">This Week</mat-option>\r\n              <mat-option value=\"month\">This Month</mat-option>\r\n              <mat-option value=\"quarter\">This Quarter</mat-option>\r\n              <mat-option value=\"year\">This Year</mat-option>\r\n              <mat-option value=\"all\">All Time</mat-option>\r\n            </mat-select>\r\n          </mat-form-field>\r\n\r\n          <mat-form-field appearance=\"outline\" class=\"filter-field\">\r\n            <mat-label>Category</mat-label>\r\n            <mat-select [(value)]=\"selectedCategory\" (selectionChange)=\"filterActivities()\">\r\n              <mat-option value=\"\">All Categories</mat-option>\r\n              <mat-option value=\"client\">Client Management</mat-option>\r\n              <mat-option value=\"file\">File Operations</mat-option>\r\n              <mat-option value=\"appointment\">Appointments</mat-option>\r\n              <mat-option value=\"transaction\">Transactions</mat-option>\r\n              <mat-option value=\"system\">System</mat-option>\r\n              <mat-option value=\"security\">Security</mat-option>\r\n            </mat-select>\r\n          </mat-form-field>\r\n\r\n          <mat-form-field appearance=\"outline\" class=\"filter-field\">\r\n            <mat-label>Action Type</mat-label>\r\n            <mat-select [(value)]=\"selectedAction\" (selectionChange)=\"filterActivities()\">\r\n              <mat-option value=\"\">All Actions</mat-option>\r\n              <mat-option value=\"create\">Create</mat-option>\r\n              <mat-option value=\"update\">Update</mat-option>\r\n              <mat-option value=\"delete\">Delete</mat-option>\r\n              <mat-option value=\"view\">View</mat-option>\r\n              <mat-option value=\"download\">Download</mat-option>\r\n              <mat-option value=\"upload\">Upload</mat-option>\r\n              <mat-option value=\"login\">Login</mat-option>\r\n              <mat-option value=\"logout\">Logout</mat-option>\r\n            </mat-select>\r\n          </mat-form-field>\r\n\r\n          <mat-form-field appearance=\"outline\" class=\"filter-field\">\r\n            <mat-label>Severity</mat-label>\r\n            <mat-select [(value)]=\"selectedSeverity\" (selectionChange)=\"filterActivities()\">\r\n              <mat-option value=\"\">All Levels</mat-option>\r\n              <mat-option value=\"low\">Low</mat-option>\r\n              <mat-option value=\"medium\">Medium</mat-option>\r\n              <mat-option value=\"high\">High</mat-option>\r\n              <mat-option value=\"critical\">Critical</mat-option>\r\n            </mat-select>\r\n          </mat-form-field>\r\n        </div>\r\n\r\n        <div class=\"search-row\">\r\n          <mat-form-field appearance=\"outline\" class=\"search-field\">\r\n            <mat-icon matPrefix>search</mat-icon>\r\n            <mat-label>Search activities...</mat-label>\r\n            <input matInput [(ngModel)]=\"searchTerm\" (input)=\"filterActivities()\" placeholder=\"Search by description, user, or details\">\r\n          </mat-form-field>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- Statistics Cards -->\r\n      <div class=\"stats-section\">\r\n        <div class=\"stat-card success\">\r\n          <div class=\"stat-icon\">\r\n            <mat-icon>check_circle</mat-icon>\r\n          </div>\r\n          <div class=\"stat-content\">\r\n            <div class=\"stat-value\">{{ getActivityCount('success') }}</div>\r\n            <div class=\"stat-label\">Successful Actions</div>\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"stat-card warning\">\r\n          <div class=\"stat-icon\">\r\n            <mat-icon>warning</mat-icon>\r\n          </div>\r\n          <div class=\"stat-content\">\r\n            <div class=\"stat-value\">{{ getActivityCount('warning') }}</div>\r\n            <div class=\"stat-label\">Warnings</div>\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"stat-card error\">\r\n          <div class=\"stat-icon\">\r\n            <mat-icon>error</mat-icon>\r\n          </div>\r\n          <div class=\"stat-content\">\r\n            <div class=\"stat-value\">{{ getActivityCount('failed') }}</div>\r\n            <div class=\"stat-label\">Failed Actions</div>\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"stat-card info\">\r\n          <div class=\"stat-icon\">\r\n            <mat-icon>info</mat-icon>\r\n          </div>\r\n          <div class=\"stat-content\">\r\n            <div class=\"stat-value\">{{ getTotalActivities() }}</div>\r\n            <div class=\"stat-label\">Total Activities</div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- Activity Log Table -->\r\n      <div class=\"log-table-section\">\r\n        <div class=\"table-header\">\r\n          <h3>Activity Timeline</h3>\r\n          <div class=\"table-controls\">\r\n            <span class=\"results-count\">{{ filteredActivities.length }} activities found</span>\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"activity-timeline\">\r\n          <div class=\"activity-entry\" *ngFor=\"let activity of filteredActivities\" [class]=\"activity.severity\">\r\n            <div class=\"activity-indicator\" [class]=\"activity.status\">\r\n              <mat-icon>{{ getActivityIcon(activity.action) }}</mat-icon>\r\n            </div>\r\n\r\n            <div class=\"activity-content\">\r\n              <div class=\"activity-header\">\r\n                <div class=\"activity-title\">{{ activity.description }}</div>\r\n                <div class=\"activity-meta\">\r\n                  <span class=\"activity-time\">{{ activity.timestamp | date:'MMM dd, yyyy HH:mm:ss' }}</span>\r\n                  <span class=\"activity-category\" [class]=\"activity.category\">{{ activity.category | titlecase }}</span>\r\n                  <span class=\"activity-severity\" [class]=\"activity.severity\">{{ activity.severity | titlecase }}</span>\r\n                </div>\r\n              </div>\r\n\r\n              <div class=\"activity-details\">\r\n                <p>{{ activity.details }}</p>\r\n                <div class=\"activity-user-info\">\r\n                  <span class=\"user-name\">\r\n                    <mat-icon>person</mat-icon>\r\n                    {{ activity.user }}\r\n                  </span>\r\n                  <span class=\"ip-address\" *ngIf=\"activity.ipAddress\">\r\n                    <mat-icon>computer</mat-icon>\r\n                    {{ activity.ipAddress }}\r\n                  </span>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <div class=\"activity-actions\">\r\n              <button mat-icon-button (click)=\"viewActivityDetails(activity)\">\r\n                <mat-icon>visibility</mat-icon>\r\n              </button>\r\n              <button mat-icon-button [matMenuTriggerFor]=\"activityMenu\">\r\n                <mat-icon>more_vert</mat-icon>\r\n              </button>\r\n              <mat-menu #activityMenu=\"matMenu\">\r\n                <button mat-menu-item (click)=\"exportSingleActivity(activity)\">\r\n                  <mat-icon>download</mat-icon>\r\n                  <span>Export Entry</span>\r\n                </button>\r\n                <button mat-menu-item (click)=\"flagActivity(activity)\">\r\n                  <mat-icon>flag</mat-icon>\r\n                  <span>Flag for Review</span>\r\n                </button>\r\n              </mat-menu>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- Pagination -->\r\n        <div class=\"pagination-section\" *ngIf=\"filteredActivities.length > pageSize\">\r\n          <mat-paginator\r\n            [length]=\"filteredActivities.length\"\r\n            [pageSize]=\"pageSize\"\r\n            [pageSizeOptions]=\"[10, 25, 50, 100]\"\r\n            (page)=\"onPageChange($event)\">\r\n          </mat-paginator>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  `,\r\n  styles: [`\r\n    .activity-log-container {\r\n      padding: 32px;\r\n      background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);\r\n      min-height: calc(100vh - 64px);\r\n      position: relative;\r\n    }\r\n\r\n    .activity-log-container::before {\r\n      content: '';\r\n      position: absolute;\r\n      top: 0;\r\n      left: 0;\r\n      right: 0;\r\n      bottom: 0;\r\n      background:\r\n        radial-gradient(circle at 25% 25%, rgba(251, 146, 60, 0.05) 0%, transparent 50%),\r\n        radial-gradient(circle at 75% 75%, rgba(168, 85, 247, 0.03) 0%, transparent 50%);\r\n      pointer-events: none;\r\n    }\r\n\r\n    .activity-log-header {\r\n      display: flex;\r\n      justify-content: space-between;\r\n      align-items: center;\r\n      margin-bottom: 40px;\r\n      position: relative;\r\n      z-index: 1;\r\n    }\r\n\r\n    .activity-log-header h1 {\r\n      margin: 0;\r\n      font-size: 36px;\r\n      font-weight: 700;\r\n      color: #1e293b;\r\n      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\r\n      position: relative;\r\n    }\r\n\r\n    .activity-log-header h1::after {\r\n      content: '';\r\n      position: absolute;\r\n      bottom: -8px;\r\n      left: 0;\r\n      width: 80px;\r\n      height: 4px;\r\n      background: linear-gradient(90deg, #fb923c 0%, #f97316 100%);\r\n      border-radius: 2px;\r\n    }\r\n\r\n    .header-actions {\r\n      display: flex;\r\n      gap: 12px;\r\n      align-items: center;\r\n    }\r\n\r\n    .filters-section {\r\n      background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);\r\n      border-radius: 20px;\r\n      padding: 32px;\r\n      margin-bottom: 32px;\r\n      box-shadow:\r\n        0 20px 60px rgba(0, 0, 0, 0.1),\r\n        0 8px 24px rgba(0, 0, 0, 0.05);\r\n      border: 1px solid rgba(226, 232, 240, 0.8);\r\n      position: relative;\r\n      overflow: hidden;\r\n    }\r\n\r\n    .filters-section::before {\r\n      content: '';\r\n      position: absolute;\r\n      top: 0;\r\n      left: 0;\r\n      right: 0;\r\n      height: 6px;\r\n      background: linear-gradient(90deg, #fb923c 0%, #f97316 50%, #fb923c 100%);\r\n    }\r\n\r\n    .filter-row {\r\n      display: grid;\r\n      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\r\n      gap: 20px;\r\n      margin-bottom: 20px;\r\n    }\r\n\r\n    .search-row {\r\n      display: flex;\r\n      gap: 20px;\r\n    }\r\n\r\n    .filter-field,\r\n    .search-field {\r\n      width: 100%;\r\n    }\r\n\r\n    .stats-section {\r\n      display: grid;\r\n      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\r\n      gap: 24px;\r\n      margin-bottom: 32px;\r\n      position: relative;\r\n      z-index: 1;\r\n    }\r\n\r\n    .stat-card {\r\n      background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);\r\n      border-radius: 20px;\r\n      padding: 28px;\r\n      box-shadow:\r\n        0 20px 60px rgba(0, 0, 0, 0.1),\r\n        0 8px 24px rgba(0, 0, 0, 0.05);\r\n      border: 1px solid rgba(226, 232, 240, 0.8);\r\n      display: flex;\r\n      align-items: center;\r\n      gap: 20px;\r\n      transition: all 0.3s ease;\r\n      position: relative;\r\n      overflow: hidden;\r\n    }\r\n\r\n    .stat-card::before {\r\n      content: '';\r\n      position: absolute;\r\n      top: 0;\r\n      left: 0;\r\n      right: 0;\r\n      height: 4px;\r\n      transition: transform 0.3s ease;\r\n      transform: scaleX(0);\r\n    }\r\n\r\n    .stat-card.success::before {\r\n      background: linear-gradient(90deg, #22c55e 0%, #16a34a 100%);\r\n    }\r\n\r\n    .stat-card.warning::before {\r\n      background: linear-gradient(90deg, #f59e0b 0%, #d97706 100%);\r\n    }\r\n\r\n    .stat-card.error::before {\r\n      background: linear-gradient(90deg, #ef4444 0%, #dc2626 100%);\r\n    }\r\n\r\n    .stat-card.info::before {\r\n      background: linear-gradient(90deg, #3b82f6 0%, #2563eb 100%);\r\n    }\r\n\r\n    .stat-card:hover::before {\r\n      transform: scaleX(1);\r\n    }\r\n\r\n    .stat-card:hover {\r\n      transform: translateY(-4px);\r\n      box-shadow:\r\n        0 30px 80px rgba(0, 0, 0, 0.15),\r\n        0 12px 32px rgba(0, 0, 0, 0.08);\r\n    }\r\n\r\n    .stat-icon {\r\n      width: 60px;\r\n      height: 60px;\r\n      border-radius: 50%;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n      font-size: 28px;\r\n    }\r\n\r\n    .stat-card.success .stat-icon {\r\n      background: linear-gradient(135deg, #dcfce7 0%, #bbf7d0 100%);\r\n      color: #16a34a;\r\n    }\r\n\r\n    .stat-card.warning .stat-icon {\r\n      background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);\r\n      color: #d97706;\r\n    }\r\n\r\n    .stat-card.error .stat-icon {\r\n      background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%);\r\n      color: #dc2626;\r\n    }\r\n\r\n    .stat-card.info .stat-icon {\r\n      background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);\r\n      color: #2563eb;\r\n    }\r\n\r\n    .stat-content {\r\n      flex: 1;\r\n    }\r\n\r\n    .stat-value {\r\n      font-size: 32px;\r\n      font-weight: 700;\r\n      color: #1e293b;\r\n      margin-bottom: 4px;\r\n    }\r\n\r\n    .stat-label {\r\n      font-size: 14px;\r\n      color: #64748b;\r\n      font-weight: 500;\r\n    }\r\n\r\n    .log-table-section {\r\n      background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);\r\n      border-radius: 20px;\r\n      padding: 32px;\r\n      box-shadow:\r\n        0 20px 60px rgba(0, 0, 0, 0.1),\r\n        0 8px 24px rgba(0, 0, 0, 0.05);\r\n      border: 1px solid rgba(226, 232, 240, 0.8);\r\n      position: relative;\r\n      overflow: hidden;\r\n    }\r\n\r\n    .log-table-section::before {\r\n      content: '';\r\n      position: absolute;\r\n      top: 0;\r\n      left: 0;\r\n      right: 0;\r\n      height: 6px;\r\n      background: linear-gradient(90deg, #a855f7 0%, #9333ea 50%, #a855f7 100%);\r\n    }\r\n\r\n    .table-header {\r\n      display: flex;\r\n      justify-content: space-between;\r\n      align-items: center;\r\n      margin-bottom: 24px;\r\n    }\r\n\r\n    .table-header h3 {\r\n      margin: 0;\r\n      font-size: 20px;\r\n      font-weight: 600;\r\n      color: #1e293b;\r\n    }\r\n\r\n    .results-count {\r\n      font-size: 14px;\r\n      color: #64748b;\r\n      font-weight: 500;\r\n    }\r\n\r\n    .activity-timeline {\r\n      display: flex;\r\n      flex-direction: column;\r\n      gap: 16px;\r\n      margin-bottom: 24px;\r\n    }\r\n\r\n    .activity-entry {\r\n      display: flex;\r\n      align-items: flex-start;\r\n      gap: 20px;\r\n      padding: 20px;\r\n      border: 1px solid rgba(226, 232, 240, 0.8);\r\n      border-radius: 16px;\r\n      transition: all 0.3s ease;\r\n      background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);\r\n      position: relative;\r\n    }\r\n\r\n    .activity-entry:hover {\r\n      border-color: #fb923c;\r\n      box-shadow: 0 8px 24px rgba(251, 146, 60, 0.15);\r\n      transform: translateY(-2px);\r\n    }\r\n\r\n    .activity-entry.critical {\r\n      border-left: 4px solid #ef4444;\r\n    }\r\n\r\n    .activity-entry.high {\r\n      border-left: 4px solid #f59e0b;\r\n    }\r\n\r\n    .activity-entry.medium {\r\n      border-left: 4px solid #3b82f6;\r\n    }\r\n\r\n    .activity-entry.low {\r\n      border-left: 4px solid #22c55e;\r\n    }\r\n\r\n    .activity-indicator {\r\n      width: 48px;\r\n      height: 48px;\r\n      border-radius: 50%;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n      flex-shrink: 0;\r\n    }\r\n\r\n    .activity-indicator.success {\r\n      background: linear-gradient(135deg, #dcfce7 0%, #bbf7d0 100%);\r\n      color: #16a34a;\r\n    }\r\n\r\n    .activity-indicator.warning {\r\n      background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);\r\n      color: #d97706;\r\n    }\r\n\r\n    .activity-indicator.failed {\r\n      background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%);\r\n      color: #dc2626;\r\n    }\r\n\r\n    .activity-content {\r\n      flex: 1;\r\n    }\r\n\r\n    .activity-header {\r\n      margin-bottom: 12px;\r\n    }\r\n\r\n    .activity-title {\r\n      font-size: 16px;\r\n      font-weight: 600;\r\n      color: #1e293b;\r\n      margin-bottom: 8px;\r\n    }\r\n\r\n    .activity-meta {\r\n      display: flex;\r\n      gap: 16px;\r\n      flex-wrap: wrap;\r\n    }\r\n\r\n    .activity-time {\r\n      font-size: 12px;\r\n      color: #64748b;\r\n      font-weight: 500;\r\n    }\r\n\r\n    .activity-category,\r\n    .activity-severity {\r\n      font-size: 11px;\r\n      padding: 4px 8px;\r\n      border-radius: 12px;\r\n      font-weight: 600;\r\n      text-transform: uppercase;\r\n    }\r\n\r\n    .activity-category.client {\r\n      background: #dbeafe;\r\n      color: #2563eb;\r\n    }\r\n\r\n    .activity-category.file {\r\n      background: #f3e8ff;\r\n      color: #9333ea;\r\n    }\r\n\r\n    .activity-category.appointment {\r\n      background: #ecfdf5;\r\n      color: #16a34a;\r\n    }\r\n\r\n    .activity-category.transaction {\r\n      background: #fef3c7;\r\n      color: #d97706;\r\n    }\r\n\r\n    .activity-category.system {\r\n      background: #f1f5f9;\r\n      color: #475569;\r\n    }\r\n\r\n    .activity-category.security {\r\n      background: #fee2e2;\r\n      color: #dc2626;\r\n    }\r\n\r\n    .activity-severity.low {\r\n      background: #dcfce7;\r\n      color: #16a34a;\r\n    }\r\n\r\n    .activity-severity.medium {\r\n      background: #dbeafe;\r\n      color: #2563eb;\r\n    }\r\n\r\n    .activity-severity.high {\r\n      background: #fef3c7;\r\n      color: #d97706;\r\n    }\r\n\r\n    .activity-severity.critical {\r\n      background: #fee2e2;\r\n      color: #dc2626;\r\n    }\r\n\r\n    .activity-details p {\r\n      margin: 0 0 12px 0;\r\n      font-size: 14px;\r\n      color: #475569;\r\n      line-height: 1.5;\r\n    }\r\n\r\n    .activity-user-info {\r\n      display: flex;\r\n      gap: 20px;\r\n      align-items: center;\r\n    }\r\n\r\n    .user-name,\r\n    .ip-address {\r\n      display: flex;\r\n      align-items: center;\r\n      gap: 6px;\r\n      font-size: 12px;\r\n      color: #64748b;\r\n    }\r\n\r\n    .user-name mat-icon,\r\n    .ip-address mat-icon {\r\n      font-size: 16px;\r\n      width: 16px;\r\n      height: 16px;\r\n    }\r\n\r\n    .activity-actions {\r\n      display: flex;\r\n      gap: 4px;\r\n      flex-shrink: 0;\r\n    }\r\n\r\n    .activity-actions button {\r\n      width: 36px;\r\n      height: 36px;\r\n    }\r\n\r\n    .activity-actions mat-icon {\r\n      font-size: 18px;\r\n      width: 18px;\r\n      height: 18px;\r\n    }\r\n\r\n    .pagination-section {\r\n      margin-top: 24px;\r\n      display: flex;\r\n      justify-content: center;\r\n    }\r\n\r\n    @media (max-width: 768px) {\r\n      .activity-log-header {\r\n        flex-direction: column;\r\n        gap: 16px;\r\n        align-items: stretch;\r\n      }\r\n\r\n      .filter-row {\r\n        grid-template-columns: 1fr;\r\n      }\r\n\r\n      .stats-section {\r\n        grid-template-columns: 1fr;\r\n      }\r\n\r\n      .activity-entry {\r\n        flex-direction: column;\r\n        gap: 16px;\r\n      }\r\n\r\n      .activity-meta {\r\n        flex-direction: column;\r\n        gap: 8px;\r\n      }\r\n    }\r\n  `]\r\n})\r\nexport class ActivityLogComponent implements OnInit {\r\n  searchTerm = '';\r\n  selectedDateRange = 'month';\r\n  selectedCategory = '';\r\n  selectedAction = '';\r\n  selectedSeverity = '';\r\n  pageSize = 25;\r\n  currentPage = 0;\r\n\r\n  activities: ActivityLogEntry[] = [\r\n    {\r\n      id: '1',\r\n      timestamp: new Date(2025, 6, 11, 14, 30, 0), // July 11, 2025 2:30 PM\r\n      action: 'create',\r\n      category: 'client',\r\n      description: 'Created new client profile',\r\n      details: 'Added new retainer client \"Maria Santos\" with ₱60,000 retainer amount. All required documentation verified.',\r\n      user: 'Manny P. (Secretary)',\r\n      ipAddress: '*************',\r\n      userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',\r\n      severity: 'medium',\r\n      status: 'success'\r\n    },\r\n    {\r\n      id: '2',\r\n      timestamp: new Date(2025, 6, 11, 13, 15, 0), // July 11, 2025 1:15 PM\r\n      action: 'upload',\r\n      category: 'file',\r\n      description: 'Uploaded case documents',\r\n      details: 'Uploaded 3 files to Evidence folder: witness-statement.pdf (2.1MB), photo-evidence.zip (15.2MB), expert-report.docx (890KB)',\r\n      user: 'Manny P. (Secretary)',\r\n      ipAddress: '*************',\r\n      severity: 'low',\r\n      status: 'success'\r\n    },\r\n    {\r\n      id: '3',\r\n      timestamp: new Date(2025, 6, 11, 12, 45, 0), // July 11, 2025 12:45 PM\r\n      action: 'update',\r\n      category: 'appointment',\r\n      description: 'Modified appointment schedule',\r\n      details: 'Rescheduled consultation for Kobe Bryant from July 10, 2025 3:00 PM to July 12, 2025 10:00 AM. Client notified via email.',\r\n      user: 'Manny P. (Secretary)',\r\n      ipAddress: '*************',\r\n      severity: 'medium',\r\n      status: 'success'\r\n    },\r\n    {\r\n      id: '4',\r\n      timestamp: new Date(2025, 6, 11, 11, 20, 0), // July 11, 2025 11:20 AM\r\n      action: 'create',\r\n      category: 'transaction',\r\n      description: 'Recorded consultation payment',\r\n      details: 'Added consultation fee payment of ₱15,000 from John Doe. Payment method: Bank Transfer. Reference: TXN-2025-0711-001',\r\n      user: 'Manny P. (Secretary)',\r\n      ipAddress: '*************',\r\n      severity: 'high',\r\n      status: 'success'\r\n    },\r\n    {\r\n      id: '5',\r\n      timestamp: new Date(2025, 6, 11, 10, 30, 0), // July 11, 2025 10:30 AM\r\n      action: 'download',\r\n      category: 'file',\r\n      description: 'Downloaded client contract',\r\n      details: 'Downloaded Lopez-Contract.pdf (2.4MB) for review and client meeting preparation.',\r\n      user: 'Manny P. (Secretary)',\r\n      ipAddress: '*************',\r\n      severity: 'low',\r\n      status: 'success'\r\n    },\r\n    {\r\n      id: '6',\r\n      timestamp: new Date(2025, 6, 11, 9, 15, 0), // July 11, 2025 9:15 AM\r\n      action: 'login',\r\n      category: 'security',\r\n      description: 'User login successful',\r\n      details: 'Secretary Manny P. logged in successfully from IP *************. Session ID: sess_2025071109150001',\r\n      user: 'Manny P. (Secretary)',\r\n      ipAddress: '*************',\r\n      severity: 'low',\r\n      status: 'success'\r\n    },\r\n    {\r\n      id: '7',\r\n      timestamp: new Date(2025, 6, 10, 17, 45, 0), // July 10, 2025 5:45 PM\r\n      action: 'logout',\r\n      category: 'security',\r\n      description: 'User logout',\r\n      details: 'Secretary Manny P. logged out. Session duration: 8 hours 30 minutes. No security incidents detected.',\r\n      user: 'Manny P. (Secretary)',\r\n      ipAddress: '*************',\r\n      severity: 'low',\r\n      status: 'success'\r\n    },\r\n    {\r\n      id: '8',\r\n      timestamp: new Date(2025, 6, 10, 16, 20, 0), // July 10, 2025 4:20 PM\r\n      action: 'update',\r\n      category: 'client',\r\n      description: 'Updated client contact information',\r\n      details: 'Updated phone number for Clara Mendoza from +63 ************ to +63 ************. Email verification sent.',\r\n      user: 'Manny P. (Secretary)',\r\n      ipAddress: '*************',\r\n      severity: 'medium',\r\n      status: 'success'\r\n    },\r\n    {\r\n      id: '9',\r\n      timestamp: new Date(2025, 6, 10, 15, 10, 0), // July 10, 2025 3:10 PM\r\n      action: 'view',\r\n      category: 'file',\r\n      description: 'Accessed confidential documents',\r\n      details: 'Viewed financial disclosure documents for case #2025-0710-001. Access logged for compliance audit.',\r\n      user: 'Manny P. (Secretary)',\r\n      ipAddress: '*************',\r\n      severity: 'high',\r\n      status: 'success'\r\n    },\r\n    {\r\n      id: '10',\r\n      timestamp: new Date(2025, 6, 10, 14, 30, 0), // July 10, 2025 2:30 PM\r\n      action: 'delete',\r\n      category: 'file',\r\n      description: 'Deleted duplicate file',\r\n      details: 'Removed duplicate file \"contract-copy-2.pdf\" from Contracts folder. Original file retained with proper naming convention.',\r\n      user: 'Manny P. (Secretary)',\r\n      ipAddress: '*************',\r\n      severity: 'medium',\r\n      status: 'success'\r\n    },\r\n    {\r\n      id: '11',\r\n      timestamp: new Date(2025, 6, 9, 16, 45, 0), // July 9, 2025 4:45 PM\r\n      action: 'create',\r\n      category: 'appointment',\r\n      description: 'Scheduled new appointment',\r\n      details: 'Created appointment for Michael Jordan - Case Review on July 12, 2025 at 2:00 PM. Calendar invite sent to all parties.',\r\n      user: 'Manny P. (Secretary)',\r\n      ipAddress: '*************',\r\n      severity: 'medium',\r\n      status: 'success'\r\n    },\r\n    {\r\n      id: '12',\r\n      timestamp: new Date(2025, 6, 9, 11, 20, 0), // July 9, 2025 11:20 AM\r\n      action: 'update',\r\n      category: 'system',\r\n      description: 'System backup completed',\r\n      details: 'Automated daily backup completed successfully. 1.2GB of data backed up to secure cloud storage. Backup verification passed.',\r\n      user: 'System',\r\n      ipAddress: '********',\r\n      severity: 'low',\r\n      status: 'success'\r\n    },\r\n    {\r\n      id: '13',\r\n      timestamp: new Date(2025, 6, 8, 13, 30, 0), // July 8, 2025 1:30 PM\r\n      action: 'view',\r\n      category: 'security',\r\n      description: 'Failed login attempt detected',\r\n      details: 'Multiple failed login attempts detected from IP ************. Account temporarily locked for security. User notified via email.',\r\n      user: 'Security System',\r\n      ipAddress: '************',\r\n      severity: 'critical',\r\n      status: 'warning'\r\n    }\r\n  ];\r\n\r\n  filteredActivities: ActivityLogEntry[] = [];\r\n\r\n  ngOnInit() {\r\n    this.filteredActivities = [...this.activities];\r\n  }\r\n\r\n  filterActivities() {\r\n    let filtered = [...this.activities];\r\n\r\n    // Filter by search term\r\n    if (this.searchTerm) {\r\n      const term = this.searchTerm.toLowerCase();\r\n      filtered = filtered.filter(activity =>\r\n        activity.description.toLowerCase().includes(term) ||\r\n        activity.details.toLowerCase().includes(term) ||\r\n        activity.user.toLowerCase().includes(term)\r\n      );\r\n    }\r\n\r\n    // Filter by category\r\n    if (this.selectedCategory) {\r\n      filtered = filtered.filter(activity => activity.category === this.selectedCategory);\r\n    }\r\n\r\n    // Filter by action\r\n    if (this.selectedAction) {\r\n      filtered = filtered.filter(activity => activity.action === this.selectedAction);\r\n    }\r\n\r\n    // Filter by severity\r\n    if (this.selectedSeverity) {\r\n      filtered = filtered.filter(activity => activity.severity === this.selectedSeverity);\r\n    }\r\n\r\n    // Filter by date range\r\n    if (this.selectedDateRange !== 'all') {\r\n      const now = new Date();\r\n      let startDate = new Date();\r\n\r\n      switch (this.selectedDateRange) {\r\n        case 'today':\r\n          startDate.setHours(0, 0, 0, 0);\r\n          break;\r\n        case 'week':\r\n          startDate.setDate(now.getDate() - 7);\r\n          break;\r\n        case 'month':\r\n          startDate.setMonth(now.getMonth() - 1);\r\n          break;\r\n        case 'quarter':\r\n          startDate.setMonth(now.getMonth() - 3);\r\n          break;\r\n        case 'year':\r\n          startDate.setFullYear(now.getFullYear() - 1);\r\n          break;\r\n      }\r\n\r\n      filtered = filtered.filter(activity => activity.timestamp >= startDate);\r\n    }\r\n\r\n    this.filteredActivities = filtered;\r\n  }\r\n\r\n  getActivityCount(status: string): number {\r\n    return this.filteredActivities.filter(activity => activity.status === status).length;\r\n  }\r\n\r\n  getTotalActivities(): number {\r\n    return this.filteredActivities.length;\r\n  }\r\n\r\n  getActivityIcon(action: string): string {\r\n    switch (action) {\r\n      case 'create': return 'add_circle';\r\n      case 'update': return 'edit';\r\n      case 'delete': return 'delete';\r\n      case 'view': return 'visibility';\r\n      case 'download': return 'download';\r\n      case 'upload': return 'upload';\r\n      case 'login': return 'login';\r\n      case 'logout': return 'logout';\r\n      default: return 'info';\r\n    }\r\n  }\r\n\r\n  exportLog() {\r\n    // Implement log export functionality\r\n    console.log('Exporting activity log...');\r\n  }\r\n\r\n  refreshLog() {\r\n    // Implement log refresh functionality\r\n    this.filterActivities();\r\n    console.log('Activity log refreshed');\r\n  }\r\n\r\n  viewActivityDetails(activity: ActivityLogEntry) {\r\n    // Implement activity details view\r\n    console.log('Viewing activity details:', activity);\r\n  }\r\n\r\n  exportSingleActivity(activity: ActivityLogEntry) {\r\n    // Implement single activity export\r\n    console.log('Exporting activity:', activity);\r\n  }\r\n\r\n  flagActivity(activity: ActivityLogEntry) {\r\n    // Implement activity flagging\r\n    console.log('Flagging activity for review:', activity);\r\n  }\r\n\r\n  onPageChange(event: any) {\r\n    this.currentPage = event.pageIndex;\r\n    this.pageSize = event.pageSize;\r\n  }\r\n}", "import { NgModule } from '@angular/core';\r\nimport { RouterModule, Routes } from '@angular/router';\r\n\r\n// Existing components\r\nimport { DashboardComponent } from './dashboard/dashboard.component';\r\nimport { LoginComponent } from './login/login.component';\r\nimport { LinkLawyerComponent } from './link-lawyer/link-lawyer.component';\r\nimport { CalendarComponent } from './calendar/calendar.component';\r\nimport { FilesComponent } from './files/files.component';\r\nimport { FinanceComponent } from './finance/finance.component';\r\nimport { ClientsComponent } from './clients/clients.component';\r\nimport { ActivityLogComponent } from './activity-log/activity-log.component';\r\n\r\nconst routes: Routes = [\r\n  { path: '', redirectTo: '/secretary-tabs', pathMatch: 'full' },\r\n  { path: 'login', component: LoginComponent },\r\n\r\n  // Secretary Tab Navigation\r\n  {\r\n    path: 'secretary-tabs',\r\n    loadChildren: () => import('./secretary-tabs/secretary-tabs.module').then(m => m.SecretaryTabsPageModule)\r\n  },\r\n\r\n  // Secretary Components (Lazy Loaded)\r\n  {\r\n    path: 'secretary-dashboard',\r\n    loadChildren: () => import('./secretary-dashboard/secretary-dashboard.module').then(m => m.SecretaryDashboardPageModule)\r\n  },\r\n  {\r\n    path: 'secretary-calendar',\r\n    loadChildren: () => import('./secretary-calendar/secretary-calendar.module').then(m => m.SecretaryCalendarPageModule)\r\n  },\r\n  {\r\n    path: 'secretary-cases',\r\n    loadChildren: () => import('./secretary-cases/secretary-cases.module').then(m => m.SecretaryCasesPageModule)\r\n  },\r\n  {\r\n    path: 'secretary-files',\r\n    loadChildren: () => import('./secretary-files/secretary-files.module').then(m => m.SecretaryFilesPageModule)\r\n  },\r\n  {\r\n    path: 'secretary-profile',\r\n    loadChildren: () => import('./secretary-profile/secretary-profile.module').then(m => m.SecretaryProfilePageModule)\r\n  },\r\n\r\n  // Legacy routes (keeping for backward compatibility)\r\n  { path: 'dashboard', component: DashboardComponent },\r\n  { path: 'lawyer-list', component: LinkLawyerComponent },\r\n  { path: 'calendar', component: CalendarComponent },\r\n  { path: 'clients', component: ClientsComponent },\r\n  { path: 'finance', component: FinanceComponent },\r\n  { path: 'activity-log', component: ActivityLogComponent },\r\n  { path: 'link-lawyer', component: LinkLawyerComponent },\r\n  { path: 'files', component: FilesComponent },\r\n\r\n  // Fallback\r\n  { path: '**', redirectTo: '/secretary-tabs' }\r\n];\r\n\r\n@NgModule({\r\n  imports: [RouterModule.forRoot(routes)],\r\n  exports: [RouterModule]\r\n})\r\nexport class AppRoutingModule { }\r\n", "import { Component } from '@angular/core';\r\nimport { BreakpointObserver, Breakpoints } from '@angular/cdk/layout';\r\nimport { Observable } from 'rxjs';\r\nimport { map, shareReplay } from 'rxjs/operators';\r\nimport { Router, NavigationEnd } from '@angular/router';\r\nimport { FirebaseService } from './services/firebase.service';\r\nimport { filter } from 'rxjs/operators';\r\n\r\n@Component({\r\n  selector: 'app-root',\r\n  template: `\r\n    <!-- Login Page Layout (No Sidebar) -->\r\n    <div *ngIf=\"isLoginPage\" class=\"login-layout\">\r\n      <router-outlet></router-outlet>\r\n    </div>\r\n\r\n    <!-- Dashboard Layout (With Sidebar) -->\r\n    <mat-sidenav-container *ngIf=\"!isLoginPage\" class=\"sidenav-container\">\r\n      <mat-sidenav #drawer class=\"sidenav\" fixedInViewport\r\n          [attr.role]=\"(isHandset$ | async) ? 'dialog' : 'navigation'\"\r\n          [mode]=\"(isHandset$ | async) ? 'over' : 'side'\"\r\n          [opened]=\"(isHandset$ | async) === false\">\r\n\r\n        <!-- Veritus Logo Header -->\r\n        <div class=\"sidebar-header\">\r\n          <div class=\"logo-container\">\r\n            <mat-icon class=\"logo-icon\">balance</mat-icon>\r\n            <span class=\"logo-text\">Veritus</span>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- Navigation Menu -->\r\n        <mat-nav-list class=\"nav-menu\">\r\n          <a mat-list-item routerLink=\"/dashboard\" routerLinkActive=\"active-nav-item\">\r\n            <mat-icon matListItemIcon>dashboard</mat-icon>\r\n            <span matListItemTitle>Dashboard</span>\r\n          </a>\r\n          <a mat-list-item routerLink=\"/lawyer-list\" routerLinkActive=\"active-nav-item\">\r\n            <mat-icon matListItemIcon>people</mat-icon>\r\n            <span matListItemTitle>Lawyer List</span>\r\n          </a>\r\n          <a mat-list-item routerLink=\"/calendar\" routerLinkActive=\"active-nav-item\">\r\n            <mat-icon matListItemIcon>event</mat-icon>\r\n            <span matListItemTitle>Calendar</span>\r\n          </a>\r\n          <a mat-list-item routerLink=\"/cases\" routerLinkActive=\"active-nav-item\">\r\n            <mat-icon matListItemIcon>folder</mat-icon>\r\n            <span matListItemTitle>Cases</span>\r\n          </a>\r\n          <a mat-list-item routerLink=\"/clients\" routerLinkActive=\"active-nav-item\">\r\n            <mat-icon matListItemIcon>person</mat-icon>\r\n            <span matListItemTitle>Clients</span>\r\n          </a>\r\n          <a mat-list-item routerLink=\"/finance\" routerLinkActive=\"active-nav-item\">\r\n            <mat-icon matListItemIcon>account_balance</mat-icon>\r\n            <span matListItemTitle>Finance</span>\r\n          </a>\r\n          <a mat-list-item routerLink=\"/activity-log\" routerLinkActive=\"active-nav-item\">\r\n            <mat-icon matListItemIcon>history</mat-icon>\r\n            <span matListItemTitle>Activity Log</span>\r\n          </a>\r\n          <a mat-list-item routerLink=\"/retainers\" routerLinkActive=\"active-nav-item\">\r\n            <mat-icon matListItemIcon>receipt</mat-icon>\r\n            <span matListItemTitle>Retainers</span>\r\n          </a>\r\n          <a mat-list-item routerLink=\"/settings\" routerLinkActive=\"active-nav-item\">\r\n            <mat-icon matListItemIcon>settings</mat-icon>\r\n            <span matListItemTitle>Settings</span>\r\n          </a>\r\n        </mat-nav-list>\r\n\r\n        <!-- Logout Button -->\r\n        <div class=\"sidebar-footer\">\r\n          <button mat-list-item (click)=\"logout()\" class=\"logout-btn\">\r\n            <mat-icon matListItemIcon>logout</mat-icon>\r\n            <span matListItemTitle>Log Out</span>\r\n          </button>\r\n        </div>\r\n      </mat-sidenav>\r\n\r\n      <mat-sidenav-content>\r\n        <!-- Top Header -->\r\n        <mat-toolbar class=\"top-header\">\r\n          <button\r\n            type=\"button\"\r\n            aria-label=\"Toggle sidenav\"\r\n            mat-icon-button\r\n            (click)=\"drawer.toggle()\"\r\n            *ngIf=\"isHandset$ | async\">\r\n            <mat-icon aria-label=\"Side nav toggle icon\">menu</mat-icon>\r\n          </button>\r\n\r\n          <span class=\"spacer\"></span>\r\n\r\n          <!-- User Profile Section -->\r\n          <div class=\"user-profile\">\r\n            <span class=\"user-greeting\">Hi, {{ currentUser?.name || 'Manny P.' }}</span>\r\n            <button mat-icon-button [matMenuTriggerFor]=\"userMenu\" class=\"user-avatar\">\r\n              <img [src]=\"currentUser?.photoURL || 'assets/default-avatar.png'\"\r\n                   alt=\"User Avatar\" class=\"avatar-img\">\r\n            </button>\r\n            <mat-menu #userMenu=\"matMenu\">\r\n              <button mat-menu-item routerLink=\"/profile\">\r\n                <mat-icon>person</mat-icon>\r\n                <span>Profile</span>\r\n              </button>\r\n              <button mat-menu-item (click)=\"logout()\">\r\n                <mat-icon>logout</mat-icon>\r\n                <span>Logout</span>\r\n              </button>\r\n            </mat-menu>\r\n          </div>\r\n        </mat-toolbar>\r\n\r\n        <router-outlet></router-outlet>\r\n      </mat-sidenav-content>\r\n    </mat-sidenav-container>\r\n  `,\r\n  styles: [`\r\n    .login-layout {\r\n      height: 100vh;\r\n      width: 100vw;\r\n      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n    }\r\n\r\n    .sidenav-container {\r\n      height: 100vh;\r\n      background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);\r\n    }\r\n\r\n    .sidenav {\r\n      width: 280px;\r\n      background: #C49A56;\r\n      color: white;\r\n      box-shadow: 4px 0 20px rgba(0, 0, 0, 0.1);\r\n      border-right: none;\r\n    }\r\n\r\n    .sidebar-header {\r\n      padding: 24px 20px;\r\n      border-bottom: 1px solid rgba(255, 255, 255, 0.2);\r\n      background: transparent;\r\n    }\r\n\r\n    .logo-container {\r\n      display: flex;\r\n      align-items: center;\r\n      gap: 16px;\r\n      transition: all 0.3s ease;\r\n    }\r\n\r\n    .logo-container:hover {\r\n      transform: translateY(-2px);\r\n    }\r\n\r\n    .logo-icon {\r\n      font-size: 36px;\r\n      width: 36px;\r\n      height: 36px;\r\n      color: #C49A56;\r\n      filter: drop-shadow(0 2px 4px rgba(196, 154, 86, 0.3));\r\n      transition: all 0.3s ease;\r\n    }\r\n\r\n    .logo-icon:hover {\r\n      transform: rotate(5deg) scale(1.1);\r\n      color: #D4AF6A;\r\n    }\r\n\r\n    .logo-text {\r\n      font-size: 24px;\r\n      font-weight: 600;\r\n      color: white;\r\n      letter-spacing: 0.5px;\r\n    }\r\n\r\n    .nav-menu {\r\n      padding: 24px 0;\r\n    }\r\n\r\n    .nav-menu .mat-mdc-list-item {\r\n      color: white;\r\n      margin: 4px 16px;\r\n      border-radius: 8px;\r\n      transition: all 0.3s ease;\r\n      position: relative;\r\n      overflow: hidden;\r\n    }\r\n\r\n    .nav-menu .mat-mdc-list-item::before {\r\n      content: '';\r\n      position: absolute;\r\n      top: 0;\r\n      left: -100%;\r\n      width: 100%;\r\n      height: 100%;\r\n      background: linear-gradient(90deg, transparent, rgba(196, 154, 86, 0.1), transparent);\r\n      transition: left 0.5s ease;\r\n    }\r\n\r\n    .nav-menu .mat-mdc-list-item:hover::before {\r\n      left: 100%;\r\n    }\r\n\r\n    .nav-menu .mat-mdc-list-item:hover {\r\n      background: rgba(255, 255, 255, 0.1);\r\n      color: white;\r\n      transform: translateX(4px);\r\n    }\r\n\r\n    .nav-menu .active-nav-item {\r\n      background: rgba(255, 255, 255, 0.2);\r\n      color: white;\r\n      transform: translateX(4px);\r\n    }\r\n\r\n    .nav-menu .active-nav-item:hover {\r\n      background: rgba(255, 255, 255, 0.25);\r\n      transform: translateX(4px);\r\n      color: white;\r\n    }\r\n\r\n    .sidebar-footer {\r\n      position: absolute;\r\n      bottom: 20px;\r\n      left: 0;\r\n      right: 0;\r\n      padding: 0 16px;\r\n    }\r\n\r\n    .logout-btn {\r\n      color: rgba(255, 255, 255, 0.8);\r\n      border-radius: 8px;\r\n      transition: all 0.3s ease;\r\n    }\r\n\r\n    .logout-btn:hover {\r\n      background: rgba(255, 255, 255, 0.1);\r\n      color: white;\r\n    }\r\n\r\n    .top-header {\r\n      background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);\r\n      color: #1e293b;\r\n      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\r\n      z-index: 100;\r\n      backdrop-filter: blur(20px);\r\n      border-bottom: 1px solid rgba(226, 232, 240, 0.5);\r\n    }\r\n\r\n    .spacer {\r\n      flex: 1 1 auto;\r\n    }\r\n\r\n    .user-profile {\r\n      display: flex;\r\n      align-items: center;\r\n      gap: 16px;\r\n      padding: 8px 16px;\r\n      border-radius: 12px;\r\n      transition: all 0.3s ease;\r\n    }\r\n\r\n    .user-profile:hover {\r\n      background: rgba(226, 232, 240, 0.5);\r\n      transform: translateY(-1px);\r\n    }\r\n\r\n    .user-greeting {\r\n      font-size: 15px;\r\n      color: #475569;\r\n      font-weight: 500;\r\n    }\r\n\r\n    .user-avatar {\r\n      width: 44px;\r\n      height: 44px;\r\n      border-radius: 50%;\r\n      overflow: hidden;\r\n      border: 3px solid #C49A56;\r\n      transition: all 0.3s ease;\r\n      box-shadow: 0 2px 8px rgba(196, 154, 86, 0.3);\r\n    }\r\n\r\n    .user-avatar:hover {\r\n      transform: scale(1.1);\r\n      box-shadow: 0 4px 16px rgba(196, 154, 86, 0.4);\r\n    }\r\n\r\n    .avatar-img {\r\n      width: 100%;\r\n      height: 100%;\r\n      object-fit: cover;\r\n      border-radius: 50%;\r\n    }\r\n\r\n    mat-sidenav-content {\r\n      background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);\r\n      min-height: 100vh;\r\n    }\r\n  `]\r\n})\r\nexport class AppComponent {\r\n  title = 'veritus-secretary';\r\n  currentUser: any = {\r\n    name: 'Manny P.',\r\n    photoURL: null\r\n  };\r\n\r\n  isLoginPage = false;\r\n\r\n  isHandset$: Observable<boolean> = this.breakpointObserver.observe(Breakpoints.Handset)\r\n    .pipe(\r\n      map(result => result.matches),\r\n      shareReplay()\r\n    );\r\n\r\n  constructor(\r\n    private breakpointObserver: BreakpointObserver,\r\n    private router: Router,\r\n    private firebaseService: FirebaseService\r\n  ) {\r\n    // Listen to route changes to determine if we're on login page\r\n    this.router.events.pipe(\r\n      filter(event => event instanceof NavigationEnd)\r\n    ).subscribe((event) => {\r\n      const navigationEvent = event as NavigationEnd;\r\n      this.isLoginPage = navigationEvent.url === '/login' || navigationEvent.url.startsWith('/login');\r\n      console.log('Route changed:', navigationEvent.url, 'isLoginPage:', this.isLoginPage);\r\n    });\r\n\r\n    // Set initial state\r\n    this.isLoginPage = this.router.url === '/login' || this.router.url.startsWith('/login');\r\n  }\r\n\r\n  async logout() {\r\n    try {\r\n      console.log('Logout clicked');\r\n\r\n      // Show confirmation\r\n      const confirmed = confirm('Are you sure you want to logout?');\r\n      if (!confirmed) {\r\n        return;\r\n      }\r\n\r\n      console.log('Starting logout process...');\r\n\r\n      // Sign out from Firebase\r\n      await this.firebaseService.signOut();\r\n      console.log('Firebase signOut successful');\r\n\r\n      // Clear any local storage\r\n      localStorage.clear();\r\n      console.log('Local storage cleared');\r\n\r\n      // Navigate to login page\r\n      await this.router.navigate(['/login']);\r\n      console.log('Successfully logged out and redirected to login');\r\n\r\n    } catch (error) {\r\n      console.error('Error during logout:', error);\r\n\r\n      // Force logout even if Firebase fails\r\n      localStorage.clear();\r\n      console.log('Forced logout - clearing storage and redirecting');\r\n\r\n      // Use window.location as fallback\r\n      window.location.href = '/login';\r\n    }\r\n  }\r\n}\r\n", "import { NgModule } from '@angular/core';\r\nimport { BrowserModule } from '@angular/platform-browser';\r\nimport { BrowserAnimationsModule } from '@angular/platform-browser/animations';\r\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\r\nimport { HttpClientModule } from '@angular/common/http';\r\n\r\n// Ionic\r\nimport { IonicModule } from '@ionic/angular';\r\n\r\n// Angular Material\r\nimport { MatToolbarModule } from '@angular/material/toolbar';\r\nimport { MatButtonModule } from '@angular/material/button';\r\nimport { MatCardModule } from '@angular/material/card';\r\nimport { MatInputModule } from '@angular/material/input';\r\nimport { MatFormFieldModule } from '@angular/material/form-field';\r\nimport { MatIconModule } from '@angular/material/icon';\r\nimport { MatSidenavModule } from '@angular/material/sidenav';\r\nimport { MatListModule } from '@angular/material/list';\r\nimport { MatGridListModule } from '@angular/material/grid-list';\r\nimport { MatMenuModule } from '@angular/material/menu';\r\nimport { MatTableModule } from '@angular/material/table';\r\nimport { MatPaginatorModule } from '@angular/material/paginator';\r\nimport { MatSortModule } from '@angular/material/sort';\r\nimport { MatDialogModule } from '@angular/material/dialog';\r\nimport { MatSnackBarModule } from '@angular/material/snack-bar';\r\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\r\nimport { MatChipsModule } from '@angular/material/chips';\r\nimport { MatSelectModule } from '@angular/material/select';\r\nimport { MatDatepickerModule } from '@angular/material/datepicker';\r\nimport { MatNativeDateModule } from '@angular/material/core';\r\nimport { LayoutModule } from '@angular/cdk/layout';\r\n\r\n// Firebase\r\nimport { initializeApp, provideFirebaseApp } from '@angular/fire/app';\r\nimport { provideAuth, getAuth } from '@angular/fire/auth';\r\nimport { provideFirestore, getFirestore } from '@angular/fire/firestore';\r\nimport { provideFunctions, getFunctions } from '@angular/fire/functions';\r\nimport { provideStorage, getStorage } from '@angular/fire/storage';\r\n\r\nimport { AppRoutingModule } from './app-routing.module';\r\nimport { AppComponent } from './app.component';\r\nimport { environment } from '../environments/environment';\r\n\r\n// Components\r\nimport { DashboardComponent } from './dashboard/dashboard.component';\r\nimport { LoginComponent } from './login/login.component';\r\nimport { LinkLawyerComponent } from './link-lawyer/link-lawyer.component';\r\n\r\nimport { CalendarComponent } from './calendar/calendar.component';\r\nimport { FilesComponent } from './files/files.component';\r\nimport { FinanceComponent } from './finance/finance.component';\r\nimport { ClientsComponent } from './clients/clients.component';\r\nimport { ActivityLogComponent } from './activity-log/activity-log.component';\r\n\r\n@NgModule({\r\n  declarations: [\r\n    AppComponent,\r\n    DashboardComponent,\r\n    LoginComponent,\r\n    LinkLawyerComponent,\r\n\r\n    CalendarComponent,\r\n    FilesComponent,\r\n    FinanceComponent,\r\n    ClientsComponent,\r\n    ActivityLogComponent\r\n  ],\r\n  imports: [\r\n    BrowserModule,\r\n    AppRoutingModule,\r\n    BrowserAnimationsModule,\r\n    FormsModule,\r\n    ReactiveFormsModule,\r\n    HttpClientModule,\r\n\r\n    // Ionic\r\n    IonicModule.forRoot(),\r\n\r\n    // Angular Material\r\n    MatToolbarModule,\r\n    MatButtonModule,\r\n    MatCardModule,\r\n    MatInputModule,\r\n    MatFormFieldModule,\r\n    MatIconModule,\r\n    MatSidenavModule,\r\n    MatListModule,\r\n    MatGridListModule,\r\n    MatMenuModule,\r\n    MatTableModule,\r\n    MatPaginatorModule,\r\n    MatSortModule,\r\n    MatDialogModule,\r\n    MatSnackBarModule,\r\n    MatProgressSpinnerModule,\r\n    MatChipsModule,\r\n    MatSelectModule,\r\n    MatDatepickerModule,\r\n    MatNativeDateModule,\r\n    LayoutModule\r\n\r\n  ],\r\n  providers: [\r\n    provideFirebaseApp(() => initializeApp(environment.firebase)),\r\n    provideAuth(() => getAuth()),\r\n    provideFirestore(() => getFirestore()),\r\n    provideFunctions(() => getFunctions()),\r\n    provideStorage(() => getStorage())\r\n  ],\r\n  bootstrap: [AppComponent]\r\n})\r\nexport class AppModule { }\r\n", "import { Component, OnInit } from '@angular/core';\r\nimport { ModalController, ToastController, AlertController } from '@ionic/angular';\r\nimport { FirebaseService, LawyerProfile } from '../services/firebase.service';\r\nimport { EnhancedAppointment } from '../models/scheduling.models';\r\n\r\ninterface Appointment {\r\n  id: string;\r\n  clientName: string;\r\n  type: string;\r\n  time: string;\r\n  date: Date;\r\n  status: 'confirmed' | 'pending' | 'cancelled';\r\n}\r\n\r\ninterface CalendarDay {\r\n  date: number;\r\n  isCurrentMonth: boolean;\r\n  isToday: boolean;\r\n  isSelected: boolean;\r\n  fullDate: Date;\r\n}\r\n\r\n@Component({\r\n  selector: 'app-calendar',\r\n  template: `\r\n    <div class=\"calendar-container\">\r\n      <div class=\"calendar-header\">\r\n        <div class=\"header-left\">\r\n          <h1>Scheduling</h1>\r\n          <div class=\"lawyer-selector\" *ngIf=\"linkedLawyers.length > 0\">\r\n            <label>Managing appointments for:</label>\r\n            <select [(ngModel)]=\"selectedLawyer\" (change)=\"onLawyerChange()\" class=\"lawyer-select\">\r\n              <option *ngFor=\"let lawyer of linkedLawyers\" [ngValue]=\"lawyer\">\r\n                {{ lawyer.name }} ({{ lawyer.rollNumber }})\r\n              </option>\r\n            </select>\r\n          </div>\r\n        </div>\r\n        <div class=\"header-right\">\r\n          <div class=\"workflow-info\">\r\n            <small>📋 Secretary creates → 🔄 Lawyer approves → ✅ Confirmed</small>\r\n          </div>\r\n          <button mat-raised-button color=\"primary\" (click)=\"openNewBookingDialog()\" class=\"header-new-booking-btn\" [disabled]=\"isLoading\">\r\n            <mat-icon>add</mat-icon>\r\n            {{ isLoading ? 'Creating...' : 'New Booking Request' }}\r\n          </button>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"calendar-content\">\r\n        <!-- Calendar Widget -->\r\n        <div class=\"calendar-widget\">\r\n          <div class=\"calendar-nav\">\r\n            <button mat-icon-button (click)=\"previousMonth()\">\r\n              <mat-icon>chevron_left</mat-icon>\r\n            </button>\r\n            <h3>{{ currentMonth }}</h3>\r\n            <button mat-icon-button (click)=\"nextMonth()\">\r\n              <mat-icon>chevron_right</mat-icon>\r\n            </button>\r\n          </div>\r\n\r\n          <div class=\"calendar-grid\">\r\n            <div class=\"calendar-header-row\">\r\n              <div class=\"day-header\" *ngFor=\"let day of weekDays\">{{ day }}</div>\r\n            </div>\r\n            <div class=\"calendar-body\">\r\n              <div class=\"calendar-row\" *ngFor=\"let week of calendarWeeks\">\r\n                <div \r\n                  class=\"calendar-day\" \r\n                  *ngFor=\"let day of week\"\r\n                  [class.other-month]=\"!day.isCurrentMonth\"\r\n                  [class.today]=\"day.isToday\"\r\n                  [class.selected]=\"day.isSelected\"\r\n                  (click)=\"selectDate(day)\">\r\n                  {{ day.date }}\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- Selected Date Appointments -->\r\n          <div class=\"selected-date-section\" *ngIf=\"selectedDate\">\r\n            <h4>Booking on {{ selectedDate | date:'fullDate' }}</h4>\r\n            <div class=\"appointments-list\">\r\n              <div \r\n                class=\"appointment-item\" \r\n                *ngFor=\"let appointment of getAppointmentsForDate(selectedDate)\">\r\n                <div class=\"appointment-time\">{{ appointment.time }}</div>\r\n                <div class=\"appointment-details\">\r\n                  <div class=\"appointment-type\">{{ appointment.type }}</div>\r\n                  <div class=\"appointment-client\">{{ appointment.clientName }}</div>\r\n                </div>\r\n                <div class=\"appointment-status\" [class]=\"appointment.status\">\r\n                  {{ appointment.status }}\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <button mat-raised-button color=\"primary\" class=\"new-booking-btn\" (click)=\"openNewBookingDialog()\">\r\n              + New Booking\r\n            </button>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- Upcoming Appointments -->\r\n        <div class=\"upcoming-appointments\">\r\n          <h3>Upcoming Appointments</h3>\r\n          <div class=\"appointment-card\" *ngFor=\"let appointment of upcomingAppointments\">\r\n            <div class=\"appointment-date\">\r\n              <div class=\"date-day\">{{ appointment.date | date:'dd' }}</div>\r\n              <div class=\"date-month\">{{ appointment.date | date:'MMM' }}</div>\r\n            </div>\r\n            <div class=\"appointment-info\">\r\n              <div class=\"appointment-title\">{{ appointment.type }}</div>\r\n              <div class=\"appointment-client\">{{ appointment.clientName }}</div>\r\n              <div class=\"appointment-time\">{{ appointment.time }}</div>\r\n            </div>\r\n            <div class=\"appointment-actions\">\r\n              <button mat-icon-button (click)=\"editAppointment(appointment)\">\r\n                <mat-icon>edit</mat-icon>\r\n              </button>\r\n              <button mat-icon-button color=\"warn\" (click)=\"deleteAppointment(appointment)\">\r\n                <mat-icon>delete</mat-icon>\r\n              </button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  `,\r\n  styles: [`\r\n    .calendar-container {\r\n      padding: 32px;\r\n      background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);\r\n      min-height: calc(100vh - 64px);\r\n      position: relative;\r\n    }\r\n\r\n    .calendar-container::before {\r\n      content: '';\r\n      position: absolute;\r\n      top: 0;\r\n      left: 0;\r\n      right: 0;\r\n      bottom: 0;\r\n      background:\r\n        radial-gradient(circle at 30% 30%, rgba(196, 154, 86, 0.08) 0%, transparent 50%),\r\n        radial-gradient(circle at 70% 70%, rgba(30, 41, 59, 0.03) 0%, transparent 50%);\r\n      pointer-events: none;\r\n    }\r\n\r\n    .calendar-header {\r\n      display: flex;\r\n      justify-content: space-between;\r\n      align-items: flex-start;\r\n      margin-bottom: 32px;\r\n      position: relative;\r\n      z-index: 1;\r\n      gap: 20px;\r\n    }\r\n\r\n    .header-left {\r\n      flex: 1;\r\n    }\r\n\r\n    .header-right {\r\n      display: flex;\r\n      flex-direction: column;\r\n      align-items: flex-end;\r\n      gap: 8px;\r\n    }\r\n\r\n    .lawyer-selector {\r\n      margin-top: 12px;\r\n      display: flex;\r\n      flex-direction: column;\r\n      gap: 8px;\r\n    }\r\n\r\n    .lawyer-selector label {\r\n      font-size: 14px;\r\n      color: #64748b;\r\n      font-weight: 500;\r\n    }\r\n\r\n    .lawyer-select {\r\n      padding: 8px 12px;\r\n      border: 2px solid #e2e8f0;\r\n      border-radius: 8px;\r\n      background: white;\r\n      font-size: 14px;\r\n      color: #1e293b;\r\n      min-width: 250px;\r\n      transition: all 0.3s ease;\r\n    }\r\n\r\n    .lawyer-select:focus {\r\n      outline: none;\r\n      border-color: #C49A56;\r\n      box-shadow: 0 0 0 3px rgba(196, 154, 86, 0.1);\r\n    }\r\n\r\n    .workflow-info {\r\n      font-size: 12px;\r\n      color: #64748b;\r\n      background: rgba(255, 255, 255, 0.8);\r\n      padding: 6px 12px;\r\n      border-radius: 6px;\r\n      border: 1px solid #e2e8f0;\r\n      white-space: nowrap;\r\n    }\r\n\r\n    .calendar-header h1 {\r\n      margin: 0;\r\n      font-size: 36px;\r\n      font-weight: 700;\r\n      color: #1e293b;\r\n      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\r\n      position: relative;\r\n    }\r\n\r\n    .calendar-header h1::after {\r\n      content: '';\r\n      position: absolute;\r\n      bottom: -8px;\r\n      left: 0;\r\n      width: 80px;\r\n      height: 4px;\r\n      background: linear-gradient(90deg, #C49A56 0%, #D4AF6A 100%);\r\n      border-radius: 2px;\r\n    }\r\n\r\n    .calendar-content {\r\n      display: grid;\r\n      grid-template-columns: 1fr 300px;\r\n      gap: 24px;\r\n    }\r\n\r\n    .calendar-widget {\r\n      background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);\r\n      border-radius: 24px;\r\n      padding: 32px;\r\n      box-shadow:\r\n        0 20px 60px rgba(0, 0, 0, 0.1),\r\n        0 8px 24px rgba(0, 0, 0, 0.05);\r\n      border: 1px solid rgba(226, 232, 240, 0.8);\r\n      position: relative;\r\n      overflow: hidden;\r\n      transition: all 0.3s ease;\r\n    }\r\n\r\n    .calendar-widget::before {\r\n      content: '';\r\n      position: absolute;\r\n      top: 0;\r\n      left: 0;\r\n      right: 0;\r\n      height: 6px;\r\n      background: linear-gradient(90deg, #C49A56 0%, #D4AF6A 50%, #C49A56 100%);\r\n    }\r\n\r\n    .calendar-widget:hover {\r\n      transform: translateY(-4px);\r\n      box-shadow:\r\n        0 30px 80px rgba(0, 0, 0, 0.15),\r\n        0 12px 32px rgba(0, 0, 0, 0.08);\r\n    }\r\n\r\n    .calendar-nav {\r\n      display: flex;\r\n      justify-content: space-between;\r\n      align-items: center;\r\n      margin-bottom: 20px;\r\n    }\r\n\r\n    .calendar-nav h3 {\r\n      margin: 0;\r\n      font-size: 18px;\r\n      font-weight: 600;\r\n    }\r\n\r\n    .calendar-grid {\r\n      border: 1px solid #e0e0e0;\r\n      border-radius: 8px;\r\n      overflow: hidden;\r\n    }\r\n\r\n    .calendar-header-row {\r\n      display: grid;\r\n      grid-template-columns: repeat(7, 1fr);\r\n      background: #f8f9fa;\r\n    }\r\n\r\n    .day-header {\r\n      padding: 12px;\r\n      text-align: center;\r\n      font-weight: 600;\r\n      font-size: 12px;\r\n      color: #666;\r\n      border-right: 1px solid #e0e0e0;\r\n    }\r\n\r\n    .calendar-body {\r\n      background: white;\r\n    }\r\n\r\n    .calendar-row {\r\n      display: grid;\r\n      grid-template-columns: repeat(7, 1fr);\r\n      border-bottom: 1px solid #e0e0e0;\r\n    }\r\n\r\n    .calendar-day {\r\n      padding: 12px;\r\n      text-align: center;\r\n      cursor: pointer;\r\n      border-right: 1px solid #e0e0e0;\r\n      transition: all 0.2s ease;\r\n      min-height: 40px;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n    }\r\n\r\n    .calendar-day:hover {\r\n      background: #f0f0f0;\r\n    }\r\n\r\n    .calendar-day.other-month {\r\n      color: #ccc;\r\n    }\r\n\r\n    .calendar-day.today {\r\n      background: #e3f2fd;\r\n      color: #1976d2;\r\n      font-weight: 600;\r\n    }\r\n\r\n    .calendar-day.selected {\r\n      background: #1976d2;\r\n      color: white;\r\n    }\r\n\r\n    .selected-date-section {\r\n      margin-top: 24px;\r\n      padding-top: 24px;\r\n      border-top: 1px solid #e0e0e0;\r\n    }\r\n\r\n    .selected-date-section h4 {\r\n      margin: 0 0 16px 0;\r\n      font-size: 16px;\r\n      font-weight: 600;\r\n    }\r\n\r\n    .appointment-item {\r\n      display: flex;\r\n      align-items: center;\r\n      gap: 12px;\r\n      padding: 12px;\r\n      background: #f8f9fa;\r\n      border-radius: 8px;\r\n      margin-bottom: 8px;\r\n    }\r\n\r\n    .appointment-time {\r\n      font-weight: 600;\r\n      color: #1976d2;\r\n      min-width: 60px;\r\n    }\r\n\r\n    .appointment-details {\r\n      flex: 1;\r\n    }\r\n\r\n    .appointment-type {\r\n      font-weight: 500;\r\n      margin-bottom: 2px;\r\n    }\r\n\r\n    .appointment-client {\r\n      font-size: 12px;\r\n      color: #666;\r\n    }\r\n\r\n    .appointment-status {\r\n      padding: 4px 8px;\r\n      border-radius: 4px;\r\n      font-size: 11px;\r\n      font-weight: 500;\r\n      text-transform: uppercase;\r\n    }\r\n\r\n    .appointment-status.confirmed {\r\n      background: #e8f5e8;\r\n      color: #2e7d32;\r\n    }\r\n\r\n    .appointment-status.pending {\r\n      background: #fff3e0;\r\n      color: #f57c00;\r\n    }\r\n\r\n    .new-booking-btn {\r\n      width: 100%;\r\n      margin-top: 16px;\r\n      background: linear-gradient(45deg, #C49A56, #D4AF6A) !important;\r\n      color: white !important;\r\n      font-weight: 600;\r\n      transition: all 0.3s ease;\r\n    }\r\n\r\n    .new-booking-btn:hover {\r\n      transform: translateY(-2px);\r\n      box-shadow: 0 4px 12px rgba(196, 154, 86, 0.3);\r\n    }\r\n\r\n    .header-new-booking-btn {\r\n      background: linear-gradient(45deg, #C49A56, #D4AF6A) !important;\r\n      color: white !important;\r\n      font-weight: 600;\r\n      padding: 12px 24px !important;\r\n      border-radius: 8px !important;\r\n      transition: all 0.3s ease;\r\n      box-shadow: 0 4px 12px rgba(196, 154, 86, 0.2);\r\n    }\r\n\r\n    .header-new-booking-btn:hover {\r\n      transform: translateY(-2px);\r\n      box-shadow: 0 8px 20px rgba(196, 154, 86, 0.3);\r\n    }\r\n\r\n    .upcoming-appointments {\r\n      background: white;\r\n      border-radius: 12px;\r\n      padding: 24px;\r\n      box-shadow: 0 2px 8px rgba(0,0,0,0.1);\r\n      height: fit-content;\r\n    }\r\n\r\n    .upcoming-appointments h3 {\r\n      margin: 0 0 20px 0;\r\n      font-size: 18px;\r\n      font-weight: 600;\r\n    }\r\n\r\n    .appointment-card {\r\n      display: flex;\r\n      align-items: center;\r\n      gap: 16px;\r\n      padding: 16px;\r\n      border: 1px solid #e0e0e0;\r\n      border-radius: 8px;\r\n      margin-bottom: 12px;\r\n      transition: all 0.2s ease;\r\n    }\r\n\r\n    .appointment-card:hover {\r\n      box-shadow: 0 2px 8px rgba(0,0,0,0.1);\r\n    }\r\n\r\n    .appointment-date {\r\n      text-align: center;\r\n      min-width: 50px;\r\n    }\r\n\r\n    .date-day {\r\n      font-size: 20px;\r\n      font-weight: 600;\r\n      color: #1976d2;\r\n    }\r\n\r\n    .date-month {\r\n      font-size: 12px;\r\n      color: #666;\r\n      text-transform: uppercase;\r\n    }\r\n\r\n    .appointment-info {\r\n      flex: 1;\r\n    }\r\n\r\n    .appointment-title {\r\n      font-weight: 500;\r\n      margin-bottom: 4px;\r\n    }\r\n\r\n    .appointment-actions {\r\n      display: flex;\r\n      gap: 4px;\r\n    }\r\n\r\n    @media (max-width: 768px) {\r\n      .calendar-content {\r\n        grid-template-columns: 1fr;\r\n      }\r\n    }\r\n  `]\r\n})\r\nexport class CalendarComponent implements OnInit {\r\n  currentMonth = 'July 2025';\r\n  selectedDate: Date | null = null;\r\n  weekDays = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];\r\n  calendarWeeks: CalendarDay[][] = [];\r\n\r\n  // Firebase integration\r\n  linkedLawyers: LawyerProfile[] = [];\r\n  selectedLawyer: LawyerProfile | null = null;\r\n  isLoading = false;\r\n\r\n  constructor(\r\n    private modalController: ModalController,\r\n    private toastController: ToastController,\r\n    private alertController: AlertController,\r\n    private firebaseService: FirebaseService\r\n  ) {}\r\n\r\n  appointments: Appointment[] = [\r\n    {\r\n      id: '1',\r\n      clientName: 'Kobe Bryant',\r\n      type: 'Consultation',\r\n      time: '10:00',\r\n      date: new Date(2025, 6, 1), // July 1, 2025\r\n      status: 'confirmed'\r\n    },\r\n    {\r\n      id: '2',\r\n      clientName: 'LeBron James',\r\n      type: 'Consultation',\r\n      time: '11:00',\r\n      date: new Date(2025, 6, 1), // July 1, 2025\r\n      status: 'confirmed'\r\n    },\r\n    {\r\n      id: '3',\r\n      clientName: 'Michael Jordan',\r\n      type: 'Case Review',\r\n      time: '14:00',\r\n      date: new Date(2025, 6, 2), // July 2, 2025\r\n      status: 'pending'\r\n    }\r\n  ];\r\n\r\n  upcomingAppointments: Appointment[] = [];\r\n\r\n  async ngOnInit() {\r\n    console.log('Calendar component initialized');\r\n    this.generateCalendar();\r\n    await this.loadLinkedLawyers();\r\n    await this.loadAppointments();\r\n    this.upcomingAppointments = this.appointments.slice(0, 5);\r\n    console.log('Initial appointments:', this.appointments);\r\n    console.log('Upcoming appointments:', this.upcomingAppointments);\r\n  }\r\n\r\n  async loadLinkedLawyers() {\r\n    const currentUser = this.firebaseService.getCurrentUser();\r\n    if (currentUser) {\r\n      try {\r\n        this.linkedLawyers = await this.firebaseService.getSecretaryLinkedLawyers(currentUser.uid);\r\n        if (this.linkedLawyers.length > 0) {\r\n          this.selectedLawyer = this.linkedLawyers[0]; // Select first lawyer by default\r\n        }\r\n        console.log('Linked lawyers loaded:', this.linkedLawyers);\r\n      } catch (error) {\r\n        console.error('Error loading linked lawyers:', error);\r\n        this.showToast('Error loading linked lawyers', 'danger');\r\n      }\r\n    }\r\n  }\r\n\r\n  async loadAppointments() {\r\n    const currentUser = this.firebaseService.getCurrentUser();\r\n    if (!currentUser) return;\r\n\r\n    this.isLoading = true;\r\n    try {\r\n      const firebaseAppointments = await this.firebaseService.getAppointmentsForSecretary(currentUser.uid);\r\n\r\n      // Convert Firebase appointments to local format\r\n      this.appointments = firebaseAppointments.map(apt => ({\r\n        id: apt.id || '',\r\n        clientName: apt.clientName,\r\n        type: apt.type,\r\n        time: apt.time,\r\n        date: new Date(apt.date),\r\n        status: apt.status as 'confirmed' | 'pending' | 'cancelled'\r\n      }));\r\n\r\n      console.log('Firebase appointments loaded:', this.appointments);\r\n    } catch (error) {\r\n      console.error('Error loading appointments from Firebase:', error);\r\n      this.showToast('Error loading appointments', 'danger');\r\n    } finally {\r\n      this.isLoading = false;\r\n    }\r\n  }\r\n\r\n  onLawyerChange() {\r\n    console.log('Selected lawyer changed to:', this.selectedLawyer);\r\n    if (this.selectedLawyer) {\r\n      this.showToast(`Now managing appointments for ${this.selectedLawyer.name}`, 'primary');\r\n      // Optionally reload appointments for the selected lawyer\r\n      this.loadAppointments();\r\n    }\r\n  }\r\n\r\n  generateCalendar() {\r\n    // Generate calendar grid for July 2025\r\n    const year = 2025;\r\n    const month = 6; // July (0-indexed)\r\n    \r\n    const firstDay = new Date(year, month, 1);\r\n    const lastDay = new Date(year, month + 1, 0);\r\n    const startDate = new Date(firstDay);\r\n    startDate.setDate(startDate.getDate() - firstDay.getDay() + 1); // Start from Monday\r\n    \r\n    this.calendarWeeks = [];\r\n    let currentDate = new Date(startDate);\r\n    \r\n    for (let week = 0; week < 6; week++) {\r\n      const weekDays = [];\r\n      for (let day = 0; day < 7; day++) {\r\n        weekDays.push({\r\n          date: currentDate.getDate(),\r\n          isCurrentMonth: currentDate.getMonth() === month,\r\n          isToday: this.isToday(currentDate),\r\n          isSelected: false,\r\n          fullDate: new Date(currentDate)\r\n        });\r\n        currentDate.setDate(currentDate.getDate() + 1);\r\n      }\r\n      this.calendarWeeks.push(weekDays);\r\n    }\r\n  }\r\n\r\n  isToday(date: Date): boolean {\r\n    const today = new Date();\r\n    return date.toDateString() === today.toDateString();\r\n  }\r\n\r\n  selectDate(day: any) {\r\n    console.log('Date selected:', day);\r\n\r\n    // Clear previous selection\r\n    this.calendarWeeks.forEach(week => {\r\n      week.forEach(d => d.isSelected = false);\r\n    });\r\n\r\n    // Set new selection\r\n    day.isSelected = true;\r\n    this.selectedDate = day.fullDate;\r\n\r\n    // Show feedback\r\n    this.showToast(`Selected ${this.selectedDate?.toDateString()}`, 'primary');\r\n    console.log('Selected date:', this.selectedDate);\r\n  }\r\n\r\n  getAppointmentsForDate(date: Date): Appointment[] {\r\n    return this.appointments.filter(apt => \r\n      apt.date.toDateString() === date.toDateString()\r\n    );\r\n  }\r\n\r\n  previousMonth() {\r\n    console.log('Previous month clicked');\r\n    this.showToast('Previous month navigation - Coming soon!', 'primary');\r\n  }\r\n\r\n  nextMonth() {\r\n    console.log('Next month clicked');\r\n    this.showToast('Next month navigation - Coming soon!', 'primary');\r\n  }\r\n\r\n  async openNewBookingDialog() {\r\n    console.log('New booking dialog opened');\r\n\r\n    if (this.linkedLawyers.length === 0) {\r\n      this.showToast('No linked lawyers found. Please link a lawyer first.', 'warning');\r\n      return;\r\n    }\r\n\r\n    const alert = await this.alertController.create({\r\n      header: 'New Booking Request',\r\n      message: 'Create a new appointment booking (requires lawyer approval)',\r\n      inputs: [\r\n        {\r\n          name: 'lawyer',\r\n          type: 'radio',\r\n          label: this.selectedLawyer?.name || 'Select Lawyer',\r\n          value: this.selectedLawyer?.uid || '',\r\n          checked: true\r\n        },\r\n        ...this.linkedLawyers.slice(1).map(lawyer => ({\r\n          name: 'lawyer',\r\n          type: 'radio' as const,\r\n          label: lawyer.name,\r\n          value: lawyer.uid,\r\n          checked: false\r\n        })),\r\n        {\r\n          name: 'clientName',\r\n          type: 'text',\r\n          placeholder: 'Client Name'\r\n        },\r\n        {\r\n          name: 'appointmentType',\r\n          type: 'text',\r\n          placeholder: 'Appointment Type (e.g., Consultation)'\r\n        },\r\n        {\r\n          name: 'time',\r\n          type: 'time',\r\n          placeholder: 'Time'\r\n        },\r\n        {\r\n          name: 'remarks',\r\n          type: 'textarea',\r\n          placeholder: 'Additional remarks (optional)'\r\n        }\r\n      ],\r\n      buttons: [\r\n        {\r\n          text: 'Cancel',\r\n          role: 'cancel'\r\n        },\r\n        {\r\n          text: 'Create Booking Request',\r\n          handler: (data) => {\r\n            this.createNewBooking(data);\r\n          }\r\n        }\r\n      ]\r\n    });\r\n\r\n    await alert.present();\r\n  }\r\n\r\n  async createNewBooking(data: any) {\r\n    if (!this.selectedDate) {\r\n      this.showToast('Please select a date first', 'warning');\r\n      return;\r\n    }\r\n\r\n    if (!data.clientName || !data.appointmentType || !data.time || !data.lawyer) {\r\n      this.showToast('Please fill in all required fields', 'warning');\r\n      return;\r\n    }\r\n\r\n    const currentUser = this.firebaseService.getCurrentUser();\r\n    if (!currentUser) {\r\n      this.showToast('Please log in to create appointments', 'danger');\r\n      return;\r\n    }\r\n\r\n    const selectedLawyer = this.linkedLawyers.find(l => l.uid === data.lawyer);\r\n    if (!selectedLawyer) {\r\n      this.showToast('Selected lawyer not found', 'danger');\r\n      return;\r\n    }\r\n\r\n    this.isLoading = true;\r\n    try {\r\n      const appointmentData: Omit<EnhancedAppointment, 'id'> = {\r\n        lawyerId: selectedLawyer.uid,\r\n        lawyerName: selectedLawyer.name,\r\n        clientName: data.clientName,\r\n        date: this.selectedDate.toISOString().split('T')[0], // Format: YYYY-MM-DD\r\n        time: data.time,\r\n        status: 'pending', // Always starts as pending for secretary-created appointments\r\n        type: data.appointmentType,\r\n        createdBy: 'secretary',\r\n        managedBy: currentUser.uid,\r\n        lastModifiedBy: currentUser.uid,\r\n        lastModifiedByRole: 'secretary',\r\n        remarks: data.remarks || '',\r\n        isUrgent: false,\r\n        createdAt: new Date(),\r\n        updatedAt: new Date()\r\n      };\r\n\r\n      const appointmentId = await this.firebaseService.createAppointment(appointmentData);\r\n\r\n      // Add to local array for immediate UI update\r\n      const newAppointment: Appointment = {\r\n        id: appointmentId,\r\n        clientName: data.clientName,\r\n        type: data.appointmentType,\r\n        time: data.time,\r\n        date: new Date(this.selectedDate),\r\n        status: 'pending'\r\n      };\r\n\r\n      this.appointments.push(newAppointment);\r\n      this.upcomingAppointments = this.appointments.slice(0, 5);\r\n\r\n      this.showToast(\r\n        `Appointment request created for ${data.clientName} with ${selectedLawyer.name}. Awaiting lawyer approval.`,\r\n        'success'\r\n      );\r\n      console.log('New appointment created in Firebase:', appointmentData);\r\n\r\n    } catch (error) {\r\n      console.error('Error creating appointment:', error);\r\n      this.showToast('Error creating appointment. Please try again.', 'danger');\r\n    } finally {\r\n      this.isLoading = false;\r\n    }\r\n  }\r\n\r\n  async editAppointment(appointment: Appointment) {\r\n    console.log('Edit appointment:', appointment);\r\n\r\n    const alert = await this.alertController.create({\r\n      header: 'Edit Appointment',\r\n      message: 'Update appointment details',\r\n      inputs: [\r\n        {\r\n          name: 'clientName',\r\n          type: 'text',\r\n          value: appointment.clientName,\r\n          placeholder: 'Client Name'\r\n        },\r\n        {\r\n          name: 'appointmentType',\r\n          type: 'text',\r\n          value: appointment.type,\r\n          placeholder: 'Appointment Type'\r\n        },\r\n        {\r\n          name: 'time',\r\n          type: 'time',\r\n          value: appointment.time,\r\n          placeholder: 'Time'\r\n        }\r\n      ],\r\n      buttons: [\r\n        {\r\n          text: 'Cancel',\r\n          role: 'cancel'\r\n        },\r\n        {\r\n          text: 'Update',\r\n          handler: (data) => {\r\n            this.updateAppointment(appointment.id, data);\r\n          }\r\n        }\r\n      ]\r\n    });\r\n\r\n    await alert.present();\r\n  }\r\n\r\n  async updateAppointment(appointmentId: string, data: any) {\r\n    const currentUser = this.firebaseService.getCurrentUser();\r\n    if (!currentUser) {\r\n      this.showToast('Please log in to update appointments', 'danger');\r\n      return;\r\n    }\r\n\r\n    this.isLoading = true;\r\n    try {\r\n      const updates: Partial<EnhancedAppointment> = {\r\n        clientName: data.clientName,\r\n        type: data.appointmentType,\r\n        time: data.time,\r\n        lastModifiedBy: currentUser.uid,\r\n        lastModifiedByRole: 'secretary',\r\n        updatedAt: new Date()\r\n      };\r\n\r\n      await this.firebaseService.updateAppointment(appointmentId, updates);\r\n\r\n      // Update local array\r\n      const index = this.appointments.findIndex(apt => apt.id === appointmentId);\r\n      if (index !== -1) {\r\n        this.appointments[index] = {\r\n          ...this.appointments[index],\r\n          clientName: data.clientName,\r\n          type: data.appointmentType,\r\n          time: data.time\r\n        };\r\n        this.upcomingAppointments = this.appointments.slice(0, 5);\r\n      }\r\n\r\n      this.showToast('Appointment updated successfully', 'success');\r\n    } catch (error) {\r\n      console.error('Error updating appointment:', error);\r\n      this.showToast('Error updating appointment. Please try again.', 'danger');\r\n    } finally {\r\n      this.isLoading = false;\r\n    }\r\n  }\r\n\r\n  async deleteAppointment(appointment: Appointment) {\r\n    console.log('Delete appointment:', appointment);\r\n\r\n    const alert = await this.alertController.create({\r\n      header: 'Delete Appointment',\r\n      message: `Are you sure you want to delete the appointment with ${appointment.clientName}?`,\r\n      buttons: [\r\n        {\r\n          text: 'Cancel',\r\n          role: 'cancel'\r\n        },\r\n        {\r\n          text: 'Delete',\r\n          role: 'destructive',\r\n          handler: () => {\r\n            this.confirmDeleteAppointment(appointment.id);\r\n          }\r\n        }\r\n      ]\r\n    });\r\n\r\n    await alert.present();\r\n  }\r\n\r\n  async confirmDeleteAppointment(appointmentId: string) {\r\n    const currentUser = this.firebaseService.getCurrentUser();\r\n    if (!currentUser) {\r\n      this.showToast('Please log in to delete appointments', 'danger');\r\n      return;\r\n    }\r\n\r\n    this.isLoading = true;\r\n    try {\r\n      await this.firebaseService.deleteAppointment(appointmentId, currentUser.uid, 'secretary');\r\n\r\n      // Remove from local array\r\n      const index = this.appointments.findIndex(apt => apt.id === appointmentId);\r\n      if (index !== -1) {\r\n        const deletedAppointment = this.appointments.splice(index, 1)[0];\r\n        this.upcomingAppointments = this.appointments.slice(0, 5);\r\n        this.showToast(`Appointment with ${deletedAppointment.clientName} deleted`, 'success');\r\n      }\r\n    } catch (error) {\r\n      console.error('Error deleting appointment:', error);\r\n      this.showToast('Error deleting appointment. Please try again.', 'danger');\r\n    } finally {\r\n      this.isLoading = false;\r\n    }\r\n  }\r\n\r\n  async showToast(message: string, color: string = 'primary') {\r\n    const toast = await this.toastController.create({\r\n      message: message,\r\n      duration: 3000,\r\n      color: color,\r\n      position: 'bottom'\r\n    });\r\n    toast.present();\r\n  }\r\n}\r\n", "import { Component, OnInit } from '@angular/core';\r\n\r\ninterface Client {\r\n  id: string;\r\n  name: string;\r\n  email: string;\r\n  phone: string;\r\n  status: 'active' | 'inactive' | 'pending';\r\n  retainerAmount: number;\r\n  joinDate: Date;\r\n  lastContact: Date;\r\n  caseCount: number;\r\n}\r\n\r\n@Component({\r\n  selector: 'app-clients',\r\n  template: `\r\n    <div class=\"clients-container\">\r\n      <div class=\"clients-header\">\r\n        <h1>Retainer Clients</h1>\r\n        <button mat-raised-button color=\"primary\" (click)=\"openAddClientDialog()\">\r\n          <mat-icon>add</mat-icon>\r\n          Add Client\r\n        </button>\r\n      </div>\r\n\r\n      <div class=\"clients-content\">\r\n        <!-- Client Statistics -->\r\n        <div class=\"stats-section\">\r\n          <div class=\"stat-card\">\r\n            <div class=\"stat-icon active\">\r\n              <mat-icon>person</mat-icon>\r\n            </div>\r\n            <div class=\"stat-content\">\r\n              <div class=\"stat-value\">{{ getClientCount('active') }}</div>\r\n              <div class=\"stat-label\">Active Clients</div>\r\n            </div>\r\n          </div>\r\n\r\n          <div class=\"stat-card\">\r\n            <div class=\"stat-icon pending\">\r\n              <mat-icon>schedule</mat-icon>\r\n            </div>\r\n            <div class=\"stat-content\">\r\n              <div class=\"stat-value\">{{ getClientCount('pending') }}</div>\r\n              <div class=\"stat-label\">Pending Clients</div>\r\n            </div>\r\n          </div>\r\n\r\n          <div class=\"stat-card\">\r\n            <div class=\"stat-icon revenue\">\r\n              <mat-icon>account_balance_wallet</mat-icon>\r\n            </div>\r\n            <div class=\"stat-content\">\r\n              <div class=\"stat-value\">₱{{ getTotalRetainer() | number }}</div>\r\n              <div class=\"stat-label\">Total Retainer</div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- Clients List -->\r\n        <div class=\"clients-list-section\">\r\n          <div class=\"list-header\">\r\n            <h3>Client List</h3>\r\n            <div class=\"list-controls\">\r\n              <mat-form-field appearance=\"outline\" class=\"search-field\">\r\n                <mat-icon matPrefix>search</mat-icon>\r\n                <input matInput placeholder=\"Search clients\" [(ngModel)]=\"searchTerm\" (input)=\"filterClients()\">\r\n              </mat-form-field>\r\n              <mat-form-field appearance=\"outline\" class=\"filter-field\">\r\n                <mat-select [(value)]=\"statusFilter\" (selectionChange)=\"filterClients()\">\r\n                  <mat-option value=\"\">All Status</mat-option>\r\n                  <mat-option value=\"active\">Active</mat-option>\r\n                  <mat-option value=\"pending\">Pending</mat-option>\r\n                  <mat-option value=\"inactive\">Inactive</mat-option>\r\n                </mat-select>\r\n              </mat-form-field>\r\n            </div>\r\n          </div>\r\n\r\n          <div class=\"clients-grid\">\r\n            <div class=\"client-card\" *ngFor=\"let client of filteredClients\">\r\n              <div class=\"client-header\">\r\n                <div class=\"client-avatar\">\r\n                  <mat-icon>person</mat-icon>\r\n                </div>\r\n                <div class=\"client-status\" [class]=\"client.status\">\r\n                  <div class=\"status-dot\"></div>\r\n                  <span>{{ client.status | titlecase }}</span>\r\n                </div>\r\n              </div>\r\n\r\n              <div class=\"client-info\">\r\n                <h4 class=\"client-name\">{{ client.name }}</h4>\r\n                <div class=\"client-contact\">\r\n                  <div class=\"contact-item\">\r\n                    <mat-icon>email</mat-icon>\r\n                    <span>{{ client.email }}</span>\r\n                  </div>\r\n                  <div class=\"contact-item\">\r\n                    <mat-icon>phone</mat-icon>\r\n                    <span>{{ client.phone }}</span>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              <div class=\"client-details\">\r\n                <div class=\"detail-row\">\r\n                  <span class=\"detail-label\">Retainer:</span>\r\n                  <span class=\"detail-value\">₱{{ client.retainerAmount | number }}</span>\r\n                </div>\r\n                <div class=\"detail-row\">\r\n                  <span class=\"detail-label\">Cases:</span>\r\n                  <span class=\"detail-value\">{{ client.caseCount }}</span>\r\n                </div>\r\n                <div class=\"detail-row\">\r\n                  <span class=\"detail-label\">Joined:</span>\r\n                  <span class=\"detail-value\">{{ client.joinDate | date:'MMM yyyy' }}</span>\r\n                </div>\r\n                <div class=\"detail-row\">\r\n                  <span class=\"detail-label\">Last Contact:</span>\r\n                  <span class=\"detail-value\">{{ getTimeAgo(client.lastContact) }}</span>\r\n                </div>\r\n              </div>\r\n\r\n              <div class=\"client-actions\">\r\n                <button mat-icon-button (click)=\"viewClient(client)\">\r\n                  <mat-icon>visibility</mat-icon>\r\n                </button>\r\n                <button mat-icon-button (click)=\"editClient(client)\">\r\n                  <mat-icon>edit</mat-icon>\r\n                </button>\r\n                <button mat-icon-button (click)=\"contactClient(client)\">\r\n                  <mat-icon>message</mat-icon>\r\n                </button>\r\n                <button mat-icon-button [matMenuTriggerFor]=\"clientMenu\">\r\n                  <mat-icon>more_vert</mat-icon>\r\n                </button>\r\n                <mat-menu #clientMenu=\"matMenu\">\r\n                  <button mat-menu-item (click)=\"viewCases(client)\">\r\n                    <mat-icon>folder</mat-icon>\r\n                    <span>View Cases</span>\r\n                  </button>\r\n                  <button mat-menu-item (click)=\"viewTransactions(client)\">\r\n                    <mat-icon>receipt</mat-icon>\r\n                    <span>View Transactions</span>\r\n                  </button>\r\n                  <button mat-menu-item (click)=\"deactivateClient(client)\">\r\n                    <mat-icon>block</mat-icon>\r\n                    <span>Deactivate</span>\r\n                  </button>\r\n                </mat-menu>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  `,\r\n  styles: [`\r\n    .clients-container {\r\n      padding: 32px;\r\n      background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);\r\n      min-height: calc(100vh - 64px);\r\n      position: relative;\r\n    }\r\n\r\n    .clients-container::before {\r\n      content: '';\r\n      position: absolute;\r\n      top: 0;\r\n      left: 0;\r\n      right: 0;\r\n      bottom: 0;\r\n      background:\r\n        radial-gradient(circle at 20% 20%, rgba(59, 130, 246, 0.05) 0%, transparent 50%),\r\n        radial-gradient(circle at 80% 80%, rgba(196, 154, 86, 0.03) 0%, transparent 50%);\r\n      pointer-events: none;\r\n    }\r\n\r\n    .clients-header {\r\n      display: flex;\r\n      justify-content: space-between;\r\n      align-items: center;\r\n      margin-bottom: 40px;\r\n      position: relative;\r\n      z-index: 1;\r\n    }\r\n\r\n    .clients-header h1 {\r\n      margin: 0;\r\n      font-size: 36px;\r\n      font-weight: 700;\r\n      color: #1e293b;\r\n      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\r\n      position: relative;\r\n    }\r\n\r\n    .clients-header h1::after {\r\n      content: '';\r\n      position: absolute;\r\n      bottom: -8px;\r\n      left: 0;\r\n      width: 80px;\r\n      height: 4px;\r\n      background: linear-gradient(90deg, #3b82f6 0%, #2563eb 100%);\r\n      border-radius: 2px;\r\n    }\r\n\r\n    .clients-content {\r\n      display: flex;\r\n      flex-direction: column;\r\n      gap: 24px;\r\n    }\r\n\r\n    .stats-section {\r\n      display: grid;\r\n      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\r\n      gap: 20px;\r\n    }\r\n\r\n    .stat-card {\r\n      background: white;\r\n      border-radius: 12px;\r\n      padding: 24px;\r\n      box-shadow: 0 2px 8px rgba(0,0,0,0.1);\r\n      display: flex;\r\n      align-items: center;\r\n      gap: 16px;\r\n    }\r\n\r\n    .stat-icon {\r\n      width: 60px;\r\n      height: 60px;\r\n      border-radius: 50%;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n    }\r\n\r\n    .stat-icon.active {\r\n      background: #e8f5e8;\r\n      color: #2e7d32;\r\n    }\r\n\r\n    .stat-icon.pending {\r\n      background: #fff3e0;\r\n      color: #f57c00;\r\n    }\r\n\r\n    .stat-icon.revenue {\r\n      background: #e3f2fd;\r\n      color: #1976d2;\r\n    }\r\n\r\n    .stat-icon mat-icon {\r\n      font-size: 28px;\r\n      width: 28px;\r\n      height: 28px;\r\n    }\r\n\r\n    .stat-content {\r\n      flex: 1;\r\n    }\r\n\r\n    .stat-value {\r\n      font-size: 24px;\r\n      font-weight: 600;\r\n      color: #333;\r\n      margin-bottom: 4px;\r\n    }\r\n\r\n    .stat-label {\r\n      font-size: 14px;\r\n      color: #666;\r\n    }\r\n\r\n    .clients-list-section {\r\n      background: white;\r\n      border-radius: 12px;\r\n      padding: 24px;\r\n      box-shadow: 0 2px 8px rgba(0,0,0,0.1);\r\n    }\r\n\r\n    .list-header {\r\n      display: flex;\r\n      justify-content: space-between;\r\n      align-items: center;\r\n      margin-bottom: 24px;\r\n    }\r\n\r\n    .list-header h3 {\r\n      margin: 0;\r\n      font-size: 18px;\r\n      font-weight: 600;\r\n    }\r\n\r\n    .list-controls {\r\n      display: flex;\r\n      gap: 16px;\r\n    }\r\n\r\n    .search-field,\r\n    .filter-field {\r\n      width: 200px;\r\n    }\r\n\r\n    .clients-grid {\r\n      display: grid;\r\n      grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));\r\n      gap: 20px;\r\n    }\r\n\r\n    .client-card {\r\n      border: 1px solid rgba(226, 232, 240, 0.8);\r\n      border-radius: 20px;\r\n      padding: 28px;\r\n      transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);\r\n      background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);\r\n      position: relative;\r\n      overflow: hidden;\r\n    }\r\n\r\n    .client-card::before {\r\n      content: '';\r\n      position: absolute;\r\n      top: 0;\r\n      left: 0;\r\n      right: 0;\r\n      height: 4px;\r\n      background: linear-gradient(90deg, #3b82f6 0%, #2563eb 100%);\r\n      transform: scaleX(0);\r\n      transition: transform 0.3s ease;\r\n    }\r\n\r\n    .client-card:hover::before {\r\n      transform: scaleX(1);\r\n    }\r\n\r\n    .client-card:hover {\r\n      border-color: #3b82f6;\r\n      box-shadow:\r\n        0 20px 60px rgba(59, 130, 246, 0.15),\r\n        0 8px 24px rgba(59, 130, 246, 0.08);\r\n      background: linear-gradient(135deg, #ffffff 0%, #f0f9ff 100%);\r\n      transform: translateY(-8px);\r\n    }\r\n\r\n    .client-header {\r\n      display: flex;\r\n      justify-content: space-between;\r\n      align-items: center;\r\n      margin-bottom: 16px;\r\n    }\r\n\r\n    .client-avatar {\r\n      width: 50px;\r\n      height: 50px;\r\n      border-radius: 50%;\r\n      background: linear-gradient(135deg, #C49A56 0%, #B8935A 100%);\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n      color: white;\r\n    }\r\n\r\n    .client-status {\r\n      display: flex;\r\n      align-items: center;\r\n      gap: 6px;\r\n      padding: 4px 8px;\r\n      border-radius: 12px;\r\n      font-size: 11px;\r\n      font-weight: 500;\r\n      text-transform: uppercase;\r\n    }\r\n\r\n    .client-status.active {\r\n      background: #e8f5e8;\r\n      color: #2e7d32;\r\n    }\r\n\r\n    .client-status.pending {\r\n      background: #fff3e0;\r\n      color: #f57c00;\r\n    }\r\n\r\n    .client-status.inactive {\r\n      background: #ffebee;\r\n      color: #d32f2f;\r\n    }\r\n\r\n    .status-dot {\r\n      width: 6px;\r\n      height: 6px;\r\n      border-radius: 50%;\r\n      background: currentColor;\r\n    }\r\n\r\n    .client-info {\r\n      margin-bottom: 16px;\r\n    }\r\n\r\n    .client-name {\r\n      margin: 0 0 8px 0;\r\n      font-size: 16px;\r\n      font-weight: 600;\r\n      color: #333;\r\n    }\r\n\r\n    .client-contact {\r\n      display: flex;\r\n      flex-direction: column;\r\n      gap: 4px;\r\n    }\r\n\r\n    .contact-item {\r\n      display: flex;\r\n      align-items: center;\r\n      gap: 8px;\r\n      font-size: 12px;\r\n      color: #666;\r\n    }\r\n\r\n    .contact-item mat-icon {\r\n      font-size: 14px;\r\n      width: 14px;\r\n      height: 14px;\r\n    }\r\n\r\n    .client-details {\r\n      margin-bottom: 16px;\r\n      padding-top: 16px;\r\n      border-top: 1px solid #e0e0e0;\r\n    }\r\n\r\n    .detail-row {\r\n      display: flex;\r\n      justify-content: space-between;\r\n      margin-bottom: 8px;\r\n      font-size: 12px;\r\n    }\r\n\r\n    .detail-label {\r\n      color: #666;\r\n    }\r\n\r\n    .detail-value {\r\n      font-weight: 500;\r\n      color: #333;\r\n    }\r\n\r\n    .client-actions {\r\n      display: flex;\r\n      justify-content: flex-end;\r\n      gap: 4px;\r\n      padding-top: 16px;\r\n      border-top: 1px solid #e0e0e0;\r\n    }\r\n\r\n    .client-actions button {\r\n      width: 32px;\r\n      height: 32px;\r\n    }\r\n\r\n    .client-actions mat-icon {\r\n      font-size: 16px;\r\n      width: 16px;\r\n      height: 16px;\r\n    }\r\n\r\n    @media (max-width: 768px) {\r\n      .clients-header {\r\n        flex-direction: column;\r\n        gap: 16px;\r\n        align-items: stretch;\r\n      }\r\n\r\n      .list-header {\r\n        flex-direction: column;\r\n        gap: 16px;\r\n        align-items: stretch;\r\n      }\r\n\r\n      .list-controls {\r\n        flex-direction: column;\r\n      }\r\n\r\n      .search-field,\r\n      .filter-field {\r\n        width: 100%;\r\n      }\r\n\r\n      .clients-grid {\r\n        grid-template-columns: 1fr;\r\n      }\r\n    }\r\n  `]\r\n})\r\nexport class ClientsComponent implements OnInit {\r\n  searchTerm = '';\r\n  statusFilter = '';\r\n  \r\n  clients: Client[] = [\r\n    {\r\n      id: '1',\r\n      name: 'Amado Cruz',\r\n      email: '<EMAIL>',\r\n      phone: '+63 ************',\r\n      status: 'active',\r\n      retainerAmount: 50000,\r\n      joinDate: new Date(2024, 8, 15), // September 15, 2024\r\n      lastContact: new Date(2025, 6, 10), // July 10, 2025\r\n      caseCount: 3\r\n    },\r\n    {\r\n      id: '2',\r\n      name: 'Clara Mendoza',\r\n      email: '<EMAIL>',\r\n      phone: '+63 ************',\r\n      status: 'active',\r\n      retainerAmount: 75000,\r\n      joinDate: new Date(2024, 10, 20), // November 20, 2024\r\n      lastContact: new Date(2025, 6, 8), // July 8, 2025\r\n      caseCount: 2\r\n    },\r\n    {\r\n      id: '3',\r\n      name: 'Kobe Bryant',\r\n      email: '<EMAIL>',\r\n      phone: '+63 ************',\r\n      status: 'active',\r\n      retainerAmount: 100000,\r\n      joinDate: new Date(2024, 11, 5), // December 5, 2024\r\n      lastContact: new Date(2025, 6, 12), // July 12, 2025\r\n      caseCount: 1\r\n    },\r\n    {\r\n      id: '4',\r\n      name: 'Maria Santos',\r\n      email: '<EMAIL>',\r\n      phone: '+63 ************',\r\n      status: 'pending',\r\n      retainerAmount: 60000,\r\n      joinDate: new Date(2025, 6, 1), // July 1, 2025\r\n      lastContact: new Date(2025, 6, 1), // July 1, 2025\r\n      caseCount: 0\r\n    }\r\n  ];\r\n\r\n  filteredClients: Client[] = [];\r\n\r\n  ngOnInit() {\r\n    this.filteredClients = [...this.clients];\r\n  }\r\n\r\n  getClientCount(status: string): number {\r\n    return this.clients.filter(client => client.status === status).length;\r\n  }\r\n\r\n  getTotalRetainer(): number {\r\n    return this.clients\r\n      .filter(client => client.status === 'active')\r\n      .reduce((total, client) => total + client.retainerAmount, 0);\r\n  }\r\n\r\n  filterClients() {\r\n    let filtered = [...this.clients];\r\n\r\n    // Filter by search term\r\n    if (this.searchTerm) {\r\n      filtered = filtered.filter(client => \r\n        client.name.toLowerCase().includes(this.searchTerm.toLowerCase()) ||\r\n        client.email.toLowerCase().includes(this.searchTerm.toLowerCase())\r\n      );\r\n    }\r\n\r\n    // Filter by status\r\n    if (this.statusFilter) {\r\n      filtered = filtered.filter(client => client.status === this.statusFilter);\r\n    }\r\n\r\n    this.filteredClients = filtered;\r\n  }\r\n\r\n  getTimeAgo(date: Date): string {\r\n    const now = new Date();\r\n    const diffTime = Math.abs(now.getTime() - date.getTime());\r\n    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\r\n    \r\n    if (diffDays === 1) {\r\n      return 'Today';\r\n    } else if (diffDays <= 7) {\r\n      return `${diffDays} days ago`;\r\n    } else {\r\n      return date.toLocaleDateString();\r\n    }\r\n  }\r\n\r\n  openAddClientDialog() {\r\n    // Implement add client dialog\r\n  }\r\n\r\n  viewClient(client: Client) {\r\n    // Implement view client details\r\n  }\r\n\r\n  editClient(client: Client) {\r\n    // Implement edit client\r\n  }\r\n\r\n  contactClient(client: Client) {\r\n    // Implement contact client\r\n  }\r\n\r\n  viewCases(client: Client) {\r\n    // Implement view client cases\r\n  }\r\n\r\n  viewTransactions(client: Client) {\r\n    // Implement view client transactions\r\n  }\r\n\r\n  deactivateClient(client: Client) {\r\n    // Implement deactivate client\r\n  }\r\n}\r\n", "import { Component, OnInit } from '@angular/core';\r\n\r\n@Component({\r\n  selector: 'app-dashboard',\r\n  template: `\r\n    <div class=\"dashboard-container\">\r\n      <!-- Left Column: Scheduling -->\r\n      <div class=\"left-column\">\r\n        <div class=\"scheduling-section\">\r\n          <h2>Scheduling</h2>\r\n\r\n          <!-- Calendar Widget -->\r\n          <div class=\"calendar-widget\">\r\n            <div class=\"calendar-header\">\r\n              <button class=\"nav-btn\" (click)=\"previousMonth()\">‹</button>\r\n              <h3>{{ currentMonth }}</h3>\r\n              <button class=\"nav-btn\" (click)=\"nextMonth()\">›</button>\r\n            </div>\r\n\r\n            <div class=\"calendar-grid\">\r\n              <div class=\"day-header\" *ngFor=\"let day of dayHeaders\">{{ day }}</div>\r\n              <div\r\n                class=\"calendar-day\"\r\n                *ngFor=\"let day of calendarDays\"\r\n                [class.other-month]=\"day.otherMonth\"\r\n                [class.today]=\"day.isToday\"\r\n                [class.has-appointment]=\"day.hasAppointment\"\r\n                (click)=\"selectDate(day)\">\r\n                {{ day.date }}\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- Booking Section -->\r\n          <div class=\"booking-section\">\r\n            <h4>Booking on {{ selectedDateString }}</h4>\r\n            <div class=\"appointment-list\">\r\n              <div class=\"appointment-item\" *ngFor=\"let appointment of todayAppointments\">\r\n                <div class=\"appointment-info\">\r\n                  <div class=\"appointment-type\">{{ appointment.type }}</div>\r\n                  <div class=\"appointment-client\">{{ appointment.client }}</div>\r\n                </div>\r\n                <div class=\"appointment-time\">{{ appointment.time }}</div>\r\n              </div>\r\n            </div>\r\n            <button class=\"new-booking-btn\">+ New Booking</button>\r\n          </div>\r\n\r\n          <!-- Retainer Clients -->\r\n          <div class=\"retainer-section\">\r\n            <div class=\"section-header\">\r\n              <h4>Retainer Clients</h4>\r\n              <button class=\"add-client-btn\">+ Add Client</button>\r\n            </div>\r\n            <div class=\"client-list\">\r\n              <div class=\"client-item\" *ngFor=\"let client of retainerClients\">\r\n                <div class=\"client-status\" [class]=\"client.status\"></div>\r\n                <span>{{ client.name }}</span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- Right Column: Files & Finance -->\r\n      <div class=\"right-column\">\r\n        <!-- Files Section -->\r\n        <div class=\"files-section\">\r\n          <div class=\"section-header\">\r\n            <h2>Files</h2>\r\n            <div class=\"search-box\">\r\n              <input type=\"text\" placeholder=\"Search a document\" [(ngModel)]=\"searchTerm\">\r\n              <span class=\"search-icon\">🔍</span>\r\n            </div>\r\n          </div>\r\n\r\n          <div class=\"file-categories\">\r\n            <div class=\"file-category\">\r\n              <div class=\"folder-icon\">📁</div>\r\n              <span>Disclosures</span>\r\n            </div>\r\n            <div class=\"file-category\">\r\n              <div class=\"folder-icon\">📁</div>\r\n              <span>Evidence</span>\r\n            </div>\r\n            <div class=\"file-category\">\r\n              <div class=\"folder-icon\">📁</div>\r\n              <span>Receipts</span>\r\n            </div>\r\n            <div class=\"file-category\">\r\n              <div class=\"folder-icon\">📁</div>\r\n              <span>Contracts</span>\r\n            </div>\r\n          </div>\r\n\r\n          <div class=\"recent-documents\">\r\n            <h4>Recent documents</h4>\r\n            <div class=\"document-item\" *ngFor=\"let doc of recentDocuments\">\r\n              <div class=\"doc-icon\">📄</div>\r\n              <div class=\"doc-info\">\r\n                <div class=\"doc-name\">{{ doc.name }}</div>\r\n                <div class=\"doc-date\">{{ doc.date }}</div>\r\n              </div>\r\n              <button class=\"doc-action\">›</button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- Finance Section -->\r\n        <div class=\"finance-section\">\r\n          <div class=\"section-header\">\r\n            <h2>Finance</h2>\r\n            <button class=\"new-transaction-btn\">+ New Transaction</button>\r\n          </div>\r\n\r\n          <div class=\"chart-container\">\r\n            <div class=\"chart-bars\">\r\n              <div class=\"bar\" *ngFor=\"let bar of chartData\" [style.height.%]=\"bar.height\" [style.background]=\"bar.color\"></div>\r\n            </div>\r\n            <div class=\"chart-labels\">\r\n              <span *ngFor=\"let label of chartLabels\">{{ label }}</span>\r\n            </div>\r\n            <div class=\"chart-period\">This year</div>\r\n          </div>\r\n\r\n          <div class=\"recent-transactions\">\r\n            <h4>Recent Transactions</h4>\r\n            <div class=\"transaction-item\" *ngFor=\"let transaction of recentTransactions\">\r\n              <div class=\"transaction-amount\">{{ transaction.amount }}</div>\r\n              <div class=\"transaction-info\">\r\n                <div class=\"transaction-type\">{{ transaction.type }}</div>\r\n                <div class=\"transaction-date\">{{ transaction.date }}</div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  `,\r\n  styles: [`\r\n    .dashboard-container {\r\n      display: flex;\r\n      gap: 24px;\r\n      padding: 24px;\r\n      background: #f8f9fa;\r\n      min-height: 100vh;\r\n    }\r\n\r\n    .left-column {\r\n      flex: 1;\r\n      max-width: 400px;\r\n    }\r\n\r\n    .right-column {\r\n      flex: 1;\r\n      display: flex;\r\n      flex-direction: column;\r\n      gap: 24px;\r\n    }\r\n\r\n    .scheduling-section,\r\n    .files-section,\r\n    .finance-section {\r\n      background: white;\r\n      border-radius: 12px;\r\n      padding: 24px;\r\n      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n    }\r\n\r\n    h2 {\r\n      font-size: 24px;\r\n      font-weight: 600;\r\n      margin: 0 0 20px 0;\r\n      color: #333;\r\n    }\r\n\r\n    h3, h4 {\r\n      font-size: 16px;\r\n      font-weight: 600;\r\n      margin: 0 0 12px 0;\r\n      color: #333;\r\n    }\r\n\r\n    .calendar-widget {\r\n      margin-bottom: 24px;\r\n    }\r\n\r\n    .calendar-header {\r\n      display: flex;\r\n      justify-content: space-between;\r\n      align-items: center;\r\n      margin-bottom: 16px;\r\n    }\r\n\r\n    .nav-btn {\r\n      background: none;\r\n      border: none;\r\n      font-size: 20px;\r\n      cursor: pointer;\r\n      padding: 8px;\r\n      border-radius: 4px;\r\n    }\r\n\r\n    .nav-btn:hover {\r\n      background: #f0f0f0;\r\n    }\r\n\r\n    .calendar-grid {\r\n      display: grid;\r\n      grid-template-columns: repeat(7, 1fr);\r\n      gap: 1px;\r\n      background: #e0e0e0;\r\n      border-radius: 8px;\r\n      overflow: hidden;\r\n    }\r\n\r\n    .day-header {\r\n      background: #f5f5f5;\r\n      padding: 8px;\r\n      text-align: center;\r\n      font-size: 12px;\r\n      font-weight: 600;\r\n      color: #666;\r\n    }\r\n\r\n    .calendar-day {\r\n      background: white;\r\n      padding: 12px 8px;\r\n      text-align: center;\r\n      cursor: pointer;\r\n      font-size: 14px;\r\n      min-height: 40px;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n    }\r\n\r\n    .calendar-day:hover {\r\n      background: #f0f0f0;\r\n    }\r\n\r\n    .calendar-day.today {\r\n      background: #1976d2;\r\n      color: white;\r\n    }\r\n\r\n    .calendar-day.other-month {\r\n      color: #ccc;\r\n    }\r\n\r\n    .calendar-day.has-appointment {\r\n      background: #e3f2fd;\r\n      color: #1976d2;\r\n    }\r\n\r\n    .booking-section {\r\n      margin-bottom: 24px;\r\n    }\r\n\r\n    .appointment-item {\r\n      display: flex;\r\n      justify-content: space-between;\r\n      align-items: center;\r\n      padding: 12px 0;\r\n      border-bottom: 1px solid #f0f0f0;\r\n    }\r\n\r\n    .appointment-type {\r\n      font-weight: 600;\r\n      color: #333;\r\n    }\r\n\r\n    .appointment-client {\r\n      color: #666;\r\n      font-size: 14px;\r\n    }\r\n\r\n    .appointment-time {\r\n      font-weight: 600;\r\n      color: #1976d2;\r\n    }\r\n\r\n    .new-booking-btn {\r\n      width: 100%;\r\n      background: #1976d2;\r\n      color: white;\r\n      border: none;\r\n      padding: 12px;\r\n      border-radius: 8px;\r\n      font-weight: 600;\r\n      cursor: pointer;\r\n      margin-top: 16px;\r\n    }\r\n\r\n    .new-booking-btn:hover {\r\n      background: #1565c0;\r\n    }\r\n\r\n    .section-header {\r\n      display: flex;\r\n      justify-content: space-between;\r\n      align-items: center;\r\n      margin-bottom: 20px;\r\n    }\r\n\r\n    .add-client-btn {\r\n      background: none;\r\n      border: 1px solid #ddd;\r\n      padding: 6px 12px;\r\n      border-radius: 6px;\r\n      font-size: 12px;\r\n      cursor: pointer;\r\n      color: #666;\r\n    }\r\n\r\n    .client-list {\r\n      display: flex;\r\n      flex-direction: column;\r\n      gap: 8px;\r\n    }\r\n\r\n    .client-item {\r\n      display: flex;\r\n      align-items: center;\r\n      gap: 8px;\r\n      padding: 8px 0;\r\n    }\r\n\r\n    .client-status {\r\n      width: 8px;\r\n      height: 8px;\r\n      border-radius: 50%;\r\n      background: #4caf50;\r\n    }\r\n\r\n    .client-status.inactive {\r\n      background: #f44336;\r\n    }\r\n\r\n    .search-box {\r\n      position: relative;\r\n      width: 250px;\r\n    }\r\n\r\n    .search-box input {\r\n      width: 100%;\r\n      padding: 8px 12px 8px 36px;\r\n      border: 1px solid #ddd;\r\n      border-radius: 6px;\r\n      font-size: 14px;\r\n    }\r\n\r\n    .search-icon {\r\n      position: absolute;\r\n      left: 12px;\r\n      top: 50%;\r\n      transform: translateY(-50%);\r\n      color: #666;\r\n    }\r\n\r\n    .file-categories {\r\n      display: grid;\r\n      grid-template-columns: 1fr 1fr;\r\n      gap: 16px;\r\n      margin-bottom: 24px;\r\n    }\r\n\r\n    .file-category {\r\n      display: flex;\r\n      align-items: center;\r\n      gap: 12px;\r\n      padding: 16px;\r\n      border: 1px solid #e0e0e0;\r\n      border-radius: 8px;\r\n      cursor: pointer;\r\n    }\r\n\r\n    .file-category:hover {\r\n      background: #f5f5f5;\r\n    }\r\n\r\n    .folder-icon {\r\n      font-size: 24px;\r\n    }\r\n\r\n    .document-item {\r\n      display: flex;\r\n      align-items: center;\r\n      gap: 12px;\r\n      padding: 12px 0;\r\n      border-bottom: 1px solid #f0f0f0;\r\n    }\r\n\r\n    .doc-icon {\r\n      font-size: 20px;\r\n    }\r\n\r\n    .doc-info {\r\n      flex: 1;\r\n    }\r\n\r\n    .doc-name {\r\n      font-weight: 500;\r\n      color: #333;\r\n    }\r\n\r\n    .doc-date {\r\n      font-size: 12px;\r\n      color: #666;\r\n    }\r\n\r\n    .doc-action {\r\n      background: none;\r\n      border: none;\r\n      font-size: 16px;\r\n      color: #666;\r\n      cursor: pointer;\r\n    }\r\n\r\n    .new-transaction-btn {\r\n      background: #1976d2;\r\n      color: white;\r\n      border: none;\r\n      padding: 8px 16px;\r\n      border-radius: 6px;\r\n      font-size: 14px;\r\n      cursor: pointer;\r\n    }\r\n\r\n    .chart-container {\r\n      margin-bottom: 24px;\r\n    }\r\n\r\n    .chart-bars {\r\n      display: flex;\r\n      align-items: flex-end;\r\n      gap: 8px;\r\n      height: 120px;\r\n      margin-bottom: 8px;\r\n    }\r\n\r\n    .bar {\r\n      flex: 1;\r\n      min-height: 20px;\r\n      border-radius: 4px 4px 0 0;\r\n    }\r\n\r\n    .chart-labels {\r\n      display: flex;\r\n      gap: 8px;\r\n      font-size: 12px;\r\n      color: #666;\r\n    }\r\n\r\n    .chart-labels span {\r\n      flex: 1;\r\n      text-align: center;\r\n    }\r\n\r\n    .chart-period {\r\n      text-align: center;\r\n      font-size: 12px;\r\n      color: #666;\r\n      margin-top: 8px;\r\n    }\r\n\r\n    .transaction-item {\r\n      display: flex;\r\n      justify-content: space-between;\r\n      align-items: center;\r\n      padding: 12px 0;\r\n      border-bottom: 1px solid #f0f0f0;\r\n    }\r\n\r\n    .transaction-amount {\r\n      font-weight: 600;\r\n      color: #333;\r\n    }\r\n\r\n    .transaction-info {\r\n      text-align: right;\r\n    }\r\n\r\n    .transaction-type {\r\n      font-weight: 500;\r\n      color: #333;\r\n    }\r\n\r\n    .transaction-date {\r\n      font-size: 12px;\r\n      color: #666;\r\n    }\r\n  `]\r\n})\r\nexport class DashboardComponent implements OnInit {\r\n  currentMonth = 'July';\r\n  currentYear = 2025;\r\n  selectedDateString = 'July 1, 2025';\r\n  searchTerm = '';\r\n\r\n  dayHeaders = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];\r\n\r\n  calendarDays = [\r\n    { date: 30, otherMonth: true, isToday: false, hasAppointment: false },\r\n    { date: 1, otherMonth: false, isToday: true, hasAppointment: true },\r\n    { date: 2, otherMonth: false, isToday: false, hasAppointment: false },\r\n    { date: 3, otherMonth: false, isToday: false, hasAppointment: false },\r\n    { date: 4, otherMonth: false, isToday: false, hasAppointment: false },\r\n    { date: 5, otherMonth: false, isToday: false, hasAppointment: false },\r\n    { date: 6, otherMonth: false, isToday: false, hasAppointment: false },\r\n    { date: 7, otherMonth: false, isToday: false, hasAppointment: false },\r\n    { date: 8, otherMonth: false, isToday: false, hasAppointment: false },\r\n    { date: 9, otherMonth: false, isToday: false, hasAppointment: false },\r\n    { date: 10, otherMonth: false, isToday: false, hasAppointment: false },\r\n    { date: 11, otherMonth: false, isToday: false, hasAppointment: false },\r\n    { date: 12, otherMonth: false, isToday: false, hasAppointment: true },\r\n    { date: 13, otherMonth: false, isToday: false, hasAppointment: false },\r\n    { date: 14, otherMonth: false, isToday: false, hasAppointment: false },\r\n    { date: 15, otherMonth: false, isToday: false, hasAppointment: false },\r\n    { date: 16, otherMonth: false, isToday: false, hasAppointment: false },\r\n    { date: 17, otherMonth: false, isToday: false, hasAppointment: false },\r\n    { date: 18, otherMonth: false, isToday: false, hasAppointment: false },\r\n    { date: 19, otherMonth: false, isToday: false, hasAppointment: false },\r\n    { date: 20, otherMonth: false, isToday: false, hasAppointment: false },\r\n    { date: 21, otherMonth: false, isToday: false, hasAppointment: false },\r\n    { date: 22, otherMonth: false, isToday: false, hasAppointment: false },\r\n    { date: 23, otherMonth: false, isToday: false, hasAppointment: false },\r\n    { date: 24, otherMonth: false, isToday: false, hasAppointment: false },\r\n    { date: 25, otherMonth: false, isToday: false, hasAppointment: false },\r\n    { date: 26, otherMonth: false, isToday: false, hasAppointment: false },\r\n    { date: 27, otherMonth: false, isToday: false, hasAppointment: false },\r\n    { date: 28, otherMonth: false, isToday: false, hasAppointment: false },\r\n    { date: 29, otherMonth: false, isToday: false, hasAppointment: false },\r\n    { date: 30, otherMonth: false, isToday: false, hasAppointment: false },\r\n    { date: 31, otherMonth: false, isToday: false, hasAppointment: false },\r\n    { date: 1, otherMonth: true, isToday: false, hasAppointment: false },\r\n    { date: 2, otherMonth: true, isToday: false, hasAppointment: false },\r\n    { date: 3, otherMonth: true, isToday: false, hasAppointment: false }\r\n  ];\r\n\r\n  todayAppointments = [\r\n    { type: 'Consultation', client: 'Kobe Bryant', time: '10:00' },\r\n    { type: 'Consultation', client: 'LeBron James', time: '11:00' }\r\n  ];\r\n\r\n  retainerClients = [\r\n    { name: 'Amado Cruz', status: 'active' },\r\n    { name: 'Clara Mendoza', status: 'inactive' },\r\n    { name: 'Kobe Bryant', status: 'active' }\r\n  ];\r\n\r\n  recentDocuments = [\r\n    { name: 'Lopez-Contract.pdf', date: 'Updated today' },\r\n    { name: 'Client-Agreement.pdf', date: '4 days ago' }\r\n  ];\r\n\r\n  chartData = [\r\n    { height: 60, color: '#e91e63' },\r\n    { height: 40, color: '#e91e63' },\r\n    { height: 80, color: '#e91e63' },\r\n    { height: 30, color: '#e91e63' },\r\n    { height: 70, color: '#e91e63' },\r\n    { height: 50, color: '#e91e63' },\r\n    { height: 90, color: '#e91e63' },\r\n    { height: 45, color: '#e91e63' },\r\n    { height: 85, color: '#e91e63' }\r\n  ];\r\n\r\n  chartLabels = ['1', '2', '3', '4', '5', '6', '7', '8', '9'];\r\n\r\n  recentTransactions = [\r\n    { amount: '₱15,000', type: 'Consultation', date: 'June 15, 2025' },\r\n    { amount: '₱5,000', type: 'Retainer', date: 'June 16, 2025' }\r\n  ];\r\n\r\n  ngOnInit() {\r\n    // Initialize component\r\n  }\r\n\r\n  previousMonth() {\r\n    // Implementation for previous month navigation\r\n  }\r\n\r\n  nextMonth() {\r\n    // Implementation for next month navigation\r\n  }\r\n\r\n  selectDate(day: any) {\r\n    // Implementation for date selection\r\n  }\r\n}\r\n", "import { Component, OnInit } from '@angular/core';\r\n\r\ninterface Document {\r\n  id: string;\r\n  name: string;\r\n  category: 'disclosures' | 'evidence' | 'receipts' | 'contracts';\r\n  uploadDate: Date;\r\n  size: string;\r\n  type: string;\r\n}\r\n\r\n@Component({\r\n  selector: 'app-files',\r\n  template: `\r\n    <div class=\"files-container\">\r\n      <div class=\"files-header\">\r\n        <h1>Files</h1>\r\n        <div class=\"search-container\">\r\n          <mat-form-field appearance=\"outline\" class=\"search-field\">\r\n            <mat-icon matPrefix>search</mat-icon>\r\n            <input matInput placeholder=\"Search a document\" [(ngModel)]=\"searchTerm\" (input)=\"filterDocuments()\">\r\n          </mat-form-field>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"files-content\">\r\n        <!-- Document Categories -->\r\n        <div class=\"document-categories\">\r\n          <div class=\"category-grid\">\r\n            <div class=\"category-card\" (click)=\"selectCategory('disclosures')\">\r\n              <div class=\"category-icon\">\r\n                <mat-icon>folder</mat-icon>\r\n              </div>\r\n              <div class=\"category-name\">Disclosures</div>\r\n              <div class=\"category-count\">{{ getCategoryCount('disclosures') }} files</div>\r\n            </div>\r\n\r\n            <div class=\"category-card\" (click)=\"selectCategory('evidence')\">\r\n              <div class=\"category-icon\">\r\n                <mat-icon>folder</mat-icon>\r\n              </div>\r\n              <div class=\"category-name\">Evidence</div>\r\n              <div class=\"category-count\">{{ getCategoryCount('evidence') }} files</div>\r\n            </div>\r\n\r\n            <div class=\"category-card\" (click)=\"selectCategory('receipts')\">\r\n              <div class=\"category-icon\">\r\n                <mat-icon>folder</mat-icon>\r\n              </div>\r\n              <div class=\"category-name\">Receipts</div>\r\n              <div class=\"category-count\">{{ getCategoryCount('receipts') }} files</div>\r\n            </div>\r\n\r\n            <div class=\"category-card\" (click)=\"selectCategory('contracts')\">\r\n              <div class=\"category-icon\">\r\n                <mat-icon>folder</mat-icon>\r\n              </div>\r\n              <div class=\"category-name\">Contracts</div>\r\n              <div class=\"category-count\">{{ getCategoryCount('contracts') }} files</div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- Recent Documents -->\r\n        <div class=\"recent-documents\">\r\n          <div class=\"section-header\">\r\n            <h3>Recent documents</h3>\r\n            <button mat-raised-button color=\"primary\">\r\n              <mat-icon>upload</mat-icon>\r\n              Upload Document\r\n            </button>\r\n          </div>\r\n\r\n          <div class=\"documents-list\">\r\n            <div class=\"document-item\" *ngFor=\"let doc of filteredDocuments\">\r\n              <div class=\"document-icon\">\r\n                <mat-icon>{{ getDocumentIcon(doc.type) }}</mat-icon>\r\n              </div>\r\n              <div class=\"document-info\">\r\n                <div class=\"document-name\">{{ doc.name }}</div>\r\n                <div class=\"document-meta\">\r\n                  <span class=\"document-size\">{{ doc.size }}</span>\r\n                  <span class=\"document-date\">{{ getTimeAgo(doc.uploadDate) }}</span>\r\n                </div>\r\n              </div>\r\n              <div class=\"document-actions\">\r\n                <button mat-icon-button>\r\n                  <mat-icon>download</mat-icon>\r\n                </button>\r\n                <button mat-icon-button>\r\n                  <mat-icon>more_vert</mat-icon>\r\n                </button>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  `,\r\n  styles: [`\r\n    .files-container {\r\n      padding: 32px;\r\n      background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);\r\n      min-height: calc(100vh - 64px);\r\n      position: relative;\r\n    }\r\n\r\n    .files-container::before {\r\n      content: '';\r\n      position: absolute;\r\n      top: 0;\r\n      left: 0;\r\n      right: 0;\r\n      bottom: 0;\r\n      background:\r\n        radial-gradient(circle at 30% 30%, rgba(168, 85, 247, 0.05) 0%, transparent 50%),\r\n        radial-gradient(circle at 70% 70%, rgba(34, 197, 94, 0.03) 0%, transparent 50%);\r\n      pointer-events: none;\r\n    }\r\n\r\n    .files-header {\r\n      display: flex;\r\n      justify-content: space-between;\r\n      align-items: center;\r\n      margin-bottom: 40px;\r\n      position: relative;\r\n      z-index: 1;\r\n    }\r\n\r\n    .files-header h1 {\r\n      margin: 0;\r\n      font-size: 36px;\r\n      font-weight: 700;\r\n      color: #1e293b;\r\n      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\r\n      position: relative;\r\n    }\r\n\r\n    .files-header h1::after {\r\n      content: '';\r\n      position: absolute;\r\n      bottom: -8px;\r\n      left: 0;\r\n      width: 80px;\r\n      height: 4px;\r\n      background: linear-gradient(90deg, #a855f7 0%, #9333ea 100%);\r\n      border-radius: 2px;\r\n    }\r\n\r\n    .search-container {\r\n      width: 300px;\r\n    }\r\n\r\n    .search-field {\r\n      width: 100%;\r\n    }\r\n\r\n    .files-content {\r\n      display: flex;\r\n      flex-direction: column;\r\n      gap: 32px;\r\n    }\r\n\r\n    .document-categories {\r\n      background: white;\r\n      border-radius: 12px;\r\n      padding: 24px;\r\n      box-shadow: 0 2px 8px rgba(0,0,0,0.1);\r\n    }\r\n\r\n    .category-grid {\r\n      display: grid;\r\n      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\r\n      gap: 16px;\r\n    }\r\n\r\n    .category-card {\r\n      display: flex;\r\n      flex-direction: column;\r\n      align-items: center;\r\n      padding: 24px;\r\n      border: 2px solid #e0e0e0;\r\n      border-radius: 12px;\r\n      cursor: pointer;\r\n      transition: all 0.3s ease;\r\n      background: #fafafa;\r\n    }\r\n\r\n    .category-card:hover {\r\n      border-color: #C49A56;\r\n      background: #fff;\r\n      transform: translateY(-2px);\r\n      box-shadow: 0 4px 12px rgba(0,0,0,0.1);\r\n    }\r\n\r\n    .category-icon {\r\n      width: 60px;\r\n      height: 60px;\r\n      border-radius: 50%;\r\n      background: linear-gradient(135deg, #C49A56 0%, #B8935A 100%);\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n      margin-bottom: 16px;\r\n    }\r\n\r\n    .category-icon mat-icon {\r\n      color: white;\r\n      font-size: 28px;\r\n      width: 28px;\r\n      height: 28px;\r\n    }\r\n\r\n    .category-name {\r\n      font-size: 16px;\r\n      font-weight: 600;\r\n      color: #333;\r\n      margin-bottom: 4px;\r\n    }\r\n\r\n    .category-count {\r\n      font-size: 12px;\r\n      color: #666;\r\n    }\r\n\r\n    .recent-documents {\r\n      background: white;\r\n      border-radius: 12px;\r\n      padding: 24px;\r\n      box-shadow: 0 2px 8px rgba(0,0,0,0.1);\r\n    }\r\n\r\n    .section-header {\r\n      display: flex;\r\n      justify-content: space-between;\r\n      align-items: center;\r\n      margin-bottom: 24px;\r\n    }\r\n\r\n    .section-header h3 {\r\n      margin: 0;\r\n      font-size: 18px;\r\n      font-weight: 600;\r\n      color: #333;\r\n    }\r\n\r\n    .documents-list {\r\n      display: flex;\r\n      flex-direction: column;\r\n      gap: 12px;\r\n    }\r\n\r\n    .document-item {\r\n      display: flex;\r\n      align-items: center;\r\n      gap: 16px;\r\n      padding: 16px;\r\n      border: 1px solid #e0e0e0;\r\n      border-radius: 8px;\r\n      transition: all 0.2s ease;\r\n    }\r\n\r\n    .document-item:hover {\r\n      background: #f8f9fa;\r\n      border-color: #C49A56;\r\n    }\r\n\r\n    .document-icon {\r\n      width: 40px;\r\n      height: 40px;\r\n      border-radius: 8px;\r\n      background: #f0f0f0;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n    }\r\n\r\n    .document-icon mat-icon {\r\n      color: #666;\r\n      font-size: 20px;\r\n      width: 20px;\r\n      height: 20px;\r\n    }\r\n\r\n    .document-info {\r\n      flex: 1;\r\n    }\r\n\r\n    .document-name {\r\n      font-size: 14px;\r\n      font-weight: 500;\r\n      color: #333;\r\n      margin-bottom: 4px;\r\n    }\r\n\r\n    .document-meta {\r\n      display: flex;\r\n      gap: 16px;\r\n      font-size: 12px;\r\n      color: #666;\r\n    }\r\n\r\n    .document-actions {\r\n      display: flex;\r\n      gap: 4px;\r\n    }\r\n\r\n    .document-actions button {\r\n      width: 32px;\r\n      height: 32px;\r\n    }\r\n\r\n    .document-actions mat-icon {\r\n      font-size: 16px;\r\n      width: 16px;\r\n      height: 16px;\r\n    }\r\n\r\n    @media (max-width: 768px) {\r\n      .files-header {\r\n        flex-direction: column;\r\n        gap: 16px;\r\n        align-items: stretch;\r\n      }\r\n\r\n      .search-container {\r\n        width: 100%;\r\n      }\r\n\r\n      .category-grid {\r\n        grid-template-columns: repeat(2, 1fr);\r\n      }\r\n\r\n      .section-header {\r\n        flex-direction: column;\r\n        gap: 16px;\r\n        align-items: stretch;\r\n      }\r\n    }\r\n  `]\r\n})\r\nexport class FilesComponent implements OnInit {\r\n  searchTerm = '';\r\n  selectedCategory: string | null = null;\r\n  \r\n  documents: Document[] = [\r\n    {\r\n      id: '1',\r\n      name: 'Lopez-Contract.pdf',\r\n      category: 'contracts',\r\n      uploadDate: new Date(),\r\n      size: '2.4 MB',\r\n      type: 'pdf'\r\n    },\r\n    {\r\n      id: '2',\r\n      name: 'Client-Agreement.pdf',\r\n      category: 'contracts',\r\n      uploadDate: new Date(Date.now() - 4 * 24 * 60 * 60 * 1000), // 4 days ago\r\n      size: '1.8 MB',\r\n      type: 'pdf'\r\n    },\r\n    {\r\n      id: '3',\r\n      name: 'Evidence-Photos.zip',\r\n      category: 'evidence',\r\n      uploadDate: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000), // 2 days ago\r\n      size: '15.2 MB',\r\n      type: 'zip'\r\n    },\r\n    {\r\n      id: '4',\r\n      name: 'Financial-Disclosure.docx',\r\n      category: 'disclosures',\r\n      uploadDate: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000), // 1 day ago\r\n      size: '890 KB',\r\n      type: 'docx'\r\n    },\r\n    {\r\n      id: '5',\r\n      name: 'Receipt-001.jpg',\r\n      category: 'receipts',\r\n      uploadDate: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000), // 3 days ago\r\n      size: '245 KB',\r\n      type: 'jpg'\r\n    }\r\n  ];\r\n\r\n  filteredDocuments: Document[] = [];\r\n\r\n  ngOnInit() {\r\n    this.filteredDocuments = [...this.documents];\r\n  }\r\n\r\n  getCategoryCount(category: string): number {\r\n    return this.documents.filter(doc => doc.category === category).length;\r\n  }\r\n\r\n  selectCategory(category: string) {\r\n    this.selectedCategory = category;\r\n    this.filterDocuments();\r\n  }\r\n\r\n  filterDocuments() {\r\n    let filtered = [...this.documents];\r\n\r\n    // Filter by category\r\n    if (this.selectedCategory) {\r\n      filtered = filtered.filter(doc => doc.category === this.selectedCategory);\r\n    }\r\n\r\n    // Filter by search term\r\n    if (this.searchTerm) {\r\n      filtered = filtered.filter(doc => \r\n        doc.name.toLowerCase().includes(this.searchTerm.toLowerCase())\r\n      );\r\n    }\r\n\r\n    this.filteredDocuments = filtered;\r\n  }\r\n\r\n  getDocumentIcon(type: string): string {\r\n    switch (type) {\r\n      case 'pdf': return 'picture_as_pdf';\r\n      case 'docx': return 'description';\r\n      case 'zip': return 'archive';\r\n      case 'jpg': \r\n      case 'png': return 'image';\r\n      default: return 'insert_drive_file';\r\n    }\r\n  }\r\n\r\n  getTimeAgo(date: Date): string {\r\n    const now = new Date();\r\n    const diffTime = Math.abs(now.getTime() - date.getTime());\r\n    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\r\n    \r\n    if (diffDays === 1) {\r\n      return 'Updated today';\r\n    } else {\r\n      return `${diffDays} days ago`;\r\n    }\r\n  }\r\n}\r\n", "import { Component, OnInit } from '@angular/core';\r\n\r\ninterface Transaction {\r\n  id: string;\r\n  amount: number;\r\n  type: 'consultation' | 'retainer' | 'expense' | 'payment';\r\n  description: string;\r\n  date: Date;\r\n  client?: string;\r\n}\r\n\r\n@Component({\r\n  selector: 'app-finance',\r\n  template: `\r\n    <div class=\"finance-container\">\r\n      <div class=\"finance-header\">\r\n        <h1>Finance</h1>\r\n        <button mat-raised-button color=\"primary\" (click)=\"openNewTransactionDialog()\">\r\n          <mat-icon>add</mat-icon>\r\n          New Transaction\r\n        </button>\r\n      </div>\r\n\r\n      <div class=\"finance-content\">\r\n        <!-- Financial Chart -->\r\n        <div class=\"chart-section\">\r\n          <div class=\"chart-header\">\r\n            <h3>Monthly Revenue</h3>\r\n            <div class=\"chart-controls\">\r\n              <button mat-button [class.active]=\"selectedPeriod === 'month'\" (click)=\"selectPeriod('month')\">This month</button>\r\n              <button mat-button [class.active]=\"selectedPeriod === 'year'\" (click)=\"selectPeriod('year')\">This year</button>\r\n            </div>\r\n          </div>\r\n          \r\n          <div class=\"chart-container\">\r\n            <div class=\"chart-canvas\">\r\n              <!-- Simple Bar Chart -->\r\n              <div class=\"chart-bars\">\r\n                <div class=\"chart-y-axis\">\r\n                  <div class=\"y-label\" *ngFor=\"let label of yAxisLabels\">{{ label }}</div>\r\n                </div>\r\n                <div class=\"bars-container\">\r\n                  <div class=\"bar-group\" *ngFor=\"let month of chartData; let i = index\">\r\n                    <div class=\"bar consultation\" [style.height.%]=\"getBarHeight(month.consultation)\"></div>\r\n                    <div class=\"bar retainer\" [style.height.%]=\"getBarHeight(month.retainer)\"></div>\r\n                    <div class=\"month-label\">{{ month.month }}</div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n              \r\n              <!-- Chart Legend -->\r\n              <div class=\"chart-legend\">\r\n                <div class=\"legend-item\">\r\n                  <div class=\"legend-color consultation\"></div>\r\n                  <span>Consultation</span>\r\n                </div>\r\n                <div class=\"legend-item\">\r\n                  <div class=\"legend-color retainer\"></div>\r\n                  <span>Retainer</span>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- Recent Transactions -->\r\n        <div class=\"transactions-section\">\r\n          <div class=\"section-header\">\r\n            <h3>Recent Transactions</h3>\r\n            <button mat-icon-button>\r\n              <mat-icon>more_vert</mat-icon>\r\n            </button>\r\n          </div>\r\n\r\n          <div class=\"transactions-list\">\r\n            <div class=\"transaction-item\" *ngFor=\"let transaction of recentTransactions\">\r\n              <div class=\"transaction-icon\" [class]=\"transaction.type\">\r\n                <mat-icon>{{ getTransactionIcon(transaction.type) }}</mat-icon>\r\n              </div>\r\n              <div class=\"transaction-info\">\r\n                <div class=\"transaction-description\">{{ transaction.description }}</div>\r\n                <div class=\"transaction-meta\">\r\n                  <span class=\"transaction-date\">{{ transaction.date | date:'MMM dd, yyyy' }}</span>\r\n                  <span class=\"transaction-client\" *ngIf=\"transaction.client\">• {{ transaction.client }}</span>\r\n                </div>\r\n              </div>\r\n              <div class=\"transaction-amount\" [class]=\"transaction.type\">\r\n                {{ formatAmount(transaction.amount) }}\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <button mat-button class=\"view-all-btn\">View All Transactions</button>\r\n        </div>\r\n\r\n        <!-- Financial Summary Cards -->\r\n        <div class=\"summary-cards\">\r\n          <div class=\"summary-card\">\r\n            <div class=\"card-icon revenue\">\r\n              <mat-icon>trending_up</mat-icon>\r\n            </div>\r\n            <div class=\"card-content\">\r\n              <div class=\"card-title\">Total Revenue</div>\r\n              <div class=\"card-value\">₱{{ formatNumber(totalRevenue) }}</div>\r\n              <div class=\"card-change positive\">+12.5% from last month</div>\r\n            </div>\r\n          </div>\r\n\r\n          <div class=\"summary-card\">\r\n            <div class=\"card-icon expenses\">\r\n              <mat-icon>trending_down</mat-icon>\r\n            </div>\r\n            <div class=\"card-content\">\r\n              <div class=\"card-title\">Total Expenses</div>\r\n              <div class=\"card-value\">₱{{ formatNumber(totalExpenses) }}</div>\r\n              <div class=\"card-change negative\">+5.2% from last month</div>\r\n            </div>\r\n          </div>\r\n\r\n          <div class=\"summary-card\">\r\n            <div class=\"card-icon profit\">\r\n              <mat-icon>account_balance</mat-icon>\r\n            </div>\r\n            <div class=\"card-content\">\r\n              <div class=\"card-title\">Net Profit</div>\r\n              <div class=\"card-value\">₱{{ formatNumber(netProfit) }}</div>\r\n              <div class=\"card-change positive\">+18.3% from last month</div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  `,\r\n  styles: [`\r\n    .finance-container {\r\n      padding: 32px;\r\n      background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);\r\n      min-height: calc(100vh - 64px);\r\n      position: relative;\r\n    }\r\n\r\n    .finance-container::before {\r\n      content: '';\r\n      position: absolute;\r\n      top: 0;\r\n      left: 0;\r\n      right: 0;\r\n      bottom: 0;\r\n      background:\r\n        radial-gradient(circle at 25% 25%, rgba(34, 197, 94, 0.05) 0%, transparent 50%),\r\n        radial-gradient(circle at 75% 75%, rgba(239, 68, 68, 0.03) 0%, transparent 50%);\r\n      pointer-events: none;\r\n    }\r\n\r\n    .finance-header {\r\n      display: flex;\r\n      justify-content: space-between;\r\n      align-items: center;\r\n      margin-bottom: 40px;\r\n      position: relative;\r\n      z-index: 1;\r\n    }\r\n\r\n    .finance-header h1 {\r\n      margin: 0;\r\n      font-size: 36px;\r\n      font-weight: 700;\r\n      color: #1e293b;\r\n      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\r\n      position: relative;\r\n    }\r\n\r\n    .finance-header h1::after {\r\n      content: '';\r\n      position: absolute;\r\n      bottom: -8px;\r\n      left: 0;\r\n      width: 80px;\r\n      height: 4px;\r\n      background: linear-gradient(90deg, #22c55e 0%, #16a34a 100%);\r\n      border-radius: 2px;\r\n    }\r\n\r\n    .finance-content {\r\n      display: grid;\r\n      grid-template-columns: 2fr 1fr;\r\n      grid-template-rows: auto auto;\r\n      gap: 24px;\r\n    }\r\n\r\n    .chart-section {\r\n      background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);\r\n      border-radius: 24px;\r\n      padding: 32px;\r\n      box-shadow:\r\n        0 20px 60px rgba(0, 0, 0, 0.1),\r\n        0 8px 24px rgba(0, 0, 0, 0.05);\r\n      grid-row: span 2;\r\n      border: 1px solid rgba(226, 232, 240, 0.8);\r\n      position: relative;\r\n      overflow: hidden;\r\n      transition: all 0.3s ease;\r\n    }\r\n\r\n    .chart-section::before {\r\n      content: '';\r\n      position: absolute;\r\n      top: 0;\r\n      left: 0;\r\n      right: 0;\r\n      height: 6px;\r\n      background: linear-gradient(90deg, #22c55e 0%, #16a34a 50%, #22c55e 100%);\r\n    }\r\n\r\n    .chart-section:hover {\r\n      transform: translateY(-4px);\r\n      box-shadow:\r\n        0 30px 80px rgba(0, 0, 0, 0.15),\r\n        0 12px 32px rgba(0, 0, 0, 0.08);\r\n    }\r\n\r\n    .chart-header {\r\n      display: flex;\r\n      justify-content: space-between;\r\n      align-items: center;\r\n      margin-bottom: 24px;\r\n    }\r\n\r\n    .chart-header h3 {\r\n      margin: 0;\r\n      font-size: 18px;\r\n      font-weight: 600;\r\n    }\r\n\r\n    .chart-controls {\r\n      display: flex;\r\n      gap: 8px;\r\n    }\r\n\r\n    .chart-controls button {\r\n      padding: 8px 16px;\r\n      border-radius: 20px;\r\n      font-size: 12px;\r\n    }\r\n\r\n    .chart-controls button.active {\r\n      background: #1976d2;\r\n      color: white;\r\n    }\r\n\r\n    .chart-container {\r\n      height: 300px;\r\n      position: relative;\r\n    }\r\n\r\n    .chart-canvas {\r\n      height: 100%;\r\n      display: flex;\r\n      flex-direction: column;\r\n    }\r\n\r\n    .chart-bars {\r\n      flex: 1;\r\n      display: flex;\r\n      align-items: flex-end;\r\n      gap: 16px;\r\n    }\r\n\r\n    .chart-y-axis {\r\n      display: flex;\r\n      flex-direction: column;\r\n      justify-content: space-between;\r\n      height: 250px;\r\n      margin-right: 16px;\r\n    }\r\n\r\n    .y-label {\r\n      font-size: 12px;\r\n      color: #666;\r\n      text-align: right;\r\n    }\r\n\r\n    .bars-container {\r\n      flex: 1;\r\n      display: flex;\r\n      justify-content: space-between;\r\n      align-items: flex-end;\r\n      height: 250px;\r\n    }\r\n\r\n    .bar-group {\r\n      display: flex;\r\n      flex-direction: column;\r\n      align-items: center;\r\n      gap: 4px;\r\n      flex: 1;\r\n      max-width: 60px;\r\n    }\r\n\r\n    .bar {\r\n      width: 20px;\r\n      border-radius: 4px 4px 0 0;\r\n      transition: all 0.3s ease;\r\n    }\r\n\r\n    .bar.consultation {\r\n      background: #e91e63;\r\n      margin-right: 2px;\r\n    }\r\n\r\n    .bar.retainer {\r\n      background: #9c27b0;\r\n      margin-left: 2px;\r\n    }\r\n\r\n    .month-label {\r\n      font-size: 11px;\r\n      color: #666;\r\n      margin-top: 8px;\r\n      writing-mode: vertical-rl;\r\n      text-orientation: mixed;\r\n    }\r\n\r\n    .chart-legend {\r\n      display: flex;\r\n      justify-content: center;\r\n      gap: 24px;\r\n      margin-top: 16px;\r\n    }\r\n\r\n    .legend-item {\r\n      display: flex;\r\n      align-items: center;\r\n      gap: 8px;\r\n      font-size: 12px;\r\n    }\r\n\r\n    .legend-color {\r\n      width: 12px;\r\n      height: 12px;\r\n      border-radius: 2px;\r\n    }\r\n\r\n    .legend-color.consultation {\r\n      background: #e91e63;\r\n    }\r\n\r\n    .legend-color.retainer {\r\n      background: #9c27b0;\r\n    }\r\n\r\n    .transactions-section {\r\n      background: white;\r\n      border-radius: 12px;\r\n      padding: 24px;\r\n      box-shadow: 0 2px 8px rgba(0,0,0,0.1);\r\n    }\r\n\r\n    .section-header {\r\n      display: flex;\r\n      justify-content: space-between;\r\n      align-items: center;\r\n      margin-bottom: 20px;\r\n    }\r\n\r\n    .section-header h3 {\r\n      margin: 0;\r\n      font-size: 18px;\r\n      font-weight: 600;\r\n    }\r\n\r\n    .transactions-list {\r\n      display: flex;\r\n      flex-direction: column;\r\n      gap: 16px;\r\n      margin-bottom: 20px;\r\n    }\r\n\r\n    .transaction-item {\r\n      display: flex;\r\n      align-items: center;\r\n      gap: 16px;\r\n    }\r\n\r\n    .transaction-icon {\r\n      width: 40px;\r\n      height: 40px;\r\n      border-radius: 50%;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n    }\r\n\r\n    .transaction-icon.consultation {\r\n      background: #e3f2fd;\r\n      color: #1976d2;\r\n    }\r\n\r\n    .transaction-icon.retainer {\r\n      background: #f3e5f5;\r\n      color: #7b1fa2;\r\n    }\r\n\r\n    .transaction-icon.expense {\r\n      background: #ffebee;\r\n      color: #d32f2f;\r\n    }\r\n\r\n    .transaction-info {\r\n      flex: 1;\r\n    }\r\n\r\n    .transaction-description {\r\n      font-weight: 500;\r\n      margin-bottom: 4px;\r\n    }\r\n\r\n    .transaction-meta {\r\n      font-size: 12px;\r\n      color: #666;\r\n    }\r\n\r\n    .transaction-amount {\r\n      font-weight: 600;\r\n      font-size: 14px;\r\n    }\r\n\r\n    .transaction-amount.consultation,\r\n    .transaction-amount.retainer {\r\n      color: #2e7d32;\r\n    }\r\n\r\n    .transaction-amount.expense {\r\n      color: #d32f2f;\r\n    }\r\n\r\n    .view-all-btn {\r\n      width: 100%;\r\n      color: #1976d2;\r\n    }\r\n\r\n    .summary-cards {\r\n      display: flex;\r\n      flex-direction: column;\r\n      gap: 16px;\r\n    }\r\n\r\n    .summary-card {\r\n      background: white;\r\n      border-radius: 12px;\r\n      padding: 20px;\r\n      box-shadow: 0 2px 8px rgba(0,0,0,0.1);\r\n      display: flex;\r\n      align-items: center;\r\n      gap: 16px;\r\n    }\r\n\r\n    .card-icon {\r\n      width: 50px;\r\n      height: 50px;\r\n      border-radius: 50%;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n    }\r\n\r\n    .card-icon.revenue {\r\n      background: #e8f5e8;\r\n      color: #2e7d32;\r\n    }\r\n\r\n    .card-icon.expenses {\r\n      background: #ffebee;\r\n      color: #d32f2f;\r\n    }\r\n\r\n    .card-icon.profit {\r\n      background: #e3f2fd;\r\n      color: #1976d2;\r\n    }\r\n\r\n    .card-content {\r\n      flex: 1;\r\n    }\r\n\r\n    .card-title {\r\n      font-size: 12px;\r\n      color: #666;\r\n      margin-bottom: 4px;\r\n    }\r\n\r\n    .card-value {\r\n      font-size: 18px;\r\n      font-weight: 600;\r\n      margin-bottom: 4px;\r\n    }\r\n\r\n    .card-change {\r\n      font-size: 11px;\r\n    }\r\n\r\n    .card-change.positive {\r\n      color: #2e7d32;\r\n    }\r\n\r\n    .card-change.negative {\r\n      color: #d32f2f;\r\n    }\r\n\r\n    @media (max-width: 1024px) {\r\n      .finance-content {\r\n        grid-template-columns: 1fr;\r\n        grid-template-rows: auto auto auto;\r\n      }\r\n\r\n      .chart-section {\r\n        grid-row: span 1;\r\n      }\r\n\r\n      .summary-cards {\r\n        flex-direction: row;\r\n      }\r\n    }\r\n\r\n    @media (max-width: 768px) {\r\n      .finance-header {\r\n        flex-direction: column;\r\n        gap: 16px;\r\n        align-items: stretch;\r\n      }\r\n\r\n      .summary-cards {\r\n        flex-direction: column;\r\n      }\r\n\r\n      .chart-header {\r\n        flex-direction: column;\r\n        gap: 16px;\r\n        align-items: stretch;\r\n      }\r\n    }\r\n  `]\r\n})\r\nexport class FinanceComponent implements OnInit {\r\n  selectedPeriod = 'month';\r\n  totalRevenue = 125000;\r\n  totalExpenses = 45000;\r\n  netProfit = 80000;\r\n\r\n  yAxisLabels = ['100', '75', '50', '25', '0'];\r\n\r\n  chartData = [\r\n    { month: '1', consultation: 45, retainer: 30 },\r\n    { month: '2', consultation: 65, retainer: 40 },\r\n    { month: '3', consultation: 35, retainer: 25 },\r\n    { month: '4', consultation: 80, retainer: 60 },\r\n    { month: '5', consultation: 55, retainer: 45 },\r\n    { month: '6', consultation: 70, retainer: 50 },\r\n    { month: '7', consultation: 90, retainer: 65 },\r\n    { month: '8', consultation: 60, retainer: 40 },\r\n    { month: '9', consultation: 75, retainer: 55 }\r\n  ];\r\n\r\n  recentTransactions: Transaction[] = [\r\n    {\r\n      id: '1',\r\n      amount: 15000,\r\n      type: 'consultation',\r\n      description: 'Consultation',\r\n      date: new Date(2025, 5, 15), // June 15, 2025\r\n      client: 'John Doe'\r\n    },\r\n    {\r\n      id: '2',\r\n      amount: 5000,\r\n      type: 'retainer',\r\n      description: 'Retainer',\r\n      date: new Date(2025, 5, 16), // June 16, 2025\r\n      client: 'Jane Smith'\r\n    },\r\n    {\r\n      id: '3',\r\n      amount: 2500,\r\n      type: 'expense',\r\n      description: 'Office Supplies',\r\n      date: new Date(2025, 5, 14), // June 14, 2025\r\n    }\r\n  ];\r\n\r\n  ngOnInit() {\r\n    // Component initialization\r\n  }\r\n\r\n  selectPeriod(period: string) {\r\n    this.selectedPeriod = period;\r\n    // Update chart data based on selected period\r\n  }\r\n\r\n  getBarHeight(value: number): number {\r\n    return (value / 100) * 100; // Convert to percentage\r\n  }\r\n\r\n  getTransactionIcon(type: string): string {\r\n    switch (type) {\r\n      case 'consultation': return 'person';\r\n      case 'retainer': return 'account_balance_wallet';\r\n      case 'expense': return 'shopping_cart';\r\n      case 'payment': return 'payment';\r\n      default: return 'attach_money';\r\n    }\r\n  }\r\n\r\n  formatAmount(amount: number): string {\r\n    const prefix = amount >= 0 ? '+' : '';\r\n    return `${prefix}₱${amount.toLocaleString()}`;\r\n  }\r\n\r\n  formatNumber(num: number): string {\r\n    return num.toLocaleString();\r\n  }\r\n\r\n  openNewTransactionDialog() {\r\n    // Implement new transaction dialog\r\n  }\r\n}\r\n", "import { Component, OnInit } from '@angular/core';\r\nimport { Router } from '@angular/router';\r\nimport {\r\n  FirebaseService,\r\n  AssistantCode,\r\n  LawyerSecretaryLink\r\n} from '../services/firebase.service';\r\n\r\n@Component({\r\n  selector: 'app-link-lawyer',\r\n  template: `\r\n    <div class=\"link-lawyer-container\">\r\n      <div class=\"header-section\">\r\n        <h1>Link to Lawyer</h1>\r\n        <p>Enter the assistant code provided by your lawyer</p>\r\n      </div>\r\n      \r\n      <div class=\"form-section\">\r\n        <mat-card class=\"link-card\">\r\n          <mat-card-header>\r\n            <mat-card-title>Assistant Code</mat-card-title>\r\n            <mat-card-subtitle>8-character code from lawyer</mat-card-subtitle>\r\n          </mat-card-header>\r\n          \r\n          <mat-card-content>\r\n            <mat-form-field appearance=\"outline\" class=\"full-width\">\r\n              <mat-label>Enter Code</mat-label>\r\n              <input matInput \r\n                     [(ngModel)]=\"assistantCode\" \r\n                     placeholder=\"XXXXXXXX\"\r\n                     maxlength=\"8\"\r\n                     (input)=\"onCodeInput($event)\"\r\n                     class=\"code-input\">\r\n            </mat-form-field>\r\n            \r\n            <div class=\"code-info\">\r\n              <mat-icon>info</mat-icon>\r\n              <span>Codes are 8 characters long and expire after 24 hours</span>\r\n            </div>\r\n          </mat-card-content>\r\n          \r\n          <mat-card-actions>\r\n            <button mat-raised-button \r\n                    color=\"primary\" \r\n                    [disabled]=\"!isValidCode()\"\r\n                    (click)=\"linkToLawyer()\"\r\n                    class=\"full-width\">\r\n              <mat-icon>link</mat-icon>\r\n              Link to Lawyer\r\n            </button>\r\n          </mat-card-actions>\r\n        </mat-card>\r\n      </div>\r\n      \r\n      <div class=\"linked-lawyers-section\" *ngIf=\"linkedLawyers.length > 0\">\r\n        <h2>Linked Lawyers</h2>\r\n        \r\n        <div class=\"lawyers-grid\">\r\n          <mat-card *ngFor=\"let lawyer of linkedLawyers\" class=\"lawyer-card\">\r\n            <mat-card-header>\r\n              <mat-card-title>{{ lawyer.lawyerName }}</mat-card-title>\r\n              <mat-card-subtitle>Linked on {{ formatDate(lawyer.createdAt) }}</mat-card-subtitle>\r\n            </mat-card-header>\r\n            \r\n            <mat-card-content>\r\n              <div class=\"permissions\">\r\n                <h4>Your Permissions:</h4>\r\n                <div class=\"permission-chips\">\r\n                  <mat-chip *ngIf=\"lawyer.permissions.canManageCalendar\" color=\"primary\" selected>\r\n                    Calendar\r\n                  </mat-chip>\r\n                  <mat-chip *ngIf=\"lawyer.permissions.canManageFiles\" color=\"primary\" selected>\r\n                    Files\r\n                  </mat-chip>\r\n                  <mat-chip *ngIf=\"lawyer.permissions.canManageCases\" color=\"primary\" selected>\r\n                    Cases\r\n                  </mat-chip>\r\n                </div>\r\n              </div>\r\n            </mat-card-content>\r\n            \r\n            <mat-card-actions>\r\n              <button mat-button color=\"primary\">Access Dashboard</button>\r\n              <button mat-button>View Details</button>\r\n            </mat-card-actions>\r\n          </mat-card>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  `,\r\n  styles: [`\r\n    .link-lawyer-container {\r\n      padding: 24px;\r\n      max-width: 800px;\r\n      margin: 0 auto;\r\n    }\r\n    \r\n    .header-section {\r\n      text-align: center;\r\n      margin-bottom: 32px;\r\n    }\r\n    \r\n    .header-section h1 {\r\n      font-size: 2.5rem;\r\n      color: #1976d2;\r\n      margin-bottom: 8px;\r\n    }\r\n    \r\n    .form-section {\r\n      margin-bottom: 48px;\r\n    }\r\n    \r\n    .link-card {\r\n      max-width: 500px;\r\n      margin: 0 auto;\r\n    }\r\n    \r\n    .full-width {\r\n      width: 100%;\r\n    }\r\n    \r\n    .code-input {\r\n      font-family: monospace;\r\n      font-size: 18px;\r\n      font-weight: bold;\r\n      letter-spacing: 2px;\r\n      text-transform: uppercase;\r\n      text-align: center;\r\n    }\r\n    \r\n    .code-info {\r\n      display: flex;\r\n      align-items: center;\r\n      gap: 8px;\r\n      margin-top: 16px;\r\n      color: #666;\r\n      font-size: 14px;\r\n    }\r\n    \r\n    .lawyers-grid {\r\n      display: grid;\r\n      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\r\n      gap: 24px;\r\n    }\r\n    \r\n    .lawyer-card {\r\n      height: fit-content;\r\n    }\r\n    \r\n    .permissions h4 {\r\n      margin: 0 0 12px 0;\r\n      color: #666;\r\n    }\r\n  `]\r\n})\r\nexport class LinkLawyerComponent implements OnInit {\r\n  linkedLawyers: LawyerSecretaryLink[] = [];\r\n  assistantCode = '';\r\n  codePreview: AssistantCode | null = null;\r\n  isLinking = false;\r\n  isLoading = false;\r\n\r\n  constructor(\r\n    private router: Router,\r\n    private firebaseService: FirebaseService\r\n  ) { }\r\n\r\n  ngOnInit() {\r\n    this.loadLinkedLawyers();\r\n  }\r\n\r\n  async loadLinkedLawyers() {\r\n    this.isLoading = true;\r\n    const currentUser = this.firebaseService.getCurrentUser();\r\n\r\n    if (currentUser) {\r\n      try {\r\n        this.linkedLawyers = await this.firebaseService.getLinkedLawyers(currentUser.uid);\r\n      } catch (error) {\r\n        console.error('Error loading linked lawyers:', error);\r\n      }\r\n    }\r\n\r\n    this.isLoading = false;\r\n  }\r\n\r\n  onCodeInput(event: any) {\r\n    const value = event.target.value.toUpperCase();\r\n    this.assistantCode = value;\r\n\r\n    if (this.codePreview) {\r\n      this.codePreview = null;\r\n    }\r\n  }\r\n\r\n  isValidCode(): boolean {\r\n    return this.assistantCode.length === 8;\r\n  }\r\n\r\n  async linkToLawyer() {\r\n    if (!this.isValidCode()) return;\r\n\r\n    const currentUser = this.firebaseService.getCurrentUser();\r\n    if (!currentUser) return;\r\n\r\n    this.isLinking = true;\r\n\r\n    try {\r\n      const codeData = await this.firebaseService.validateAssistantCode(this.assistantCode);\r\n\r\n      if (!codeData) {\r\n        alert('Invalid or expired code');\r\n        this.isLinking = false;\r\n        return;\r\n      }\r\n\r\n      const alreadyLinked = this.linkedLawyers.some(link =>\r\n        link.lawyerId === codeData.lawyerId\r\n      );\r\n\r\n      if (alreadyLinked) {\r\n        alert('You are already linked to this lawyer');\r\n        this.isLinking = false;\r\n        return;\r\n      }\r\n\r\n      this.codePreview = codeData;\r\n      this.isLinking = false;\r\n\r\n    } catch (error) {\r\n      console.error('Error validating code:', error);\r\n      alert('Error validating code');\r\n      this.isLinking = false;\r\n    }\r\n  }\r\n\r\n  async confirmLink() {\r\n    if (!this.codePreview) return;\r\n\r\n    const currentUser = this.firebaseService.getCurrentUser();\r\n    if (!currentUser) return;\r\n\r\n    this.isLinking = true;\r\n\r\n    try {\r\n      const link = await this.firebaseService.useAssistantCode(\r\n        this.assistantCode,\r\n        currentUser.uid\r\n      );\r\n\r\n      this.linkedLawyers.unshift(link);\r\n      this.assistantCode = '';\r\n      this.codePreview = null;\r\n\r\n      alert(`Successfully linked to ${link.lawyerName}`);\r\n\r\n    } catch (error) {\r\n      console.error('Error linking to lawyer:', error);\r\n      alert('Failed to link to lawyer');\r\n    } finally {\r\n      this.isLinking = false;\r\n    }\r\n  }\r\n\r\n  cancelPreview() {\r\n    this.codePreview = null;\r\n    this.assistantCode = '';\r\n  }\r\n\r\n  switchToLawyer(lawyer: LawyerSecretaryLink) {\r\n    localStorage.setItem('selectedLawyerContext', JSON.stringify({\r\n      lawyerId: lawyer.lawyerId,\r\n      lawyerName: lawyer.lawyerName,\r\n      permissions: lawyer.permissions\r\n    }));\r\n\r\n    this.router.navigate(['/dashboard']);\r\n  }\r\n\r\n  formatDate(date: any): string {\r\n    return new Date(date).toLocaleDateString();\r\n  }\r\n}\r\n", "import { Component } from '@angular/core';\r\nimport { Router } from '@angular/router';\r\nimport { FirebaseService } from '../services/firebase.service';\r\n\r\n@Component({\r\n  selector: 'app-login',\r\n  template: `\r\n    <div class=\"login-container\">\r\n      <mat-card class=\"login-card\">\r\n        <mat-card-header>\r\n          <mat-card-title>Secretary <PERSON> Login</mat-card-title>\r\n          <mat-card-subtitle>Access your lawyer management dashboard</mat-card-subtitle>\r\n        </mat-card-header>\r\n        \r\n        <mat-card-content>\r\n          <form class=\"login-form\">\r\n            <mat-form-field appearance=\"outline\" class=\"full-width\">\r\n              <mat-label>Email</mat-label>\r\n              <input matInput type=\"email\" [(ngModel)]=\"email\" name=\"email\" placeholder=\"<EMAIL>\">\r\n              <mat-icon matSuffix>email</mat-icon>\r\n            </mat-form-field>\r\n\r\n            <mat-form-field appearance=\"outline\" class=\"full-width\">\r\n              <mat-label>Password</mat-label>\r\n              <input matInput [type]=\"hidePassword ? 'password' : 'text'\" [(ngModel)]=\"password\" name=\"password\" placeholder=\"Enter your password\">\r\n              <button mat-icon-button matSuffix (click)=\"hidePassword = !hidePassword\" type=\"button\">\r\n                <mat-icon>{{hidePassword ? 'visibility_off' : 'visibility'}}</mat-icon>\r\n              </button>\r\n            </mat-form-field>\r\n\r\n            <!-- Demo Credentials Info -->\r\n            <div class=\"demo-info\">\r\n              <small>\r\n                <strong>Demo Credentials:</strong><br>\r\n                Email: secretary&#64;veritus.com<br>\r\n                Password: secretary123\r\n              </small>\r\n            </div>\r\n            \r\n            <button mat-raised-button color=\"primary\" class=\"full-width login-button\" (click)=\"login()\" [disabled]=\"isLoading\">\r\n              <span *ngIf=\"!isLoading\">Sign In</span>\r\n              <span *ngIf=\"isLoading\">Signing In...</span>\r\n            </button>\r\n\r\n            <div *ngIf=\"errorMessage\" class=\"error-message\">\r\n              {{ errorMessage }}\r\n            </div>\r\n          </form>\r\n        </mat-card-content>\r\n        \r\n        <mat-card-actions>\r\n          <button mat-button color=\"accent\" (click)=\"forgotPassword()\" [disabled]=\"isLoading\">\r\n            Forgot Password?\r\n          </button>\r\n          <button mat-button color=\"primary\" (click)=\"createDemoAccount()\" [disabled]=\"isLoading\">\r\n            Create Demo Account\r\n          </button>\r\n        </mat-card-actions>\r\n      </mat-card>\r\n    </div>\r\n  `,\r\n  styles: [`\r\n    .login-container {\r\n      display: flex;\r\n      justify-content: center;\r\n      align-items: center;\r\n      min-height: 100vh;\r\n      width: 100%;\r\n      padding: 24px;\r\n      box-sizing: border-box;\r\n    }\r\n    \r\n    .login-card {\r\n      width: 100%;\r\n      max-width: 400px;\r\n    }\r\n    \r\n    .login-form {\r\n      display: flex;\r\n      flex-direction: column;\r\n      gap: 16px;\r\n    }\r\n    \r\n    .full-width {\r\n      width: 100%;\r\n    }\r\n    \r\n    .login-button {\r\n      height: 48px;\r\n      font-size: 16px;\r\n      margin-top: 16px;\r\n    }\r\n\r\n    .error-message {\r\n      color: #f44336;\r\n      font-size: 14px;\r\n      margin-top: 8px;\r\n      padding: 8px;\r\n      background-color: #ffebee;\r\n      border-radius: 4px;\r\n      border-left: 4px solid #f44336;\r\n    }\r\n\r\n    .demo-info {\r\n      background-color: #e3f2fd;\r\n      border: 1px solid #2196f3;\r\n      border-radius: 4px;\r\n      padding: 12px;\r\n      margin: 16px 0;\r\n      text-align: center;\r\n    }\r\n\r\n    .demo-info small {\r\n      color: #1976d2;\r\n      line-height: 1.4;\r\n    }\r\n  `]\r\n})\r\nexport class LoginComponent {\r\n  email = '';\r\n  password = '';\r\n  hidePassword = true;\r\n  isLoading = false;\r\n  errorMessage = '';\r\n\r\n  constructor(\r\n    private router: Router,\r\n    private firebaseService: FirebaseService\r\n  ) { }\r\n\r\n  async login() {\r\n    if (!this.email || !this.password) {\r\n      this.errorMessage = 'Please enter both email and password';\r\n      return;\r\n    }\r\n\r\n    this.isLoading = true;\r\n    this.errorMessage = '';\r\n\r\n    try {\r\n      console.log('Attempting login for:', this.email);\r\n\r\n      // Authenticate with Firebase\r\n      const user = await this.firebaseService.signIn(this.email, this.password);\r\n      console.log('Login successful:', user);\r\n\r\n      // Check if user has secretary profile\r\n      const secretaryProfile = await this.firebaseService.getSecretaryProfile(user.uid);\r\n\r\n      if (!secretaryProfile) {\r\n        throw new Error('No secretary profile found. Please contact your administrator.');\r\n      }\r\n\r\n      console.log('Secretary profile found:', secretaryProfile);\r\n\r\n      // Navigate to dashboard\r\n      this.router.navigate(['/secretary-tabs']).then(() => {\r\n        console.log('Navigation to secretary dashboard successful');\r\n      });\r\n\r\n    } catch (error: any) {\r\n      console.error('Login error:', error);\r\n\r\n      // Handle specific Firebase auth errors\r\n      if (error.code === 'auth/user-not-found') {\r\n        this.errorMessage = 'No account found with this email address';\r\n      } else if (error.code === 'auth/wrong-password') {\r\n        this.errorMessage = 'Incorrect password';\r\n      } else if (error.code === 'auth/invalid-email') {\r\n        this.errorMessage = 'Invalid email address';\r\n      } else if (error.code === 'auth/too-many-requests') {\r\n        this.errorMessage = 'Too many failed attempts. Please try again later';\r\n      } else if (error.message) {\r\n        this.errorMessage = error.message;\r\n      } else {\r\n        this.errorMessage = 'Login failed. Please try again';\r\n      }\r\n    } finally {\r\n      this.isLoading = false;\r\n    }\r\n  }\r\n\r\n  async forgotPassword() {\r\n    if (!this.email) {\r\n      this.errorMessage = 'Please enter your email address first';\r\n      return;\r\n    }\r\n\r\n    try {\r\n      await this.firebaseService.resetPassword(this.email);\r\n      this.errorMessage = '';\r\n      alert('Password reset email sent! Check your inbox.');\r\n    } catch (error: any) {\r\n      console.error('Password reset error:', error);\r\n      this.errorMessage = 'Failed to send password reset email';\r\n    }\r\n  }\r\n\r\n  async createDemoAccount() {\r\n    this.isLoading = true;\r\n    this.errorMessage = '';\r\n\r\n    try {\r\n      console.log('Creating demo secretary account...');\r\n\r\n      // Create Firebase user\r\n      const userCredential = await this.firebaseService.signUp(\r\n        '<EMAIL>',\r\n        'secretary123',\r\n        {\r\n          name: 'Demo Secretary',\r\n          role: 'secretary'\r\n        }\r\n      );\r\n\r\n      console.log('Demo user created:', userCredential);\r\n\r\n      // Create secretary profile\r\n      const secretaryProfile = {\r\n        uid: userCredential.uid,\r\n        email: '<EMAIL>',\r\n        name: 'Demo Secretary',\r\n        phone: '+**********',\r\n        role: 'secretary' as const,\r\n        linkedLawyers: [],\r\n        permissions: {\r\n          canManageCalendar: true,\r\n          canManageFiles: true,\r\n          canManageCases: true,\r\n          canManageRetainers: false,\r\n          canViewFinances: true,\r\n          canManageFinances: false\r\n        },\r\n        createdAt: new Date(),\r\n        updatedAt: new Date()\r\n      };\r\n\r\n      await this.firebaseService.createSecretaryProfile(secretaryProfile);\r\n\r\n      alert('Demo account created successfully! You can now login with:\\nEmail: <EMAIL>\\nPassword: secretary123');\r\n\r\n      // Auto-fill the form\r\n      this.email = '<EMAIL>';\r\n      this.password = 'secretary123';\r\n\r\n    } catch (error: any) {\r\n      console.error('Demo account creation error:', error);\r\n\r\n      if (error.code === 'auth/email-already-in-use') {\r\n        this.errorMessage = 'Demo account already exists. You can login with the provided credentials.';\r\n        // Auto-fill the form\r\n        this.email = '<EMAIL>';\r\n        this.password = 'secretary123';\r\n      } else {\r\n        this.errorMessage = 'Failed to create demo account: ' + (error.message || 'Unknown error');\r\n      }\r\n    } finally {\r\n      this.isLoading = false;\r\n    }\r\n  }\r\n}\r\n", "import { Injectable } from '@angular/core';\r\nimport { initializeApp } from 'firebase/app';\r\nimport { \r\n  getAuth, \r\n  Auth, \r\n  signInWithEmailAndPassword, \r\n  createUserWithEmailAndPassword,\r\n  signOut,\r\n  sendPasswordResetEmail,\r\n  User\r\n} from 'firebase/auth';\r\nimport { \r\n  getFirestore, \r\n  Firestore, \r\n  doc, \r\n  setDoc, \r\n  getDoc, \r\n  updateDoc, \r\n  deleteDoc,\r\n  collection, \r\n  addDoc, \r\n  query, \r\n  where, \r\n  orderBy, \r\n  getDocs,\r\n  limit\r\n} from 'firebase/firestore';\r\nimport { getFunctions, httpsCallable } from 'firebase/functions';\r\nimport { environment } from '../../environments/environment';\r\nimport {\r\n  LawyerAvailability,\r\n  EnhancedAppointment,\r\n  RescheduleRequest,\r\n  AppointmentFilter,\r\n  AppointmentReminder,\r\n  LawyerCalendarSummary\r\n} from '../models/scheduling.models';\r\n\r\n// Interfaces\r\nexport interface SecretaryProfile {\r\n  uid: string;\r\n  email: string;\r\n  name: string;\r\n  phone?: string;\r\n  avatar?: string;\r\n  role: 'secretary';\r\n  linkedLawyers: string[]; // Array of lawyer UIDs\r\n  permissions: SecretaryPermissions;\r\n  createdAt: Date;\r\n  updatedAt: Date;\r\n}\r\n\r\nexport interface SecretaryPermissions {\r\n  canManageCalendar: boolean;\r\n  canManageFiles: boolean;\r\n  canManageCases: boolean;\r\n  canManageRetainers: boolean;\r\n  canViewFinances: boolean;\r\n  canManageFinances: boolean;\r\n}\r\n\r\nexport interface AssistantCode {\r\n  id?: string;\r\n  code: string;\r\n  lawyerId: string;\r\n  lawyerName: string;\r\n  permissions: SecretaryPermissions;\r\n  isUsed: boolean;\r\n  usedBy?: string; // Secretary UID who used the code\r\n  expiresAt: Date;\r\n  createdAt: Date;\r\n  usedAt?: Date;\r\n}\r\n\r\nexport interface LawyerSecretaryLink {\r\n  id?: string;\r\n  lawyerId: string;\r\n  secretaryId: string;\r\n  lawyerName: string;\r\n  secretaryName: string;\r\n  status: 'pending' | 'approved' | 'rejected';\r\n  permissions: SecretaryPermissions;\r\n  requestedAt: Date;\r\n  respondedAt?: Date;\r\n  createdAt: Date;\r\n}\r\n\r\nexport interface LawyerProfile {\r\n  uid: string;\r\n  email: string;\r\n  name: string;\r\n  rollNumber: string;\r\n  barId: string;\r\n  phone?: string;\r\n  avatar?: string;\r\n  role: 'lawyer';\r\n  secretaryCode?: string;\r\n  createdAt: Date;\r\n  updatedAt: Date;\r\n}\r\n\r\nexport interface AuditLog {\r\n  id?: string;\r\n  userId: string;\r\n  userRole: 'lawyer' | 'secretary' | 'client';\r\n  userName: string;\r\n  action: string;\r\n  entityType: string;\r\n  entityId: string;\r\n  changes?: any;\r\n  metadata?: any;\r\n  timestamp: Date;\r\n}\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class FirebaseService {\r\n  private app = initializeApp(environment.firebase);\r\n  private auth: Auth = getAuth(this.app);\r\n  private firestore: Firestore = getFirestore(this.app);\r\n  private functions = getFunctions(this.app);\r\n\r\n  constructor() { }\r\n\r\n  // Authentication Methods\r\n  async signIn(email: string, password: string): Promise<User> {\r\n    const userCredential = await signInWithEmailAndPassword(this.auth, email, password);\r\n    return userCredential.user;\r\n  }\r\n\r\n  async signUp(email: string, password: string, userData: any): Promise<User> {\r\n    const userCredential = await createUserWithEmailAndPassword(this.auth, email, password);\r\n    return userCredential.user;\r\n  }\r\n\r\n  async signOut(): Promise<void> {\r\n    await signOut(this.auth);\r\n  }\r\n\r\n  async resetPassword(email: string): Promise<void> {\r\n    await sendPasswordResetEmail(this.auth, email);\r\n  }\r\n\r\n  getCurrentUser(): User | null {\r\n    return this.auth.currentUser;\r\n  }\r\n\r\n  // Secretary Profile Methods\r\n  async createSecretaryProfile(profile: SecretaryProfile): Promise<void> {\r\n    const docRef = doc(this.firestore, 'secretaries', profile.uid);\r\n    await setDoc(docRef, profile);\r\n    await this.logActivity(profile.uid, 'secretary', profile.name, 'CREATE', 'secretary_profile', profile.uid);\r\n  }\r\n\r\n  async getSecretaryProfile(uid: string): Promise<SecretaryProfile | null> {\r\n    const docRef = doc(this.firestore, 'secretaries', uid);\r\n    const docSnap = await getDoc(docRef);\r\n\r\n    if (docSnap.exists()) {\r\n      return docSnap.data() as SecretaryProfile;\r\n    }\r\n    return null;\r\n  }\r\n\r\n  async updateSecretaryProfile(uid: string, updates: Partial<SecretaryProfile>): Promise<void> {\r\n    const docRef = doc(this.firestore, 'secretaries', uid);\r\n    await updateDoc(docRef, { ...updates, updatedAt: new Date() });\r\n\r\n    const secretary = await this.getSecretaryProfile(uid);\r\n    if (secretary) {\r\n      await this.logActivity(uid, 'secretary', secretary.name, 'UPDATE', 'secretary_profile', uid, updates);\r\n    }\r\n  }\r\n\r\n  // Lawyer Profile Methods (for secretary access)\r\n  async getLawyerProfile(uid: string): Promise<LawyerProfile | null> {\r\n    const docRef = doc(this.firestore, 'lawyers', uid);\r\n    const docSnap = await getDoc(docRef);\r\n\r\n    if (docSnap.exists()) {\r\n      return docSnap.data() as LawyerProfile;\r\n    }\r\n    return null;\r\n  }\r\n\r\n  async getSecretaryLinkedLawyers(secretaryId: string): Promise<LawyerProfile[]> {\r\n    const secretary = await this.getSecretaryProfile(secretaryId);\r\n    if (!secretary || secretary.linkedLawyers.length === 0) {\r\n      return [];\r\n    }\r\n\r\n    const lawyers: LawyerProfile[] = [];\r\n    for (const lawyerId of secretary.linkedLawyers) {\r\n      const lawyer = await this.getLawyerProfile(lawyerId);\r\n      if (lawyer) {\r\n        lawyers.push(lawyer);\r\n      }\r\n    }\r\n    return lawyers;\r\n  }\r\n\r\n  // Assistant Code Management Methods\r\n  async validateAssistantCode(code: string): Promise<AssistantCode | null> {\r\n    const q = query(\r\n      collection(this.firestore, 'assistant_codes'),\r\n      where('code', '==', code),\r\n      where('isUsed', '==', false)\r\n    );\r\n\r\n    const querySnapshot = await getDocs(q);\r\n\r\n    if (querySnapshot.empty) {\r\n      return null;\r\n    }\r\n\r\n    const doc = querySnapshot.docs[0];\r\n    const assistantCode = { id: doc.id, ...doc.data() } as AssistantCode;\r\n\r\n    // Check if code has expired\r\n    const expiryDate = assistantCode.expiresAt instanceof Date\r\n      ? assistantCode.expiresAt\r\n      : (assistantCode.expiresAt as any).toDate();\r\n\r\n    if (new Date() > expiryDate) {\r\n      return null;\r\n    }\r\n\r\n    return assistantCode;\r\n  }\r\n\r\n  async useAssistantCode(\r\n    code: string,\r\n    secretaryId: string\r\n  ): Promise<LawyerSecretaryLink> {\r\n    try {\r\n      const linkSecretary = httpsCallable(this.functions, 'linkSecretary');\r\n      const result = await linkSecretary({ code });\r\n\r\n      if (result.data && (result.data as any).success) {\r\n        return (result.data as any).link;\r\n      } else {\r\n        throw new Error('Failed to link secretary');\r\n      }\r\n    } catch (error) {\r\n      console.error('Error calling linkSecretary function:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  async getLinkedLawyers(secretaryId: string): Promise<LawyerSecretaryLink[]> {\r\n    const q = query(\r\n      collection(this.firestore, 'lawyer_secretary_links'),\r\n      where('secretaryId', '==', secretaryId),\r\n      where('status', '==', 'approved')\r\n    );\r\n\r\n    const querySnapshot = await getDocs(q);\r\n    return querySnapshot.docs.map(doc => ({\r\n      id: doc.id,\r\n      ...doc.data()\r\n    } as LawyerSecretaryLink));\r\n  }\r\n\r\n  async checkSecretaryPermission(\r\n    secretaryId: string,\r\n    lawyerId: string,\r\n    permission: keyof SecretaryPermissions\r\n  ): Promise<boolean> {\r\n    const q = query(\r\n      collection(this.firestore, 'lawyer_secretary_links'),\r\n      where('secretaryId', '==', secretaryId),\r\n      where('lawyerId', '==', lawyerId),\r\n      where('status', '==', 'approved')\r\n    );\r\n\r\n    const querySnapshot = await getDocs(q);\r\n\r\n    if (querySnapshot.empty) {\r\n      return false;\r\n    }\r\n\r\n    const link = querySnapshot.docs[0].data() as LawyerSecretaryLink;\r\n    return link.permissions[permission] || false;\r\n  }\r\n\r\n  async getAuditLogs(\r\n    entityType?: string,\r\n    entityId?: string,\r\n    userId?: string,\r\n    limitCount: number = 50\r\n  ): Promise<AuditLog[]> {\r\n    let q = query(\r\n      collection(this.firestore, 'audit_logs'),\r\n      orderBy('timestamp', 'desc')\r\n    );\r\n\r\n    if (entityType) {\r\n      q = query(q, where('entityType', '==', entityType));\r\n    }\r\n\r\n    if (entityId) {\r\n      q = query(q, where('entityId', '==', entityId));\r\n    }\r\n\r\n    if (userId) {\r\n      q = query(q, where('userId', '==', userId));\r\n    }\r\n\r\n    const querySnapshot = await getDocs(q);\r\n    return querySnapshot.docs.slice(0, limitCount).map(doc => ({\r\n      id: doc.id,\r\n      ...doc.data()\r\n    } as AuditLog));\r\n  }\r\n\r\n  // Lawyer Availability Management Methods\r\n  async getLawyerAvailability(lawyerId: string, date?: string): Promise<LawyerAvailability[]> {\r\n    let q = query(\r\n      collection(this.firestore, 'availability'),\r\n      where('lawyerId', '==', lawyerId)\r\n    );\r\n\r\n    if (date) {\r\n      q = query(q, where('date', '==', date));\r\n    }\r\n\r\n    q = query(q, orderBy('date', 'asc'));\r\n\r\n    const querySnapshot = await getDocs(q);\r\n    return querySnapshot.docs.map(doc => ({\r\n      id: doc.id,\r\n      ...doc.data()\r\n    } as LawyerAvailability));\r\n  }\r\n\r\n  async createLawyerAvailability(availability: Omit<LawyerAvailability, 'id'>): Promise<string> {\r\n    const docRef = await addDoc(collection(this.firestore, 'availability'), {\r\n      ...availability,\r\n      createdAt: new Date(),\r\n      updatedAt: new Date()\r\n    });\r\n\r\n    // Log activity\r\n    const secretary = await this.getSecretaryProfile(availability.createdBy);\r\n    if (secretary) {\r\n      await this.logActivity(\r\n        availability.createdBy,\r\n        'secretary',\r\n        secretary.name,\r\n        'CREATE',\r\n        'availability',\r\n        docRef.id,\r\n        { lawyerId: availability.lawyerId, date: availability.date, timeSlots: availability.timeSlots }\r\n      );\r\n    }\r\n\r\n    return docRef.id;\r\n  }\r\n\r\n  async updateLawyerAvailability(\r\n    availabilityId: string,\r\n    updates: Partial<LawyerAvailability>,\r\n    updatedBy: string\r\n  ): Promise<void> {\r\n    const docRef = doc(this.firestore, 'availability', availabilityId);\r\n    await updateDoc(docRef, { ...updates, updatedAt: new Date() });\r\n\r\n    // Log activity\r\n    const secretary = await this.getSecretaryProfile(updatedBy);\r\n    if (secretary) {\r\n      await this.logActivity(\r\n        updatedBy,\r\n        'secretary',\r\n        secretary.name,\r\n        'UPDATE',\r\n        'availability',\r\n        availabilityId,\r\n        updates\r\n      );\r\n    }\r\n  }\r\n\r\n  async deleteLawyerAvailability(availabilityId: string, deletedBy: string): Promise<void> {\r\n    const docRef = doc(this.firestore, 'availability', availabilityId);\r\n    await deleteDoc(docRef);\r\n\r\n    // Log activity\r\n    const secretary = await this.getSecretaryProfile(deletedBy);\r\n    if (secretary) {\r\n      await this.logActivity(\r\n        deletedBy,\r\n        'secretary',\r\n        secretary.name,\r\n        'DELETE',\r\n        'availability',\r\n        availabilityId\r\n      );\r\n    }\r\n  }\r\n\r\n  // Enhanced Appointment Management Methods\r\n  async createAppointment(appointmentData: Omit<EnhancedAppointment, 'id'>): Promise<string> {\r\n    const docRef = await addDoc(collection(this.firestore, 'appointments'), {\r\n      ...appointmentData,\r\n      createdAt: new Date(),\r\n      updatedAt: new Date()\r\n    });\r\n\r\n    // Log the activity\r\n    await this.logActivity(\r\n      appointmentData.managedBy || appointmentData.lawyerId,\r\n      appointmentData.createdBy,\r\n      appointmentData.clientName,\r\n      'CREATE',\r\n      'appointment',\r\n      docRef.id,\r\n      {\r\n        lawyerName: appointmentData.lawyerName,\r\n        date: appointmentData.date,\r\n        time: appointmentData.time,\r\n        type: appointmentData.type\r\n      }\r\n    );\r\n\r\n    return docRef.id;\r\n  }\r\n\r\n  async updateAppointment(appointmentId: string, updates: Partial<EnhancedAppointment>): Promise<void> {\r\n    const docRef = doc(this.firestore, 'appointments', appointmentId);\r\n    await updateDoc(docRef, {\r\n      ...updates,\r\n      updatedAt: new Date()\r\n    });\r\n\r\n    // Log the activity\r\n    if (updates.lastModifiedBy) {\r\n      await this.logActivity(\r\n        updates.lastModifiedBy,\r\n        updates.lastModifiedByRole || 'secretary',\r\n        updates.clientName || 'Unknown',\r\n        'UPDATE',\r\n        'appointment',\r\n        appointmentId,\r\n        updates\r\n      );\r\n    }\r\n  }\r\n\r\n  async deleteAppointment(appointmentId: string, deletedBy: string, deletedByRole: 'lawyer' | 'secretary' | 'client'): Promise<void> {\r\n    const docRef = doc(this.firestore, 'appointments', appointmentId);\r\n\r\n    // Get appointment details for logging\r\n    const docSnap = await getDoc(docRef);\r\n    const appointment = docSnap.data() as EnhancedAppointment;\r\n\r\n    await deleteDoc(docRef);\r\n\r\n    // Log the activity\r\n    await this.logActivity(\r\n      deletedBy,\r\n      deletedByRole,\r\n      appointment.clientName,\r\n      'DELETE',\r\n      'appointment',\r\n      appointmentId,\r\n      {\r\n        lawyerName: appointment.lawyerName,\r\n        date: appointment.date,\r\n        time: appointment.time,\r\n        type: appointment.type\r\n      }\r\n    );\r\n  }\r\n\r\n  async getAppointmentsForSecretary(\r\n    secretaryId: string,\r\n    filter?: AppointmentFilter\r\n  ): Promise<EnhancedAppointment[]> {\r\n    const secretary = await this.getSecretaryProfile(secretaryId);\r\n    if (!secretary || secretary.linkedLawyers.length === 0) {\r\n      return [];\r\n    }\r\n\r\n    let q = query(\r\n      collection(this.firestore, 'appointments'),\r\n      where('lawyerId', 'in', secretary.linkedLawyers)\r\n    );\r\n\r\n    if (filter?.status && filter.status.length > 0) {\r\n      q = query(q, where('status', 'in', filter.status));\r\n    }\r\n\r\n    if (filter?.dateFrom) {\r\n      q = query(q, where('date', '>=', filter.dateFrom));\r\n    }\r\n\r\n    if (filter?.dateTo) {\r\n      q = query(q, where('date', '<=', filter.dateTo));\r\n    }\r\n\r\n    q = query(q, orderBy('date', 'asc'), orderBy('time', 'asc'));\r\n\r\n    const querySnapshot = await getDocs(q);\r\n    let appointments = querySnapshot.docs.map(doc => ({\r\n      id: doc.id,\r\n      ...doc.data()\r\n    } as EnhancedAppointment));\r\n\r\n    // Apply additional filters that can't be done in Firestore query\r\n    if (filter?.type && filter.type.length > 0) {\r\n      appointments = appointments.filter(apt => filter.type!.includes(apt.type));\r\n    }\r\n\r\n    if (filter?.isUrgent !== undefined) {\r\n      appointments = appointments.filter(apt => apt.isUrgent === filter.isUrgent);\r\n    }\r\n\r\n    if (filter?.createdBy && filter.createdBy.length > 0) {\r\n      appointments = appointments.filter(apt => filter.createdBy!.includes(apt.createdBy));\r\n    }\r\n\r\n    return appointments;\r\n  }\r\n\r\n  async getAppointmentsByLawyer(lawyerId: string, dateFrom?: string, dateTo?: string): Promise<EnhancedAppointment[]> {\r\n    let q = query(\r\n      collection(this.firestore, 'appointments'),\r\n      where('lawyerId', '==', lawyerId)\r\n    );\r\n\r\n    if (dateFrom) {\r\n      q = query(q, where('date', '>=', dateFrom));\r\n    }\r\n\r\n    if (dateTo) {\r\n      q = query(q, where('date', '<=', dateTo));\r\n    }\r\n\r\n    q = query(q, orderBy('date', 'asc'), orderBy('time', 'asc'));\r\n\r\n    const querySnapshot = await getDocs(q);\r\n    return querySnapshot.docs.map(doc => ({\r\n      id: doc.id,\r\n      ...doc.data()\r\n    } as EnhancedAppointment));\r\n  }\r\n\r\n  async updateAppointmentStatus(\r\n    appointmentId: string,\r\n    status: string,\r\n    updatedBy: string,\r\n    reason?: string\r\n  ): Promise<void> {\r\n    const updates: any = {\r\n      status,\r\n      lastModifiedBy: updatedBy,\r\n      lastModifiedByRole: 'secretary',\r\n      updatedAt: new Date()\r\n    };\r\n\r\n    if (reason) {\r\n      updates.remarks = reason;\r\n    }\r\n\r\n    const docRef = doc(this.firestore, 'appointments', appointmentId);\r\n    await updateDoc(docRef, updates);\r\n\r\n    // Log activity\r\n    const secretary = await this.getSecretaryProfile(updatedBy);\r\n    if (secretary) {\r\n      await this.logActivity(\r\n        updatedBy,\r\n        'secretary',\r\n        secretary.name,\r\n        'UPDATE',\r\n        'appointment',\r\n        appointmentId,\r\n        { status, reason }\r\n      );\r\n    }\r\n  }\r\n\r\n  // Reschedule Request Management Methods\r\n  async createRescheduleRequest(request: Omit<RescheduleRequest, 'id'>): Promise<string> {\r\n    const docRef = await addDoc(collection(this.firestore, 'reschedule_requests'), {\r\n      ...request,\r\n      createdAt: new Date(),\r\n      expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) // Expires in 7 days\r\n    });\r\n\r\n    // Log activity\r\n    await this.logActivity(\r\n      request.requestedBy,\r\n      'secretary',\r\n      request.requestedByName,\r\n      'CREATE',\r\n      'reschedule_request',\r\n      docRef.id,\r\n      {\r\n        appointmentId: request.appointmentId,\r\n        originalDate: request.originalDate,\r\n        proposedDate: request.proposedDate,\r\n        reason: request.reason\r\n      }\r\n    );\r\n\r\n    return docRef.id;\r\n  }\r\n\r\n  async getRescheduleRequests(appointmentId?: string): Promise<RescheduleRequest[]> {\r\n    let q = query(collection(this.firestore, 'reschedule_requests'));\r\n\r\n    if (appointmentId) {\r\n      q = query(q, where('appointmentId', '==', appointmentId));\r\n    }\r\n\r\n    q = query(q, orderBy('createdAt', 'desc'));\r\n\r\n    const querySnapshot = await getDocs(q);\r\n    return querySnapshot.docs.map(doc => ({\r\n      id: doc.id,\r\n      ...doc.data()\r\n    } as RescheduleRequest));\r\n  }\r\n\r\n  async updateRescheduleRequestStatus(\r\n    requestId: string,\r\n    status: 'approved' | 'rejected',\r\n    respondedBy: string,\r\n    response?: string\r\n  ): Promise<void> {\r\n    const updates: any = {\r\n      status,\r\n      respondedAt: new Date()\r\n    };\r\n\r\n    if (response) {\r\n      updates.clientResponse = response;\r\n    }\r\n\r\n    const docRef = doc(this.firestore, 'reschedule_requests', requestId);\r\n    await updateDoc(docRef, updates);\r\n\r\n    // If approved, update the original appointment\r\n    if (status === 'approved') {\r\n      const request = await this.getRescheduleRequest(requestId);\r\n      if (request) {\r\n        await this.updateAppointmentFromReschedule(request);\r\n      }\r\n    }\r\n\r\n    // Log activity\r\n    await this.logActivity(\r\n      respondedBy,\r\n      'secretary',\r\n      'Secretary',\r\n      'UPDATE',\r\n      'reschedule_request',\r\n      requestId,\r\n      { status, response }\r\n    );\r\n  }\r\n\r\n  private async getRescheduleRequest(requestId: string): Promise<RescheduleRequest | null> {\r\n    const docRef = doc(this.firestore, 'reschedule_requests', requestId);\r\n    const docSnap = await getDoc(docRef);\r\n\r\n    if (docSnap.exists()) {\r\n      return { id: docSnap.id, ...docSnap.data() } as RescheduleRequest;\r\n    }\r\n    return null;\r\n  }\r\n\r\n  private async updateAppointmentFromReschedule(request: RescheduleRequest): Promise<void> {\r\n    const updates = {\r\n      originalDate: request.originalDate,\r\n      originalTime: request.originalTime,\r\n      date: request.proposedDate,\r\n      time: request.proposedTime,\r\n      status: 'rescheduled',\r\n      rescheduleReason: request.reason,\r\n      rescheduleRequestedBy: request.requestedBy,\r\n      rescheduleRequestedAt: request.createdAt,\r\n      updatedAt: new Date()\r\n    };\r\n\r\n    const docRef = doc(this.firestore, 'appointments', request.appointmentId);\r\n    await updateDoc(docRef, updates);\r\n  }\r\n\r\n  // Calendar Summary Methods\r\n  async getLawyerCalendarSummary(lawyerId: string, date: string): Promise<LawyerCalendarSummary> {\r\n    const lawyer = await this.getLawyerProfile(lawyerId);\r\n    const appointments = await this.getAppointmentsByLawyer(lawyerId, date, date);\r\n    const availability = await this.getLawyerAvailability(lawyerId, date);\r\n\r\n    const totalAppointments = appointments.length;\r\n    const confirmedAppointments = appointments.filter(apt => apt.status === 'confirmed').length;\r\n    const pendingAppointments = appointments.filter(apt => apt.status === 'pending').length;\r\n\r\n    // Calculate available slots\r\n    let availableSlots = 0;\r\n    availability.forEach(avail => {\r\n      const bookedSlots = appointments.filter(apt =>\r\n        apt.date === avail.date && avail.timeSlots.includes(apt.time)\r\n      ).length;\r\n      availableSlots += Math.max(0, avail.timeSlots.length - bookedSlots);\r\n    });\r\n\r\n    // Find next appointment\r\n    const futureAppointments = appointments.filter(apt =>\r\n      new Date(apt.date + ' ' + apt.time) > new Date()\r\n    ).sort((a, b) =>\r\n      new Date(a.date + ' ' + a.time).getTime() - new Date(b.date + ' ' + b.time).getTime()\r\n    );\r\n\r\n    const nextAppointment = futureAppointments.length > 0 ? {\r\n      date: futureAppointments[0].date,\r\n      time: futureAppointments[0].time,\r\n      clientName: futureAppointments[0].clientName\r\n    } : undefined;\r\n\r\n    return {\r\n      lawyerId,\r\n      lawyerName: lawyer?.name || 'Unknown',\r\n      totalAppointments,\r\n      confirmedAppointments,\r\n      pendingAppointments,\r\n      availableSlots,\r\n      nextAppointment\r\n    };\r\n  }\r\n\r\n  // Reminder Management Methods\r\n  async createAppointmentReminder(reminder: Omit<AppointmentReminder, 'id'>): Promise<string> {\r\n    const docRef = await addDoc(collection(this.firestore, 'appointment_reminders'), {\r\n      ...reminder,\r\n      createdAt: new Date()\r\n    });\r\n    return docRef.id;\r\n  }\r\n\r\n  async getPendingReminders(): Promise<AppointmentReminder[]> {\r\n    const q = query(\r\n      collection(this.firestore, 'appointment_reminders'),\r\n      where('status', '==', 'pending'),\r\n      where('scheduledFor', '<=', new Date())\r\n    );\r\n\r\n    const querySnapshot = await getDocs(q);\r\n    return querySnapshot.docs.map(doc => ({\r\n      id: doc.id,\r\n      ...doc.data()\r\n    } as AppointmentReminder));\r\n  }\r\n\r\n  async markReminderAsSent(reminderId: string): Promise<void> {\r\n    const docRef = doc(this.firestore, 'appointment_reminders', reminderId);\r\n    await updateDoc(docRef, {\r\n      status: 'sent',\r\n      sentAt: new Date()\r\n    });\r\n  }\r\n\r\n  // Audit Logging Methods\r\n  async logActivity(\r\n    userId: string,\r\n    userRole: 'lawyer' | 'secretary' | 'client',\r\n    userName: string,\r\n    action: string,\r\n    entityType: string,\r\n    entityId: string,\r\n    changes?: any,\r\n    metadata?: any\r\n  ): Promise<void> {\r\n    const auditLog: AuditLog = {\r\n      userId,\r\n      userRole,\r\n      userName,\r\n      action,\r\n      entityType,\r\n      entityId,\r\n      changes,\r\n      metadata,\r\n      timestamp: new Date()\r\n    };\r\n\r\n    await addDoc(collection(this.firestore, 'audit_logs'), auditLog);\r\n  }\r\n}\r\n", "export const environment = {\r\n  production: false,\r\n  firebase: {\r\n    apiKey: \"AIzaSyDll_CbcZsrTCgUBFGt36TNarkpwc4ubHQ\",\r\n    authDomain: \"veritus-620ca.firebaseapp.com\",\r\n    projectId: \"veritus-620ca\",\r\n    storageBucket: \"veritus-620ca.firebasestorage.app\",\r\n    messagingSenderId: \"931230393501\",\r\n    appId: \"1:931230393501:web:2ef920ceb1588daf2da074\"\r\n  }\r\n};\r\n", "import { platformBrowserDynamic } from '@angular/platform-browser-dynamic';\r\nimport { AppModule } from './app/app.module';\r\n\r\nplatformBrowserDynamic().bootstrapModule(AppModule)\r\n  .catch(err => console.error(err));\r\n", "var map = {\n\t\"./ion-accordion_2.entry.js\": [\n\t\t7518,\n\t\t\"common\",\n\t\t\"node_modules_ionic_core_dist_esm_ion-accordion_2_entry_js\"\n\t],\n\t\"./ion-action-sheet.entry.js\": [\n\t\t1981,\n\t\t\"common\",\n\t\t\"node_modules_ionic_core_dist_esm_ion-action-sheet_entry_js\"\n\t],\n\t\"./ion-alert.entry.js\": [\n\t\t1603,\n\t\t\"common\",\n\t\t\"node_modules_ionic_core_dist_esm_ion-alert_entry_js\"\n\t],\n\t\"./ion-app_8.entry.js\": [\n\t\t2273,\n\t\t\"common\",\n\t\t\"node_modules_ionic_core_dist_esm_ion-app_8_entry_js\"\n\t],\n\t\"./ion-avatar_3.entry.js\": [\n\t\t9642,\n\t\t\"node_modules_ionic_core_dist_esm_ion-avatar_3_entry_js\"\n\t],\n\t\"./ion-back-button.entry.js\": [\n\t\t2095,\n\t\t\"common\",\n\t\t\"node_modules_ionic_core_dist_esm_ion-back-button_entry_js\"\n\t],\n\t\"./ion-backdrop.entry.js\": [\n\t\t2335,\n\t\t\"node_modules_ionic_core_dist_esm_ion-backdrop_entry_js\"\n\t],\n\t\"./ion-breadcrumb_2.entry.js\": [\n\t\t8221,\n\t\t\"common\",\n\t\t\"node_modules_ionic_core_dist_esm_ion-breadcrumb_2_entry_js\"\n\t],\n\t\"./ion-button_2.entry.js\": [\n\t\t7184,\n\t\t\"node_modules_ionic_core_dist_esm_ion-button_2_entry_js\"\n\t],\n\t\"./ion-card_5.entry.js\": [\n\t\t8759,\n\t\t\"node_modules_ionic_core_dist_esm_ion-card_5_entry_js\"\n\t],\n\t\"./ion-checkbox.entry.js\": [\n\t\t4248,\n\t\t\"node_modules_ionic_core_dist_esm_ion-checkbox_entry_js\"\n\t],\n\t\"./ion-chip.entry.js\": [\n\t\t9863,\n\t\t\"node_modules_ionic_core_dist_esm_ion-chip_entry_js\"\n\t],\n\t\"./ion-col_3.entry.js\": [\n\t\t1769,\n\t\t\"node_modules_ionic_core_dist_esm_ion-col_3_entry_js\"\n\t],\n\t\"./ion-datetime-button.entry.js\": [\n\t\t2569,\n\t\t\"default-node_modules_ionic_core_dist_esm_data-bb424ba8_js\",\n\t\t\"node_modules_ionic_core_dist_esm_ion-datetime-button_entry_js\"\n\t],\n\t\"./ion-datetime_3.entry.js\": [\n\t\t6534,\n\t\t\"default-node_modules_ionic_core_dist_esm_data-bb424ba8_js\",\n\t\t\"common\",\n\t\t\"node_modules_ionic_core_dist_esm_ion-datetime_3_entry_js\"\n\t],\n\t\"./ion-fab_3.entry.js\": [\n\t\t5458,\n\t\t\"common\",\n\t\t\"node_modules_ionic_core_dist_esm_ion-fab_3_entry_js\"\n\t],\n\t\"./ion-img.entry.js\": [\n\t\t654,\n\t\t\"node_modules_ionic_core_dist_esm_ion-img_entry_js\"\n\t],\n\t\"./ion-infinite-scroll_2.entry.js\": [\n\t\t6034,\n\t\t\"common\",\n\t\t\"node_modules_ionic_core_dist_esm_ion-infinite-scroll_2_entry_js\"\n\t],\n\t\"./ion-input.entry.js\": [\n\t\t761,\n\t\t\"default-node_modules_ionic_core_dist_esm_form-controller-21dd62b1_js-node_modules_ionic_core_-a176d1\",\n\t\t\"common\",\n\t\t\"node_modules_ionic_core_dist_esm_ion-input_entry_js\"\n\t],\n\t\"./ion-item-option_3.entry.js\": [\n\t\t6492,\n\t\t\"common\",\n\t\t\"node_modules_ionic_core_dist_esm_ion-item-option_3_entry_js\"\n\t],\n\t\"./ion-item_8.entry.js\": [\n\t\t9557,\n\t\t\"common\",\n\t\t\"node_modules_ionic_core_dist_esm_ion-item_8_entry_js\"\n\t],\n\t\"./ion-loading.entry.js\": [\n\t\t8353,\n\t\t\"common\",\n\t\t\"node_modules_ionic_core_dist_esm_ion-loading_entry_js\"\n\t],\n\t\"./ion-menu_3.entry.js\": [\n\t\t1024,\n\t\t\"common\",\n\t\t\"node_modules_ionic_core_dist_esm_ion-menu_3_entry_js\"\n\t],\n\t\"./ion-modal.entry.js\": [\n\t\t9160,\n\t\t\"common\",\n\t\t\"node_modules_ionic_core_dist_esm_ion-modal_entry_js\"\n\t],\n\t\"./ion-nav_2.entry.js\": [\n\t\t393,\n\t\t\"node_modules_ionic_core_dist_esm_ion-nav_2_entry_js\"\n\t],\n\t\"./ion-picker-column-internal.entry.js\": [\n\t\t3970,\n\t\t\"common\",\n\t\t\"node_modules_ionic_core_dist_esm_ion-picker-column-internal_entry_js\"\n\t],\n\t\"./ion-picker-internal.entry.js\": [\n\t\t437,\n\t\t\"node_modules_ionic_core_dist_esm_ion-picker-internal_entry_js\"\n\t],\n\t\"./ion-popover.entry.js\": [\n\t\t6772,\n\t\t\"common\",\n\t\t\"node_modules_ionic_core_dist_esm_ion-popover_entry_js\"\n\t],\n\t\"./ion-progress-bar.entry.js\": [\n\t\t4810,\n\t\t\"node_modules_ionic_core_dist_esm_ion-progress-bar_entry_js\"\n\t],\n\t\"./ion-radio_2.entry.js\": [\n\t\t4639,\n\t\t\"common\",\n\t\t\"node_modules_ionic_core_dist_esm_ion-radio_2_entry_js\"\n\t],\n\t\"./ion-range.entry.js\": [\n\t\t628,\n\t\t\"common\",\n\t\t\"node_modules_ionic_core_dist_esm_ion-range_entry_js\"\n\t],\n\t\"./ion-refresher_2.entry.js\": [\n\t\t852,\n\t\t\"common\",\n\t\t\"node_modules_ionic_core_dist_esm_ion-refresher_2_entry_js\"\n\t],\n\t\"./ion-reorder_2.entry.js\": [\n\t\t1479,\n\t\t\"common\",\n\t\t\"node_modules_ionic_core_dist_esm_ion-reorder_2_entry_js\"\n\t],\n\t\"./ion-ripple-effect.entry.js\": [\n\t\t4065,\n\t\t\"node_modules_ionic_core_dist_esm_ion-ripple-effect_entry_js\"\n\t],\n\t\"./ion-route_4.entry.js\": [\n\t\t7971,\n\t\t\"node_modules_ionic_core_dist_esm_ion-route_4_entry_js\"\n\t],\n\t\"./ion-searchbar.entry.js\": [\n\t\t3184,\n\t\t\"common\",\n\t\t\"node_modules_ionic_core_dist_esm_ion-searchbar_entry_js\"\n\t],\n\t\"./ion-segment_2.entry.js\": [\n\t\t469,\n\t\t\"common\",\n\t\t\"node_modules_ionic_core_dist_esm_ion-segment_2_entry_js\"\n\t],\n\t\"./ion-select_3.entry.js\": [\n\t\t8471,\n\t\t\"common\",\n\t\t\"node_modules_ionic_core_dist_esm_ion-select_3_entry_js\"\n\t],\n\t\"./ion-spinner.entry.js\": [\n\t\t388,\n\t\t\"common\",\n\t\t\"node_modules_ionic_core_dist_esm_ion-spinner_entry_js\"\n\t],\n\t\"./ion-split-pane.entry.js\": [\n\t\t2392,\n\t\t\"node_modules_ionic_core_dist_esm_ion-split-pane_entry_js\"\n\t],\n\t\"./ion-tab-bar_2.entry.js\": [\n\t\t6059,\n\t\t\"common\",\n\t\t\"node_modules_ionic_core_dist_esm_ion-tab-bar_2_entry_js\"\n\t],\n\t\"./ion-tab_2.entry.js\": [\n\t\t5427,\n\t\t\"node_modules_ionic_core_dist_esm_ion-tab_2_entry_js\"\n\t],\n\t\"./ion-text.entry.js\": [\n\t\t7817,\n\t\t\"node_modules_ionic_core_dist_esm_ion-text_entry_js\"\n\t],\n\t\"./ion-textarea.entry.js\": [\n\t\t1735,\n\t\t\"default-node_modules_ionic_core_dist_esm_form-controller-21dd62b1_js-node_modules_ionic_core_-a176d1\",\n\t\t\"node_modules_ionic_core_dist_esm_ion-textarea_entry_js\"\n\t],\n\t\"./ion-toast.entry.js\": [\n\t\t7510,\n\t\t\"common\",\n\t\t\"node_modules_ionic_core_dist_esm_ion-toast_entry_js\"\n\t],\n\t\"./ion-toggle.entry.js\": [\n\t\t5297,\n\t\t\"common\",\n\t\t\"node_modules_ionic_core_dist_esm_ion-toggle_entry_js\"\n\t]\n};\nfunction webpackAsyncContext(req) {\n\tif(!__webpack_require__.o(map, req)) {\n\t\treturn Promise.resolve().then(() => {\n\t\t\tvar e = new Error(\"Cannot find module '\" + req + \"'\");\n\t\t\te.code = 'MODULE_NOT_FOUND';\n\t\t\tthrow e;\n\t\t});\n\t}\n\n\tvar ids = map[req], id = ids[0];\n\treturn Promise.all(ids.slice(1).map(__webpack_require__.e)).then(() => {\n\t\treturn __webpack_require__(id);\n\t});\n}\nwebpackAsyncContext.keys = () => (Object.keys(map));\nwebpackAsyncContext.id = 8996;\nmodule.exports = webpackAsyncContext;", "function webpackEmptyAsyncContext(req) {\n\t// Here Promise.resolve().then() is used instead of new Promise() to prevent\n\t// uncaught exception popping up in devtools\n\treturn Promise.resolve().then(() => {\n\t\tvar e = new Error(\"Cannot find module '\" + req + \"'\");\n\t\te.code = 'MODULE_NOT_FOUND';\n\t\tthrow e;\n\t});\n}\nwebpackEmptyAsyncContext.keys = () => ([]);\nwebpackEmptyAsyncContext.resolve = webpackEmptyAsyncContext;\nwebpackEmptyAsyncContext.id = 4140;\nmodule.exports = webpackEmptyAsyncContext;"], "names": ["i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "activity_r2", "ip<PERSON><PERSON><PERSON>", "ɵɵtemplate", "ActivityLogComponent_div_136_span_26_Template", "ɵɵlistener", "ActivityLogComponent_div_136_Template_button_click_28_listener", "ɵɵrestoreView", "_r1", "$implicit", "ctx_r2", "ɵɵnextContext", "ɵɵresetView", "viewActivityDetails", "ActivityLogComponent_div_136_Template_button_click_36_listener", "exportSingleActivity", "ActivityLogComponent_div_136_Template_button_click_41_listener", "flagActivity", "ɵɵclassMap", "severity", "status", "ɵɵtextInterpolate", "getActivityIcon", "action", "description", "ɵɵpipeBind2", "timestamp", "category", "ɵɵpipeBind1", "details", "user", "ɵɵproperty", "activityMenu_r4", "ActivityLogComponent_div_137_Template_mat_paginator_page_1_listener", "$event", "_r5", "onPageChange", "filteredActivities", "length", "pageSize", "ɵɵpureFunction0", "_c0", "ActivityLogComponent", "constructor", "searchTerm", "selectedDateRange", "selectedCate<PERSON><PERSON>", "selectedAction", "selectedSeverity", "currentPage", "activities", "id", "Date", "userAgent", "ngOnInit", "filterActivities", "filtered", "term", "toLowerCase", "filter", "activity", "includes", "now", "startDate", "setHours", "setDate", "getDate", "setMonth", "getMonth", "setFullYear", "getFullYear", "getActivityCount", "getTotalActivities", "exportLog", "console", "log", "refreshLog", "event", "pageIndex", "selectors", "decls", "vars", "consts", "template", "ActivityLogComponent_Template", "rf", "ctx", "ActivityLogComponent_Template_button_click_5_listener", "ActivityLogComponent_Template_button_click_9_listener", "ɵɵtwoWayListener", "ActivityLogComponent_Template_mat_select_valueChange_17_listener", "ɵɵtwoWayBindingSet", "ActivityLogComponent_Template_mat_select_selectionChange_17_listener", "ActivityLogComponent_Template_mat_select_valueChange_33_listener", "ActivityLogComponent_Template_mat_select_selectionChange_33_listener", "ActivityLogComponent_Template_mat_select_valueChange_51_listener", "ActivityLogComponent_Template_mat_select_selectionChange_51_listener", "ActivityLogComponent_Template_mat_select_valueChange_73_listener", "ActivityLogComponent_Template_mat_select_selectionChange_73_listener", "ActivityLogComponent_Template_input_ngModelChange_90_listener", "ActivityLogComponent_Template_input_input_90_listener", "ActivityLogComponent_div_136_Template", "ActivityLogComponent_div_137_Template", "ɵɵtwoWayProperty", "RouterModule", "DashboardComponent", "LoginComponent", "LinkLawyerComponent", "CalendarComponent", "FilesComponent", "FinanceComponent", "ClientsComponent", "routes", "path", "redirectTo", "pathMatch", "component", "loadChildren", "then", "m", "SecretaryTabsPageModule", "SecretaryDashboardPageModule", "SecretaryCalendarPageModule", "SecretaryCasesPageModule", "SecretaryFilesPageModule", "SecretaryProfilePageModule", "AppRoutingModule", "forRoot", "imports", "i1", "exports", "Breakpoints", "map", "shareReplay", "NavigationEnd", "ɵɵelement", "AppComponent_mat_sidenav_container_1_button_66_Template_button_click_0_listener", "_r3", "drawer_r4", "ɵɵreference", "toggle", "AppComponent_mat_sidenav_container_1_Template_button_click_59_listener", "ctx_r1", "logout", "AppComponent_mat_sidenav_container_1_button_66_Template", "AppComponent_mat_sidenav_container_1_Template_button_click_81_listener", "isHandset$", "currentUser", "name", "userMenu_r5", "photoURL", "ɵɵsanitizeUrl", "AppComponent", "breakpointObserver", "router", "firebaseService", "title", "isLoginPage", "observe", "Handset", "pipe", "result", "matches", "events", "subscribe", "navigationEvent", "url", "startsWith", "_this", "_asyncToGenerator", "confirmed", "confirm", "signOut", "localStorage", "clear", "navigate", "error", "window", "location", "href", "ɵɵdirectiveInject", "BreakpointObserver", "i2", "Router", "i3", "FirebaseService", "AppComponent_Template", "AppComponent_div_0_Template", "AppComponent_mat_sidenav_container_1_Template", "BrowserModule", "BrowserAnimationsModule", "FormsModule", "ReactiveFormsModule", "HttpClientModule", "IonicModule", "MatToolbarModule", "MatButtonModule", "MatCardModule", "MatInputModule", "MatFormFieldModule", "MatIconModule", "MatSidenavModule", "MatListModule", "MatGridListModule", "MatMenuModule", "MatTableModule", "MatPaginatorModule", "MatSortModule", "MatDialogModule", "MatSnackBarModule", "MatProgressSpinnerModule", "MatChipsModule", "MatSelectModule", "MatDatepickerModule", "MatNativeDateModule", "LayoutModule", "initializeApp", "provideFirebaseApp", "provideAuth", "getAuth", "provideFirestore", "getFirestore", "provideFunctions", "getFunctions", "provideStorage", "getStorage", "environment", "AppModule", "bootstrap", "firebase", "declarations", "lawyer_r3", "ɵɵtextInterpolate2", "rollNumber", "CalendarComponent_div_5_Template_select_ngModelChange_3_listener", "<PERSON><PERSON><PERSON><PERSON>", "CalendarComponent_div_5_Template_select_change_3_listener", "onLawyerChange", "CalendarComponent_div_5_option_4_Template", "linkedLawyers", "day_r4", "CalendarComponent_div_29_div_1_Template_div_click_0_listener", "day_r6", "selectDate", "ɵɵclassProp", "isCurrentMonth", "isToday", "isSelected", "date", "CalendarComponent_div_29_div_1_Template", "week_r7", "appointment_r9", "time", "type", "clientName", "CalendarComponent_div_30_div_5_Template", "CalendarComponent_div_30_Template_button_click_6_listener", "_r8", "openNewBookingDialog", "selectedDate", "getAppointmentsForDate", "CalendarComponent_div_34_Template_button_click_16_listener", "appointment_r11", "_r10", "editAppointment", "CalendarComponent_div_34_Template_button_click_19_listener", "deleteAppointment", "modalController", "toastController", "alertController", "currentMonth", "weekDays", "calendarWeeks", "isLoading", "appointments", "upcomingAppointments", "generateCalendar", "loadLinkedLawyers", "loadAppointments", "slice", "_this2", "getCurrentUser", "getSecretaryLinkedLawyers", "uid", "showToast", "_this3", "firebaseAppointments", "getAppointmentsForSecretary", "apt", "year", "month", "firstDay", "lastDay", "getDay", "currentDate", "week", "day", "push", "fullDate", "today", "toDateString", "for<PERSON>ach", "d", "previousMonth", "nextMonth", "_this4", "alert", "create", "header", "message", "inputs", "label", "value", "checked", "lawyer", "placeholder", "buttons", "text", "role", "handler", "data", "createNewBooking", "present", "_this5", "appointmentType", "find", "l", "appointmentData", "lawyerId", "<PERSON><PERSON><PERSON>", "toISOString", "split", "created<PERSON>y", "managedBy", "lastModifiedBy", "lastModifiedByRole", "remarks", "is<PERSON><PERSON>", "createdAt", "updatedAt", "appointmentId", "createAppointment", "newAppointment", "appointment", "_this6", "updateAppointment", "_this7", "updates", "index", "findIndex", "_this8", "confirmDeleteAppointment", "_this9", "deletedAppointment", "splice", "_x", "_this0", "color", "toast", "duration", "position", "apply", "arguments", "ModalController", "ToastController", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "CalendarComponent_Template", "CalendarComponent_div_5_Template", "CalendarComponent_Template_button_click_10_listener", "CalendarComponent_Template_button_click_17_listener", "CalendarComponent_Template_button_click_22_listener", "CalendarComponent_div_27_Template", "CalendarComponent_div_29_Template", "CalendarComponent_div_30_Template", "CalendarComponent_div_34_Template", "ClientsComponent_div_58_Template_button_click_48_listener", "client_r2", "viewClient", "ClientsComponent_div_58_Template_button_click_51_listener", "editClient", "ClientsComponent_div_58_Template_button_click_54_listener", "contactClient", "ClientsComponent_div_58_Template_button_click_62_listener", "viewCases", "ClientsComponent_div_58_Template_button_click_67_listener", "viewTransactions", "ClientsComponent_div_58_Template_button_click_72_listener", "deactivateClient", "email", "phone", "retainer<PERSON><PERSON>", "caseCount", "joinDate", "getTimeAgo", "lastContact", "clientMenu_r4", "statusFilter", "clients", "filteredClients", "getClientCount", "client", "getTotalRetainer", "reduce", "total", "filterClients", "diffTime", "Math", "abs", "getTime", "diffDays", "ceil", "toLocaleDateString", "openAddClientDialog", "ClientsComponent_Template", "ClientsComponent_Template_button_click_4_listener", "ClientsComponent_Template_input_ngModelChange_46_listener", "ClientsComponent_Template_input_input_46_listener", "ClientsComponent_Template_mat_select_valueChange_48_listener", "ClientsComponent_Template_mat_select_selectionChange_48_listener", "ClientsComponent_div_58_Template", "day_r1", "DashboardComponent_div_15_Template_div_click_0_listener", "day_r3", "_r2", "ctx_r3", "otherMonth", "hasAppointment", "appointment_r5", "client_r6", "doc_r7", "ɵɵstyleProp", "bar_r8", "height", "label_r9", "transaction_r10", "amount", "currentYear", "selectedDateString", "dayHeaders", "calendarDays", "todayAppointments", "retainerClients", "recentDocuments", "chartData", "chartLabels", "recentTransactions", "DashboardComponent_Template", "DashboardComponent_Template_button_click_7_listener", "DashboardComponent_Template_button_click_11_listener", "DashboardComponent_div_14_Template", "DashboardComponent_div_15_Template", "DashboardComponent_div_20_Template", "DashboardComponent_div_30_Template", "DashboardComponent_Template_input_ngModelChange_37_listener", "DashboardComponent_div_64_Template", "DashboardComponent_div_73_Template", "DashboardComponent_span_75_Template", "DashboardComponent_div_81_Template", "getDocumentIcon", "doc_r1", "size", "uploadDate", "documents", "filteredDocuments", "getCategoryCount", "doc", "selectCategory", "filterDocuments", "FilesComponent_Template", "FilesComponent_Template_input_ngModelChange_8_listener", "FilesComponent_Template_input_input_8_listener", "FilesComponent_Template_div_click_12_listener", "FilesComponent_Template_div_click_20_listener", "FilesComponent_Template_div_click_28_listener", "FilesComponent_Template_div_click_36_listener", "FilesComponent_div_53_Template", "label_r1", "getBarHeight", "month_r2", "consultation", "retainer", "transaction_r4", "FinanceComponent_div_42_span_11_Template", "getTransactionIcon", "formatAmount", "<PERSON><PERSON><PERSON><PERSON>", "totalRevenue", "totalExpenses", "netProfit", "yAxisLabels", "selectPeriod", "period", "prefix", "toLocaleString", "formatNumber", "num", "openNewTransactionDialog", "FinanceComponent_Template", "FinanceComponent_Template_button_click_4_listener", "FinanceComponent_Template_button_click_14_listener", "FinanceComponent_Template_button_click_16_listener", "FinanceComponent_div_22_Template", "FinanceComponent_div_24_Template", "FinanceComponent_div_42_Template", "LinkLawyerComponent_div_28_mat_card_4_mat_chip_11_Template", "LinkLawyerComponent_div_28_mat_card_4_mat_chip_12_Template", "LinkLawyerComponent_div_28_mat_card_4_mat_chip_13_Template", "lawyer_r1", "formatDate", "permissions", "canManageCalendar", "canManageFiles", "canManageCases", "LinkLawyerComponent_div_28_mat_card_4_Template", "<PERSON><PERSON><PERSON>", "codePreview", "isLinking", "getLinkedLawyers", "onCodeInput", "target", "toUpperCase", "isValidCode", "linkToLawyer", "codeData", "validateAssistantCode", "alreadyLinked", "some", "link", "confirmLink", "useAssistantCode", "unshift", "cancelPreview", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "setItem", "JSON", "stringify", "LinkLawyerComponent_Template", "LinkLawyerComponent_Template_input_ngModelChange_17_listener", "LinkLawyerComponent_Template_input_input_17_listener", "LinkLawyerComponent_Template_button_click_24_listener", "LinkLawyerComponent_div_28_Template", "ctx_r0", "errorMessage", "password", "hidePassword", "login", "signIn", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "getSecretaryProfile", "Error", "code", "forgotPassword", "resetPassword", "createDemoAccount", "userCredential", "signUp", "canManageRetainers", "canViewFinances", "canManageFinances", "createSecretaryProfile", "LoginComponent_Template", "LoginComponent_Template_input_ngModelChange_12_listener", "LoginComponent_Template_input_ngModelChange_18_listener", "LoginComponent_Template_button_click_19_listener", "LoginComponent_Template_button_click_30_listener", "LoginComponent_span_31_Template", "LoginComponent_span_32_Template", "LoginComponent_div_33_Template", "LoginComponent_Template_button_click_35_listener", "LoginComponent_Template_button_click_37_listener", "signInWithEmailAndPassword", "createUserWithEmailAndPassword", "sendPasswordResetEmail", "setDoc", "getDoc", "updateDoc", "deleteDoc", "collection", "addDoc", "query", "where", "orderBy", "getDocs", "httpsCallable", "app", "auth", "firestore", "functions", "userData", "profile", "doc<PERSON>ef", "logActivity", "docSnap", "exists", "updateSecretaryProfile", "secretary", "getLawyerProfile", "secretaryId", "lawyers", "q", "querySnapshot", "empty", "docs", "expiryDate", "expiresAt", "toDate", "_this1", "linkSecretary", "success", "_this10", "checkSecretaryPermission", "permission", "_this11", "getAuditLogs", "_x2", "_x3", "_this12", "entityType", "entityId", "userId", "limitCount", "getLawyerAvailability", "_this13", "createLawyerAvailability", "availability", "_this14", "timeSlots", "updateLawyerAvailability", "availabilityId", "updatedBy", "_this15", "deleteLawyerAvailability", "deletedBy", "_this16", "_this17", "_this18", "deletedByRole", "_this19", "_this20", "dateFrom", "dateTo", "undefined", "getAppointmentsByL<PERSON>yer", "_this21", "updateAppointmentStatus", "reason", "_this22", "createRescheduleRequest", "request", "_this23", "requestedBy", "requestedByName", "originalDate", "proposedDate", "getRescheduleRequests", "_this24", "updateRescheduleRequestStatus", "requestId", "respondedBy", "response", "_this25", "respondedAt", "clientResponse", "getRescheduleRequest", "updateAppointmentFromReschedule", "_this26", "_this27", "originalTime", "proposedTime", "rescheduleReason", "rescheduleRequestedBy", "rescheduleRequestedAt", "getLawyerCalendarSummary", "_this28", "totalAppointments", "confirmedAppointments", "pendingAppointments", "availableSlots", "avail", "bookedSlots", "max", "futureAppointments", "sort", "a", "b", "nextAppointment", "createAppointmentReminder", "reminder", "_this29", "getPendingReminders", "_this30", "markReminderAsSent", "reminderId", "_this31", "sentAt", "userRole", "userName", "changes", "metadata", "_this32", "auditLog", "factory", "ɵfac", "providedIn", "production", "<PERSON><PERSON><PERSON><PERSON>", "authDomain", "projectId", "storageBucket", "messagingSenderId", "appId", "__Ng<PERSON>li_bootstrap_1", "platformBrowser", "bootstrapModule", "catch", "err"], "sourceRoot": "webpack:///", "x_google_ignoreList": [14, 15]}