import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { Form<PERSON>uilder, FormGroup, Validators } from '@angular/forms';

interface DocumentTemplate {
  id: string;
  title: string;
  description: string;
  icon: string;
  fields: DocumentField[];
  category: string;
}

interface DocumentField {
  id: string;
  label: string;
  type: 'text' | 'textarea' | 'date' | 'select' | 'number';
  required: boolean;
  placeholder?: string;
  options?: string[];
}

interface DocumentType {
  id: string;
  name: string;
  icon: string;
  description: string;
}

@Component({
  selector: 'app-generate-document',
  templateUrl: './generate-document.page.html',
  styleUrls: ['./generate-document.page.scss'],
  standalone: false,
})
export class GenerateDocumentPage implements OnInit {
  templateId: string = '';
  currentTemplate: DocumentTemplate | null = null;
  documentForm: FormGroup;
  selectedDocumentType: string = '';
  
  documentTypes: DocumentType[] = [
    {
      id: 'contract',
      name: 'Contract',
      icon: 'document-text',
      description: 'Legal agreements and contracts'
    },
    {
      id: 'affidavit',
      name: 'Affidavit',
      icon: 'clipboard',
      description: 'Sworn written statements'
    },
    {
      id: 'power-of-attorney',
      name: 'Power of Attorney',
      icon: 'person-add',
      description: 'Legal authorization documents'
    },
    {
      id: 'will',
      name: 'Last Will',
      icon: 'library',
      description: 'Testament and estate planning'
    }
  ];

  templates: { [key: string]: DocumentTemplate } = {
    '1': {
      id: '1',
      title: 'Legal Document Template',
      description: 'Generate legal documents quickly',
      icon: 'document-text',
      category: 'General',
      fields: [
        {
          id: 'client_name',
          label: 'Client Name',
          type: 'text',
          required: true,
          placeholder: 'Enter full name'
        },
        {
          id: 'document_type',
          label: 'Document Type',
          type: 'select',
          required: true,
          options: ['Contract', 'Agreement', 'Affidavit', 'Power of Attorney']
        },
        {
          id: 'description',
          label: 'Description',
          type: 'textarea',
          required: true,
          placeholder: 'Describe the purpose of this document'
        }
      ]
    },
    '2': {
      id: '2',
      title: 'Contract Template',
      description: 'Create contracts and agreements',
      icon: 'contract',
      category: 'Contracts',
      fields: [
        {
          id: 'party1_name',
          label: 'First Party Name',
          type: 'text',
          required: true,
          placeholder: 'Enter first party name'
        },
        {
          id: 'party2_name',
          label: 'Second Party Name',
          type: 'text',
          required: true,
          placeholder: 'Enter second party name'
        },
        {
          id: 'contract_value',
          label: 'Contract Value',
          type: 'number',
          required: false,
          placeholder: 'Enter amount'
        },
        {
          id: 'start_date',
          label: 'Start Date',
          type: 'date',
          required: true
        },
        {
          id: 'end_date',
          label: 'End Date',
          type: 'date',
          required: false
        },
        {
          id: 'terms',
          label: 'Terms and Conditions',
          type: 'textarea',
          required: true,
          placeholder: 'Enter contract terms'
        }
      ]
    },
    '3': {
      id: '3',
      title: 'Affidavit Template',
      description: 'Generate sworn statements',
      icon: 'clipboard',
      category: 'Legal Statements',
      fields: [
        {
          id: 'affiant_name',
          label: 'Affiant Name',
          type: 'text',
          required: true,
          placeholder: 'Enter affiant full name'
        },
        {
          id: 'affiant_address',
          label: 'Affiant Address',
          type: 'textarea',
          required: true,
          placeholder: 'Enter complete address'
        },
        {
          id: 'statement',
          label: 'Statement',
          type: 'textarea',
          required: true,
          placeholder: 'Enter the sworn statement'
        },
        {
          id: 'date_of_oath',
          label: 'Date of Oath',
          type: 'date',
          required: true
        }
      ]
    }
  };

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private formBuilder: FormBuilder
  ) {
    this.documentForm = this.formBuilder.group({});
  }

  ngOnInit() {
    this.templateId = this.route.snapshot.paramMap.get('id') || '1';
    this.loadTemplate();
  }

  loadTemplate() {
    this.currentTemplate = this.templates[this.templateId];
    if (this.currentTemplate) {
      this.buildForm();
    }
  }

  buildForm() {
    if (!this.currentTemplate) return;

    const formControls: { [key: string]: any } = {};
    
    this.currentTemplate.fields.forEach(field => {
      const validators = field.required ? [Validators.required] : [];
      formControls[field.id] = ['', validators];
    });

    this.documentForm = this.formBuilder.group(formControls);
  }

  selectDocumentType(type: DocumentType) {
    this.selectedDocumentType = type.id;
  }

  onGenerateDocument() {
    if (this.documentForm.valid) {
      const formData = this.documentForm.value;
      console.log('Generating document with data:', formData);
      
      // TODO: Implement actual document generation
      // This would typically involve:
      // 1. Send data to document generation service
      // 2. Generate PDF from template
      // 3. Save to encrypted storage
      // 4. Provide download link
      
      // For now, show success message and navigate back
      this.router.navigate(['/client-tabs/documents']);
    } else {
      console.log('Form is invalid');
      this.markFormGroupTouched();
    }
  }

  private markFormGroupTouched() {
    Object.keys(this.documentForm.controls).forEach(key => {
      const control = this.documentForm.get(key);
      control?.markAsTouched();
    });
  }

  onBack() {
    this.router.navigate(['/client-tabs/home']);
  }

  downloadTemplate() {
    // TODO: Implement template download
    console.log('Downloading template...');
  }
}
