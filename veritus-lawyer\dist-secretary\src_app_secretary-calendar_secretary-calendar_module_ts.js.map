{"version": 3, "file": "src_app_secretary-calendar_secretary-calendar_module_ts.js", "mappings": ";;;;;;;;;;;;;;;;AACuD;AAEW;;;AAElE,MAAME,MAAM,GAAW,CACrB;EACEC,IAAI,EAAE,EAAE;EACRC,SAAS,EAAEH,2EAAqBA;CACjC,CACF;AAMK,MAAOI,kCAAkC;;;uBAAlCA,kCAAkC;IAAA;EAAA;;;YAAlCA;IAAkC;EAAA;;;gBAHnCL,yDAAY,CAACM,QAAQ,CAACJ,MAAM,CAAC,EAC7BF,yDAAY;IAAA;EAAA;;;sHAEXK,kCAAkC;IAAAE,OAAA,GAAAC,yDAAA;IAAAC,OAAA,GAFnCT,yDAAY;EAAA;AAAA,K;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACbuB;AACmB;AACrB;AAE7C;AAC2D;AACJ;AACA;AACI;AACO;AACT;AACU;AACN;AACF;AACmB;AACrB;AACF;AAEkC;AACvB;;AAElE;AA4BM,MAAO0B,2BAA2B;;;uBAA3BA,2BAA2B;IAAA;EAAA;;;YAA3BA;IAA2B;EAAA;;;gBAxBpChB,yDAAY,EACZC,uDAAW,EACXC,+DAAmB,EACnBC,uDAAW,EACXR,kGAAkC;MAElC;MACAS,qEAAe,EACfC,iEAAa,EACbC,iEAAa,EACbC,qEAAe,EACfC,6EAAkB,EAClBC,oEAAc,EACdC,8EAAmB,EACnBC,wEAAmB,EACnBC,sEAAe,EACfC,yFAAwB,EACxBC,oEAAc,EACdC,kEAAa;MAEb;MACAxB,2EAAqB;IAAA;EAAA;;;sHAGZyB,2BAA2B;IAAAnB,OAAA,GAxBpCG,yDAAY,EACZC,uDAAW,EACXC,+DAAmB,EACnBC,uDAAW,EACXR,kGAAkC;IAElC;IACAS,qEAAe,EACfC,iEAAa,EACbC,iEAAa,EACbC,qEAAe,EACfC,6EAAkB,EAClBC,oEAAc,EACdC,8EAAmB,EACnBC,wEAAmB,EACnBC,sEAAe,EACfC,yFAAwB,EACxBC,oEAAc,EACdC,kEAAa;IAEb;IACAxB,2EAAqB;EAAA;AAAA,K;;;;;;;;;;;;;;;;;;;;;;AC9CsB;AACF;AACA;AACE;;;;;;;;;ICkCzC0B,4DADF,cAA4D,aACX;IAAAA,oDAAA,yCAAwB;IAAAA,0DAAA,EAAK;IAItEA,4DAHN,cAAiC,eACrB,sBACS,qBACC;IAAAA,oDAAA,oBAAa;IAAAA,0DAAA,EAAiB;IAC9CA,4DAAA,wBAAmB;IAAAA,oDAAA,uCAAgC;IACrDA,0DADqD,EAAoB,EACvD;IAEhBA,4DADF,wBAAkB,SACb;IAAAA,oDAAA,sDAA8C;IAAAA,0DAAA,EAAI;IACrDA,4DAAA,SAAG;IAAAA,oDAAA,IAA0C;IAAAA,0DAAA,EAAI;IACjDA,4DAAA,sBAAkD;IAChDA,uDAAA,oBAA0D;IAC1DA,oDAAA,wBACF;IAIRA,0DAJQ,EAAa,EACI,EACV,EACP,EACF;;;;IARKA,uDAAA,IAA0C;IAA1CA,gEAAA,qBAAAO,MAAA,CAAAC,aAAA,CAAAC,MAAA,KAA0C;;;;;IAYnDT,4DADF,cAAgE,aACf;IAAAA,oDAAA,0CAAyB;IAAAA,0DAAA,EAAK;IAIvEA,4DAHN,cAAiC,eACrB,sBACS,qBACC;IAAAA,oDAAA,6BAAsB;IAAAA,0DAAA,EAAiB;IACvDA,4DAAA,wBAAmB;IAAAA,oDAAA,6CAAsC;IAC3DA,0DAD2D,EAAoB,EAC7D;IAEhBA,4DADF,wBAAkB,SACb;IAAAA,oDAAA,oEAA4D;IAAAA,0DAAA,EAAI;IACnEA,4DAAA,SAAG;IAAAA,oDAAA,IAA6C;IAAAA,0DAAA,EAAI;IACpDA,4DAAA,sBAAkD;IAChDA,uDAAA,oBAA0D;IAC1DA,oDAAA,6BACF;IAIRA,0DAJQ,EAAa,EACI,EACV,EACP,EACF;;;;IARKA,uDAAA,IAA6C;IAA7CA,gEAAA,yBAAAO,MAAA,CAAAG,YAAA,CAAAD,MAAA,KAA6C;;;;;;IAYtDT,4DADF,cAAgE,aACf;IAAAA,oDAAA,iCAAqB;IAAAA,0DAAA,EAAK;IAInEA,4DAHN,cAAiC,eACrB,sBACS,qBACC;IAAAA,oDAAA,0BAAmB;IAAAA,0DAAA,EAAiB;IACpDA,4DAAA,wBAAmB;IAAAA,oDAAA,+CAAwC;IAC7DA,0DAD6D,EAAoB,EAC/D;IAEhBA,4DADF,wBAAkB,SACb;IAAAA,oDAAA,qEAA6D;IAAAA,0DAAA,EAAI;IACpEA,4DAAA,SAAG;IAAAA,oDAAA,IAAiC;IAAAA,0DAAA,EAAI;IACxCA,4DAAA,sBAAmD;IAAxBA,wDAAA,mBAAAY,mEAAA;MAAAZ,2DAAA,CAAAc,GAAA;MAAA,MAAAP,MAAA,GAAAP,2DAAA;MAAA,OAAAA,yDAAA,CAASO,MAAA,CAAAU,WAAA,EAAa;IAAA,EAAC;IAChDjB,uDAAA,oBAAyD;IACzDA,oDAAA,sBACF;IAIRA,0DAJQ,EAAa,EACI,EACV,EACP,EACF;;;;IARKA,uDAAA,IAAiC;IAAjCA,gEAAA,oBAAAO,MAAA,CAAAW,YAAA,KAAiC;;;ADpE1C,MAAO5C,qBAAqB;EAYhC6C,YACUC,eAAgC,EAChCC,eAAgC;IADhC,KAAAD,eAAe,GAAfA,eAAe;IACf,KAAAC,eAAe,GAAfA,eAAe;IAbzB,KAAAH,YAAY,GAAW,IAAII,IAAI,EAAE,CAACC,WAAW,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAC7D,KAAAhB,aAAa,GAAoB,EAAE;IACnC,KAAAiB,cAAc,GAAyB,IAAI;IAC3C,KAAAf,YAAY,GAA0B,EAAE;IAExC;IACA,KAAAgB,WAAW,GAAiD,UAAU;IAEtE;IACA,KAAAC,SAAS,GAAG,KAAK;EAKb;EAEJC,QAAQA,CAAA;IACNC,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;IACpD,IAAI,CAACC,sBAAsB,EAAE;IAC7B,IAAI,CAACC,iBAAiB,EAAE;IACxB,IAAI,CAACC,gBAAgB,EAAE;EACzB;EAEMF,sBAAsBA,CAAA;IAAA,IAAAG,KAAA;IAAA,OAAAC,6KAAA;MAC1BN,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC;MAC7C,MAAMM,WAAW,GAAGF,KAAI,CAACd,eAAe,CAACiB,cAAc,EAAE;MACzDR,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEM,WAAW,CAAC;MAEzC,IAAIA,WAAW,EAAE;QACf,IAAI;UACF,MAAME,OAAO,SAASJ,KAAI,CAACd,eAAe,CAACmB,mBAAmB,CAACH,WAAW,CAACI,GAAG,CAAC;UAC/EX,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEQ,OAAO,CAAC;SAC3C,CAAC,OAAOG,KAAK,EAAE;UACdZ,OAAO,CAACY,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;;;IAE3D;EACH;EAEMT,iBAAiBA,CAAA;IAAA,IAAAU,MAAA;IAAA,OAAAP,6KAAA;MACrBN,OAAO,CAACC,GAAG,CAAC,2BAA2B,CAAC;MACxC,MAAMM,WAAW,GAAGM,MAAI,CAACtB,eAAe,CAACiB,cAAc,EAAE;MACzDR,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAEM,WAAW,CAAC;MAE5D,IAAIA,WAAW,EAAE;QACf,IAAI;UACFM,MAAI,CAAClC,aAAa,SAASkC,MAAI,CAACtB,eAAe,CAACuB,yBAAyB,CAACP,WAAW,CAACI,GAAG,CAAC;UAC1FX,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEY,MAAI,CAAClC,aAAa,CAAC;SAC1D,CAAC,OAAOiC,KAAK,EAAE;UACdZ,OAAO,CAACY,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;UACrD;UACAC,MAAI,CAAClC,aAAa,GAAG,CACnB;YACEgC,GAAG,EAAE,SAAS;YACdI,KAAK,EAAE,qBAAqB;YAC5BC,IAAI,EAAE,YAAY;YAClBC,UAAU,EAAE,WAAW;YACvBC,KAAK,EAAE,WAAW;YAClBC,KAAK,EAAE,aAAa;YACpBC,IAAI,EAAE,QAAQ;YACdC,SAAS,EAAE,IAAI5B,IAAI,EAAE;YACrB6B,SAAS,EAAE,IAAI7B,IAAI;WACpB,EACD;YACEkB,GAAG,EAAE,SAAS;YACdI,KAAK,EAAE,qBAAqB;YAC5BC,IAAI,EAAE,UAAU;YAChBC,UAAU,EAAE,WAAW;YACvBC,KAAK,EAAE,WAAW;YAClBC,KAAK,EAAE,aAAa;YACpBC,IAAI,EAAE,QAAQ;YACdC,SAAS,EAAE,IAAI5B,IAAI,EAAE;YACrB6B,SAAS,EAAE,IAAI7B,IAAI;WACpB,CACF;UACDO,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEY,MAAI,CAAClC,aAAa,CAAC;;;IAEhE;EACH;EAEMyB,gBAAgBA,CAAA;IAAA,IAAAmB,MAAA;IAAA,OAAAjB,6KAAA;MACpBN,OAAO,CAACC,GAAG,CAAC,yBAAyB,CAAC;MACtC,MAAMM,WAAW,GAAGgB,MAAI,CAAChC,eAAe,CAACiB,cAAc,EAAE;MACzD,IAAI,CAACD,WAAW,EAAE;QAChBP,OAAO,CAACC,GAAG,CAAC,uBAAuB,CAAC;QACpC;;MAGFsB,MAAI,CAACzB,SAAS,GAAG,IAAI;MACrB,IAAI;QACFyB,MAAI,CAAC1C,YAAY,SAAS0C,MAAI,CAAChC,eAAe,CAACiC,2BAA2B,CAACjB,WAAW,CAACI,GAAG,CAAC;QAC3FX,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEsB,MAAI,CAAC1C,YAAY,CAAC;OACvD,CAAC,OAAO+B,KAAK,EAAE;QACdZ,OAAO,CAACY,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;QACnD;QACAW,MAAI,CAAC1C,YAAY,GAAG,CAClB;UACE4C,EAAE,EAAE,MAAM;UACVC,QAAQ,EAAE,SAAS;UACnBC,UAAU,EAAE,YAAY;UACxBC,UAAU,EAAE,eAAe;UAC3BC,IAAI,EAAE,IAAIpC,IAAI,EAAE,CAACC,WAAW,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;UAC5CmC,IAAI,EAAE,OAAO;UACbC,MAAM,EAAE,SAAS;UACjBC,IAAI,EAAE,cAAc;UACpBC,SAAS,EAAE,QAAQ;UACnBZ,SAAS,EAAE,IAAI5B,IAAI,EAAE;UACrB6B,SAAS,EAAE,IAAI7B,IAAI;SACpB,EACD;UACEgC,EAAE,EAAE,MAAM;UACVC,QAAQ,EAAE,SAAS;UACnBC,UAAU,EAAE,UAAU;UACtBC,UAAU,EAAE,YAAY;UACxBC,IAAI,EAAE,IAAIpC,IAAI,EAAE,CAACC,WAAW,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;UAC5CmC,IAAI,EAAE,OAAO;UACbC,MAAM,EAAE,WAAW;UACnBC,IAAI,EAAE,aAAa;UACnBC,SAAS,EAAE,QAAQ;UACnBZ,SAAS,EAAE,IAAI5B,IAAI,EAAE;UACrB6B,SAAS,EAAE,IAAI7B,IAAI;SACpB,CACF;QACDO,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEsB,MAAI,CAAC1C,YAAY,CAAC;QAC1D0C,MAAI,CAACW,SAAS,CAAC,6BAA6B,EAAE,SAAS,CAAC;OACzD,SAAS;QACRX,MAAI,CAACzB,SAAS,GAAG,KAAK;;IACvB;EACH;EAEAqC,gBAAgBA,CAACC,MAAqB;IACpC,IAAI,CAACxC,cAAc,GAAGwC,MAAM;EAC9B;EAEAC,cAAcA,CAACR,IAAY;IACzB,IAAI,CAACxC,YAAY,GAAGwC,IAAI;EAC1B;EAEAS,qBAAqBA,CAAA;IACnB;IACA,IAAI,CAAClC,gBAAgB,EAAE;EACzB;EAEAhB,WAAWA,CAAA;IACTY,OAAO,CAACC,GAAG,CAAC,oBAAoB,CAAC;IACjC,IAAI,CAACE,iBAAiB,EAAE;IACxB,IAAI,CAACC,gBAAgB,EAAE;EACzB;EAEAmC,WAAWA,CAACC,KAAU;IACpBxC,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEuC,KAAK,CAAC;IACvC,MAAMC,GAAG,GAAGD,KAAK,CAACE,MAAM,CAACC,KAAK;IAC9B3C,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEwC,GAAG,CAAC;IACjC,IAAIA,GAAG,KAAK,UAAU,IAAIA,GAAG,KAAK,cAAc,IAAIA,GAAG,KAAK,cAAc,EAAE;MAC1E,IAAI,CAAC5C,WAAW,GAAG4C,GAAG;MACtBzC,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAE,IAAI,CAACJ,WAAW,CAAC;;EAEpD;EAEA+C,cAAcA,CAACH,GAAiD;IAC9D,IAAI,CAAC5C,WAAW,GAAG4C,GAAG;EACxB;EAEMP,SAASA,CAACW,OAAe,EAAEC,KAAa;IAAA,IAAAC,MAAA;IAAA,OAAAzC,6KAAA;MAC5C,MAAM0C,KAAK,SAASD,MAAI,CAACvD,eAAe,CAACyD,MAAM,CAAC;QAC9CJ,OAAO;QACPK,QAAQ,EAAE,IAAI;QACdJ,KAAK;QACLK,QAAQ,EAAE;OACX,CAAC;MACF,MAAMH,KAAK,CAACI,OAAO,EAAE;IAAC;EACxB;EAEAC,iBAAiBA,CAAA;IACf,OAAO,IAAI,CAACxE,YAAY,CAACyE,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACxB,MAAM,KAAK,WAAW,CAAC,CAACnD,MAAM;EACvE;EAEA4E,oBAAoBA,CAAA;IAClB,OAAO,IAAI,CAAC3E,YAAY,CAACD,MAAM;EACjC;EAEA6E,eAAeA,CAAA;IACb,OAAO,IAAI,CAAC5E,YAAY,CAACyE,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACxB,MAAM,KAAK,SAAS,CAAC,CAACnD,MAAM;EACrE;EAEA8E,gBAAgBA,CAAA;IACd,MAAMC,GAAG,GAAG,IAAIlE,IAAI,EAAE;IACtB,OAAO,IAAI,CAACZ,YAAY,CAACyE,MAAM,CAACM,GAAG,IAAG;MACpC,MAAMC,mBAAmB,GAAG,IAAIpE,IAAI,CAACmE,GAAG,CAAC/B,IAAI,GAAG,GAAG,GAAG+B,GAAG,CAAC9B,IAAI,CAAC;MAC/D,OAAO+B,mBAAmB,GAAGF,GAAG;IAClC,CAAC,CAAC,CAAC/E,MAAM;EACX;EAEAkF,cAAcA,CAAC/B,MAAc;IAC3B,QAAQA,MAAM;MACZ,KAAK,WAAW;QAAE,OAAO,SAAS;MAClC,KAAK,WAAW;QAAE,OAAO,QAAQ;MACjC,KAAK,WAAW;QAAE,OAAO,QAAQ;MACjC;QAAS,OAAO,SAAS;;EAE7B;;;uBAxMWtF,qBAAqB,EAAA0B,+DAAA,CAAAnB,uEAAA,GAAAmB,+DAAA,CAAA8F,2DAAA;IAAA;EAAA;;;YAArBxH,qBAAqB;MAAA0H,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAAlG,iEAAA;MAAAoG,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,+BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCnB9BzG,4DAFJ,iBAAY,qBAC2B,mBACyB;UAAAA,oDAAA,4BAAqB;UAErFA,0DAFqF,EAAY,EACjF,EACH;UAOPA,4DALN,qBAA0D,aACF,aAGxB,qBAC+C;UAA5DA,8DAAA,2BAAA4G,oEAAAC,MAAA;YAAA7G,gEAAA,CAAA0G,GAAA,CAAAhF,WAAA,EAAAmF,MAAA,MAAAH,GAAA,CAAAhF,WAAA,GAAAmF,MAAA;YAAA,OAAAA,MAAA;UAAA,EAAyB;UAAC7G,wDAAA,uBAAA+G,gEAAAF,MAAA;YAAA,OAAaH,GAAA,CAAAtC,WAAA,CAAAyC,MAAA,CAAmB;UAAA,EAAC;UACtE7G,4DAAA,4BAAqC;UACnCA,uDAAA,kBAA6C;UAC7CA,4DAAA,iBAAW;UAAAA,oDAAA,gBAAQ;UACrBA,0DADqB,EAAY,EACZ;UACrBA,4DAAA,6BAAyC;UACvCA,uDAAA,mBAAyC;UACzCA,4DAAA,iBAAW;UAAAA,oDAAA,oBAAY;UACzBA,0DADyB,EAAY,EAChB;UACrBA,4DAAA,8BAAyC;UACvCA,uDAAA,oBAAyC;UACzCA,4DAAA,iBAAW;UAAAA,oDAAA,oBAAY;UAG7BA,0DAH6B,EAAY,EAChB,EACT,EACV;UAIJA,4DADF,eAA2H,aAClE;UACrDA,oDAAA,sBAAa;UAAAA,4DAAA,cAAQ;UAAAA,oDAAA,IAAiB;UAAAA,0DAAA,EAAS;UAACA,oDAAA,2BAChC;UAAAA,4DAAA,cAAQ;UAAAA,oDAAA,IAA0B;UAAAA,0DAAA,EAAS;UAACA,oDAAA,yBAC9C;UAAAA,4DAAA,cAAQ;UAAAA,oDAAA,IAAyB;UAEnDA,0DAFmD,EAAS,EACtD,EACA;UA6CNA,wDA1CA,KAAAiH,qCAAA,mBAA4D,KAAAC,qCAAA,mBAqBI,KAAAC,qCAAA,mBAqBA;UAsB5DnH,4DAFJ,eAA2B,eACF,eACyD;UAC5EA,oDAAA,IACF;UAAAA,0DAAA,EAAM;UACNA,4DAAA,eAA0D;UAAAA,oDAAA,0BAAkB;UAC9EA,0DAD8E,EAAM,EAC9E;UAGJA,4DADF,eAAuB,eACyD;UAC5EA,oDAAA,IACF;UAAAA,0DAAA,EAAM;UACNA,4DAAA,eAA0D;UAAAA,oDAAA,sBAAc;UAC1EA,0DAD0E,EAAM,EAC1E;UAGJA,4DADF,eAAuB,eACyD;UAC5EA,oDAAA,IACF;UAAAA,0DAAA,EAAM;UACNA,4DAAA,eAA0D;UAAAA,oDAAA,iBAAS;UACrEA,0DADqE,EAAM,EACrE;UAGJA,4DADF,eAAuB,eACyD;UAC5EA,oDAAA,IACF;UAAAA,0DAAA,EAAM;UACNA,4DAAA,eAA0D;UAAAA,oDAAA,eAAO;UAKzEA,0DALyE,EAAM,EACnE,EACF,EAEF,EACM;;;UAvHKA,uDAAA,GAAyB;UAAzBA,8DAAA,YAAA0G,GAAA,CAAAhF,WAAA,CAAyB;UAmBf1B,uDAAA,IAAiB;UAAjBA,+DAAA,CAAA0G,GAAA,CAAAhF,WAAA,CAAiB;UACd1B,uDAAA,GAA0B;UAA1BA,+DAAA,CAAA0G,GAAA,CAAAlG,aAAA,CAAAC,MAAA,CAA0B;UAC5BT,uDAAA,GAAyB;UAAzBA,+DAAA,CAAA0G,GAAA,CAAAhG,YAAA,CAAAD,MAAA,CAAyB;UAK7CT,uDAAA,EAAgC;UAAhCA,wDAAA,SAAA0G,GAAA,CAAAhF,WAAA,gBAAgC;UAqBhC1B,uDAAA,EAAoC;UAApCA,wDAAA,SAAA0G,GAAA,CAAAhF,WAAA,oBAAoC;UAqBpC1B,uDAAA,EAAoC;UAApCA,wDAAA,SAAA0G,GAAA,CAAAhF,WAAA,oBAAoC;UAuBpC1B,uDAAA,GACF;UADEA,gEAAA,MAAA0G,GAAA,CAAArB,oBAAA,QACF;UAMErF,uDAAA,GACF;UADEA,gEAAA,MAAA0G,GAAA,CAAAlG,aAAA,CAAAC,MAAA,MACF;UAMET,uDAAA,GACF;UADEA,gEAAA,MAAA0G,GAAA,CAAAxB,iBAAA,QACF;UAMElF,uDAAA,GACF;UADEA,gEAAA,MAAA0G,GAAA,CAAApB,eAAA,QACF;;;qBD7GJvG,yDAAY,EAAAwI,iDAAA,EACZvI,uDAAW,EAAAyI,2DAAA,EAAAA,mDAAA,EACXvI,uDAAW,EAAA4G,qDAAA,EAAAA,mDAAA,EAAAA,0DAAA,EAAAA,yDAAA,EAAAA,2DAAA,EAAAA,wDAAA,EAAAA,sDAAA,EAAAA,qDAAA,EAAAA,mDAAA,EAAAA,oDAAA,EAAAA,sDAAA,EAAAA,4DAAA,EAAAA,oDAAA,EAAAA,sDAAA,EAAAA,+DAAA,EAAAA,8DAAA,EACXzH,yDAAY,EAAAuK,uDAAA;MAAAE,MAAA;IAAA;EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AElBoB;AAC6S;AAClP;AACd;AAC6B;AACjE;AACU;AACX;AACsC;AAC1C;AACA;AAC6B;AACA;AACsC;AAChD;AAChB;AACsC;;AAEjF;AACA;AACA;AACA;AACA;AAJA,MAAA4D,GAAA;AAAA,SAAAC,8BAAAlG,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAWoGzG,0DAAE,EAwHqiC,CAAC;EAAA;AAAA;AAAA,MAAA6M,GAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA,GAAAC,EAAA;EAAAC,iBAAA,EAAAD;AAAA;AAAA,MAAAE,GAAA,GAAAA,CAAAF,EAAA,EAAAG,EAAA;EAAA9I,KAAA,EAAA2I,EAAA;EAAAI,MAAA,EAAAD;AAAA;AAAA,SAAAE,kCAAA/G,EAAA,EAAAC,GAAA;AAAA,MAAA+G,GAAA;AAAA,MAAAC,GAAA;AAAA,SAAAC,uDAAAlH,EAAA,EAAAC,GAAA;AAAA,SAAAkH,yCAAAnH,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAxHxiCzG,wDAAE,IAAA2N,sDAAA,yBAq8C+xE,CAAC;EAAA;EAAA,IAAAlH,EAAA;IAAA,MAAAoH,MAAA,GAr8ClyE7N,2DAAE,GAAA8N,SAAA;IAAF9N,wDAAE,oBAAA6N,MAAA,CAAAE,aAq8C8xE,CAAC;EAAA;AAAA;AAAA,SAAAC,yCAAAvH,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAr8CjyEzG,oDAAE,EAq8Cm1E,CAAC;EAAA;EAAA,IAAAyG,EAAA;IAAA,MAAAoH,MAAA,GAr8Ct1E7N,2DAAE,GAAA8N,SAAA;IAAF9N,+DAAE,CAAA6N,MAAA,CAAAI,SAq8Cm1E,CAAC;EAAA;AAAA;AAAA,SAAAC,2BAAAzH,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAA3F,GAAA,GAr8Ct1Ed,8DAAE;IAAFA,4DAAE,eAq8Cg+C,CAAC;IAr8Cn+CA,wDAAE,mBAAAoO,gDAAA;MAAA,MAAAC,MAAA,GAAFrO,2DAAE,CAAAc,GAAA;MAAA,MAAA+M,MAAA,GAAAQ,MAAA,CAAAP,SAAA;MAAA,MAAAQ,IAAA,GAAAD,MAAA,CAAAE,MAAA;MAAA,MAAAC,MAAA,GAAFxO,2DAAE;MAAA,MAAAyO,YAAA,GAAFzO,yDAAE;MAAA,OAAFA,yDAAE,CAq8Cq4CwO,MAAA,CAAAG,YAAA,CAAAd,MAAA,EAAAY,YAAA,EAAAH,IAA8B,CAAC;IAAA,CAAC,CAAC,4BAAAM,yDAAA/H,MAAA;MAAA,MAAAyH,IAAA,GAr8Cx6CtO,2DAAE,CAAAc,GAAA,EAAAyN,MAAA;MAAA,MAAAC,MAAA,GAAFxO,2DAAE;MAAA,OAAFA,yDAAE,CAq8Cm8CwO,MAAA,CAAAK,gBAAA,CAAAhI,MAAA,EAAAyH,IAA0B,CAAC;IAAA,CAAC,CAAC;IAr8Cl+CtO,uDAAE,aAq8C+gD,CAAC,YAAmT,CAAC;IAr8Ct0DA,4DAAE,cAq8C82D,CAAC,cAA6C,CAAC;IAr8C/5DA,wDAAE,IAAA4N,wCAAA,gBAq8C4tE,CAAC,IAAAI,wCAAA,MAAqG,CAAC;IAr8Cr0EhO,0DAAE,CAq8Cq2E,CAAC,CAAc,CAAC,CAAW,CAAC;EAAA;EAAA,IAAAyG,EAAA;IAAA,MAAAoH,MAAA,GAAAnH,GAAA,CAAAoH,SAAA;IAAA,MAAAQ,IAAA,GAAA5H,GAAA,CAAA6H,MAAA;IAAA,MAAAO,UAAA,GAr8Cn4E9O,yDAAE;IAAA,MAAAwO,MAAA,GAAFxO,2DAAE;IAAFA,wDAAE,CAAA6N,MAAA,CAAAmB,UAq8CsxC,CAAC;IAr8CzxChP,yDAAE,oBAAAwO,MAAA,CAAAU,aAAA,KAAAZ,IAq8CkvC,CAAC;IAr8CrvCtO,wDAAE,OAAAwO,MAAA,CAAAW,cAAA,CAAAb,IAAA,CAq8C8yB,CAAC,aAAAT,MAAA,CAAAuB,QAA4gB,CAAC,uBAAAZ,MAAA,CAAAa,kBAAoD,CAAC;IAr8Cn3CrP,yDAAE,aAAAwO,MAAA,CAAAe,YAAA,CAAAjB,IAAA,oBAAAA,IAAA,sBAAAE,MAAA,CAAAgB,KAAA,CAAA/O,MAAA,mBAAA+N,MAAA,CAAAiB,gBAAA,CAAAnB,IAAA,oBAAAE,MAAA,CAAAU,aAAA,KAAAZ,IAAA,gBAAAT,MAAA,CAAA6B,SAAA,8BAAA7B,MAAA,CAAA6B,SAAA,IAAA7B,MAAA,CAAA8B,cAAA,GAAA9B,MAAA,CAAA8B,cAAA;IAAF3P,uDAAE,EAq8C6vD,CAAC;IAr8ChwDA,wDAAE,qBAAA8O,UAq8C6vD,CAAC,sBAAAjB,MAAA,CAAAuB,QAAA,IAAAZ,MAAA,CAAAoB,aAA8D,CAAC;IAr8C/zD5P,uDAAE,EAq8Co1E,CAAC;IAr8Cv1EA,2DAAE,IAAA6N,MAAA,CAAAE,aAAA,QAq8Co1E,CAAC;EAAA;AAAA;AAAA,SAAA+B,mCAAArJ,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAr8Cv1EzG,0DAAE,EAq8C8uF,CAAC;EAAA;AAAA;AAAA,SAAA+P,2BAAAtJ,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAuJ,GAAA,GAr8CjvFhQ,8DAAE;IAAFA,4DAAE,sBAq8C2uH,CAAC;IAr8C9uHA,wDAAE,yBAAAiQ,+DAAA;MAAFjQ,2DAAE,CAAAgQ,GAAA;MAAA,MAAAxB,MAAA,GAAFxO,2DAAE;MAAA,OAAFA,yDAAE,CAq8CsoHwO,MAAA,CAAA0B,2BAAA,CAA4B,CAAC;IAAA,CAAC,CAAC,0BAAAC,gEAAAtJ,MAAA;MAr8CvqH7G,2DAAE,CAAAgQ,GAAA;MAAA,MAAAxB,MAAA,GAAFxO,2DAAE;MAAA,OAAFA,yDAAE,CAq8CysHwO,MAAA,CAAA4B,wBAAA,CAAAvJ,MAA+B,CAAC;IAAA,CAAC,CAAC;IAr8C7uH7G,0DAAE,CAq8CgwH,CAAC;EAAA;EAAA,IAAAyG,EAAA;IAAA,MAAA4J,OAAA,GAAA3J,GAAA,CAAAoH,SAAA;IAAA,MAAAwC,KAAA,GAAA5J,GAAA,CAAA6H,MAAA;IAAA,MAAAC,MAAA,GAr8CnwHxO,2DAAE;IAAFA,wDAAE,CAAAqQ,OAAA,CAAAE,SAq8Cw2G,CAAC;IAr8C32GvQ,yDAAE,4BAAAwO,MAAA,CAAAU,aAAA,KAAAoB,KAq8C4zG,CAAC;IAr8C/zGtQ,wDAAE,OAAAwO,MAAA,CAAAiB,gBAAA,CAAAa,KAAA,CAq8CsgG,CAAC,YAAAD,OAAA,CAAAG,OAA8Y,CAAC,aAAAH,OAAA,CAAArL,QAA8C,CAAC,WAAAqL,OAAA,CAAAI,MAAyC,CAAC,sBAAAjC,MAAA,CAAApB,iBAA2D,CAAC,oBAAAoB,MAAA,CAAAkC,eAAuD,CAAC;IAr8CrmH1Q,yDAAE,aAAAwO,MAAA,CAAAmC,eAAA,YAAAnC,MAAA,CAAAU,aAAA,KAAAoB,KAAA,GAAA9B,MAAA,CAAAmC,eAAA,4BAAAnC,MAAA,CAAAW,cAAA,CAAAmB,KAAA,kBAAA9B,MAAA,CAAAU,aAAA,KAAAoB,KAAA;EAAA;AAAA;AAAA,MAAAM,IAAA;AAAA,MAAAC,IAAA;AANtG,MAAMC,eAAe,GAAG,IAAI/H,yDAAc,CAAC,eAAe,CAAC;AAC3D;AACA,MAAMgI,aAAa,CAAC;EAChB5P,WAAWA,CAAC,2BAA4BoF,QAAQ,EAAE;IAC9C,IAAI,CAACA,QAAQ,GAAGA,QAAQ;EAC5B;EACA;IAAS,IAAI,CAACyK,IAAI,YAAAC,sBAAAC,CAAA;MAAA,YAAAA,CAAA,IAAwFH,aAAa,EAAvB/Q,+DAAE,CAAuCA,sDAAc;IAAA,CAA4C;EAAE;EACrM;IAAS,IAAI,CAACmR,IAAI,kBAD8EnR,+DAAE;MAAA6D,IAAA,EACJkN,aAAa;MAAA/K,SAAA;MAAAC,UAAA;MAAAC,QAAA,GADXlG,gEAAE,CACuE,CAAC;QAAEsR,OAAO,EAAER,eAAe;QAAES,WAAW,EAAER;MAAc,CAAC,CAAC;IAAA,EAAiB;EAAE;AAC1P;AACA;EAAA,QAAAS,SAAA,oBAAAA,SAAA,KAHoGxR,+DAAE,CAGX+Q,aAAa,EAAc,CAAC;IAC3GlN,IAAI,EAAEmF,oDAAS;IACf0I,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,iBAAiB;MAC3BC,SAAS,EAAE,CAAC;QAAEN,OAAO,EAAER,eAAe;QAAES,WAAW,EAAER;MAAc,CAAC,CAAC;MACrE9K,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEpC,IAAI,EAAE7D,sDAAcoJ;EAAC,CAAC,CAAC;AAAA;;AAE5D;AACA;AACA;AACA;AACA;AACA,MAAMyI,aAAa,GAAG,IAAI9I,yDAAc,CAAC,aAAa,CAAC;AACvD;AACA;AACA;AACA;AACA,MAAM+I,OAAO,GAAG,IAAI/I,yDAAc,CAAC,SAAS,CAAC;AAC7C;AACA,MAAMgJ,WAAW,SAAStH,0DAAS,CAAC;EAChCtJ,WAAWA,CAAC6Q,WAAW,EAAEC,gBAAgB,EAAEC,WAAW,EAAE;IACpD,KAAK,CAACF,WAAW,EAAEC,gBAAgB,CAAC;IACpC,IAAI,CAACC,WAAW,GAAGA,WAAW;EAClC;EACA;IAAS,IAAI,CAAClB,IAAI,YAAAmB,oBAAAjB,CAAA;MAAA,YAAAA,CAAA,IAAwFa,WAAW,EA7BrB/R,+DAAE,CA6BqCA,sDAAc,GA7BrDA,+DAAE,CA6BgEA,2DAAmB,GA7BrFA,+DAAE,CA6BgG8R,OAAO;IAAA,CAA4D;EAAE;EACvQ;IAAS,IAAI,CAACX,IAAI,kBA9B8EnR,+DAAE;MAAA6D,IAAA,EA8BJkO,WAAW;MAAA/L,SAAA;MAAAC,UAAA;MAAAC,QAAA,GA9BTlG,gEAAE,CA8BoF,CAAC;QAAEsR,OAAO,EAAEO,aAAa;QAAEN,WAAW,EAAEQ;MAAY,CAAC,CAAC,GA9B5I/R,wEAAE;IAAA,EA8BkL;EAAE;AAC1R;AACA;EAAA,QAAAwR,SAAA,oBAAAA,SAAA,KAhCoGxR,+DAAE,CAgCX+R,WAAW,EAAc,CAAC;IACzGlO,IAAI,EAAEmF,oDAAS;IACf0I,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,gCAAgC;MAC1CC,SAAS,EAAE,CAAC;QAAEN,OAAO,EAAEO,aAAa;QAAEN,WAAW,EAAEQ;MAAY,CAAC,CAAC;MACjE9L,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEpC,IAAI,EAAE7D,sDAAcoJ;EAAC,CAAC,EAAE;IAAEvF,IAAI,EAAE7D,2DAAmBoS;EAAC,CAAC,EAAE;IAAEvO,IAAI,EAAEyO,SAAS;IAAEC,UAAU,EAAE,CAAC;MACxG1O,IAAI,EAAEoF,iDAAM;MACZyI,IAAI,EAAE,CAACI,OAAO;IAClB,CAAC,EAAE;MACCjO,IAAI,EAAEqF,mDAAQA;IAClB,CAAC;EAAE,CAAC,CAAC;AAAA;;AAErB;AACA;AACA;AACA;AACA,MAAMsJ,aAAa,GAAG,IAAIzJ,yDAAc,CAAC,eAAe,CAAC;AACzD,MAAM0J,MAAM,CAAC;EACT;EACA,IAAI1E,aAAaA,CAAA,EAAG;IAChB,OAAO,IAAI,CAAC2E,cAAc;EAC9B;EACA,IAAI3E,aAAaA,CAACvJ,KAAK,EAAE;IACrB,IAAI,CAACmO,sBAAsB,CAACnO,KAAK,CAAC;EACtC;EACA;EACA,IAAIgM,OAAOA,CAAA,EAAG;IACV,OAAO,IAAI,CAACoC,cAAc;EAC9B;EACAzR,WAAWA,CAAC0R,iBAAiB,EAAEC,gBAAgB,EAAE;IAC7C,IAAI,CAACD,iBAAiB,GAAGA,iBAAiB;IAC1C,IAAI,CAACC,gBAAgB,GAAGA,gBAAgB;IACxC;IACA,IAAI,CAAC1D,QAAQ,GAAG,KAAK;IACrB;AACR;AACA;IACQ,IAAI,CAAC2D,gBAAgB,GAAGT,SAAS;IACjC;IACA,IAAI,CAACrE,SAAS,GAAG,EAAE;IACnB;IACA,IAAI,CAAC2E,cAAc,GAAG,IAAI;IAC1B;IACA,IAAI,CAACI,aAAa,GAAG,IAAIpI,yCAAO,CAAC,CAAC;IAClC;AACR;AACA;AACA;IACQ,IAAI,CAAC5F,QAAQ,GAAG,IAAI;IACpB;AACR;AACA;AACA;IACQ,IAAI,CAACyL,MAAM,GAAG,IAAI;IAClB;AACR;AACA;IACQ,IAAI,CAACwC,QAAQ,GAAG,KAAK;EACzB;EACAC,WAAWA,CAACC,OAAO,EAAE;IACjB,IAAIA,OAAO,CAACC,cAAc,CAAC,WAAW,CAAC,IAAID,OAAO,CAACC,cAAc,CAAC,UAAU,CAAC,EAAE;MAC3E,IAAI,CAACJ,aAAa,CAACK,IAAI,CAAC,CAAC;IAC7B;EACJ;EACAC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACN,aAAa,CAACO,QAAQ,CAAC,CAAC;EACjC;EACA3R,QAAQA,CAAA,EAAG;IACP,IAAI,CAACgR,cAAc,GAAG,IAAIlI,+DAAc,CAAC,IAAI,CAACqI,gBAAgB,IAAI,IAAI,CAACS,gBAAgB,EAAE,IAAI,CAACX,iBAAiB,CAAC;EACpH;EACA;AACJ;AACA;AACA;AACA;AACA;EACIF,sBAAsBA,CAACnO,KAAK,EAAE;IAC1B;IACA;IACA;IACA;IACA,IAAIA,KAAK,IAAIA,KAAK,CAAC0N,WAAW,KAAK,IAAI,EAAE;MACrC,IAAI,CAACQ,cAAc,GAAGlO,KAAK;IAC/B;EACJ;EACA;IAAS,IAAI,CAACwM,IAAI,YAAAyC,eAAAvC,CAAA;MAAA,YAAAA,CAAA,IAAwFuB,MAAM,EAvHhBzS,+DAAE,CAuHgCA,2DAAmB,GAvHrDA,+DAAE,CAuHgEwS,aAAa;IAAA,CAA4D;EAAE;EAC7O;IAAS,IAAI,CAACkB,IAAI,kBAxH8E1T,+DAAE;MAAA6D,IAAA,EAwHJ4O,MAAM;MAAAzM,SAAA;MAAA4N,cAAA,WAAAC,sBAAApN,EAAA,EAAAC,GAAA,EAAAoN,QAAA;QAAA,IAAArN,EAAA;UAxHJzG,4DAAE,CAAA8T,QAAA,EAwHsc/B,WAAW;UAxHnd/R,4DAAE,CAAA8T,QAAA,EAwHoiB/C,aAAa,KAA2B3H,sDAAW;QAAA;QAAA,IAAA3C,EAAA;UAAA,IAAAuN,EAAA;UAxHzlBhU,4DAAE,CAAAgU,EAAA,GAAFhU,yDAAE,QAAA0G,GAAA,CAAAqH,aAAA,GAAAiG,EAAA,CAAAG,KAAA;UAAFnU,4DAAE,CAAAgU,EAAA,GAAFhU,yDAAE,QAAA0G,GAAA,CAAAqM,gBAAA,GAAAiB,EAAA,CAAAG,KAAA;QAAA;MAAA;MAAAC,SAAA,WAAAC,aAAA5N,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFzG,yDAAE,CAwHorBoJ,sDAAW;QAAA;QAAA,IAAA3C,EAAA;UAAA,IAAAuN,EAAA;UAxHjsBhU,4DAAE,CAAAgU,EAAA,GAAFhU,yDAAE,QAAA0G,GAAA,CAAA8M,gBAAA,GAAAQ,EAAA,CAAAG,KAAA;QAAA;MAAA;MAAAI,SAAA,aAwHmU,EAAE;MAAAC,MAAA;QAAApF,QAAA,GAxHvUpP,0DAAE,CAAA0U,0BAAA,0BAwH0FvL,2DAAgB;QAAA8E,SAAA,GAxH5GjO,0DAAE,CAAA2U,IAAA;QAAAjF,SAAA,GAAF1P,0DAAE,CAAA2U,IAAA;QAAAhF,cAAA,GAAF3P,0DAAE,CAAA2U,IAAA;QAAA3F,UAAA;QAAAuB,SAAA;MAAA;MAAAqE,QAAA;MAAA3O,UAAA;MAAAC,QAAA,GAAFlG,gEAAE,CAwHsV,CAAC;QAAEsR,OAAO,EAAEQ,OAAO;QAAEP,WAAW,EAAEkB;MAAO,CAAC,CAAC,GAxHnYzS,sEAAE,EAAFA,kEAAE,EAAFA,iEAAE;MAAA+U,kBAAA,EAAArI,GAAA;MAAAtG,KAAA;MAAAC,IAAA;MAAAE,QAAA,WAAAyO,gBAAAvO,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFzG,6DAAE;UAAFA,wDAAE,IAAA2M,6BAAA,qBAwH4gC,CAAC;QAAA;MAAA;MAAAuI,aAAA;IAAA,EAA4I;EAAE;AACjwC;AACA;EAAA,QAAA1D,SAAA,oBAAAA,SAAA,KA1HoGxR,+DAAE,CA0HXyS,MAAM,EAAc,CAAC;IACpG5O,IAAI,EAAEwF,oDAAS;IACfqI,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,SAAS;MAAEwD,eAAe,EAAE7L,kEAAuB,CAAC8L,OAAO;MAAEF,aAAa,EAAE3L,4DAAiB,CAACoL,IAAI;MAAEC,QAAQ,EAAE,QAAQ;MAAEhD,SAAS,EAAE,CAAC;QAAEN,OAAO,EAAEQ,OAAO;QAAEP,WAAW,EAAEkB;MAAO,CAAC,CAAC;MAAExM,UAAU,EAAE,IAAI;MAAEoP,IAAI,EAAE;QAC/M;QACA;QACA,QAAQ,EAAE;MACd,CAAC;MAAE9O,QAAQ,EAAE;IAAgR,CAAC;EAC1S,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAE1C,IAAI,EAAE7D,2DAAmBoS;EAAC,CAAC,EAAE;IAAEvO,IAAI,EAAEyO,SAAS;IAAEC,UAAU,EAAE,CAAC;MAC9E1O,IAAI,EAAEoF,iDAAM;MACZyI,IAAI,EAAE,CAACc,aAAa;IACxB,CAAC,EAAE;MACC3O,IAAI,EAAEqF,mDAAQA;IAClB,CAAC;EAAE,CAAC,CAAC,EAAkB;IAAEkG,QAAQ,EAAE,CAAC;MACpCvL,IAAI,EAAE2F,gDAAK;MACXkI,IAAI,EAAE,CAAC;QAAE4D,SAAS,EAAEnM,2DAAgBA;MAAC,CAAC;IAC1C,CAAC,CAAC;IAAE4E,aAAa,EAAE,CAAC;MAChBlK,IAAI,EAAE4F,uDAAY;MAClBiI,IAAI,EAAE,CAACK,WAAW;IACtB,CAAC,CAAC;IAAEgB,gBAAgB,EAAE,CAAC;MACnBlP,IAAI,EAAE4F,uDAAY;MAClBiI,IAAI,EAAE,CAACX,aAAa,EAAE;QAAEwE,IAAI,EAAEnM,sDAAW;QAAEoM,MAAM,EAAE;MAAK,CAAC;IAC7D,CAAC,CAAC;IAAEhC,gBAAgB,EAAE,CAAC;MACnB3P,IAAI,EAAE6F,oDAAS;MACfgI,IAAI,EAAE,CAACtI,sDAAW,EAAE;QAAEoM,MAAM,EAAE;MAAK,CAAC;IACxC,CAAC,CAAC;IAAEvH,SAAS,EAAE,CAAC;MACZpK,IAAI,EAAE2F,gDAAK;MACXkI,IAAI,EAAE,CAAC,OAAO;IAClB,CAAC,CAAC;IAAEhC,SAAS,EAAE,CAAC;MACZ7L,IAAI,EAAE2F,gDAAK;MACXkI,IAAI,EAAE,CAAC,YAAY;IACvB,CAAC,CAAC;IAAE/B,cAAc,EAAE,CAAC;MACjB9L,IAAI,EAAE2F,gDAAK;MACXkI,IAAI,EAAE,CAAC,iBAAiB;IAC5B,CAAC,CAAC;IAAE1C,UAAU,EAAE,CAAC;MACbnL,IAAI,EAAE2F,gDAAKA;IACf,CAAC,CAAC;IAAE+G,SAAS,EAAE,CAAC;MACZ1M,IAAI,EAAE2F,gDAAKA;IACf,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA,MAAMiM,YAAY,GAAG,2BAA2B;AAChD;AACA,MAAMC,mBAAmB,GAAG,kCAAkC;AAC9D;AACA;AACA;AACA;AACA,MAAMC,SAAS,CAAC;EACZxU,WAAWA,CAACyU,MAAM,EAAE;IAChB,IAAI,CAACA,MAAM,GAAGA,MAAM;EACxB;EACA;EACAC,IAAIA,CAAA,EAAG;IACH,IAAI,CAACD,MAAM,CAACE,OAAO,CAACC,IAAI,IAAIA,IAAI,CAACC,gBAAgB,CAAC,CAAC,CAAC;EACxD;EACA;EACAC,cAAcA,CAACC,OAAO,EAAE;IACpB,MAAMC,iBAAiB,GAAG,IAAI,CAACP,MAAM,CAACQ,IAAI,CAACL,IAAI,IAAIA,IAAI,CAACM,UAAU,CAACC,aAAa,KAAKJ,OAAO,CAAC;IAC7F,MAAMK,WAAW,GAAG,IAAI,CAACC,YAAY;IACrC,IAAIL,iBAAiB,KAAKI,WAAW,EAAE;MACnC;IACJ;IACAA,WAAW,EAAEP,gBAAgB,CAAC,CAAC;IAC/B,IAAIG,iBAAiB,EAAE;MACnB,MAAMM,OAAO,GAAGF,WAAW,EAAEF,UAAU,CAACC,aAAa,CAACI,qBAAqB,GAAG,CAAC;MAC/E;MACAP,iBAAiB,CAACQ,cAAc,CAACF,OAAO,CAAC;MACzC,IAAI,CAACD,YAAY,GAAGL,iBAAiB;IACzC;EACJ;AACJ;AACA,MAAMS,UAAU,CAAC;EACbzV,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC0V,WAAW,GAAGlN,qDAAM,CAACC,qDAAU,CAAC;IACrC,IAAI,CAACkN,aAAa,GAAG,KAAK;EAC9B;EACA;EACA,IAAIzH,kBAAkBA,CAAA,EAAG;IACrB,OAAO,IAAI,CAACyH,aAAa;EAC7B;EACA,IAAIzH,kBAAkBA,CAAC0H,QAAQ,EAAE;IAC7B,IAAI,IAAI,CAACD,aAAa,KAAKC,QAAQ,EAAE;MACjC,IAAI,CAACD,aAAa,GAAGC,QAAQ;MAC7B,IAAI,IAAI,CAACC,cAAc,EAAE;QACrB,IAAI,CAACC,oBAAoB,CAAC,CAAC;MAC/B;IACJ;EACJ;EACA;EACAN,cAAcA,CAACO,2BAA2B,EAAE;IACxC,MAAMhB,OAAO,GAAG,IAAI,CAACW,WAAW,CAACP,aAAa;IAC9C;IACA;IACA,IAAI,CAACY,2BAA2B,IAC5B,CAAChB,OAAO,CAACQ,qBAAqB,IAC9B,CAAC,IAAI,CAACS,qBAAqB,EAAE;MAC7BjB,OAAO,CAACkB,SAAS,CAACC,GAAG,CAAC5B,YAAY,CAAC;MACnC;IACJ;IACA;IACA;IACA;IACA,MAAM6B,iBAAiB,GAAGpB,OAAO,CAACQ,qBAAqB,CAAC,CAAC;IACzD,MAAMa,UAAU,GAAGL,2BAA2B,CAACM,KAAK,GAAGF,iBAAiB,CAACE,KAAK;IAC9E,MAAMC,SAAS,GAAGP,2BAA2B,CAACQ,IAAI,GAAGJ,iBAAiB,CAACI,IAAI;IAC3ExB,OAAO,CAACkB,SAAS,CAACC,GAAG,CAAC3B,mBAAmB,CAAC;IAC1C,IAAI,CAACyB,qBAAqB,CAAC5K,KAAK,CAACoL,WAAW,CAAC,WAAW,EAAE,cAAcF,SAAS,cAAcF,UAAU,GAAG,CAAC;IAC7G;IACArB,OAAO,CAACQ,qBAAqB,CAAC,CAAC;IAC/BR,OAAO,CAACkB,SAAS,CAACQ,MAAM,CAAClC,mBAAmB,CAAC;IAC7CQ,OAAO,CAACkB,SAAS,CAACC,GAAG,CAAC5B,YAAY,CAAC;IACnC,IAAI,CAAC0B,qBAAqB,CAAC5K,KAAK,CAACoL,WAAW,CAAC,WAAW,EAAE,EAAE,CAAC;EACjE;EACA;EACA3B,gBAAgBA,CAAA,EAAG;IACf,IAAI,CAACa,WAAW,CAACP,aAAa,CAACc,SAAS,CAACQ,MAAM,CAACnC,YAAY,CAAC;EACjE;EACA;EACA7T,QAAQA,CAAA,EAAG;IACP,IAAI,CAACiW,oBAAoB,CAAC,CAAC;EAC/B;EACA;EACAvE,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC0D,cAAc,EAAEY,MAAM,CAAC,CAAC;IAC7B,IAAI,CAACZ,cAAc,GAAG,IAAI,CAACG,qBAAqB,GAAG,IAAI;EAC3D;EACA;EACAU,oBAAoBA,CAAA,EAAG;IACnB,MAAMC,YAAY,GAAG,IAAI,CAACjB,WAAW,CAACP,aAAa,CAACyB,aAAa,IAAIC,QAAQ;IAC7E,MAAMC,aAAa,GAAI,IAAI,CAACjB,cAAc,GAAGc,YAAY,CAACI,aAAa,CAAC,MAAM,CAAE;IAChF,MAAMC,oBAAoB,GAAI,IAAI,CAAChB,qBAAqB,GAAGW,YAAY,CAACI,aAAa,CAAC,MAAM,CAAE;IAC9FD,aAAa,CAACG,SAAS,GAAG,mBAAmB;IAC7CD,oBAAoB,CAACC,SAAS,GAC1B,kEAAkE;IACtEH,aAAa,CAACI,WAAW,CAAC,IAAI,CAAClB,qBAAqB,CAAC;IACrD,IAAI,CAACF,oBAAoB,CAAC,CAAC;EAC/B;EACA;AACJ;AACA;AACA;EACIA,oBAAoBA,CAAA,EAAG;IACnB,IAAI,CAAC,IAAI,CAACD,cAAc,KAAK,OAAOxF,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;MACzE,MAAM8G,KAAK,CAAC,6DAA6D,CAAC;IAC9E;IACA,MAAMC,aAAa,GAAG,IAAI,CAACzB,aAAa,GAClC,IAAI,CAACD,WAAW,CAACP,aAAa,CAACkC,aAAa,CAAC,mBAAmB,CAAC,GACjE,IAAI,CAAC3B,WAAW,CAACP,aAAa;IACpC,IAAI,CAACiC,aAAa,KAAK,OAAO/G,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;MACnE,MAAM8G,KAAK,CAAC,qCAAqC,CAAC;IACtD;IACAC,aAAa,CAACF,WAAW,CAAC,IAAI,CAACrB,cAAc,CAAC;EAClD;EACA;IAAS,IAAI,CAAChG,IAAI,YAAAyH,mBAAAvH,CAAA;MAAA,YAAAA,CAAA,IAAwF0F,UAAU;IAAA,CAAmD;EAAE;EACzK;IAAS,IAAI,CAACzF,IAAI,kBApR8EnR,+DAAE;MAAA6D,IAAA,EAoRJ+S,UAAU;MAAApC,MAAA;QAAAnF,kBAAA,GApRRrP,0DAAE,CAAA0U,0BAAA,8CAoRmFvL,2DAAgB;MAAA;MAAAjD,QAAA,GApRrGlG,sEAAE;IAAA,EAoRuH;EAAE;AAC/N;AACA;EAAA,QAAAwR,SAAA,oBAAAA,SAAA,KAtRoGxR,+DAAE,CAsRX4W,UAAU,EAAc,CAAC;IACxG/S,IAAI,EAAEmF,oDAASA;EACnB,CAAC,CAAC,QAAkB;IAAEqG,kBAAkB,EAAE,CAAC;MACnCxL,IAAI,EAAE2F,gDAAK;MACXkI,IAAI,EAAE,CAAC;QAAE4D,SAAS,EAAEnM,2DAAgBA;MAAC,CAAC;IAC1C,CAAC;EAAE,CAAC;AAAA;AAChB;AACA;AACA;AACA;AACA,SAASuP,+BAA+BA,CAAA,EAAG;EACvC,MAAMC,MAAM,GAAIzC,OAAO,KAAM;IACzBwB,IAAI,EAAExB,OAAO,GAAG,CAACA,OAAO,CAAC0C,UAAU,IAAI,CAAC,IAAI,IAAI,GAAG,GAAG;IACtDpB,KAAK,EAAEtB,OAAO,GAAG,CAACA,OAAO,CAAC2C,WAAW,IAAI,CAAC,IAAI,IAAI,GAAG;EACzD,CAAC,CAAC;EACF,OAAOF,MAAM;AACjB;AACA;AACA,MAAMG,uBAAuB,GAAG,IAAI/P,yDAAc,CAAC,qBAAqB,EAAE;EACtEgQ,UAAU,EAAE,MAAM;EAClBC,OAAO,EAAEN;AACb,CAAC,CAAC;;AAEF;AACA;AACA;AACA;AACA,MAAMO,kBAAkB,SAASrC,UAAU,CAAC;EACxCzV,WAAWA,CAACkV,UAAU,EAAE;IACpB,KAAK,CAAC,CAAC;IACP,IAAI,CAACA,UAAU,GAAGA,UAAU;IAC5B;IACA,IAAI,CAACjH,QAAQ,GAAG,KAAK;EACzB;EACA;EACA8J,KAAKA,CAAA,EAAG;IACJ,IAAI,CAAC7C,UAAU,CAACC,aAAa,CAAC4C,KAAK,CAAC,CAAC;EACzC;EACAC,aAAaA,CAAA,EAAG;IACZ,OAAO,IAAI,CAAC9C,UAAU,CAACC,aAAa,CAACsC,UAAU;EACnD;EACAQ,cAAcA,CAAA,EAAG;IACb,OAAO,IAAI,CAAC/C,UAAU,CAACC,aAAa,CAACuC,WAAW;EACpD;EACA;IAAS,IAAI,CAAC7H,IAAI,YAAAqI,2BAAAnI,CAAA;MAAA,YAAAA,CAAA,IAAwF+H,kBAAkB,EAlU5BjZ,+DAAE,CAkU4CA,qDAAa;IAAA,CAA4C;EAAE;EACzM;IAAS,IAAI,CAACmR,IAAI,kBAnU8EnR,+DAAE;MAAA6D,IAAA,EAmUJoV,kBAAkB;MAAAjT,SAAA;MAAAsT,QAAA;MAAAC,YAAA,WAAAC,gCAAA/S,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAnUhBzG,yDAAE,oBAAA0G,GAAA,CAAA0I,QAAA;UAAFpP,yDAAE,yBAAA0G,GAAA,CAAA0I,QAmUa,CAAC;QAAA;MAAA;MAAAoF,MAAA;QAAApF,QAAA,GAnUhBpP,0DAAE,CAAA0U,0BAAA,0BAmUmHvL,2DAAgB;MAAA;MAAAlD,UAAA;MAAAC,QAAA,GAnUrIlG,sEAAE,EAAFA,wEAAE;IAAA,EAmUsR;EAAE;AAC9X;AACA;EAAA,QAAAwR,SAAA,oBAAAA,SAAA,KArUoGxR,+DAAE,CAqUXiZ,kBAAkB,EAAc,CAAC;IAChHpV,IAAI,EAAEmF,oDAAS;IACf0I,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,sBAAsB;MAChC0D,IAAI,EAAE;QACF,8BAA8B,EAAE,UAAU;QAC1C,sBAAsB,EAAE;MAC5B,CAAC;MACDpP,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEpC,IAAI,EAAE7D,qDAAa4J;EAAC,CAAC,CAAC,EAAkB;IAAEwF,QAAQ,EAAE,CAAC;MAC1EvL,IAAI,EAAE2F,gDAAK;MACXkI,IAAI,EAAE,CAAC;QAAE4D,SAAS,EAAEnM,2DAAgBA;MAAC,CAAC;IAC1C,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA,MAAMsQ,2BAA2B,GAAGnO,sFAA+B,CAAC;EAChEoO,OAAO,EAAE;AACb,CAAC,CAAC;AACF;AACA;AACA;AACA;AACA,MAAMC,mBAAmB,GAAG,GAAG;AAC/B;AACA;AACA;AACA;AACA,MAAMC,sBAAsB,GAAG,GAAG;AAClC;AACA;AACA;AACA;AACA,MAAMC,qBAAqB,CAAC;EACxB;EACA,IAAI3K,aAAaA,CAAA,EAAG;IAChB,OAAO,IAAI,CAAC4K,cAAc;EAC9B;EACA,IAAI5K,aAAaA,CAAC6K,CAAC,EAAE;IACjB,MAAMvV,KAAK,GAAGwV,KAAK,CAACD,CAAC,CAAC,GAAG,CAAC,GAAGA,CAAC;IAC9B,IAAI,IAAI,CAACD,cAAc,IAAItV,KAAK,EAAE;MAC9B,IAAI,CAACyV,qBAAqB,GAAG,IAAI;MACjC,IAAI,CAACH,cAAc,GAAGtV,KAAK;MAC3B,IAAI,IAAI,CAAC0V,WAAW,EAAE;QAClB,IAAI,CAACA,WAAW,CAACC,gBAAgB,CAAC3V,KAAK,CAAC;MAC5C;IACJ;EACJ;EACArD,WAAWA,CAAC0V,WAAW,EAAEuD,kBAAkB,EAAEC,cAAc,EAAEC,IAAI,EAAEC,OAAO,EAAEC,SAAS,EAAEC,cAAc,EAAE;IACnG,IAAI,CAAC5D,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACuD,kBAAkB,GAAGA,kBAAkB;IAC5C,IAAI,CAACC,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACC,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACC,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,cAAc,GAAGA,cAAc;IACpC;IACA,IAAI,CAACC,eAAe,GAAG,CAAC;IACxB;IACA,IAAI,CAACT,qBAAqB,GAAG,KAAK;IAClC;IACA,IAAI,CAACU,UAAU,GAAG,IAAI/P,yCAAO,CAAC,CAAC;IAC/B;IACA,IAAI,CAACgQ,uBAAuB,GAAG,KAAK;IACpC;IACA,IAAI,CAACC,mBAAmB,GAAG,IAAI;IAC/B;IACA,IAAI,CAACC,oBAAoB,GAAG,IAAI;IAChC;IACA,IAAI,CAACC,cAAc,GAAG,IAAInQ,yCAAO,CAAC,CAAC;IACnC;AACR;AACA;AACA;IACQ,IAAI,CAACoQ,iBAAiB,GAAG,KAAK;IAC9B,IAAI,CAAClB,cAAc,GAAG,CAAC;IACvB;IACA,IAAI,CAACmB,kBAAkB,GAAG,IAAIpR,uDAAY,CAAC,CAAC;IAC5C;IACA,IAAI,CAACqR,YAAY,GAAG,IAAIrR,uDAAY,CAAC,CAAC;IACtC;IACA0Q,OAAO,CAACY,iBAAiB,CAAC,MAAM;MAC5BtQ,+CAAS,CAACgM,WAAW,CAACP,aAAa,EAAE,YAAY,CAAC,CAC7C8E,IAAI,CAACvP,yDAAS,CAAC,IAAI,CAAC8O,UAAU,CAAC,CAAC,CAChCU,SAAS,CAAC,MAAM;QACjB,IAAI,CAACC,aAAa,CAAC,CAAC;MACxB,CAAC,CAAC;IACN,CAAC,CAAC;EACN;EACAC,eAAeA,CAAA,EAAG;IACd;IACA1Q,+CAAS,CAAC,IAAI,CAAC2Q,kBAAkB,CAAClF,aAAa,EAAE,YAAY,EAAEmD,2BAA2B,CAAC,CACtF2B,IAAI,CAACvP,yDAAS,CAAC,IAAI,CAAC8O,UAAU,CAAC,CAAC,CAChCU,SAAS,CAAC,MAAM;MACjB,IAAI,CAACI,qBAAqB,CAAC,QAAQ,CAAC;IACxC,CAAC,CAAC;IACF5Q,+CAAS,CAAC,IAAI,CAAC6Q,cAAc,CAACpF,aAAa,EAAE,YAAY,EAAEmD,2BAA2B,CAAC,CAClF2B,IAAI,CAACvP,yDAAS,CAAC,IAAI,CAAC8O,UAAU,CAAC,CAAC,CAChCU,SAAS,CAAC,MAAM;MACjB,IAAI,CAACI,qBAAqB,CAAC,OAAO,CAAC;IACvC,CAAC,CAAC;EACN;EACAE,kBAAkBA,CAAA,EAAG;IACjB,MAAMC,SAAS,GAAG,IAAI,CAACtB,IAAI,GAAG,IAAI,CAACA,IAAI,CAACuB,MAAM,GAAG/Q,wCAAE,CAAC,KAAK,CAAC;IAC1D,MAAMgR,MAAM,GAAG,IAAI,CAACzB,cAAc,CAACwB,MAAM,CAAC,GAAG,CAAC;IAC9C,MAAME,OAAO,GAAGA,CAAA,KAAM;MAClB,IAAI,CAACC,gBAAgB,CAAC,CAAC;MACvB,IAAI,CAACC,yBAAyB,CAAC,CAAC;IACpC,CAAC;IACD,IAAI,CAAC/B,WAAW,GAAG,IAAI1O,8DAAe,CAAC,IAAI,CAACoK,MAAM,CAAC,CAC9CsG,yBAAyB,CAAC,IAAI,CAACC,mBAAmB,CAAC,CAAC,CAAC,CACrDC,cAAc,CAAC,CAAC,CAChBC,QAAQ,CAAC;IACV;IAAA,CACCC,aAAa,CAAC,MAAM,KAAK,CAAC;IAC/B,IAAI,CAACpC,WAAW,CAACC,gBAAgB,CAAC,IAAI,CAACL,cAAc,CAAC;IACtD;IACA;IACA;IACA;IACA,IAAI,CAACS,OAAO,CAACgC,QAAQ,CAACnB,IAAI,CAACtP,oDAAI,CAAC,CAAC,CAAC,CAAC,CAACuP,SAAS,CAACU,OAAO,CAAC;IACtD;IACA;IACAhR,2CAAK,CAAC6Q,SAAS,EAAEE,MAAM,EAAE,IAAI,CAAClG,MAAM,CAACzC,OAAO,EAAE,IAAI,CAACqJ,aAAa,CAAC,CAAC,CAAC,CAC9DpB,IAAI,CAACvP,yDAAS,CAAC,IAAI,CAAC8O,UAAU,CAAC,CAAC,CAChCU,SAAS,CAAC,MAAM;MACjB;MACA;MACA;MACA,IAAI,CAACd,OAAO,CAACkC,GAAG,CAAC,MAAM;QACnBC,OAAO,CAACC,OAAO,CAAC,CAAC,CAACC,IAAI,CAAC,MAAM;UACzB;UACA,IAAI,CAAClC,eAAe,GAAGmC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAED,IAAI,CAACE,GAAG,CAAC,IAAI,CAACC,qBAAqB,CAAC,CAAC,EAAE,IAAI,CAACtC,eAAe,CAAC,CAAC;UAChGqB,OAAO,CAAC,CAAC;QACb,CAAC,CAAC;MACN,CAAC,CAAC;MACF,IAAI,CAAC7B,WAAW,CAACgC,yBAAyB,CAAC,IAAI,CAACC,mBAAmB,CAAC,CAAC,CAAC;IAC1E,CAAC,CAAC;IACF;IACA;IACA;IACA,IAAI,CAACjC,WAAW,CAAC2B,MAAM,CAACR,SAAS,CAAC4B,aAAa,IAAI;MAC/C,IAAI,CAAC/B,YAAY,CAACgC,IAAI,CAACD,aAAa,CAAC;MACrC,IAAI,CAACE,YAAY,CAACF,aAAa,CAAC;IACpC,CAAC,CAAC;EACN;EACA;EACAT,aAAaA,CAAA,EAAG;IACZ,IAAI,OAAOY,cAAc,KAAK,UAAU,EAAE;MACtC,OAAOpS,wCAAK;IAChB;IACA,OAAO,IAAI,CAAC4K,MAAM,CAACzC,OAAO,CAACiI,IAAI,CAACrP,0DAAS,CAAC,IAAI,CAAC6J,MAAM,CAAC,EAAE5J,0DAAS,CAAEqR,QAAQ,IAAK,IAAIpS,6CAAU,CAAEqS,QAAQ,IAAK,IAAI,CAAC/C,OAAO,CAACY,iBAAiB,CAAC,MAAM;MAC9I,MAAMoC,cAAc,GAAG,IAAIH,cAAc,CAACI,OAAO,IAAIF,QAAQ,CAACjK,IAAI,CAACmK,OAAO,CAAC,CAAC;MAC5EH,QAAQ,CAACvH,OAAO,CAACC,IAAI,IAAIwH,cAAc,CAACE,OAAO,CAAC1H,IAAI,CAACM,UAAU,CAACC,aAAa,CAAC,CAAC;MAC/E,OAAO,MAAM;QACTiH,cAAc,CAACG,UAAU,CAAC,CAAC;MAC/B,CAAC;IACL,CAAC,CAAC,CAAC,CAAC;IACJ;IACA;IACAzR,qDAAI,CAAC,CAAC,CAAC;IACP;IACA;IACA9G,uDAAM,CAACqY,OAAO,IAAIA,OAAO,CAACG,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,WAAW,CAACrG,KAAK,GAAG,CAAC,IAAIoG,CAAC,CAACC,WAAW,CAACC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC;EAC9F;EACAC,qBAAqBA,CAAA,EAAG;IACpB;IACA,IAAI,IAAI,CAACC,cAAc,IAAI,IAAI,CAACpI,MAAM,CAACnV,MAAM,EAAE;MAC3C,IAAI,CAACub,gBAAgB,CAAC,CAAC;MACvB,IAAI,CAACgC,cAAc,GAAG,IAAI,CAACpI,MAAM,CAACnV,MAAM;MACxC,IAAI,CAAC2Z,kBAAkB,CAAC6D,YAAY,CAAC,CAAC;IAC1C;IACA;IACA;IACA,IAAI,IAAI,CAAChE,qBAAqB,EAAE;MAC5B,IAAI,CAACiE,cAAc,CAAC,IAAI,CAACpE,cAAc,CAAC;MACxC,IAAI,CAACqE,uBAAuB,CAAC,CAAC;MAC9B,IAAI,CAAClC,yBAAyB,CAAC,CAAC;MAChC,IAAI,CAAChC,qBAAqB,GAAG,KAAK;MAClC,IAAI,CAACG,kBAAkB,CAAC6D,YAAY,CAAC,CAAC;IAC1C;IACA;IACA;IACA,IAAI,IAAI,CAACG,sBAAsB,EAAE;MAC7B,IAAI,CAACC,wBAAwB,CAAC,CAAC;MAC/B,IAAI,CAACD,sBAAsB,GAAG,KAAK;MACnC,IAAI,CAAChE,kBAAkB,CAAC6D,YAAY,CAAC,CAAC;IAC1C;EACJ;EACA3K,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC4G,WAAW,EAAEoE,OAAO,CAAC,CAAC;IAC3B,IAAI,CAAC3D,UAAU,CAACtH,IAAI,CAAC,CAAC;IACtB,IAAI,CAACsH,UAAU,CAACpH,QAAQ,CAAC,CAAC;IAC1B,IAAI,CAACwH,cAAc,CAACxH,QAAQ,CAAC,CAAC;EAClC;EACA;EACAgL,cAAcA,CAACla,KAAK,EAAE;IAClB;IACA,IAAIqH,sEAAc,CAACrH,KAAK,CAAC,EAAE;MACvB;IACJ;IACA,QAAQA,KAAK,CAACma,OAAO;MACjB,KAAK5S,yDAAK;MACV,KAAKD,yDAAK;QACN,IAAI,IAAI,CAAC8S,UAAU,KAAK,IAAI,CAACvP,aAAa,EAAE;UACxC,MAAM6G,IAAI,GAAG,IAAI,CAACH,MAAM,CAAC8I,GAAG,CAAC,IAAI,CAACD,UAAU,CAAC;UAC7C,IAAI1I,IAAI,IAAI,CAACA,IAAI,CAAC3G,QAAQ,EAAE;YACxB,IAAI,CAAC6L,kBAAkB,CAACiC,IAAI,CAAC,IAAI,CAACuB,UAAU,CAAC;YAC7C,IAAI,CAACE,aAAa,CAACta,KAAK,CAAC;UAC7B;QACJ;QACA;MACJ;QACI,IAAI,CAAC6V,WAAW,CAAC0E,SAAS,CAACva,KAAK,CAAC;IACzC;EACJ;EACA;AACJ;AACA;EACIwa,iBAAiBA,CAAA,EAAG;IAChB,MAAMC,WAAW,GAAG,IAAI,CAACjI,WAAW,CAACP,aAAa,CAACwI,WAAW;IAC9D;IACA;IACA;IACA,IAAIA,WAAW,KAAK,IAAI,CAACC,mBAAmB,EAAE;MAC1C,IAAI,CAACA,mBAAmB,GAAGD,WAAW,IAAI,EAAE;MAC5C;MACA;MACA,IAAI,CAACvE,OAAO,CAACkC,GAAG,CAAC,MAAM;QACnB,IAAI,CAACT,gBAAgB,CAAC,CAAC;QACvB,IAAI,CAACC,yBAAyB,CAAC,CAAC;QAChC,IAAI,CAAC7B,kBAAkB,CAAC6D,YAAY,CAAC,CAAC;MAC1C,CAAC,CAAC;IACN;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIjC,gBAAgBA,CAAA,EAAG;IACf,IAAI,CAACgD,uBAAuB,CAAC,CAAC;IAC9B,IAAI,CAACb,uBAAuB,CAAC,CAAC;IAC9B,IAAI,CAACE,wBAAwB,CAAC,CAAC;EACnC;EACA;EACA,IAAII,UAAUA,CAAA,EAAG;IACb,OAAO,IAAI,CAACvE,WAAW,GAAG,IAAI,CAACA,WAAW,CAAC+E,eAAe,GAAG,CAAC;EAClE;EACA;EACA,IAAIR,UAAUA,CAACja,KAAK,EAAE;IAClB,IAAI,CAAC,IAAI,CAAC0a,aAAa,CAAC1a,KAAK,CAAC,IAAI,IAAI,CAACia,UAAU,KAAKja,KAAK,IAAI,CAAC,IAAI,CAAC0V,WAAW,EAAE;MAC9E;IACJ;IACA,IAAI,CAACA,WAAW,CAACiF,aAAa,CAAC3a,KAAK,CAAC;EACzC;EACA;AACJ;AACA;AACA;EACI0a,aAAaA,CAACE,KAAK,EAAE;IACjB,OAAO,IAAI,CAACxJ,MAAM,GAAG,CAAC,CAAC,IAAI,CAACA,MAAM,CAACyJ,OAAO,CAAC,CAAC,CAACD,KAAK,CAAC,GAAG,IAAI;EAC9D;EACA;AACJ;AACA;AACA;EACIjC,YAAYA,CAACmC,QAAQ,EAAE;IACnB,IAAI,IAAI,CAAC1E,uBAAuB,EAAE;MAC9B,IAAI,CAACsD,cAAc,CAACoB,QAAQ,CAAC;IACjC;IACA,IAAI,IAAI,CAAC1J,MAAM,IAAI,IAAI,CAACA,MAAM,CAACnV,MAAM,EAAE;MACnC,IAAI,CAACmV,MAAM,CAACyJ,OAAO,CAAC,CAAC,CAACC,QAAQ,CAAC,CAACpG,KAAK,CAAC,CAAC;MACvC;MACA;MACA;MACA,MAAMqG,WAAW,GAAG,IAAI,CAACC,iBAAiB,CAAClJ,aAAa;MACxD,MAAMmJ,GAAG,GAAG,IAAI,CAACtD,mBAAmB,CAAC,CAAC;MACtC,IAAIsD,GAAG,IAAI,KAAK,EAAE;QACdF,WAAW,CAACG,UAAU,GAAG,CAAC;MAC9B,CAAC,MACI;QACDH,WAAW,CAACG,UAAU,GAAGH,WAAW,CAACI,WAAW,GAAGJ,WAAW,CAAC1G,WAAW;MAC9E;IACJ;EACJ;EACA;EACAsD,mBAAmBA,CAAA,EAAG;IAClB,OAAO,IAAI,CAAC7B,IAAI,IAAI,IAAI,CAACA,IAAI,CAAC9V,KAAK,KAAK,KAAK,GAAG,KAAK,GAAG,KAAK;EACjE;EACA;EACA6Z,wBAAwBA,CAAA,EAAG;IACvB,IAAI,IAAI,CAACrD,iBAAiB,EAAE;MACxB;IACJ;IACA,MAAM4E,cAAc,GAAG,IAAI,CAACA,cAAc;IAC1C,MAAMC,UAAU,GAAG,IAAI,CAAC1D,mBAAmB,CAAC,CAAC,KAAK,KAAK,GAAG,CAACyD,cAAc,GAAGA,cAAc;IAC1F;IACA;IACA;IACA;IACA;IACA;IACA,IAAI,CAACE,QAAQ,CAACxJ,aAAa,CAAC/J,KAAK,CAAC+I,SAAS,GAAG,cAAcuH,IAAI,CAACkD,KAAK,CAACF,UAAU,CAAC,KAAK;IACvF;IACA;IACA;IACA;IACA,IAAI,IAAI,CAACrF,SAAS,CAACwF,OAAO,IAAI,IAAI,CAACxF,SAAS,CAACyF,IAAI,EAAE;MAC/C,IAAI,CAACT,iBAAiB,CAAClJ,aAAa,CAACoJ,UAAU,GAAG,CAAC;IACvD;EACJ;EACA;EACA,IAAIE,cAAcA,CAAA,EAAG;IACjB,OAAO,IAAI,CAAClF,eAAe;EAC/B;EACA,IAAIkF,cAAcA,CAACpb,KAAK,EAAE;IACtB,IAAI,CAAC0b,SAAS,CAAC1b,KAAK,CAAC;EACzB;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACI2b,aAAaA,CAACC,SAAS,EAAE;IACrB,MAAMC,UAAU,GAAG,IAAI,CAACb,iBAAiB,CAAClJ,aAAa,CAACuC,WAAW;IACnE;IACA,MAAMyH,YAAY,GAAI,CAACF,SAAS,IAAI,QAAQ,GAAG,CAAC,CAAC,GAAG,CAAC,IAAIC,UAAU,GAAI,CAAC;IACxE,OAAO,IAAI,CAACH,SAAS,CAAC,IAAI,CAACxF,eAAe,GAAG4F,YAAY,CAAC;EAC9D;EACA;EACAC,qBAAqBA,CAACH,SAAS,EAAE;IAC7B,IAAI,CAAC9E,aAAa,CAAC,CAAC;IACpB,IAAI,CAAC6E,aAAa,CAACC,SAAS,CAAC;EACjC;EACA;AACJ;AACA;AACA;AACA;AACA;EACIlC,cAAcA,CAACsC,UAAU,EAAE;IACvB,IAAI,IAAI,CAACxF,iBAAiB,EAAE;MACxB;IACJ;IACA,MAAMyF,aAAa,GAAG,IAAI,CAAC7K,MAAM,GAAG,IAAI,CAACA,MAAM,CAACyJ,OAAO,CAAC,CAAC,CAACmB,UAAU,CAAC,GAAG,IAAI;IAC5E,IAAI,CAACC,aAAa,EAAE;MAChB;IACJ;IACA;IACA,MAAMJ,UAAU,GAAG,IAAI,CAACb,iBAAiB,CAAClJ,aAAa,CAACuC,WAAW;IACnE,MAAM;MAAED,UAAU;MAAEC;IAAY,CAAC,GAAG4H,aAAa,CAACpK,UAAU,CAACC,aAAa;IAC1E,IAAIoK,cAAc,EAAEC,aAAa;IACjC,IAAI,IAAI,CAACxE,mBAAmB,CAAC,CAAC,IAAI,KAAK,EAAE;MACrCuE,cAAc,GAAG9H,UAAU;MAC3B+H,aAAa,GAAGD,cAAc,GAAG7H,WAAW;IAChD,CAAC,MACI;MACD8H,aAAa,GAAG,IAAI,CAACC,aAAa,CAACtK,aAAa,CAACuC,WAAW,GAAGD,UAAU;MACzE8H,cAAc,GAAGC,aAAa,GAAG9H,WAAW;IAChD;IACA,MAAMgI,gBAAgB,GAAG,IAAI,CAACjB,cAAc;IAC5C,MAAMkB,eAAe,GAAG,IAAI,CAAClB,cAAc,GAAGS,UAAU;IACxD,IAAIK,cAAc,GAAGG,gBAAgB,EAAE;MACnC;MACA,IAAI,CAACjB,cAAc,IAAIiB,gBAAgB,GAAGH,cAAc;IAC5D,CAAC,MACI,IAAIC,aAAa,GAAGG,eAAe,EAAE;MACtC;MACA,IAAI,CAAClB,cAAc,IAAI/C,IAAI,CAACE,GAAG,CAAC4D,aAAa,GAAGG,eAAe,EAAEJ,cAAc,GAAGG,gBAAgB,CAAC;IACvG;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACI7B,uBAAuBA,CAAA,EAAG;IACtB,IAAI,IAAI,CAAChE,iBAAiB,EAAE;MACxB,IAAI,CAACJ,uBAAuB,GAAG,KAAK;IACxC,CAAC,MACI;MACD,MAAMmG,SAAS,GAAG,IAAI,CAACH,aAAa,CAACtK,aAAa,CAACqJ,WAAW,GAAG,IAAI,CAAC9I,WAAW,CAACP,aAAa,CAACuC,WAAW;MAC3G,IAAI,CAACkI,SAAS,EAAE;QACZ,IAAI,CAACnB,cAAc,GAAG,CAAC;MAC3B;MACA,IAAImB,SAAS,KAAK,IAAI,CAACnG,uBAAuB,EAAE;QAC5C,IAAI,CAACR,kBAAkB,CAAC6D,YAAY,CAAC,CAAC;MAC1C;MACA,IAAI,CAACrD,uBAAuB,GAAGmG,SAAS;IAC5C;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI5C,uBAAuBA,CAAA,EAAG;IACtB,IAAI,IAAI,CAACnD,iBAAiB,EAAE;MACxB,IAAI,CAACH,mBAAmB,GAAG,IAAI,CAACC,oBAAoB,GAAG,IAAI;IAC/D,CAAC,MACI;MACD;MACA,IAAI,CAACA,oBAAoB,GAAG,IAAI,CAAC8E,cAAc,IAAI,CAAC;MACpD,IAAI,CAAC/E,mBAAmB,GAAG,IAAI,CAAC+E,cAAc,IAAI,IAAI,CAAC5C,qBAAqB,CAAC,CAAC;MAC9E,IAAI,CAAC5C,kBAAkB,CAAC6D,YAAY,CAAC,CAAC;IAC1C;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIjB,qBAAqBA,CAAA,EAAG;IACpB,MAAMgE,eAAe,GAAG,IAAI,CAACJ,aAAa,CAACtK,aAAa,CAACqJ,WAAW;IACpE,MAAMU,UAAU,GAAG,IAAI,CAACb,iBAAiB,CAAClJ,aAAa,CAACuC,WAAW;IACnE,OAAOmI,eAAe,GAAGX,UAAU,IAAI,CAAC;EAC5C;EACA;EACApE,yBAAyBA,CAAA,EAAG;IACxB,MAAMgF,YAAY,GAAG,IAAI,CAACrL,MAAM,IAAI,IAAI,CAACA,MAAM,CAACnV,MAAM,GAAG,IAAI,CAACmV,MAAM,CAACyJ,OAAO,CAAC,CAAC,CAAC,IAAI,CAACnQ,aAAa,CAAC,GAAG,IAAI;IACzG,MAAMgS,oBAAoB,GAAGD,YAAY,GAAGA,YAAY,CAAC5K,UAAU,CAACC,aAAa,GAAG,IAAI;IACxF,IAAI4K,oBAAoB,EAAE;MACtB,IAAI,CAACC,OAAO,CAAClL,cAAc,CAACiL,oBAAoB,CAAC;IACrD,CAAC,MACI;MACD,IAAI,CAACC,OAAO,CAACtL,IAAI,CAAC,CAAC;IACvB;EACJ;EACA;EACAyF,aAAaA,CAAA,EAAG;IACZ,IAAI,CAACP,cAAc,CAAC1H,IAAI,CAAC,CAAC;EAC9B;EACA;AACJ;AACA;AACA;AACA;EACIoI,qBAAqBA,CAAC2E,SAAS,EAAEgB,UAAU,EAAE;IACzC;IACA;IACA,IAAIA,UAAU,IAAIA,UAAU,CAACC,MAAM,IAAI,IAAI,IAAID,UAAU,CAACC,MAAM,KAAK,CAAC,EAAE;MACpE;IACJ;IACA;IACA,IAAI,CAAC/F,aAAa,CAAC,CAAC;IACpB;IACApQ,4CAAK,CAACyO,mBAAmB,EAAEC,sBAAsB;IAC7C;IAAA,CACCwB,IAAI,CAACvP,yDAAS,CAACd,2CAAK,CAAC,IAAI,CAACgQ,cAAc,EAAE,IAAI,CAACJ,UAAU,CAAC,CAAC,CAAC,CAC5DU,SAAS,CAAC,MAAM;MACjB,MAAM;QAAEiG,iBAAiB;QAAEC;MAAS,CAAC,GAAG,IAAI,CAACpB,aAAa,CAACC,SAAS,CAAC;MACrE;MACA,IAAImB,QAAQ,KAAK,CAAC,IAAIA,QAAQ,IAAID,iBAAiB,EAAE;QACjD,IAAI,CAAChG,aAAa,CAAC,CAAC;MACxB;IACJ,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;AACA;EACI4E,SAASA,CAAClb,QAAQ,EAAE;IAChB,IAAI,IAAI,CAACgW,iBAAiB,EAAE;MACxB,OAAO;QAAEsG,iBAAiB,EAAE,CAAC;QAAEC,QAAQ,EAAE;MAAE,CAAC;IAChD;IACA,MAAMD,iBAAiB,GAAG,IAAI,CAACtE,qBAAqB,CAAC,CAAC;IACtD,IAAI,CAACtC,eAAe,GAAGmC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAED,IAAI,CAACE,GAAG,CAACuE,iBAAiB,EAAEtc,QAAQ,CAAC,CAAC;IACzE;IACA;IACA,IAAI,CAACoZ,sBAAsB,GAAG,IAAI;IAClC,IAAI,CAACD,uBAAuB,CAAC,CAAC;IAC9B,OAAO;MAAEmD,iBAAiB;MAAEC,QAAQ,EAAE,IAAI,CAAC7G;IAAgB,CAAC;EAChE;EACA;IAAS,IAAI,CAAC1J,IAAI,YAAAwQ,8BAAAtQ,CAAA;MAAA,YAAAA,CAAA,IAAwF2I,qBAAqB,EA9yB/B7Z,+DAAE,CA8yB+CA,qDAAa,GA9yB9DA,+DAAE,CA8yByEA,4DAAoB,GA9yB/FA,+DAAE,CA8yB0GnB,kEAAgB,GA9yB5HmB,+DAAE,CA8yBuI8F,8DAAiB,MA9yB1J9F,+DAAE,CA8yBqLA,iDAAS,GA9yBhMA,+DAAE,CA8yB2MuH,2DAAW,GA9yBxNvH,+DAAE,CA8yBmO8J,gEAAqB;IAAA,CAA4D;EAAE;EACxZ;IAAS,IAAI,CAACqH,IAAI,kBA/yB8EnR,+DAAE;MAAA6D,IAAA,EA+yBJgW,qBAAqB;MAAArF,MAAA;QAAAwG,iBAAA,GA/yBnBhb,0DAAE,CAAA0U,0BAAA,4CA+yB2FvL,2DAAgB;QAAA+F,aAAA,GA/yB7GlP,0DAAE,CAAA0U,0BAAA,oCA+yBgK3K,0DAAe;MAAA;MAAA8X,OAAA;QAAA5G,kBAAA;QAAAC,YAAA;MAAA;MAAAhV,QAAA,GA/yBjLlG,sEAAE;IAAA,EA+yBwR;EAAE;AAChY;AACA;EAAA,QAAAwR,SAAA,oBAAAA,SAAA,KAjzBoGxR,+DAAE,CAizBX6Z,qBAAqB,EAAc,CAAC;IACnHhW,IAAI,EAAEmF,oDAASA;EACnB,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEnF,IAAI,EAAE7D,qDAAa4J;EAAC,CAAC,EAAE;IAAE/F,IAAI,EAAE7D,4DAAoByhB;EAAC,CAAC,EAAE;IAAE5d,IAAI,EAAEhF,kEAAgB6iB;EAAC,CAAC,EAAE;IAAE7d,IAAI,EAAEiC,8DAAiB;IAAEyM,UAAU,EAAE,CAAC;MAC5I1O,IAAI,EAAEqF,mDAAQA;IAClB,CAAC;EAAE,CAAC,EAAE;IAAErF,IAAI,EAAE7D,iDAAS4hB;EAAC,CAAC,EAAE;IAAE/d,IAAI,EAAE0D,2DAAWgE;EAAC,CAAC,EAAE;IAAE1H,IAAI,EAAEyO,SAAS;IAAEC,UAAU,EAAE,CAAC;MAC9E1O,IAAI,EAAEqF,mDAAQA;IAClB,CAAC,EAAE;MACCrF,IAAI,EAAEoF,iDAAM;MACZyI,IAAI,EAAE,CAAC5H,gEAAqB;IAChC,CAAC;EAAE,CAAC,CAAC,EAAkB;IAAEkR,iBAAiB,EAAE,CAAC;MAC7CnX,IAAI,EAAE2F,gDAAK;MACXkI,IAAI,EAAE,CAAC;QAAE4D,SAAS,EAAEnM,2DAAgBA;MAAC,CAAC;IAC1C,CAAC,CAAC;IAAE+F,aAAa,EAAE,CAAC;MAChBrL,IAAI,EAAE2F,gDAAK;MACXkI,IAAI,EAAE,CAAC;QAAE4D,SAAS,EAAEvL,0DAAeA;MAAC,CAAC;IACzC,CAAC,CAAC;IAAEkR,kBAAkB,EAAE,CAAC;MACrBpX,IAAI,EAAEmG,iDAAMA;IAChB,CAAC,CAAC;IAAEkR,YAAY,EAAE,CAAC;MACfrX,IAAI,EAAEmG,iDAAMA;IAChB,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM8X,YAAY,SAASjI,qBAAqB,CAAC;EAC7C1Y,WAAWA,CAACkV,UAAU,EAAE0L,iBAAiB,EAAEC,aAAa,EAAEvC,GAAG,EAAEwC,MAAM,EAAEC,QAAQ,EAAEC,aAAa,EAAE;IAC5F,KAAK,CAAC9L,UAAU,EAAE0L,iBAAiB,EAAEC,aAAa,EAAEvC,GAAG,EAAEwC,MAAM,EAAEC,QAAQ,EAAEC,aAAa,CAAC;IACzF;IACA,IAAI,CAACvS,aAAa,GAAG,KAAK;EAC9B;EACA+L,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAACwF,OAAO,GAAG,IAAIxL,SAAS,CAAC,IAAI,CAACC,MAAM,CAAC;IACzC,KAAK,CAAC+F,kBAAkB,CAAC,CAAC;EAC9B;EACAgD,aAAaA,CAACta,KAAK,EAAE;IACjBA,KAAK,CAAC+d,cAAc,CAAC,CAAC;EAC1B;EACA;IAAS,IAAI,CAACpR,IAAI,YAAAqR,qBAAAnR,CAAA;MAAA,YAAAA,CAAA,IAAwF4Q,YAAY,EA11BtB9hB,+DAAE,CA01BsCA,qDAAa,GA11BrDA,+DAAE,CA01BgEA,4DAAoB,GA11BtFA,+DAAE,CA01BiGnB,kEAAgB,GA11BnHmB,+DAAE,CA01B8H8F,8DAAiB,MA11BjJ9F,+DAAE,CA01B4KA,iDAAS,GA11BvLA,+DAAE,CA01BkMuH,2DAAW,GA11B/MvH,+DAAE,CA01B0N8J,gEAAqB;IAAA,CAA4D;EAAE;EAC/Y;IAAS,IAAI,CAAC4J,IAAI,kBA31B8E1T,+DAAE;MAAA6D,IAAA,EA21BJie,YAAY;MAAA9b,SAAA;MAAA4N,cAAA,WAAA0O,4BAAA7b,EAAA,EAAAC,GAAA,EAAAoN,QAAA;QAAA,IAAArN,EAAA;UA31BVzG,4DAAE,CAAA8T,QAAA,EA21B+YmF,kBAAkB;QAAA;QAAA,IAAAxS,EAAA;UAAA,IAAAuN,EAAA;UA31BnahU,4DAAE,CAAAgU,EAAA,GAAFhU,yDAAE,QAAA0G,GAAA,CAAAkP,MAAA,GAAA5B,EAAA;QAAA;MAAA;MAAAI,SAAA,WAAAmO,mBAAA9b,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFzG,yDAAE,CAAA6M,GAAA;UAAF7M,yDAAE,CAAA8M,GAAA;UAAF9M,yDAAE,CAAA+M,GAAA;UAAF/M,yDAAE,CAAAgN,GAAA;UAAFhN,yDAAE,CAAAiN,GAAA;QAAA;QAAA,IAAAxG,EAAA;UAAA,IAAAuN,EAAA;UAAFhU,4DAAE,CAAAgU,EAAA,GAAFhU,yDAAE,QAAA0G,GAAA,CAAA8Y,iBAAA,GAAAxL,EAAA,CAAAG,KAAA;UAAFnU,4DAAE,CAAAgU,EAAA,GAAFhU,yDAAE,QAAA0G,GAAA,CAAAoZ,QAAA,GAAA9L,EAAA,CAAAG,KAAA;UAAFnU,4DAAE,CAAAgU,EAAA,GAAFhU,yDAAE,QAAA0G,GAAA,CAAAka,aAAA,GAAA5M,EAAA,CAAAG,KAAA;UAAFnU,4DAAE,CAAAgU,EAAA,GAAFhU,yDAAE,QAAA0G,GAAA,CAAAgV,cAAA,GAAA1H,EAAA,CAAAG,KAAA;UAAFnU,4DAAE,CAAAgU,EAAA,GAAFhU,yDAAE,QAAA0G,GAAA,CAAA8U,kBAAA,GAAAxH,EAAA,CAAAG,KAAA;QAAA;MAAA;MAAAI,SAAA;MAAA+E,QAAA;MAAAC,YAAA,WAAAiJ,0BAAA/b,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFzG,yDAAE,mDAAA0G,GAAA,CAAAkU,uBA21BO,CAAC,2BAAZlU,GAAA,CAAAyV,mBAAA,CAAoB,CAAC,IAAI,KAAd,CAAC;QAAA;MAAA;MAAA3H,MAAA;QAAA5E,aAAA,GA31BV5P,0DAAE,CAAA0U,0BAAA,oCA21BsHvL,2DAAgB;MAAA;MAAAlD,UAAA;MAAAC,QAAA,GA31BxIlG,sEAAE,EAAFA,wEAAE,EAAFA,iEAAE;MAAA+U,kBAAA,EAAArI,GAAA;MAAAtG,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAkc,sBAAAhc,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAA,MAAAic,GAAA,GAAF1iB,8DAAE;UAAFA,6DAAE;UAAFA,4DAAE,kBA21B+mD,CAAC;UA31BlnDA,wDAAE,mBAAA2iB,8CAAA;YAAF3iB,2DAAE,CAAA0iB,GAAA;YAAA,OAAF1iB,yDAAE,CA21B2+C0G,GAAA,CAAA6Z,qBAAA,CAAsB,QAAQ,CAAC;UAAA,CAAC,CAAC,uBAAAqC,kDAAA/b,MAAA;YA31B9gD7G,2DAAE,CAAA0iB,GAAA;YAAA,OAAF1iB,yDAAE,CA21BiiD0G,GAAA,CAAA+U,qBAAA,CAAsB,QAAQ,EAAA5U,MAAQ,CAAC;UAAA,CAAC,CAAC,sBAAAgc,iDAAA;YA31B5kD7iB,2DAAE,CAAA0iB,GAAA;YAAA,OAAF1iB,yDAAE,CA21B8lD0G,GAAA,CAAA4U,aAAA,CAAc,CAAC;UAAA,CAAC,CAAC;UA31BjnDtb,uDAAE,YA21B8qD,CAAC;UA31BjrDA,0DAAE,CA21ByrD,CAAC;UA31B5rDA,4DAAE,eA21Bm3D,CAAC;UA31Bt3DA,wDAAE,qBAAA8iB,6CAAAjc,MAAA;YAAF7G,2DAAE,CAAA0iB,GAAA;YAAA,OAAF1iB,yDAAE,CA21BgxD0G,GAAA,CAAA6X,cAAA,CAAA1X,MAAqB,CAAC;UAAA,CAAC,CAAC;UA31B1yD7G,4DAAE,eA21Bi/D,CAAC;UA31Bp/DA,wDAAE,+BAAA+iB,uDAAA;YAAF/iB,2DAAE,CAAA0iB,GAAA;YAAA,OAAF1iB,yDAAE,CA21B49D0G,GAAA,CAAAmY,iBAAA,CAAkB,CAAC;UAAA,CAAC,CAAC;UA31Bn/D7e,4DAAE,eA21BuiE,CAAC;UA31B1iEA,0DAAE,EA21BwkE,CAAC;UA31B3kEA,0DAAE,CA21BolE,CAAC,CAAS,CAAC,CAAO,CAAC;UA31BzmEA,4DAAE,oBA21ButF,CAAC;UA31B1tFA,wDAAE,uBAAAgjB,mDAAAnc,MAAA;YAAF7G,2DAAE,CAAA0iB,GAAA;YAAA,OAAF1iB,yDAAE,CA21BylF0G,GAAA,CAAA+U,qBAAA,CAAsB,OAAO,EAAA5U,MAAQ,CAAC;UAAA,CAAC,CAAC,mBAAAoc,+CAAA;YA31BnoFjjB,2DAAE,CAAA0iB,GAAA;YAAA,OAAF1iB,yDAAE,CA21BkpF0G,GAAA,CAAA6Z,qBAAA,CAAsB,OAAO,CAAC;UAAA,CAAC,CAAC,sBAAA2C,kDAAA;YA31BprFljB,2DAAE,CAAA0iB,GAAA;YAAA,OAAF1iB,yDAAE,CA21BssF0G,GAAA,CAAA4U,aAAA,CAAc,CAAC;UAAA,CAAC,CAAC;UA31BztFtb,uDAAE,aA21BsxF,CAAC;UA31BzxFA,0DAAE,CA21BiyF,CAAC;QAAA;QAAA,IAAAyG,EAAA;UA31BpyFzG,yDAAE,2CAAA0G,GAAA,CAAAoU,oBA21Bu6C,CAAC;UA31B16C9a,wDAAE,sBAAA0G,GAAA,CAAAoU,oBAAA,IAAApU,GAAA,CAAAkJ,aA21By1C,CAAC,aAAAlJ,GAAA,CAAAoU,oBAAA,QAA+H,CAAC;UA31B59C9a,uDAAE,EA21Bk3D,CAAC;UA31Br3DA,yDAAE,4BAAA0G,GAAA,CAAA+T,cAAA,qBA21Bk3D,CAAC;UA31Br3Dza,uDAAE,EA21B4/E,CAAC;UA31B//EA,yDAAE,2CAAA0G,GAAA,CAAAmU,mBA21B4/E,CAAC;UA31B//E7a,wDAAE,sBAAA0G,GAAA,CAAAmU,mBAAA,IAAAnU,GAAA,CAAAkJ,aA21B+6E,CAAC,aAAAlJ,GAAA,CAAAmU,mBAAA,QAA6H,CAAC;QAAA;MAAA;MAAAsI,YAAA,GAAsiG7Y,8DAAS,EAAwP6B,sEAAiB;MAAArD,MAAA;MAAAoM,aAAA;IAAA,EAA0P;EAAE;AACxsM;AACA;EAAA,QAAA1D,SAAA,oBAAAA,SAAA,KA71BoGxR,+DAAE,CA61BX8hB,YAAY,EAAc,CAAC;IAC1Gje,IAAI,EAAEwF,oDAAS;IACfqI,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,gBAAgB;MAAEuD,aAAa,EAAE3L,4DAAiB,CAACoL,IAAI;MAAEQ,eAAe,EAAE7L,kEAAuB,CAAC8L,OAAO;MAAEC,IAAI,EAAE;QACxH,OAAO,EAAE,oBAAoB;QAC7B,wDAAwD,EAAE,yBAAyB;QACnF,gCAAgC,EAAE;MACtC,CAAC;MAAEpP,UAAU,EAAE,IAAI;MAAErH,OAAO,EAAE,CAAC0L,8DAAS,EAAE6B,sEAAiB,CAAC;MAAE5F,QAAQ,EAAE,6yDAA6yD;MAAEuC,MAAM,EAAE,CAAC,uvFAAuvF;IAAE,CAAC;EACtoJ,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEjF,IAAI,EAAE7D,qDAAa4J;EAAC,CAAC,EAAE;IAAE/F,IAAI,EAAE7D,4DAAoByhB;EAAC,CAAC,EAAE;IAAE5d,IAAI,EAAEhF,kEAAgB6iB;EAAC,CAAC,EAAE;IAAE7d,IAAI,EAAEiC,8DAAiB;IAAEyM,UAAU,EAAE,CAAC;MAC5I1O,IAAI,EAAEqF,mDAAQA;IAClB,CAAC;EAAE,CAAC,EAAE;IAAErF,IAAI,EAAE7D,iDAAS4hB;EAAC,CAAC,EAAE;IAAE/d,IAAI,EAAE0D,2DAAWgE;EAAC,CAAC,EAAE;IAAE1H,IAAI,EAAEyO,SAAS;IAAEC,UAAU,EAAE,CAAC;MAC9E1O,IAAI,EAAEqF,mDAAQA;IAClB,CAAC,EAAE;MACCrF,IAAI,EAAEoF,iDAAM;MACZyI,IAAI,EAAE,CAAC5H,gEAAqB;IAChC,CAAC;EAAE,CAAC,CAAC,EAAkB;IAAE8L,MAAM,EAAE,CAAC;MAClC/R,IAAI,EAAEoG,0DAAe;MACrByH,IAAI,EAAE,CAACuH,kBAAkB,EAAE;QAAEmK,WAAW,EAAE;MAAM,CAAC;IACrD,CAAC,CAAC;IAAE5D,iBAAiB,EAAE,CAAC;MACpB3b,IAAI,EAAE6F,oDAAS;MACfgI,IAAI,EAAE,CAAC,kBAAkB,EAAE;QAAE8D,MAAM,EAAE;MAAK,CAAC;IAC/C,CAAC,CAAC;IAAEsK,QAAQ,EAAE,CAAC;MACXjc,IAAI,EAAE6F,oDAAS;MACfgI,IAAI,EAAE,CAAC,SAAS,EAAE;QAAE8D,MAAM,EAAE;MAAK,CAAC;IACtC,CAAC,CAAC;IAAEoL,aAAa,EAAE,CAAC;MAChB/c,IAAI,EAAE6F,oDAAS;MACfgI,IAAI,EAAE,CAAC,cAAc,EAAE;QAAE8D,MAAM,EAAE;MAAK,CAAC;IAC3C,CAAC,CAAC;IAAEkG,cAAc,EAAE,CAAC;MACjB7X,IAAI,EAAE6F,oDAAS;MACfgI,IAAI,EAAE,CAAC,eAAe;IAC1B,CAAC,CAAC;IAAE8J,kBAAkB,EAAE,CAAC;MACrB3X,IAAI,EAAE6F,oDAAS;MACfgI,IAAI,EAAE,CAAC,mBAAmB;IAC9B,CAAC,CAAC;IAAE9B,aAAa,EAAE,CAAC;MAChB/L,IAAI,EAAE2F,gDAAK;MACXkI,IAAI,EAAE,CAAC;QAAE4D,SAAS,EAAEnM,2DAAgBA;MAAC,CAAC;IAC1C,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA,MAAMka,eAAe,GAAG,IAAIta,yDAAc,CAAC,iBAAiB,CAAC;;AAE7D;AACA;AACA;AACA;AACA,MAAMua,iBAAiB,GAAG;EACtB;EACAC,YAAY,EAAElX,6DAAO,CAAC,cAAc,EAAE;EAClC;EACAC,2DAAK,CAAC,uDAAuD,EAAEC,2DAAK,CAAC;IAAE+I,SAAS,EAAE;EAAO,CAAC,CAAC,CAAC;EAC5F;EACA;EACA;EACA;EACAhJ,2DAAK,CAAC,MAAM,EAAEC,2DAAK,CAAC;IAChB+I,SAAS,EAAE,0BAA0B;IACrCkO,SAAS,EAAE,KAAK;IAChB;IACA;IACAC,UAAU,EAAE;EAChB,CAAC,CAAC,CAAC,EACHnX,2DAAK,CAAC,OAAO,EAAEC,2DAAK,CAAC;IACjB+I,SAAS,EAAE,yBAAyB;IACpCkO,SAAS,EAAE,KAAK;IAChBC,UAAU,EAAE;EAChB,CAAC,CAAC,CAAC,EACHjX,gEAAU,CAAC,wDAAwD,EAAEC,6DAAO,CAAC,sDAAsD,CAAC,CAAC,EACrID,gEAAU,CAAC,4BAA4B,EAAE,CACrCD,2DAAK,CAAC;IAAE+I,SAAS,EAAE,0BAA0B;IAAEmO,UAAU,EAAE;EAAS,CAAC,CAAC,EACtEhX,6DAAO,CAAC,sDAAsD,CAAC,CAClE,CAAC,EACFD,gEAAU,CAAC,6BAA6B,EAAE,CACtCD,2DAAK,CAAC;IAAE+I,SAAS,EAAE,yBAAyB;IAAEmO,UAAU,EAAE;EAAS,CAAC,CAAC,EACrEhX,6DAAO,CAAC,sDAAsD,CAAC,CAClE,CAAC,CACL;AACL,CAAC;;AAED;AACA;AACA;AACA;AACA,MAAMiX,gBAAgB,SAAS/Y,gEAAe,CAAC;EAC3CxJ,WAAWA,CAACwiB,wBAAwB,EAAE1R,gBAAgB,EAAE2R,KAAK,EAAEC,SAAS,EAAE;IACtE,KAAK,CAACF,wBAAwB,EAAE1R,gBAAgB,EAAE4R,SAAS,CAAC;IAC5D,IAAI,CAACD,KAAK,GAAGA,KAAK;IAClB;IACA,IAAI,CAACE,aAAa,GAAG3Y,+CAAY,CAACH,KAAK;IACvC;IACA,IAAI,CAAC+Y,WAAW,GAAG5Y,+CAAY,CAACH,KAAK;EACzC;EACA;EACApJ,QAAQA,CAAA,EAAG;IACP,KAAK,CAACA,QAAQ,CAAC,CAAC;IAChB,IAAI,CAACkiB,aAAa,GAAG,IAAI,CAACF,KAAK,CAACI,gBAAgB,CAC3C5I,IAAI,CAACrP,0DAAS,CAAC,IAAI,CAAC6X,KAAK,CAACK,iBAAiB,CAAC,IAAI,CAACL,KAAK,CAACM,SAAS,CAAC,CAAC,CAAC,CACnE7I,SAAS,CAAE8I,WAAW,IAAK;MAC5B,IAAIA,WAAW,IAAI,CAAC,IAAI,CAACC,WAAW,CAAC,CAAC,EAAE;QACpC,IAAI,CAACC,MAAM,CAAC,IAAI,CAACT,KAAK,CAACU,QAAQ,CAAC;MACpC;IACJ,CAAC,CAAC;IACF,IAAI,CAACP,WAAW,GAAG,IAAI,CAACH,KAAK,CAACW,mBAAmB,CAAClJ,SAAS,CAAC,MAAM;MAC9D,IAAI,CAAC,IAAI,CAACuI,KAAK,CAAClT,eAAe,EAAE;QAC7B,IAAI,CAAC8T,MAAM,CAAC,CAAC;MACjB;IACJ,CAAC,CAAC;EACN;EACA;EACAlR,WAAWA,CAAA,EAAG;IACV,KAAK,CAACA,WAAW,CAAC,CAAC;IACnB,IAAI,CAACwQ,aAAa,CAACW,WAAW,CAAC,CAAC;IAChC,IAAI,CAACV,WAAW,CAACU,WAAW,CAAC,CAAC;EAClC;EACA;IAAS,IAAI,CAACzT,IAAI,YAAA0T,yBAAAxT,CAAA;MAAA,YAAAA,CAAA,IAAwFwS,gBAAgB,EA78B1B1jB,+DAAE,CA68B0CA,mEAA2B,GA78BvEA,+DAAE,CA68BkFA,2DAAmB,GA78BvGA,+DAAE,CA68BkHkK,yDAAU,CAAC,MAAM0a,UAAU,CAAC,GA78BhJ5kB,+DAAE,CA68B2JoM,sDAAQ;IAAA,CAA4C;EAAE;EACnT;IAAS,IAAI,CAAC+E,IAAI,kBA98B8EnR,+DAAE;MAAA6D,IAAA,EA88BJ6f,gBAAgB;MAAA1d,SAAA;MAAAC,UAAA;MAAAC,QAAA,GA98BdlG,wEAAE;IAAA,EA88BsG;EAAE;AAC9M;AACA;EAAA,QAAAwR,SAAA,oBAAAA,SAAA,KAh9BoGxR,+DAAE,CAg9BX0jB,gBAAgB,EAAc,CAAC;IAC9G7f,IAAI,EAAEmF,oDAAS;IACf0I,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,kBAAkB;MAC5B1L,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEpC,IAAI,EAAE7D,mEAA2B2kB;EAAC,CAAC,EAAE;IAAE9gB,IAAI,EAAE7D,2DAAmBoS;EAAC,CAAC,EAAE;IAAEvO,IAAI,EAAE+gB,UAAU;IAAErS,UAAU,EAAE,CAAC;MACtH1O,IAAI,EAAEoF,iDAAM;MACZyI,IAAI,EAAE,CAACxH,yDAAU,CAAC,MAAM0a,UAAU,CAAC;IACvC,CAAC;EAAE,CAAC,EAAE;IAAE/gB,IAAI,EAAEyO,SAAS;IAAEC,UAAU,EAAE,CAAC;MAClC1O,IAAI,EAAEoF,iDAAM;MACZyI,IAAI,EAAE,CAACtF,sDAAQ;IACnB,CAAC;EAAE,CAAC,CAAC;AAAA;AACrB;AACA;AACA;AACA;AACA,MAAMwY,UAAU,CAAC;EACb;EACA,IAAI5f,QAAQA,CAACA,QAAQ,EAAE;IACnB,IAAI,CAAC6f,cAAc,GAAG7f,QAAQ;IAC9B,IAAI,CAAC8f,8BAA8B,CAAC,CAAC;EACzC;EACA3jB,WAAWA,CAAC0V,WAAW,EAAEyD,IAAI,EAAEyH,iBAAiB,EAAE;IAC9C,IAAI,CAAClL,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACyD,IAAI,GAAGA,IAAI;IAChB;IACA,IAAI,CAACyK,sBAAsB,GAAG5Z,+CAAY,CAACH,KAAK;IAChD;IACA,IAAI,CAACga,qBAAqB,GAAG,IAAIpa,yCAAO,CAAC,CAAC;IAC1C;IACA,IAAI,CAACqa,YAAY,GAAG,IAAIpb,uDAAY,CAAC,CAAC;IACtC;IACA,IAAI,CAACma,gBAAgB,GAAG,IAAIna,uDAAY,CAAC,CAAC;IAC1C;IACA,IAAI,CAAC0a,mBAAmB,GAAG,IAAI1a,uDAAY,CAAC,CAAC;IAC7C;IACA,IAAI,CAACqb,WAAW,GAAG,IAAIrb,uDAAY,CAAC,IAAI,CAAC;IACzC;IACA;IACA;IACA,IAAI,CAACuD,iBAAiB,GAAG,OAAO;IAChC;IACA,IAAI,CAACsD,eAAe,GAAG,KAAK;IAC5B,IAAI4J,IAAI,EAAE;MACN,IAAI,CAACyK,sBAAsB,GAAGzK,IAAI,CAACuB,MAAM,CAACR,SAAS,CAAEoE,GAAG,IAAK;QACzD,IAAI,CAACqF,8BAA8B,CAACrF,GAAG,CAAC;QACxCsC,iBAAiB,CAAC9D,YAAY,CAAC,CAAC;MACpC,CAAC,CAAC;IACN;IACA;IACA;IACA,IAAI,CAAC+G,qBAAqB,CACrB5J,IAAI,CAAClP,qEAAoB,CAAC,CAACiZ,CAAC,EAAEC,CAAC,KAAK;MACrC,OAAOD,CAAC,CAACE,SAAS,KAAKD,CAAC,CAACC,SAAS,IAAIF,CAAC,CAACG,OAAO,KAAKF,CAAC,CAACE,OAAO;IACjE,CAAC,CAAC,CAAC,CACEjK,SAAS,CAAChX,KAAK,IAAI;MACpB;MACA,IAAI,IAAI,CAAC4f,iBAAiB,CAAC5f,KAAK,CAACihB,OAAO,CAAC,IAAI,IAAI,CAACrB,iBAAiB,CAAC,IAAI,CAACC,SAAS,CAAC,EAAE;QACjF,IAAI,CAACgB,WAAW,CAAChI,IAAI,CAAC,CAAC;MAC3B;MACA,IAAI,IAAI,CAAC+G,iBAAiB,CAAC5f,KAAK,CAACghB,SAAS,CAAC,IAAI,CAAC,IAAI,CAACpB,iBAAiB,CAAC,IAAI,CAACC,SAAS,CAAC,EAAE;QACpF,IAAI,CAACK,mBAAmB,CAACrH,IAAI,CAAC,CAAC;MACnC;IACJ,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;EACItb,QAAQA,CAAA,EAAG;IACP,IAAI,IAAI,CAACsiB,SAAS,IAAI,QAAQ,IAAI,IAAI,CAACzT,MAAM,IAAI,IAAI,EAAE;MACnD,IAAI,CAACyT,SAAS,GAAG,IAAI,CAACqB,0BAA0B,CAAC,IAAI,CAAC9U,MAAM,CAAC;IACjE;EACJ;EACA6C,WAAWA,CAAA,EAAG;IACV,IAAI,CAACyR,sBAAsB,CAACN,WAAW,CAAC,CAAC;IACzC,IAAI,CAACO,qBAAqB,CAACzR,QAAQ,CAAC,CAAC;EACzC;EACAiS,sBAAsBA,CAACnhB,KAAK,EAAE;IAC1B,MAAM8f,WAAW,GAAG,IAAI,CAACF,iBAAiB,CAAC5f,KAAK,CAACihB,OAAO,CAAC;IACzD,IAAI,CAACtB,gBAAgB,CAAC9G,IAAI,CAACiH,WAAW,CAAC;IACvC,IAAIA,WAAW,EAAE;MACb,IAAI,CAACc,YAAY,CAAC/H,IAAI,CAAC,IAAI,CAACrG,WAAW,CAACP,aAAa,CAACmP,YAAY,CAAC;IACvE;EACJ;EACA;EACAtJ,mBAAmBA,CAAA,EAAG;IAClB,OAAO,IAAI,CAAC7B,IAAI,IAAI,IAAI,CAACA,IAAI,CAAC9V,KAAK,KAAK,KAAK,GAAG,KAAK,GAAG,KAAK;EACjE;EACA;EACAyf,iBAAiBA,CAACjf,QAAQ,EAAE;IACxB,OAAQA,QAAQ,IAAI,QAAQ,IAAIA,QAAQ,IAAI,oBAAoB,IAAIA,QAAQ,IAAI,qBAAqB;EACzG;EACA;EACA8f,8BAA8BA,CAACrF,GAAG,GAAG,IAAI,CAACtD,mBAAmB,CAAC,CAAC,EAAE;IAC7D,IAAI,IAAI,CAAC0I,cAAc,GAAG,CAAC,EAAE;MACzB,IAAI,CAACX,SAAS,GAAGzE,GAAG,IAAI,KAAK,GAAG,MAAM,GAAG,OAAO;IACpD,CAAC,MACI,IAAI,IAAI,CAACoF,cAAc,GAAG,CAAC,EAAE;MAC9B,IAAI,CAACX,SAAS,GAAGzE,GAAG,IAAI,KAAK,GAAG,OAAO,GAAG,MAAM;IACpD,CAAC,MACI;MACD,IAAI,CAACyE,SAAS,GAAG,QAAQ;IAC7B;EACJ;EACA;AACJ;AACA;AACA;EACIqB,0BAA0BA,CAAC9U,MAAM,EAAE;IAC/B,MAAMgP,GAAG,GAAG,IAAI,CAACtD,mBAAmB,CAAC,CAAC;IACtC,IAAKsD,GAAG,IAAI,KAAK,IAAIhP,MAAM,IAAI,CAAC,IAAMgP,GAAG,IAAI,KAAK,IAAIhP,MAAM,GAAG,CAAE,EAAE;MAC/D,OAAO,oBAAoB;IAC/B;IACA,OAAO,qBAAqB;EAChC;EACA;IAAS,IAAI,CAACO,IAAI,YAAA0U,mBAAAxU,CAAA;MAAA,YAAAA,CAAA,IAAwF0T,UAAU,EArkCpB5kB,+DAAE,CAqkCoCA,qDAAa,GArkCnDA,+DAAE,CAqkC8D8F,8DAAiB,MArkCjF9F,+DAAE,CAqkC4GA,4DAAoB;IAAA,CAA4C;EAAE;EAChR;IAAS,IAAI,CAAC0T,IAAI,kBAtkC8E1T,+DAAE;MAAA6D,IAAA,EAskCJ+gB,UAAU;MAAA5e,SAAA;MAAAoO,SAAA,WAAAuR,iBAAAlf,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAtkCRzG,yDAAE,CAskCqe2K,gEAAe;QAAA;QAAA,IAAAlE,EAAA;UAAA,IAAAuN,EAAA;UAtkCtfhU,4DAAE,CAAAgU,EAAA,GAAFhU,yDAAE,QAAA0G,GAAA,CAAAkf,WAAA,GAAA5R,EAAA,CAAAG,KAAA;QAAA;MAAA;MAAAI,SAAA;MAAAC,MAAA;QAAA8P,QAAA,GAAFtkB,0DAAE,CAAA2U,IAAA;QAAAlE,MAAA;QAAArD,iBAAA;QAAAsD,eAAA;QAAA1L,QAAA;MAAA;MAAA6c,OAAA;QAAAoD,YAAA;QAAAjB,gBAAA;QAAAO,mBAAA;QAAAW,WAAA;MAAA;MAAAjf,UAAA;MAAAC,QAAA,GAAFlG,iEAAE;MAAAoG,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAsf,oBAAApf,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAA,MAAAic,GAAA,GAAF1iB,8DAAE;UAAFA,4DAAE,eAskCi2B,CAAC;UAtkCp2BA,wDAAE,iCAAA8lB,gEAAAjf,MAAA;YAAF7G,2DAAE,CAAA0iB,GAAA;YAAA,OAAF1iB,yDAAE,CAskC2uB0G,GAAA,CAAA8e,sBAAA,CAAA3e,MAA6B,CAAC;UAAA,CAAC,CAAC,gCAAAkf,+DAAAlf,MAAA;YAtkC7wB7G,2DAAE,CAAA0iB,GAAA;YAAA,OAAF1iB,yDAAE,CAskCyyB0G,GAAA,CAAAse,qBAAA,CAAA3R,IAAA,CAAAxM,MAAiC,CAAC;UAAA,CAAC,CAAC;UAtkC/0B7G,wDAAE,IAAAwN,iCAAA,wBAskCi4B,CAAC;UAtkCp4BxN,0DAAE,CAskCu5B,CAAC;QAAA;QAAA,IAAAyG,EAAA;UAtkC15BzG,wDAAE,kBAAFA,6DAAE,IAAAqN,GAAA,EAAA3G,GAAA,CAAAwd,SAAA,EAAFlkB,6DAAE,IAAAkN,GAAA,EAAAxG,GAAA,CAAA0G,iBAAA,EAskC2sB,CAAC;QAAA;MAAA;MAAA+V,YAAA,GAA6yBO,gBAAgB,EAA6DrY,kEAAa;MAAAvC,MAAA;MAAAoM,aAAA;MAAAgR,IAAA;QAAAC,SAAA,EAAgE,CAAC7C,iBAAiB,CAACC,YAAY;MAAC;IAAA,EAAkG;EAAE;AAC73D;AACA;EAAA,QAAA/R,SAAA,oBAAAA,SAAA,KAxkCoGxR,+DAAE,CAwkCX4kB,UAAU,EAAc,CAAC;IACxG/gB,IAAI,EAAEwF,oDAAS;IACfqI,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,cAAc;MAAEuD,aAAa,EAAE3L,4DAAiB,CAACoL,IAAI;MAAEQ,eAAe,EAAE7L,kEAAuB,CAAC8L,OAAO;MAAEgR,UAAU,EAAE,CAAC9C,iBAAiB,CAACC,YAAY,CAAC;MAAElO,IAAI,EAAE;QACpK,OAAO,EAAE;MACb,CAAC;MAAEpP,UAAU,EAAE,IAAI;MAAErH,OAAO,EAAE,CAAC8kB,gBAAgB,EAAErY,kEAAa,CAAC;MAAE9E,QAAQ,EAAE,uXAAuX;MAAEuC,MAAM,EAAE,CAAC,siBAAsiB;IAAE,CAAC;EAClgC,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEjF,IAAI,EAAE7D,qDAAa4J;EAAC,CAAC,EAAE;IAAE/F,IAAI,EAAEiC,8DAAiB;IAAEyM,UAAU,EAAE,CAAC;MAChF1O,IAAI,EAAEqF,mDAAQA;IAClB,CAAC;EAAE,CAAC,EAAE;IAAErF,IAAI,EAAE7D,4DAAoByhB;EAAC,CAAC,CAAC,EAAkB;IAAEwD,YAAY,EAAE,CAAC;MACxEphB,IAAI,EAAEmG,iDAAMA;IAChB,CAAC,CAAC;IAAEga,gBAAgB,EAAE,CAAC;MACnBngB,IAAI,EAAEmG,iDAAMA;IAChB,CAAC,CAAC;IAAEua,mBAAmB,EAAE,CAAC;MACtB1gB,IAAI,EAAEmG,iDAAMA;IAChB,CAAC,CAAC;IAAEkb,WAAW,EAAE,CAAC;MACdrhB,IAAI,EAAEmG,iDAAMA;IAChB,CAAC,CAAC;IAAE4b,WAAW,EAAE,CAAC;MACd/hB,IAAI,EAAE6F,oDAAS;MACfgI,IAAI,EAAE,CAAC/G,gEAAe;IAC1B,CAAC,CAAC;IAAE2Z,QAAQ,EAAE,CAAC;MACXzgB,IAAI,EAAE2F,gDAAK;MACXkI,IAAI,EAAE,CAAC,SAAS;IACpB,CAAC,CAAC;IAAEjB,MAAM,EAAE,CAAC;MACT5M,IAAI,EAAE2F,gDAAKA;IACf,CAAC,CAAC;IAAE4D,iBAAiB,EAAE,CAAC;MACpBvJ,IAAI,EAAE2F,gDAAKA;IACf,CAAC,CAAC;IAAEkH,eAAe,EAAE,CAAC;MAClB7M,IAAI,EAAE2F,gDAAKA;IACf,CAAC,CAAC;IAAExE,QAAQ,EAAE,CAAC;MACXnB,IAAI,EAAE2F,gDAAKA;IACf,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA,IAAI6c,MAAM,GAAG,CAAC;AACd;AACA,MAAMC,uBAAuB,GAAG,IAAI;AACpC;AACA;AACA;AACA;AACA;AACA,MAAMC,WAAW,CAAC;EACd;EACA,IAAIlX,kBAAkBA,CAAA,EAAG;IACrB,OAAO,IAAI,CAACmX,mBAAmB;EACnC;EACA,IAAInX,kBAAkBA,CAAC7K,KAAK,EAAE;IAC1B,IAAI,CAACgiB,mBAAmB,GAAGhiB,KAAK;IAChC,IAAI,CAAC4V,kBAAkB,CAAC6D,YAAY,CAAC,CAAC;EAC1C;EACA;EACA,IAAI/O,aAAaA,CAAA,EAAG;IAChB,OAAO,IAAI,CAAC4K,cAAc;EAC9B;EACA,IAAI5K,aAAaA,CAAC1K,KAAK,EAAE;IACrB,IAAI,CAACiiB,cAAc,GAAGzM,KAAK,CAACxV,KAAK,CAAC,GAAG,IAAI,GAAGA,KAAK;EACrD;EACA;EACA,IAAI4I,iBAAiBA,CAAA,EAAG;IACpB,OAAO,IAAI,CAACsZ,kBAAkB;EAClC;EACA,IAAItZ,iBAAiBA,CAAC5I,KAAK,EAAE;IACzB,MAAMmiB,WAAW,GAAGniB,KAAK,GAAG,EAAE;IAC9B,IAAI,CAACkiB,kBAAkB,GAAG,OAAO,CAACE,IAAI,CAACD,WAAW,CAAC,GAAGniB,KAAK,GAAG,IAAI,GAAGmiB,WAAW;EACpF;EACA;AACJ;AACA;AACA;AACA;AACA;EACI,IAAIhW,eAAeA,CAAA,EAAG;IAClB,OAAO,IAAI,CAACkW,gBAAgB;EAChC;EACA,IAAIlW,eAAeA,CAACnM,KAAK,EAAE;IACvB,IAAI,CAACqiB,gBAAgB,GAAG7M,KAAK,CAACxV,KAAK,CAAC,GAAG,IAAI,GAAGA,KAAK;EACvD;EACA;AACJ;AACA;AACA;AACA;EACI,IAAIsiB,eAAeA,CAAA,EAAG;IAClB,OAAO,IAAI,CAACC,gBAAgB;EAChC;EACA,IAAID,eAAeA,CAACtiB,KAAK,EAAE;IACvB,IAAI,CAAC8hB,uBAAuB,EAAE;MAC1B,MAAM,IAAIhO,KAAK,CAAC,yEAAyE,CAAC;IAC9F;IACA,MAAMlB,SAAS,GAAG,IAAI,CAACP,WAAW,CAACP,aAAa,CAACc,SAAS;IAC1DA,SAAS,CAACQ,MAAM,CAAC,0BAA0B,EAAE,kBAAkB,IAAI,CAACkP,eAAe,EAAE,CAAC;IACtF,IAAItiB,KAAK,EAAE;MACP4S,SAAS,CAACC,GAAG,CAAC,0BAA0B,EAAE,kBAAkB7S,KAAK,EAAE,CAAC;IACxE;IACA,IAAI,CAACuiB,gBAAgB,GAAGviB,KAAK;EACjC;EACArD,WAAWA,CAAC0V,WAAW,EAAEuD,kBAAkB,EAAE4M,aAAa,EAAEvM,cAAc,EAAE;IACxE,IAAI,CAAC5D,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACuD,kBAAkB,GAAGA,kBAAkB;IAC5C,IAAI,CAACK,cAAc,GAAGA,cAAc;IACpC;IACA,IAAI,CAACjL,KAAK,GAAG,IAAIrF,oDAAS,CAAC,CAAC;IAC5B;IACA,IAAI,CAACsc,cAAc,GAAG,CAAC;IACvB;IACA,IAAI,CAACQ,oBAAoB,GAAG,IAAI;IAChC;IACA,IAAI,CAACC,qBAAqB,GAAG,CAAC;IAC9B;IACA,IAAI,CAACC,iBAAiB,GAAGhc,+CAAY,CAACH,KAAK;IAC3C;IACA,IAAI,CAACoc,qBAAqB,GAAGjc,+CAAY,CAACH,KAAK;IAC/C,IAAI,CAACwb,mBAAmB,GAAG,KAAK;IAChC;IACA,IAAI,CAACa,WAAW,GAAG,IAAI;IACvB;IACA,IAAI,CAACC,aAAa,GAAG,KAAK;IAC1B,IAAI,CAACxN,cAAc,GAAG,IAAI;IAC1B;IACA,IAAI,CAACyN,cAAc,GAAG,OAAO;IAC7B;AACR;AACA;AACA;IACQ,IAAI,CAACvM,iBAAiB,GAAG,KAAK;IAC9B;IACA,IAAI,CAACpL,aAAa,GAAG,KAAK;IAC1B;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACc,eAAe,GAAG,KAAK;IAC5B;IACA,IAAI,CAAC8W,mBAAmB,GAAG,IAAI3d,uDAAY,CAAC,CAAC;IAC7C;IACA,IAAI,CAAC4d,WAAW,GAAG,IAAI5d,uDAAY,CAAC,CAAC;IACrC;IACA,IAAI,CAAC6d,aAAa,GAAG,IAAI7d,uDAAY,CAAC,CAAC;IACvC;IACA,IAAI,CAAC8d,iBAAiB,GAAG,IAAI9d,uDAAY,CAAC,IAAI,CAAC;IAC/C;IACA,IAAI,CAAC+d,SAAS,GAAG,CAACje,qDAAM,CAAC4B,2DAAQ,CAAC,CAACsc,SAAS;IAC5C,IAAI,CAACC,QAAQ,GAAGzB,MAAM,EAAE;IACxB,IAAI,CAACjZ,iBAAiB,GAClB4Z,aAAa,IAAIA,aAAa,CAAC5Z,iBAAiB,GAAG4Z,aAAa,CAAC5Z,iBAAiB,GAAG,OAAO;IAChG,IAAI,CAAC4N,iBAAiB,GAClBgM,aAAa,IAAIA,aAAa,CAAChM,iBAAiB,IAAI,IAAI,GAClDgM,aAAa,CAAChM,iBAAiB,GAC/B,KAAK;IACf,IAAI,CAACsM,aAAa,GACdN,aAAa,IAAIA,aAAa,CAACM,aAAa,IAAI,IAAI,GAAGN,aAAa,CAACM,aAAa,GAAG,KAAK;IAC9F,IAAIN,aAAa,EAAErW,eAAe,IAAI,IAAI,EAAE;MACxC,IAAI,CAACA,eAAe,GAAGqW,aAAa,CAACrW,eAAe;IACxD;IACA,IAAI,CAACD,eAAe,GAAG,CAAC,CAACsW,aAAa,EAAEtW,eAAe;IACvD,IAAI,CAACrB,kBAAkB,GACnB2X,aAAa,IAAIA,aAAa,CAAC3X,kBAAkB,IAAI,IAAI,GACnD2X,aAAa,CAAC3X,kBAAkB,GAChC,KAAK;IACf,IAAI,CAACgY,WAAW,GACZL,aAAa,IAAIA,aAAa,CAACK,WAAW,IAAI,IAAI,GAAGL,aAAa,CAACK,WAAW,GAAG,IAAI;EAC7F;EACA;AACJ;AACA;AACA;AACA;AACA;EACItJ,qBAAqBA,CAAA,EAAG;IACpB;IACA;IACA,MAAMgK,aAAa,GAAI,IAAI,CAACtB,cAAc,GAAG,IAAI,CAACuB,cAAc,CAAC,IAAI,CAACvB,cAAc,CAAE;IACtF;IACA;IACA,IAAI,IAAI,CAAC3M,cAAc,IAAIiO,aAAa,EAAE;MACtC,MAAME,UAAU,GAAG,IAAI,CAACnO,cAAc,IAAI,IAAI;MAC9C,IAAI,CAACmO,UAAU,EAAE;QACb,IAAI,CAACN,iBAAiB,CAACzK,IAAI,CAAC,IAAI,CAACgL,kBAAkB,CAACH,aAAa,CAAC,CAAC;QACnE;QACA;QACA,MAAMI,OAAO,GAAG,IAAI,CAACC,eAAe,CAAC9R,aAAa;QAClD6R,OAAO,CAAC5b,KAAK,CAACiX,SAAS,GAAG2E,OAAO,CAAC1C,YAAY,GAAG,IAAI;MACzD;MACA;MACA;MACA/I,OAAO,CAACC,OAAO,CAAC,CAAC,CAACC,IAAI,CAAC,MAAM;QACzB,IAAI,CAACpN,KAAK,CAACsG,OAAO,CAAC,CAACxR,GAAG,EAAE8a,KAAK,KAAM9a,GAAG,CAAC2O,QAAQ,GAAGmM,KAAK,KAAK2I,aAAc,CAAC;QAC5E,IAAI,CAACE,UAAU,EAAE;UACb,IAAI,CAACT,mBAAmB,CAACtK,IAAI,CAAC6K,aAAa,CAAC;UAC5C;UACA;UACA,IAAI,CAACK,eAAe,CAAC9R,aAAa,CAAC/J,KAAK,CAACiX,SAAS,GAAG,EAAE;QAC3D;MACJ,CAAC,CAAC;IACN;IACA;IACA,IAAI,CAAChU,KAAK,CAACsG,OAAO,CAAC,CAACxR,GAAG,EAAE8a,KAAK,KAAK;MAC/B9a,GAAG,CAACU,QAAQ,GAAGoa,KAAK,GAAG2I,aAAa;MACpC;MACA;MACA,IAAI,IAAI,CAACjO,cAAc,IAAI,IAAI,IAAIxV,GAAG,CAACU,QAAQ,IAAI,CAAC,IAAI,CAACV,GAAG,CAACmM,MAAM,EAAE;QACjEnM,GAAG,CAACmM,MAAM,GAAGsX,aAAa,GAAG,IAAI,CAACjO,cAAc;MACpD;IACJ,CAAC,CAAC;IACF,IAAI,IAAI,CAACA,cAAc,KAAKiO,aAAa,EAAE;MACvC,IAAI,CAACjO,cAAc,GAAGiO,aAAa;MACnC,IAAI,CAACd,oBAAoB,GAAG,IAAI;MAChC,IAAI,CAAC7M,kBAAkB,CAAC6D,YAAY,CAAC,CAAC;IAC1C;EACJ;EACAtC,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAAC0M,yBAAyB,CAAC,CAAC;IAChC,IAAI,CAACC,qBAAqB,CAAC,CAAC;IAC5B;IACA;IACA,IAAI,CAACnB,iBAAiB,GAAG,IAAI,CAAC3X,KAAK,CAAC2D,OAAO,CAACkI,SAAS,CAAC,MAAM;MACxD,MAAM0M,aAAa,GAAG,IAAI,CAACC,cAAc,CAAC,IAAI,CAACvB,cAAc,CAAC;MAC9D;MACA;MACA,IAAIsB,aAAa,KAAK,IAAI,CAACjO,cAAc,EAAE;QACvC,MAAMyO,IAAI,GAAG,IAAI,CAAC/Y,KAAK,CAAC6P,OAAO,CAAC,CAAC;QACjC,IAAI3d,WAAW;QACf,KAAK,IAAI8mB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,IAAI,CAAC9nB,MAAM,EAAE+nB,CAAC,EAAE,EAAE;UAClC,IAAID,IAAI,CAACC,CAAC,CAAC,CAACvV,QAAQ,EAAE;YAClB;YACA;YACA;YACA,IAAI,CAACwT,cAAc,GAAG,IAAI,CAAC3M,cAAc,GAAG0O,CAAC;YAC7C,IAAI,CAACvB,oBAAoB,GAAG,IAAI;YAChCvlB,WAAW,GAAG6mB,IAAI,CAACC,CAAC,CAAC;YACrB;UACJ;QACJ;QACA;QACA;QACA;QACA,IAAI,CAAC9mB,WAAW,IAAI6mB,IAAI,CAACR,aAAa,CAAC,EAAE;UACrCrL,OAAO,CAACC,OAAO,CAAC,CAAC,CAACC,IAAI,CAAC,MAAM;YACzB2L,IAAI,CAACR,aAAa,CAAC,CAAC9U,QAAQ,GAAG,IAAI;YACnC,IAAI,CAAC0U,iBAAiB,CAACzK,IAAI,CAAC,IAAI,CAACgL,kBAAkB,CAACH,aAAa,CAAC,CAAC;UACvE,CAAC,CAAC;QACN;MACJ;MACA,IAAI,CAAC3N,kBAAkB,CAAC6D,YAAY,CAAC,CAAC;IAC1C,CAAC,CAAC;EACN;EACA;EACAoK,yBAAyBA,CAAA,EAAG;IACxB;IACA;IACA;IACA,IAAI,CAACI,QAAQ,CAACtV,OAAO,CAACiI,IAAI,CAACrP,0DAAS,CAAC,IAAI,CAAC0c,QAAQ,CAAC,CAAC,CAACpN,SAAS,CAAEkN,IAAI,IAAK;MACrE,IAAI,CAAC/Y,KAAK,CAACkZ,KAAK,CAACH,IAAI,CAACpjB,MAAM,CAACb,GAAG,IAAI;QAChC,OAAOA,GAAG,CAACwO,gBAAgB,KAAK,IAAI,IAAI,CAACxO,GAAG,CAACwO,gBAAgB;MACjE,CAAC,CAAC,CAAC;MACH,IAAI,CAACtD,KAAK,CAACmZ,eAAe,CAAC,CAAC;IAChC,CAAC,CAAC;EACN;EACArV,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC9D,KAAK,CAAC8O,OAAO,CAAC,CAAC;IACpB,IAAI,CAAC6I,iBAAiB,CAAC1C,WAAW,CAAC,CAAC;IACpC,IAAI,CAAC2C,qBAAqB,CAAC3C,WAAW,CAAC,CAAC;EAC5C;EACA;EACAmE,aAAaA,CAAA,EAAG;IACZ,IAAI,IAAI,CAACC,UAAU,EAAE;MACjB,IAAI,CAACA,UAAU,CAAC5M,yBAAyB,CAAC,CAAC;IAC/C;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACID,gBAAgBA,CAAA,EAAG;IACf,IAAI,IAAI,CAAC6M,UAAU,EAAE;MACjB,IAAI,CAACA,UAAU,CAAC7M,gBAAgB,CAAC,CAAC;IACtC;EACJ;EACA;AACJ;AACA;AACA;EACI8M,QAAQA,CAAC1J,KAAK,EAAE;IACZ,MAAM2J,MAAM,GAAG,IAAI,CAACF,UAAU;IAC9B,IAAIE,MAAM,EAAE;MACRA,MAAM,CAACtK,UAAU,GAAGW,KAAK;IAC7B;EACJ;EACA4J,aAAaA,CAAC5J,KAAK,EAAE;IACjB,IAAI,CAAC6H,oBAAoB,GAAG7H,KAAK;IACjC,IAAI,CAACqI,WAAW,CAACvK,IAAI,CAAC,IAAI,CAACgL,kBAAkB,CAAC9I,KAAK,CAAC,CAAC;EACzD;EACA8I,kBAAkBA,CAAC9I,KAAK,EAAE;IACtB,MAAM/a,KAAK,GAAG,IAAI4kB,iBAAiB,CAAC,CAAC;IACrC5kB,KAAK,CAAC+a,KAAK,GAAGA,KAAK;IACnB,IAAI,IAAI,CAAC5P,KAAK,IAAI,IAAI,CAACA,KAAK,CAAC/O,MAAM,EAAE;MACjC4D,KAAK,CAACC,GAAG,GAAG,IAAI,CAACkL,KAAK,CAAC6P,OAAO,CAAC,CAAC,CAACD,KAAK,CAAC;IAC3C;IACA,OAAO/a,KAAK;EAChB;EACA;AACJ;AACA;AACA;AACA;AACA;EACIikB,qBAAqBA,CAAA,EAAG;IACpB,IAAI,IAAI,CAAClB,qBAAqB,EAAE;MAC5B,IAAI,CAACA,qBAAqB,CAAC3C,WAAW,CAAC,CAAC;IAC5C;IACA,IAAI,CAAC2C,qBAAqB,GAAGrc,2CAAK,CAAC,GAAG,IAAI,CAACyE,KAAK,CAAC0Z,GAAG,CAAC5kB,GAAG,IAAIA,GAAG,CAAC0O,aAAa,CAAC,CAAC,CAACqI,SAAS,CAAC,MAAM,IAAI,CAACjB,kBAAkB,CAAC6D,YAAY,CAAC,CAAC,CAAC;EAC3I;EACA;EACA+J,cAAcA,CAAC5I,KAAK,EAAE;IAClB;IACA;IACA;IACA,OAAOvC,IAAI,CAACE,GAAG,CAAC,IAAI,CAACvN,KAAK,CAAC/O,MAAM,GAAG,CAAC,EAAEoc,IAAI,CAACC,GAAG,CAACsC,KAAK,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;EACnE;EACA;EACAjQ,cAAcA,CAACqZ,CAAC,EAAE;IACd,OAAO,iBAAiB,IAAI,CAACV,QAAQ,IAAIU,CAAC,EAAE;EAChD;EACA;EACA/Y,gBAAgBA,CAAC+Y,CAAC,EAAE;IAChB,OAAO,mBAAmB,IAAI,CAACV,QAAQ,IAAIU,CAAC,EAAE;EAClD;EACA;AACJ;AACA;AACA;EACIpY,wBAAwBA,CAAC+Y,SAAS,EAAE;IAChC,IAAI,CAAC,IAAI,CAAC7B,aAAa,IAAI,CAAC,IAAI,CAACJ,qBAAqB,EAAE;MACpD;IACJ;IACA,MAAMiB,OAAO,GAAG,IAAI,CAACC,eAAe,CAAC9R,aAAa;IAClD6R,OAAO,CAAC5b,KAAK,CAACuR,MAAM,GAAG,IAAI,CAACoJ,qBAAqB,GAAG,IAAI;IACxD;IACA;IACA,IAAI,IAAI,CAACkB,eAAe,CAAC9R,aAAa,CAAC8S,YAAY,EAAE;MACjDjB,OAAO,CAAC5b,KAAK,CAACuR,MAAM,GAAGqL,SAAS,GAAG,IAAI;IAC3C;EACJ;EACA;EACAjZ,2BAA2BA,CAAA,EAAG;IAC1B,MAAMiY,OAAO,GAAG,IAAI,CAACC,eAAe,CAAC9R,aAAa;IAClD,IAAI,CAAC4Q,qBAAqB,GAAGiB,OAAO,CAAC1C,YAAY;IACjD0C,OAAO,CAAC5b,KAAK,CAACuR,MAAM,GAAG,EAAE;IACzB,IAAI,CAAC4J,aAAa,CAACxK,IAAI,CAAC,CAAC;EAC7B;EACA;EACAvO,YAAYA,CAACrK,GAAG,EAAE+kB,SAAS,EAAEjK,KAAK,EAAE;IAChCiK,SAAS,CAAC5K,UAAU,GAAGW,KAAK;IAC5B,IAAI,CAAC9a,GAAG,CAAC8K,QAAQ,EAAE;MACf,IAAI,CAACF,aAAa,GAAGkQ,KAAK;IAC9B;EACJ;EACA;EACA7P,YAAYA,CAAC6P,KAAK,EAAE;IAChB,MAAMkK,WAAW,GAAG,IAAI,CAACrC,oBAAoB,IAAI,IAAI,CAAC/X,aAAa;IACnE,OAAOkQ,KAAK,KAAKkK,WAAW,GAAG,CAAC,GAAG,CAAC,CAAC;EACzC;EACA;EACAza,gBAAgBA,CAAC0a,WAAW,EAAEnK,KAAK,EAAE;IACjC;IACA;IACA;IACA;IACA,IAAImK,WAAW,IAAIA,WAAW,KAAK,OAAO,IAAIA,WAAW,KAAK,OAAO,EAAE;MACnE,IAAI,CAACV,UAAU,CAACpK,UAAU,GAAGW,KAAK;IACtC;EACJ;EACA;IAAS,IAAI,CAACpO,IAAI,YAAAwY,oBAAAtY,CAAA;MAAA,YAAAA,CAAA,IAAwFqV,WAAW,EA/7CrBvmB,+DAAE,CA+7CqCA,qDAAa,GA/7CpDA,+DAAE,CA+7C+DA,4DAAoB,GA/7CrFA,+DAAE,CA+7CgGqjB,eAAe,MA/7CjHrjB,+DAAE,CA+7C4I8J,gEAAqB;IAAA,CAA4D;EAAE;EACjU;IAAS,IAAI,CAAC4J,IAAI,kBAh8C8E1T,+DAAE;MAAA6D,IAAA,EAg8CJ0iB,WAAW;MAAAvgB,SAAA;MAAA4N,cAAA,WAAA6V,2BAAAhjB,EAAA,EAAAC,GAAA,EAAAoN,QAAA;QAAA,IAAArN,EAAA;UAh8CTzG,4DAAE,CAAA8T,QAAA,EAq8C1CrB,MAAM;QAAA;QAAA,IAAAhM,EAAA;UAAA,IAAAuN,EAAA;UAr8CkChU,4DAAE,CAAAgU,EAAA,GAAFhU,yDAAE,QAAA0G,GAAA,CAAA+hB,QAAA,GAAAzU,EAAA;QAAA;MAAA;MAAAI,SAAA,WAAAsV,kBAAAjjB,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFzG,yDAAE,CAAAyN,GAAA;UAAFzN,yDAAE,CAAA0N,GAAA;QAAA;QAAA,IAAAjH,EAAA;UAAA,IAAAuN,EAAA;UAAFhU,4DAAE,CAAAgU,EAAA,GAAFhU,yDAAE,QAAA0G,GAAA,CAAA0hB,eAAA,GAAApU,EAAA,CAAAG,KAAA;UAAFnU,4DAAE,CAAAgU,EAAA,GAAFhU,yDAAE,QAAA0G,GAAA,CAAAmiB,UAAA,GAAA7U,EAAA,CAAAG,KAAA;QAAA;MAAA;MAAAI,SAAA;MAAA+E,QAAA;MAAAC,YAAA,WAAAoQ,yBAAAljB,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFzG,wDAAE,CAg8CJ,MAAM,IAAA0G,GAAA,CAAA/B,KAAA,IAAa,SAAS,CAAlB,CAAC;UAh8CT3E,yDAAE,iCAAA0G,GAAA,CAAA0G,iBAg8CM,CAAC;UAh8CTpN,yDAAE,qCAAA0G,GAAA,CAAA4gB,aAg8CM,CAAC,sCAAA5gB,GAAA,CAAA6gB,cAAA,KAAQ,OAAT,CAAC,mCAAA7gB,GAAA,CAAA2gB,WAAD,CAAC;QAAA;MAAA;MAAA7S,MAAA;QAAA7P,KAAA;QAAA0K,kBAAA,GAh8CTrP,0DAAE,CAAA0U,0BAAA,8CAg8CmJvL,2DAAgB;QAAAke,WAAA,GAh8CrKrnB,0DAAE,CAAA0U,0BAAA,qCAg8CuNvL,2DAAgB;QAAAme,aAAA,GAh8CzOtnB,0DAAE,CAAA0U,0BAAA,oCAg8C4RvL,2DAAgB;QAAA+F,aAAA,GAh8C9SlP,0DAAE,CAAA0U,0BAAA,oCAg8CiW3K,0DAAe;QAAAwd,cAAA;QAAAna,iBAAA;QAAAuD,eAAA,GAh8ClX3Q,0DAAE,CAAA0U,0BAAA,wCAg8Cqf3K,0DAAe;QAAAiR,iBAAA,GAh8CtgBhb,0DAAE,CAAA0U,0BAAA,4CAg8CqkBvL,2DAAgB;QAAAyG,aAAA,GAh8CvlB5P,0DAAE,CAAA0U,0BAAA,oCAg8C0oBvL,2DAAgB;QAAAuH,eAAA,GAh8C5pB1Q,0DAAE,CAAA0U,0BAAA,wCAg8CqtBvL,2DAAgB;QAAA2d,eAAA;MAAA;MAAAjF,OAAA;QAAA2F,mBAAA;QAAAC,WAAA;QAAAC,aAAA;QAAAC,iBAAA;MAAA;MAAA/S,QAAA;MAAA3O,UAAA;MAAAC,QAAA,GAh8CvuBlG,gEAAE,CAg8C2xC,CACr3C;QACIsR,OAAO,EAAEkB,aAAa;QACtBjB,WAAW,EAAEgV;MACjB,CAAC,CACJ,GAr8C2FvmB,sEAAE,EAAFA,iEAAE;MAAA+U,kBAAA,EAAArI,GAAA;MAAAtG,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAsjB,qBAAApjB,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAA,MAAAic,GAAA,GAAF1iB,8DAAE;UAAFA,6DAAE;UAAFA,4DAAE,0BAq8CgjB,CAAC;UAr8CnjBA,wDAAE,0BAAA8pB,4DAAAjjB,MAAA;YAAF7G,2DAAE,CAAA0iB,GAAA;YAAA,OAAF1iB,yDAAE,CAq8Cwd0G,GAAA,CAAAsiB,aAAA,CAAAniB,MAAoB,CAAC;UAAA,CAAC,CAAC,gCAAAkjB,kEAAAljB,MAAA;YAr8Cjf7G,2DAAE,CAAA0iB,GAAA;YAAA,OAAF1iB,yDAAE,CAAA0G,GAAA,CAAAwI,aAAA,GAAArI,MAAA;UAAA,CAq8C+iB,CAAC;UAr8CljB7G,8DAAE,IAAAkO,0BAAA,mBAAFlO,uEAq8Cu4E,CAAC;UAr8Cx4EA,0DAAE,CAq8Cw5E,CAAC;UAr8C35EA,wDAAE,IAAA8P,kCAAA,MAq8C6tF,CAAC;UAr8ChuF9P,4DAAE,eAq8C83F,CAAC;UAr8Cj4FA,8DAAE,IAAA+P,0BAAA,4BAAF/P,uEAq8CuwH,CAAC;UAr8CxwHA,0DAAE,CAq8C6wH,CAAC;QAAA;QAAA,IAAAyG,EAAA;UAr8ChxHzG,wDAAE,kBAAA0G,GAAA,CAAAwI,aAAA,KAq8CsU,CAAC,kBAAAxI,GAAA,CAAAkJ,aAAkD,CAAC,sBAAAlJ,GAAA,CAAAsU,iBAA0D,CAAC;UAr8Cvbhb,uDAAE,EAq8Cq4E,CAAC;UAr8Cx4EA,wDAAE,CAAA0G,GAAA,CAAA8I,KAq8Cq4E,CAAC;UAr8Cx4ExP,uDAAE,EAq8CivF,CAAC;UAr8CpvFA,2DAAE,IAAA0G,GAAA,CAAAkhB,SAAA,SAq8CivF,CAAC;UAr8CpvF5nB,uDAAE,CAq8C02F,CAAC;UAr8C72FA,yDAAE,4BAAA0G,GAAA,CAAA+T,cAAA,qBAq8C02F,CAAC;UAr8C72Fza,uDAAE,EAq8CqwH,CAAC;UAr8CxwHA,wDAAE,CAAA0G,GAAA,CAAA8I,KAq8CqwH,CAAC;QAAA;MAAA;MAAA2T,YAAA,GAAs9QrB,YAAY,EAAsF7I,kBAAkB,EAAuFxN,8DAAe,EAA2JnB,8DAAS,EAAwPK,gEAAe,EAAiJia,UAAU;MAAA9b,MAAA;MAAAoM,aAAA;IAAA,EAAuS;EAAE;AAC34a;AACA;EAAA,QAAA1D,SAAA,oBAAAA,SAAA,KAv8CoGxR,+DAAE,CAu8CXumB,WAAW,EAAc,CAAC;IACzG1iB,IAAI,EAAEwF,oDAAS;IACfqI,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,eAAe;MAAEiD,QAAQ,EAAE,aAAa;MAAEM,aAAa,EAAE3L,4DAAiB,CAACoL,IAAI;MAAEQ,eAAe,EAAE7L,kEAAuB,CAAC8L,OAAO;MAAExD,SAAS,EAAE,CACrJ;QACIN,OAAO,EAAEkB,aAAa;QACtBjB,WAAW,EAAEgV;MACjB,CAAC,CACJ;MAAElR,IAAI,EAAE;QACL,OAAO,EAAE,mBAAmB;QAC5B,SAAS,EAAE,+BAA+B;QAC1C,0CAA0C,EAAE,eAAe;QAC3D,2CAA2C,EAAE,4BAA4B;QACzE,wCAAwC,EAAE,aAAa;QACvD,sCAAsC,EAAE;MAC5C,CAAC;MAAEpP,UAAU,EAAE,IAAI;MAAErH,OAAO,EAAE,CAC1BkjB,YAAY,EACZ7I,kBAAkB,EAClBxN,8DAAe,EACfnB,8DAAS,EACTK,gEAAe,EACfia,UAAU,CACb;MAAEre,QAAQ,EAAE,6hHAA6hH;MAAEuC,MAAM,EAAE,CAAC,m5QAAm5Q;IAAE,CAAC;EACv9X,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEjF,IAAI,EAAE7D,qDAAa4J;EAAC,CAAC,EAAE;IAAE/F,IAAI,EAAE7D,4DAAoByhB;EAAC,CAAC,EAAE;IAAE5d,IAAI,EAAEyO,SAAS;IAAEC,UAAU,EAAE,CAAC;MACxG1O,IAAI,EAAEoF,iDAAM;MACZyI,IAAI,EAAE,CAAC2R,eAAe;IAC1B,CAAC,EAAE;MACCxf,IAAI,EAAEqF,mDAAQA;IAClB,CAAC;EAAE,CAAC,EAAE;IAAErF,IAAI,EAAEyO,SAAS;IAAEC,UAAU,EAAE,CAAC;MAClC1O,IAAI,EAAEqF,mDAAQA;IAClB,CAAC,EAAE;MACCrF,IAAI,EAAEoF,iDAAM;MACZyI,IAAI,EAAE,CAAC5H,gEAAqB;IAChC,CAAC;EAAE,CAAC,CAAC,EAAkB;IAAE2e,QAAQ,EAAE,CAAC;MACpC5kB,IAAI,EAAEoG,0DAAe;MACrByH,IAAI,EAAE,CAACe,MAAM,EAAE;QAAE2Q,WAAW,EAAE;MAAK,CAAC;IACxC,CAAC,CAAC;IAAEgF,eAAe,EAAE,CAAC;MAClBvkB,IAAI,EAAE6F,oDAAS;MACfgI,IAAI,EAAE,CAAC,gBAAgB;IAC3B,CAAC,CAAC;IAAEmX,UAAU,EAAE,CAAC;MACbhlB,IAAI,EAAE6F,oDAAS;MACfgI,IAAI,EAAE,CAAC,WAAW;IACtB,CAAC,CAAC;IAAE/M,KAAK,EAAE,CAAC;MACRd,IAAI,EAAE2F,gDAAKA;IACf,CAAC,CAAC;IAAE6F,kBAAkB,EAAE,CAAC;MACrBxL,IAAI,EAAE2F,gDAAK;MACXkI,IAAI,EAAE,CAAC;QAAE4D,SAAS,EAAEnM,2DAAgBA;MAAC,CAAC;IAC1C,CAAC,CAAC;IAAEke,WAAW,EAAE,CAAC;MACdxjB,IAAI,EAAE2F,gDAAK;MACXkI,IAAI,EAAE,CAAC;QAAEyY,KAAK,EAAE,kBAAkB;QAAE7U,SAAS,EAAEnM,2DAAgBA;MAAC,CAAC;IACrE,CAAC,CAAC;IAAEme,aAAa,EAAE,CAAC;MAChBzjB,IAAI,EAAE2F,gDAAK;MACXkI,IAAI,EAAE,CAAC;QAAE4D,SAAS,EAAEnM,2DAAgBA;MAAC,CAAC;IAC1C,CAAC,CAAC;IAAE+F,aAAa,EAAE,CAAC;MAChBrL,IAAI,EAAE2F,gDAAK;MACXkI,IAAI,EAAE,CAAC;QAAE4D,SAAS,EAAEvL,0DAAeA;MAAC,CAAC;IACzC,CAAC,CAAC;IAAEwd,cAAc,EAAE,CAAC;MACjB1jB,IAAI,EAAE2F,gDAAKA;IACf,CAAC,CAAC;IAAE4D,iBAAiB,EAAE,CAAC;MACpBvJ,IAAI,EAAE2F,gDAAKA;IACf,CAAC,CAAC;IAAEmH,eAAe,EAAE,CAAC;MAClB9M,IAAI,EAAE2F,gDAAK;MACXkI,IAAI,EAAE,CAAC;QAAE4D,SAAS,EAAEvL,0DAAeA;MAAC,CAAC;IACzC,CAAC,CAAC;IAAEiR,iBAAiB,EAAE,CAAC;MACpBnX,IAAI,EAAE2F,gDAAK;MACXkI,IAAI,EAAE,CAAC;QAAE4D,SAAS,EAAEnM,2DAAgBA;MAAC,CAAC;IAC1C,CAAC,CAAC;IAAEyG,aAAa,EAAE,CAAC;MAChB/L,IAAI,EAAE2F,gDAAK;MACXkI,IAAI,EAAE,CAAC;QAAE4D,SAAS,EAAEnM,2DAAgBA;MAAC,CAAC;IAC1C,CAAC,CAAC;IAAEuH,eAAe,EAAE,CAAC;MAClB7M,IAAI,EAAE2F,gDAAK;MACXkI,IAAI,EAAE,CAAC;QAAE4D,SAAS,EAAEnM,2DAAgBA;MAAC,CAAC;IAC1C,CAAC,CAAC;IAAE2d,eAAe,EAAE,CAAC;MAClBjjB,IAAI,EAAE2F,gDAAKA;IACf,CAAC,CAAC;IAAEge,mBAAmB,EAAE,CAAC;MACtB3jB,IAAI,EAAEmG,iDAAMA;IAChB,CAAC,CAAC;IAAEyd,WAAW,EAAE,CAAC;MACd5jB,IAAI,EAAEmG,iDAAMA;IAChB,CAAC,CAAC;IAAE0d,aAAa,EAAE,CAAC;MAChB7jB,IAAI,EAAEmG,iDAAMA;IAChB,CAAC,CAAC;IAAE2d,iBAAiB,EAAE,CAAC;MACpB9jB,IAAI,EAAEmG,iDAAMA;IAChB,CAAC;EAAE,CAAC;AAAA;AAChB;AACA,MAAMif,iBAAiB,CAAC;;AAGxB;AACA,IAAImB,YAAY,GAAG,CAAC;AACpB;AACA;AACA;AACA;AACA,MAAMC,SAAS,SAASxQ,qBAAqB,CAAC;EAC1C;EACA,IAAIxK,kBAAkBA,CAAA,EAAG;IACrB,OAAO,IAAI,CAACmX,mBAAmB,CAAChiB,KAAK;EACzC;EACA,IAAI6K,kBAAkBA,CAAC7K,KAAK,EAAE;IAC1B,IAAI,CAACgiB,mBAAmB,CAACnT,IAAI,CAAC7O,KAAK,CAAC;IACpC,IAAI,CAAC4V,kBAAkB,CAAC6D,YAAY,CAAC,CAAC;EAC1C;EACA,IAAI7Q,iBAAiBA,CAAA,EAAG;IACpB,OAAO,IAAI,CAACsZ,kBAAkB;EAClC;EACA,IAAItZ,iBAAiBA,CAAC5I,KAAK,EAAE;IACzB,MAAMmiB,WAAW,GAAGniB,KAAK,GAAG,EAAE;IAC9B,IAAI,CAACkiB,kBAAkB,GAAG,OAAO,CAACE,IAAI,CAACD,WAAW,CAAC,GAAGniB,KAAK,GAAG,IAAI,GAAGmiB,WAAW;EACpF;EACA;EACA,IAAIG,eAAeA,CAAA,EAAG;IAClB,OAAO,IAAI,CAACC,gBAAgB;EAChC;EACA,IAAID,eAAeA,CAACtiB,KAAK,EAAE;IACvB,MAAM4S,SAAS,GAAG,IAAI,CAACP,WAAW,CAACP,aAAa,CAACc,SAAS;IAC1DA,SAAS,CAACQ,MAAM,CAAC,0BAA0B,EAAE,kBAAkB,IAAI,CAACkP,eAAe,EAAE,CAAC;IACtF,IAAItiB,KAAK,EAAE;MACP4S,SAAS,CAACC,GAAG,CAAC,0BAA0B,EAAE,kBAAkB7S,KAAK,EAAE,CAAC;IACxE;IACA,IAAI,CAACuiB,gBAAgB,GAAGviB,KAAK;EACjC;EACArD,WAAWA,CAACkV,UAAU,EAAEoJ,GAAG,EAAEwC,MAAM,EAAEF,iBAAiB,EAAEC,aAAa,EAAEE,QAAQ,EAAEC,aAAa,EAAE6E,aAAa,EAAE;IAC3G,KAAK,CAAC3Q,UAAU,EAAE0L,iBAAiB,EAAEC,aAAa,EAAEvC,GAAG,EAAEwC,MAAM,EAAEC,QAAQ,EAAEC,aAAa,CAAC;IACzF,IAAI,CAACqE,mBAAmB,GAAG,IAAIpb,kDAAe,CAAC,KAAK,CAAC;IACrD;IACA,IAAI,CAACic,WAAW,GAAG,IAAI;IACvB;IACA,IAAI,CAACzX,aAAa,GAAG,KAAK;IAC1B;IACA,IAAI,CAACjL,KAAK,GAAG,SAAS;IACtB,IAAI,CAACqW,iBAAiB,GAClBgM,aAAa,IAAIA,aAAa,CAAChM,iBAAiB,IAAI,IAAI,GAClDgM,aAAa,CAAChM,iBAAiB,GAC/B,KAAK;IACf,IAAI,CAAC3L,kBAAkB,GACnB2X,aAAa,IAAIA,aAAa,CAAC3X,kBAAkB,IAAI,IAAI,GACnD2X,aAAa,CAAC3X,kBAAkB,GAChC,KAAK;IACf,IAAI,CAACgY,WAAW,GACZL,aAAa,IAAIA,aAAa,CAACK,WAAW,IAAI,IAAI,GAAGL,aAAa,CAACK,WAAW,GAAG,IAAI;EAC7F;EACA1I,aAAaA,CAAA,EAAG;IACZ;EAAA;EAEJhD,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAACwF,OAAO,GAAG,IAAIxL,SAAS,CAAC,IAAI,CAACC,MAAM,CAAC;IACzC;IACA;IACA,IAAI,CAACA,MAAM,CAACzC,OAAO,CAACiI,IAAI,CAACrP,0DAAS,CAAC,IAAI,CAAC,EAAEF,yDAAS,CAAC,IAAI,CAAC8O,UAAU,CAAC,CAAC,CAACU,SAAS,CAAC,MAAM;MAClF,IAAI,CAACiP,gBAAgB,CAAC,CAAC;IAC3B,CAAC,CAAC;IACF,KAAK,CAAC3O,kBAAkB,CAAC,CAAC;EAC9B;EACAJ,eAAeA,CAAA,EAAG;IACd,IAAI,CAAC,IAAI,CAACgP,QAAQ,KAAK,OAAO/Y,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;MACnE,MAAM,IAAI8G,KAAK,CAAC,uDAAuD,CAAC;IAC5E;IACA,KAAK,CAACiD,eAAe,CAAC,CAAC;EAC3B;EACA;EACA+O,gBAAgBA,CAAA,EAAG;IACf,IAAI,CAAC,IAAI,CAAC1U,MAAM,EAAE;MACd;IACJ;IACA,MAAM4U,KAAK,GAAG,IAAI,CAAC5U,MAAM,CAACyJ,OAAO,CAAC,CAAC;IACnC,KAAK,IAAImJ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGgC,KAAK,CAAC/pB,MAAM,EAAE+nB,CAAC,EAAE,EAAE;MACnC,IAAIgC,KAAK,CAAChC,CAAC,CAAC,CAACiC,MAAM,EAAE;QACjB,IAAI,CAACvb,aAAa,GAAGsZ,CAAC;QACtB,IAAI,CAACpO,kBAAkB,CAAC6D,YAAY,CAAC,CAAC;QACtC,IAAI,IAAI,CAACsM,QAAQ,EAAE;UACf,IAAI,CAACA,QAAQ,CAACG,YAAY,GAAGF,KAAK,CAAChC,CAAC,CAAC,CAACllB,EAAE;QAC5C;QACA;MACJ;IACJ;IACA;IACA,IAAI,CAAC4L,aAAa,GAAG,CAAC,CAAC;IACvB,IAAI,CAACiS,OAAO,CAACtL,IAAI,CAAC,CAAC;EACvB;EACA8U,QAAQA,CAAA,EAAG;IACP,OAAO,IAAI,CAACJ,QAAQ,GAAG,SAAS,GAAG,IAAI,CAAC1T,WAAW,CAACP,aAAa,CAACsU,YAAY,CAAC,MAAM,CAAC;EAC1F;EACA;IAAS,IAAI,CAAC5Z,IAAI,YAAA6Z,kBAAA3Z,CAAA;MAAA,YAAAA,CAAA,IAAwFmZ,SAAS,EA5nDnBrqB,+DAAE,CA4nDmCA,qDAAa,GA5nDlDA,+DAAE,CA4nD6D8F,8DAAiB,MA5nDhF9F,+DAAE,CA4nD2GA,iDAAS,GA5nDtHA,+DAAE,CA4nDiIA,4DAAoB,GA5nDvJA,+DAAE,CA4nDkKnB,kEAAgB,GA5nDpLmB,+DAAE,CA4nD+LuH,2DAAW,GA5nD5MvH,+DAAE,CA4nDuN8J,gEAAqB,MA5nD9O9J,+DAAE,CA4nDyQqjB,eAAe;IAAA,CAA4D;EAAE;EACxb;IAAS,IAAI,CAAC3P,IAAI,kBA7nD8E1T,+DAAE;MAAA6D,IAAA,EA6nDJwmB,SAAS;MAAArkB,SAAA;MAAA4N,cAAA,WAAAkX,yBAAArkB,EAAA,EAAAC,GAAA,EAAAoN,QAAA;QAAA,IAAArN,EAAA;UA7nDPzG,4DAAE,CAAA8T,QAAA,EA6nDijCiX,UAAU;QAAA;QAAA,IAAAtkB,EAAA;UAAA,IAAAuN,EAAA;UA7nD7jChU,4DAAE,CAAAgU,EAAA,GAAFhU,yDAAE,QAAA0G,GAAA,CAAAkP,MAAA,GAAA5B,EAAA;QAAA;MAAA;MAAAI,SAAA,WAAA4W,gBAAAvkB,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFzG,yDAAE,CAAA6M,GAAA;UAAF7M,yDAAE,CAAA8M,GAAA;UAAF9M,yDAAE,CAAA+M,GAAA;UAAF/M,yDAAE,CAAAgN,GAAA;UAAFhN,yDAAE,CAAAiN,GAAA;QAAA;QAAA,IAAAxG,EAAA;UAAA,IAAAuN,EAAA;UAAFhU,4DAAE,CAAAgU,EAAA,GAAFhU,yDAAE,QAAA0G,GAAA,CAAA8Y,iBAAA,GAAAxL,EAAA,CAAAG,KAAA;UAAFnU,4DAAE,CAAAgU,EAAA,GAAFhU,yDAAE,QAAA0G,GAAA,CAAAoZ,QAAA,GAAA9L,EAAA,CAAAG,KAAA;UAAFnU,4DAAE,CAAAgU,EAAA,GAAFhU,yDAAE,QAAA0G,GAAA,CAAAka,aAAA,GAAA5M,EAAA,CAAAG,KAAA;UAAFnU,4DAAE,CAAAgU,EAAA,GAAFhU,yDAAE,QAAA0G,GAAA,CAAAgV,cAAA,GAAA1H,EAAA,CAAAG,KAAA;UAAFnU,4DAAE,CAAAgU,EAAA,GAAFhU,yDAAE,QAAA0G,GAAA,CAAA8U,kBAAA,GAAAxH,EAAA,CAAAG,KAAA;QAAA;MAAA;MAAAI,SAAA;MAAA+E,QAAA;MAAAC,YAAA,WAAA0R,uBAAAxkB,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFzG,yDAAE,SA6nDJ0G,GAAA,CAAAikB,QAAA,CAAS,CAAC;UA7nDR3qB,yDAAE,iCAAA0G,GAAA,CAAA0G,iBA6nDI,CAAC;UA7nDPpN,yDAAE,mDAAA0G,GAAA,CAAAkU,uBA6nDI,CAAC,2BAATlU,GAAA,CAAAyV,mBAAA,CAAoB,CAAC,IAAI,KAAjB,CAAC,qCAAAzV,GAAA,CAAA2gB,WAAD,CAAC,gBAAA3gB,GAAA,CAAA/B,KAAA,KAAC,MAAM,IAAA+B,GAAA,CAAA/B,KAAA,KAAc,QAAtB,CAAC,eAAA+B,GAAA,CAAA/B,KAAA,KAAC,QAAF,CAAC,aAAA+B,GAAA,CAAA/B,KAAA,KAAC,MAAF,CAAC,4BAAA+B,GAAA,CAAA+T,cAAA,KAAU,gBAAX,CAAC;QAAA;MAAA;MAAAjG,MAAA;QAAAnF,kBAAA,GA7nDPrP,0DAAE,CAAA0U,0BAAA,8CA6nDqIvL,2DAAgB;QAAAke,WAAA,GA7nDvJrnB,0DAAE,CAAA0U,0BAAA,qCA6nDyMvL,2DAAgB;QAAAiE,iBAAA;QAAA0Z,eAAA;QAAAlX,aAAA,GA7nD3N5P,0DAAE,CAAA0U,0BAAA,oCA6nD0VvL,2DAAgB;QAAAxE,KAAA;QAAA4lB,QAAA;MAAA;MAAA3V,QAAA;MAAA3O,UAAA;MAAAC,QAAA,GA7nD5WlG,sEAAE,EAAFA,wEAAE,EAAFA,iEAAE;MAAAkrB,KAAA,EAAAta,IAAA;MAAAmE,kBAAA,EAAArI,GAAA;MAAAtG,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAA4kB,mBAAA1kB,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAA,MAAAic,GAAA,GAAF1iB,8DAAE;UAAFA,6DAAE;UAAFA,4DAAE,kBA6nDs0E,CAAC;UA7nDz0EA,wDAAE,mBAAAorB,2CAAA;YAAFprB,2DAAE,CAAA0iB,GAAA;YAAA,OAAF1iB,yDAAE,CA6nDksE0G,GAAA,CAAA6Z,qBAAA,CAAsB,QAAQ,CAAC;UAAA,CAAC,CAAC,uBAAA8K,+CAAAxkB,MAAA;YA7nDruE7G,2DAAE,CAAA0iB,GAAA;YAAA,OAAF1iB,yDAAE,CA6nDwvE0G,GAAA,CAAA+U,qBAAA,CAAsB,QAAQ,EAAA5U,MAAQ,CAAC;UAAA,CAAC,CAAC,sBAAAykB,8CAAA;YA7nDnyEtrB,2DAAE,CAAA0iB,GAAA;YAAA,OAAF1iB,yDAAE,CA6nDqzE0G,GAAA,CAAA4U,aAAA,CAAc,CAAC;UAAA,CAAC,CAAC;UA7nDx0Etb,uDAAE,YA6nDq4E,CAAC;UA7nDx4EA,0DAAE,CA6nDg5E,CAAC;UA7nDn5EA,4DAAE,eA6nDq/E,CAAC;UA7nDx/EA,wDAAE,qBAAAurB,0CAAA1kB,MAAA;YAAF7G,2DAAE,CAAA0iB,GAAA;YAAA,OAAF1iB,yDAAE,CA6nD69E0G,GAAA,CAAA6X,cAAA,CAAA1X,MAAqB,CAAC;UAAA,CAAC,CAAC;UA7nDv/E7G,4DAAE,eA6nD8kF,CAAC;UA7nDjlFA,wDAAE,+BAAAwrB,oDAAA;YAAFxrB,2DAAE,CAAA0iB,GAAA;YAAA,OAAF1iB,yDAAE,CA6nDyjF0G,GAAA,CAAAmY,iBAAA,CAAkB,CAAC;UAAA,CAAC,CAAC;UA7nDhlF7e,4DAAE,eA6nDmoF,CAAC;UA7nDtoFA,0DAAE,EA6nDoqF,CAAC;UA7nDvqFA,0DAAE,CA6nDgrF,CAAC,CAAS,CAAC,CAAO,CAAC;UA7nDrsFA,4DAAE,oBA6nDmzG,CAAC;UA7nDtzGA,wDAAE,uBAAAyrB,gDAAA5kB,MAAA;YAAF7G,2DAAE,CAAA0iB,GAAA;YAAA,OAAF1iB,yDAAE,CA6nDqrG0G,GAAA,CAAA+U,qBAAA,CAAsB,OAAO,EAAA5U,MAAQ,CAAC;UAAA,CAAC,CAAC,mBAAA6kB,4CAAA;YA7nD/tG1rB,2DAAE,CAAA0iB,GAAA;YAAA,OAAF1iB,yDAAE,CA6nD8uG0G,GAAA,CAAA6Z,qBAAA,CAAsB,OAAO,CAAC;UAAA,CAAC,CAAC,sBAAAoL,+CAAA;YA7nDhxG3rB,2DAAE,CAAA0iB,GAAA;YAAA,OAAF1iB,yDAAE,CA6nDkyG0G,GAAA,CAAA4U,aAAA,CAAc,CAAC;UAAA,CAAC,CAAC;UA7nDrzGtb,uDAAE,aA6nDk3G,CAAC;UA7nDr3GA,0DAAE,CA6nD63G,CAAC;QAAA;QAAA,IAAAyG,EAAA;UA7nDh4GzG,yDAAE,2CAAA0G,GAAA,CAAAoU,oBA6nD8nE,CAAC;UA7nDjoE9a,wDAAE,sBAAA0G,GAAA,CAAAoU,oBAAA,IAAApU,GAAA,CAAAkJ,aA6nDgjE,CAAC,aAAAlJ,GAAA,CAAAoU,oBAAA,QAA+H,CAAC;UA7nDnrE9a,uDAAE,GA6nDwlG,CAAC;UA7nD3lGA,yDAAE,2CAAA0G,GAAA,CAAAmU,mBA6nDwlG,CAAC;UA7nD3lG7a,wDAAE,sBAAA0G,GAAA,CAAAmU,mBAAA,IAAAnU,GAAA,CAAAkJ,aA6nD2gG,CAAC,aAAAlJ,GAAA,CAAAmU,mBAAA,QAA6H,CAAC;QAAA;MAAA;MAAAsI,YAAA,GAAokO7Y,8DAAS,EAAwP6B,sEAAiB;MAAArD,MAAA;MAAAoM,aAAA;IAAA,EAA0P;EAAE;AACl0V;AACA;EAAA,QAAA1D,SAAA,oBAAAA,SAAA,KA/nDoGxR,+DAAE,CA+nDXqqB,SAAS,EAAc,CAAC;IACvGxmB,IAAI,EAAEwF,oDAAS;IACfqI,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,mBAAmB;MAAEiD,QAAQ,EAAE,yBAAyB;MAAES,IAAI,EAAE;QACvE,aAAa,EAAE,YAAY;QAC3B,OAAO,EAAE,wCAAwC;QACjD,wDAAwD,EAAE,yBAAyB;QACnF,gCAAgC,EAAE,gCAAgC;QAClE,0CAA0C,EAAE,aAAa;QACzD,qBAAqB,EAAE,wCAAwC;QAC/D,oBAAoB,EAAE,oBAAoB;QAC1C,kBAAkB,EAAE,kBAAkB;QACtC,iCAAiC,EAAE,qCAAqC;QACxE,sCAAsC,EAAE;MAC5C,CAAC;MAAEH,aAAa,EAAE3L,4DAAiB,CAACoL,IAAI;MAAEQ,eAAe,EAAE7L,kEAAuB,CAAC8L,OAAO;MAAEnP,UAAU,EAAE,IAAI;MAAErH,OAAO,EAAE,CAAC0L,8DAAS,EAAE6B,sEAAiB,CAAC;MAAE5F,QAAQ,EAAE,krDAAkrD;MAAEuC,MAAM,EAAE,CAAC,qxNAAqxN;IAAE,CAAC;EACloR,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEjF,IAAI,EAAE7D,qDAAa4J;EAAC,CAAC,EAAE;IAAE/F,IAAI,EAAEiC,8DAAiB;IAAEyM,UAAU,EAAE,CAAC;MAChF1O,IAAI,EAAEqF,mDAAQA;IAClB,CAAC;EAAE,CAAC,EAAE;IAAErF,IAAI,EAAE7D,iDAAS4hB;EAAC,CAAC,EAAE;IAAE/d,IAAI,EAAE7D,4DAAoByhB;EAAC,CAAC,EAAE;IAAE5d,IAAI,EAAEhF,kEAAgB6iB;EAAC,CAAC,EAAE;IAAE7d,IAAI,EAAE0D,2DAAWgE;EAAC,CAAC,EAAE;IAAE1H,IAAI,EAAEyO,SAAS;IAAEC,UAAU,EAAE,CAAC;MAC1I1O,IAAI,EAAEqF,mDAAQA;IAClB,CAAC,EAAE;MACCrF,IAAI,EAAEoF,iDAAM;MACZyI,IAAI,EAAE,CAAC5H,gEAAqB;IAChC,CAAC;EAAE,CAAC,EAAE;IAAEjG,IAAI,EAAEyO,SAAS;IAAEC,UAAU,EAAE,CAAC;MAClC1O,IAAI,EAAEqF,mDAAQA;IAClB,CAAC,EAAE;MACCrF,IAAI,EAAEoF,iDAAM;MACZyI,IAAI,EAAE,CAAC2R,eAAe;IAC1B,CAAC;EAAE,CAAC,CAAC,EAAkB;IAAEhU,kBAAkB,EAAE,CAAC;MAC9CxL,IAAI,EAAE2F,gDAAK;MACXkI,IAAI,EAAE,CAAC;QAAE4D,SAAS,EAAEnM,2DAAgBA;MAAC,CAAC;IAC1C,CAAC,CAAC;IAAEke,WAAW,EAAE,CAAC;MACdxjB,IAAI,EAAE2F,gDAAK;MACXkI,IAAI,EAAE,CAAC;QAAEyY,KAAK,EAAE,kBAAkB;QAAE7U,SAAS,EAAEnM,2DAAgBA;MAAC,CAAC;IACrE,CAAC,CAAC;IAAEiE,iBAAiB,EAAE,CAAC;MACpBvJ,IAAI,EAAE2F,gDAAKA;IACf,CAAC,CAAC;IAAEoM,MAAM,EAAE,CAAC;MACT/R,IAAI,EAAEoG,0DAAe;MACrByH,IAAI,EAAE,CAACxH,yDAAU,CAAC,MAAM6gB,UAAU,CAAC,EAAE;QAAE3H,WAAW,EAAE;MAAK,CAAC;IAC9D,CAAC,CAAC;IAAE0D,eAAe,EAAE,CAAC;MAClBjjB,IAAI,EAAE2F,gDAAKA;IACf,CAAC,CAAC;IAAEoG,aAAa,EAAE,CAAC;MAChB/L,IAAI,EAAE2F,gDAAK;MACXkI,IAAI,EAAE,CAAC;QAAE4D,SAAS,EAAEnM,2DAAgBA;MAAC,CAAC;IAC1C,CAAC,CAAC;IAAExE,KAAK,EAAE,CAAC;MACRd,IAAI,EAAE2F,gDAAKA;IACf,CAAC,CAAC;IAAE+gB,QAAQ,EAAE,CAAC;MACX1mB,IAAI,EAAE2F,gDAAKA;IACf,CAAC,CAAC;IAAEgW,iBAAiB,EAAE,CAAC;MACpB3b,IAAI,EAAE6F,oDAAS;MACfgI,IAAI,EAAE,CAAC,kBAAkB,EAAE;QAAE8D,MAAM,EAAE;MAAK,CAAC;IAC/C,CAAC,CAAC;IAAEsK,QAAQ,EAAE,CAAC;MACXjc,IAAI,EAAE6F,oDAAS;MACfgI,IAAI,EAAE,CAAC,SAAS,EAAE;QAAE8D,MAAM,EAAE;MAAK,CAAC;IACtC,CAAC,CAAC;IAAEoL,aAAa,EAAE,CAAC;MAChB/c,IAAI,EAAE6F,oDAAS;MACfgI,IAAI,EAAE,CAAC,cAAc,EAAE;QAAE8D,MAAM,EAAE;MAAK,CAAC;IAC3C,CAAC,CAAC;IAAEkG,cAAc,EAAE,CAAC;MACjB7X,IAAI,EAAE6F,oDAAS;MACfgI,IAAI,EAAE,CAAC,eAAe;IAC1B,CAAC,CAAC;IAAE8J,kBAAkB,EAAE,CAAC;MACrB3X,IAAI,EAAE6F,oDAAS;MACfgI,IAAI,EAAE,CAAC,mBAAmB;IAC9B,CAAC;EAAE,CAAC;AAAA;AAChB;AACA;AACA;AACA,MAAMqZ,UAAU,SAASnU,UAAU,CAAC;EAChC;EACA,IAAI6T,MAAMA,CAAA,EAAG;IACT,OAAO,IAAI,CAACmB,SAAS;EACzB;EACA,IAAInB,MAAMA,CAACjmB,KAAK,EAAE;IACd,IAAIA,KAAK,KAAK,IAAI,CAAConB,SAAS,EAAE;MAC1B,IAAI,CAACA,SAAS,GAAGpnB,KAAK;MACtB,IAAI,CAACqnB,UAAU,CAACvB,gBAAgB,CAAC,CAAC;IACtC;EACJ;EACA;AACJ;AACA;AACA;EACI,IAAIwB,cAAcA,CAAA,EAAG;IACjB,OAAQ,IAAI,CAAC1c,QAAQ,IACjB,IAAI,CAACQ,aAAa,IAClB,IAAI,CAACic,UAAU,CAACjc,aAAa,IAC7B,CAAC,CAAC,IAAI,CAACmc,YAAY,CAAC3c,QAAQ;EACpC;EACAjO,WAAWA,CAAC0qB,UAAU,EACtB,oBAAqBxV,UAAU,EAAE2V,mBAAmB,EAAE1M,QAAQ,EAAE2M,aAAa,EAAE9J,aAAa,EAAE;IAC1F,KAAK,CAAC,CAAC;IACP,IAAI,CAAC0J,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACxV,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAAC4V,aAAa,GAAGA,aAAa;IAClC,IAAI,CAACtR,UAAU,GAAG,IAAI/P,yCAAO,CAAC,CAAC;IAC/B;IACA,IAAI,CAACghB,SAAS,GAAG,KAAK;IACtB;IACA,IAAI,CAACxc,QAAQ,GAAG,KAAK;IACrB;IACA,IAAI,CAACQ,aAAa,GAAG,KAAK;IAC1B,IAAI,CAAC0P,QAAQ,GAAG,CAAC;IACjB;IACA,IAAI,CAAChc,EAAE,GAAG,gBAAgB8mB,YAAY,EAAE,EAAE;IAC1C,IAAI,CAAC2B,YAAY,GAAGC,mBAAmB,IAAI,CAAC,CAAC;IAC7C,IAAI,CAAC1M,QAAQ,GAAG4M,QAAQ,CAAC5M,QAAQ,CAAC,IAAI,CAAC;IACvC,IAAI6C,aAAa,KAAK,gBAAgB,EAAE;MACpC,IAAI,CAAC4J,YAAY,CAAC5F,SAAS,GAAG;QAAEgG,aAAa,EAAE,CAAC;QAAEC,YAAY,EAAE;MAAE,CAAC;IACvE;IACAP,UAAU,CAACrF,mBAAmB,CACzBpL,IAAI,CAACvP,yDAAS,CAAC,IAAI,CAAC8O,UAAU,CAAC,CAAC,CAChCU,SAAS,CAAChM,kBAAkB,IAAI;MACjC,IAAI,CAACA,kBAAkB,GAAGA,kBAAkB;IAChD,CAAC,CAAC;EACN;EACA;EACA6J,KAAKA,CAAA,EAAG;IACJ,IAAI,CAAC7C,UAAU,CAACC,aAAa,CAAC4C,KAAK,CAAC,CAAC;EACzC;EACAqC,eAAeA,CAAA,EAAG;IACd,IAAI,CAAC0Q,aAAa,CAACI,OAAO,CAAC,IAAI,CAAChW,UAAU,CAAC;EAC/C;EACA/C,WAAWA,CAAA,EAAG;IACV,IAAI,CAACqH,UAAU,CAACtH,IAAI,CAAC,CAAC;IACtB,IAAI,CAACsH,UAAU,CAACpH,QAAQ,CAAC,CAAC;IAC1B,KAAK,CAACD,WAAW,CAAC,CAAC;IACnB,IAAI,CAAC2Y,aAAa,CAACK,cAAc,CAAC,IAAI,CAACjW,UAAU,CAAC;EACtD;EACAkW,YAAYA,CAAA,EAAG;IACX;IACA;IACA,IAAI,CAACV,UAAU,CAACpN,UAAU,GAAG,IAAI,CAACoN,UAAU,CAACjW,MAAM,CAACyJ,OAAO,CAAC,CAAC,CAACmN,OAAO,CAAC,IAAI,CAAC;EAC/E;EACAjO,cAAcA,CAACla,KAAK,EAAE;IAClB,IAAIA,KAAK,CAACma,OAAO,KAAK7S,yDAAK,IAAItH,KAAK,CAACma,OAAO,KAAK5S,yDAAK,EAAE;MACpD,IAAI,IAAI,CAACwD,QAAQ,EAAE;QACf/K,KAAK,CAAC+d,cAAc,CAAC,CAAC;MAC1B,CAAC,MACI,IAAI,IAAI,CAACyJ,UAAU,CAACtB,QAAQ,EAAE;QAC/B;QACA;QACA,IAAIlmB,KAAK,CAACma,OAAO,KAAK7S,yDAAK,EAAE;UACzBtH,KAAK,CAAC+d,cAAc,CAAC,CAAC;QAC1B;QACA,IAAI,CAAC/L,UAAU,CAACC,aAAa,CAACmW,KAAK,CAAC,CAAC;MACzC;IACJ;EACJ;EACAC,gBAAgBA,CAAA,EAAG;IACf,OAAO,IAAI,CAACb,UAAU,CAACtB,QAAQ,GACzB,IAAI,CAACsB,UAAU,CAACtB,QAAQ,EAAEjnB,EAAE,GAC5B,IAAI,CAAC+S,UAAU,CAACC,aAAa,CAACsU,YAAY,CAAC,eAAe,CAAC;EACrE;EACA+B,gBAAgBA,CAAA,EAAG;IACf,IAAI,IAAI,CAACd,UAAU,CAACtB,QAAQ,EAAE;MAC1B,OAAO,IAAI,CAACE,MAAM,GAAG,MAAM,GAAG,OAAO;IACzC,CAAC,MACI;MACD,OAAO,IAAI,CAACpU,UAAU,CAACC,aAAa,CAACsU,YAAY,CAAC,eAAe,CAAC;IACtE;EACJ;EACAgC,eAAeA,CAAA,EAAG;IACd,OAAO,IAAI,CAACnC,MAAM,IAAI,CAAC,IAAI,CAACoB,UAAU,CAACtB,QAAQ,GAAG,MAAM,GAAG,IAAI;EACnE;EACAI,QAAQA,CAAA,EAAG;IACP,OAAO,IAAI,CAACkB,UAAU,CAACtB,QAAQ,GAAG,KAAK,GAAG,IAAI,CAAClU,UAAU,CAACC,aAAa,CAACsU,YAAY,CAAC,MAAM,CAAC;EAChG;EACArb,YAAYA,CAAA,EAAG;IACX,IAAI,IAAI,CAACsc,UAAU,CAACtB,QAAQ,EAAE;MAC1B,OAAO,IAAI,CAACqB,SAAS,IAAI,CAAC,IAAI,CAACxc,QAAQ,GAAG,CAAC,GAAG,CAAC,CAAC;IACpD,CAAC,MACI;MACD,OAAO,IAAI,CAACA,QAAQ,GAAG,CAAC,CAAC,GAAG,IAAI,CAACkQ,QAAQ;IAC7C;EACJ;EACA;IAAS,IAAI,CAACtO,IAAI,YAAA6b,mBAAA3b,CAAA;MAAA,YAAAA,CAAA,IAAwF6Z,UAAU,EA5yDpB/qB,+DAAE,CA4yDoCqqB,SAAS,GA5yD/CrqB,+DAAE,CA4yD0DA,qDAAa,GA5yDzEA,+DAAE,CA4yDoFuK,8EAAyB,MA5yD/GvK,+DAAE,CA4yD0I,UAAU,GA5yDtJA,+DAAE,CA4yDkLyH,2DAAe,GA5yDnMzH,+DAAE,CA4yD8M8J,gEAAqB;IAAA,CAA4D;EAAE;EACnY;IAAS,IAAI,CAAC4J,IAAI,kBA7yD8E1T,+DAAE;MAAA6D,IAAA,EA6yDJknB,UAAU;MAAA/kB,SAAA;MAAAuO,SAAA;MAAA+E,QAAA;MAAAC,YAAA,WAAAyT,wBAAAvmB,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UA7yDRzG,wDAAE,mBAAAitB,oCAAA;YAAA,OA6yDJvmB,GAAA,CAAA6lB,YAAA,CAAa,CAAC;UAAA,CAAL,CAAC,qBAAAW,sCAAArmB,MAAA;YAAA,OAAVH,GAAA,CAAA6X,cAAA,CAAA1X,MAAqB,CAAC;UAAA,CAAb,CAAC;QAAA;QAAA,IAAAJ,EAAA;UA7yDRzG,yDAAE,kBA6yDJ0G,GAAA,CAAAgmB,gBAAA,CAAiB,CAAC,kBAAlBhmB,GAAA,CAAAkmB,eAAA,CAAgB,CAAC,mBAAAlmB,GAAA,CAAA0I,QAAA,mBAAjB1I,GAAA,CAAAimB,gBAAA,CAAiB,CAAC,QAAAjmB,GAAA,CAAApD,EAAA,cAAlBoD,GAAA,CAAA6I,YAAA,CAAa,CAAC,UAAd7I,GAAA,CAAAikB,QAAA,CAAS,CAAC;UA7yDR3qB,yDAAE,yBAAA0G,GAAA,CAAA0I,QA6yDK,CAAC,oBAAA1I,GAAA,CAAA+jB,MAAD,CAAC;QAAA;MAAA;MAAAjW,MAAA;QAAAiW,MAAA,GA7yDRzqB,0DAAE,CAAA0U,0BAAA,sBA6yD6GvL,2DAAgB;QAAAiG,QAAA,GA7yD/HpP,0DAAE,CAAA0U,0BAAA,0BA6yDmKvL,2DAAgB;QAAAyG,aAAA,GA7yDrL5P,0DAAE,CAAA0U,0BAAA,oCA6yDwOvL,2DAAgB;QAAAmW,QAAA,GA7yD1Ptf,0DAAE,CAAA0U,0BAAA,0BA6yD+RlQ,KAAK,IAAMA,KAAK,IAAI,IAAI,GAAG,CAAC,GAAGuF,8DAAe,CAACvF,KAAK,CAAE;QAAAlB,EAAA;MAAA;MAAAsR,QAAA;MAAA3O,UAAA;MAAAC,QAAA,GA7yDvVlG,sEAAE,EAAFA,wEAAE,EAAFA,iEAAE;MAAAkrB,KAAA,EAAAra,IAAA;MAAAkE,kBAAA,EAAArI,GAAA;MAAAtG,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAA4mB,oBAAA1mB,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFzG,6DAAE;UAAFA,uDAAE,aA6yDi8B,CAAC,YAAyJ,CAAC;UA7yD9lCA,4DAAE,aA6yDgoC,CAAC,aAAuC,CAAC;UA7yD3qCA,0DAAE,EA6yDusC,CAAC;UA7yD1sCA,0DAAE,CA6yDktC,CAAC,CAAQ,CAAC;QAAA;QAAA,IAAAyG,EAAA;UA7yD9tCzG,uDAAE,CA6yD0iC,CAAC;UA7yD7iCA,wDAAE,qBAAA0G,GAAA,CAAA2P,UAAA,CAAAC,aA6yD0iC,CAAC,sBAAA5P,GAAA,CAAAolB,cAAyC,CAAC;QAAA;MAAA;MAAA3I,YAAA,GAA4pI7Y,8DAAS;MAAAxB,MAAA;MAAAoM,aAAA;MAAAC,eAAA;IAAA,EAA6T;EAAE;AAC/pL;AACA;EAAA,QAAA3D,SAAA,oBAAAA,SAAA,KA/yDoGxR,+DAAE,CA+yDX+qB,UAAU,EAAc,CAAC;IACxGlnB,IAAI,EAAEwF,oDAAS;IACfqI,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,8BAA8B;MAAEiD,QAAQ,EAAE,YAAY;MAAEO,eAAe,EAAE7L,kEAAuB,CAAC8jB,MAAM;MAAElY,aAAa,EAAE3L,4DAAiB,CAACoL,IAAI;MAAEU,IAAI,EAAE;QAC7J,OAAO,EAAE,kDAAkD;QAC3D,sBAAsB,EAAE,oBAAoB;QAC5C,qBAAqB,EAAE,mBAAmB;QAC1C,sBAAsB,EAAE,UAAU;QAClC,sBAAsB,EAAE,oBAAoB;QAC5C,WAAW,EAAE,IAAI;QACjB,iBAAiB,EAAE,gBAAgB;QACnC,aAAa,EAAE,YAAY;QAC3B,8BAA8B,EAAE,UAAU;QAC1C,yBAAyB,EAAE,QAAQ;QACnC,SAAS,EAAE,gBAAgB;QAC3B,WAAW,EAAE;MACjB,CAAC;MAAEpP,UAAU,EAAE,IAAI;MAAErH,OAAO,EAAE,CAAC0L,8DAAS,CAAC;MAAE/D,QAAQ,EAAE,uUAAuU;MAAEuC,MAAM,EAAE,CAAC,w9HAAw9H;IAAE,CAAC;EAC92I,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEjF,IAAI,EAAEwmB;EAAU,CAAC,EAAE;IAAExmB,IAAI,EAAE7D,qDAAa4J;EAAC,CAAC,EAAE;IAAE/F,IAAI,EAAEyO,SAAS;IAAEC,UAAU,EAAE,CAAC;MAC7F1O,IAAI,EAAEqF,mDAAQA;IAClB,CAAC,EAAE;MACCrF,IAAI,EAAEoF,iDAAM;MACZyI,IAAI,EAAE,CAACnH,8EAAyB;IACpC,CAAC;EAAE,CAAC,EAAE;IAAE1G,IAAI,EAAEyO,SAAS;IAAEC,UAAU,EAAE,CAAC;MAClC1O,IAAI,EAAEuG,oDAAS;MACfsH,IAAI,EAAE,CAAC,UAAU;IACrB,CAAC;EAAE,CAAC,EAAE;IAAE7N,IAAI,EAAE4D,2DAAeslB;EAAC,CAAC,EAAE;IAAElpB,IAAI,EAAEyO,SAAS;IAAEC,UAAU,EAAE,CAAC;MAC7D1O,IAAI,EAAEqF,mDAAQA;IAClB,CAAC,EAAE;MACCrF,IAAI,EAAEoF,iDAAM;MACZyI,IAAI,EAAE,CAAC5H,gEAAqB;IAChC,CAAC;EAAE,CAAC,CAAC,EAAkB;IAAE2gB,MAAM,EAAE,CAAC;MAClC5mB,IAAI,EAAE2F,gDAAK;MACXkI,IAAI,EAAE,CAAC;QAAE4D,SAAS,EAAEnM,2DAAgBA;MAAC,CAAC;IAC1C,CAAC,CAAC;IAAEiG,QAAQ,EAAE,CAAC;MACXvL,IAAI,EAAE2F,gDAAK;MACXkI,IAAI,EAAE,CAAC;QAAE4D,SAAS,EAAEnM,2DAAgBA;MAAC,CAAC;IAC1C,CAAC,CAAC;IAAEyG,aAAa,EAAE,CAAC;MAChB/L,IAAI,EAAE2F,gDAAK;MACXkI,IAAI,EAAE,CAAC;QAAE4D,SAAS,EAAEnM,2DAAgBA;MAAC,CAAC;IAC1C,CAAC,CAAC;IAAEmW,QAAQ,EAAE,CAAC;MACXzb,IAAI,EAAE2F,gDAAK;MACXkI,IAAI,EAAE,CAAC;QACC4D,SAAS,EAAG9Q,KAAK,IAAMA,KAAK,IAAI,IAAI,GAAG,CAAC,GAAGuF,8DAAe,CAACvF,KAAK;MACpE,CAAC;IACT,CAAC,CAAC;IAAElB,EAAE,EAAE,CAAC;MACLO,IAAI,EAAE2F,gDAAKA;IACf,CAAC;EAAE,CAAC;AAAA;AAChB;AACA;AACA;AACA,MAAM6jB,cAAc,CAAC;EACjBlsB,WAAWA,CAAA,EAAG;IACV;IACA,IAAI,CAACmC,EAAE,GAAG,qBAAqB8mB,YAAY,EAAE,EAAE;EACnD;EACA;IAAS,IAAI,CAACpZ,IAAI,YAAAsc,uBAAApc,CAAA;MAAA,YAAAA,CAAA,IAAwFmc,cAAc;IAAA,CAAmD;EAAE;EAC7K;IAAS,IAAI,CAAC3Z,IAAI,kBAt2D8E1T,+DAAE;MAAA6D,IAAA,EAs2DJwpB,cAAc;MAAArnB,SAAA;MAAAuO,SAAA,WAAyG,UAAU;MAAA+E,QAAA;MAAAC,YAAA,WAAAgU,4BAAA9mB,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAt2D/HzG,yDAAE,oBAAA0G,GAAA,CAAAgkB,YAAA,QAAAhkB,GAAA,CAAApD,EAAA;QAAA;MAAA;MAAAkR,MAAA;QAAAlR,EAAA;MAAA;MAAAsR,QAAA;MAAA3O,UAAA;MAAAC,QAAA,GAAFlG,iEAAE;MAAA+U,kBAAA,EAAArI,GAAA;MAAAtG,KAAA;MAAAC,IAAA;MAAAE,QAAA,WAAAinB,wBAAA/mB,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFzG,6DAAE;UAAFA,0DAAE,EAs2DoU,CAAC;QAAA;MAAA;MAAAkV,aAAA;MAAAC,eAAA;IAAA,EAAkH;EAAE;AAC/hB;AACA;EAAA,QAAA3D,SAAA,oBAAAA,SAAA,KAx2DoGxR,+DAAE,CAw2DXqtB,cAAc,EAAc,CAAC;IAC5GxpB,IAAI,EAAEwF,oDAAS;IACfqI,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,mBAAmB;MAC7BiD,QAAQ,EAAE,gBAAgB;MAC1BrO,QAAQ,EAAE,2BAA2B;MACrC8O,IAAI,EAAE;QACF,wBAAwB,EAAE,cAAc;QACxC,WAAW,EAAE,IAAI;QACjB,OAAO,EAAE,uBAAuB;QAChC,MAAM,EAAE;MACZ,CAAC;MACDH,aAAa,EAAE3L,4DAAiB,CAACoL,IAAI;MACrCQ,eAAe,EAAE7L,kEAAuB,CAAC8jB,MAAM;MAC/CnnB,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,QAAkB;IAAE3C,EAAE,EAAE,CAAC;MACnBO,IAAI,EAAE2F,gDAAKA;IACf,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAM1J,aAAa,CAAC;EAChB;IAAS,IAAI,CAACkR,IAAI,YAAAyc,sBAAAvc,CAAA;MAAA,YAAAA,CAAA,IAAwFpR,aAAa;IAAA,CAAkD;EAAE;EAC3K;IAAS,IAAI,CAAC4tB,IAAI,kBA93D8E1tB,8DAAE;MAAA6D,IAAA,EA83DS/D;IAAa,EAclG;EAAE;EACxB;IAAS,IAAI,CAAC8tB,IAAI,kBA74D8E5tB,8DAAE;MAAApB,OAAA,GA64DkC4L,oEAAe,EAAEA,oEAAe;IAAA,EAAI;EAAE;AAC9K;AACA;EAAA,QAAAgH,SAAA,oBAAAA,SAAA,KA/4DoGxR,+DAAE,CA+4DXF,aAAa,EAAc,CAAC;IAC3G+D,IAAI,EAAEwG,mDAAQ;IACdqH,IAAI,EAAE,CAAC;MACC9S,OAAO,EAAE,CACL4L,oEAAe,EACfuG,aAAa,EACbgB,WAAW,EACXU,MAAM,EACN8T,WAAW,EACX8D,SAAS,EACTgD,cAAc,EACdtC,UAAU,CACb;MACDjsB,OAAO,EAAE,CACL0L,oEAAe,EACfuG,aAAa,EACbgB,WAAW,EACXU,MAAM,EACN8T,WAAW,EACX8D,SAAS,EACTgD,cAAc,EACdtC,UAAU;IAElB,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA", "sources": ["./src/app/secretary-calendar/secretary-calendar-routing.module.ts", "./src/app/secretary-calendar/secretary-calendar.module.ts", "./src/app/secretary-calendar/secretary-calendar.page.ts", "./src/app/secretary-calendar/secretary-calendar.page.html", "./node_modules/@angular/material/fesm2022/tabs.mjs"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { Routes, RouterModule } from '@angular/router';\r\n\r\nimport { SecretaryCalendarPage } from './secretary-calendar.page';\r\n\r\nconst routes: Routes = [\r\n  {\r\n    path: '',\r\n    component: SecretaryCalendarPage\r\n  }\r\n];\r\n\r\n@NgModule({\r\n  imports: [RouterModule.forChild(routes)],\r\n  exports: [RouterModule],\r\n})\r\nexport class SecretaryCalendarPageRoutingModule {}\r\n", "import { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\r\nimport { IonicModule } from '@ionic/angular';\r\n\r\n// Angular Material imports\r\nimport { MatButtonModule } from '@angular/material/button';\r\nimport { MatIconModule } from '@angular/material/icon';\r\nimport { MatCardModule } from '@angular/material/card';\r\nimport { MatSelectModule } from '@angular/material/select';\r\nimport { MatFormFieldModule } from '@angular/material/form-field';\r\nimport { MatInputModule } from '@angular/material/input';\r\nimport { MatDatepickerModule } from '@angular/material/datepicker';\r\nimport { MatNativeDateModule } from '@angular/material/core';\r\nimport { MatDialogModule } from '@angular/material/dialog';\r\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\r\nimport { MatChipsModule } from '@angular/material/chips';\r\nimport { MatTabsModule } from '@angular/material/tabs';\r\n\r\nimport { SecretaryCalendarPageRoutingModule } from './secretary-calendar-routing.module';\r\nimport { SecretaryCalendarPage } from './secretary-calendar.page';\r\n\r\n// Note: Custom components will be added later when properly configured\r\n\r\n@NgModule({\r\n  imports: [\r\n    CommonModule,\r\n    FormsModule,\r\n    ReactiveFormsModule,\r\n    IonicModule,\r\n    SecretaryCalendarPageRoutingModule,\r\n\r\n    // Angular Material modules\r\n    MatButtonModule,\r\n    MatIconModule,\r\n    MatCardModule,\r\n    MatSelectModule,\r\n    MatFormFieldModule,\r\n    MatInputModule,\r\n    MatDatepickerModule,\r\n    MatNativeDateModule,\r\n    MatDialogModule,\r\n    MatProgressSpinnerModule,\r\n    MatChipsModule,\r\n    MatTabsModule,\r\n\r\n    // Standalone component\r\n    SecretaryCalendarPage\r\n  ]\r\n})\r\nexport class SecretaryCalendarPageModule {}\r\n", "import { Component, OnInit } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { FormsModule } from '@angular/forms';\r\nimport { IonicModule } from '@ionic/angular';\r\nimport { RouterModule } from '@angular/router';\r\nimport { FirebaseService, LawyerProfile } from '../services/firebase.service';\r\nimport { EnhancedAppointment } from '../models/scheduling.models';\r\nimport { ToastController } from '@ionic/angular';\r\n\r\n@Component({\r\n  selector: 'app-secretary-calendar',\r\n  templateUrl: './secretary-calendar.page.html',\r\n  styleUrls: ['./secretary-calendar.page.scss'],\r\n  standalone: true,\r\n  imports: [\r\n    CommonModule,\r\n    FormsModule,\r\n    IonicModule,\r\n    RouterModule\r\n  ]\r\n})\r\nexport class SecretaryCalendarPage implements OnInit {\r\n  selectedDate: string = new Date().toISOString().split('T')[0];\r\n  linkedLawyers: LawyerProfile[] = [];\r\n  selectedLawyer: LawyerProfile | null = null;\r\n  appointments: EnhancedAppointment[] = [];\r\n\r\n  // Tab management\r\n  selectedTab: 'calendar' | 'appointments' | 'availability' = 'calendar';\r\n\r\n  // Loading states\r\n  isLoading = false;\r\n\r\n  constructor(\r\n    private firebaseService: FirebaseService,\r\n    private toastController: ToastController\r\n  ) { }\r\n\r\n  ngOnInit() {\r\n    console.log('SecretaryCalendarPage ngOnInit called');\r\n    this.testFirebaseConnection();\r\n    this.loadLinkedLawyers();\r\n    this.loadAppointments();\r\n  }\r\n\r\n  async testFirebaseConnection() {\r\n    console.log('Testing Firebase connection...');\r\n    const currentUser = this.firebaseService.getCurrentUser();\r\n    console.log('Current user:', currentUser);\r\n\r\n    if (currentUser) {\r\n      try {\r\n        const profile = await this.firebaseService.getSecretaryProfile(currentUser.uid);\r\n        console.log('Secretary profile:', profile);\r\n      } catch (error) {\r\n        console.error('Error getting secretary profile:', error);\r\n      }\r\n    }\r\n  }\r\n\r\n  async loadLinkedLawyers() {\r\n    console.log('Loading linked lawyers...');\r\n    const currentUser = this.firebaseService.getCurrentUser();\r\n    console.log('Current user for linked lawyers:', currentUser);\r\n\r\n    if (currentUser) {\r\n      try {\r\n        this.linkedLawyers = await this.firebaseService.getSecretaryLinkedLawyers(currentUser.uid);\r\n        console.log('Linked lawyers loaded:', this.linkedLawyers);\r\n      } catch (error) {\r\n        console.error('Error loading linked lawyers:', error);\r\n        // Add mock data for testing\r\n        this.linkedLawyers = [\r\n          {\r\n            uid: 'lawyer1',\r\n            email: '<EMAIL>',\r\n            name: 'John Smith',\r\n            rollNumber: 'BAR123456',\r\n            barId: 'BAR123456',\r\n            phone: '+1234567890',\r\n            role: 'lawyer',\r\n            createdAt: new Date(),\r\n            updatedAt: new Date()\r\n          },\r\n          {\r\n            uid: 'lawyer2',\r\n            email: '<EMAIL>',\r\n            name: 'Jane Doe',\r\n            rollNumber: 'BAR789012',\r\n            barId: 'BAR789012',\r\n            phone: '+0987654321',\r\n            role: 'lawyer',\r\n            createdAt: new Date(),\r\n            updatedAt: new Date()\r\n          }\r\n        ];\r\n        console.log('Using mock linked lawyers:', this.linkedLawyers);\r\n      }\r\n    }\r\n  }\r\n\r\n  async loadAppointments() {\r\n    console.log('Loading appointments...');\r\n    const currentUser = this.firebaseService.getCurrentUser();\r\n    if (!currentUser) {\r\n      console.log('No current user found');\r\n      return;\r\n    }\r\n\r\n    this.isLoading = true;\r\n    try {\r\n      this.appointments = await this.firebaseService.getAppointmentsForSecretary(currentUser.uid);\r\n      console.log('Appointments loaded:', this.appointments);\r\n    } catch (error) {\r\n      console.error('Error loading appointments:', error);\r\n      // Add mock data for testing\r\n      this.appointments = [\r\n        {\r\n          id: 'apt1',\r\n          lawyerId: 'lawyer1',\r\n          lawyerName: 'John Smith',\r\n          clientName: 'Alice Johnson',\r\n          date: new Date().toISOString().split('T')[0],\r\n          time: '09:00',\r\n          status: 'pending',\r\n          type: 'Consultation',\r\n          createdBy: 'client',\r\n          createdAt: new Date(),\r\n          updatedAt: new Date()\r\n        },\r\n        {\r\n          id: 'apt2',\r\n          lawyerId: 'lawyer2',\r\n          lawyerName: 'Jane Doe',\r\n          clientName: 'Bob Wilson',\r\n          date: new Date().toISOString().split('T')[0],\r\n          time: '14:00',\r\n          status: 'confirmed',\r\n          type: 'Case Review',\r\n          createdBy: 'client',\r\n          createdAt: new Date(),\r\n          updatedAt: new Date()\r\n        }\r\n      ];\r\n      console.log('Using mock appointments:', this.appointments);\r\n      this.showToast('Using mock data for testing', 'warning');\r\n    } finally {\r\n      this.isLoading = false;\r\n    }\r\n  }\r\n\r\n  onLawyerSelected(lawyer: LawyerProfile) {\r\n    this.selectedLawyer = lawyer;\r\n  }\r\n\r\n  onDateSelected(date: string) {\r\n    this.selectedDate = date;\r\n  }\r\n\r\n  onAvailabilityChanged() {\r\n    // Refresh data when availability changes\r\n    this.loadAppointments();\r\n  }\r\n\r\n  refreshData() {\r\n    console.log('Refreshing data...');\r\n    this.loadLinkedLawyers();\r\n    this.loadAppointments();\r\n  }\r\n\r\n  onTabChange(event: any) {\r\n    console.log('Tab change event:', event);\r\n    const tab = event.detail.value;\r\n    console.log('Selected tab:', tab);\r\n    if (tab === 'calendar' || tab === 'appointments' || tab === 'availability') {\r\n      this.selectedTab = tab;\r\n      console.log('Tab changed to:', this.selectedTab);\r\n    }\r\n  }\r\n\r\n  setSelectedTab(tab: 'calendar' | 'appointments' | 'availability') {\r\n    this.selectedTab = tab;\r\n  }\r\n\r\n  async showToast(message: string, color: string) {\r\n    const toast = await this.toastController.create({\r\n      message,\r\n      duration: 3000,\r\n      color,\r\n      position: 'top'\r\n    });\r\n    await toast.present();\r\n  }\r\n\r\n  getConfirmedCount(): number {\r\n    return this.appointments.filter(a => a.status === 'confirmed').length;\r\n  }\r\n\r\n  getTotalAppointments(): number {\r\n    return this.appointments.length;\r\n  }\r\n\r\n  getPendingCount(): number {\r\n    return this.appointments.filter(a => a.status === 'pending').length;\r\n  }\r\n\r\n  getUpcomingCount(): number {\r\n    const now = new Date();\r\n    return this.appointments.filter(apt => {\r\n      const appointmentDateTime = new Date(apt.date + ' ' + apt.time);\r\n      return appointmentDateTime > now;\r\n    }).length;\r\n  }\r\n\r\n  getStatusColor(status: string): string {\r\n    switch (status) {\r\n      case 'confirmed': return 'success';\r\n      case 'completed': return 'medium';\r\n      case 'cancelled': return 'danger';\r\n      default: return 'warning';\r\n    }\r\n  }\r\n}\r\n", "<ion-header>\r\n  <ion-toolbar class=\"veritus-toolbar\">\r\n    <ion-title class=\"veritus-text-white veritus-font-semibold\">Scheduling Management</ion-title>\r\n  </ion-toolbar>\r\n</ion-header>\r\n\r\n<ion-content class=\"calendar-content veritus-gradient-bg\">\r\n  <div class=\"calendar-container veritus-safe-area-top\">\r\n\r\n    <!-- Tab Navigation -->\r\n    <div class=\"tab-navigation\">\r\n      <ion-segment [(ngModel)]=\"selectedTab\" (ionChange)=\"onTabChange($event)\">\r\n        <ion-segment-button value=\"calendar\">\r\n          <ion-icon name=\"calendar-outline\"></ion-icon>\r\n          <ion-label>Calendar</ion-label>\r\n        </ion-segment-button>\r\n        <ion-segment-button value=\"appointments\">\r\n          <ion-icon name=\"list-outline\"></ion-icon>\r\n          <ion-label>Appointments</ion-label>\r\n        </ion-segment-button>\r\n        <ion-segment-button value=\"availability\">\r\n          <ion-icon name=\"time-outline\"></ion-icon>\r\n          <ion-label>Availability</ion-label>\r\n        </ion-segment-button>\r\n      </ion-segment>\r\n    </div>\r\n\r\n    <!-- Debug Info -->\r\n    <div class=\"debug-info\" style=\"background: rgba(255,255,255,0.1); padding: 10px; margin-bottom: 20px; border-radius: 8px;\">\r\n      <p style=\"margin: 0; color: white; font-size: 0.9rem;\">\r\n        Current Tab: <strong>{{ selectedTab }}</strong> |\r\n        Linked Lawyers: <strong>{{ linkedLawyers.length }}</strong> |\r\n        Appointments: <strong>{{ appointments.length }}</strong>\r\n      </p>\r\n    </div>\r\n\r\n    <!-- Calendar Tab Content -->\r\n    <div *ngIf=\"selectedTab === 'calendar'\" class=\"tab-content\">\r\n      <h3 style=\"color: white; margin-bottom: 16px;\">📅 Multi-Lawyer Calendar</h3>\r\n      <div class=\"placeholder-content\">\r\n        <ion-card>\r\n          <ion-card-header>\r\n            <ion-card-title>Calendar View</ion-card-title>\r\n            <ion-card-subtitle>Multi-lawyer calendar management</ion-card-subtitle>\r\n          </ion-card-header>\r\n          <ion-card-content>\r\n            <p>Calendar functionality will be available here.</p>\r\n            <p>Linked Lawyers: {{ linkedLawyers.length }}</p>\r\n            <ion-button routerLink=\"/calendar\" fill=\"outline\">\r\n              <ion-icon name=\"calendar-outline\" slot=\"start\"></ion-icon>\r\n              Go to Calendar\r\n            </ion-button>\r\n          </ion-card-content>\r\n        </ion-card>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Appointments Tab Content -->\r\n    <div *ngIf=\"selectedTab === 'appointments'\" class=\"tab-content\">\r\n      <h3 style=\"color: white; margin-bottom: 16px;\">📋 Appointment Management</h3>\r\n      <div class=\"placeholder-content\">\r\n        <ion-card>\r\n          <ion-card-header>\r\n            <ion-card-title>Appointment Management</ion-card-title>\r\n            <ion-card-subtitle>Manage appointments for linked lawyers</ion-card-subtitle>\r\n          </ion-card-header>\r\n          <ion-card-content>\r\n            <p>Appointment management functionality will be available here.</p>\r\n            <p>Total Appointments: {{ appointments.length }}</p>\r\n            <ion-button routerLink=\"/calendar\" fill=\"outline\">\r\n              <ion-icon name=\"calendar-outline\" slot=\"start\"></ion-icon>\r\n              Manage Appointments\r\n            </ion-button>\r\n          </ion-card-content>\r\n        </ion-card>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Availability Tab Content -->\r\n    <div *ngIf=\"selectedTab === 'availability'\" class=\"tab-content\">\r\n      <h3 style=\"color: white; margin-bottom: 16px;\">⏰ Lawyer Availability</h3>\r\n      <div class=\"placeholder-content\">\r\n        <ion-card>\r\n          <ion-card-header>\r\n            <ion-card-title>Lawyer Availability</ion-card-title>\r\n            <ion-card-subtitle>Manage lawyer schedules and availability</ion-card-subtitle>\r\n          </ion-card-header>\r\n          <ion-card-content>\r\n            <p>Availability management functionality will be available here.</p>\r\n            <p>Selected Date: {{ selectedDate }}</p>\r\n            <ion-button fill=\"outline\" (click)=\"refreshData()\">\r\n              <ion-icon name=\"refresh-outline\" slot=\"start\"></ion-icon>\r\n              Refresh Data\r\n            </ion-button>\r\n          </ion-card-content>\r\n        </ion-card>\r\n      </div>\r\n    </div>\r\n    <!-- Quick Stats -->\r\n    <div class=\"stats-section\">\r\n      <div class=\"stat-card\">\r\n        <div class=\"stat-number veritus-text-xl veritus-font-bold veritus-text-white\">\r\n          {{ getTotalAppointments() }}\r\n        </div>\r\n        <div class=\"stat-label veritus-text-sm veritus-text-gray\">Total Appointments</div>\r\n      </div>\r\n\r\n      <div class=\"stat-card\">\r\n        <div class=\"stat-number veritus-text-xl veritus-font-bold veritus-text-white\">\r\n          {{ linkedLawyers.length }}\r\n        </div>\r\n        <div class=\"stat-label veritus-text-sm veritus-text-gray\">Linked Lawyers</div>\r\n      </div>\r\n\r\n      <div class=\"stat-card\">\r\n        <div class=\"stat-number veritus-text-xl veritus-font-bold veritus-text-white\">\r\n          {{ getConfirmedCount() }}\r\n        </div>\r\n        <div class=\"stat-label veritus-text-sm veritus-text-gray\">Confirmed</div>\r\n      </div>\r\n\r\n      <div class=\"stat-card\">\r\n        <div class=\"stat-number veritus-text-xl veritus-font-bold veritus-text-white\">\r\n          {{ getPendingCount() }}\r\n        </div>\r\n        <div class=\"stat-label veritus-text-sm veritus-text-gray\">Pending</div>\r\n      </div>\r\n    </div>\r\n\r\n  </div>\r\n</ion-content>\r\n", "import * as i0 from '@angular/core';\nimport { InjectionToken, Directive, Inject, Optional, booleanAttribute, TemplateRef, Component, ChangeDetectionStrategy, ViewEncapsulation, Input, ContentChild, ViewChild, inject, ElementRef, EventEmitter, ANIMATION_MODULE_TYPE, numberAttribute, Output, ContentChildren, forwardRef, QueryList, Attribute, NgModule } from '@angular/core';\nimport { MatRipple, MAT_RIPPLE_GLOBAL_OPTIONS, MatCommonModule } from '@angular/material/core';\nimport { CdkPortal, TemplatePortal, CdkPortalOutlet } from '@angular/cdk/portal';\nimport { Subject, fromEvent, of, merge, EMPTY, Observable, timer, Subscription, BehaviorSubject } from 'rxjs';\nimport * as i1 from '@angular/cdk/scrolling';\nimport { CdkScrollable } from '@angular/cdk/scrolling';\nimport * as i3 from '@angular/cdk/platform';\nimport { normalizePassiveListenerOptions, Platform } from '@angular/cdk/platform';\nimport * as i2 from '@angular/cdk/bidi';\nimport * as i4 from '@angular/cdk/a11y';\nimport { FocusKeyManager, CdkMonitorFocus } from '@angular/cdk/a11y';\nimport { hasModifierKey, SPACE, ENTER } from '@angular/cdk/keycodes';\nimport { takeUntil, take, startWith, switchMap, skip, filter, distinctUntilChanged } from 'rxjs/operators';\nimport { CdkObserveContent } from '@angular/cdk/observers';\nimport { DOCUMENT } from '@angular/common';\nimport { trigger, state, style, transition, animate } from '@angular/animations';\n\n/**\n * Injection token that can be used to reference instances of `MatTabContent`. It serves as\n * alternative token to the actual `MatTabContent` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nconst MAT_TAB_CONTENT = new InjectionToken('MatTabContent');\n/** Decorates the `ng-template` tags and reads out the template from it. */\nclass MatTabContent {\n    constructor(/** Content for the tab. */ template) {\n        this.template = template;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatTabContent, deps: [{ token: i0.TemplateRef }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"17.2.0\", type: MatTabContent, isStandalone: true, selector: \"[matTabContent]\", providers: [{ provide: MAT_TAB_CONTENT, useExisting: MatTabContent }], ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatTabContent, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[matTabContent]',\n                    providers: [{ provide: MAT_TAB_CONTENT, useExisting: MatTabContent }],\n                    standalone: true,\n                }]\n        }], ctorParameters: () => [{ type: i0.TemplateRef }] });\n\n/**\n * Injection token that can be used to reference instances of `MatTabLabel`. It serves as\n * alternative token to the actual `MatTabLabel` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nconst MAT_TAB_LABEL = new InjectionToken('MatTabLabel');\n/**\n * Used to provide a tab label to a tab without causing a circular dependency.\n * @docs-private\n */\nconst MAT_TAB = new InjectionToken('MAT_TAB');\n/** Used to flag tab labels for use with the portal directive */\nclass MatTabLabel extends CdkPortal {\n    constructor(templateRef, viewContainerRef, _closestTab) {\n        super(templateRef, viewContainerRef);\n        this._closestTab = _closestTab;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatTabLabel, deps: [{ token: i0.TemplateRef }, { token: i0.ViewContainerRef }, { token: MAT_TAB, optional: true }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"17.2.0\", type: MatTabLabel, isStandalone: true, selector: \"[mat-tab-label], [matTabLabel]\", providers: [{ provide: MAT_TAB_LABEL, useExisting: MatTabLabel }], usesInheritance: true, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatTabLabel, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[mat-tab-label], [matTabLabel]',\n                    providers: [{ provide: MAT_TAB_LABEL, useExisting: MatTabLabel }],\n                    standalone: true,\n                }]\n        }], ctorParameters: () => [{ type: i0.TemplateRef }, { type: i0.ViewContainerRef }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [MAT_TAB]\n                }, {\n                    type: Optional\n                }] }] });\n\n/**\n * Used to provide a tab group to a tab without causing a circular dependency.\n * @docs-private\n */\nconst MAT_TAB_GROUP = new InjectionToken('MAT_TAB_GROUP');\nclass MatTab {\n    /** Content for the tab label given by `<ng-template mat-tab-label>`. */\n    get templateLabel() {\n        return this._templateLabel;\n    }\n    set templateLabel(value) {\n        this._setTemplateLabelInput(value);\n    }\n    /** @docs-private */\n    get content() {\n        return this._contentPortal;\n    }\n    constructor(_viewContainerRef, _closestTabGroup) {\n        this._viewContainerRef = _viewContainerRef;\n        this._closestTabGroup = _closestTabGroup;\n        /** whether the tab is disabled. */\n        this.disabled = false;\n        /**\n         * Template provided in the tab content that will be used if present, used to enable lazy-loading\n         */\n        this._explicitContent = undefined;\n        /** Plain text label for the tab, used when there is no template label. */\n        this.textLabel = '';\n        /** Portal that will be the hosted content of the tab */\n        this._contentPortal = null;\n        /** Emits whenever the internal state of the tab changes. */\n        this._stateChanges = new Subject();\n        /**\n         * The relatively indexed position where 0 represents the center, negative is left, and positive\n         * represents the right.\n         */\n        this.position = null;\n        /**\n         * The initial relatively index origin of the tab if it was created and selected after there\n         * was already a selected tab. Provides context of what position the tab should originate from.\n         */\n        this.origin = null;\n        /**\n         * Whether the tab is currently active.\n         */\n        this.isActive = false;\n    }\n    ngOnChanges(changes) {\n        if (changes.hasOwnProperty('textLabel') || changes.hasOwnProperty('disabled')) {\n            this._stateChanges.next();\n        }\n    }\n    ngOnDestroy() {\n        this._stateChanges.complete();\n    }\n    ngOnInit() {\n        this._contentPortal = new TemplatePortal(this._explicitContent || this._implicitContent, this._viewContainerRef);\n    }\n    /**\n     * This has been extracted to a util because of TS 4 and VE.\n     * View Engine doesn't support property rename inheritance.\n     * TS 4.0 doesn't allow properties to override accessors or vice-versa.\n     * @docs-private\n     */\n    _setTemplateLabelInput(value) {\n        // Only update the label if the query managed to find one. This works around an issue where a\n        // user may have manually set `templateLabel` during creation mode, which would then get\n        // clobbered by `undefined` when the query resolves. Also note that we check that the closest\n        // tab matches the current one so that we don't pick up labels from nested tabs.\n        if (value && value._closestTab === this) {\n            this._templateLabel = value;\n        }\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatTab, deps: [{ token: i0.ViewContainerRef }, { token: MAT_TAB_GROUP, optional: true }], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"16.1.0\", version: \"17.2.0\", type: MatTab, isStandalone: true, selector: \"mat-tab\", inputs: { disabled: [\"disabled\", \"disabled\", booleanAttribute], textLabel: [\"label\", \"textLabel\"], ariaLabel: [\"aria-label\", \"ariaLabel\"], ariaLabelledby: [\"aria-labelledby\", \"ariaLabelledby\"], labelClass: \"labelClass\", bodyClass: \"bodyClass\" }, host: { attributes: { \"hidden\": \"\" } }, providers: [{ provide: MAT_TAB, useExisting: MatTab }], queries: [{ propertyName: \"templateLabel\", first: true, predicate: MatTabLabel, descendants: true }, { propertyName: \"_explicitContent\", first: true, predicate: MatTabContent, descendants: true, read: TemplateRef, static: true }], viewQueries: [{ propertyName: \"_implicitContent\", first: true, predicate: TemplateRef, descendants: true, static: true }], exportAs: [\"matTab\"], usesOnChanges: true, ngImport: i0, template: \"<!-- Create a template for the content of the <mat-tab> so that we can grab a reference to this\\n    TemplateRef and use it in a Portal to render the tab content in the appropriate place in the\\n    tab-group. -->\\n<ng-template><ng-content></ng-content></ng-template>\\n\", changeDetection: i0.ChangeDetectionStrategy.Default, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatTab, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-tab', changeDetection: ChangeDetectionStrategy.Default, encapsulation: ViewEncapsulation.None, exportAs: 'matTab', providers: [{ provide: MAT_TAB, useExisting: MatTab }], standalone: true, host: {\n                        // This element will be rendered on the server in order to support hydration.\n                        // Hide it so it doesn't cause a layout shift when it's removed on the client.\n                        'hidden': '',\n                    }, template: \"<!-- Create a template for the content of the <mat-tab> so that we can grab a reference to this\\n    TemplateRef and use it in a Portal to render the tab content in the appropriate place in the\\n    tab-group. -->\\n<ng-template><ng-content></ng-content></ng-template>\\n\" }]\n        }], ctorParameters: () => [{ type: i0.ViewContainerRef }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [MAT_TAB_GROUP]\n                }, {\n                    type: Optional\n                }] }], propDecorators: { disabled: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], templateLabel: [{\n                type: ContentChild,\n                args: [MatTabLabel]\n            }], _explicitContent: [{\n                type: ContentChild,\n                args: [MatTabContent, { read: TemplateRef, static: true }]\n            }], _implicitContent: [{\n                type: ViewChild,\n                args: [TemplateRef, { static: true }]\n            }], textLabel: [{\n                type: Input,\n                args: ['label']\n            }], ariaLabel: [{\n                type: Input,\n                args: ['aria-label']\n            }], ariaLabelledby: [{\n                type: Input,\n                args: ['aria-labelledby']\n            }], labelClass: [{\n                type: Input\n            }], bodyClass: [{\n                type: Input\n            }] } });\n\n/** Class that is applied when a tab indicator is active. */\nconst ACTIVE_CLASS = 'mdc-tab-indicator--active';\n/** Class that is applied when the tab indicator should not transition. */\nconst NO_TRANSITION_CLASS = 'mdc-tab-indicator--no-transition';\n/**\n * Abstraction around the MDC tab indicator that acts as the tab header's ink bar.\n * @docs-private\n */\nclass MatInkBar {\n    constructor(_items) {\n        this._items = _items;\n    }\n    /** Hides the ink bar. */\n    hide() {\n        this._items.forEach(item => item.deactivateInkBar());\n    }\n    /** Aligns the ink bar to a DOM node. */\n    alignToElement(element) {\n        const correspondingItem = this._items.find(item => item.elementRef.nativeElement === element);\n        const currentItem = this._currentItem;\n        if (correspondingItem === currentItem) {\n            return;\n        }\n        currentItem?.deactivateInkBar();\n        if (correspondingItem) {\n            const domRect = currentItem?.elementRef.nativeElement.getBoundingClientRect?.();\n            // The ink bar won't animate unless we give it the `DOMRect` of the previous item.\n            correspondingItem.activateInkBar(domRect);\n            this._currentItem = correspondingItem;\n        }\n    }\n}\nclass InkBarItem {\n    constructor() {\n        this._elementRef = inject(ElementRef);\n        this._fitToContent = false;\n    }\n    /** Whether the ink bar should fit to the entire tab or just its content. */\n    get fitInkBarToContent() {\n        return this._fitToContent;\n    }\n    set fitInkBarToContent(newValue) {\n        if (this._fitToContent !== newValue) {\n            this._fitToContent = newValue;\n            if (this._inkBarElement) {\n                this._appendInkBarElement();\n            }\n        }\n    }\n    /** Aligns the ink bar to the current item. */\n    activateInkBar(previousIndicatorClientRect) {\n        const element = this._elementRef.nativeElement;\n        // Early exit if no indicator is present to handle cases where an indicator\n        // may be activated without a prior indicator state\n        if (!previousIndicatorClientRect ||\n            !element.getBoundingClientRect ||\n            !this._inkBarContentElement) {\n            element.classList.add(ACTIVE_CLASS);\n            return;\n        }\n        // This animation uses the FLIP approach. You can read more about it at the link below:\n        // https://aerotwist.com/blog/flip-your-animations/\n        // Calculate the dimensions based on the dimensions of the previous indicator\n        const currentClientRect = element.getBoundingClientRect();\n        const widthDelta = previousIndicatorClientRect.width / currentClientRect.width;\n        const xPosition = previousIndicatorClientRect.left - currentClientRect.left;\n        element.classList.add(NO_TRANSITION_CLASS);\n        this._inkBarContentElement.style.setProperty('transform', `translateX(${xPosition}px) scaleX(${widthDelta})`);\n        // Force repaint before updating classes and transform to ensure the transform properly takes effect\n        element.getBoundingClientRect();\n        element.classList.remove(NO_TRANSITION_CLASS);\n        element.classList.add(ACTIVE_CLASS);\n        this._inkBarContentElement.style.setProperty('transform', '');\n    }\n    /** Removes the ink bar from the current item. */\n    deactivateInkBar() {\n        this._elementRef.nativeElement.classList.remove(ACTIVE_CLASS);\n    }\n    /** Initializes the foundation. */\n    ngOnInit() {\n        this._createInkBarElement();\n    }\n    /** Destroys the foundation. */\n    ngOnDestroy() {\n        this._inkBarElement?.remove();\n        this._inkBarElement = this._inkBarContentElement = null;\n    }\n    /** Creates and appends the ink bar element. */\n    _createInkBarElement() {\n        const documentNode = this._elementRef.nativeElement.ownerDocument || document;\n        const inkBarElement = (this._inkBarElement = documentNode.createElement('span'));\n        const inkBarContentElement = (this._inkBarContentElement = documentNode.createElement('span'));\n        inkBarElement.className = 'mdc-tab-indicator';\n        inkBarContentElement.className =\n            'mdc-tab-indicator__content mdc-tab-indicator__content--underline';\n        inkBarElement.appendChild(this._inkBarContentElement);\n        this._appendInkBarElement();\n    }\n    /**\n     * Appends the ink bar to the tab host element or content, depending on whether\n     * the ink bar should fit to content.\n     */\n    _appendInkBarElement() {\n        if (!this._inkBarElement && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw Error('Ink bar element has not been created and cannot be appended');\n        }\n        const parentElement = this._fitToContent\n            ? this._elementRef.nativeElement.querySelector('.mdc-tab__content')\n            : this._elementRef.nativeElement;\n        if (!parentElement && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw Error('Missing element to host the ink bar');\n        }\n        parentElement.appendChild(this._inkBarElement);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: InkBarItem, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"16.1.0\", version: \"17.2.0\", type: InkBarItem, inputs: { fitInkBarToContent: [\"fitInkBarToContent\", \"fitInkBarToContent\", booleanAttribute] }, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: InkBarItem, decorators: [{\n            type: Directive\n        }], propDecorators: { fitInkBarToContent: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }] } });\n/**\n * The default positioner function for the MatInkBar.\n * @docs-private\n */\nfunction _MAT_INK_BAR_POSITIONER_FACTORY() {\n    const method = (element) => ({\n        left: element ? (element.offsetLeft || 0) + 'px' : '0',\n        width: element ? (element.offsetWidth || 0) + 'px' : '0',\n    });\n    return method;\n}\n/** Injection token for the MatInkBar's Positioner. */\nconst _MAT_INK_BAR_POSITIONER = new InjectionToken('MatInkBarPositioner', {\n    providedIn: 'root',\n    factory: _MAT_INK_BAR_POSITIONER_FACTORY,\n});\n\n/**\n * Used in the `mat-tab-group` view to display tab labels.\n * @docs-private\n */\nclass MatTabLabelWrapper extends InkBarItem {\n    constructor(elementRef) {\n        super();\n        this.elementRef = elementRef;\n        /** Whether the tab is disabled. */\n        this.disabled = false;\n    }\n    /** Sets focus on the wrapper element */\n    focus() {\n        this.elementRef.nativeElement.focus();\n    }\n    getOffsetLeft() {\n        return this.elementRef.nativeElement.offsetLeft;\n    }\n    getOffsetWidth() {\n        return this.elementRef.nativeElement.offsetWidth;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatTabLabelWrapper, deps: [{ token: i0.ElementRef }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"16.1.0\", version: \"17.2.0\", type: MatTabLabelWrapper, isStandalone: true, selector: \"[matTabLabelWrapper]\", inputs: { disabled: [\"disabled\", \"disabled\", booleanAttribute] }, host: { properties: { \"class.mat-mdc-tab-disabled\": \"disabled\", \"attr.aria-disabled\": \"!!disabled\" } }, usesInheritance: true, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatTabLabelWrapper, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[matTabLabelWrapper]',\n                    host: {\n                        '[class.mat-mdc-tab-disabled]': 'disabled',\n                        '[attr.aria-disabled]': '!!disabled',\n                    },\n                    standalone: true,\n                }]\n        }], ctorParameters: () => [{ type: i0.ElementRef }], propDecorators: { disabled: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }] } });\n\n/** Config used to bind passive event listeners */\nconst passiveEventListenerOptions = normalizePassiveListenerOptions({\n    passive: true,\n});\n/**\n * Amount of milliseconds to wait before starting to scroll the header automatically.\n * Set a little conservatively in order to handle fake events dispatched on touch devices.\n */\nconst HEADER_SCROLL_DELAY = 650;\n/**\n * Interval in milliseconds at which to scroll the header\n * while the user is holding their pointer.\n */\nconst HEADER_SCROLL_INTERVAL = 100;\n/**\n * Base class for a tab header that supported pagination.\n * @docs-private\n */\nclass MatPaginatedTabHeader {\n    /** The index of the active tab. */\n    get selectedIndex() {\n        return this._selectedIndex;\n    }\n    set selectedIndex(v) {\n        const value = isNaN(v) ? 0 : v;\n        if (this._selectedIndex != value) {\n            this._selectedIndexChanged = true;\n            this._selectedIndex = value;\n            if (this._keyManager) {\n                this._keyManager.updateActiveItem(value);\n            }\n        }\n    }\n    constructor(_elementRef, _changeDetectorRef, _viewportRuler, _dir, _ngZone, _platform, _animationMode) {\n        this._elementRef = _elementRef;\n        this._changeDetectorRef = _changeDetectorRef;\n        this._viewportRuler = _viewportRuler;\n        this._dir = _dir;\n        this._ngZone = _ngZone;\n        this._platform = _platform;\n        this._animationMode = _animationMode;\n        /** The distance in pixels that the tab labels should be translated to the left. */\n        this._scrollDistance = 0;\n        /** Whether the header should scroll to the selected index after the view has been checked. */\n        this._selectedIndexChanged = false;\n        /** Emits when the component is destroyed. */\n        this._destroyed = new Subject();\n        /** Whether the controls for pagination should be displayed */\n        this._showPaginationControls = false;\n        /** Whether the tab list can be scrolled more towards the end of the tab label list. */\n        this._disableScrollAfter = true;\n        /** Whether the tab list can be scrolled more towards the beginning of the tab label list. */\n        this._disableScrollBefore = true;\n        /** Stream that will stop the automated scrolling. */\n        this._stopScrolling = new Subject();\n        /**\n         * Whether pagination should be disabled. This can be used to avoid unnecessary\n         * layout recalculations if it's known that pagination won't be required.\n         */\n        this.disablePagination = false;\n        this._selectedIndex = 0;\n        /** Event emitted when the option is selected. */\n        this.selectFocusedIndex = new EventEmitter();\n        /** Event emitted when a label is focused. */\n        this.indexFocused = new EventEmitter();\n        // Bind the `mouseleave` event on the outside since it doesn't change anything in the view.\n        _ngZone.runOutsideAngular(() => {\n            fromEvent(_elementRef.nativeElement, 'mouseleave')\n                .pipe(takeUntil(this._destroyed))\n                .subscribe(() => {\n                this._stopInterval();\n            });\n        });\n    }\n    ngAfterViewInit() {\n        // We need to handle these events manually, because we want to bind passive event listeners.\n        fromEvent(this._previousPaginator.nativeElement, 'touchstart', passiveEventListenerOptions)\n            .pipe(takeUntil(this._destroyed))\n            .subscribe(() => {\n            this._handlePaginatorPress('before');\n        });\n        fromEvent(this._nextPaginator.nativeElement, 'touchstart', passiveEventListenerOptions)\n            .pipe(takeUntil(this._destroyed))\n            .subscribe(() => {\n            this._handlePaginatorPress('after');\n        });\n    }\n    ngAfterContentInit() {\n        const dirChange = this._dir ? this._dir.change : of('ltr');\n        const resize = this._viewportRuler.change(150);\n        const realign = () => {\n            this.updatePagination();\n            this._alignInkBarToSelectedTab();\n        };\n        this._keyManager = new FocusKeyManager(this._items)\n            .withHorizontalOrientation(this._getLayoutDirection())\n            .withHomeAndEnd()\n            .withWrap()\n            // Allow focus to land on disabled tabs, as per https://w3c.github.io/aria-practices/#kbd_disabled_controls\n            .skipPredicate(() => false);\n        this._keyManager.updateActiveItem(this._selectedIndex);\n        // Defer the first call in order to allow for slower browsers to lay out the elements.\n        // This helps in cases where the user lands directly on a page with paginated tabs.\n        // Note that we use `onStable` instead of `requestAnimationFrame`, because the latter\n        // can hold up tests that are in a background tab.\n        this._ngZone.onStable.pipe(take(1)).subscribe(realign);\n        // On dir change or window resize, realign the ink bar and update the orientation of\n        // the key manager if the direction has changed.\n        merge(dirChange, resize, this._items.changes, this._itemsResized())\n            .pipe(takeUntil(this._destroyed))\n            .subscribe(() => {\n            // We need to defer this to give the browser some time to recalculate\n            // the element dimensions. The call has to be wrapped in `NgZone.run`,\n            // because the viewport change handler runs outside of Angular.\n            this._ngZone.run(() => {\n                Promise.resolve().then(() => {\n                    // Clamp the scroll distance, because it can change with the number of tabs.\n                    this._scrollDistance = Math.max(0, Math.min(this._getMaxScrollDistance(), this._scrollDistance));\n                    realign();\n                });\n            });\n            this._keyManager.withHorizontalOrientation(this._getLayoutDirection());\n        });\n        // If there is a change in the focus key manager we need to emit the `indexFocused`\n        // event in order to provide a public event that notifies about focus changes. Also we realign\n        // the tabs container by scrolling the new focused tab into the visible section.\n        this._keyManager.change.subscribe(newFocusIndex => {\n            this.indexFocused.emit(newFocusIndex);\n            this._setTabFocus(newFocusIndex);\n        });\n    }\n    /** Sends any changes that could affect the layout of the items. */\n    _itemsResized() {\n        if (typeof ResizeObserver !== 'function') {\n            return EMPTY;\n        }\n        return this._items.changes.pipe(startWith(this._items), switchMap((tabItems) => new Observable((observer) => this._ngZone.runOutsideAngular(() => {\n            const resizeObserver = new ResizeObserver(entries => observer.next(entries));\n            tabItems.forEach(item => resizeObserver.observe(item.elementRef.nativeElement));\n            return () => {\n                resizeObserver.disconnect();\n            };\n        }))), \n        // Skip the first emit since the resize observer emits when an item\n        // is observed for new items when the tab is already inserted\n        skip(1), \n        // Skip emissions where all the elements are invisible since we don't want\n        // the header to try and re-render with invalid measurements. See #25574.\n        filter(entries => entries.some(e => e.contentRect.width > 0 && e.contentRect.height > 0)));\n    }\n    ngAfterContentChecked() {\n        // If the number of tab labels have changed, check if scrolling should be enabled\n        if (this._tabLabelCount != this._items.length) {\n            this.updatePagination();\n            this._tabLabelCount = this._items.length;\n            this._changeDetectorRef.markForCheck();\n        }\n        // If the selected index has changed, scroll to the label and check if the scrolling controls\n        // should be disabled.\n        if (this._selectedIndexChanged) {\n            this._scrollToLabel(this._selectedIndex);\n            this._checkScrollingControls();\n            this._alignInkBarToSelectedTab();\n            this._selectedIndexChanged = false;\n            this._changeDetectorRef.markForCheck();\n        }\n        // If the scroll distance has been changed (tab selected, focused, scroll controls activated),\n        // then translate the header to reflect this.\n        if (this._scrollDistanceChanged) {\n            this._updateTabScrollPosition();\n            this._scrollDistanceChanged = false;\n            this._changeDetectorRef.markForCheck();\n        }\n    }\n    ngOnDestroy() {\n        this._keyManager?.destroy();\n        this._destroyed.next();\n        this._destroyed.complete();\n        this._stopScrolling.complete();\n    }\n    /** Handles keyboard events on the header. */\n    _handleKeydown(event) {\n        // We don't handle any key bindings with a modifier key.\n        if (hasModifierKey(event)) {\n            return;\n        }\n        switch (event.keyCode) {\n            case ENTER:\n            case SPACE:\n                if (this.focusIndex !== this.selectedIndex) {\n                    const item = this._items.get(this.focusIndex);\n                    if (item && !item.disabled) {\n                        this.selectFocusedIndex.emit(this.focusIndex);\n                        this._itemSelected(event);\n                    }\n                }\n                break;\n            default:\n                this._keyManager.onKeydown(event);\n        }\n    }\n    /**\n     * Callback for when the MutationObserver detects that the content has changed.\n     */\n    _onContentChanges() {\n        const textContent = this._elementRef.nativeElement.textContent;\n        // We need to diff the text content of the header, because the MutationObserver callback\n        // will fire even if the text content didn't change which is inefficient and is prone\n        // to infinite loops if a poorly constructed expression is passed in (see #14249).\n        if (textContent !== this._currentTextContent) {\n            this._currentTextContent = textContent || '';\n            // The content observer runs outside the `NgZone` by default, which\n            // means that we need to bring the callback back in ourselves.\n            this._ngZone.run(() => {\n                this.updatePagination();\n                this._alignInkBarToSelectedTab();\n                this._changeDetectorRef.markForCheck();\n            });\n        }\n    }\n    /**\n     * Updates the view whether pagination should be enabled or not.\n     *\n     * WARNING: Calling this method can be very costly in terms of performance. It should be called\n     * as infrequently as possible from outside of the Tabs component as it causes a reflow of the\n     * page.\n     */\n    updatePagination() {\n        this._checkPaginationEnabled();\n        this._checkScrollingControls();\n        this._updateTabScrollPosition();\n    }\n    /** Tracks which element has focus; used for keyboard navigation */\n    get focusIndex() {\n        return this._keyManager ? this._keyManager.activeItemIndex : 0;\n    }\n    /** When the focus index is set, we must manually send focus to the correct label */\n    set focusIndex(value) {\n        if (!this._isValidIndex(value) || this.focusIndex === value || !this._keyManager) {\n            return;\n        }\n        this._keyManager.setActiveItem(value);\n    }\n    /**\n     * Determines if an index is valid.  If the tabs are not ready yet, we assume that the user is\n     * providing a valid index and return true.\n     */\n    _isValidIndex(index) {\n        return this._items ? !!this._items.toArray()[index] : true;\n    }\n    /**\n     * Sets focus on the HTML element for the label wrapper and scrolls it into the view if\n     * scrolling is enabled.\n     */\n    _setTabFocus(tabIndex) {\n        if (this._showPaginationControls) {\n            this._scrollToLabel(tabIndex);\n        }\n        if (this._items && this._items.length) {\n            this._items.toArray()[tabIndex].focus();\n            // Do not let the browser manage scrolling to focus the element, this will be handled\n            // by using translation. In LTR, the scroll left should be 0. In RTL, the scroll width\n            // should be the full width minus the offset width.\n            const containerEl = this._tabListContainer.nativeElement;\n            const dir = this._getLayoutDirection();\n            if (dir == 'ltr') {\n                containerEl.scrollLeft = 0;\n            }\n            else {\n                containerEl.scrollLeft = containerEl.scrollWidth - containerEl.offsetWidth;\n            }\n        }\n    }\n    /** The layout direction of the containing app. */\n    _getLayoutDirection() {\n        return this._dir && this._dir.value === 'rtl' ? 'rtl' : 'ltr';\n    }\n    /** Performs the CSS transformation on the tab list that will cause the list to scroll. */\n    _updateTabScrollPosition() {\n        if (this.disablePagination) {\n            return;\n        }\n        const scrollDistance = this.scrollDistance;\n        const translateX = this._getLayoutDirection() === 'ltr' ? -scrollDistance : scrollDistance;\n        // Don't use `translate3d` here because we don't want to create a new layer. A new layer\n        // seems to cause flickering and overflow in Internet Explorer. For example, the ink bar\n        // and ripples will exceed the boundaries of the visible tab bar.\n        // See: https://github.com/angular/components/issues/10276\n        // We round the `transform` here, because transforms with sub-pixel precision cause some\n        // browsers to blur the content of the element.\n        this._tabList.nativeElement.style.transform = `translateX(${Math.round(translateX)}px)`;\n        // Setting the `transform` on IE will change the scroll offset of the parent, causing the\n        // position to be thrown off in some cases. We have to reset it ourselves to ensure that\n        // it doesn't get thrown off. Note that we scope it only to IE and Edge, because messing\n        // with the scroll position throws off Chrome 71+ in RTL mode (see #14689).\n        if (this._platform.TRIDENT || this._platform.EDGE) {\n            this._tabListContainer.nativeElement.scrollLeft = 0;\n        }\n    }\n    /** Sets the distance in pixels that the tab header should be transformed in the X-axis. */\n    get scrollDistance() {\n        return this._scrollDistance;\n    }\n    set scrollDistance(value) {\n        this._scrollTo(value);\n    }\n    /**\n     * Moves the tab list in the 'before' or 'after' direction (towards the beginning of the list or\n     * the end of the list, respectively). The distance to scroll is computed to be a third of the\n     * length of the tab list view window.\n     *\n     * This is an expensive call that forces a layout reflow to compute box and scroll metrics and\n     * should be called sparingly.\n     */\n    _scrollHeader(direction) {\n        const viewLength = this._tabListContainer.nativeElement.offsetWidth;\n        // Move the scroll distance one-third the length of the tab list's viewport.\n        const scrollAmount = ((direction == 'before' ? -1 : 1) * viewLength) / 3;\n        return this._scrollTo(this._scrollDistance + scrollAmount);\n    }\n    /** Handles click events on the pagination arrows. */\n    _handlePaginatorClick(direction) {\n        this._stopInterval();\n        this._scrollHeader(direction);\n    }\n    /**\n     * Moves the tab list such that the desired tab label (marked by index) is moved into view.\n     *\n     * This is an expensive call that forces a layout reflow to compute box and scroll metrics and\n     * should be called sparingly.\n     */\n    _scrollToLabel(labelIndex) {\n        if (this.disablePagination) {\n            return;\n        }\n        const selectedLabel = this._items ? this._items.toArray()[labelIndex] : null;\n        if (!selectedLabel) {\n            return;\n        }\n        // The view length is the visible width of the tab labels.\n        const viewLength = this._tabListContainer.nativeElement.offsetWidth;\n        const { offsetLeft, offsetWidth } = selectedLabel.elementRef.nativeElement;\n        let labelBeforePos, labelAfterPos;\n        if (this._getLayoutDirection() == 'ltr') {\n            labelBeforePos = offsetLeft;\n            labelAfterPos = labelBeforePos + offsetWidth;\n        }\n        else {\n            labelAfterPos = this._tabListInner.nativeElement.offsetWidth - offsetLeft;\n            labelBeforePos = labelAfterPos - offsetWidth;\n        }\n        const beforeVisiblePos = this.scrollDistance;\n        const afterVisiblePos = this.scrollDistance + viewLength;\n        if (labelBeforePos < beforeVisiblePos) {\n            // Scroll header to move label to the before direction\n            this.scrollDistance -= beforeVisiblePos - labelBeforePos;\n        }\n        else if (labelAfterPos > afterVisiblePos) {\n            // Scroll header to move label to the after direction\n            this.scrollDistance += Math.min(labelAfterPos - afterVisiblePos, labelBeforePos - beforeVisiblePos);\n        }\n    }\n    /**\n     * Evaluate whether the pagination controls should be displayed. If the scroll width of the\n     * tab list is wider than the size of the header container, then the pagination controls should\n     * be shown.\n     *\n     * This is an expensive call that forces a layout reflow to compute box and scroll metrics and\n     * should be called sparingly.\n     */\n    _checkPaginationEnabled() {\n        if (this.disablePagination) {\n            this._showPaginationControls = false;\n        }\n        else {\n            const isEnabled = this._tabListInner.nativeElement.scrollWidth > this._elementRef.nativeElement.offsetWidth;\n            if (!isEnabled) {\n                this.scrollDistance = 0;\n            }\n            if (isEnabled !== this._showPaginationControls) {\n                this._changeDetectorRef.markForCheck();\n            }\n            this._showPaginationControls = isEnabled;\n        }\n    }\n    /**\n     * Evaluate whether the before and after controls should be enabled or disabled.\n     * If the header is at the beginning of the list (scroll distance is equal to 0) then disable the\n     * before button. If the header is at the end of the list (scroll distance is equal to the\n     * maximum distance we can scroll), then disable the after button.\n     *\n     * This is an expensive call that forces a layout reflow to compute box and scroll metrics and\n     * should be called sparingly.\n     */\n    _checkScrollingControls() {\n        if (this.disablePagination) {\n            this._disableScrollAfter = this._disableScrollBefore = true;\n        }\n        else {\n            // Check if the pagination arrows should be activated.\n            this._disableScrollBefore = this.scrollDistance == 0;\n            this._disableScrollAfter = this.scrollDistance == this._getMaxScrollDistance();\n            this._changeDetectorRef.markForCheck();\n        }\n    }\n    /**\n     * Determines what is the maximum length in pixels that can be set for the scroll distance. This\n     * is equal to the difference in width between the tab list container and tab header container.\n     *\n     * This is an expensive call that forces a layout reflow to compute box and scroll metrics and\n     * should be called sparingly.\n     */\n    _getMaxScrollDistance() {\n        const lengthOfTabList = this._tabListInner.nativeElement.scrollWidth;\n        const viewLength = this._tabListContainer.nativeElement.offsetWidth;\n        return lengthOfTabList - viewLength || 0;\n    }\n    /** Tells the ink-bar to align itself to the current label wrapper */\n    _alignInkBarToSelectedTab() {\n        const selectedItem = this._items && this._items.length ? this._items.toArray()[this.selectedIndex] : null;\n        const selectedLabelWrapper = selectedItem ? selectedItem.elementRef.nativeElement : null;\n        if (selectedLabelWrapper) {\n            this._inkBar.alignToElement(selectedLabelWrapper);\n        }\n        else {\n            this._inkBar.hide();\n        }\n    }\n    /** Stops the currently-running paginator interval.  */\n    _stopInterval() {\n        this._stopScrolling.next();\n    }\n    /**\n     * Handles the user pressing down on one of the paginators.\n     * Starts scrolling the header after a certain amount of time.\n     * @param direction In which direction the paginator should be scrolled.\n     */\n    _handlePaginatorPress(direction, mouseEvent) {\n        // Don't start auto scrolling for right mouse button clicks. Note that we shouldn't have to\n        // null check the `button`, but we do it so we don't break tests that use fake events.\n        if (mouseEvent && mouseEvent.button != null && mouseEvent.button !== 0) {\n            return;\n        }\n        // Avoid overlapping timers.\n        this._stopInterval();\n        // Start a timer after the delay and keep firing based on the interval.\n        timer(HEADER_SCROLL_DELAY, HEADER_SCROLL_INTERVAL)\n            // Keep the timer going until something tells it to stop or the component is destroyed.\n            .pipe(takeUntil(merge(this._stopScrolling, this._destroyed)))\n            .subscribe(() => {\n            const { maxScrollDistance, distance } = this._scrollHeader(direction);\n            // Stop the timer if we've reached the start or the end.\n            if (distance === 0 || distance >= maxScrollDistance) {\n                this._stopInterval();\n            }\n        });\n    }\n    /**\n     * Scrolls the header to a given position.\n     * @param position Position to which to scroll.\n     * @returns Information on the current scroll distance and the maximum.\n     */\n    _scrollTo(position) {\n        if (this.disablePagination) {\n            return { maxScrollDistance: 0, distance: 0 };\n        }\n        const maxScrollDistance = this._getMaxScrollDistance();\n        this._scrollDistance = Math.max(0, Math.min(maxScrollDistance, position));\n        // Mark that the scroll distance has changed so that after the view is checked, the CSS\n        // transformation can move the header.\n        this._scrollDistanceChanged = true;\n        this._checkScrollingControls();\n        return { maxScrollDistance, distance: this._scrollDistance };\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatPaginatedTabHeader, deps: [{ token: i0.ElementRef }, { token: i0.ChangeDetectorRef }, { token: i1.ViewportRuler }, { token: i2.Directionality, optional: true }, { token: i0.NgZone }, { token: i3.Platform }, { token: ANIMATION_MODULE_TYPE, optional: true }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"16.1.0\", version: \"17.2.0\", type: MatPaginatedTabHeader, inputs: { disablePagination: [\"disablePagination\", \"disablePagination\", booleanAttribute], selectedIndex: [\"selectedIndex\", \"selectedIndex\", numberAttribute] }, outputs: { selectFocusedIndex: \"selectFocusedIndex\", indexFocused: \"indexFocused\" }, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatPaginatedTabHeader, decorators: [{\n            type: Directive\n        }], ctorParameters: () => [{ type: i0.ElementRef }, { type: i0.ChangeDetectorRef }, { type: i1.ViewportRuler }, { type: i2.Directionality, decorators: [{\n                    type: Optional\n                }] }, { type: i0.NgZone }, { type: i3.Platform }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [ANIMATION_MODULE_TYPE]\n                }] }], propDecorators: { disablePagination: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], selectedIndex: [{\n                type: Input,\n                args: [{ transform: numberAttribute }]\n            }], selectFocusedIndex: [{\n                type: Output\n            }], indexFocused: [{\n                type: Output\n            }] } });\n\n/**\n * The header of the tab group which displays a list of all the tabs in the tab group. Includes\n * an ink bar that follows the currently selected tab. When the tabs list's width exceeds the\n * width of the header container, then arrows will be displayed to allow the user to scroll\n * left and right across the header.\n * @docs-private\n */\nclass MatTabHeader extends MatPaginatedTabHeader {\n    constructor(elementRef, changeDetectorRef, viewportRuler, dir, ngZone, platform, animationMode) {\n        super(elementRef, changeDetectorRef, viewportRuler, dir, ngZone, platform, animationMode);\n        /** Whether the ripple effect is disabled or not. */\n        this.disableRipple = false;\n    }\n    ngAfterContentInit() {\n        this._inkBar = new MatInkBar(this._items);\n        super.ngAfterContentInit();\n    }\n    _itemSelected(event) {\n        event.preventDefault();\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatTabHeader, deps: [{ token: i0.ElementRef }, { token: i0.ChangeDetectorRef }, { token: i1.ViewportRuler }, { token: i2.Directionality, optional: true }, { token: i0.NgZone }, { token: i3.Platform }, { token: ANIMATION_MODULE_TYPE, optional: true }], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"16.1.0\", version: \"17.2.0\", type: MatTabHeader, isStandalone: true, selector: \"mat-tab-header\", inputs: { disableRipple: [\"disableRipple\", \"disableRipple\", booleanAttribute] }, host: { properties: { \"class.mat-mdc-tab-header-pagination-controls-enabled\": \"_showPaginationControls\", \"class.mat-mdc-tab-header-rtl\": \"_getLayoutDirection() == 'rtl'\" }, classAttribute: \"mat-mdc-tab-header\" }, queries: [{ propertyName: \"_items\", predicate: MatTabLabelWrapper }], viewQueries: [{ propertyName: \"_tabListContainer\", first: true, predicate: [\"tabListContainer\"], descendants: true, static: true }, { propertyName: \"_tabList\", first: true, predicate: [\"tabList\"], descendants: true, static: true }, { propertyName: \"_tabListInner\", first: true, predicate: [\"tabListInner\"], descendants: true, static: true }, { propertyName: \"_nextPaginator\", first: true, predicate: [\"nextPaginator\"], descendants: true }, { propertyName: \"_previousPaginator\", first: true, predicate: [\"previousPaginator\"], descendants: true }], usesInheritance: true, ngImport: i0, template: \"<!-- TODO: this also had `mat-elevation-z4`. Figure out what we should do with it. -->\\n<button class=\\\"mat-mdc-tab-header-pagination mat-mdc-tab-header-pagination-before\\\"\\n     #previousPaginator\\n     aria-hidden=\\\"true\\\"\\n     type=\\\"button\\\"\\n     mat-ripple\\n     tabindex=\\\"-1\\\"\\n     [matRippleDisabled]=\\\"_disableScrollBefore || disableRipple\\\"\\n     [class.mat-mdc-tab-header-pagination-disabled]=\\\"_disableScrollBefore\\\"\\n     [disabled]=\\\"_disableScrollBefore || null\\\"\\n     (click)=\\\"_handlePaginatorClick('before')\\\"\\n     (mousedown)=\\\"_handlePaginatorPress('before', $event)\\\"\\n     (touchend)=\\\"_stopInterval()\\\">\\n  <div class=\\\"mat-mdc-tab-header-pagination-chevron\\\"></div>\\n</button>\\n\\n<div\\n  class=\\\"mat-mdc-tab-label-container\\\"\\n  #tabListContainer\\n  (keydown)=\\\"_handleKeydown($event)\\\"\\n  [class._mat-animation-noopable]=\\\"_animationMode === 'NoopAnimations'\\\">\\n  <div\\n    #tabList\\n    class=\\\"mat-mdc-tab-list\\\"\\n    role=\\\"tablist\\\"\\n    (cdkObserveContent)=\\\"_onContentChanges()\\\">\\n    <div class=\\\"mat-mdc-tab-labels\\\" #tabListInner>\\n      <ng-content></ng-content>\\n    </div>\\n  </div>\\n</div>\\n\\n<!-- TODO: this also had `mat-elevation-z4`. Figure out what we should do with it. -->\\n<button class=\\\"mat-mdc-tab-header-pagination mat-mdc-tab-header-pagination-after\\\"\\n     #nextPaginator\\n     aria-hidden=\\\"true\\\"\\n     type=\\\"button\\\"\\n     mat-ripple\\n     [matRippleDisabled]=\\\"_disableScrollAfter || disableRipple\\\"\\n     [class.mat-mdc-tab-header-pagination-disabled]=\\\"_disableScrollAfter\\\"\\n     [disabled]=\\\"_disableScrollAfter || null\\\"\\n     tabindex=\\\"-1\\\"\\n     (mousedown)=\\\"_handlePaginatorPress('after', $event)\\\"\\n     (click)=\\\"_handlePaginatorClick('after')\\\"\\n     (touchend)=\\\"_stopInterval()\\\">\\n  <div class=\\\"mat-mdc-tab-header-pagination-chevron\\\"></div>\\n</button>\\n\", styles: [\".mat-mdc-tab-header{display:flex;overflow:hidden;position:relative;flex-shrink:0}.mdc-tab-indicator .mdc-tab-indicator__content{transition-duration:var(--mat-tab-animation-duration, 250ms)}.mat-mdc-tab-header-pagination{-webkit-user-select:none;user-select:none;position:relative;display:none;justify-content:center;align-items:center;min-width:32px;cursor:pointer;z-index:2;-webkit-tap-highlight-color:rgba(0,0,0,0);touch-action:none;box-sizing:content-box;background:none;border:none;outline:0;padding:0}.mat-mdc-tab-header-pagination::-moz-focus-inner{border:0}.mat-mdc-tab-header-pagination .mat-ripple-element{opacity:.12;background-color:var(--mat-tab-header-inactive-ripple-color)}.mat-mdc-tab-header-pagination-controls-enabled .mat-mdc-tab-header-pagination{display:flex}.mat-mdc-tab-header-pagination-before,.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-after{padding-left:4px}.mat-mdc-tab-header-pagination-before .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-after .mat-mdc-tab-header-pagination-chevron{transform:rotate(-135deg)}.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-before,.mat-mdc-tab-header-pagination-after{padding-right:4px}.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-before .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-header-pagination-after .mat-mdc-tab-header-pagination-chevron{transform:rotate(45deg)}.mat-mdc-tab-header-pagination-chevron{border-style:solid;border-width:2px 2px 0 0;height:8px;width:8px;border-color:var(--mat-tab-header-pagination-icon-color)}.mat-mdc-tab-header-pagination-disabled{box-shadow:none;cursor:default;pointer-events:none}.mat-mdc-tab-header-pagination-disabled .mat-mdc-tab-header-pagination-chevron{opacity:.4}.mat-mdc-tab-list{flex-grow:1;position:relative;transition:transform 500ms cubic-bezier(0.35, 0, 0.25, 1)}._mat-animation-noopable .mat-mdc-tab-list{transition:none}._mat-animation-noopable span.mdc-tab-indicator__content,._mat-animation-noopable span.mdc-tab__text-label{transition:none}.mat-mdc-tab-label-container{display:flex;flex-grow:1;overflow:hidden;z-index:1;border-bottom-style:solid;border-bottom-width:var(--mat-tab-header-divider-height);border-bottom-color:var(--mat-tab-header-divider-color)}.mat-mdc-tab-group-inverted-header .mat-mdc-tab-label-container{border-bottom:none;border-top-style:solid;border-top-width:var(--mat-tab-header-divider-height);border-top-color:var(--mat-tab-header-divider-color)}.mat-mdc-tab-labels{display:flex;flex:1 0 auto}[mat-align-tabs=center]>.mat-mdc-tab-header .mat-mdc-tab-labels{justify-content:center}[mat-align-tabs=end]>.mat-mdc-tab-header .mat-mdc-tab-labels{justify-content:flex-end}.mat-mdc-tab::before{margin:5px}.cdk-high-contrast-active .mat-mdc-tab[aria-disabled=true]{color:GrayText}\"], dependencies: [{ kind: \"directive\", type: MatRipple, selector: \"[mat-ripple], [matRipple]\", inputs: [\"matRippleColor\", \"matRippleUnbounded\", \"matRippleCentered\", \"matRippleRadius\", \"matRippleAnimation\", \"matRippleDisabled\", \"matRippleTrigger\"], exportAs: [\"matRipple\"] }, { kind: \"directive\", type: CdkObserveContent, selector: \"[cdkObserveContent]\", inputs: [\"cdkObserveContentDisabled\", \"debounce\"], outputs: [\"cdkObserveContent\"], exportAs: [\"cdkObserveContent\"] }], changeDetection: i0.ChangeDetectionStrategy.Default, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatTabHeader, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-tab-header', encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.Default, host: {\n                        'class': 'mat-mdc-tab-header',\n                        '[class.mat-mdc-tab-header-pagination-controls-enabled]': '_showPaginationControls',\n                        '[class.mat-mdc-tab-header-rtl]': \"_getLayoutDirection() == 'rtl'\",\n                    }, standalone: true, imports: [MatRipple, CdkObserveContent], template: \"<!-- TODO: this also had `mat-elevation-z4`. Figure out what we should do with it. -->\\n<button class=\\\"mat-mdc-tab-header-pagination mat-mdc-tab-header-pagination-before\\\"\\n     #previousPaginator\\n     aria-hidden=\\\"true\\\"\\n     type=\\\"button\\\"\\n     mat-ripple\\n     tabindex=\\\"-1\\\"\\n     [matRippleDisabled]=\\\"_disableScrollBefore || disableRipple\\\"\\n     [class.mat-mdc-tab-header-pagination-disabled]=\\\"_disableScrollBefore\\\"\\n     [disabled]=\\\"_disableScrollBefore || null\\\"\\n     (click)=\\\"_handlePaginatorClick('before')\\\"\\n     (mousedown)=\\\"_handlePaginatorPress('before', $event)\\\"\\n     (touchend)=\\\"_stopInterval()\\\">\\n  <div class=\\\"mat-mdc-tab-header-pagination-chevron\\\"></div>\\n</button>\\n\\n<div\\n  class=\\\"mat-mdc-tab-label-container\\\"\\n  #tabListContainer\\n  (keydown)=\\\"_handleKeydown($event)\\\"\\n  [class._mat-animation-noopable]=\\\"_animationMode === 'NoopAnimations'\\\">\\n  <div\\n    #tabList\\n    class=\\\"mat-mdc-tab-list\\\"\\n    role=\\\"tablist\\\"\\n    (cdkObserveContent)=\\\"_onContentChanges()\\\">\\n    <div class=\\\"mat-mdc-tab-labels\\\" #tabListInner>\\n      <ng-content></ng-content>\\n    </div>\\n  </div>\\n</div>\\n\\n<!-- TODO: this also had `mat-elevation-z4`. Figure out what we should do with it. -->\\n<button class=\\\"mat-mdc-tab-header-pagination mat-mdc-tab-header-pagination-after\\\"\\n     #nextPaginator\\n     aria-hidden=\\\"true\\\"\\n     type=\\\"button\\\"\\n     mat-ripple\\n     [matRippleDisabled]=\\\"_disableScrollAfter || disableRipple\\\"\\n     [class.mat-mdc-tab-header-pagination-disabled]=\\\"_disableScrollAfter\\\"\\n     [disabled]=\\\"_disableScrollAfter || null\\\"\\n     tabindex=\\\"-1\\\"\\n     (mousedown)=\\\"_handlePaginatorPress('after', $event)\\\"\\n     (click)=\\\"_handlePaginatorClick('after')\\\"\\n     (touchend)=\\\"_stopInterval()\\\">\\n  <div class=\\\"mat-mdc-tab-header-pagination-chevron\\\"></div>\\n</button>\\n\", styles: [\".mat-mdc-tab-header{display:flex;overflow:hidden;position:relative;flex-shrink:0}.mdc-tab-indicator .mdc-tab-indicator__content{transition-duration:var(--mat-tab-animation-duration, 250ms)}.mat-mdc-tab-header-pagination{-webkit-user-select:none;user-select:none;position:relative;display:none;justify-content:center;align-items:center;min-width:32px;cursor:pointer;z-index:2;-webkit-tap-highlight-color:rgba(0,0,0,0);touch-action:none;box-sizing:content-box;background:none;border:none;outline:0;padding:0}.mat-mdc-tab-header-pagination::-moz-focus-inner{border:0}.mat-mdc-tab-header-pagination .mat-ripple-element{opacity:.12;background-color:var(--mat-tab-header-inactive-ripple-color)}.mat-mdc-tab-header-pagination-controls-enabled .mat-mdc-tab-header-pagination{display:flex}.mat-mdc-tab-header-pagination-before,.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-after{padding-left:4px}.mat-mdc-tab-header-pagination-before .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-after .mat-mdc-tab-header-pagination-chevron{transform:rotate(-135deg)}.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-before,.mat-mdc-tab-header-pagination-after{padding-right:4px}.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-before .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-header-pagination-after .mat-mdc-tab-header-pagination-chevron{transform:rotate(45deg)}.mat-mdc-tab-header-pagination-chevron{border-style:solid;border-width:2px 2px 0 0;height:8px;width:8px;border-color:var(--mat-tab-header-pagination-icon-color)}.mat-mdc-tab-header-pagination-disabled{box-shadow:none;cursor:default;pointer-events:none}.mat-mdc-tab-header-pagination-disabled .mat-mdc-tab-header-pagination-chevron{opacity:.4}.mat-mdc-tab-list{flex-grow:1;position:relative;transition:transform 500ms cubic-bezier(0.35, 0, 0.25, 1)}._mat-animation-noopable .mat-mdc-tab-list{transition:none}._mat-animation-noopable span.mdc-tab-indicator__content,._mat-animation-noopable span.mdc-tab__text-label{transition:none}.mat-mdc-tab-label-container{display:flex;flex-grow:1;overflow:hidden;z-index:1;border-bottom-style:solid;border-bottom-width:var(--mat-tab-header-divider-height);border-bottom-color:var(--mat-tab-header-divider-color)}.mat-mdc-tab-group-inverted-header .mat-mdc-tab-label-container{border-bottom:none;border-top-style:solid;border-top-width:var(--mat-tab-header-divider-height);border-top-color:var(--mat-tab-header-divider-color)}.mat-mdc-tab-labels{display:flex;flex:1 0 auto}[mat-align-tabs=center]>.mat-mdc-tab-header .mat-mdc-tab-labels{justify-content:center}[mat-align-tabs=end]>.mat-mdc-tab-header .mat-mdc-tab-labels{justify-content:flex-end}.mat-mdc-tab::before{margin:5px}.cdk-high-contrast-active .mat-mdc-tab[aria-disabled=true]{color:GrayText}\"] }]\n        }], ctorParameters: () => [{ type: i0.ElementRef }, { type: i0.ChangeDetectorRef }, { type: i1.ViewportRuler }, { type: i2.Directionality, decorators: [{\n                    type: Optional\n                }] }, { type: i0.NgZone }, { type: i3.Platform }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [ANIMATION_MODULE_TYPE]\n                }] }], propDecorators: { _items: [{\n                type: ContentChildren,\n                args: [MatTabLabelWrapper, { descendants: false }]\n            }], _tabListContainer: [{\n                type: ViewChild,\n                args: ['tabListContainer', { static: true }]\n            }], _tabList: [{\n                type: ViewChild,\n                args: ['tabList', { static: true }]\n            }], _tabListInner: [{\n                type: ViewChild,\n                args: ['tabListInner', { static: true }]\n            }], _nextPaginator: [{\n                type: ViewChild,\n                args: ['nextPaginator']\n            }], _previousPaginator: [{\n                type: ViewChild,\n                args: ['previousPaginator']\n            }], disableRipple: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }] } });\n\n/** Injection token that can be used to provide the default options the tabs module. */\nconst MAT_TABS_CONFIG = new InjectionToken('MAT_TABS_CONFIG');\n\n/**\n * Animations used by the Material tabs.\n * @docs-private\n */\nconst matTabsAnimations = {\n    /** Animation translates a tab along the X axis. */\n    translateTab: trigger('translateTab', [\n        // Transitions to `none` instead of 0, because some browsers might blur the content.\n        state('center, void, left-origin-center, right-origin-center', style({ transform: 'none' })),\n        // If the tab is either on the left or right, we additionally add a `min-height` of 1px\n        // in order to ensure that the element has a height before its state changes. This is\n        // necessary because Chrome does seem to skip the transition in RTL mode if the element does\n        // not have a static height and is not rendered. See related issue: #9465\n        state('left', style({\n            transform: 'translate3d(-100%, 0, 0)',\n            minHeight: '1px',\n            // Normally this is redundant since we detach the content from the DOM, but if the user\n            // opted into keeping the content in the DOM, we have to hide it so it isn't focusable.\n            visibility: 'hidden',\n        })),\n        state('right', style({\n            transform: 'translate3d(100%, 0, 0)',\n            minHeight: '1px',\n            visibility: 'hidden',\n        })),\n        transition('* => left, * => right, left => center, right => center', animate('{{animationDuration}} cubic-bezier(0.35, 0, 0.25, 1)')),\n        transition('void => left-origin-center', [\n            style({ transform: 'translate3d(-100%, 0, 0)', visibility: 'hidden' }),\n            animate('{{animationDuration}} cubic-bezier(0.35, 0, 0.25, 1)'),\n        ]),\n        transition('void => right-origin-center', [\n            style({ transform: 'translate3d(100%, 0, 0)', visibility: 'hidden' }),\n            animate('{{animationDuration}} cubic-bezier(0.35, 0, 0.25, 1)'),\n        ]),\n    ]),\n};\n\n/**\n * The portal host directive for the contents of the tab.\n * @docs-private\n */\nclass MatTabBodyPortal extends CdkPortalOutlet {\n    constructor(componentFactoryResolver, viewContainerRef, _host, _document) {\n        super(componentFactoryResolver, viewContainerRef, _document);\n        this._host = _host;\n        /** Subscription to events for when the tab body begins centering. */\n        this._centeringSub = Subscription.EMPTY;\n        /** Subscription to events for when the tab body finishes leaving from center position. */\n        this._leavingSub = Subscription.EMPTY;\n    }\n    /** Set initial visibility or set up subscription for changing visibility. */\n    ngOnInit() {\n        super.ngOnInit();\n        this._centeringSub = this._host._beforeCentering\n            .pipe(startWith(this._host._isCenterPosition(this._host._position)))\n            .subscribe((isCentering) => {\n            if (isCentering && !this.hasAttached()) {\n                this.attach(this._host._content);\n            }\n        });\n        this._leavingSub = this._host._afterLeavingCenter.subscribe(() => {\n            if (!this._host.preserveContent) {\n                this.detach();\n            }\n        });\n    }\n    /** Clean up centering subscription. */\n    ngOnDestroy() {\n        super.ngOnDestroy();\n        this._centeringSub.unsubscribe();\n        this._leavingSub.unsubscribe();\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatTabBodyPortal, deps: [{ token: i0.ComponentFactoryResolver }, { token: i0.ViewContainerRef }, { token: forwardRef(() => MatTabBody) }, { token: DOCUMENT }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"17.2.0\", type: MatTabBodyPortal, isStandalone: true, selector: \"[matTabBodyHost]\", usesInheritance: true, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatTabBodyPortal, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[matTabBodyHost]',\n                    standalone: true,\n                }]\n        }], ctorParameters: () => [{ type: i0.ComponentFactoryResolver }, { type: i0.ViewContainerRef }, { type: MatTabBody, decorators: [{\n                    type: Inject,\n                    args: [forwardRef(() => MatTabBody)]\n                }] }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }] });\n/**\n * Wrapper for the contents of a tab.\n * @docs-private\n */\nclass MatTabBody {\n    /** The shifted index position of the tab body, where zero represents the active center tab. */\n    set position(position) {\n        this._positionIndex = position;\n        this._computePositionAnimationState();\n    }\n    constructor(_elementRef, _dir, changeDetectorRef) {\n        this._elementRef = _elementRef;\n        this._dir = _dir;\n        /** Subscription to the directionality change observable. */\n        this._dirChangeSubscription = Subscription.EMPTY;\n        /** Emits when an animation on the tab is complete. */\n        this._translateTabComplete = new Subject();\n        /** Event emitted when the tab begins to animate towards the center as the active tab. */\n        this._onCentering = new EventEmitter();\n        /** Event emitted before the centering of the tab begins. */\n        this._beforeCentering = new EventEmitter();\n        /** Event emitted before the centering of the tab begins. */\n        this._afterLeavingCenter = new EventEmitter();\n        /** Event emitted when the tab completes its animation towards the center. */\n        this._onCentered = new EventEmitter(true);\n        // Note that the default value will always be overwritten by `MatTabBody`, but we need one\n        // anyway to prevent the animations module from throwing an error if the body is used on its own.\n        /** Duration for the tab's animation. */\n        this.animationDuration = '500ms';\n        /** Whether the tab's content should be kept in the DOM while it's off-screen. */\n        this.preserveContent = false;\n        if (_dir) {\n            this._dirChangeSubscription = _dir.change.subscribe((dir) => {\n                this._computePositionAnimationState(dir);\n                changeDetectorRef.markForCheck();\n            });\n        }\n        // Ensure that we get unique animation events, because the `.done` callback can get\n        // invoked twice in some browsers. See https://github.com/angular/angular/issues/24084.\n        this._translateTabComplete\n            .pipe(distinctUntilChanged((x, y) => {\n            return x.fromState === y.fromState && x.toState === y.toState;\n        }))\n            .subscribe(event => {\n            // If the transition to the center is complete, emit an event.\n            if (this._isCenterPosition(event.toState) && this._isCenterPosition(this._position)) {\n                this._onCentered.emit();\n            }\n            if (this._isCenterPosition(event.fromState) && !this._isCenterPosition(this._position)) {\n                this._afterLeavingCenter.emit();\n            }\n        });\n    }\n    /**\n     * After initialized, check if the content is centered and has an origin. If so, set the\n     * special position states that transition the tab from the left or right before centering.\n     */\n    ngOnInit() {\n        if (this._position == 'center' && this.origin != null) {\n            this._position = this._computePositionFromOrigin(this.origin);\n        }\n    }\n    ngOnDestroy() {\n        this._dirChangeSubscription.unsubscribe();\n        this._translateTabComplete.complete();\n    }\n    _onTranslateTabStarted(event) {\n        const isCentering = this._isCenterPosition(event.toState);\n        this._beforeCentering.emit(isCentering);\n        if (isCentering) {\n            this._onCentering.emit(this._elementRef.nativeElement.clientHeight);\n        }\n    }\n    /** The text direction of the containing app. */\n    _getLayoutDirection() {\n        return this._dir && this._dir.value === 'rtl' ? 'rtl' : 'ltr';\n    }\n    /** Whether the provided position state is considered center, regardless of origin. */\n    _isCenterPosition(position) {\n        return (position == 'center' || position == 'left-origin-center' || position == 'right-origin-center');\n    }\n    /** Computes the position state that will be used for the tab-body animation trigger. */\n    _computePositionAnimationState(dir = this._getLayoutDirection()) {\n        if (this._positionIndex < 0) {\n            this._position = dir == 'ltr' ? 'left' : 'right';\n        }\n        else if (this._positionIndex > 0) {\n            this._position = dir == 'ltr' ? 'right' : 'left';\n        }\n        else {\n            this._position = 'center';\n        }\n    }\n    /**\n     * Computes the position state based on the specified origin position. This is used if the\n     * tab is becoming visible immediately after creation.\n     */\n    _computePositionFromOrigin(origin) {\n        const dir = this._getLayoutDirection();\n        if ((dir == 'ltr' && origin <= 0) || (dir == 'rtl' && origin > 0)) {\n            return 'left-origin-center';\n        }\n        return 'right-origin-center';\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatTabBody, deps: [{ token: i0.ElementRef }, { token: i2.Directionality, optional: true }, { token: i0.ChangeDetectorRef }], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"17.2.0\", type: MatTabBody, isStandalone: true, selector: \"mat-tab-body\", inputs: { _content: [\"content\", \"_content\"], origin: \"origin\", animationDuration: \"animationDuration\", preserveContent: \"preserveContent\", position: \"position\" }, outputs: { _onCentering: \"_onCentering\", _beforeCentering: \"_beforeCentering\", _afterLeavingCenter: \"_afterLeavingCenter\", _onCentered: \"_onCentered\" }, host: { classAttribute: \"mat-mdc-tab-body\" }, viewQueries: [{ propertyName: \"_portalHost\", first: true, predicate: CdkPortalOutlet, descendants: true }], ngImport: i0, template: \"<div class=\\\"mat-mdc-tab-body-content\\\" #content\\n     [@translateTab]=\\\"{\\n        value: _position,\\n        params: {animationDuration: animationDuration}\\n     }\\\"\\n     (@translateTab.start)=\\\"_onTranslateTabStarted($event)\\\"\\n     (@translateTab.done)=\\\"_translateTabComplete.next($event)\\\"\\n     cdkScrollable>\\n  <ng-template matTabBodyHost></ng-template>\\n</div>\\n\", styles: [\".mat-mdc-tab-body{top:0;left:0;right:0;bottom:0;position:absolute;display:block;overflow:hidden;outline:0;flex-basis:100%}.mat-mdc-tab-body.mat-mdc-tab-body-active{position:relative;overflow-x:hidden;overflow-y:auto;z-index:1;flex-grow:1}.mat-mdc-tab-group.mat-mdc-tab-group-dynamic-height .mat-mdc-tab-body.mat-mdc-tab-body-active{overflow-y:hidden}.mat-mdc-tab-body-content{height:100%;overflow:auto}.mat-mdc-tab-group-dynamic-height .mat-mdc-tab-body-content{overflow:hidden}.mat-mdc-tab-body-content[style*=\\\"visibility: hidden\\\"]{display:none}\"], dependencies: [{ kind: \"directive\", type: MatTabBodyPortal, selector: \"[matTabBodyHost]\" }, { kind: \"directive\", type: CdkScrollable, selector: \"[cdk-scrollable], [cdkScrollable]\" }], animations: [matTabsAnimations.translateTab], changeDetection: i0.ChangeDetectionStrategy.Default, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatTabBody, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-tab-body', encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.Default, animations: [matTabsAnimations.translateTab], host: {\n                        'class': 'mat-mdc-tab-body',\n                    }, standalone: true, imports: [MatTabBodyPortal, CdkScrollable], template: \"<div class=\\\"mat-mdc-tab-body-content\\\" #content\\n     [@translateTab]=\\\"{\\n        value: _position,\\n        params: {animationDuration: animationDuration}\\n     }\\\"\\n     (@translateTab.start)=\\\"_onTranslateTabStarted($event)\\\"\\n     (@translateTab.done)=\\\"_translateTabComplete.next($event)\\\"\\n     cdkScrollable>\\n  <ng-template matTabBodyHost></ng-template>\\n</div>\\n\", styles: [\".mat-mdc-tab-body{top:0;left:0;right:0;bottom:0;position:absolute;display:block;overflow:hidden;outline:0;flex-basis:100%}.mat-mdc-tab-body.mat-mdc-tab-body-active{position:relative;overflow-x:hidden;overflow-y:auto;z-index:1;flex-grow:1}.mat-mdc-tab-group.mat-mdc-tab-group-dynamic-height .mat-mdc-tab-body.mat-mdc-tab-body-active{overflow-y:hidden}.mat-mdc-tab-body-content{height:100%;overflow:auto}.mat-mdc-tab-group-dynamic-height .mat-mdc-tab-body-content{overflow:hidden}.mat-mdc-tab-body-content[style*=\\\"visibility: hidden\\\"]{display:none}\"] }]\n        }], ctorParameters: () => [{ type: i0.ElementRef }, { type: i2.Directionality, decorators: [{\n                    type: Optional\n                }] }, { type: i0.ChangeDetectorRef }], propDecorators: { _onCentering: [{\n                type: Output\n            }], _beforeCentering: [{\n                type: Output\n            }], _afterLeavingCenter: [{\n                type: Output\n            }], _onCentered: [{\n                type: Output\n            }], _portalHost: [{\n                type: ViewChild,\n                args: [CdkPortalOutlet]\n            }], _content: [{\n                type: Input,\n                args: ['content']\n            }], origin: [{\n                type: Input\n            }], animationDuration: [{\n                type: Input\n            }], preserveContent: [{\n                type: Input\n            }], position: [{\n                type: Input\n            }] } });\n\n/** Used to generate unique ID's for each tab component */\nlet nextId = 0;\n/** Boolean constant that determines whether the tab group supports the `backgroundColor` input */\nconst ENABLE_BACKGROUND_INPUT = true;\n/**\n * Material design tab-group component. Supports basic tab pairs (label + content) and includes\n * animated ink-bar, keyboard navigation, and screen reader.\n * See: https://material.io/design/components/tabs.html\n */\nclass MatTabGroup {\n    /** Whether the ink bar should fit its width to the size of the tab label content. */\n    get fitInkBarToContent() {\n        return this._fitInkBarToContent;\n    }\n    set fitInkBarToContent(value) {\n        this._fitInkBarToContent = value;\n        this._changeDetectorRef.markForCheck();\n    }\n    /** The index of the active tab. */\n    get selectedIndex() {\n        return this._selectedIndex;\n    }\n    set selectedIndex(value) {\n        this._indexToSelect = isNaN(value) ? null : value;\n    }\n    /** Duration for the tab animation. Will be normalized to milliseconds if no units are set. */\n    get animationDuration() {\n        return this._animationDuration;\n    }\n    set animationDuration(value) {\n        const stringValue = value + '';\n        this._animationDuration = /^\\d+$/.test(stringValue) ? value + 'ms' : stringValue;\n    }\n    /**\n     * `tabindex` to be set on the inner element that wraps the tab content. Can be used for improved\n     * accessibility when the tab does not have focusable elements or if it has scrollable content.\n     * The `tabindex` will be removed automatically for inactive tabs.\n     * Read more at https://www.w3.org/TR/wai-aria-practices/examples/tabs/tabs-2/tabs.html\n     */\n    get contentTabIndex() {\n        return this._contentTabIndex;\n    }\n    set contentTabIndex(value) {\n        this._contentTabIndex = isNaN(value) ? null : value;\n    }\n    /**\n     * Background color of the tab group.\n     * @deprecated The background color should be customized through Sass theming APIs.\n     * @breaking-change 20.0.0 Remove this input\n     */\n    get backgroundColor() {\n        return this._backgroundColor;\n    }\n    set backgroundColor(value) {\n        if (!ENABLE_BACKGROUND_INPUT) {\n            throw new Error(`mat-tab-group background color must be set through the Sass theming API`);\n        }\n        const classList = this._elementRef.nativeElement.classList;\n        classList.remove('mat-tabs-with-background', `mat-background-${this.backgroundColor}`);\n        if (value) {\n            classList.add('mat-tabs-with-background', `mat-background-${value}`);\n        }\n        this._backgroundColor = value;\n    }\n    constructor(_elementRef, _changeDetectorRef, defaultConfig, _animationMode) {\n        this._elementRef = _elementRef;\n        this._changeDetectorRef = _changeDetectorRef;\n        this._animationMode = _animationMode;\n        /** All of the tabs that belong to the group. */\n        this._tabs = new QueryList();\n        /** The tab index that should be selected after the content has been checked. */\n        this._indexToSelect = 0;\n        /** Index of the tab that was focused last. */\n        this._lastFocusedTabIndex = null;\n        /** Snapshot of the height of the tab body wrapper before another tab is activated. */\n        this._tabBodyWrapperHeight = 0;\n        /** Subscription to tabs being added/removed. */\n        this._tabsSubscription = Subscription.EMPTY;\n        /** Subscription to changes in the tab labels. */\n        this._tabLabelSubscription = Subscription.EMPTY;\n        this._fitInkBarToContent = false;\n        /** Whether tabs should be stretched to fill the header. */\n        this.stretchTabs = true;\n        /** Whether the tab group should grow to the size of the active tab. */\n        this.dynamicHeight = false;\n        this._selectedIndex = null;\n        /** Position of the tab header. */\n        this.headerPosition = 'above';\n        /**\n         * Whether pagination should be disabled. This can be used to avoid unnecessary\n         * layout recalculations if it's known that pagination won't be required.\n         */\n        this.disablePagination = false;\n        /** Whether ripples in the tab group are disabled. */\n        this.disableRipple = false;\n        /**\n         * By default tabs remove their content from the DOM while it's off-screen.\n         * Setting this to `true` will keep it in the DOM which will prevent elements\n         * like iframes and videos from reloading next time it comes back into the view.\n         */\n        this.preserveContent = false;\n        /** Output to enable support for two-way binding on `[(selectedIndex)]` */\n        this.selectedIndexChange = new EventEmitter();\n        /** Event emitted when focus has changed within a tab group. */\n        this.focusChange = new EventEmitter();\n        /** Event emitted when the body animation has completed */\n        this.animationDone = new EventEmitter();\n        /** Event emitted when the tab selection has changed. */\n        this.selectedTabChange = new EventEmitter(true);\n        /** Whether the tab group is rendered on the server. */\n        this._isServer = !inject(Platform).isBrowser;\n        this._groupId = nextId++;\n        this.animationDuration =\n            defaultConfig && defaultConfig.animationDuration ? defaultConfig.animationDuration : '500ms';\n        this.disablePagination =\n            defaultConfig && defaultConfig.disablePagination != null\n                ? defaultConfig.disablePagination\n                : false;\n        this.dynamicHeight =\n            defaultConfig && defaultConfig.dynamicHeight != null ? defaultConfig.dynamicHeight : false;\n        if (defaultConfig?.contentTabIndex != null) {\n            this.contentTabIndex = defaultConfig.contentTabIndex;\n        }\n        this.preserveContent = !!defaultConfig?.preserveContent;\n        this.fitInkBarToContent =\n            defaultConfig && defaultConfig.fitInkBarToContent != null\n                ? defaultConfig.fitInkBarToContent\n                : false;\n        this.stretchTabs =\n            defaultConfig && defaultConfig.stretchTabs != null ? defaultConfig.stretchTabs : true;\n    }\n    /**\n     * After the content is checked, this component knows what tabs have been defined\n     * and what the selected index should be. This is where we can know exactly what position\n     * each tab should be in according to the new selected index, and additionally we know how\n     * a new selected tab should transition in (from the left or right).\n     */\n    ngAfterContentChecked() {\n        // Don't clamp the `indexToSelect` immediately in the setter because it can happen that\n        // the amount of tabs changes before the actual change detection runs.\n        const indexToSelect = (this._indexToSelect = this._clampTabIndex(this._indexToSelect));\n        // If there is a change in selected index, emit a change event. Should not trigger if\n        // the selected index has not yet been initialized.\n        if (this._selectedIndex != indexToSelect) {\n            const isFirstRun = this._selectedIndex == null;\n            if (!isFirstRun) {\n                this.selectedTabChange.emit(this._createChangeEvent(indexToSelect));\n                // Preserve the height so page doesn't scroll up during tab change.\n                // Fixes https://stackblitz.com/edit/mat-tabs-scroll-page-top-on-tab-change\n                const wrapper = this._tabBodyWrapper.nativeElement;\n                wrapper.style.minHeight = wrapper.clientHeight + 'px';\n            }\n            // Changing these values after change detection has run\n            // since the checked content may contain references to them.\n            Promise.resolve().then(() => {\n                this._tabs.forEach((tab, index) => (tab.isActive = index === indexToSelect));\n                if (!isFirstRun) {\n                    this.selectedIndexChange.emit(indexToSelect);\n                    // Clear the min-height, this was needed during tab change to avoid\n                    // unnecessary scrolling.\n                    this._tabBodyWrapper.nativeElement.style.minHeight = '';\n                }\n            });\n        }\n        // Setup the position for each tab and optionally setup an origin on the next selected tab.\n        this._tabs.forEach((tab, index) => {\n            tab.position = index - indexToSelect;\n            // If there is already a selected tab, then set up an origin for the next selected tab\n            // if it doesn't have one already.\n            if (this._selectedIndex != null && tab.position == 0 && !tab.origin) {\n                tab.origin = indexToSelect - this._selectedIndex;\n            }\n        });\n        if (this._selectedIndex !== indexToSelect) {\n            this._selectedIndex = indexToSelect;\n            this._lastFocusedTabIndex = null;\n            this._changeDetectorRef.markForCheck();\n        }\n    }\n    ngAfterContentInit() {\n        this._subscribeToAllTabChanges();\n        this._subscribeToTabLabels();\n        // Subscribe to changes in the amount of tabs, in order to be\n        // able to re-render the content as new tabs are added or removed.\n        this._tabsSubscription = this._tabs.changes.subscribe(() => {\n            const indexToSelect = this._clampTabIndex(this._indexToSelect);\n            // Maintain the previously-selected tab if a new tab is added or removed and there is no\n            // explicit change that selects a different tab.\n            if (indexToSelect === this._selectedIndex) {\n                const tabs = this._tabs.toArray();\n                let selectedTab;\n                for (let i = 0; i < tabs.length; i++) {\n                    if (tabs[i].isActive) {\n                        // Assign both to the `_indexToSelect` and `_selectedIndex` so we don't fire a changed\n                        // event, otherwise the consumer may end up in an infinite loop in some edge cases like\n                        // adding a tab within the `selectedIndexChange` event.\n                        this._indexToSelect = this._selectedIndex = i;\n                        this._lastFocusedTabIndex = null;\n                        selectedTab = tabs[i];\n                        break;\n                    }\n                }\n                // If we haven't found an active tab and a tab exists at the selected index, it means\n                // that the active tab was swapped out. Since this won't be picked up by the rendering\n                // loop in `ngAfterContentChecked`, we need to sync it up manually.\n                if (!selectedTab && tabs[indexToSelect]) {\n                    Promise.resolve().then(() => {\n                        tabs[indexToSelect].isActive = true;\n                        this.selectedTabChange.emit(this._createChangeEvent(indexToSelect));\n                    });\n                }\n            }\n            this._changeDetectorRef.markForCheck();\n        });\n    }\n    /** Listens to changes in all of the tabs. */\n    _subscribeToAllTabChanges() {\n        // Since we use a query with `descendants: true` to pick up the tabs, we may end up catching\n        // some that are inside of nested tab groups. We filter them out manually by checking that\n        // the closest group to the tab is the current one.\n        this._allTabs.changes.pipe(startWith(this._allTabs)).subscribe((tabs) => {\n            this._tabs.reset(tabs.filter(tab => {\n                return tab._closestTabGroup === this || !tab._closestTabGroup;\n            }));\n            this._tabs.notifyOnChanges();\n        });\n    }\n    ngOnDestroy() {\n        this._tabs.destroy();\n        this._tabsSubscription.unsubscribe();\n        this._tabLabelSubscription.unsubscribe();\n    }\n    /** Re-aligns the ink bar to the selected tab element. */\n    realignInkBar() {\n        if (this._tabHeader) {\n            this._tabHeader._alignInkBarToSelectedTab();\n        }\n    }\n    /**\n     * Recalculates the tab group's pagination dimensions.\n     *\n     * WARNING: Calling this method can be very costly in terms of performance. It should be called\n     * as infrequently as possible from outside of the Tabs component as it causes a reflow of the\n     * page.\n     */\n    updatePagination() {\n        if (this._tabHeader) {\n            this._tabHeader.updatePagination();\n        }\n    }\n    /**\n     * Sets focus to a particular tab.\n     * @param index Index of the tab to be focused.\n     */\n    focusTab(index) {\n        const header = this._tabHeader;\n        if (header) {\n            header.focusIndex = index;\n        }\n    }\n    _focusChanged(index) {\n        this._lastFocusedTabIndex = index;\n        this.focusChange.emit(this._createChangeEvent(index));\n    }\n    _createChangeEvent(index) {\n        const event = new MatTabChangeEvent();\n        event.index = index;\n        if (this._tabs && this._tabs.length) {\n            event.tab = this._tabs.toArray()[index];\n        }\n        return event;\n    }\n    /**\n     * Subscribes to changes in the tab labels. This is needed, because the @Input for the label is\n     * on the MatTab component, whereas the data binding is inside the MatTabGroup. In order for the\n     * binding to be updated, we need to subscribe to changes in it and trigger change detection\n     * manually.\n     */\n    _subscribeToTabLabels() {\n        if (this._tabLabelSubscription) {\n            this._tabLabelSubscription.unsubscribe();\n        }\n        this._tabLabelSubscription = merge(...this._tabs.map(tab => tab._stateChanges)).subscribe(() => this._changeDetectorRef.markForCheck());\n    }\n    /** Clamps the given index to the bounds of 0 and the tabs length. */\n    _clampTabIndex(index) {\n        // Note the `|| 0`, which ensures that values like NaN can't get through\n        // and which would otherwise throw the component into an infinite loop\n        // (since Math.max(NaN, 0) === NaN).\n        return Math.min(this._tabs.length - 1, Math.max(index || 0, 0));\n    }\n    /** Returns a unique id for each tab label element */\n    _getTabLabelId(i) {\n        return `mat-tab-label-${this._groupId}-${i}`;\n    }\n    /** Returns a unique id for each tab content element */\n    _getTabContentId(i) {\n        return `mat-tab-content-${this._groupId}-${i}`;\n    }\n    /**\n     * Sets the height of the body wrapper to the height of the activating tab if dynamic\n     * height property is true.\n     */\n    _setTabBodyWrapperHeight(tabHeight) {\n        if (!this.dynamicHeight || !this._tabBodyWrapperHeight) {\n            return;\n        }\n        const wrapper = this._tabBodyWrapper.nativeElement;\n        wrapper.style.height = this._tabBodyWrapperHeight + 'px';\n        // This conditional forces the browser to paint the height so that\n        // the animation to the new height can have an origin.\n        if (this._tabBodyWrapper.nativeElement.offsetHeight) {\n            wrapper.style.height = tabHeight + 'px';\n        }\n    }\n    /** Removes the height of the tab body wrapper. */\n    _removeTabBodyWrapperHeight() {\n        const wrapper = this._tabBodyWrapper.nativeElement;\n        this._tabBodyWrapperHeight = wrapper.clientHeight;\n        wrapper.style.height = '';\n        this.animationDone.emit();\n    }\n    /** Handle click events, setting new selected index if appropriate. */\n    _handleClick(tab, tabHeader, index) {\n        tabHeader.focusIndex = index;\n        if (!tab.disabled) {\n            this.selectedIndex = index;\n        }\n    }\n    /** Retrieves the tabindex for the tab. */\n    _getTabIndex(index) {\n        const targetIndex = this._lastFocusedTabIndex ?? this.selectedIndex;\n        return index === targetIndex ? 0 : -1;\n    }\n    /** Callback for when the focused state of a tab has changed. */\n    _tabFocusChanged(focusOrigin, index) {\n        // Mouse/touch focus happens during the `mousedown`/`touchstart` phase which\n        // can cause the tab to be moved out from under the pointer, interrupting the\n        // click sequence (see #21898). We don't need to scroll the tab into view for\n        // such cases anyway, because it will be done when the tab becomes selected.\n        if (focusOrigin && focusOrigin !== 'mouse' && focusOrigin !== 'touch') {\n            this._tabHeader.focusIndex = index;\n        }\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatTabGroup, deps: [{ token: i0.ElementRef }, { token: i0.ChangeDetectorRef }, { token: MAT_TABS_CONFIG, optional: true }, { token: ANIMATION_MODULE_TYPE, optional: true }], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"17.0.0\", version: \"17.2.0\", type: MatTabGroup, isStandalone: true, selector: \"mat-tab-group\", inputs: { color: \"color\", fitInkBarToContent: [\"fitInkBarToContent\", \"fitInkBarToContent\", booleanAttribute], stretchTabs: [\"mat-stretch-tabs\", \"stretchTabs\", booleanAttribute], dynamicHeight: [\"dynamicHeight\", \"dynamicHeight\", booleanAttribute], selectedIndex: [\"selectedIndex\", \"selectedIndex\", numberAttribute], headerPosition: \"headerPosition\", animationDuration: \"animationDuration\", contentTabIndex: [\"contentTabIndex\", \"contentTabIndex\", numberAttribute], disablePagination: [\"disablePagination\", \"disablePagination\", booleanAttribute], disableRipple: [\"disableRipple\", \"disableRipple\", booleanAttribute], preserveContent: [\"preserveContent\", \"preserveContent\", booleanAttribute], backgroundColor: \"backgroundColor\" }, outputs: { selectedIndexChange: \"selectedIndexChange\", focusChange: \"focusChange\", animationDone: \"animationDone\", selectedTabChange: \"selectedTabChange\" }, host: { properties: { \"class\": \"\\\"mat-\\\" + (color || \\\"primary\\\")\", \"class.mat-mdc-tab-group-dynamic-height\": \"dynamicHeight\", \"class.mat-mdc-tab-group-inverted-header\": \"headerPosition === \\\"below\\\"\", \"class.mat-mdc-tab-group-stretch-tabs\": \"stretchTabs\", \"style.--mat-tab-animation-duration\": \"animationDuration\" }, classAttribute: \"mat-mdc-tab-group\" }, providers: [\n            {\n                provide: MAT_TAB_GROUP,\n                useExisting: MatTabGroup,\n            },\n        ], queries: [{ propertyName: \"_allTabs\", predicate: MatTab, descendants: true }], viewQueries: [{ propertyName: \"_tabBodyWrapper\", first: true, predicate: [\"tabBodyWrapper\"], descendants: true }, { propertyName: \"_tabHeader\", first: true, predicate: [\"tabHeader\"], descendants: true }], exportAs: [\"matTabGroup\"], ngImport: i0, template: \"<mat-tab-header #tabHeader\\n                [selectedIndex]=\\\"selectedIndex || 0\\\"\\n                [disableRipple]=\\\"disableRipple\\\"\\n                [disablePagination]=\\\"disablePagination\\\"\\n                (indexFocused)=\\\"_focusChanged($event)\\\"\\n                (selectFocusedIndex)=\\\"selectedIndex = $event\\\">\\n\\n  @for (tab of _tabs; track tab; let i = $index) {\\n    <div class=\\\"mdc-tab mat-mdc-tab mat-mdc-focus-indicator\\\"\\n        #tabNode\\n        role=\\\"tab\\\"\\n        matTabLabelWrapper\\n        cdkMonitorElementFocus\\n        [id]=\\\"_getTabLabelId(i)\\\"\\n        [attr.tabIndex]=\\\"_getTabIndex(i)\\\"\\n        [attr.aria-posinset]=\\\"i + 1\\\"\\n        [attr.aria-setsize]=\\\"_tabs.length\\\"\\n        [attr.aria-controls]=\\\"_getTabContentId(i)\\\"\\n        [attr.aria-selected]=\\\"selectedIndex === i\\\"\\n        [attr.aria-label]=\\\"tab.ariaLabel || null\\\"\\n        [attr.aria-labelledby]=\\\"(!tab.ariaLabel && tab.ariaLabelledby) ? tab.ariaLabelledby : null\\\"\\n        [class.mdc-tab--active]=\\\"selectedIndex === i\\\"\\n        [class]=\\\"tab.labelClass\\\"\\n        [disabled]=\\\"tab.disabled\\\"\\n        [fitInkBarToContent]=\\\"fitInkBarToContent\\\"\\n        (click)=\\\"_handleClick(tab, tabHeader, i)\\\"\\n        (cdkFocusChange)=\\\"_tabFocusChanged($event, i)\\\">\\n      <span class=\\\"mdc-tab__ripple\\\"></span>\\n\\n      <!-- Needs to be a separate element, because we can't put\\n          `overflow: hidden` on tab due to the ink bar. -->\\n      <div\\n        class=\\\"mat-mdc-tab-ripple\\\"\\n        mat-ripple\\n        [matRippleTrigger]=\\\"tabNode\\\"\\n        [matRippleDisabled]=\\\"tab.disabled || disableRipple\\\"></div>\\n\\n      <span class=\\\"mdc-tab__content\\\">\\n        <span class=\\\"mdc-tab__text-label\\\">\\n          <!--\\n            If there is a label template, use it, otherwise fall back to the text label.\\n            Note that we don't have indentation around the text label, because it adds\\n            whitespace around the text which breaks some internal tests.\\n          -->\\n          @if (tab.templateLabel) {\\n            <ng-template [cdkPortalOutlet]=\\\"tab.templateLabel\\\"></ng-template>\\n          } @else {{{tab.textLabel}}}\\n        </span>\\n      </span>\\n    </div>\\n  }\\n</mat-tab-header>\\n\\n<!--\\n  We need to project the content somewhere to avoid hydration errors. Some observations:\\n  1. This is only necessary on the server.\\n  2. We get a hydration error if there aren't any nodes after the `ng-content`.\\n  3. We get a hydration error if `ng-content` is wrapped in another element.\\n-->\\n@if (_isServer) {\\n  <ng-content/>\\n}\\n\\n<div\\n  class=\\\"mat-mdc-tab-body-wrapper\\\"\\n  [class._mat-animation-noopable]=\\\"_animationMode === 'NoopAnimations'\\\"\\n  #tabBodyWrapper>\\n  @for (tab of _tabs; track tab; let i = $index) {\\n    <mat-tab-body role=\\\"tabpanel\\\"\\n                 [id]=\\\"_getTabContentId(i)\\\"\\n                 [attr.tabindex]=\\\"(contentTabIndex != null && selectedIndex === i) ? contentTabIndex : null\\\"\\n                 [attr.aria-labelledby]=\\\"_getTabLabelId(i)\\\"\\n                 [attr.aria-hidden]=\\\"selectedIndex !== i\\\"\\n                 [class.mat-mdc-tab-body-active]=\\\"selectedIndex === i\\\"\\n                 [class]=\\\"tab.bodyClass\\\"\\n                 [content]=\\\"tab.content!\\\"\\n                 [position]=\\\"tab.position!\\\"\\n                 [origin]=\\\"tab.origin\\\"\\n                 [animationDuration]=\\\"animationDuration\\\"\\n                 [preserveContent]=\\\"preserveContent\\\"\\n                 (_onCentered)=\\\"_removeTabBodyWrapperHeight()\\\"\\n                 (_onCentering)=\\\"_setTabBodyWrapperHeight($event)\\\">\\n    </mat-tab-body>\\n  }\\n</div>\\n\", styles: [\".mdc-tab{min-width:90px;padding-right:24px;padding-left:24px;display:flex;flex:1 0 auto;justify-content:center;box-sizing:border-box;margin:0;padding-top:0;padding-bottom:0;border:none;outline:none;text-align:center;white-space:nowrap;cursor:pointer;-webkit-appearance:none;z-index:1}.mdc-tab::-moz-focus-inner{padding:0;border:0}.mdc-tab[hidden]{display:none}.mdc-tab--min-width{flex:0 1 auto}.mdc-tab__content{display:flex;align-items:center;justify-content:center;height:inherit;pointer-events:none}.mdc-tab__text-label{transition:150ms color linear;display:inline-block;line-height:1;z-index:2}.mdc-tab__icon{transition:150ms color linear;z-index:2}.mdc-tab--stacked .mdc-tab__content{flex-direction:column;align-items:center;justify-content:center}.mdc-tab--stacked .mdc-tab__text-label{padding-top:6px;padding-bottom:4px}.mdc-tab--active .mdc-tab__text-label,.mdc-tab--active .mdc-tab__icon{transition-delay:100ms}.mdc-tab:not(.mdc-tab--stacked) .mdc-tab__icon+.mdc-tab__text-label{padding-left:8px;padding-right:0}[dir=rtl] .mdc-tab:not(.mdc-tab--stacked) .mdc-tab__icon+.mdc-tab__text-label,.mdc-tab:not(.mdc-tab--stacked) .mdc-tab__icon+.mdc-tab__text-label[dir=rtl]{padding-left:0;padding-right:8px}.mdc-tab-indicator{display:flex;position:absolute;top:0;left:0;justify-content:center;width:100%;height:100%;pointer-events:none;z-index:1}.mdc-tab-indicator__content{transform-origin:left;opacity:0}.mdc-tab-indicator__content--underline{align-self:flex-end;box-sizing:border-box;width:100%;border-top-style:solid}.mdc-tab-indicator__content--icon{align-self:center;margin:0 auto}.mdc-tab-indicator--active .mdc-tab-indicator__content{opacity:1}.mdc-tab-indicator .mdc-tab-indicator__content{transition:250ms transform cubic-bezier(0.4, 0, 0.2, 1)}.mdc-tab-indicator--no-transition .mdc-tab-indicator__content{transition:none}.mdc-tab-indicator--fade .mdc-tab-indicator__content{transition:150ms opacity linear}.mdc-tab-indicator--active.mdc-tab-indicator--fade .mdc-tab-indicator__content{transition-delay:100ms}.mat-mdc-tab-ripple{position:absolute;top:0;left:0;bottom:0;right:0;pointer-events:none}.mat-mdc-tab{-webkit-tap-highlight-color:rgba(0,0,0,0);-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-decoration:none;background:none;font-family:var(--mat-tab-header-label-text-font);font-size:var(--mat-tab-header-label-text-size);letter-spacing:var(--mat-tab-header-label-text-tracking);line-height:var(--mat-tab-header-label-text-line-height);font-weight:var(--mat-tab-header-label-text-weight)}.mat-mdc-tab .mdc-tab-indicator__content--underline{border-color:var(--mdc-tab-indicator-active-indicator-color)}.mat-mdc-tab .mdc-tab-indicator__content--underline{border-top-width:var(--mdc-tab-indicator-active-indicator-height)}.mat-mdc-tab .mdc-tab-indicator__content--underline{border-radius:var(--mdc-tab-indicator-active-indicator-shape)}.mat-mdc-tab:not(.mdc-tab--stacked){height:var(--mdc-secondary-navigation-tab-container-height)}.mat-mdc-tab:not(:disabled).mdc-tab--active .mdc-tab__icon{fill:currentColor}.mat-mdc-tab:not(:disabled):hover.mdc-tab--active .mdc-tab__icon{fill:currentColor}.mat-mdc-tab:not(:disabled):focus.mdc-tab--active .mdc-tab__icon{fill:currentColor}.mat-mdc-tab:not(:disabled):active.mdc-tab--active .mdc-tab__icon{fill:currentColor}.mat-mdc-tab:disabled.mdc-tab--active .mdc-tab__icon{fill:currentColor}.mat-mdc-tab:not(:disabled):not(.mdc-tab--active) .mdc-tab__icon{fill:currentColor}.mat-mdc-tab:not(:disabled):hover:not(.mdc-tab--active) .mdc-tab__icon{fill:currentColor}.mat-mdc-tab:not(:disabled):focus:not(.mdc-tab--active) .mdc-tab__icon{fill:currentColor}.mat-mdc-tab:not(:disabled):active:not(.mdc-tab--active) .mdc-tab__icon{fill:currentColor}.mat-mdc-tab:disabled:not(.mdc-tab--active) .mdc-tab__icon{fill:currentColor}.mat-mdc-tab.mdc-tab{flex-grow:0}.mat-mdc-tab:hover .mdc-tab__text-label{color:var(--mat-tab-header-inactive-hover-label-text-color)}.mat-mdc-tab:focus .mdc-tab__text-label{color:var(--mat-tab-header-inactive-focus-label-text-color)}.mat-mdc-tab.mdc-tab--active .mdc-tab__text-label{color:var(--mat-tab-header-active-label-text-color)}.mat-mdc-tab.mdc-tab--active .mdc-tab__ripple::before,.mat-mdc-tab.mdc-tab--active .mat-ripple-element{background-color:var(--mat-tab-header-active-ripple-color)}.mat-mdc-tab.mdc-tab--active:hover .mdc-tab__text-label{color:var(--mat-tab-header-active-hover-label-text-color)}.mat-mdc-tab.mdc-tab--active:hover .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-header-active-hover-indicator-color)}.mat-mdc-tab.mdc-tab--active:focus .mdc-tab__text-label{color:var(--mat-tab-header-active-focus-label-text-color)}.mat-mdc-tab.mdc-tab--active:focus .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-header-active-focus-indicator-color)}.mat-mdc-tab.mat-mdc-tab-disabled{opacity:.4;pointer-events:none}.mat-mdc-tab.mat-mdc-tab-disabled .mdc-tab__content{pointer-events:none}.mat-mdc-tab.mat-mdc-tab-disabled .mdc-tab__ripple::before,.mat-mdc-tab.mat-mdc-tab-disabled .mat-ripple-element{background-color:var(--mat-tab-header-disabled-ripple-color)}.mat-mdc-tab .mdc-tab__ripple::before{content:\\\"\\\";display:block;position:absolute;top:0;left:0;right:0;bottom:0;opacity:0;pointer-events:none;background-color:var(--mat-tab-header-inactive-ripple-color)}.mat-mdc-tab .mdc-tab__text-label{color:var(--mat-tab-header-inactive-label-text-color);display:inline-flex;align-items:center}.mat-mdc-tab .mdc-tab__content{position:relative;pointer-events:auto}.mat-mdc-tab:hover .mdc-tab__ripple::before{opacity:.04}.mat-mdc-tab.cdk-program-focused .mdc-tab__ripple::before,.mat-mdc-tab.cdk-keyboard-focused .mdc-tab__ripple::before{opacity:.12}.mat-mdc-tab .mat-ripple-element{opacity:.12;background-color:var(--mat-tab-header-inactive-ripple-color)}.mat-mdc-tab-group.mat-mdc-tab-group-stretch-tabs>.mat-mdc-tab-header .mat-mdc-tab{flex-grow:1}.mat-mdc-tab-group{display:flex;flex-direction:column;max-width:100%}.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header-pagination{background-color:var(--mat-tab-header-with-background-background-color)}.mat-mdc-tab-group.mat-tabs-with-background.mat-primary>.mat-mdc-tab-header .mat-mdc-tab .mdc-tab__text-label{color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-group.mat-tabs-with-background.mat-primary>.mat-mdc-tab-header .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-group.mat-tabs-with-background:not(.mat-primary)>.mat-mdc-tab-header .mat-mdc-tab:not(.mdc-tab--active) .mdc-tab__text-label{color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-group.mat-tabs-with-background:not(.mat-primary)>.mat-mdc-tab-header .mat-mdc-tab:not(.mdc-tab--active) .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header .mat-mdc-focus-indicator::before,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-mdc-focus-indicator::before{border-color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header .mat-ripple-element,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header .mdc-tab__ripple::before,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-ripple-element,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mdc-tab__ripple::before{background-color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-mdc-tab-header-pagination-chevron{color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-group.mat-mdc-tab-group-inverted-header{flex-direction:column-reverse}.mat-mdc-tab-group.mat-mdc-tab-group-inverted-header .mdc-tab-indicator__content--underline{align-self:flex-start}.mat-mdc-tab-body-wrapper{position:relative;overflow:hidden;display:flex;transition:height 500ms cubic-bezier(0.35, 0, 0.25, 1)}.mat-mdc-tab-body-wrapper._mat-animation-noopable{transition:none !important;animation:none !important}\"], dependencies: [{ kind: \"component\", type: MatTabHeader, selector: \"mat-tab-header\", inputs: [\"disableRipple\"] }, { kind: \"directive\", type: MatTabLabelWrapper, selector: \"[matTabLabelWrapper]\", inputs: [\"disabled\"] }, { kind: \"directive\", type: CdkMonitorFocus, selector: \"[cdkMonitorElementFocus], [cdkMonitorSubtreeFocus]\", outputs: [\"cdkFocusChange\"], exportAs: [\"cdkMonitorFocus\"] }, { kind: \"directive\", type: MatRipple, selector: \"[mat-ripple], [matRipple]\", inputs: [\"matRippleColor\", \"matRippleUnbounded\", \"matRippleCentered\", \"matRippleRadius\", \"matRippleAnimation\", \"matRippleDisabled\", \"matRippleTrigger\"], exportAs: [\"matRipple\"] }, { kind: \"directive\", type: CdkPortalOutlet, selector: \"[cdkPortalOutlet]\", inputs: [\"cdkPortalOutlet\"], outputs: [\"attached\"], exportAs: [\"cdkPortalOutlet\"] }, { kind: \"component\", type: MatTabBody, selector: \"mat-tab-body\", inputs: [\"content\", \"origin\", \"animationDuration\", \"preserveContent\", \"position\"], outputs: [\"_onCentering\", \"_beforeCentering\", \"_afterLeavingCenter\", \"_onCentered\"] }], changeDetection: i0.ChangeDetectionStrategy.Default, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatTabGroup, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-tab-group', exportAs: 'matTabGroup', encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.Default, providers: [\n                        {\n                            provide: MAT_TAB_GROUP,\n                            useExisting: MatTabGroup,\n                        },\n                    ], host: {\n                        'class': 'mat-mdc-tab-group',\n                        '[class]': '\"mat-\" + (color || \"primary\")',\n                        '[class.mat-mdc-tab-group-dynamic-height]': 'dynamicHeight',\n                        '[class.mat-mdc-tab-group-inverted-header]': 'headerPosition === \"below\"',\n                        '[class.mat-mdc-tab-group-stretch-tabs]': 'stretchTabs',\n                        '[style.--mat-tab-animation-duration]': 'animationDuration',\n                    }, standalone: true, imports: [\n                        MatTabHeader,\n                        MatTabLabelWrapper,\n                        CdkMonitorFocus,\n                        MatRipple,\n                        CdkPortalOutlet,\n                        MatTabBody,\n                    ], template: \"<mat-tab-header #tabHeader\\n                [selectedIndex]=\\\"selectedIndex || 0\\\"\\n                [disableRipple]=\\\"disableRipple\\\"\\n                [disablePagination]=\\\"disablePagination\\\"\\n                (indexFocused)=\\\"_focusChanged($event)\\\"\\n                (selectFocusedIndex)=\\\"selectedIndex = $event\\\">\\n\\n  @for (tab of _tabs; track tab; let i = $index) {\\n    <div class=\\\"mdc-tab mat-mdc-tab mat-mdc-focus-indicator\\\"\\n        #tabNode\\n        role=\\\"tab\\\"\\n        matTabLabelWrapper\\n        cdkMonitorElementFocus\\n        [id]=\\\"_getTabLabelId(i)\\\"\\n        [attr.tabIndex]=\\\"_getTabIndex(i)\\\"\\n        [attr.aria-posinset]=\\\"i + 1\\\"\\n        [attr.aria-setsize]=\\\"_tabs.length\\\"\\n        [attr.aria-controls]=\\\"_getTabContentId(i)\\\"\\n        [attr.aria-selected]=\\\"selectedIndex === i\\\"\\n        [attr.aria-label]=\\\"tab.ariaLabel || null\\\"\\n        [attr.aria-labelledby]=\\\"(!tab.ariaLabel && tab.ariaLabelledby) ? tab.ariaLabelledby : null\\\"\\n        [class.mdc-tab--active]=\\\"selectedIndex === i\\\"\\n        [class]=\\\"tab.labelClass\\\"\\n        [disabled]=\\\"tab.disabled\\\"\\n        [fitInkBarToContent]=\\\"fitInkBarToContent\\\"\\n        (click)=\\\"_handleClick(tab, tabHeader, i)\\\"\\n        (cdkFocusChange)=\\\"_tabFocusChanged($event, i)\\\">\\n      <span class=\\\"mdc-tab__ripple\\\"></span>\\n\\n      <!-- Needs to be a separate element, because we can't put\\n          `overflow: hidden` on tab due to the ink bar. -->\\n      <div\\n        class=\\\"mat-mdc-tab-ripple\\\"\\n        mat-ripple\\n        [matRippleTrigger]=\\\"tabNode\\\"\\n        [matRippleDisabled]=\\\"tab.disabled || disableRipple\\\"></div>\\n\\n      <span class=\\\"mdc-tab__content\\\">\\n        <span class=\\\"mdc-tab__text-label\\\">\\n          <!--\\n            If there is a label template, use it, otherwise fall back to the text label.\\n            Note that we don't have indentation around the text label, because it adds\\n            whitespace around the text which breaks some internal tests.\\n          -->\\n          @if (tab.templateLabel) {\\n            <ng-template [cdkPortalOutlet]=\\\"tab.templateLabel\\\"></ng-template>\\n          } @else {{{tab.textLabel}}}\\n        </span>\\n      </span>\\n    </div>\\n  }\\n</mat-tab-header>\\n\\n<!--\\n  We need to project the content somewhere to avoid hydration errors. Some observations:\\n  1. This is only necessary on the server.\\n  2. We get a hydration error if there aren't any nodes after the `ng-content`.\\n  3. We get a hydration error if `ng-content` is wrapped in another element.\\n-->\\n@if (_isServer) {\\n  <ng-content/>\\n}\\n\\n<div\\n  class=\\\"mat-mdc-tab-body-wrapper\\\"\\n  [class._mat-animation-noopable]=\\\"_animationMode === 'NoopAnimations'\\\"\\n  #tabBodyWrapper>\\n  @for (tab of _tabs; track tab; let i = $index) {\\n    <mat-tab-body role=\\\"tabpanel\\\"\\n                 [id]=\\\"_getTabContentId(i)\\\"\\n                 [attr.tabindex]=\\\"(contentTabIndex != null && selectedIndex === i) ? contentTabIndex : null\\\"\\n                 [attr.aria-labelledby]=\\\"_getTabLabelId(i)\\\"\\n                 [attr.aria-hidden]=\\\"selectedIndex !== i\\\"\\n                 [class.mat-mdc-tab-body-active]=\\\"selectedIndex === i\\\"\\n                 [class]=\\\"tab.bodyClass\\\"\\n                 [content]=\\\"tab.content!\\\"\\n                 [position]=\\\"tab.position!\\\"\\n                 [origin]=\\\"tab.origin\\\"\\n                 [animationDuration]=\\\"animationDuration\\\"\\n                 [preserveContent]=\\\"preserveContent\\\"\\n                 (_onCentered)=\\\"_removeTabBodyWrapperHeight()\\\"\\n                 (_onCentering)=\\\"_setTabBodyWrapperHeight($event)\\\">\\n    </mat-tab-body>\\n  }\\n</div>\\n\", styles: [\".mdc-tab{min-width:90px;padding-right:24px;padding-left:24px;display:flex;flex:1 0 auto;justify-content:center;box-sizing:border-box;margin:0;padding-top:0;padding-bottom:0;border:none;outline:none;text-align:center;white-space:nowrap;cursor:pointer;-webkit-appearance:none;z-index:1}.mdc-tab::-moz-focus-inner{padding:0;border:0}.mdc-tab[hidden]{display:none}.mdc-tab--min-width{flex:0 1 auto}.mdc-tab__content{display:flex;align-items:center;justify-content:center;height:inherit;pointer-events:none}.mdc-tab__text-label{transition:150ms color linear;display:inline-block;line-height:1;z-index:2}.mdc-tab__icon{transition:150ms color linear;z-index:2}.mdc-tab--stacked .mdc-tab__content{flex-direction:column;align-items:center;justify-content:center}.mdc-tab--stacked .mdc-tab__text-label{padding-top:6px;padding-bottom:4px}.mdc-tab--active .mdc-tab__text-label,.mdc-tab--active .mdc-tab__icon{transition-delay:100ms}.mdc-tab:not(.mdc-tab--stacked) .mdc-tab__icon+.mdc-tab__text-label{padding-left:8px;padding-right:0}[dir=rtl] .mdc-tab:not(.mdc-tab--stacked) .mdc-tab__icon+.mdc-tab__text-label,.mdc-tab:not(.mdc-tab--stacked) .mdc-tab__icon+.mdc-tab__text-label[dir=rtl]{padding-left:0;padding-right:8px}.mdc-tab-indicator{display:flex;position:absolute;top:0;left:0;justify-content:center;width:100%;height:100%;pointer-events:none;z-index:1}.mdc-tab-indicator__content{transform-origin:left;opacity:0}.mdc-tab-indicator__content--underline{align-self:flex-end;box-sizing:border-box;width:100%;border-top-style:solid}.mdc-tab-indicator__content--icon{align-self:center;margin:0 auto}.mdc-tab-indicator--active .mdc-tab-indicator__content{opacity:1}.mdc-tab-indicator .mdc-tab-indicator__content{transition:250ms transform cubic-bezier(0.4, 0, 0.2, 1)}.mdc-tab-indicator--no-transition .mdc-tab-indicator__content{transition:none}.mdc-tab-indicator--fade .mdc-tab-indicator__content{transition:150ms opacity linear}.mdc-tab-indicator--active.mdc-tab-indicator--fade .mdc-tab-indicator__content{transition-delay:100ms}.mat-mdc-tab-ripple{position:absolute;top:0;left:0;bottom:0;right:0;pointer-events:none}.mat-mdc-tab{-webkit-tap-highlight-color:rgba(0,0,0,0);-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-decoration:none;background:none;font-family:var(--mat-tab-header-label-text-font);font-size:var(--mat-tab-header-label-text-size);letter-spacing:var(--mat-tab-header-label-text-tracking);line-height:var(--mat-tab-header-label-text-line-height);font-weight:var(--mat-tab-header-label-text-weight)}.mat-mdc-tab .mdc-tab-indicator__content--underline{border-color:var(--mdc-tab-indicator-active-indicator-color)}.mat-mdc-tab .mdc-tab-indicator__content--underline{border-top-width:var(--mdc-tab-indicator-active-indicator-height)}.mat-mdc-tab .mdc-tab-indicator__content--underline{border-radius:var(--mdc-tab-indicator-active-indicator-shape)}.mat-mdc-tab:not(.mdc-tab--stacked){height:var(--mdc-secondary-navigation-tab-container-height)}.mat-mdc-tab:not(:disabled).mdc-tab--active .mdc-tab__icon{fill:currentColor}.mat-mdc-tab:not(:disabled):hover.mdc-tab--active .mdc-tab__icon{fill:currentColor}.mat-mdc-tab:not(:disabled):focus.mdc-tab--active .mdc-tab__icon{fill:currentColor}.mat-mdc-tab:not(:disabled):active.mdc-tab--active .mdc-tab__icon{fill:currentColor}.mat-mdc-tab:disabled.mdc-tab--active .mdc-tab__icon{fill:currentColor}.mat-mdc-tab:not(:disabled):not(.mdc-tab--active) .mdc-tab__icon{fill:currentColor}.mat-mdc-tab:not(:disabled):hover:not(.mdc-tab--active) .mdc-tab__icon{fill:currentColor}.mat-mdc-tab:not(:disabled):focus:not(.mdc-tab--active) .mdc-tab__icon{fill:currentColor}.mat-mdc-tab:not(:disabled):active:not(.mdc-tab--active) .mdc-tab__icon{fill:currentColor}.mat-mdc-tab:disabled:not(.mdc-tab--active) .mdc-tab__icon{fill:currentColor}.mat-mdc-tab.mdc-tab{flex-grow:0}.mat-mdc-tab:hover .mdc-tab__text-label{color:var(--mat-tab-header-inactive-hover-label-text-color)}.mat-mdc-tab:focus .mdc-tab__text-label{color:var(--mat-tab-header-inactive-focus-label-text-color)}.mat-mdc-tab.mdc-tab--active .mdc-tab__text-label{color:var(--mat-tab-header-active-label-text-color)}.mat-mdc-tab.mdc-tab--active .mdc-tab__ripple::before,.mat-mdc-tab.mdc-tab--active .mat-ripple-element{background-color:var(--mat-tab-header-active-ripple-color)}.mat-mdc-tab.mdc-tab--active:hover .mdc-tab__text-label{color:var(--mat-tab-header-active-hover-label-text-color)}.mat-mdc-tab.mdc-tab--active:hover .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-header-active-hover-indicator-color)}.mat-mdc-tab.mdc-tab--active:focus .mdc-tab__text-label{color:var(--mat-tab-header-active-focus-label-text-color)}.mat-mdc-tab.mdc-tab--active:focus .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-header-active-focus-indicator-color)}.mat-mdc-tab.mat-mdc-tab-disabled{opacity:.4;pointer-events:none}.mat-mdc-tab.mat-mdc-tab-disabled .mdc-tab__content{pointer-events:none}.mat-mdc-tab.mat-mdc-tab-disabled .mdc-tab__ripple::before,.mat-mdc-tab.mat-mdc-tab-disabled .mat-ripple-element{background-color:var(--mat-tab-header-disabled-ripple-color)}.mat-mdc-tab .mdc-tab__ripple::before{content:\\\"\\\";display:block;position:absolute;top:0;left:0;right:0;bottom:0;opacity:0;pointer-events:none;background-color:var(--mat-tab-header-inactive-ripple-color)}.mat-mdc-tab .mdc-tab__text-label{color:var(--mat-tab-header-inactive-label-text-color);display:inline-flex;align-items:center}.mat-mdc-tab .mdc-tab__content{position:relative;pointer-events:auto}.mat-mdc-tab:hover .mdc-tab__ripple::before{opacity:.04}.mat-mdc-tab.cdk-program-focused .mdc-tab__ripple::before,.mat-mdc-tab.cdk-keyboard-focused .mdc-tab__ripple::before{opacity:.12}.mat-mdc-tab .mat-ripple-element{opacity:.12;background-color:var(--mat-tab-header-inactive-ripple-color)}.mat-mdc-tab-group.mat-mdc-tab-group-stretch-tabs>.mat-mdc-tab-header .mat-mdc-tab{flex-grow:1}.mat-mdc-tab-group{display:flex;flex-direction:column;max-width:100%}.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header-pagination{background-color:var(--mat-tab-header-with-background-background-color)}.mat-mdc-tab-group.mat-tabs-with-background.mat-primary>.mat-mdc-tab-header .mat-mdc-tab .mdc-tab__text-label{color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-group.mat-tabs-with-background.mat-primary>.mat-mdc-tab-header .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-group.mat-tabs-with-background:not(.mat-primary)>.mat-mdc-tab-header .mat-mdc-tab:not(.mdc-tab--active) .mdc-tab__text-label{color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-group.mat-tabs-with-background:not(.mat-primary)>.mat-mdc-tab-header .mat-mdc-tab:not(.mdc-tab--active) .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header .mat-mdc-focus-indicator::before,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-mdc-focus-indicator::before{border-color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header .mat-ripple-element,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header .mdc-tab__ripple::before,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-ripple-element,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mdc-tab__ripple::before{background-color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-mdc-tab-header-pagination-chevron{color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-group.mat-mdc-tab-group-inverted-header{flex-direction:column-reverse}.mat-mdc-tab-group.mat-mdc-tab-group-inverted-header .mdc-tab-indicator__content--underline{align-self:flex-start}.mat-mdc-tab-body-wrapper{position:relative;overflow:hidden;display:flex;transition:height 500ms cubic-bezier(0.35, 0, 0.25, 1)}.mat-mdc-tab-body-wrapper._mat-animation-noopable{transition:none !important;animation:none !important}\"] }]\n        }], ctorParameters: () => [{ type: i0.ElementRef }, { type: i0.ChangeDetectorRef }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [MAT_TABS_CONFIG]\n                }, {\n                    type: Optional\n                }] }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [ANIMATION_MODULE_TYPE]\n                }] }], propDecorators: { _allTabs: [{\n                type: ContentChildren,\n                args: [MatTab, { descendants: true }]\n            }], _tabBodyWrapper: [{\n                type: ViewChild,\n                args: ['tabBodyWrapper']\n            }], _tabHeader: [{\n                type: ViewChild,\n                args: ['tabHeader']\n            }], color: [{\n                type: Input\n            }], fitInkBarToContent: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], stretchTabs: [{\n                type: Input,\n                args: [{ alias: 'mat-stretch-tabs', transform: booleanAttribute }]\n            }], dynamicHeight: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], selectedIndex: [{\n                type: Input,\n                args: [{ transform: numberAttribute }]\n            }], headerPosition: [{\n                type: Input\n            }], animationDuration: [{\n                type: Input\n            }], contentTabIndex: [{\n                type: Input,\n                args: [{ transform: numberAttribute }]\n            }], disablePagination: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], disableRipple: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], preserveContent: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], backgroundColor: [{\n                type: Input\n            }], selectedIndexChange: [{\n                type: Output\n            }], focusChange: [{\n                type: Output\n            }], animationDone: [{\n                type: Output\n            }], selectedTabChange: [{\n                type: Output\n            }] } });\n/** A simple change event emitted on focus or selection changes. */\nclass MatTabChangeEvent {\n}\n\n// Increasing integer for generating unique ids for tab nav components.\nlet nextUniqueId = 0;\n/**\n * Navigation component matching the styles of the tab group header.\n * Provides anchored navigation with animated ink bar.\n */\nclass MatTabNav extends MatPaginatedTabHeader {\n    /** Whether the ink bar should fit its width to the size of the tab label content. */\n    get fitInkBarToContent() {\n        return this._fitInkBarToContent.value;\n    }\n    set fitInkBarToContent(value) {\n        this._fitInkBarToContent.next(value);\n        this._changeDetectorRef.markForCheck();\n    }\n    get animationDuration() {\n        return this._animationDuration;\n    }\n    set animationDuration(value) {\n        const stringValue = value + '';\n        this._animationDuration = /^\\d+$/.test(stringValue) ? value + 'ms' : stringValue;\n    }\n    /** Background color of the tab nav. */\n    get backgroundColor() {\n        return this._backgroundColor;\n    }\n    set backgroundColor(value) {\n        const classList = this._elementRef.nativeElement.classList;\n        classList.remove('mat-tabs-with-background', `mat-background-${this.backgroundColor}`);\n        if (value) {\n            classList.add('mat-tabs-with-background', `mat-background-${value}`);\n        }\n        this._backgroundColor = value;\n    }\n    constructor(elementRef, dir, ngZone, changeDetectorRef, viewportRuler, platform, animationMode, defaultConfig) {\n        super(elementRef, changeDetectorRef, viewportRuler, dir, ngZone, platform, animationMode);\n        this._fitInkBarToContent = new BehaviorSubject(false);\n        /** Whether tabs should be stretched to fill the header. */\n        this.stretchTabs = true;\n        /** Whether the ripple effect is disabled or not. */\n        this.disableRipple = false;\n        /** Theme color of the nav bar. */\n        this.color = 'primary';\n        this.disablePagination =\n            defaultConfig && defaultConfig.disablePagination != null\n                ? defaultConfig.disablePagination\n                : false;\n        this.fitInkBarToContent =\n            defaultConfig && defaultConfig.fitInkBarToContent != null\n                ? defaultConfig.fitInkBarToContent\n                : false;\n        this.stretchTabs =\n            defaultConfig && defaultConfig.stretchTabs != null ? defaultConfig.stretchTabs : true;\n    }\n    _itemSelected() {\n        // noop\n    }\n    ngAfterContentInit() {\n        this._inkBar = new MatInkBar(this._items);\n        // We need this to run before the `changes` subscription in parent to ensure that the\n        // selectedIndex is up-to-date by the time the super class starts looking for it.\n        this._items.changes.pipe(startWith(null), takeUntil(this._destroyed)).subscribe(() => {\n            this.updateActiveLink();\n        });\n        super.ngAfterContentInit();\n    }\n    ngAfterViewInit() {\n        if (!this.tabPanel && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw new Error('A mat-tab-nav-panel must be specified via [tabPanel].');\n        }\n        super.ngAfterViewInit();\n    }\n    /** Notifies the component that the active link has been changed. */\n    updateActiveLink() {\n        if (!this._items) {\n            return;\n        }\n        const items = this._items.toArray();\n        for (let i = 0; i < items.length; i++) {\n            if (items[i].active) {\n                this.selectedIndex = i;\n                this._changeDetectorRef.markForCheck();\n                if (this.tabPanel) {\n                    this.tabPanel._activeTabId = items[i].id;\n                }\n                return;\n            }\n        }\n        // The ink bar should hide itself if no items are active.\n        this.selectedIndex = -1;\n        this._inkBar.hide();\n    }\n    _getRole() {\n        return this.tabPanel ? 'tablist' : this._elementRef.nativeElement.getAttribute('role');\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatTabNav, deps: [{ token: i0.ElementRef }, { token: i2.Directionality, optional: true }, { token: i0.NgZone }, { token: i0.ChangeDetectorRef }, { token: i1.ViewportRuler }, { token: i3.Platform }, { token: ANIMATION_MODULE_TYPE, optional: true }, { token: MAT_TABS_CONFIG, optional: true }], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"16.1.0\", version: \"17.2.0\", type: MatTabNav, isStandalone: true, selector: \"[mat-tab-nav-bar]\", inputs: { fitInkBarToContent: [\"fitInkBarToContent\", \"fitInkBarToContent\", booleanAttribute], stretchTabs: [\"mat-stretch-tabs\", \"stretchTabs\", booleanAttribute], animationDuration: \"animationDuration\", backgroundColor: \"backgroundColor\", disableRipple: [\"disableRipple\", \"disableRipple\", booleanAttribute], color: \"color\", tabPanel: \"tabPanel\" }, host: { properties: { \"attr.role\": \"_getRole()\", \"class.mat-mdc-tab-header-pagination-controls-enabled\": \"_showPaginationControls\", \"class.mat-mdc-tab-header-rtl\": \"_getLayoutDirection() == 'rtl'\", \"class.mat-mdc-tab-nav-bar-stretch-tabs\": \"stretchTabs\", \"class.mat-primary\": \"color !== \\\"warn\\\" && color !== \\\"accent\\\"\", \"class.mat-accent\": \"color === \\\"accent\\\"\", \"class.mat-warn\": \"color === \\\"warn\\\"\", \"class._mat-animation-noopable\": \"_animationMode === \\\"NoopAnimations\\\"\", \"style.--mat-tab-animation-duration\": \"animationDuration\" }, classAttribute: \"mat-mdc-tab-nav-bar mat-mdc-tab-header\" }, queries: [{ propertyName: \"_items\", predicate: i0.forwardRef(() => MatTabLink), descendants: true }], viewQueries: [{ propertyName: \"_tabListContainer\", first: true, predicate: [\"tabListContainer\"], descendants: true, static: true }, { propertyName: \"_tabList\", first: true, predicate: [\"tabList\"], descendants: true, static: true }, { propertyName: \"_tabListInner\", first: true, predicate: [\"tabListInner\"], descendants: true, static: true }, { propertyName: \"_nextPaginator\", first: true, predicate: [\"nextPaginator\"], descendants: true }, { propertyName: \"_previousPaginator\", first: true, predicate: [\"previousPaginator\"], descendants: true }], exportAs: [\"matTabNavBar\", \"matTabNav\"], usesInheritance: true, ngImport: i0, template: \"<!-- TODO: this also had `mat-elevation-z4`. Figure out what we should do with it. -->\\n<button class=\\\"mat-mdc-tab-header-pagination mat-mdc-tab-header-pagination-before\\\"\\n     #previousPaginator\\n     aria-hidden=\\\"true\\\"\\n     type=\\\"button\\\"\\n     mat-ripple\\n     tabindex=\\\"-1\\\"\\n     [matRippleDisabled]=\\\"_disableScrollBefore || disableRipple\\\"\\n     [class.mat-mdc-tab-header-pagination-disabled]=\\\"_disableScrollBefore\\\"\\n     [disabled]=\\\"_disableScrollBefore || null\\\"\\n     (click)=\\\"_handlePaginatorClick('before')\\\"\\n     (mousedown)=\\\"_handlePaginatorPress('before', $event)\\\"\\n     (touchend)=\\\"_stopInterval()\\\">\\n  <div class=\\\"mat-mdc-tab-header-pagination-chevron\\\"></div>\\n</button>\\n\\n<div class=\\\"mat-mdc-tab-link-container\\\" #tabListContainer (keydown)=\\\"_handleKeydown($event)\\\">\\n  <div class=\\\"mat-mdc-tab-list\\\" #tabList (cdkObserveContent)=\\\"_onContentChanges()\\\">\\n    <div class=\\\"mat-mdc-tab-links\\\" #tabListInner>\\n      <ng-content></ng-content>\\n    </div>\\n  </div>\\n</div>\\n\\n<!-- TODO: this also had `mat-elevation-z4`. Figure out what we should do with it. -->\\n<button class=\\\"mat-mdc-tab-header-pagination mat-mdc-tab-header-pagination-after\\\"\\n     #nextPaginator\\n     aria-hidden=\\\"true\\\"\\n     type=\\\"button\\\"\\n     mat-ripple\\n     [matRippleDisabled]=\\\"_disableScrollAfter || disableRipple\\\"\\n     [class.mat-mdc-tab-header-pagination-disabled]=\\\"_disableScrollAfter\\\"\\n     [disabled]=\\\"_disableScrollAfter || null\\\"\\n     tabindex=\\\"-1\\\"\\n     (mousedown)=\\\"_handlePaginatorPress('after', $event)\\\"\\n     (click)=\\\"_handlePaginatorClick('after')\\\"\\n     (touchend)=\\\"_stopInterval()\\\">\\n  <div class=\\\"mat-mdc-tab-header-pagination-chevron\\\"></div>\\n</button>\\n\", styles: [\".mdc-tab{min-width:90px;padding-right:24px;padding-left:24px;display:flex;flex:1 0 auto;justify-content:center;box-sizing:border-box;margin:0;padding-top:0;padding-bottom:0;border:none;outline:none;text-align:center;white-space:nowrap;cursor:pointer;-webkit-appearance:none;z-index:1}.mdc-tab::-moz-focus-inner{padding:0;border:0}.mdc-tab[hidden]{display:none}.mdc-tab--min-width{flex:0 1 auto}.mdc-tab__content{display:flex;align-items:center;justify-content:center;height:inherit;pointer-events:none}.mdc-tab__text-label{transition:150ms color linear;display:inline-block;line-height:1;z-index:2}.mdc-tab__icon{transition:150ms color linear;z-index:2}.mdc-tab--stacked .mdc-tab__content{flex-direction:column;align-items:center;justify-content:center}.mdc-tab--stacked .mdc-tab__text-label{padding-top:6px;padding-bottom:4px}.mdc-tab--active .mdc-tab__text-label,.mdc-tab--active .mdc-tab__icon{transition-delay:100ms}.mdc-tab:not(.mdc-tab--stacked) .mdc-tab__icon+.mdc-tab__text-label{padding-left:8px;padding-right:0}[dir=rtl] .mdc-tab:not(.mdc-tab--stacked) .mdc-tab__icon+.mdc-tab__text-label,.mdc-tab:not(.mdc-tab--stacked) .mdc-tab__icon+.mdc-tab__text-label[dir=rtl]{padding-left:0;padding-right:8px}.mdc-tab-indicator{display:flex;position:absolute;top:0;left:0;justify-content:center;width:100%;height:100%;pointer-events:none;z-index:1}.mdc-tab-indicator__content{transform-origin:left;opacity:0}.mdc-tab-indicator__content--underline{align-self:flex-end;box-sizing:border-box;width:100%;border-top-style:solid}.mdc-tab-indicator__content--icon{align-self:center;margin:0 auto}.mdc-tab-indicator--active .mdc-tab-indicator__content{opacity:1}.mdc-tab-indicator .mdc-tab-indicator__content{transition:250ms transform cubic-bezier(0.4, 0, 0.2, 1)}.mdc-tab-indicator--no-transition .mdc-tab-indicator__content{transition:none}.mdc-tab-indicator--fade .mdc-tab-indicator__content{transition:150ms opacity linear}.mdc-tab-indicator--active.mdc-tab-indicator--fade .mdc-tab-indicator__content{transition-delay:100ms}.mat-mdc-tab-ripple{position:absolute;top:0;left:0;bottom:0;right:0;pointer-events:none}.mat-mdc-tab-header{display:flex;overflow:hidden;position:relative;flex-shrink:0}.mdc-tab-indicator .mdc-tab-indicator__content{transition-duration:var(--mat-tab-animation-duration, 250ms)}.mat-mdc-tab-header-pagination{-webkit-user-select:none;user-select:none;position:relative;display:none;justify-content:center;align-items:center;min-width:32px;cursor:pointer;z-index:2;-webkit-tap-highlight-color:rgba(0,0,0,0);touch-action:none;box-sizing:content-box;background:none;border:none;outline:0;padding:0}.mat-mdc-tab-header-pagination::-moz-focus-inner{border:0}.mat-mdc-tab-header-pagination .mat-ripple-element{opacity:.12;background-color:var(--mat-tab-header-inactive-ripple-color)}.mat-mdc-tab-header-pagination-controls-enabled .mat-mdc-tab-header-pagination{display:flex}.mat-mdc-tab-header-pagination-before,.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-after{padding-left:4px}.mat-mdc-tab-header-pagination-before .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-after .mat-mdc-tab-header-pagination-chevron{transform:rotate(-135deg)}.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-before,.mat-mdc-tab-header-pagination-after{padding-right:4px}.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-before .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-header-pagination-after .mat-mdc-tab-header-pagination-chevron{transform:rotate(45deg)}.mat-mdc-tab-header-pagination-chevron{border-style:solid;border-width:2px 2px 0 0;height:8px;width:8px;border-color:var(--mat-tab-header-pagination-icon-color)}.mat-mdc-tab-header-pagination-disabled{box-shadow:none;cursor:default;pointer-events:none}.mat-mdc-tab-header-pagination-disabled .mat-mdc-tab-header-pagination-chevron{opacity:.4}.mat-mdc-tab-list{flex-grow:1;position:relative;transition:transform 500ms cubic-bezier(0.35, 0, 0.25, 1)}._mat-animation-noopable .mat-mdc-tab-list{transition:none}._mat-animation-noopable span.mdc-tab-indicator__content,._mat-animation-noopable span.mdc-tab__text-label{transition:none}.mat-mdc-tab-links{display:flex;flex:1 0 auto}[mat-align-tabs=center]>.mat-mdc-tab-link-container .mat-mdc-tab-links{justify-content:center}[mat-align-tabs=end]>.mat-mdc-tab-link-container .mat-mdc-tab-links{justify-content:flex-end}.mat-mdc-tab-link-container{display:flex;flex-grow:1;overflow:hidden;z-index:1;border-bottom-style:solid;border-bottom-width:var(--mat-tab-header-divider-height);border-bottom-color:var(--mat-tab-header-divider-color)}.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-link-container,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-header-pagination{background-color:var(--mat-tab-header-with-background-background-color)}.mat-mdc-tab-nav-bar.mat-tabs-with-background.mat-primary>.mat-mdc-tab-link-container .mat-mdc-tab-link .mdc-tab__text-label{color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-nav-bar.mat-tabs-with-background.mat-primary>.mat-mdc-tab-link-container .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-nav-bar.mat-tabs-with-background:not(.mat-primary)>.mat-mdc-tab-link-container .mat-mdc-tab-link:not(.mdc-tab--active) .mdc-tab__text-label{color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-nav-bar.mat-tabs-with-background:not(.mat-primary)>.mat-mdc-tab-link-container .mat-mdc-tab-link:not(.mdc-tab--active) .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-link-container .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-link-container .mat-mdc-focus-indicator::before,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-mdc-focus-indicator::before{border-color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-link-container .mat-ripple-element,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-link-container .mdc-tab__ripple::before,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-ripple-element,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mdc-tab__ripple::before{background-color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-link-container .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-mdc-tab-header-pagination-chevron{color:var(--mat-tab-header-with-background-foreground-color)}\"], dependencies: [{ kind: \"directive\", type: MatRipple, selector: \"[mat-ripple], [matRipple]\", inputs: [\"matRippleColor\", \"matRippleUnbounded\", \"matRippleCentered\", \"matRippleRadius\", \"matRippleAnimation\", \"matRippleDisabled\", \"matRippleTrigger\"], exportAs: [\"matRipple\"] }, { kind: \"directive\", type: CdkObserveContent, selector: \"[cdkObserveContent]\", inputs: [\"cdkObserveContentDisabled\", \"debounce\"], outputs: [\"cdkObserveContent\"], exportAs: [\"cdkObserveContent\"] }], changeDetection: i0.ChangeDetectionStrategy.Default, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatTabNav, decorators: [{\n            type: Component,\n            args: [{ selector: '[mat-tab-nav-bar]', exportAs: 'matTabNavBar, matTabNav', host: {\n                        '[attr.role]': '_getRole()',\n                        'class': 'mat-mdc-tab-nav-bar mat-mdc-tab-header',\n                        '[class.mat-mdc-tab-header-pagination-controls-enabled]': '_showPaginationControls',\n                        '[class.mat-mdc-tab-header-rtl]': \"_getLayoutDirection() == 'rtl'\",\n                        '[class.mat-mdc-tab-nav-bar-stretch-tabs]': 'stretchTabs',\n                        '[class.mat-primary]': 'color !== \"warn\" && color !== \"accent\"',\n                        '[class.mat-accent]': 'color === \"accent\"',\n                        '[class.mat-warn]': 'color === \"warn\"',\n                        '[class._mat-animation-noopable]': '_animationMode === \"NoopAnimations\"',\n                        '[style.--mat-tab-animation-duration]': 'animationDuration',\n                    }, encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.Default, standalone: true, imports: [MatRipple, CdkObserveContent], template: \"<!-- TODO: this also had `mat-elevation-z4`. Figure out what we should do with it. -->\\n<button class=\\\"mat-mdc-tab-header-pagination mat-mdc-tab-header-pagination-before\\\"\\n     #previousPaginator\\n     aria-hidden=\\\"true\\\"\\n     type=\\\"button\\\"\\n     mat-ripple\\n     tabindex=\\\"-1\\\"\\n     [matRippleDisabled]=\\\"_disableScrollBefore || disableRipple\\\"\\n     [class.mat-mdc-tab-header-pagination-disabled]=\\\"_disableScrollBefore\\\"\\n     [disabled]=\\\"_disableScrollBefore || null\\\"\\n     (click)=\\\"_handlePaginatorClick('before')\\\"\\n     (mousedown)=\\\"_handlePaginatorPress('before', $event)\\\"\\n     (touchend)=\\\"_stopInterval()\\\">\\n  <div class=\\\"mat-mdc-tab-header-pagination-chevron\\\"></div>\\n</button>\\n\\n<div class=\\\"mat-mdc-tab-link-container\\\" #tabListContainer (keydown)=\\\"_handleKeydown($event)\\\">\\n  <div class=\\\"mat-mdc-tab-list\\\" #tabList (cdkObserveContent)=\\\"_onContentChanges()\\\">\\n    <div class=\\\"mat-mdc-tab-links\\\" #tabListInner>\\n      <ng-content></ng-content>\\n    </div>\\n  </div>\\n</div>\\n\\n<!-- TODO: this also had `mat-elevation-z4`. Figure out what we should do with it. -->\\n<button class=\\\"mat-mdc-tab-header-pagination mat-mdc-tab-header-pagination-after\\\"\\n     #nextPaginator\\n     aria-hidden=\\\"true\\\"\\n     type=\\\"button\\\"\\n     mat-ripple\\n     [matRippleDisabled]=\\\"_disableScrollAfter || disableRipple\\\"\\n     [class.mat-mdc-tab-header-pagination-disabled]=\\\"_disableScrollAfter\\\"\\n     [disabled]=\\\"_disableScrollAfter || null\\\"\\n     tabindex=\\\"-1\\\"\\n     (mousedown)=\\\"_handlePaginatorPress('after', $event)\\\"\\n     (click)=\\\"_handlePaginatorClick('after')\\\"\\n     (touchend)=\\\"_stopInterval()\\\">\\n  <div class=\\\"mat-mdc-tab-header-pagination-chevron\\\"></div>\\n</button>\\n\", styles: [\".mdc-tab{min-width:90px;padding-right:24px;padding-left:24px;display:flex;flex:1 0 auto;justify-content:center;box-sizing:border-box;margin:0;padding-top:0;padding-bottom:0;border:none;outline:none;text-align:center;white-space:nowrap;cursor:pointer;-webkit-appearance:none;z-index:1}.mdc-tab::-moz-focus-inner{padding:0;border:0}.mdc-tab[hidden]{display:none}.mdc-tab--min-width{flex:0 1 auto}.mdc-tab__content{display:flex;align-items:center;justify-content:center;height:inherit;pointer-events:none}.mdc-tab__text-label{transition:150ms color linear;display:inline-block;line-height:1;z-index:2}.mdc-tab__icon{transition:150ms color linear;z-index:2}.mdc-tab--stacked .mdc-tab__content{flex-direction:column;align-items:center;justify-content:center}.mdc-tab--stacked .mdc-tab__text-label{padding-top:6px;padding-bottom:4px}.mdc-tab--active .mdc-tab__text-label,.mdc-tab--active .mdc-tab__icon{transition-delay:100ms}.mdc-tab:not(.mdc-tab--stacked) .mdc-tab__icon+.mdc-tab__text-label{padding-left:8px;padding-right:0}[dir=rtl] .mdc-tab:not(.mdc-tab--stacked) .mdc-tab__icon+.mdc-tab__text-label,.mdc-tab:not(.mdc-tab--stacked) .mdc-tab__icon+.mdc-tab__text-label[dir=rtl]{padding-left:0;padding-right:8px}.mdc-tab-indicator{display:flex;position:absolute;top:0;left:0;justify-content:center;width:100%;height:100%;pointer-events:none;z-index:1}.mdc-tab-indicator__content{transform-origin:left;opacity:0}.mdc-tab-indicator__content--underline{align-self:flex-end;box-sizing:border-box;width:100%;border-top-style:solid}.mdc-tab-indicator__content--icon{align-self:center;margin:0 auto}.mdc-tab-indicator--active .mdc-tab-indicator__content{opacity:1}.mdc-tab-indicator .mdc-tab-indicator__content{transition:250ms transform cubic-bezier(0.4, 0, 0.2, 1)}.mdc-tab-indicator--no-transition .mdc-tab-indicator__content{transition:none}.mdc-tab-indicator--fade .mdc-tab-indicator__content{transition:150ms opacity linear}.mdc-tab-indicator--active.mdc-tab-indicator--fade .mdc-tab-indicator__content{transition-delay:100ms}.mat-mdc-tab-ripple{position:absolute;top:0;left:0;bottom:0;right:0;pointer-events:none}.mat-mdc-tab-header{display:flex;overflow:hidden;position:relative;flex-shrink:0}.mdc-tab-indicator .mdc-tab-indicator__content{transition-duration:var(--mat-tab-animation-duration, 250ms)}.mat-mdc-tab-header-pagination{-webkit-user-select:none;user-select:none;position:relative;display:none;justify-content:center;align-items:center;min-width:32px;cursor:pointer;z-index:2;-webkit-tap-highlight-color:rgba(0,0,0,0);touch-action:none;box-sizing:content-box;background:none;border:none;outline:0;padding:0}.mat-mdc-tab-header-pagination::-moz-focus-inner{border:0}.mat-mdc-tab-header-pagination .mat-ripple-element{opacity:.12;background-color:var(--mat-tab-header-inactive-ripple-color)}.mat-mdc-tab-header-pagination-controls-enabled .mat-mdc-tab-header-pagination{display:flex}.mat-mdc-tab-header-pagination-before,.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-after{padding-left:4px}.mat-mdc-tab-header-pagination-before .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-after .mat-mdc-tab-header-pagination-chevron{transform:rotate(-135deg)}.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-before,.mat-mdc-tab-header-pagination-after{padding-right:4px}.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-before .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-header-pagination-after .mat-mdc-tab-header-pagination-chevron{transform:rotate(45deg)}.mat-mdc-tab-header-pagination-chevron{border-style:solid;border-width:2px 2px 0 0;height:8px;width:8px;border-color:var(--mat-tab-header-pagination-icon-color)}.mat-mdc-tab-header-pagination-disabled{box-shadow:none;cursor:default;pointer-events:none}.mat-mdc-tab-header-pagination-disabled .mat-mdc-tab-header-pagination-chevron{opacity:.4}.mat-mdc-tab-list{flex-grow:1;position:relative;transition:transform 500ms cubic-bezier(0.35, 0, 0.25, 1)}._mat-animation-noopable .mat-mdc-tab-list{transition:none}._mat-animation-noopable span.mdc-tab-indicator__content,._mat-animation-noopable span.mdc-tab__text-label{transition:none}.mat-mdc-tab-links{display:flex;flex:1 0 auto}[mat-align-tabs=center]>.mat-mdc-tab-link-container .mat-mdc-tab-links{justify-content:center}[mat-align-tabs=end]>.mat-mdc-tab-link-container .mat-mdc-tab-links{justify-content:flex-end}.mat-mdc-tab-link-container{display:flex;flex-grow:1;overflow:hidden;z-index:1;border-bottom-style:solid;border-bottom-width:var(--mat-tab-header-divider-height);border-bottom-color:var(--mat-tab-header-divider-color)}.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-link-container,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-header-pagination{background-color:var(--mat-tab-header-with-background-background-color)}.mat-mdc-tab-nav-bar.mat-tabs-with-background.mat-primary>.mat-mdc-tab-link-container .mat-mdc-tab-link .mdc-tab__text-label{color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-nav-bar.mat-tabs-with-background.mat-primary>.mat-mdc-tab-link-container .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-nav-bar.mat-tabs-with-background:not(.mat-primary)>.mat-mdc-tab-link-container .mat-mdc-tab-link:not(.mdc-tab--active) .mdc-tab__text-label{color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-nav-bar.mat-tabs-with-background:not(.mat-primary)>.mat-mdc-tab-link-container .mat-mdc-tab-link:not(.mdc-tab--active) .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-link-container .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-link-container .mat-mdc-focus-indicator::before,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-mdc-focus-indicator::before{border-color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-link-container .mat-ripple-element,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-link-container .mdc-tab__ripple::before,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-ripple-element,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mdc-tab__ripple::before{background-color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-link-container .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-mdc-tab-header-pagination-chevron{color:var(--mat-tab-header-with-background-foreground-color)}\"] }]\n        }], ctorParameters: () => [{ type: i0.ElementRef }, { type: i2.Directionality, decorators: [{\n                    type: Optional\n                }] }, { type: i0.NgZone }, { type: i0.ChangeDetectorRef }, { type: i1.ViewportRuler }, { type: i3.Platform }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [ANIMATION_MODULE_TYPE]\n                }] }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [MAT_TABS_CONFIG]\n                }] }], propDecorators: { fitInkBarToContent: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], stretchTabs: [{\n                type: Input,\n                args: [{ alias: 'mat-stretch-tabs', transform: booleanAttribute }]\n            }], animationDuration: [{\n                type: Input\n            }], _items: [{\n                type: ContentChildren,\n                args: [forwardRef(() => MatTabLink), { descendants: true }]\n            }], backgroundColor: [{\n                type: Input\n            }], disableRipple: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], color: [{\n                type: Input\n            }], tabPanel: [{\n                type: Input\n            }], _tabListContainer: [{\n                type: ViewChild,\n                args: ['tabListContainer', { static: true }]\n            }], _tabList: [{\n                type: ViewChild,\n                args: ['tabList', { static: true }]\n            }], _tabListInner: [{\n                type: ViewChild,\n                args: ['tabListInner', { static: true }]\n            }], _nextPaginator: [{\n                type: ViewChild,\n                args: ['nextPaginator']\n            }], _previousPaginator: [{\n                type: ViewChild,\n                args: ['previousPaginator']\n            }] } });\n/**\n * Link inside a `mat-tab-nav-bar`.\n */\nclass MatTabLink extends InkBarItem {\n    /** Whether the link is active. */\n    get active() {\n        return this._isActive;\n    }\n    set active(value) {\n        if (value !== this._isActive) {\n            this._isActive = value;\n            this._tabNavBar.updateActiveLink();\n        }\n    }\n    /**\n     * Whether ripples are disabled on interaction.\n     * @docs-private\n     */\n    get rippleDisabled() {\n        return (this.disabled ||\n            this.disableRipple ||\n            this._tabNavBar.disableRipple ||\n            !!this.rippleConfig.disabled);\n    }\n    constructor(_tabNavBar, \n    /** @docs-private */ elementRef, globalRippleOptions, tabIndex, _focusMonitor, animationMode) {\n        super();\n        this._tabNavBar = _tabNavBar;\n        this.elementRef = elementRef;\n        this._focusMonitor = _focusMonitor;\n        this._destroyed = new Subject();\n        /** Whether the tab link is active or not. */\n        this._isActive = false;\n        /** Whether the tab link is disabled. */\n        this.disabled = false;\n        /** Whether ripples are disabled on the tab link. */\n        this.disableRipple = false;\n        this.tabIndex = 0;\n        /** Unique id for the tab. */\n        this.id = `mat-tab-link-${nextUniqueId++}`;\n        this.rippleConfig = globalRippleOptions || {};\n        this.tabIndex = parseInt(tabIndex) || 0;\n        if (animationMode === 'NoopAnimations') {\n            this.rippleConfig.animation = { enterDuration: 0, exitDuration: 0 };\n        }\n        _tabNavBar._fitInkBarToContent\n            .pipe(takeUntil(this._destroyed))\n            .subscribe(fitInkBarToContent => {\n            this.fitInkBarToContent = fitInkBarToContent;\n        });\n    }\n    /** Focuses the tab link. */\n    focus() {\n        this.elementRef.nativeElement.focus();\n    }\n    ngAfterViewInit() {\n        this._focusMonitor.monitor(this.elementRef);\n    }\n    ngOnDestroy() {\n        this._destroyed.next();\n        this._destroyed.complete();\n        super.ngOnDestroy();\n        this._focusMonitor.stopMonitoring(this.elementRef);\n    }\n    _handleFocus() {\n        // Since we allow navigation through tabbing in the nav bar, we\n        // have to update the focused index whenever the link receives focus.\n        this._tabNavBar.focusIndex = this._tabNavBar._items.toArray().indexOf(this);\n    }\n    _handleKeydown(event) {\n        if (event.keyCode === SPACE || event.keyCode === ENTER) {\n            if (this.disabled) {\n                event.preventDefault();\n            }\n            else if (this._tabNavBar.tabPanel) {\n                // Only prevent the default action on space since it can scroll the page.\n                // Don't prevent enter since it can break link navigation.\n                if (event.keyCode === SPACE) {\n                    event.preventDefault();\n                }\n                this.elementRef.nativeElement.click();\n            }\n        }\n    }\n    _getAriaControls() {\n        return this._tabNavBar.tabPanel\n            ? this._tabNavBar.tabPanel?.id\n            : this.elementRef.nativeElement.getAttribute('aria-controls');\n    }\n    _getAriaSelected() {\n        if (this._tabNavBar.tabPanel) {\n            return this.active ? 'true' : 'false';\n        }\n        else {\n            return this.elementRef.nativeElement.getAttribute('aria-selected');\n        }\n    }\n    _getAriaCurrent() {\n        return this.active && !this._tabNavBar.tabPanel ? 'page' : null;\n    }\n    _getRole() {\n        return this._tabNavBar.tabPanel ? 'tab' : this.elementRef.nativeElement.getAttribute('role');\n    }\n    _getTabIndex() {\n        if (this._tabNavBar.tabPanel) {\n            return this._isActive && !this.disabled ? 0 : -1;\n        }\n        else {\n            return this.disabled ? -1 : this.tabIndex;\n        }\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatTabLink, deps: [{ token: MatTabNav }, { token: i0.ElementRef }, { token: MAT_RIPPLE_GLOBAL_OPTIONS, optional: true }, { token: 'tabindex', attribute: true }, { token: i4.FocusMonitor }, { token: ANIMATION_MODULE_TYPE, optional: true }], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"16.1.0\", version: \"17.2.0\", type: MatTabLink, isStandalone: true, selector: \"[mat-tab-link], [matTabLink]\", inputs: { active: [\"active\", \"active\", booleanAttribute], disabled: [\"disabled\", \"disabled\", booleanAttribute], disableRipple: [\"disableRipple\", \"disableRipple\", booleanAttribute], tabIndex: [\"tabIndex\", \"tabIndex\", (value) => (value == null ? 0 : numberAttribute(value))], id: \"id\" }, host: { listeners: { \"focus\": \"_handleFocus()\", \"keydown\": \"_handleKeydown($event)\" }, properties: { \"attr.aria-controls\": \"_getAriaControls()\", \"attr.aria-current\": \"_getAriaCurrent()\", \"attr.aria-disabled\": \"disabled\", \"attr.aria-selected\": \"_getAriaSelected()\", \"attr.id\": \"id\", \"attr.tabIndex\": \"_getTabIndex()\", \"attr.role\": \"_getRole()\", \"class.mat-mdc-tab-disabled\": \"disabled\", \"class.mdc-tab--active\": \"active\" }, classAttribute: \"mdc-tab mat-mdc-tab-link mat-mdc-focus-indicator\" }, exportAs: [\"matTabLink\"], usesInheritance: true, ngImport: i0, template: \"<span class=\\\"mdc-tab__ripple\\\"></span>\\n\\n<div\\n  class=\\\"mat-mdc-tab-ripple\\\"\\n  mat-ripple\\n  [matRippleTrigger]=\\\"elementRef.nativeElement\\\"\\n  [matRippleDisabled]=\\\"rippleDisabled\\\"></div>\\n\\n<span class=\\\"mdc-tab__content\\\">\\n  <span class=\\\"mdc-tab__text-label\\\">\\n    <ng-content></ng-content>\\n  </span>\\n</span>\\n\\n\", styles: [\".mat-mdc-tab-link{-webkit-tap-highlight-color:rgba(0,0,0,0);-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-decoration:none;background:none;font-family:var(--mat-tab-header-label-text-font);font-size:var(--mat-tab-header-label-text-size);letter-spacing:var(--mat-tab-header-label-text-tracking);line-height:var(--mat-tab-header-label-text-line-height);font-weight:var(--mat-tab-header-label-text-weight)}.mat-mdc-tab-link .mdc-tab-indicator__content--underline{border-color:var(--mdc-tab-indicator-active-indicator-color)}.mat-mdc-tab-link .mdc-tab-indicator__content--underline{border-top-width:var(--mdc-tab-indicator-active-indicator-height)}.mat-mdc-tab-link .mdc-tab-indicator__content--underline{border-radius:var(--mdc-tab-indicator-active-indicator-shape)}.mat-mdc-tab-link:not(.mdc-tab--stacked){height:var(--mdc-secondary-navigation-tab-container-height)}.mat-mdc-tab-link:not(:disabled).mdc-tab--active .mdc-tab__icon{fill:currentColor}.mat-mdc-tab-link:not(:disabled):hover.mdc-tab--active .mdc-tab__icon{fill:currentColor}.mat-mdc-tab-link:not(:disabled):focus.mdc-tab--active .mdc-tab__icon{fill:currentColor}.mat-mdc-tab-link:not(:disabled):active.mdc-tab--active .mdc-tab__icon{fill:currentColor}.mat-mdc-tab-link:disabled.mdc-tab--active .mdc-tab__icon{fill:currentColor}.mat-mdc-tab-link:not(:disabled):not(.mdc-tab--active) .mdc-tab__icon{fill:currentColor}.mat-mdc-tab-link:not(:disabled):hover:not(.mdc-tab--active) .mdc-tab__icon{fill:currentColor}.mat-mdc-tab-link:not(:disabled):focus:not(.mdc-tab--active) .mdc-tab__icon{fill:currentColor}.mat-mdc-tab-link:not(:disabled):active:not(.mdc-tab--active) .mdc-tab__icon{fill:currentColor}.mat-mdc-tab-link:disabled:not(.mdc-tab--active) .mdc-tab__icon{fill:currentColor}.mat-mdc-tab-link.mdc-tab{flex-grow:0}.mat-mdc-tab-link:hover .mdc-tab__text-label{color:var(--mat-tab-header-inactive-hover-label-text-color)}.mat-mdc-tab-link:focus .mdc-tab__text-label{color:var(--mat-tab-header-inactive-focus-label-text-color)}.mat-mdc-tab-link.mdc-tab--active .mdc-tab__text-label{color:var(--mat-tab-header-active-label-text-color)}.mat-mdc-tab-link.mdc-tab--active .mdc-tab__ripple::before,.mat-mdc-tab-link.mdc-tab--active .mat-ripple-element{background-color:var(--mat-tab-header-active-ripple-color)}.mat-mdc-tab-link.mdc-tab--active:hover .mdc-tab__text-label{color:var(--mat-tab-header-active-hover-label-text-color)}.mat-mdc-tab-link.mdc-tab--active:hover .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-header-active-hover-indicator-color)}.mat-mdc-tab-link.mdc-tab--active:focus .mdc-tab__text-label{color:var(--mat-tab-header-active-focus-label-text-color)}.mat-mdc-tab-link.mdc-tab--active:focus .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-header-active-focus-indicator-color)}.mat-mdc-tab-link.mat-mdc-tab-disabled{opacity:.4;pointer-events:none}.mat-mdc-tab-link.mat-mdc-tab-disabled .mdc-tab__content{pointer-events:none}.mat-mdc-tab-link.mat-mdc-tab-disabled .mdc-tab__ripple::before,.mat-mdc-tab-link.mat-mdc-tab-disabled .mat-ripple-element{background-color:var(--mat-tab-header-disabled-ripple-color)}.mat-mdc-tab-link .mdc-tab__ripple::before{content:\\\"\\\";display:block;position:absolute;top:0;left:0;right:0;bottom:0;opacity:0;pointer-events:none;background-color:var(--mat-tab-header-inactive-ripple-color)}.mat-mdc-tab-link .mdc-tab__text-label{color:var(--mat-tab-header-inactive-label-text-color);display:inline-flex;align-items:center}.mat-mdc-tab-link .mdc-tab__content{position:relative;pointer-events:auto}.mat-mdc-tab-link:hover .mdc-tab__ripple::before{opacity:.04}.mat-mdc-tab-link.cdk-program-focused .mdc-tab__ripple::before,.mat-mdc-tab-link.cdk-keyboard-focused .mdc-tab__ripple::before{opacity:.12}.mat-mdc-tab-link .mat-ripple-element{opacity:.12;background-color:var(--mat-tab-header-inactive-ripple-color)}.mat-mdc-tab-header.mat-mdc-tab-nav-bar-stretch-tabs .mat-mdc-tab-link{flex-grow:1}.mat-mdc-tab-link::before{margin:5px}@media(max-width: 599px){.mat-mdc-tab-link{min-width:72px}}\"], dependencies: [{ kind: \"directive\", type: MatRipple, selector: \"[mat-ripple], [matRipple]\", inputs: [\"matRippleColor\", \"matRippleUnbounded\", \"matRippleCentered\", \"matRippleRadius\", \"matRippleAnimation\", \"matRippleDisabled\", \"matRippleTrigger\"], exportAs: [\"matRipple\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatTabLink, decorators: [{\n            type: Component,\n            args: [{ selector: '[mat-tab-link], [matTabLink]', exportAs: 'matTabLink', changeDetection: ChangeDetectionStrategy.OnPush, encapsulation: ViewEncapsulation.None, host: {\n                        'class': 'mdc-tab mat-mdc-tab-link mat-mdc-focus-indicator',\n                        '[attr.aria-controls]': '_getAriaControls()',\n                        '[attr.aria-current]': '_getAriaCurrent()',\n                        '[attr.aria-disabled]': 'disabled',\n                        '[attr.aria-selected]': '_getAriaSelected()',\n                        '[attr.id]': 'id',\n                        '[attr.tabIndex]': '_getTabIndex()',\n                        '[attr.role]': '_getRole()',\n                        '[class.mat-mdc-tab-disabled]': 'disabled',\n                        '[class.mdc-tab--active]': 'active',\n                        '(focus)': '_handleFocus()',\n                        '(keydown)': '_handleKeydown($event)',\n                    }, standalone: true, imports: [MatRipple], template: \"<span class=\\\"mdc-tab__ripple\\\"></span>\\n\\n<div\\n  class=\\\"mat-mdc-tab-ripple\\\"\\n  mat-ripple\\n  [matRippleTrigger]=\\\"elementRef.nativeElement\\\"\\n  [matRippleDisabled]=\\\"rippleDisabled\\\"></div>\\n\\n<span class=\\\"mdc-tab__content\\\">\\n  <span class=\\\"mdc-tab__text-label\\\">\\n    <ng-content></ng-content>\\n  </span>\\n</span>\\n\\n\", styles: [\".mat-mdc-tab-link{-webkit-tap-highlight-color:rgba(0,0,0,0);-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-decoration:none;background:none;font-family:var(--mat-tab-header-label-text-font);font-size:var(--mat-tab-header-label-text-size);letter-spacing:var(--mat-tab-header-label-text-tracking);line-height:var(--mat-tab-header-label-text-line-height);font-weight:var(--mat-tab-header-label-text-weight)}.mat-mdc-tab-link .mdc-tab-indicator__content--underline{border-color:var(--mdc-tab-indicator-active-indicator-color)}.mat-mdc-tab-link .mdc-tab-indicator__content--underline{border-top-width:var(--mdc-tab-indicator-active-indicator-height)}.mat-mdc-tab-link .mdc-tab-indicator__content--underline{border-radius:var(--mdc-tab-indicator-active-indicator-shape)}.mat-mdc-tab-link:not(.mdc-tab--stacked){height:var(--mdc-secondary-navigation-tab-container-height)}.mat-mdc-tab-link:not(:disabled).mdc-tab--active .mdc-tab__icon{fill:currentColor}.mat-mdc-tab-link:not(:disabled):hover.mdc-tab--active .mdc-tab__icon{fill:currentColor}.mat-mdc-tab-link:not(:disabled):focus.mdc-tab--active .mdc-tab__icon{fill:currentColor}.mat-mdc-tab-link:not(:disabled):active.mdc-tab--active .mdc-tab__icon{fill:currentColor}.mat-mdc-tab-link:disabled.mdc-tab--active .mdc-tab__icon{fill:currentColor}.mat-mdc-tab-link:not(:disabled):not(.mdc-tab--active) .mdc-tab__icon{fill:currentColor}.mat-mdc-tab-link:not(:disabled):hover:not(.mdc-tab--active) .mdc-tab__icon{fill:currentColor}.mat-mdc-tab-link:not(:disabled):focus:not(.mdc-tab--active) .mdc-tab__icon{fill:currentColor}.mat-mdc-tab-link:not(:disabled):active:not(.mdc-tab--active) .mdc-tab__icon{fill:currentColor}.mat-mdc-tab-link:disabled:not(.mdc-tab--active) .mdc-tab__icon{fill:currentColor}.mat-mdc-tab-link.mdc-tab{flex-grow:0}.mat-mdc-tab-link:hover .mdc-tab__text-label{color:var(--mat-tab-header-inactive-hover-label-text-color)}.mat-mdc-tab-link:focus .mdc-tab__text-label{color:var(--mat-tab-header-inactive-focus-label-text-color)}.mat-mdc-tab-link.mdc-tab--active .mdc-tab__text-label{color:var(--mat-tab-header-active-label-text-color)}.mat-mdc-tab-link.mdc-tab--active .mdc-tab__ripple::before,.mat-mdc-tab-link.mdc-tab--active .mat-ripple-element{background-color:var(--mat-tab-header-active-ripple-color)}.mat-mdc-tab-link.mdc-tab--active:hover .mdc-tab__text-label{color:var(--mat-tab-header-active-hover-label-text-color)}.mat-mdc-tab-link.mdc-tab--active:hover .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-header-active-hover-indicator-color)}.mat-mdc-tab-link.mdc-tab--active:focus .mdc-tab__text-label{color:var(--mat-tab-header-active-focus-label-text-color)}.mat-mdc-tab-link.mdc-tab--active:focus .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-header-active-focus-indicator-color)}.mat-mdc-tab-link.mat-mdc-tab-disabled{opacity:.4;pointer-events:none}.mat-mdc-tab-link.mat-mdc-tab-disabled .mdc-tab__content{pointer-events:none}.mat-mdc-tab-link.mat-mdc-tab-disabled .mdc-tab__ripple::before,.mat-mdc-tab-link.mat-mdc-tab-disabled .mat-ripple-element{background-color:var(--mat-tab-header-disabled-ripple-color)}.mat-mdc-tab-link .mdc-tab__ripple::before{content:\\\"\\\";display:block;position:absolute;top:0;left:0;right:0;bottom:0;opacity:0;pointer-events:none;background-color:var(--mat-tab-header-inactive-ripple-color)}.mat-mdc-tab-link .mdc-tab__text-label{color:var(--mat-tab-header-inactive-label-text-color);display:inline-flex;align-items:center}.mat-mdc-tab-link .mdc-tab__content{position:relative;pointer-events:auto}.mat-mdc-tab-link:hover .mdc-tab__ripple::before{opacity:.04}.mat-mdc-tab-link.cdk-program-focused .mdc-tab__ripple::before,.mat-mdc-tab-link.cdk-keyboard-focused .mdc-tab__ripple::before{opacity:.12}.mat-mdc-tab-link .mat-ripple-element{opacity:.12;background-color:var(--mat-tab-header-inactive-ripple-color)}.mat-mdc-tab-header.mat-mdc-tab-nav-bar-stretch-tabs .mat-mdc-tab-link{flex-grow:1}.mat-mdc-tab-link::before{margin:5px}@media(max-width: 599px){.mat-mdc-tab-link{min-width:72px}}\"] }]\n        }], ctorParameters: () => [{ type: MatTabNav }, { type: i0.ElementRef }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [MAT_RIPPLE_GLOBAL_OPTIONS]\n                }] }, { type: undefined, decorators: [{\n                    type: Attribute,\n                    args: ['tabindex']\n                }] }, { type: i4.FocusMonitor }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [ANIMATION_MODULE_TYPE]\n                }] }], propDecorators: { active: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], disabled: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], disableRipple: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], tabIndex: [{\n                type: Input,\n                args: [{\n                        transform: (value) => (value == null ? 0 : numberAttribute(value)),\n                    }]\n            }], id: [{\n                type: Input\n            }] } });\n/**\n * Tab panel component associated with MatTabNav.\n */\nclass MatTabNavPanel {\n    constructor() {\n        /** Unique id for the tab panel. */\n        this.id = `mat-tab-nav-panel-${nextUniqueId++}`;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatTabNavPanel, deps: [], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"17.2.0\", type: MatTabNavPanel, isStandalone: true, selector: \"mat-tab-nav-panel\", inputs: { id: \"id\" }, host: { attributes: { \"role\": \"tabpanel\" }, properties: { \"attr.aria-labelledby\": \"_activeTabId\", \"attr.id\": \"id\" }, classAttribute: \"mat-mdc-tab-nav-panel\" }, exportAs: [\"matTabNavPanel\"], ngImport: i0, template: '<ng-content></ng-content>', isInline: true, changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatTabNavPanel, decorators: [{\n            type: Component,\n            args: [{\n                    selector: 'mat-tab-nav-panel',\n                    exportAs: 'matTabNavPanel',\n                    template: '<ng-content></ng-content>',\n                    host: {\n                        '[attr.aria-labelledby]': '_activeTabId',\n                        '[attr.id]': 'id',\n                        'class': 'mat-mdc-tab-nav-panel',\n                        'role': 'tabpanel',\n                    },\n                    encapsulation: ViewEncapsulation.None,\n                    changeDetection: ChangeDetectionStrategy.OnPush,\n                    standalone: true,\n                }]\n        }], propDecorators: { id: [{\n                type: Input\n            }] } });\n\nclass MatTabsModule {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatTabsModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule }); }\n    static { this.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"17.2.0\", ngImport: i0, type: MatTabsModule, imports: [MatCommonModule,\n            MatTabContent,\n            MatTabLabel,\n            MatTab,\n            MatTabGroup,\n            MatTabNav,\n            MatTabNavPanel,\n            MatTabLink], exports: [MatCommonModule,\n            MatTabContent,\n            MatTabLabel,\n            MatTab,\n            MatTabGroup,\n            MatTabNav,\n            MatTabNavPanel,\n            MatTabLink] }); }\n    static { this.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatTabsModule, imports: [MatCommonModule, MatCommonModule] }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatTabsModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [\n                        MatCommonModule,\n                        MatTabContent,\n                        MatTabLabel,\n                        MatTab,\n                        MatTabGroup,\n                        MatTabNav,\n                        MatTabNavPanel,\n                        MatTabLink,\n                    ],\n                    exports: [\n                        MatCommonModule,\n                        MatTabContent,\n                        MatTabLabel,\n                        MatTab,\n                        MatTabGroup,\n                        MatTabNav,\n                        MatTabNavPanel,\n                        MatTabLink,\n                    ],\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MAT_TAB, MAT_TABS_CONFIG, MAT_TAB_CONTENT, MAT_TAB_GROUP, MAT_TAB_LABEL, MatInkBar, MatPaginatedTabHeader, MatTab, MatTabBody, MatTabBodyPortal, MatTabChangeEvent, MatTabContent, MatTabGroup, MatTabHeader, MatTabLabel, MatTabLabelWrapper, MatTabLink, MatTabNav, MatTabNavPanel, MatTabsModule, _MAT_INK_BAR_POSITIONER, _MAT_INK_BAR_POSITIONER_FACTORY, matTabsAnimations };\n"], "names": ["RouterModule", "Secretary<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "routes", "path", "component", "SecretaryCalendarPageRoutingModule", "<PERSON><PERSON><PERSON><PERSON>", "imports", "i1", "exports", "CommonModule", "FormsModule", "ReactiveFormsModule", "IonicModule", "MatButtonModule", "MatIconModule", "MatCardModule", "MatSelectModule", "MatFormFieldModule", "MatInputModule", "MatDatepickerModule", "MatNativeDateModule", "MatDialogModule", "MatProgressSpinnerModule", "MatChipsModule", "MatTabsModule", "SecretaryCalendarPageModule", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r0", "linkedLawyers", "length", "appointments", "ɵɵlistener", "SecretaryCalendar<PERSON>age_div_33_Template_ion_button_click_15_listener", "ɵɵrestoreView", "_r2", "ɵɵnextContext", "ɵɵresetView", "refreshData", "selectedDate", "constructor", "firebaseService", "toastController", "Date", "toISOString", "split", "<PERSON><PERSON><PERSON><PERSON>", "selectedTab", "isLoading", "ngOnInit", "console", "log", "testFirebaseConnection", "loadLinkedLawyers", "loadAppointments", "_this", "_asyncToGenerator", "currentUser", "getCurrentUser", "profile", "getSecretaryProfile", "uid", "error", "_this2", "getSecretaryLinkedLawyers", "email", "name", "rollNumber", "barId", "phone", "role", "createdAt", "updatedAt", "_this3", "getAppointmentsForSecretary", "id", "lawyerId", "<PERSON><PERSON><PERSON>", "clientName", "date", "time", "status", "type", "created<PERSON>y", "showToast", "onLawyerSelected", "lawyer", "onDateSelected", "onAvailabilityChanged", "onTabChange", "event", "tab", "detail", "value", "setSelectedTab", "message", "color", "_this4", "toast", "create", "duration", "position", "present", "getConfirmedCount", "filter", "a", "getTotalAppointments", "getPendingCount", "getUpcomingCount", "now", "apt", "appointmentDateTime", "getStatusColor", "ɵɵdirectiveInject", "FirebaseService", "i2", "ToastController", "selectors", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "SecretaryCale<PERSON>rPage_Template", "rf", "ctx", "ɵɵtwoWayListener", "SecretaryCale<PERSON>r<PERSON>age_Template_ion_segment_ngModelChange_7_listener", "$event", "ɵɵtwoWayBindingSet", "SecretaryCale<PERSON>r<PERSON>age_Template_ion_segment_ionChange_7_listener", "ɵɵtemplate", "SecretaryC<PERSON><PERSON><PERSON><PERSON>age_div_31_Template", "SecretaryC<PERSON><PERSON>r<PERSON>age_div_32_Template", "SecretaryC<PERSON><PERSON><PERSON><PERSON><PERSON>_div_33_Template", "ɵɵtwoWayProperty", "ɵɵtextInterpolate", "ɵɵproperty", "i3", "NgIf", "i4", "NgControlStatus", "NgModel", "IonButton", "IonCard", "IonCardContent", "IonCardHeader", "IonCardSubtitle", "IonCardTitle", "IonContent", "IonHeader", "IonIcon", "IonLabel", "IonSegment", "IonSegmentButton", "IonTitle", "IonToolbar", "SelectValueAccessor", "RouterLinkDelegate", "i5", "RouterLink", "styles", "InjectionToken", "Directive", "Inject", "Optional", "booleanAttribute", "TemplateRef", "Component", "ChangeDetectionStrategy", "ViewEncapsulation", "Input", "ContentChild", "ViewChild", "inject", "ElementRef", "EventEmitter", "ANIMATION_MODULE_TYPE", "numberAttribute", "Output", "ContentChildren", "forwardRef", "QueryList", "Attribute", "NgModule", "<PERSON><PERSON><PERSON><PERSON>", "MAT_RIPPLE_GLOBAL_OPTIONS", "MatCommonModule", "CdkPortal", "TemplatePortal", "CdkPortalOutlet", "Subject", "fromEvent", "of", "merge", "EMPTY", "Observable", "timer", "Subscription", "BehaviorSubject", "CdkScrollable", "normalizePassiveListenerOptions", "Platform", "FocusKeyManager", "CdkMonitorFocus", "hasModifierKey", "SPACE", "ENTER", "takeUntil", "take", "startWith", "switchMap", "skip", "distinctUntilChanged", "CdkObserveContent", "DOCUMENT", "trigger", "state", "style", "transition", "animate", "_c0", "MatTab_ng_template_0_Template", "ɵɵprojection", "_c1", "_c2", "_c3", "_c4", "_c5", "_c6", "a0", "animationDuration", "_c7", "a1", "params", "MatTabBody_ng_template_2_Template", "_c8", "_c9", "MatTabGroup_For_3_Conditional_6_ng_template_0_Template", "MatTabGroup_For_3_Conditional_6_Template", "tab_r4", "$implicit", "templateLabel", "MatTabGroup_For_3_Conditional_7_Template", "textLabel", "MatTabGroup_For_3_Template", "ɵɵgetCurrentView", "MatTabGroup_For_3_Template_div_click_0_listener", "ctx_r2", "i_r5", "$index", "ctx_r5", "tabHeader_r7", "ɵɵreference", "_handleClick", "MatTabGroup_For_3_Template_div_cdkFocusChange_0_listener", "_tabFocusChanged", "tabNode_r8", "ɵɵclassMap", "labelClass", "ɵɵclassProp", "selectedIndex", "_getTabLabelId", "disabled", "fitInkBarToContent", "ɵɵattribute", "_getTabIndex", "_tabs", "_getTabContentId", "aria<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "disable<PERSON><PERSON><PERSON>", "ɵɵconditional", "MatTabGroup_Conditional_4_Template", "MatTabGroup_For_8_Template", "_r9", "MatTabGroup_For_8_Template_mat_tab_body__onCentered_0_listener", "_removeTabBodyWrapperHeight", "MatTabGroup_For_8_Template_mat_tab_body__onCentering_0_listener", "_setTabBodyWrapperHeight", "tab_r10", "i_r11", "bodyClass", "content", "origin", "preserve<PERSON><PERSON>nt", "contentTabIndex", "_c10", "_c11", "MAT_TAB_CONTENT", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ɵfac", "MatTabContent_Factory", "t", "ɵdir", "ɵɵdefineDirective", "ɵɵProvidersFeature", "provide", "useExisting", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "providers", "MAT_TAB_LABEL", "MAT_TAB", "MatTab<PERSON><PERSON><PERSON>", "templateRef", "viewContainerRef", "_closestTab", "MatTabLabel_Factory", "ViewContainerRef", "ɵɵInheritDefinitionFeature", "undefined", "decorators", "MAT_TAB_GROUP", "Mat<PERSON><PERSON>", "_templateLabel", "_setTemplateLabelInput", "_contentPortal", "_viewContainerRef", "_closestTabGroup", "_explicitContent", "_stateChanges", "isActive", "ngOnChanges", "changes", "hasOwnProperty", "next", "ngOnDestroy", "complete", "_implicitContent", "MatTab_Factory", "ɵcmp", "ɵɵdefineComponent", "contentQueries", "MatTab_ContentQueries", "dirIndex", "ɵɵcontentQuery", "_t", "ɵɵqueryRefresh", "ɵɵloadQuery", "first", "viewQuery", "MatTab_Query", "ɵɵviewQuery", "hostAttrs", "inputs", "ɵɵInputFlags", "HasDecoratorInputTransform", "None", "exportAs", "ɵɵInputTransformsFeature", "ɵɵNgOnChangesFeature", "ngContentSelectors", "MatTab_Template", "ɵɵprojectionDef", "encapsulation", "changeDetection", "<PERSON><PERSON><PERSON>", "host", "transform", "read", "static", "ACTIVE_CLASS", "NO_TRANSITION_CLASS", "MatInkBar", "_items", "hide", "for<PERSON>ach", "item", "deactivateInkBar", "alignToElement", "element", "correspondingItem", "find", "elementRef", "nativeElement", "currentItem", "_currentItem", "domRect", "getBoundingClientRect", "activateInkBar", "InkBarItem", "_elementRef", "_fitTo<PERSON>ontent", "newValue", "_inkBarElement", "_appendInkBarElement", "previousIndicatorClientRect", "_inkBarContentElement", "classList", "add", "currentClientRect", "<PERSON><PERSON><PERSON><PERSON>", "width", "xPosition", "left", "setProperty", "remove", "_createInkBarElement", "documentNode", "ownerDocument", "document", "inkBarElement", "createElement", "inkBarContentElement", "className", "append<PERSON><PERSON><PERSON>", "Error", "parentElement", "querySelector", "InkBarItem_Factory", "_MAT_INK_BAR_POSITIONER_FACTORY", "method", "offsetLeft", "offsetWidth", "_MAT_INK_BAR_POSITIONER", "providedIn", "factory", "MatTabLabelWrapper", "focus", "getOffsetLeft", "getOffsetWidth", "MatTabLabelWrapper_Factory", "hostVars", "hostBindings", "MatTabLabelWrapper_HostBindings", "passiveEventListenerOptions", "passive", "HEADER_SCROLL_DELAY", "HEADER_SCROLL_INTERVAL", "MatPaginatedTabHeader", "_selectedIndex", "v", "isNaN", "_selectedIndexChanged", "_keyManager", "updateActiveItem", "_changeDetectorRef", "_viewportRuler", "_dir", "_ngZone", "_platform", "_animationMode", "_scrollDistance", "_destroyed", "_showPaginationControls", "_disableScrollAfter", "_disableScrollBefore", "_stopScrolling", "disablePagination", "selectFocusedIndex", "indexFocused", "runOutsideAngular", "pipe", "subscribe", "_stopInterval", "ngAfterViewInit", "_previousPaginator", "_handlePaginatorPress", "_nextPaginator", "ngAfterContentInit", "<PERSON><PERSON><PERSON><PERSON>", "change", "resize", "realign", "updatePagination", "_alignInkBarToSelectedTab", "withHorizontalOrientation", "_getLayoutDirection", "withHomeAndEnd", "withWrap", "skipPredicate", "onStable", "_itemsResized", "run", "Promise", "resolve", "then", "Math", "max", "min", "_getMaxScrollDistance", "newFocusIndex", "emit", "_setTabFocus", "ResizeObserver", "tabItems", "observer", "resizeObserver", "entries", "observe", "disconnect", "some", "e", "contentRect", "height", "ngAfterContentChecked", "_tabLabelCount", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_scrollToLabel", "_checkScrollingControls", "_scrollDistanceChanged", "_updateTabScrollPosition", "destroy", "_handleKeydown", "keyCode", "focusIndex", "get", "_itemSelected", "onKeydown", "_onContentChanges", "textContent", "_currentTextContent", "_checkPaginationEnabled", "activeItemIndex", "_isValidIndex", "setActiveItem", "index", "toArray", "tabIndex", "containerEl", "_tabListContainer", "dir", "scrollLeft", "scrollWidth", "scrollDistance", "translateX", "_tabList", "round", "TRIDENT", "EDGE", "_scrollTo", "_scrollHeader", "direction", "viewLength", "scrollAmount", "_handlePaginatorClick", "labelIndex", "<PERSON><PERSON><PERSON><PERSON>", "labelBeforePos", "labelAfterPos", "_tabListInner", "beforeVisiblePos", "afterVisiblePos", "isEnabled", "lengthOfTabList", "selectedItem", "<PERSON><PERSON><PERSON><PERSON>W<PERSON><PERSON>", "_inkBar", "mouseEvent", "button", "maxScrollDistance", "distance", "MatPaginatedTabHeader_Factory", "ChangeDetectorRef", "ViewportRuler", "Directionality", "NgZone", "outputs", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "changeDetectorRef", "viewportRuler", "ngZone", "platform", "animationMode", "preventDefault", "MatTabHeader_Factory", "MatTabHeader_ContentQueries", "MatTabHeader_Query", "MatTabHeader_HostBindings", "MatTabHeader_Template", "_r1", "MatTabHeader_Template_button_click_0_listener", "MatTabHeader_Template_button_mousedown_0_listener", "MatTab<PERSON><PERSON>er_Template_button_touchend_0_listener", "MatTab<PERSON><PERSON><PERSON>_Template_div_keydown_3_listener", "Mat<PERSON><PERSON><PERSON><PERSON><PERSON>_Template_div_cdkObserveContent_5_listener", "MatTab<PERSON>eader_Template_button_mousedown_10_listener", "MatTab<PERSON><PERSON><PERSON>_Template_button_click_10_listener", "Mat<PERSON>ab<PERSON><PERSON><PERSON>_Template_button_touchend_10_listener", "dependencies", "descendants", "MAT_TABS_CONFIG", "matTabsAnimations", "translateTab", "minHeight", "visibility", "MatTabBodyPortal", "componentFactoryResolver", "_host", "_document", "_centeringSub", "_leavingSub", "_beforeCentering", "_isCenterPosition", "_position", "isCentering", "has<PERSON>tta<PERSON>", "attach", "_content", "_afterLeavingCenter", "detach", "unsubscribe", "MatTabBodyPortal_Factory", "ComponentFactoryResolver", "MatTabBody", "_positionIndex", "_computePositionAnimationState", "_dirChangeSubscription", "_translateTabComplete", "_onCentering", "_onCentered", "x", "y", "fromState", "toState", "_computePositionFromO<PERSON>in", "_onTranslateTabStarted", "clientHeight", "MatTabBody_Factory", "MatTabBody_Query", "_portalHost", "MatTabBody_Template", "MatTabBody_Template_div_animation_translateTab_start_0_listener", "MatTabBody_Template_div_animation_translateTab_done_0_listener", "ɵɵpureFunction2", "ɵɵpureFunction1", "data", "animation", "animations", "nextId", "ENABLE_BACKGROUND_INPUT", "MatTabGroup", "_fitInkBarToContent", "_indexToSelect", "_animationDuration", "stringValue", "test", "_contentTabIndex", "backgroundColor", "_backgroundColor", "defaultConfig", "_lastFocusedTabIndex", "_tabBodyWrapperHeight", "_tabsSubscription", "_tabLabelSubscription", "stretchTabs", "dynamicHeight", "headerPosition", "selectedIndexChange", "focusChange", "animationDone", "selectedTabChange", "_isServer", "<PERSON><PERSON><PERSON><PERSON>", "_groupId", "indexToSelect", "_clampTabIndex", "isFirstRun", "_createChangeEvent", "wrapper", "_tabBodyWrapper", "_subscribeToAllTabChanges", "_subscribeToTabLabels", "tabs", "i", "_allTabs", "reset", "notifyOn<PERSON><PERSON>es", "realignInkBar", "_tabHeader", "focusTab", "header", "_focusChanged", "MatTabChangeEvent", "map", "tabHeight", "offsetHeight", "tabHeader", "targetIndex", "<PERSON><PERSON><PERSON><PERSON>", "MatTabGroup_Factory", "MatTabGroup_ContentQueries", "MatTabGroup_Query", "MatTabGroup_HostBindings", "ɵɵstyleProp", "MatTabGroup_Template", "MatTabGroup_Template_mat_tab_header_indexFocused_0_listener", "MatTabGroup_Template_mat_tab_header_selectFocusedIndex_0_listener", "ɵɵrepeaterCreate", "ɵɵrepeaterTrackByIdentity", "ɵɵrepeater", "alias", "nextUniqueId", "MatTabNav", "updateActiveLink", "tabPanel", "items", "active", "_activeTabId", "_getRole", "getAttribute", "MatTabNav_Factory", "MatTabNav_ContentQueries", "MatTabLink", "MatTabNav_Query", "MatTabNav_HostBindings", "attrs", "MatTabNav_Template", "MatTabNav_Template_button_click_0_listener", "MatTabNav_Template_button_mousedown_0_listener", "MatTabNav_Template_button_touchend_0_listener", "MatTabNav_Template_div_keydown_3_listener", "MatTabNav_Template_div_cdkObserveContent_5_listener", "MatTabNav_Template_button_mousedown_10_listener", "MatTabNav_Template_button_click_10_listener", "MatTabNav_Template_button_touchend_10_listener", "_isActive", "_tabNavBar", "rippleDisabled", "rippleConfig", "globalRippleOptions", "_focusMonitor", "parseInt", "enterDuration", "exitDuration", "monitor", "stopMonitoring", "_handleFocus", "indexOf", "click", "_getAriaControls", "_getAriaSelected", "_getAriaCurrent", "MatTabLink_Factory", "ɵɵinjectAttribute", "FocusMonitor", "MatTabLink_HostBindings", "MatTabLink_focus_HostBindingHandler", "MatTabLink_keydown_HostBindingHandler", "MatTabLink_Template", "OnPush", "MatTabNavPanel", "MatTabNavPanel_Factory", "MatTabNavPanel_HostBindings", "MatTabNavPanel_Template", "MatTabsModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector"], "sourceRoot": "webpack:///", "x_google_ignoreList": [4]}