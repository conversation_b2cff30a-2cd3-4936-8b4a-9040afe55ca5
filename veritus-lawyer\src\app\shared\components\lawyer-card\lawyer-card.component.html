<app-veritus-card class="lawyer-card" shadow="md" padding="md">
  <div class="lawyer-header">
    <app-veritus-avatar
      [src]="lawyer.avatar"
      [alt]="lawyer.name"
      [initials]="getInitials(lawyer.name)"
      size="lg"
      [showBorder]="true">
    </app-veritus-avatar>
    <div class="lawyer-info">
      <h3 class="lawyer-name veritus-text-lg veritus-font-semibold">{{ lawyer.name }}</h3>
      <p class="lawyer-firm veritus-text-sm veritus-text-gray">{{ lawyer.firm }}</p>
      <p class="lawyer-location veritus-text-xs veritus-text-gray">
        <ion-icon name="location-outline"></ion-icon>
        {{ lawyer.location }}
      </p>
    </div>
    <div class="lawyer-rating">
      <div class="rating-stars">
        <ion-icon
          *ngFor="let filled of getStarArray(lawyer.rating)"
          [name]="filled ? 'star' : 'star-outline'"
          [class.filled]="filled">
        </ion-icon>
      </div>
      <span class="rating-value veritus-text-sm">{{ lawyer.rating }}</span>
    </div>
  </div>

  <div class="lawyer-specialties">
    <span
      *ngFor="let specialty of lawyer.specialties"
      class="specialty-tag veritus-text-xs">
      {{ specialty }}
    </span>
  </div>

  <p class="lawyer-bio veritus-text-sm veritus-text-gray">{{ lawyer.bio }}</p>

  <div class="lawyer-actions" *ngIf="showActions">
    <app-veritus-button
      variant="outline-gold"
      size="sm"
      (clicked)="onViewDetails()">
      View Details
    </app-veritus-button>
    <app-veritus-button
      variant="primary"
      size="sm"
      (clicked)="onRequestRetainer()">
      Request Retainer
    </app-veritus-button>
  </div>
</app-veritus-card>
