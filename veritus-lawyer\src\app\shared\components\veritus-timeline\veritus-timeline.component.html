<div class="timeline-container">
  <div class="timeline-item" *ngFor="let item of items; let last = last">
    <div class="timeline-marker">
      <div 
        class="timeline-dot"
        [ngClass]="{'timeline-dot-completed': item.completed}">
      </div>
      <div 
        class="timeline-line" 
        *ngIf="!last"
        [ngClass]="{'timeline-line-completed': item.completed}">
      </div>
    </div>
    <div class="timeline-content">
      <h3 class="timeline-title veritus-text-base veritus-font-medium">{{ item.title }}</h3>
      <p class="timeline-description veritus-text-sm veritus-text-gray">{{ item.description }}</p>
      <span class="timeline-date veritus-text-xs veritus-text-gray">{{ item.date }}</span>
    </div>
  </div>
</div>
