import { Injectable } from '@angular/core';
import { AngularFirestore } from '@angular/fire/compat/firestore';
import { AngularFireAuth } from '@angular/fire/compat/auth';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

export interface LawyerProfile {
  uid: string;
  email: string;
  name: string;
  phone: string;
  rollNumber: string;
  firm: string;
  specialization: string;
  yearsOfPractice: number;
  status: 'verified' | 'pending' | 'suspended';
  avatar?: string;
  createdAt: Date;
  updatedAt: Date;
  // Additional fields from lawyer registration
  address?: string;
  city?: string;
  state?: string;
  zipCode?: string;
  bio?: string;
  education?: string[];
  certifications?: string[];
  languages?: string[];
  practiceAreas?: string[];
  barAdmissions?: string[];
  experience?: string;
  achievements?: string[];
  profilePicture?: string;
  documents?: {
    barCertificate?: string;
    idDocument?: string;
    diploma?: string;
    [key: string]: string | undefined;
  };
  verificationStatus?: {
    documentsVerified: boolean;
    identityVerified: boolean;
    barMembershipVerified: boolean;
    verifiedAt?: Date;
    verifiedBy?: string;
  };
  settings?: {
    isAvailable: boolean;
    workingHours: {
      start: string;
      end: string;
    };
    timeZone: string;
    consultationFee: number;
    currency: string;
  };
}

export interface VerificationRequest {
  id: string;
  lawyerId: string;
  lawyerName: string;
  lawyerEmail: string;
  rollNumber: string;
  firm: string;
  status: 'pending' | 'approved' | 'rejected';
  submittedAt: Date;
  reviewedAt?: Date;
  reviewedBy?: string;
  documents: {
    barCertificate?: string;
    idDocument?: string;
    diploma?: string;
    [key: string]: string | undefined;
  };
  notes?: string;
  rejectionReason?: string;
}

@Injectable({
  providedIn: 'root'
})
export class FirebaseService {

  constructor(
    private firestore: AngularFirestore,
    private auth: AngularFireAuth
  ) { }

  // Lawyer Management Methods
  getAllLawyers(): Observable<LawyerProfile[]> {
    return this.firestore.collection<LawyerProfile>('lawyers', ref =>
      ref.orderBy('createdAt', 'desc')
    ).valueChanges({ idField: 'uid' }).pipe(
      map(lawyers => lawyers.map(lawyer => ({
        ...lawyer,
        createdAt: lawyer.createdAt instanceof Date ? lawyer.createdAt : (lawyer.createdAt as any)?.toDate?.() || new Date(),
        updatedAt: lawyer.updatedAt instanceof Date ? lawyer.updatedAt : (lawyer.updatedAt as any)?.toDate?.() || new Date()
      })))
    );
  }

  getLawyersByStatus(status: string): Observable<LawyerProfile[]> {
    return this.firestore.collection<LawyerProfile>('lawyers', ref =>
      ref.where('status', '==', status).orderBy('createdAt', 'desc')
    ).valueChanges({ idField: 'uid' }).pipe(
      map(lawyers => lawyers.map(lawyer => ({
        ...lawyer,
        createdAt: lawyer.createdAt instanceof Date ? lawyer.createdAt : (lawyer.createdAt as any)?.toDate?.() || new Date(),
        updatedAt: lawyer.updatedAt instanceof Date ? lawyer.updatedAt : (lawyer.updatedAt as any)?.toDate?.() || new Date()
      })))
    );
  }

  getLawyerById(uid: string): Observable<LawyerProfile | undefined> {
    return this.firestore.doc<LawyerProfile>(`lawyers/${uid}`).valueChanges();
  }

  updateLawyerStatus(uid: string, status: 'verified' | 'pending' | 'suspended'): Promise<void> {
    return this.firestore.doc(`lawyers/${uid}`).update({
      status: status,
      updatedAt: new Date()
    });
  }

  updateLawyerProfile(uid: string, data: Partial<LawyerProfile>): Promise<void> {
    return this.firestore.doc(`lawyers/${uid}`).update({
      ...data,
      updatedAt: new Date()
    });
  }

  // Verification Request Methods
  getAllVerificationRequests(): Observable<VerificationRequest[]> {
    return this.firestore.collection<VerificationRequest>('verification_requests', ref => 
      ref.orderBy('submittedAt', 'desc')
    ).valueChanges({ idField: 'id' });
  }

  getPendingVerificationRequests(): Observable<VerificationRequest[]> {
    return this.firestore.collection<VerificationRequest>('verification_requests', ref => 
      ref.where('status', '==', 'pending').orderBy('submittedAt', 'desc')
    ).valueChanges({ idField: 'id' });
  }

  updateVerificationRequest(id: string, data: Partial<VerificationRequest>): Promise<void> {
    return this.firestore.doc(`verification_requests/${id}`).update({
      ...data,
      reviewedAt: new Date()
    });
  }

  approveVerificationRequest(id: string, reviewedBy: string): Promise<void> {
    return this.firestore.doc(`verification_requests/${id}`).update({
      status: 'approved',
      reviewedAt: new Date(),
      reviewedBy: reviewedBy
    });
  }

  rejectVerificationRequest(id: string, reviewedBy: string, rejectionReason: string): Promise<void> {
    return this.firestore.doc(`verification_requests/${id}`).update({
      status: 'rejected',
      reviewedAt: new Date(),
      reviewedBy: reviewedBy,
      rejectionReason: rejectionReason
    });
  }

  // Search Methods
  searchLawyers(query: string): Observable<LawyerProfile[]> {
    // Note: Firestore doesn't support full-text search natively
    // This is a basic implementation that searches by name
    // For production, consider using Algolia or Elasticsearch
    return this.firestore.collection<LawyerProfile>('lawyers', ref => 
      ref.where('name', '>=', query)
         .where('name', '<=', query + '\uf8ff')
         .orderBy('name')
    ).valueChanges({ idField: 'uid' });
  }

  // Statistics Methods
  getLawyerStats(): Observable<any> {
    return this.getAllLawyers().pipe(
      map(lawyers => {
        const total = lawyers.length;
        const verified = lawyers.filter(l => l.status === 'verified').length;
        const pending = lawyers.filter(l => l.status === 'pending').length;
        const suspended = lawyers.filter(l => l.status === 'suspended').length;
        
        return {
          total,
          verified,
          pending,
          suspended,
          verificationRate: total > 0 ? (verified / total) * 100 : 0
        };
      })
    );
  }

  // Audit Logging
  async logAdminAction(action: string, entityType: string, entityId: string, details?: any): Promise<void> {
    const currentUser = await this.auth.currentUser;
    const logEntry = {
      action,
      entityType,
      entityId,
      details,
      timestamp: new Date(),
      adminId: currentUser?.uid || 'unknown',
      adminEmail: currentUser?.email || 'unknown'
    };

    await this.firestore.collection('admin_audit_logs').add(logEntry);
  }
}
