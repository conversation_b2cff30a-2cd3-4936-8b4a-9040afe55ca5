.sidebar {
  width: 250px;
  height: 100vh;
  background: linear-gradient(180deg, var(--veritus-primary) 0%, var(--veritus-primary-dark) 100%);
  color: white;
  display: flex;
  flex-direction: column;
  position: fixed;
  left: 0;
  top: 0;
  z-index: 1000;
  box-shadow: 4px 0 20px rgba(196, 154, 86, 0.15);
  border-right: 1px solid rgba(196, 154, 86, 0.3);
}

.sidebar-header {
  padding: 35px 20px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  background: linear-gradient(135deg, var(--veritus-primary-light) 0%, var(--veritus-primary) 100%);
  position: relative;

  &::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, rgba(255, 255, 255, 0.3) 0%, rgba(255, 255, 255, 0.1) 100%);
  }

  .logo-container {
    text-align: center;
  }

  .logo {
    margin: 0 0 5px 0;
    font-size: 2rem;
    font-weight: 700;
    letter-spacing: -0.5px;
    color: white;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .logo-subtitle {
    margin: 0;
    font-size: 0.8rem;
    opacity: 0.9;
    font-weight: 300;
    letter-spacing: 0.5px;
    text-transform: uppercase;
  }
}

.sidebar-nav {
  flex: 1;
  padding: 20px 0;
  
  .nav-list {
    list-style: none;
    margin: 0;
    padding: 0;
  }
  
  .nav-item {
    margin-bottom: 5px;
  }
  
  .nav-link {
    display: flex;
    align-items: center;
    padding: 15px 20px;
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: all 0.3s ease;
    border-left: 3px solid transparent;
    
    &:hover {
      background: rgba(255, 255, 255, 0.1);
      color: white;
      border-left-color: rgba(255, 255, 255, 0.3);
    }
    
    &.active {
      background: rgba(255, 255, 255, 0.15);
      color: white;
      border-left-color: white;
      font-weight: 600;
    }
    
    .icon {
      width: 20px;
      height: 20px;
      margin-right: 15px;
      background-size: contain;
      background-repeat: no-repeat;
      background-position: center;
      opacity: 0.8;
    }
    
    span {
      font-size: 0.95rem;
    }
  }
}

.sidebar-footer {
  padding: 20px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  
  .logout-btn {
    display: flex;
    align-items: center;
    width: 100%;
    padding: 15px 20px;
    background: transparent;
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    color: rgba(255, 255, 255, 0.8);
    cursor: pointer;
    transition: all 0.3s ease;
    
    &:hover {
      background: rgba(255, 255, 255, 0.1);
      color: white;
      border-color: rgba(255, 255, 255, 0.4);
    }
    
    .icon {
      width: 18px;
      height: 18px;
      margin-right: 12px;
      background-size: contain;
      background-repeat: no-repeat;
      background-position: center;
      opacity: 0.8;
    }
    
    span {
      font-size: 0.95rem;
    }
  }
}

// Icon styles using SVG
.dashboard-icon {
  width: 20px;
  height: 20px;
  background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"><path fill="white" d="M64 64c-17.7 0-32 14.3-32 32v320c0 17.7 14.3 32 32 32h384c17.7 0 32-14.3 32-32V96c0-17.7-14.3-32-32-32H64zm384 64v256H64V128h384zM176 176c-8.8 0-16 7.2-16 16s7.2 16 16 16 16-7.2 16-16-7.2-16-16-16zm-32 80c-8.8 0-16 7.2-16 16s7.2 16 16 16 16-7.2 16-16-7.2-16-16-16zm64-32c-8.8 0-16 7.2-16 16s7.2 16 16 16 16-7.2 16-16-7.2-16-16-16zm80-48c-8.8 0-16 7.2-16 16s7.2 16 16 16 16-7.2 16-16-7.2-16-16-16zm-48 80c-8.8 0-16 7.2-16 16s7.2 16 16 16 16-7.2 16-16-7.2-16-16-16zm-80 80c-35.3 0-64 28.7-64 64s28.7 64 64 64 64-28.7 64-64-28.7-64-64-64zm0 96c-17.7 0-32-14.3-32-32s14.3-32 32-32 32 14.3 32 32-14.3 32-32 32zm208-144c0-8.8-7.2-16-16-16s-16 7.2-16 16v96c0 8.8 7.2 16 16 16s16-7.2 16-16v-96zm32-32c0-8.8-7.2-16-16-16s-16 7.2-16 16v128c0 8.8 7.2 16 16 16s16-7.2 16-16V256zm32 16c0-8.8-7.2-16-16-16s-16 7.2-16 16v112c0 8.8 7.2 16 16 16s16-7.2 16-16V272zm32-48c0-8.8-7.2-16-16-16s-16 7.2-16 16v144c0 8.8 7.2 16 16 16s16-7.2 16-16V224z"/></svg>');
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.lawyer-icon {
  width: 20px;
  height: 20px;
  background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"><path fill="white" d="M64 32C46.3 32 32 46.3 32 64v384c0 17.7 14.3 32 32 32h320c17.7 0 32-14.3 32-32V64c0-17.7-14.3-32-32-32H64zm32 64h256v320H96V96zm48 48c-13.3 0-24 10.7-24 24s10.7 24 24 24 24-10.7 24-24-10.7-24-24-24zm80 8h128c8.8 0 16 7.2 16 16s-7.2 16-16 16H224c-8.8 0-16-7.2-16-16s7.2-16 16-16zm-80 72c-13.3 0-24 10.7-24 24s10.7 24 24 24 24-10.7 24-24-10.7-24-24-24zm80 8h128c8.8 0 16 7.2 16 16s-7.2 16-16 16H224c-8.8 0-16-7.2-16-16s7.2-16 16-16zm-80 72c-13.3 0-24 10.7-24 24s10.7 24 24 24 24-10.7 24-24-10.7-24-24-24zm80 8h96c8.8 0 16 7.2 16 16s-7.2 16-16 16h-96c-8.8 0-16-7.2-16-16s7.2-16 16-16zm32 24h32c8.8 0 16 7.2 16 16s-7.2 16-16 16h-32c-8.8 0-16-7.2-16-16s7.2-16 16-16zm-112 48c-13.3 0-24 10.7-24 24s10.7 24 24 24 24-10.7 24-24-10.7-24-24-24zm80 8h128c8.8 0 16 7.2 16 16s-7.2 16-16 16H224c-8.8 0-16-7.2-16-16s7.2-16 16-16zm-80 72c-13.3 0-24 10.7-24 24s10.7 24 24 24 24-10.7 24-24-10.7-24-24-24zm80 8h128c8.8 0 16 7.2 16 16s-7.2 16-16 16H224c-8.8 0-16-7.2-16-16s7.2-16 16-16z"/></svg>');
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.verification-icon {
  width: 20px;
  height: 20px;
  background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"><path fill="white" d="M256 0c53 0 96 43 96 96c0 23.7-8.1 45.4-21.7 62.6C315.4 175.2 288 200.6 256 224c-32-23.4-59.4-48.8-74.3-65.4C168.1 141.4 160 119.7 160 96c0-53 43-96 96-96zm0 64c-17.7 0-32 14.3-32 32s14.3 32 32 32s32-14.3 32-32s-14.3-32-32-32zM96 256c0-17.7 14.3-32 32-32h256c17.7 0 32 14.3 32 32v192c0 17.7-14.3 32-32 32H128c-17.7 0-32-14.3-32-32V256zm64 32v128h192V288H160zm96 32l64 64c6.2 6.2 6.2 16.4 0 22.6s-16.4 6.2-22.6 0L256 365.3l-41.4 41.4c-6.2 6.2-16.4 6.2-22.6 0s-6.2-16.4 0-22.6l64-64z"/></svg>');
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.template-icon::before {
  content: "📄";
  font-size: 16px;
  display: block;
}

.users-icon {
  width: 20px;
  height: 20px;
  background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"><path fill="white" d="M96 128a128 128 0 1 1 256 0A128 128 0 1 1 96 128zM0 482.3C0 383.8 79.8 304 178.3 304h155.4C432.2 304 512 383.8 512 482.3c0 16.4-13.3 29.7-29.7 29.7H29.7C13.3 512 0 498.7 0 482.3zM64 128a64 64 0 1 0 128 0A64 64 0 1 0 64 128zm256 0a64 64 0 1 0 128 0A64 64 0 1 0 320 128zM178.3 336c-58.9 0-106.7 47.8-106.7 106.7h267.4c0-58.9-47.8-106.7-106.7-106.7H178.3z"/></svg>');
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.settings-icon {
  width: 20px;
  height: 20px;
  background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"><path fill="white" d="M495.9 166.6c3.2 8.7 .5 18.4-6.4 24.6l-43.3 39.4c1.1 8.3 1.7 16.8 1.7 25.4s-.6 17.1-1.7 25.4l43.3 39.4c6.9 6.2 9.6 15.9 6.4 24.6c-4.4 11.9-9.7 23.3-15.8 34.3l-4.7 8.1c-6.6 11-14 21.4-22.1 31.2c-5.9 7.2-15.7 9.6-24.5 6.8l-55.7-17.7c-13.4 10.3-28.2 18.9-44 25.4l-12.5 57.1c-2 9.1-9 16.3-18.2 17.8c-13.8 2.3-28 3.5-42.5 3.5s-28.7-1.2-42.5-3.5c-9.2-1.5-16.2-8.7-18.2-17.8l-12.5-57.1c-15.8-6.5-30.6-15.1-44-25.4L83.1 425.9c-8.8 2.8-18.6 .3-24.5-6.8c-8.1-9.8-15.5-20.2-22.1-31.2l-4.7-8.1c-6.1-11-11.4-22.4-15.8-34.3c-3.2-8.7-.5-18.4 6.4-24.6l43.3-39.4C64.6 273.1 64 264.6 64 256s.6-17.1 1.7-25.4L22.4 191.2c-6.9-6.2-9.6-15.9-6.4-24.6c4.4-11.9 9.7-23.3 15.8-34.3l4.7-8.1c6.6-11 14-21.4 22.1-31.2c5.9-7.2 15.7-9.6 24.5-6.8l55.7 17.7c13.4-10.3 28.2-18.9 44-25.4l12.5-57.1c2-9.1 9-16.3 18.2-17.8C227.3 1.2 241.5 0 256 0s28.7 1.2 42.5 3.5c9.2 1.5 16.2 8.7 18.2 17.8l12.5 57.1c15.8 6.5 30.6 15.1 44 25.4l55.7-17.7c8.8-2.8 18.6-.3 24.5 6.8c8.1 9.8 15.5 20.2 22.1 31.2l4.7 8.1c6.1 11 11.4 22.4 15.8 34.3zM256 336a80 80 0 1 0 0-160 80 80 0 1 0 0 160z"/></svg>');
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.logout-icon::before {
  content: "⏻";
  font-size: 16px;
  display: block;
}

// Responsive design
@media (max-width: 768px) {
  .sidebar {
    width: 200px;
    transform: translateX(-100%);
    transition: transform 0.3s ease;
    
    &.mobile-open {
      transform: translateX(0);
    }
  }
  
  .sidebar-header {
    padding: 20px 15px;
    
    .logo {
      font-size: 1.5rem;
    }
  }
  
  .nav-link {
    padding: 12px 15px;
    
    .icon {
      margin-right: 12px;
    }
    
    span {
      font-size: 0.9rem;
    }
  }
  
  .sidebar-footer {
    padding: 15px;
    
    .logout-btn {
      padding: 12px 15px;
    }
  }
}

@media (max-width: 480px) {
  .sidebar {
    width: 100%;
  }
}
