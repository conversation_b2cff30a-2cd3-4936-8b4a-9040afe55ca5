.veritus-btn-primary {
  background: var(--veritus-primary);
  border: 2px solid var(--veritus-primary);
  color: var(--veritus-white);
  font-size: var(--veritus-font-size-base);
  font-weight: var(--veritus-font-weight-medium);
  padding: var(--veritus-spacing-md) var(--veritus-spacing-xl);
  border-radius: var(--veritus-border-radius);
  min-height: 48px;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover:not(:disabled) {
    background: var(--veritus-primary-dark);
    border-color: var(--veritus-primary-dark);
    transform: translateY(-1px);
  }
}

.veritus-btn-outline-gold {
  background: transparent;
  border: 2px solid var(--veritus-gold);
  color: var(--veritus-gold);
  font-size: var(--veritus-font-size-base);
  font-weight: var(--veritus-font-weight-medium);
  padding: var(--veritus-spacing-md) var(--veritus-spacing-xl);
  border-radius: var(--veritus-border-radius);
  min-height: 48px;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover:not(:disabled) {
    background: var(--veritus-gold);
    color: var(--veritus-white);
    transform: translateY(-1px);
  }
}

.veritus-btn-text {
  background: transparent;
  border: none;
  color: var(--veritus-primary);
  font-size: var(--veritus-font-size-sm);
  font-weight: var(--veritus-font-weight-medium);
  padding: var(--veritus-spacing-sm);
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover:not(:disabled) {
    color: var(--veritus-primary-dark);
  }
}

.veritus-btn-sm {
  padding: var(--veritus-spacing-sm) var(--veritus-spacing-md);
  font-size: var(--veritus-font-size-sm);
  min-height: 36px;
}

.veritus-btn-md {
  padding: var(--veritus-spacing-md) var(--veritus-spacing-xl);
  font-size: var(--veritus-font-size-base);
  min-height: 48px;
}

.veritus-btn-lg {
  padding: var(--veritus-spacing-xl) var(--veritus-spacing-2xl);
  font-size: var(--veritus-font-size-lg);
  min-height: 56px;
}

.veritus-btn-full {
  width: 100%;
}

.veritus-btn-disabled {
  opacity: 0.5;
  cursor: not-allowed;
}
