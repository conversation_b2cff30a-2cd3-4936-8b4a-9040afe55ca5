"use strict";(self.webpackChunkveritus_secretary=self.webpackChunkveritus_secretary||[]).push([[1049],{1049:(v,s,n)=>{n.r(s),n.d(s,{ion_avatar:()=>t,ion_badge:()=>i,ion_thumbnail:()=>u});var r=n(4363),d=n(611),l=n(333);const t=class{constructor(e){(0,r.r)(this,e)}render(){return(0,r.h)(r.H,{key:"f6014b524497bb18ae919ba6f6928407310d6870",class:(0,d.b)(this)},(0,r.h)("slot",{key:"192ff4a8e10c0b0a4a2ed795ff2675afa8b23449"}))}};t.style={ios:":host{border-radius:var(--border-radius);display:block}::slotted(ion-img),::slotted(img){border-radius:var(--border-radius);width:100%;height:100%;-o-object-fit:cover;object-fit:cover;overflow:hidden}:host{--border-radius:50%;width:48px;height:48px}",md:":host{border-radius:var(--border-radius);display:block}::slotted(ion-img),::slotted(img){border-radius:var(--border-radius);width:100%;height:100%;-o-object-fit:cover;object-fit:cover;overflow:hidden}:host{--border-radius:50%;width:64px;height:64px}"};const i=class{constructor(e){(0,r.r)(this,e),this.color=void 0}render(){const e=(0,d.b)(this);return(0,r.h)(r.H,{key:"22d41ceefb76f40dfbf739fd71483f1272a45858",class:(0,l.c)(this.color,{[e]:!0})},(0,r.h)("slot",{key:"e7e65463bac5903971a8f9f6be55515f42b81a83"}))}};i.style={ios:":host{--background:var(--ion-color-primary, #3880ff);--color:var(--ion-color-primary-contrast, #fff);--padding-top:3px;--padding-end:8px;--padding-bottom:3px;--padding-start:8px;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);display:inline-block;min-width:10px;background:var(--background);color:var(--color);font-family:var(--ion-font-family, inherit);font-size:0.8125rem;font-weight:bold;line-height:1;text-align:center;white-space:nowrap;contain:content;vertical-align:baseline}:host(.ion-color){background:var(--ion-color-base);color:var(--ion-color-contrast)}:host(:empty){display:none}:host{border-radius:10px;font-size:max(13px, 0.8125rem)}",md:":host{--background:var(--ion-color-primary, #3880ff);--color:var(--ion-color-primary-contrast, #fff);--padding-top:3px;--padding-end:8px;--padding-bottom:3px;--padding-start:8px;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);display:inline-block;min-width:10px;background:var(--background);color:var(--color);font-family:var(--ion-font-family, inherit);font-size:0.8125rem;font-weight:bold;line-height:1;text-align:center;white-space:nowrap;contain:content;vertical-align:baseline}:host(.ion-color){background:var(--ion-color-base);color:var(--ion-color-contrast)}:host(:empty){display:none}:host{--padding-top:3px;--padding-end:4px;--padding-bottom:4px;--padding-start:4px;border-radius:4px}"};const u=class{constructor(e){(0,r.r)(this,e)}render(){return(0,r.h)(r.H,{key:"d2667635930e4c0896805f452357e7dc9086bc72",class:(0,d.b)(this)},(0,r.h)("slot",{key:"66eb1487f3da4da2ef71b812a8d0f0fe884c7d81"}))}};u.style=":host{--size:48px;--border-radius:0;border-radius:var(--border-radius);display:block;width:var(--size);height:var(--size)}::slotted(ion-img),::slotted(img){border-radius:var(--border-radius);width:100%;height:100%;-o-object-fit:cover;object-fit:cover;overflow:hidden}"},333:(v,s,n)=>{n.d(s,{c:()=>l,g:()=>p,h:()=>d,o:()=>g});var r=n(467);const d=(t,o)=>null!==o.closest(t),l=(t,o)=>"string"==typeof t&&t.length>0?Object.assign({"ion-color":!0,[`ion-color-${t}`]:!0},o):o,p=t=>{const o={};return(t=>void 0!==t?(Array.isArray(t)?t:t.split(" ")).filter(a=>null!=a).map(a=>a.trim()).filter(a=>""!==a):[])(t).forEach(a=>o[a]=!0),o},f=/^[a-z][a-z0-9+\-.]*:/,g=function(){var t=(0,r.A)(function*(o,a,b,c){if(null!=o&&"#"!==o[0]&&!f.test(o)){const i=document.querySelector("ion-router");if(i)return a?.preventDefault(),i.push(o,b,c)}return!1});return function(a,b,c,i){return t.apply(this,arguments)}}()}}]);