<ion-header [translucent]="true">
  <ion-toolbar>
    <ion-title>Case Files</ion-title>
    <ion-buttons slot="start">
      <ion-back-button defaultHref="/tabs/dashboard"></ion-back-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>

<ion-content [fullscreen]="true">
  <div class="case-files-container">

    <!-- Header Section -->
    <div class="header-section">
      <h1 class="page-title">{{ selectedCase?.title || 'Case Files' }}</h1>
      <p class="page-subtitle">Manage and organize case documents</p>
    </div>

    <!-- Case Selection -->
    <div class="case-selector" *ngIf="cases.length > 0">
      <ion-item>
        <ion-label>Select Case</ion-label>
        <ion-select
          [(ngModel)]="selectedCaseId"
          (ionChange)="onCaseChange()"
          placeholder="Choose a case">
          <ion-select-option
            *ngFor="let case of cases"
            [value]="case.id">
            {{ case.title }}
          </ion-select-option>
        </ion-select>
      </ion-item>
    </div>



      <!-- Folders Grid -->
      <div class="folders-grid" *ngIf="caseFolders.length > 0">
        <h3 class="grid-title">Folders</h3>
        <div class="folder-items">
          <ion-card class="folder-card" *ngFor="let folder of caseFolders" (click)="navigateToFolder(folder)">
            <div class="folder-icon">
              <ion-icon name="folder" color="warning"></ion-icon>
            </div>
            <div class="folder-info">
              <h3 class="folder-name">{{ folder.name }}</h3>
              <p class="folder-details">{{ folder.fileCount }} files • {{ folder.subFolderCount }} folders</p>
              <p class="folder-created">Created {{ formatDate(folder.createdAt) }}</p>
            </div>
          </ion-card>
        </div>
      </div>

      <!-- Loading -->
      <div class="loading-container" *ngIf="isLoading">
        <ion-spinner name="crescent"></ion-spinner>
        <p>Loading files...</p>
      </div>

      <!-- Files Grid -->
      <div class="files-grid" *ngIf="!isLoading && caseFiles.length > 0">
        <h3 class="grid-title" *ngIf="caseFolders.length > 0">Files</h3>
        <div class="file-items">
          <ion-card class="file-card" *ngFor="let file of caseFiles" (click)="onFileClick(file)">
            <div class="file-icon">
              <ion-icon [name]="getFileIcon(file.type)" [color]="getFileColor(file.type)"></ion-icon>
            </div>
            <div class="file-info">
              <h3 class="file-name">{{ file.name }}</h3>
              <p class="file-details">{{ formatFileSize(file.size) }} • {{ formatDate(file.uploadedAt) }}</p>
              <p class="file-uploader">By {{ file.uploadedBy }}</p>
            </div>
            <div class="file-actions">
              <ion-button fill="clear" size="small" (click)="downloadFile(file, $event)">
                <ion-icon name="download" slot="icon-only"></ion-icon>
              </ion-button>
              <ion-button fill="clear" size="small" color="danger" (click)="deleteFile(file, $event)">
                <ion-icon name="trash" slot="icon-only"></ion-icon>
              </ion-button>
            </div>
          </ion-card>
        </div>
      </div>

      <!-- Empty State -->
      <div class="empty-state" *ngIf="!isLoading && caseFolders.length === 0 && caseFiles.length === 0">
        <ion-icon name="folder-open-outline" class="empty-icon"></ion-icon>
        <h3>No folders or files yet</h3>
        <p>Create a folder or upload your first document to get started</p>
        <div class="empty-actions">
        </div>
      </div>
    </div>

    <!-- No Case Selected -->
    <div class="no-case-selected" *ngIf="!selectedCaseId && cases.length > 0">
      <ion-icon name="briefcase-outline" class="empty-icon"></ion-icon>
      <h3>Select a case to manage files</h3>
      <p>Choose a case from the dropdown above to view and upload documents</p>
    </div>
</ion-content>

<ion-fab vertical="bottom" horizontal="end" slot="fixed" *ngIf="selectedCaseId">
  <ion-button expand="block" shape="round" color="primary" (click)="triggerFileInput()" style="border-radius: 12px; padding: 0 16px; height: 48px;">
    <ion-icon name="cloud-upload-outline" slot="start"></ion-icon>
    Upload File
  </ion-button>
  <input
    type="file"
    #fileInput
    (change)="onFilesSelected($event)"
    hidden
    accept=".pdf,.doc,.docx,.jpg,.jpeg,.png,.txt" />
</ion-fab>
