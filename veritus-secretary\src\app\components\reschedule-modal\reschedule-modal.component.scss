.reschedule-toolbar {
  --background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
  --color: white;
}

.reschedule-content {
  --background: #f8f9fa;
}

.reschedule-container {
  padding: 20px;
  max-width: 600px;
  margin: 0 auto;
}

.current-appointment-section {
  margin-bottom: 32px;
  
  h3 {
    color: #1a1a2e;
    margin-bottom: 16px;
    font-weight: 600;
  }
  
  .appointment-card {
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border-left: 4px solid #3b82f6;
    
    .appointment-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: 16px;
      
      .client-info {
        h4 {
          margin: 0 0 4px 0;
          color: #1a1a2e;
          font-size: 1.2rem;
        }
        
        .appointment-type {
          margin: 0;
          color: #6b7280;
          font-size: 0.9rem;
          background: #e5e7eb;
          padding: 4px 8px;
          border-radius: 4px;
          display: inline-block;
        }
      }
      
      .lawyer-info {
        text-align: right;
        
        .lawyer-name {
          margin: 0;
          color: #374151;
          font-weight: 500;
        }
      }
    }
    
    .appointment-details {
      display: flex;
      gap: 24px;
      
      .detail-item {
        display: flex;
        align-items: center;
        gap: 8px;
        color: #4b5563;
        
        ion-icon {
          color: #3b82f6;
        }
      }
    }
  }
}

.reschedule-form-section {
  h3 {
    color: #1a1a2e;
    margin-bottom: 24px;
    font-weight: 600;
  }
  
  .quick-date-selection {
    margin-bottom: 24px;
    
    h4 {
      color: #374151;
      margin-bottom: 12px;
      font-size: 1rem;
    }
    
    .quick-buttons {
      display: flex;
      gap: 8px;
      flex-wrap: wrap;
    }
  }
  
  .form-field {
    margin-bottom: 24px;
    
    ion-label {
      color: #374151;
      font-weight: 500;
      margin-bottom: 8px;
    }
    
    .date-picker {
      background: white;
      border-radius: 8px;
      border: 1px solid #d1d5db;
      padding: 12px;
    }
    
    .loading-slots {
      display: flex;
      align-items: center;
      gap: 12px;
      padding: 16px;
      background: white;
      border-radius: 8px;
      border: 1px solid #d1d5db;
      color: #6b7280;
    }
    
    .time-slots-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
      gap: 8px;
      padding: 16px;
      background: white;
      border-radius: 8px;
      border: 1px solid #d1d5db;
      
      .time-slot-button {
        margin: 0;
        height: 40px;
      }
    }
    
    .no-slots-message {
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 16px;
      background: #fef3c7;
      border: 1px solid #f59e0b;
      border-radius: 8px;
      color: #92400e;
      
      ion-icon {
        color: #f59e0b;
      }
    }
    
    .reason-textarea {
      background: white;
      border-radius: 8px;
      border: 1px solid #d1d5db;
      padding: 12px;
      min-height: 100px;
    }
    
    .character-count {
      text-align: right;
      font-size: 0.8rem;
      color: #6b7280;
      margin-top: 4px;
    }
    
    .validation-error {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-top: 8px;
      color: #dc2626;
      font-size: 0.9rem;
      
      ion-icon {
        color: #dc2626;
      }
    }
  }
}

.validation-summary {
  background: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 24px;
  
  h4 {
    color: #dc2626;
    margin: 0 0 12px 0;
    font-size: 1rem;
  }
  
  ul {
    margin: 0;
    padding-left: 20px;
    color: #7f1d1d;
    
    li {
      margin-bottom: 4px;
    }
  }
}

.action-buttons {
  display: flex;
  justify-content: space-between;
  gap: 16px;
  margin-bottom: 24px;
  
  ion-button {
    flex: 1;
    height: 48px;
  }
}

.info-note {
  display: flex;
  gap: 12px;
  padding: 16px;
  background: #eff6ff;
  border: 1px solid #bfdbfe;
  border-radius: 8px;
  
  ion-icon {
    color: #3b82f6;
    font-size: 1.2rem;
    margin-top: 2px;
  }
  
  .note-content {
    flex: 1;
    
    p {
      margin: 0;
      color: #1e40af;
      font-size: 0.9rem;
      line-height: 1.5;
      
      strong {
        color: #1d4ed8;
      }
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .reschedule-container {
    padding: 16px;
  }
  
  .appointment-details {
    flex-direction: column;
    gap: 12px;
  }
  
  .appointment-header {
    flex-direction: column;
    gap: 12px;
    
    .lawyer-info {
      text-align: left;
    }
  }
  
  .time-slots-grid {
    grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
  }
  
  .action-buttons {
    flex-direction: column;
    
    ion-button {
      width: 100%;
    }
  }
}
