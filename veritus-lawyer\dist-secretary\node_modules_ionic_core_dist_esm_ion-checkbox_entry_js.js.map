{"version": 3, "file": "node_modules_ionic_core_dist_esm_ion-checkbox_entry_js.js", "mappings": ";;;;;;;;;;;;;;AAAA;AACA;AACA;AAC2D;;AAE3D;AACA;AACA;AACA;AACA;AACA;AACA,MAAME,0BAA0B,GAAIC,EAAE,IAAK;EACvC,MAAMC,SAAS,GAAGD,EAAE;EACpB,IAAIE,aAAa;EACjB,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;IAC3B,IAAID,aAAa,KAAKE,SAAS,EAAE;MAC7B;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACY,MAAMC,YAAY,GAAGJ,SAAS,CAACK,KAAK,KAAKF,SAAS,IAAIG,YAAY,CAACN,SAAS,CAAC;MAC7E,MAAMO,qBAAqB,GAAGP,SAAS,CAACQ,YAAY,CAAC,YAAY,CAAC;MAC9D;MACCR,SAAS,CAACQ,YAAY,CAAC,iBAAiB,CAAC,IAAIR,SAAS,CAACS,UAAU,KAAK,IAAK;MAChF,MAAMC,eAAe,GAAGb,uDAAa,CAACG,SAAS,CAAC;MAChD;AACZ;AACA;AACA;MACYC,aAAa,GACTD,SAAS,CAACW,MAAM,KAAK,IAAI,IAAK,CAACP,YAAY,IAAI,CAACG,qBAAqB,IAAIG,eAAe,KAAK,IAAK;IAC1G;IACA,OAAOT,aAAa;EACxB,CAAC;EACD,OAAO;IAAEC;EAAiB,CAAC;AAC/B,CAAC;AACD,MAAMI,YAAY,GAAIN,SAAS,IAAK;EAChC;AACJ;AACA;AACA;AACA;EACI,IAAIY,2BAA2B,CAACC,QAAQ,CAACb,SAAS,CAACc,OAAO,CAAC,IAAId,SAAS,CAACe,aAAa,CAAC,gBAAgB,CAAC,KAAK,IAAI,EAAE;IAC/G,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;EACI,IAAIC,6BAA6B,CAACH,QAAQ,CAACb,SAAS,CAACc,OAAO,CAAC,IAAId,SAAS,CAACiB,WAAW,KAAK,EAAE,EAAE;IAC3F,OAAO,IAAI;EACf;EACA,OAAO,KAAK;AAChB,CAAC;AACD,MAAML,2BAA2B,GAAG,CAAC,WAAW,EAAE,cAAc,EAAE,YAAY,EAAE,WAAW,CAAC;AAC5F,MAAMI,6BAA6B,GAAG,CAAC,YAAY,EAAE,cAAc,EAAE,WAAW,CAAC;;;;;;;;;;;;;;;;;;;;;AC7DjF;AACA;AACA;AAC6G;AAC7B;AAC8B;AACnD;AACqB;AACnB;AAE7D,MAAMsB,cAAc,GAAG,knNAAknN;AACzoN,MAAMC,oBAAoB,GAAGD,cAAc;AAE3C,MAAME,aAAa,GAAG,4gOAA4gO;AACliO,MAAMC,mBAAmB,GAAGD,aAAa;AAEzC,MAAME,QAAQ,GAAG,MAAM;EACnBC,WAAWA,CAACC,OAAO,EAAE;IACjBxB,qDAAgB,CAAC,IAAI,EAAEwB,OAAO,CAAC;IAC/B,IAAI,CAACC,SAAS,GAAGvB,qDAAW,CAAC,IAAI,EAAE,WAAW,EAAE,CAAC,CAAC;IAClD,IAAI,CAACwB,QAAQ,GAAGxB,qDAAW,CAAC,IAAI,EAAE,UAAU,EAAE,CAAC,CAAC;IAChD,IAAI,CAACyB,OAAO,GAAGzB,qDAAW,CAAC,IAAI,EAAE,SAAS,EAAE,CAAC,CAAC;IAC9C,IAAI,CAAC0B,QAAQ,GAAG1B,qDAAW,CAAC,IAAI,EAAE,UAAU,EAAE,CAAC,CAAC;IAChD,IAAI,CAAC2B,OAAO,GAAG,UAAUC,WAAW,EAAE,EAAE;IACxC,IAAI,CAACC,mBAAmB,GAAG,CAAC,CAAC;IAC7B;IACA;IACA,IAAI,CAACC,2BAA2B,GAAG,KAAK;IACxC;AACR;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACC,UAAU,GAAIC,KAAK,IAAK;MACzB,MAAMC,SAAS,GAAI,IAAI,CAACC,OAAO,GAAGF,KAAM;MACxC,IAAI,CAACT,SAAS,CAACY,IAAI,CAAC;QAChBD,OAAO,EAAED,SAAS;QAClBG,KAAK,EAAE,IAAI,CAACA;MAChB,CAAC,CAAC;IACN,CAAC;IACD,IAAI,CAACC,aAAa,GAAIC,EAAE,IAAK;MACzBA,EAAE,CAACC,cAAc,CAAC,CAAC;MACnB,IAAI,CAACC,QAAQ,CAAC,CAAC;MACf,IAAI,CAACT,UAAU,CAAC,CAAC,IAAI,CAACG,OAAO,CAAC;MAC9B,IAAI,CAACO,aAAa,GAAG,KAAK;IAC9B,CAAC;IACD,IAAI,CAACC,OAAO,GAAG,MAAM;MACjB,IAAI,CAAClB,QAAQ,CAACW,IAAI,CAAC,CAAC;IACxB,CAAC;IACD,IAAI,CAACQ,MAAM,GAAG,MAAM;MAChB,IAAI,CAAClB,OAAO,CAACU,IAAI,CAAC,CAAC;IACvB,CAAC;IACD,IAAI,CAACS,OAAO,GAAIN,EAAE,IAAK;MACnB,IAAI,IAAI,CAACO,QAAQ,EAAE;QACf;MACJ;MACA,IAAI,CAACR,aAAa,CAACC,EAAE,CAAC;IAC1B,CAAC;IACD,IAAI,CAACQ,KAAK,GAAGjE,SAAS;IACtB,IAAI,CAACkE,IAAI,GAAG,IAAI,CAACpB,OAAO;IACxB,IAAI,CAACO,OAAO,GAAG,KAAK;IACpB,IAAI,CAACO,aAAa,GAAG,KAAK;IAC1B,IAAI,CAACI,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACT,KAAK,GAAG,IAAI;IACjB,IAAI,CAACY,cAAc,GAAG,OAAO;IAC7B,IAAI,CAACC,OAAO,GAAG,eAAe;IAC9B,IAAI,CAACC,SAAS,GAAG,QAAQ;IACzB,IAAI,CAAC7D,MAAM,GAAGR,SAAS;EAC3B;EACAsE,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAACC,oBAAoB,GAAG5E,+DAA0B,CAAC,IAAI,CAACC,EAAE,CAAC,CAAC,CAAC;EACrE;EACA4E,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAACC,SAAS,CAAC,CAAC;IAChB;IACA,IAAI,CAAC,IAAI,CAACF,oBAAoB,CAACxE,gBAAgB,CAAC,CAAC,EAAE;MAC/C,IAAI,CAACiD,mBAAmB,GAAG0B,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAElD,uDAAqB,CAAC,IAAI,CAAC7B,EAAE,CAAC,CAAC;IAChF;EACJ;EACAgF,YAAYA,CAAA,EAAG;IACX,IAAI,CAACH,SAAS,CAAC,CAAC;EACpB;EACAA,SAASA,CAAA,EAAG;IACR,MAAMI,KAAK,GAAG;MACV,sBAAsB,EAAE,IAAI,CAACb,QAAQ;MACrC;MACAxD,MAAM,EAAE,CAAC,CAAC,IAAI,CAACA;IACnB,CAAC;IACD;IACA,IAAI,IAAI,CAAC+D,oBAAoB,CAACxE,gBAAgB,CAAC,CAAC,EAAE;MAC9C8E,KAAK,CAAC,kBAAkB,CAAC,GAAG,IAAI,CAACxB,OAAO;IAC5C;IACA,IAAI,CAACR,QAAQ,CAACS,IAAI,CAACuB,KAAK,CAAC;EAC7B;EACAlB,QAAQA,CAAA,EAAG;IACP,IAAI,IAAI,CAACmB,OAAO,EAAE;MACd,IAAI,CAACA,OAAO,CAACC,KAAK,CAAC,CAAC;IACxB;EACJ;EACA;EACAC,MAAMA,CAAA,EAAG;IACL,MAAM;MAAET;IAAqB,CAAC,GAAG,IAAI;IACrC,OAAOA,oBAAoB,CAACxE,gBAAgB,CAAC,CAAC,GAAG,IAAI,CAACkF,oBAAoB,CAAC,CAAC,GAAG,IAAI,CAACC,cAAc,CAAC,CAAC;EACxG;EACAA,cAAcA,CAAA,EAAG;IACb,MAAM;MAAEjB,KAAK;MAAEZ,OAAO;MAAEW,QAAQ;MAAEpE,EAAE;MAAEuF,UAAU;MAAEvB,aAAa;MAAEZ,mBAAmB;MAAEF,OAAO;MAAEsB,OAAO;MAAED,cAAc;MAAED,IAAI;MAAEX,KAAK;MAAEc;IAAW,CAAC,GAAG,IAAI;IACxJ,MAAMe,IAAI,GAAGlD,4DAAU,CAAC,IAAI,CAAC;IAC7B,MAAMmD,IAAI,GAAGF,UAAU,CAACC,IAAI,EAAExB,aAAa,CAAC;IAC5ClC,uDAAiB,CAAC,IAAI,EAAE9B,EAAE,EAAEsE,IAAI,EAAEb,OAAO,GAAGE,KAAK,GAAG,EAAE,EAAES,QAAQ,CAAC;IACjE,OAAQvE,qDAAC,CAAC4B,iDAAI,EAAE;MAAE,cAAc,EAAEuC,aAAa,GAAG,OAAO,GAAG,GAAGP,OAAO,EAAE;MAAEiC,KAAK,EAAEvD,qDAAkB,CAACkC,KAAK,EAAE;QACnG,CAACmB,IAAI,GAAG,IAAI;QACZ,SAAS,EAAEpD,qDAAW,CAAC,UAAU,EAAEpC,EAAE,CAAC;QACtC,kBAAkB,EAAEyD,OAAO;QAC3B,mBAAmB,EAAEW,QAAQ;QAC7B,wBAAwB,EAAEJ,aAAa;QACvC2B,WAAW,EAAE,IAAI;QACjB,CAAC,oBAAoBnB,OAAO,EAAE,GAAG,IAAI;QACrC,CAAC,sBAAsBC,SAAS,EAAE,GAAG,IAAI;QACzC,CAAC,4BAA4BF,cAAc,EAAE,GAAG;MACpD,CAAC,CAAC;MAAEJ,OAAO,EAAE,IAAI,CAACA;IAAQ,CAAC,EAAEtE,qDAAC,CAAC,OAAO,EAAE;MAAE6F,KAAK,EAAE;IAAmB,CAAC,EAAE7F,qDAAC,CAAC,OAAO,EAAEiF,MAAM,CAACC,MAAM,CAAC;MAAEa,IAAI,EAAE,UAAU;MAAEnC,OAAO,EAAEA,OAAO,GAAG,IAAI,GAAGrD,SAAS;MAAEgE,QAAQ,EAAEA,QAAQ;MAAEyB,EAAE,EAAE3C,OAAO;MAAE4C,QAAQ,EAAE,IAAI,CAAClC,aAAa;MAAEK,OAAO,EAAEA,CAAA,KAAM,IAAI,CAACA,OAAO,CAAC,CAAC;MAAEC,MAAM,EAAEA,CAAA,KAAM,IAAI,CAACA,MAAM,CAAC,CAAC;MAAE6B,GAAG,EAAGb,OAAO,IAAM,IAAI,CAACA,OAAO,GAAGA;IAAS,CAAC,EAAE9B,mBAAmB,CAAC,CAAC,EAAEvD,qDAAC,CAAC,KAAK,EAAE;MAAE6F,KAAK,EAAE;QACvW,oBAAoB,EAAE,IAAI;QAC1B,2BAA2B,EAAE1F,EAAE,CAACkB,WAAW,KAAK;MACpD,CAAC;MAAE8E,IAAI,EAAE;IAAQ,CAAC,EAAEnG,qDAAC,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC,EAAEA,qDAAC,CAAC,KAAK,EAAE;MAAE6F,KAAK,EAAE;IAAiB,CAAC,EAAE7F,qDAAC,CAAC,KAAK,EAAE;MAAE6F,KAAK,EAAE,eAAe;MAAEO,OAAO,EAAE,WAAW;MAAED,IAAI,EAAE;IAAY,CAAC,EAAEP,IAAI,CAAC,CAAC,CAAC,CAAC;EAC1K;EACA;EACAJ,oBAAoBA,CAAA,EAAG;IACnB,IAAI,CAAC,IAAI,CAAChC,2BAA2B,EAAE;MACnCnB,qDAAe,CAAC;AAC5B;AACA;AACA;AACA;AACA,gNAAgN,EAAE,IAAI,CAAClC,EAAE,CAAC;MAC9M,IAAI,IAAI,CAACY,MAAM,EAAE;QACbsB,qDAAe,CAAC;AAChC,wHAAwH,EAAE,IAAI,CAAClC,EAAE,CAAC;MACtH;MACA,IAAI,CAACqD,2BAA2B,GAAG,IAAI;IAC3C;IACA,MAAM;MAAEgB,KAAK;MAAEZ,OAAO;MAAEW,QAAQ;MAAEpE,EAAE;MAAEuF,UAAU;MAAEvB,aAAa;MAAEd,OAAO;MAAEoB,IAAI;MAAEX;IAAM,CAAC,GAAG,IAAI;IAC9F,MAAM6B,IAAI,GAAGlD,4DAAU,CAAC,IAAI,CAAC;IAC7B,MAAM;MAAEhC,KAAK;MAAE4F,OAAO;MAAEC;IAAU,CAAC,GAAGnE,uDAAY,CAAChC,EAAE,EAAEkD,OAAO,CAAC;IAC/D,MAAMuC,IAAI,GAAGF,UAAU,CAACC,IAAI,EAAExB,aAAa,CAAC;IAC5ClC,uDAAiB,CAAC,IAAI,EAAE9B,EAAE,EAAEsE,IAAI,EAAEb,OAAO,GAAGE,KAAK,GAAG,EAAE,EAAES,QAAQ,CAAC;IACjE,OAAQvE,qDAAC,CAAC4B,iDAAI,EAAE;MAAE,iBAAiB,EAAEnB,KAAK,GAAG4F,OAAO,GAAG,IAAI;MAAE,cAAc,EAAE,GAAGzC,OAAO,EAAE;MAAE,aAAa,EAAEW,QAAQ,GAAG,MAAM,GAAG,IAAI;MAAEgC,IAAI,EAAE,UAAU;MAAEV,KAAK,EAAEvD,qDAAkB,CAACkC,KAAK,EAAE;QAC/K,CAACmB,IAAI,GAAG,IAAI;QACZ,SAAS,EAAEpD,qDAAW,CAAC,UAAU,EAAEpC,EAAE,CAAC;QACtC,kBAAkB,EAAEyD,OAAO;QAC3B,mBAAmB,EAAEW,QAAQ;QAC7B,wBAAwB,EAAEJ,aAAa;QACvC,iBAAiB,EAAE,IAAI;QACvB2B,WAAW,EAAE;MACjB,CAAC,CAAC;MAAExB,OAAO,EAAE,IAAI,CAACA;IAAQ,CAAC,EAAEtE,qDAAC,CAAC,KAAK,EAAE;MAAE6F,KAAK,EAAE,eAAe;MAAEO,OAAO,EAAE,WAAW;MAAED,IAAI,EAAE;IAAY,CAAC,EAAEP,IAAI,CAAC,EAAE5F,qDAAC,CAAC,OAAO,EAAE;MAAEwG,OAAO,EAAEnD;IAAQ,CAAC,EAAEiD,SAAS,CAAC,EAAEtG,qDAAC,CAAC,OAAO,EAAE;MAAE+F,IAAI,EAAE,UAAU;MAAE,cAAc,EAAE,GAAGnC,OAAO,EAAE;MAAEW,QAAQ,EAAEA,QAAQ;MAAEyB,EAAE,EAAE3C,OAAO;MAAE4C,QAAQ,EAAE,IAAI,CAAClC,aAAa;MAAEK,OAAO,EAAEA,CAAA,KAAM,IAAI,CAACA,OAAO,CAAC,CAAC;MAAEC,MAAM,EAAEA,CAAA,KAAM,IAAI,CAACA,MAAM,CAAC,CAAC;MAAE6B,GAAG,EAAGb,OAAO,IAAM,IAAI,CAACA,OAAO,GAAGA;IAAS,CAAC,CAAC,CAAC;EAC7Y;EACAK,UAAUA,CAACC,IAAI,EAAExB,aAAa,EAAE;IAC5B,IAAIyB,IAAI,GAAGzB,aAAa,GAAInE,qDAAC,CAAC,MAAM,EAAE;MAAEyB,CAAC,EAAE,aAAa;MAAE0E,IAAI,EAAE;IAAO,CAAC,CAAC,GAAKnG,qDAAC,CAAC,MAAM,EAAE;MAAEyB,CAAC,EAAE,2BAA2B;MAAE0E,IAAI,EAAE;IAAO,CAAC,CAAE;IAC1I,IAAIR,IAAI,KAAK,IAAI,EAAE;MACfC,IAAI,GAAGzB,aAAa,GAAInE,qDAAC,CAAC,MAAM,EAAE;QAAEyB,CAAC,EAAE,UAAU;QAAE0E,IAAI,EAAE;MAAO,CAAC,CAAC,GAAKnG,qDAAC,CAAC,MAAM,EAAE;QAAEyB,CAAC,EAAE,kCAAkC;QAAE0E,IAAI,EAAE;MAAO,CAAC,CAAE;IAC9I;IACA,OAAOP,IAAI;EACf;EACA,IAAIzF,EAAEA,CAAA,EAAG;IAAE,OAAO2B,qDAAU,CAAC,IAAI,CAAC;EAAE;EACpC,WAAW2E,QAAQA,CAAA,EAAG;IAAE,OAAO;MAC3B,SAAS,EAAE,CAAC,cAAc,CAAC;MAC3B,UAAU,EAAE,CAAC,cAAc;IAC/B,CAAC;EAAE;AACP,CAAC;AACD,IAAInD,WAAW,GAAG,CAAC;AACnBR,QAAQ,CAACsC,KAAK,GAAG;EACbsB,GAAG,EAAE/D,oBAAoB;EACzBgE,EAAE,EAAE9D;AACR,CAAC", "sources": ["./node_modules/@ionic/core/dist/esm/form-controller-21dd62b1.js", "./node_modules/@ionic/core/dist/esm/ion-checkbox.entry.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { h as findItemLabel } from './helpers-be245865.js';\n\n/**\n * Creates a controller that tracks whether a form control is using the legacy or modern syntax. This should be removed when the legacy form control syntax is removed.\n *\n * @internal\n * @prop el: The Ionic form component to reference\n */\nconst createLegacyFormController = (el) => {\n    const controlEl = el;\n    let legacyControl;\n    const hasLegacyControl = () => {\n        if (legacyControl === undefined) {\n            /**\n             * Detect if developers are using the legacy form control syntax\n             * so a deprecation warning is logged. This warning can be disabled\n             * by either using the new `label` property or setting `aria-label`\n             * on the control.\n             * Alternatively, components that use a slot for the label\n             * can check to see if the component has slotted text\n             * in the light DOM.\n             */\n            const hasLabelProp = controlEl.label !== undefined || hasLabelSlot(controlEl);\n            const hasAriaLabelAttribute = controlEl.hasAttribute('aria-label') ||\n                // Shadow DOM form controls cannot use aria-labelledby\n                (controlEl.hasAttribute('aria-labelledby') && controlEl.shadowRoot === null);\n            const legacyItemLabel = findItemLabel(controlEl);\n            /**\n             * Developers can manually opt-out of the modern form markup\n             * by setting `legacy=\"true\"` on components.\n             */\n            legacyControl =\n                controlEl.legacy === true || (!hasLabelProp && !hasAriaLabelAttribute && legacyItemLabel !== null);\n        }\n        return legacyControl;\n    };\n    return { hasLegacyControl };\n};\nconst hasLabelSlot = (controlEl) => {\n    /**\n     * Components that have a named label slot\n     * also have other slots, so we need to query for\n     * anything that is explicitly passed to slot=\"label\"\n     */\n    if (NAMED_LABEL_SLOT_COMPONENTS.includes(controlEl.tagName) && controlEl.querySelector('[slot=\"label\"]') !== null) {\n        return true;\n    }\n    /**\n     * Components that have an unnamed slot for the label\n     * have no other slots, so we can check the textContent\n     * of the element.\n     */\n    if (UNNAMED_LABEL_SLOT_COMPONENTS.includes(controlEl.tagName) && controlEl.textContent !== '') {\n        return true;\n    }\n    return false;\n};\nconst NAMED_LABEL_SLOT_COMPONENTS = ['ION-INPUT', 'ION-TEXTAREA', 'ION-SELECT', 'ION-RANGE'];\nconst UNNAMED_LABEL_SLOT_COMPONENTS = ['ION-TOGGLE', 'ION-CHECKBOX', 'ION-RADIO'];\n\nexport { createLegacyFormController as c };\n", "/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, d as createEvent, h, H as Host, f as getElement } from './index-a1a47f01.js';\nimport { c as createLegacyFormController } from './form-controller-21dd62b1.js';\nimport { i as inheritAriaAttributes, d as renderHiddenInput, e as getAriaLabel } from './helpers-be245865.js';\nimport { p as printIonWarning } from './index-9b0d46f4.js';\nimport { c as createColorClasses, h as hostContext } from './theme-01f3f29c.js';\nimport { b as getIonMode } from './ionic-global-94f25d1b.js';\n\nconst checkboxIosCss = \":host{--checkbox-background-checked:var(--ion-color-primary, #3880ff);--border-color-checked:var(--ion-color-primary, #3880ff);--checkmark-color:var(--ion-color-primary-contrast, #fff);--checkmark-width:1;--transition:none;display:inline-block;position:relative;cursor:pointer;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;z-index:2}:host(.in-item){width:100%;height:100%}:host([slot=start]:not(.legacy-checkbox)),:host([slot=end]:not(.legacy-checkbox)){width:auto}:host(.legacy-checkbox){width:var(--size);height:var(--size)}:host(.ion-color){--checkbox-background-checked:var(--ion-color-base);--border-color-checked:var(--ion-color-base);--checkmark-color:var(--ion-color-contrast)}:host(.legacy-checkbox) label{top:0;margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;position:absolute;width:100%;height:100%;border:0;background:transparent;cursor:pointer;-webkit-appearance:none;-moz-appearance:none;appearance:none;outline:none;display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;opacity:0}@supports (inset-inline-start: 0){:host(.legacy-checkbox) label{inset-inline-start:0}}@supports not (inset-inline-start: 0){:host(.legacy-checkbox) label{left:0}:host-context([dir=rtl]):host(.legacy-checkbox) label,:host-context([dir=rtl]).legacy-checkbox label{left:unset;right:unset;right:0}@supports selector(:dir(rtl)){:host(.legacy-checkbox:dir(rtl)) label{left:unset;right:unset;right:0}}}:host(.legacy-checkbox) label::-moz-focus-inner{border:0}.checkbox-wrapper{display:-ms-flexbox;display:flex;-ms-flex-positive:1;flex-grow:1;-ms-flex-align:center;align-items:center;height:inherit;cursor:inherit}.label-text-wrapper{text-overflow:ellipsis;white-space:nowrap;overflow:hidden}:host(.in-item:not(.legacy-checkbox)) .label-text-wrapper{margin-top:10px;margin-bottom:10px}:host(.in-item.checkbox-label-placement-stacked) .label-text-wrapper{margin-top:10px;margin-bottom:16px}:host(.in-item.checkbox-label-placement-stacked) .native-wrapper{margin-bottom:10px}.label-text-wrapper-hidden{display:none}input{position:absolute;top:0;left:0;right:0;bottom:0;width:100%;height:100%;margin:0;padding:0;border:0;outline:0;clip:rect(0 0 0 0);opacity:0;overflow:hidden;-webkit-appearance:none;-moz-appearance:none}.native-wrapper{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center}.checkbox-icon{border-radius:var(--border-radius);position:relative;-webkit-transition:var(--transition);transition:var(--transition);border-width:var(--border-width);border-style:var(--border-style);border-color:var(--border-color);background:var(--checkbox-background);-webkit-box-sizing:border-box;box-sizing:border-box}:host(.legacy-checkbox) .checkbox-icon{display:block;width:100%;height:100%}:host(:not(.legacy-checkbox)) .checkbox-icon{width:var(--size);height:var(--size)}.checkbox-icon path{fill:none;stroke:var(--checkmark-color);stroke-width:var(--checkmark-width);opacity:0}:host(.checkbox-justify-space-between) .checkbox-wrapper{-ms-flex-pack:justify;justify-content:space-between}:host(.checkbox-justify-start) .checkbox-wrapper{-ms-flex-pack:start;justify-content:start}:host(.checkbox-justify-end) .checkbox-wrapper{-ms-flex-pack:end;justify-content:end}:host(.checkbox-alignment-start) .checkbox-wrapper{-ms-flex-align:start;align-items:start}:host(.checkbox-alignment-center) .checkbox-wrapper{-ms-flex-align:center;align-items:center}:host(.checkbox-label-placement-start) .checkbox-wrapper{-ms-flex-direction:row;flex-direction:row}:host(.checkbox-label-placement-start) .label-text-wrapper{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px}:host(.checkbox-label-placement-end) .checkbox-wrapper{-ms-flex-direction:row-reverse;flex-direction:row-reverse}:host(.checkbox-label-placement-end) .label-text-wrapper{-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:0;margin-inline-end:0}:host(.checkbox-label-placement-fixed) .label-text-wrapper{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px}:host(.checkbox-label-placement-fixed) .label-text-wrapper{-ms-flex:0 0 100px;flex:0 0 100px;width:100px;min-width:100px;max-width:200px}:host(.checkbox-label-placement-stacked) .checkbox-wrapper{-ms-flex-direction:column;flex-direction:column}:host(.checkbox-label-placement-stacked) .label-text-wrapper{-webkit-transform:scale(0.75);transform:scale(0.75);margin-left:0;margin-right:0;margin-bottom:16px;max-width:calc(100% / 0.75)}:host(.checkbox-label-placement-stacked.checkbox-alignment-start) .label-text-wrapper{-webkit-transform-origin:left top;transform-origin:left top}:host-context([dir=rtl]):host(.checkbox-label-placement-stacked.checkbox-alignment-start) .label-text-wrapper,:host-context([dir=rtl]).checkbox-label-placement-stacked.checkbox-alignment-start .label-text-wrapper{-webkit-transform-origin:right top;transform-origin:right top}@supports selector(:dir(rtl)){:host(.checkbox-label-placement-stacked.checkbox-alignment-start:dir(rtl)) .label-text-wrapper{-webkit-transform-origin:right top;transform-origin:right top}}:host(.checkbox-label-placement-stacked.checkbox-alignment-center) .label-text-wrapper{-webkit-transform-origin:center top;transform-origin:center top}:host-context([dir=rtl]):host(.checkbox-label-placement-stacked.checkbox-alignment-center) .label-text-wrapper,:host-context([dir=rtl]).checkbox-label-placement-stacked.checkbox-alignment-center .label-text-wrapper{-webkit-transform-origin:calc(100% - center) top;transform-origin:calc(100% - center) top}@supports selector(:dir(rtl)){:host(.checkbox-label-placement-stacked.checkbox-alignment-center:dir(rtl)) .label-text-wrapper{-webkit-transform-origin:calc(100% - center) top;transform-origin:calc(100% - center) top}}:host(.checkbox-checked) .checkbox-icon,:host(.checkbox-indeterminate) .checkbox-icon{border-color:var(--border-color-checked);background:var(--checkbox-background-checked)}:host(.checkbox-checked) .checkbox-icon path,:host(.checkbox-indeterminate) .checkbox-icon path{opacity:1}:host(.checkbox-disabled){pointer-events:none}:host{--border-radius:50%;--border-width:0.0625rem;--border-style:solid;--border-color:rgba(var(--ion-text-color-rgb, 0, 0, 0), 0.23);--checkbox-background:var(--ion-item-background, var(--ion-background-color, #fff));--size:min(1.625rem, 65.988px)}:host(.checkbox-disabled){opacity:0.3}:host(.in-item.legacy-checkbox){-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:8px;margin-inline-end:8px;margin-top:10px;margin-bottom:9px;display:block;position:static}:host(.in-item.legacy-checkbox[slot=start]){-webkit-margin-start:2px;margin-inline-start:2px;-webkit-margin-end:16px;margin-inline-end:16px;margin-top:8px;margin-bottom:8px}\";\nconst IonCheckboxIosStyle0 = checkboxIosCss;\n\nconst checkboxMdCss = \":host{--checkbox-background-checked:var(--ion-color-primary, #3880ff);--border-color-checked:var(--ion-color-primary, #3880ff);--checkmark-color:var(--ion-color-primary-contrast, #fff);--checkmark-width:1;--transition:none;display:inline-block;position:relative;cursor:pointer;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;z-index:2}:host(.in-item){width:100%;height:100%}:host([slot=start]:not(.legacy-checkbox)),:host([slot=end]:not(.legacy-checkbox)){width:auto}:host(.legacy-checkbox){width:var(--size);height:var(--size)}:host(.ion-color){--checkbox-background-checked:var(--ion-color-base);--border-color-checked:var(--ion-color-base);--checkmark-color:var(--ion-color-contrast)}:host(.legacy-checkbox) label{top:0;margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;position:absolute;width:100%;height:100%;border:0;background:transparent;cursor:pointer;-webkit-appearance:none;-moz-appearance:none;appearance:none;outline:none;display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;opacity:0}@supports (inset-inline-start: 0){:host(.legacy-checkbox) label{inset-inline-start:0}}@supports not (inset-inline-start: 0){:host(.legacy-checkbox) label{left:0}:host-context([dir=rtl]):host(.legacy-checkbox) label,:host-context([dir=rtl]).legacy-checkbox label{left:unset;right:unset;right:0}@supports selector(:dir(rtl)){:host(.legacy-checkbox:dir(rtl)) label{left:unset;right:unset;right:0}}}:host(.legacy-checkbox) label::-moz-focus-inner{border:0}.checkbox-wrapper{display:-ms-flexbox;display:flex;-ms-flex-positive:1;flex-grow:1;-ms-flex-align:center;align-items:center;height:inherit;cursor:inherit}.label-text-wrapper{text-overflow:ellipsis;white-space:nowrap;overflow:hidden}:host(.in-item:not(.legacy-checkbox)) .label-text-wrapper{margin-top:10px;margin-bottom:10px}:host(.in-item.checkbox-label-placement-stacked) .label-text-wrapper{margin-top:10px;margin-bottom:16px}:host(.in-item.checkbox-label-placement-stacked) .native-wrapper{margin-bottom:10px}.label-text-wrapper-hidden{display:none}input{position:absolute;top:0;left:0;right:0;bottom:0;width:100%;height:100%;margin:0;padding:0;border:0;outline:0;clip:rect(0 0 0 0);opacity:0;overflow:hidden;-webkit-appearance:none;-moz-appearance:none}.native-wrapper{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center}.checkbox-icon{border-radius:var(--border-radius);position:relative;-webkit-transition:var(--transition);transition:var(--transition);border-width:var(--border-width);border-style:var(--border-style);border-color:var(--border-color);background:var(--checkbox-background);-webkit-box-sizing:border-box;box-sizing:border-box}:host(.legacy-checkbox) .checkbox-icon{display:block;width:100%;height:100%}:host(:not(.legacy-checkbox)) .checkbox-icon{width:var(--size);height:var(--size)}.checkbox-icon path{fill:none;stroke:var(--checkmark-color);stroke-width:var(--checkmark-width);opacity:0}:host(.checkbox-justify-space-between) .checkbox-wrapper{-ms-flex-pack:justify;justify-content:space-between}:host(.checkbox-justify-start) .checkbox-wrapper{-ms-flex-pack:start;justify-content:start}:host(.checkbox-justify-end) .checkbox-wrapper{-ms-flex-pack:end;justify-content:end}:host(.checkbox-alignment-start) .checkbox-wrapper{-ms-flex-align:start;align-items:start}:host(.checkbox-alignment-center) .checkbox-wrapper{-ms-flex-align:center;align-items:center}:host(.checkbox-label-placement-start) .checkbox-wrapper{-ms-flex-direction:row;flex-direction:row}:host(.checkbox-label-placement-start) .label-text-wrapper{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px}:host(.checkbox-label-placement-end) .checkbox-wrapper{-ms-flex-direction:row-reverse;flex-direction:row-reverse}:host(.checkbox-label-placement-end) .label-text-wrapper{-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:0;margin-inline-end:0}:host(.checkbox-label-placement-fixed) .label-text-wrapper{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px}:host(.checkbox-label-placement-fixed) .label-text-wrapper{-ms-flex:0 0 100px;flex:0 0 100px;width:100px;min-width:100px;max-width:200px}:host(.checkbox-label-placement-stacked) .checkbox-wrapper{-ms-flex-direction:column;flex-direction:column}:host(.checkbox-label-placement-stacked) .label-text-wrapper{-webkit-transform:scale(0.75);transform:scale(0.75);margin-left:0;margin-right:0;margin-bottom:16px;max-width:calc(100% / 0.75)}:host(.checkbox-label-placement-stacked.checkbox-alignment-start) .label-text-wrapper{-webkit-transform-origin:left top;transform-origin:left top}:host-context([dir=rtl]):host(.checkbox-label-placement-stacked.checkbox-alignment-start) .label-text-wrapper,:host-context([dir=rtl]).checkbox-label-placement-stacked.checkbox-alignment-start .label-text-wrapper{-webkit-transform-origin:right top;transform-origin:right top}@supports selector(:dir(rtl)){:host(.checkbox-label-placement-stacked.checkbox-alignment-start:dir(rtl)) .label-text-wrapper{-webkit-transform-origin:right top;transform-origin:right top}}:host(.checkbox-label-placement-stacked.checkbox-alignment-center) .label-text-wrapper{-webkit-transform-origin:center top;transform-origin:center top}:host-context([dir=rtl]):host(.checkbox-label-placement-stacked.checkbox-alignment-center) .label-text-wrapper,:host-context([dir=rtl]).checkbox-label-placement-stacked.checkbox-alignment-center .label-text-wrapper{-webkit-transform-origin:calc(100% - center) top;transform-origin:calc(100% - center) top}@supports selector(:dir(rtl)){:host(.checkbox-label-placement-stacked.checkbox-alignment-center:dir(rtl)) .label-text-wrapper{-webkit-transform-origin:calc(100% - center) top;transform-origin:calc(100% - center) top}}:host(.checkbox-checked) .checkbox-icon,:host(.checkbox-indeterminate) .checkbox-icon{border-color:var(--border-color-checked);background:var(--checkbox-background-checked)}:host(.checkbox-checked) .checkbox-icon path,:host(.checkbox-indeterminate) .checkbox-icon path{opacity:1}:host(.checkbox-disabled){pointer-events:none}:host{--border-radius:calc(var(--size) * .125);--border-width:2px;--border-style:solid;--border-color:rgb(var(--ion-text-color-rgb, 0, 0, 0), 0.6);--checkmark-width:3;--checkbox-background:var(--ion-item-background, var(--ion-background-color, #fff));--transition:background 180ms cubic-bezier(0.4, 0, 0.2, 1);--size:18px}.checkbox-icon path{stroke-dasharray:30;stroke-dashoffset:30}:host(.checkbox-checked) .checkbox-icon path,:host(.checkbox-indeterminate) .checkbox-icon path{stroke-dashoffset:0;-webkit-transition:stroke-dashoffset 90ms linear 90ms;transition:stroke-dashoffset 90ms linear 90ms}:host(.legacy-checkbox.checkbox-disabled),:host(.checkbox-disabled) .label-text-wrapper{opacity:0.38}:host(.checkbox-disabled) .native-wrapper{opacity:0.63}:host(.in-item.legacy-checkbox){margin-left:0;margin-right:0;margin-top:18px;margin-bottom:18px;display:block;position:static}:host(.in-item.legacy-checkbox[slot=start]){-webkit-margin-start:4px;margin-inline-start:4px;-webkit-margin-end:36px;margin-inline-end:36px;margin-top:18px;margin-bottom:18px}\";\nconst IonCheckboxMdStyle0 = checkboxMdCss;\n\nconst Checkbox = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.ionChange = createEvent(this, \"ionChange\", 7);\n        this.ionFocus = createEvent(this, \"ionFocus\", 7);\n        this.ionBlur = createEvent(this, \"ionBlur\", 7);\n        this.ionStyle = createEvent(this, \"ionStyle\", 7);\n        this.inputId = `ion-cb-${checkboxIds++}`;\n        this.inheritedAttributes = {};\n        // TODO(FW-3100): remove this\n        // This flag ensures we log the deprecation warning at most once.\n        this.hasLoggedDeprecationWarning = false;\n        /**\n         * Sets the checked property and emits\n         * the ionChange event. Use this to update the\n         * checked state in response to user-generated\n         * actions such as a click.\n         */\n        this.setChecked = (state) => {\n            const isChecked = (this.checked = state);\n            this.ionChange.emit({\n                checked: isChecked,\n                value: this.value,\n            });\n        };\n        this.toggleChecked = (ev) => {\n            ev.preventDefault();\n            this.setFocus();\n            this.setChecked(!this.checked);\n            this.indeterminate = false;\n        };\n        this.onFocus = () => {\n            this.ionFocus.emit();\n        };\n        this.onBlur = () => {\n            this.ionBlur.emit();\n        };\n        this.onClick = (ev) => {\n            if (this.disabled) {\n                return;\n            }\n            this.toggleChecked(ev);\n        };\n        this.color = undefined;\n        this.name = this.inputId;\n        this.checked = false;\n        this.indeterminate = false;\n        this.disabled = false;\n        this.value = 'on';\n        this.labelPlacement = 'start';\n        this.justify = 'space-between';\n        this.alignment = 'center';\n        this.legacy = undefined;\n    }\n    connectedCallback() {\n        this.legacyFormController = createLegacyFormController(this.el); // TODO(FW-3100): remove this\n    }\n    componentWillLoad() {\n        this.emitStyle();\n        // TODO(FW-3100): remove check\n        if (!this.legacyFormController.hasLegacyControl()) {\n            this.inheritedAttributes = Object.assign({}, inheritAriaAttributes(this.el));\n        }\n    }\n    styleChanged() {\n        this.emitStyle();\n    }\n    emitStyle() {\n        const style = {\n            'interactive-disabled': this.disabled,\n            // TODO(FW-3100): remove this\n            legacy: !!this.legacy,\n        };\n        // TODO(FW-3100): remove this\n        if (this.legacyFormController.hasLegacyControl()) {\n            style['checkbox-checked'] = this.checked;\n        }\n        this.ionStyle.emit(style);\n    }\n    setFocus() {\n        if (this.focusEl) {\n            this.focusEl.focus();\n        }\n    }\n    // TODO(FW-3100): run contents of renderCheckbox directly instead\n    render() {\n        const { legacyFormController } = this;\n        return legacyFormController.hasLegacyControl() ? this.renderLegacyCheckbox() : this.renderCheckbox();\n    }\n    renderCheckbox() {\n        const { color, checked, disabled, el, getSVGPath, indeterminate, inheritedAttributes, inputId, justify, labelPlacement, name, value, alignment, } = this;\n        const mode = getIonMode(this);\n        const path = getSVGPath(mode, indeterminate);\n        renderHiddenInput(true, el, name, checked ? value : '', disabled);\n        return (h(Host, { \"aria-checked\": indeterminate ? 'mixed' : `${checked}`, class: createColorClasses(color, {\n                [mode]: true,\n                'in-item': hostContext('ion-item', el),\n                'checkbox-checked': checked,\n                'checkbox-disabled': disabled,\n                'checkbox-indeterminate': indeterminate,\n                interactive: true,\n                [`checkbox-justify-${justify}`]: true,\n                [`checkbox-alignment-${alignment}`]: true,\n                [`checkbox-label-placement-${labelPlacement}`]: true,\n            }), onClick: this.onClick }, h(\"label\", { class: \"checkbox-wrapper\" }, h(\"input\", Object.assign({ type: \"checkbox\", checked: checked ? true : undefined, disabled: disabled, id: inputId, onChange: this.toggleChecked, onFocus: () => this.onFocus(), onBlur: () => this.onBlur(), ref: (focusEl) => (this.focusEl = focusEl) }, inheritedAttributes)), h(\"div\", { class: {\n                'label-text-wrapper': true,\n                'label-text-wrapper-hidden': el.textContent === '',\n            }, part: \"label\" }, h(\"slot\", null)), h(\"div\", { class: \"native-wrapper\" }, h(\"svg\", { class: \"checkbox-icon\", viewBox: \"0 0 24 24\", part: \"container\" }, path)))));\n    }\n    // TODO(FW-3100): remove this\n    renderLegacyCheckbox() {\n        if (!this.hasLoggedDeprecationWarning) {\n            printIonWarning(`ion-checkbox now requires providing a label with either the default slot or the \"aria-label\" attribute. To migrate, remove any usage of \"ion-label\" and pass the label text to either the component or the \"aria-label\" attribute.\n\nExample: <ion-checkbox>Label</ion-checkbox>\nExample with aria-label: <ion-checkbox aria-label=\"Label\"></ion-checkbox>\n\nDevelopers can use the \"legacy\" property to continue using the legacy form markup. This property will be removed in an upcoming major release of Ionic where this form control will use the modern form markup.`, this.el);\n            if (this.legacy) {\n                printIonWarning(`ion-checkbox is being used with the \"legacy\" property enabled which will forcibly enable the legacy form markup. This property will be removed in an upcoming major release of Ionic where this form control will use the modern form markup.\nDevelopers can dismiss this warning by removing their usage of the \"legacy\" property and using the new checkbox syntax.`, this.el);\n            }\n            this.hasLoggedDeprecationWarning = true;\n        }\n        const { color, checked, disabled, el, getSVGPath, indeterminate, inputId, name, value } = this;\n        const mode = getIonMode(this);\n        const { label, labelId, labelText } = getAriaLabel(el, inputId);\n        const path = getSVGPath(mode, indeterminate);\n        renderHiddenInput(true, el, name, checked ? value : '', disabled);\n        return (h(Host, { \"aria-labelledby\": label ? labelId : null, \"aria-checked\": `${checked}`, \"aria-hidden\": disabled ? 'true' : null, role: \"checkbox\", class: createColorClasses(color, {\n                [mode]: true,\n                'in-item': hostContext('ion-item', el),\n                'checkbox-checked': checked,\n                'checkbox-disabled': disabled,\n                'checkbox-indeterminate': indeterminate,\n                'legacy-checkbox': true,\n                interactive: true,\n            }), onClick: this.onClick }, h(\"svg\", { class: \"checkbox-icon\", viewBox: \"0 0 24 24\", part: \"container\" }, path), h(\"label\", { htmlFor: inputId }, labelText), h(\"input\", { type: \"checkbox\", \"aria-checked\": `${checked}`, disabled: disabled, id: inputId, onChange: this.toggleChecked, onFocus: () => this.onFocus(), onBlur: () => this.onBlur(), ref: (focusEl) => (this.focusEl = focusEl) })));\n    }\n    getSVGPath(mode, indeterminate) {\n        let path = indeterminate ? (h(\"path\", { d: \"M6 12L18 12\", part: \"mark\" })) : (h(\"path\", { d: \"M5.9,12.5l3.8,3.8l8.8-8.8\", part: \"mark\" }));\n        if (mode === 'md') {\n            path = indeterminate ? (h(\"path\", { d: \"M2 12H22\", part: \"mark\" })) : (h(\"path\", { d: \"M1.73,12.91 8.1,19.28 22.79,4.59\", part: \"mark\" }));\n        }\n        return path;\n    }\n    get el() { return getElement(this); }\n    static get watchers() { return {\n        \"checked\": [\"styleChanged\"],\n        \"disabled\": [\"styleChanged\"]\n    }; }\n};\nlet checkboxIds = 0;\nCheckbox.style = {\n    ios: IonCheckboxIosStyle0,\n    md: IonCheckboxMdStyle0\n};\n\nexport { Checkbox as ion_checkbox };\n"], "names": ["h", "findItemLabel", "createLegacyFormController", "el", "controlEl", "legacyControl", "hasLegacyControl", "undefined", "hasLabelProp", "label", "hasLabelSlot", "hasAriaLabelAttribute", "hasAttribute", "shadowRoot", "legacyItemLabel", "legacy", "NAMED_LABEL_SLOT_COMPONENTS", "includes", "tagName", "querySelector", "UNNAMED_LABEL_SLOT_COMPONENTS", "textContent", "c", "r", "registerInstance", "d", "createEvent", "H", "Host", "f", "getElement", "i", "inheritAriaAttributes", "renderHiddenInput", "e", "getAriaLabel", "p", "printIonWarning", "createColorClasses", "hostContext", "b", "getIonMode", "checkboxIosCss", "IonCheckboxIosStyle0", "checkboxMdCss", "IonCheckboxMdStyle0", "Checkbox", "constructor", "hostRef", "ionChange", "ionFocus", "ionBlur", "ionStyle", "inputId", "checkboxIds", "inheritedAttributes", "hasLoggedDeprecationWarning", "setChecked", "state", "isChecked", "checked", "emit", "value", "toggleChecked", "ev", "preventDefault", "setFocus", "indeterminate", "onFocus", "onBlur", "onClick", "disabled", "color", "name", "labelPlacement", "justify", "alignment", "connectedCallback", "legacyFormController", "componentWillLoad", "emitStyle", "Object", "assign", "styleChanged", "style", "focusEl", "focus", "render", "renderLegacyCheckbox", "renderCheckbox", "getSV<PERSON>ath", "mode", "path", "class", "interactive", "type", "id", "onChange", "ref", "part", "viewBox", "labelId", "labelText", "role", "htmlFor", "watchers", "ios", "md", "ion_checkbox"], "sourceRoot": "webpack:///", "x_google_ignoreList": [0, 1]}