import { Injectable } from '@angular/core';
import { initializeApp, FirebaseApp } from 'firebase/app';

// Import types from auth module for compatibility
// TODO: Migrate to use AuthService from modules/auth/services/auth.service
import {
  getAuth,
  Auth,
  signInWithEmailAndPassword,
  createUserWithEmailAndPassword,
  signOut,
  sendPasswordResetEmail,
  User,
  onAuthStateChanged
} from 'firebase/auth';
import {
  getFirestore,
  Firestore,
  doc,
  setDoc,
  getDoc,
  collection,
  addDoc,
  query,
  where,
  orderBy,
  getDocs,
  updateDoc,
  deleteDoc
} from 'firebase/firestore';
import {
  getStorage,
  ref,
  uploadBytes,
  uploadBytesResumable,
  getDownloadURL,
  deleteObject
} from 'firebase/storage';
import {
  getFunctions,
  httpsCallable,
  Functions
} from 'firebase/functions';
import { environment } from '../../environments/environment';
import { BehaviorSubject, Observable } from 'rxjs';

export interface LawyerProfile {
  uid: string;
  email: string;
  name: string;
  rollNumber: string;
  barId: string;
  phone?: string;
  avatar?: string;
  role: 'lawyer';
  createdAt: Date;
  updatedAt: Date;
}



export interface ClientProfile {
  uid: string;
  email: string;
  name: string;
  phone?: string;
  avatar?: string;
  role: 'client';
  createdAt: Date;
  updatedAt: Date;
}

export interface AuditLog {
  id?: string;
  userId: string;
  userRole: 'lawyer' | 'secretary' | 'client';
  userName: string;
  action: string;
  entityType: string;
  entityId: string;
  changes?: any;
  metadata?: any;
  timestamp: Date;
}

export interface Case {
  id?: string;
  lawyerId: string;
  clientName: string;
  title: string;
  status: 'ongoing' | 'closed' | 'cancelled';
  description?: string;
  fileCount: number;
  createdAt: Date;
  updatedAt: Date;
}

export interface Appointment {
  id?: string;
  lawyerId: string;
  clientName: string;
  type: string;
  status: 'new' | 'confirmed' | 'completed' | 'cancelled';
  date: string;
  time: string;
  notes?: string;
  createdBy?: string; // Secretary UID if created by secretary
  createdByRole?: 'lawyer' | 'secretary';
  createdAt: Date;
  updatedAt: Date;
}

@Injectable({
  providedIn: 'root'
})
export class FirebaseService {
  private app: FirebaseApp;
  private auth: Auth;
  private firestore: Firestore;
  private storage: any; // Firebase Storage instance
  private functions: Functions;
  private currentUserSubject = new BehaviorSubject<User | null>(null);
  public currentUser$ = this.currentUserSubject.asObservable();

  constructor() {
    // Initialize Firebase
    this.app = initializeApp(environment.firebase);
    this.auth = getAuth(this.app);
    this.firestore = getFirestore(this.app);
    this.storage = getStorage(this.app);
    this.functions = getFunctions(this.app);

    // Listen to auth state changes
    onAuthStateChanged(this.auth, (user) => {
      this.currentUserSubject.next(user);
    });
  }

  // Authentication Methods
  async signIn(email: string, password: string): Promise<User> {
    const userCredential = await signInWithEmailAndPassword(this.auth, email, password);
    return userCredential.user;
  }

  async signUp(email: string, password: string, userData: any): Promise<User> {
    const userCredential = await createUserWithEmailAndPassword(this.auth, email, password);
    const user = userCredential.user;

    // Create profile based on role
    if (userData.role === 'lawyer') {
      const lawyerProfile: LawyerProfile = {
        uid: user.uid,
        email: user.email!,
        name: userData.name || email.split('@')[0], // Use the provided name
        rollNumber: userData.rollNumber || '',
        barId: userData.barId || '',
        phone: userData.phone || '',
        role: 'lawyer',
        secretaryCode: this.generateSecretaryCode(),
        createdAt: new Date(),
        updatedAt: new Date()
      };
      await this.createLawyerProfile(lawyerProfile);
    } else if (userData.role === 'secretary') {
      const secretaryProfile: SecretaryProfile = {
        uid: user.uid,
        email: user.email!,
        name: userData.name || email.split('@')[0],
        phone: userData.phone || '',
        role: 'secretary',
        linkedLawyers: [],
        permissions: {
          canManageCalendar: true,
          canManageFiles: true,
          canManageCases: true,
          canManageRetainers: true,
          canViewFinances: false,
          canManageFinances: false
        },
        createdAt: new Date(),
        updatedAt: new Date()
      };
      await this.createSecretaryProfile(secretaryProfile);

      // If lawyer code provided, create link request
      if (userData.lawyerCode) {
        await this.requestLawyerLink(user.uid, userData.lawyerCode);
      }
    } else if (userData.role === 'client') {
      const clientProfile: ClientProfile = {
        uid: user.uid,
        email: user.email!,
        name: userData.name || email.split('@')[0],
        phone: userData.phone || '',
        role: 'client',
        createdAt: new Date(),
        updatedAt: new Date()
      };
      await this.createClientProfile(clientProfile);
    }

    return user;
  }

  async signOut(): Promise<void> {
    await signOut(this.auth);
  }

  async resetPassword(email: string): Promise<void> {
    await sendPasswordResetEmail(this.auth, email);
  }

  getCurrentUser(): User | null {
    return this.auth.currentUser;
  }

  // Lawyer Profile Methods
  async createLawyerProfile(profile: LawyerProfile): Promise<void> {
    const docRef = doc(this.firestore, 'lawyers', profile.uid);
    await setDoc(docRef, profile);
  }

  async getLawyerProfile(uid: string): Promise<LawyerProfile | null> {
    const docRef = doc(this.firestore, 'lawyers', uid);
    const docSnap = await getDoc(docRef);
    
    if (docSnap.exists()) {
      return docSnap.data() as LawyerProfile;
    }
    return null;
  }

  async updateLawyerProfile(uid: string, updates: Partial<LawyerProfile>): Promise<void> {
    const docRef = doc(this.firestore, 'lawyers', uid);
    await updateDoc(docRef, { ...updates, updatedAt: new Date() });
  }

  // Case Management Methods
  async createCase(caseData: Omit<Case, 'id'>): Promise<string> {
    const docRef = await addDoc(collection(this.firestore, 'cases'), caseData);
    return docRef.id;
  }

  async getCases(lawyerId: string, status?: string): Promise<Case[]> {
    let q = query(collection(this.firestore, 'cases'), where('lawyerId', '==', lawyerId));

    if (status) {
      q = query(q, where('status', '==', status));
    }

    const querySnapshot = await getDocs(q);
    return querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    } as Case));
  }



  async updateCase(
    caseId: string,
    updates: Partial<Case>,
    updatedBy?: string,
    updatedByRole?: 'lawyer' | 'secretary'
  ): Promise<void> {
    const docRef = doc(this.firestore, 'cases', caseId);
    await updateDoc(docRef, { ...updates, updatedAt: new Date() });

    if (updatedBy && updatedByRole) {
      await this.logActivity(updatedBy, updatedByRole, 'User', 'UPDATE', 'case', caseId, updates);
    }
  }

  async deleteCase(caseId: string): Promise<void> {
    const docRef = doc(this.firestore, 'cases', caseId);
    await deleteDoc(docRef);
  }

  // Appointment Methods
  async createAppointment(appointmentData: Omit<Appointment, 'id'>): Promise<string> {
    const docRef = await addDoc(collection(this.firestore, 'appointments'), appointmentData);
    return docRef.id;
  }

  async getAppointments(lawyerId: string, status?: string): Promise<Appointment[]> {
    let q = query(collection(this.firestore, 'appointments'), where('lawyerId', '==', lawyerId));
    
    if (status) {
      q = query(q, where('status', '==', status));
    }

    const querySnapshot = await getDocs(q);
    return querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    } as Appointment));
  }

  async updateAppointment(
    appointmentId: string,
    updates: Partial<Appointment>,
    updatedBy?: string,
    updatedByRole?: 'lawyer' | 'secretary'
  ): Promise<void> {
    const docRef = doc(this.firestore, 'appointments', appointmentId);
    await updateDoc(docRef, { ...updates, updatedAt: new Date() });

    if (updatedBy && updatedByRole) {
      await this.logActivity(updatedBy, updatedByRole, 'User', 'UPDATE', 'appointment', appointmentId, updates);
    }
  }

  async deleteAppointment(appointmentId: string): Promise<void> {
    const docRef = doc(this.firestore, 'appointments', appointmentId);
    await deleteDoc(docRef);
  }

  // Statistics Methods
  async getDashboardStats(lawyerId: string): Promise<{ ongoingCases: number; completedCases: number }> {
    const ongoingCases = await this.getCases(lawyerId, 'ongoing');
    const completedCases = await this.getCases(lawyerId, 'closed');

    return {
      ongoingCases: ongoingCases.length,
      completedCases: completedCases.length
    };
  }

  // Secretary Dashboard Statistics
  async getSecretaryDashboardStats(secretaryId: string): Promise<any> {
    const secretary = await this.getSecretaryProfile(secretaryId);
    if (!secretary) return { linkedLawyers: 0, totalCases: 0, totalAppointments: 0 };

    let totalCases = 0;
    let totalAppointments = 0;

    for (const lawyerId of secretary.linkedLawyers) {
      const cases = await this.getCases(lawyerId);
      const appointments = await this.getAppointments(lawyerId);
      totalCases += cases.length;
      totalAppointments += appointments.length;
    }

    return {
      linkedLawyers: secretary.linkedLawyers.length,
      totalCases,
      totalAppointments
    };
  }

  // Enhanced Methods with Secretary Support
  async createCaseWithAudit(
    caseData: Omit<Case, 'id'>,
    createdBy: string,
    createdByRole: 'lawyer' | 'secretary'
  ): Promise<string> {
    const docRef = await addDoc(collection(this.firestore, 'cases'), caseData);
    await this.logActivity(
      createdBy,
      createdByRole,
      'User',
      'CREATE',
      'case',
      docRef.id,
      { title: caseData.title, clientName: caseData.clientName }
    );
    return docRef.id;
  }

  async createAppointmentWithAudit(
    appointmentData: Omit<Appointment, 'id'>,
    createdBy: string,
    createdByRole: 'lawyer' | 'secretary'
  ): Promise<string> {
    const enhancedData = {
      ...appointmentData,
      createdBy,
      createdByRole
    };

    const docRef = await addDoc(collection(this.firestore, 'appointments'), enhancedData);
    await this.logActivity(
      createdBy,
      createdByRole,
      'User',
      'CREATE',
      'appointment',
      docRef.id,
      { clientName: appointmentData.clientName, date: appointmentData.date, time: appointmentData.time }
    );
    return docRef.id;
  }

  // Secretary Profile Methods
  async createSecretaryProfile(profile: SecretaryProfile): Promise<void> {
    const docRef = doc(this.firestore, 'secretaries', profile.uid);
    await setDoc(docRef, profile);
    await this.logActivity(profile.uid, 'secretary', profile.name, 'CREATE', 'secretary_profile', profile.uid);
  }

  async getSecretaryProfile(uid: string): Promise<SecretaryProfile | null> {
    const docRef = doc(this.firestore, 'secretaries', uid);
    const docSnap = await getDoc(docRef);

    if (docSnap.exists()) {
      return docSnap.data() as SecretaryProfile;
    }
    return null;
  }

  async updateSecretaryProfile(uid: string, updates: Partial<SecretaryProfile>): Promise<void> {
    const docRef = doc(this.firestore, 'secretaries', uid);
    await updateDoc(docRef, { ...updates, updatedAt: new Date() });

    const secretary = await this.getSecretaryProfile(uid);
    if (secretary) {
      await this.logActivity(uid, 'secretary', secretary.name, 'UPDATE', 'secretary_profile', uid, updates);
    }
  }

  // Client Profile Methods
  async createClientProfile(profile: ClientProfile): Promise<void> {
    const docRef = doc(this.firestore, 'clients', profile.uid);
    await setDoc(docRef, profile);
  }

  async getClientProfile(uid: string): Promise<ClientProfile | null> {
    const docRef = doc(this.firestore, 'clients', uid);
    const docSnap = await getDoc(docRef);

    if (docSnap.exists()) {
      return docSnap.data() as ClientProfile;
    }
    return null;
  }

  // Lawyer-Secretary Linking Methods
  async requestLawyerLink(secretaryId: string, lawyerCode: string): Promise<void> {
    // Find lawyer by code
    const lawyersQuery = query(
      collection(this.firestore, 'lawyers'),
      where('secretaryCode', '==', lawyerCode)
    );
    const lawyersSnapshot = await getDocs(lawyersQuery);

    if (lawyersSnapshot.empty) {
      throw new Error('Invalid lawyer code');
    }

    const lawyerDoc = lawyersSnapshot.docs[0];
    const lawyer = lawyerDoc.data() as LawyerProfile;
    const secretary = await this.getSecretaryProfile(secretaryId);

    if (!secretary) {
      throw new Error('Secretary profile not found');
    }

    // Create link request
    const linkRequest: LawyerSecretaryLink = {
      lawyerId: lawyer.uid,
      secretaryId: secretaryId,
      lawyerName: lawyer.name,
      secretaryName: secretary.name,
      status: 'pending',
      permissions: secretary.permissions,
      requestedAt: new Date(),
      createdAt: new Date()
    };

    await addDoc(collection(this.firestore, 'lawyer_secretary_links'), linkRequest);
    await this.logActivity(secretaryId, 'secretary', secretary.name, 'REQUEST', 'lawyer_link', lawyer.uid);
  }

  async getSecretaryLinkedLawyers(secretaryId: string): Promise<LawyerProfile[]> {
    const secretary = await this.getSecretaryProfile(secretaryId);
    if (!secretary || secretary.linkedLawyers.length === 0) {
      return [];
    }

    const lawyers: LawyerProfile[] = [];
    for (const lawyerId of secretary.linkedLawyers) {
      const lawyer = await this.getLawyerProfile(lawyerId);
      if (lawyer) {
        lawyers.push(lawyer);
      }
    }
    return lawyers;
  }

  // Assistant Code Management Methods
  async generateAssistantCode(
    lawyerId: string,
    permissions: SecretaryPermissions
  ): Promise<AssistantCode> {
    try {
      const generateCode = httpsCallable(this.functions, 'generateAssistantCode');
      const result = await generateCode({ permissions });

      if (result.data && (result.data as any).success) {
        return (result.data as any).code;
      } else {
        throw new Error('Failed to generate assistant code');
      }
    } catch (error) {
      console.error('Error calling generateAssistantCode function:', error);
      throw error;
    }
  }

  async validateAssistantCode(code: string): Promise<AssistantCode | null> {
    const q = query(
      collection(this.firestore, 'assistant_codes'),
      where('code', '==', code),
      where('isUsed', '==', false)
    );

    const querySnapshot = await getDocs(q);

    if (querySnapshot.empty) {
      return null;
    }

    const doc = querySnapshot.docs[0];
    const assistantCode = { id: doc.id, ...doc.data() } as AssistantCode;

    // Check if code has expired
    const expiryDate = assistantCode.expiresAt instanceof Date
      ? assistantCode.expiresAt
      : (assistantCode.expiresAt as any).toDate();

    if (new Date() > expiryDate) {
      return null;
    }

    return assistantCode;
  }

  async useAssistantCode(
    code: string,
    secretaryId: string
  ): Promise<LawyerSecretaryLink> {
    try {
      const linkSecretary = httpsCallable(this.functions, 'linkSecretary');
      const result = await linkSecretary({ code });

      if (result.data && (result.data as any).success) {
        return (result.data as any).link;
      } else {
        throw new Error('Failed to link secretary');
      }
    } catch (error) {
      console.error('Error calling linkSecretary function:', error);
      throw error;
    }
  }

  async getAssistantCodes(lawyerId: string): Promise<AssistantCode[]> {
    const q = query(
      collection(this.firestore, 'assistant_codes'),
      where('lawyerId', '==', lawyerId),
      orderBy('createdAt', 'desc')
    );

    const querySnapshot = await getDocs(q);
    return querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    } as AssistantCode));
  }

  async revokeAssistantCode(codeId: string, lawyerId: string): Promise<void> {
    try {
      const revokeCode = httpsCallable(this.functions, 'revokeAssistantCode');
      const result = await revokeCode({ codeId });

      if (!(result.data as any).success) {
        throw new Error('Failed to revoke assistant code');
      }
    } catch (error) {
      console.error('Error calling revokeAssistantCode function:', error);
      throw error;
    }
  }

  async getLinkedSecretaries(lawyerId: string): Promise<LawyerSecretaryLink[]> {
    const q = query(
      collection(this.firestore, 'lawyer_secretary_links'),
      where('lawyerId', '==', lawyerId),
      where('status', '==', 'approved')
    );

    const querySnapshot = await getDocs(q);
    return querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    } as LawyerSecretaryLink));
  }

  async getLinkedLawyers(secretaryId: string): Promise<LawyerSecretaryLink[]> {
    const q = query(
      collection(this.firestore, 'lawyer_secretary_links'),
      where('secretaryId', '==', secretaryId),
      where('status', '==', 'approved')
    );

    const querySnapshot = await getDocs(q);
    return querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    } as LawyerSecretaryLink));
  }

  async updateSecretaryPermissions(
    linkId: string,
    permissions: SecretaryPermissions,
    lawyerId: string
  ): Promise<void> {
    const lawyer = await this.getLawyerProfile(lawyerId);
    if (!lawyer) {
      throw new Error('Lawyer profile not found');
    }

    await updateDoc(doc(this.firestore, 'lawyer_secretary_links', linkId), {
      permissions,
      updatedAt: new Date()
    });

    await this.logActivity(
      lawyerId,
      'lawyer',
      lawyer.name,
      'UPDATE_PERMISSIONS',
      'secretary_link',
      linkId,
      { permissions }
    );
  }

  async revokeSecretaryAccess(
    linkId: string,
    lawyerId: string,
    secretaryId: string
  ): Promise<void> {
    const lawyer = await this.getLawyerProfile(lawyerId);
    const secretary = await this.getSecretaryProfile(secretaryId);

    if (!lawyer || !secretary) {
      throw new Error('Profile not found');
    }

    // Remove the link
    await deleteDoc(doc(this.firestore, 'lawyer_secretary_links', linkId));

    // Update secretary's linked lawyers list
    const updatedLinkedLawyers = secretary.linkedLawyers.filter(id => id !== lawyerId);
    await updateDoc(doc(this.firestore, 'secretaries', secretaryId), {
      linkedLawyers: updatedLinkedLawyers,
      updatedAt: new Date()
    });

    await this.logActivity(
      lawyerId,
      'lawyer',
      lawyer.name,
      'REVOKE_ACCESS',
      'secretary_link',
      linkId,
      { secretaryId, secretaryName: secretary.name }
    );
  }

  async checkSecretaryPermission(
    secretaryId: string,
    lawyerId: string,
    permission: keyof SecretaryPermissions
  ): Promise<boolean> {
    const q = query(
      collection(this.firestore, 'lawyer_secretary_links'),
      where('secretaryId', '==', secretaryId),
      where('lawyerId', '==', lawyerId),
      where('status', '==', 'approved')
    );

    const querySnapshot = await getDocs(q);

    if (querySnapshot.empty) {
      return false;
    }

    const link = querySnapshot.docs[0].data() as LawyerSecretaryLink;
    return link.permissions[permission] || false;
  }

  async getAuditLogs(
    entityType?: string,
    entityId?: string,
    userId?: string,
    limit: number = 50
  ): Promise<AuditLog[]> {
    let q = query(
      collection(this.firestore, 'audit_logs'),
      orderBy('timestamp', 'desc')
    );

    if (entityType) {
      q = query(q, where('entityType', '==', entityType));
    }

    if (entityId) {
      q = query(q, where('entityId', '==', entityId));
    }

    if (userId) {
      q = query(q, where('userId', '==', userId));
    }

    const querySnapshot = await getDocs(q);
    return querySnapshot.docs.slice(0, limit).map(doc => ({
      id: doc.id,
      ...doc.data()
    } as AuditLog));
  }

  private generateUniqueCode(): string {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    let result = '';
    for (let i = 0; i < 8; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  }

  // Audit Logging Methods
  async logActivity(
    userId: string,
    userRole: 'lawyer' | 'secretary' | 'client',
    userName: string,
    action: string,
    entityType: string,
    entityId: string,
    changes?: any,
    metadata?: any
  ): Promise<void> {
    const auditLog: AuditLog = {
      userId,
      userRole,
      userName,
      action,
      entityType,
      entityId,
      changes,
      metadata,
      timestamp: new Date()
    };

    await addDoc(collection(this.firestore, 'audit_logs'), auditLog);
  }

  // Profile Image Methods
  async uploadProfileImage(file: File, userId: string): Promise<string> {
    try {
      // Create a unique filename with timestamp
      const timestamp = Date.now();
      const fileExtension = file.name.split('.').pop();
      const fileName = `profile_${userId}_${timestamp}.${fileExtension}`;

      // Create storage reference
      const storageRef = ref(this.storage, `profile-images/${fileName}`);

      // Upload file
      const snapshot = await uploadBytes(storageRef, file);

      // Get download URL
      const downloadURL = await getDownloadURL(snapshot.ref);

      // Update user profile with image URL
      await this.updateUserProfileImage(userId, downloadURL);

      return downloadURL;
    } catch (error) {
      console.error('Error uploading profile image:', error);
      throw error;
    }
  }

  async uploadProfileImageWithProgress(
    file: File,
    userId: string,
    progressCallback?: (progress: number) => void
  ): Promise<string> {
    return new Promise((resolve, reject) => {
      try {
        // Create a unique filename with timestamp
        const timestamp = Date.now();
        const fileExtension = file.name.split('.').pop();
        const fileName = `profile_${userId}_${timestamp}.${fileExtension}`;

        // Create storage reference
        const storageRef = ref(this.storage, `profile-images/${fileName}`);

        // Create upload task with progress tracking
        const uploadTask = uploadBytesResumable(storageRef, file);

        // Set timeout for upload (60 seconds for better reliability)
        const timeout = setTimeout(() => {
          uploadTask.cancel();
          reject(new Error('Upload timeout - please try again with a smaller image'));
        }, 60000);

        uploadTask.on('state_changed',
          (snapshot) => {
            // Progress tracking
            const progress = (snapshot.bytesTransferred / snapshot.totalBytes) * 100;
            if (progressCallback) {
              progressCallback(progress);
            }
            console.log(`Upload is ${progress.toFixed(1)}% done`);
          },
          (error) => {
            // Error handling
            clearTimeout(timeout);
            console.error('Upload error:', error);
            reject(error);
          },
          async () => {
            // Upload completed successfully
            clearTimeout(timeout);
            try {
              const downloadURL = await getDownloadURL(uploadTask.snapshot.ref);
              await this.updateUserProfileImage(userId, downloadURL);
              resolve(downloadURL);
            } catch (error) {
              reject(error);
            }
          }
        );
      } catch (error) {
        reject(error);
      }
    });
  }

  async updateUserProfileImage(userId: string, imageUrl: string): Promise<void> {
    try {
      // Update lawyer profile
      const lawyerRef = doc(this.firestore, 'lawyers', userId);
      const lawyerDoc = await getDoc(lawyerRef);

      if (lawyerDoc.exists()) {
        await updateDoc(lawyerRef, {
          profileImageUrl: imageUrl,
          updatedAt: new Date()
        });
        return;
      }

      // Update secretary profile
      const secretaryRef = doc(this.firestore, 'secretaries', userId);
      const secretaryDoc = await getDoc(secretaryRef);

      if (secretaryDoc.exists()) {
        await updateDoc(secretaryRef, {
          profileImageUrl: imageUrl,
          updatedAt: new Date()
        });
        return;
      }

      // Update client profile
      const clientRef = doc(this.firestore, 'clients', userId);
      const clientDoc = await getDoc(clientRef);

      if (clientDoc.exists()) {
        await updateDoc(clientRef, {
          profileImageUrl: imageUrl,
          updatedAt: new Date()
        });
        return;
      }

      throw new Error('User profile not found');
    } catch (error) {
      console.error('Error updating user profile image:', error);
      throw error;
    }
  }

  async getUserProfileImage(userId: string): Promise<string | null> {
    try {
      // Check lawyer profile
      const lawyerRef = doc(this.firestore, 'lawyers', userId);
      const lawyerDoc = await getDoc(lawyerRef);

      if (lawyerDoc.exists()) {
        const data = lawyerDoc.data();
        return data['profileImageUrl'] || null;
      }

      // Check secretary profile
      const secretaryRef = doc(this.firestore, 'secretaries', userId);
      const secretaryDoc = await getDoc(secretaryRef);

      if (secretaryDoc.exists()) {
        const data = secretaryDoc.data();
        return data['profileImageUrl'] || null;
      }

      // Check client profile
      const clientRef = doc(this.firestore, 'clients', userId);
      const clientDoc = await getDoc(clientRef);

      if (clientDoc.exists()) {
        const data = clientDoc.data();
        return data['profileImageUrl'] || null;
      }

      return null;
    } catch (error) {
      console.error('Error getting user profile image:', error);
      return null;
    }
  }

  async deleteProfileImage(userId: string, imageUrl: string): Promise<void> {
    try {
      // Delete from storage
      const imageRef = ref(this.storage, imageUrl);
      await deleteObject(imageRef);

      // Update user profile to remove image URL
      await this.updateUserProfileImage(userId, '');
    } catch (error) {
      console.error('Error deleting profile image:', error);
      throw error;
    }
  }

  // Case File Management Methods
  async uploadCaseFile(
    file: File,
    caseId: string,
    userId: string,
    progressCallback?: (progress: number) => void
  ): Promise<any> {
    return new Promise((resolve, reject) => {
      try {
        // Create unique filename
        const timestamp = Date.now();
        const fileExtension = file.name.split('.').pop();
        const fileName = `case_${caseId}_${timestamp}.${fileExtension}`;
        const storagePath = `case-files/${caseId}/${fileName}`;

        // Create storage reference
        const storageRef = ref(this.storage, storagePath);

        // Create upload task with progress tracking
        const uploadTask = uploadBytesResumable(storageRef, file);

        // Set timeout for upload (2 minutes for larger files)
        const timeout = setTimeout(() => {
          uploadTask.cancel();
          reject(new Error('Upload timeout - please try again'));
        }, 120000);

        uploadTask.on('state_changed',
          (snapshot) => {
            // Progress callback
            const progress = (snapshot.bytesTransferred / snapshot.totalBytes) * 100;
            if (progressCallback) {
              progressCallback(progress);
            }
          },
          (error) => {
            clearTimeout(timeout);
            console.error('Upload error:', error);
            reject(error);
          },
          async () => {
            clearTimeout(timeout);
            try {
              // Get download URL
              const downloadURL = await getDownloadURL(uploadTask.snapshot.ref);

              // Create file document in Firestore
              const fileDoc = {
                name: fileName,
                originalName: file.name,
                type: file.type,
                size: file.size,
                caseId: caseId,
                uploadedBy: userId,
                uploadedAt: new Date(),
                downloadUrl: downloadURL,
                storagePath: storagePath
              };

              const docRef = await addDoc(collection(this.firestore, 'case_files'), fileDoc);

              // Update case file count
              await this.updateCaseFileCount(caseId);

              resolve({
                id: docRef.id,
                ...fileDoc
              });
            } catch (error) {
              console.error('Error saving file metadata:', error);
              reject(error);
            }
          }
        );
      } catch (error) {
        console.error('Error starting upload:', error);
        reject(error);
      }
    });
  }

  async getCaseFiles(caseId: string): Promise<any[]> {
    try {
      const q = query(
        collection(this.firestore, 'case_files'),
        where('caseId', '==', caseId),
        orderBy('uploadedAt', 'desc')
      );

      const querySnapshot = await getDocs(q);
      return querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));
    } catch (error) {
      console.error('Error getting case files:', error);
      throw error;
    }
  }

  async deleteCaseFile(fileId: string, storagePath: string): Promise<void> {
    try {
      // Delete from storage
      const storageRef = ref(this.storage, storagePath);
      await deleteObject(storageRef);

      // Delete from Firestore
      await deleteDoc(doc(this.firestore, 'case_files', fileId));
    } catch (error) {
      console.error('Error deleting case file:', error);
      throw error;
    }
  }

  private async updateCaseFileCount(caseId: string): Promise<void> {
    try {
      const files = await this.getCaseFiles(caseId);
      const caseRef = doc(this.firestore, 'cases', caseId);
      await updateDoc(caseRef, { fileCount: files.length });
    } catch (error) {
      console.error('Error updating case file count:', error);
    }
  }

  // Utility Methods
  private generateSecretaryCode(): string {
    return Math.random().toString(36).substring(2, 8).toUpperCase();
  }
}
