import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ModalController, ActionSheetController, ToastController } from '@ionic/angular';

interface Client {
  id: string;
  name: string;
  email: string;
  phone: string;
  status: 'Active' | 'Review' | 'Pending' | 'Suspended' | 'Overdue';
  avatar: string;
  tab: 'active' | 'inactive' | 'overdue';
  retainerAmount: number;
  startDate: string;
  endDate: string;
  lastActivity: string;
  caseType: string;
  notes?: string;
}

interface ContractFile {
  id: string;
  name: string;
  type: string;
  icon: string;
  count: string;
  size?: string;
  uploadDate: string;
  clientId?: string;
  folderId?: string;
}

interface FileFolder {
  id: string;
  name: string;
  clientId: string;
  fileCount: number;
  createdDate: string;
}

interface Activity {
  id: string;
  title: string;
  description: string;
  date: string;
  time: string;
  type: 'contract' | 'payment' | 'meeting' | 'document' | 'communication';
  clientId: string;
  clientName: string;
  icon: string;
}

@Component({
  selector: 'app-retainer',
  templateUrl: './retainer.page.html',
  styleUrls: ['./retainer.page.scss'],
  standalone: false,
})
export class RetainerPage implements OnInit {
  selectedTab: 'active' | 'inactive' | 'overdue' = 'active';
  searchTerm: string = '';
  showSearchBar: boolean = false;

  clients: Client[] = [
    {
      id: '1',
      name: 'Kobe Bryant',
      email: '<EMAIL>',
      phone: '+****************',
      status: 'Review',
      avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',
      tab: 'active',
      retainerAmount: 15000,
      startDate: '2024-01-15',
      endDate: '2024-12-15',
      lastActivity: '2024-07-09',
      caseType: 'Corporate Law',
      notes: 'High-profile client, requires immediate attention'
    },
    {
      id: '2',
      name: 'Lebron James',
      email: '<EMAIL>',
      phone: '+****************',
      status: 'Active',
      avatar: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=150&h=150&fit=crop&crop=face',
      tab: 'active',
      retainerAmount: 25000,
      startDate: '2024-02-01',
      endDate: '2025-02-01',
      lastActivity: '2024-07-08',
      caseType: 'Contract Negotiation',
      notes: 'Long-term client, excellent payment history'
    },
    {
      id: '3',
      name: 'Jane Smith',
      email: '<EMAIL>',
      phone: '+****************',
      status: 'Pending',
      avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face',
      tab: 'inactive',
      retainerAmount: 8000,
      startDate: '2024-03-10',
      endDate: '2024-09-10',
      lastActivity: '2024-06-15',
      caseType: 'Family Law',
      notes: 'Awaiting document submission'
    },
    {
      id: '4',
      name: 'Michael Jordan',
      email: '<EMAIL>',
      phone: '+****************',
      status: 'Overdue',
      avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',
      tab: 'overdue',
      retainerAmount: 12000,
      startDate: '2023-12-01',
      endDate: '2024-06-01',
      lastActivity: '2024-05-20',
      caseType: 'Intellectual Property',
      notes: 'Payment overdue, follow up required'
    }
  ];

  contractFiles: ContractFile[] = [
    {
      id: '1',
      name: 'Agreement.pdf',
      type: 'Contracts',
      icon: 'document-text',
      count: '25',
      size: '2.4 MB',
      uploadDate: '2024-07-01',
      clientId: '1',
      folderId: 'folder1'
    },
    {
      id: '2',
      name: 'Receipts & Transaction',
      type: 'Legal Services',
      icon: 'receipt',
      count: 'P5,000',
      size: '1.2 MB',
      uploadDate: '2024-07-05',
      clientId: '2',
      folderId: 'folder2'
    },
    {
      id: '3',
      name: 'Court Filing.pdf',
      type: 'Legal Documents',
      icon: 'document-text',
      count: '8',
      size: '3.1 MB',
      uploadDate: '2024-07-08',
      clientId: '1',
      folderId: 'folder1'
    }
  ];

  fileFolders: FileFolder[] = [
    {
      id: 'folder1',
      name: 'Kobe Bryant - Corporate',
      clientId: '1',
      fileCount: 15,
      createdDate: '2024-01-15'
    },
    {
      id: 'folder2',
      name: 'Lebron James - Contracts',
      clientId: '2',
      fileCount: 8,
      createdDate: '2024-02-01'
    }
  ];

  recentActivities: Activity[] = [
    {
      id: '1',
      title: 'Contract Review Completed',
      description: 'Reviewed and approved corporate agreement',
      date: '2024-07-09',
      time: '14:30',
      type: 'contract',
      clientId: '1',
      clientName: 'Kobe Bryant',
      icon: 'document-text'
    },
    {
      id: '2',
      title: 'Payment Received',
      description: 'Retainer payment of $5,000 received',
      date: '2024-07-08',
      time: '10:15',
      type: 'payment',
      clientId: '2',
      clientName: 'Lebron James',
      icon: 'card'
    },
    {
      id: '3',
      title: 'Client Meeting',
      description: 'Initial consultation completed',
      date: '2024-07-07',
      time: '16:00',
      type: 'meeting',
      clientId: '3',
      clientName: 'Jane Smith',
      icon: 'people'
    }
  ];

  constructor(
    private router: Router,
    private alertController: AlertController,
    private modalController: ModalController,
    private actionSheetController: ActionSheetController,
    private toastController: ToastController
  ) { }

  ngOnInit() {
    this.loadRetainerData();
  }

  loadRetainerData() {
    // In a real app, this would load from a service
    console.log('Loading retainer data...');
  }

  selectTab(tab: 'active' | 'inactive' | 'overdue') {
    this.selectedTab = tab;
  }

  getFilteredClients(): Client[] {
    let filtered = this.clients.filter(client => client.tab === this.selectedTab);

    if (this.searchTerm) {
      filtered = filtered.filter(client =>
        client.name.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
        client.email.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
        client.caseType.toLowerCase().includes(this.searchTerm.toLowerCase())
      );
    }

    return filtered;
  }

  toggleSearch() {
    this.showSearchBar = !this.showSearchBar;
    if (!this.showSearchBar) {
      this.searchTerm = '';
    }
  }

  async onAddRetainer() {
    const alert = await this.alertController.create({
      header: 'Add New Client',
      message: 'Choose how to add a new client to your retainer list.',
      buttons: [
        {
          text: 'Manual Entry',
          handler: () => {
            this.openClientForm();
          }
        },
        {
          text: 'Import from Contacts',
          handler: () => {
            this.importFromContacts();
          }
        },
        {
          text: 'Cancel',
          role: 'cancel'
        }
      ]
    });

    await alert.present();
  }

  async openClientForm(client?: Client) {
    // In a real app, this would open a modal with a form
    const alert = await this.alertController.create({
      header: client ? 'Edit Client' : 'Add New Client',
      inputs: [
        {
          name: 'name',
          type: 'text',
          placeholder: 'Full Name',
          value: client?.name || ''
        },
        {
          name: 'email',
          type: 'email',
          placeholder: 'Email Address',
          value: client?.email || ''
        },
        {
          name: 'phone',
          type: 'tel',
          placeholder: 'Phone Number',
          value: client?.phone || ''
        },
        {
          name: 'caseType',
          type: 'text',
          placeholder: 'Case Type',
          value: client?.caseType || ''
        },
        {
          name: 'retainerAmount',
          type: 'number',
          placeholder: 'Retainer Amount',
          value: client?.retainerAmount?.toString() || ''
        }
      ],
      buttons: [
        {
          text: 'Cancel',
          role: 'cancel'
        },
        {
          text: client ? 'Update' : 'Add',
          handler: (data) => {
            if (client) {
              this.updateClient(client.id, data);
            } else {
              this.addNewClient(data);
            }
          }
        }
      ]
    });

    await alert.present();
  }

  addNewClient(data: any) {
    const newClient: Client = {
      id: Date.now().toString(),
      name: data.name,
      email: data.email,
      phone: data.phone,
      status: 'Pending',
      avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',
      tab: 'inactive',
      retainerAmount: parseFloat(data.retainerAmount) || 0,
      startDate: new Date().toISOString().split('T')[0],
      endDate: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      lastActivity: new Date().toISOString().split('T')[0],
      caseType: data.caseType,
      notes: 'New client - pending initial consultation'
    };

    this.clients.push(newClient);
    this.showToast('Client added successfully');
  }

  updateClient(clientId: string, data: any) {
    const clientIndex = this.clients.findIndex(c => c.id === clientId);
    if (clientIndex !== -1) {
      this.clients[clientIndex] = {
        ...this.clients[clientIndex],
        name: data.name,
        email: data.email,
        phone: data.phone,
        caseType: data.caseType,
        retainerAmount: parseFloat(data.retainerAmount) || this.clients[clientIndex].retainerAmount
      };
      this.showToast('Client updated successfully');
    }
  }

  importFromContacts() {
    // In a real app, this would open the contacts picker
    this.showToast('Contact import feature coming soon');
  }

  async onClientClick(client: Client) {
    const actionSheet = await this.actionSheetController.create({
      header: client.name,
      subHeader: `${client.caseType} - ${client.status}`,
      buttons: [
        {
          text: 'View Details',
          icon: 'eye',
          handler: () => {
            this.viewClientDetails(client);
          }
        },
        {
          text: 'Edit Client',
          icon: 'create',
          handler: () => {
            this.openClientForm(client);
          }
        },
        {
          text: 'View Files',
          icon: 'folder',
          handler: () => {
            this.viewClientFiles(client);
          }
        },
        {
          text: 'Activity History',
          icon: 'time',
          handler: () => {
            this.viewClientActivity(client);
          }
        },
        {
          text: 'Change Status',
          icon: 'swap-horizontal',
          handler: () => {
            this.changeClientStatus(client);
          }
        },
        {
          text: 'Cancel',
          icon: 'close',
          role: 'cancel'
        }
      ]
    });

    await actionSheet.present();
  }

  async viewClientDetails(client: Client) {
    const alert = await this.alertController.create({
      header: 'Client Details',
      subHeader: client.name,
      message: `
        <strong>Email:</strong> ${client.email}<br>
        <strong>Phone:</strong> ${client.phone}<br>
        <strong>Case Type:</strong> ${client.caseType}<br>
        <strong>Status:</strong> ${client.status}<br>
        <strong>Retainer:</strong> $${client.retainerAmount.toLocaleString()}<br>
        <strong>Start Date:</strong> ${client.startDate}<br>
        <strong>End Date:</strong> ${client.endDate}<br>
        <strong>Last Activity:</strong> ${client.lastActivity}<br>
        ${client.notes ? `<strong>Notes:</strong> ${client.notes}` : ''}
      `,
      buttons: ['Close']
    });

    await alert.present();
  }

  viewClientFiles(client: Client) {
    // Filter files for this client
    const clientFiles = this.contractFiles.filter(file => file.clientId === client.id);
    console.log('Client files:', clientFiles);
    this.showToast(`${clientFiles.length} files found for ${client.name}`);
  }

  viewClientActivity(client: Client) {
    // Filter activities for this client
    const clientActivities = this.recentActivities.filter(activity => activity.clientId === client.id);
    console.log('Client activities:', clientActivities);
    this.showToast(`${clientActivities.length} activities found for ${client.name}`);
  }

  async changeClientStatus(client: Client) {
    const alert = await this.alertController.create({
      header: 'Change Status',
      subHeader: client.name,
      inputs: [
        {
          name: 'status',
          type: 'radio',
          label: 'Active',
          value: 'Active',
          checked: client.status === 'Active'
        },
        {
          name: 'status',
          type: 'radio',
          label: 'Review',
          value: 'Review',
          checked: client.status === 'Review'
        },
        {
          name: 'status',
          type: 'radio',
          label: 'Pending',
          value: 'Pending',
          checked: client.status === 'Pending'
        },
        {
          name: 'status',
          type: 'radio',
          label: 'Suspended',
          value: 'Suspended',
          checked: client.status === 'Suspended'
        },
        {
          name: 'status',
          type: 'radio',
          label: 'Overdue',
          value: 'Overdue',
          checked: client.status === 'Overdue'
        }
      ],
      buttons: [
        {
          text: 'Cancel',
          role: 'cancel'
        },
        {
          text: 'Update',
          handler: (data) => {
            this.updateClientStatus(client, data);
          }
        }
      ]
    });

    await alert.present();
  }

  updateClientStatus(client: Client, newStatus: string) {
    const clientIndex = this.clients.findIndex(c => c.id === client.id);
    if (clientIndex !== -1) {
      this.clients[clientIndex].status = newStatus as any;

      // Update tab based on status
      if (newStatus === 'Active' || newStatus === 'Review') {
        this.clients[clientIndex].tab = 'active';
      } else if (newStatus === 'Overdue') {
        this.clients[clientIndex].tab = 'overdue';
      } else {
        this.clients[clientIndex].tab = 'inactive';
      }

      this.showToast(`Status updated to ${newStatus}`);
    }
  }

  async onUploadFiles() {
    const actionSheet = await this.actionSheetController.create({
      header: 'Upload Files',
      buttons: [
        {
          text: 'Create New Folder',
          icon: 'folder-open',
          handler: () => {
            this.createNewFolder();
          }
        },
        {
          text: 'Upload to Existing Folder',
          icon: 'cloud-upload',
          handler: () => {
            this.uploadToFolder();
          }
        },
        {
          text: 'Upload General Files',
          icon: 'document',
          handler: () => {
            this.uploadGeneralFiles();
          }
        },
        {
          text: 'Cancel',
          icon: 'close',
          role: 'cancel'
        }
      ]
    });

    await actionSheet.present();
  }

  async createNewFolder() {
    const alert = await this.alertController.create({
      header: 'Create New Folder',
      inputs: [
        {
          name: 'folderName',
          type: 'text',
          placeholder: 'Folder Name'
        },
        {
          name: 'clientId',
          type: 'text',
          placeholder: 'Client ID (optional)'
        }
      ],
      buttons: [
        {
          text: 'Cancel',
          role: 'cancel'
        },
        {
          text: 'Create',
          handler: (data) => {
            this.addNewFolder(data);
          }
        }
      ]
    });

    await alert.present();
  }

  addNewFolder(data: any) {
    const newFolder: FileFolder = {
      id: Date.now().toString(),
      name: data.folderName,
      clientId: data.clientId || '',
      fileCount: 0,
      createdDate: new Date().toISOString().split('T')[0]
    };

    this.fileFolders.push(newFolder);
    this.showToast('Folder created successfully');
  }

  uploadToFolder() {
    // In a real app, this would show folder selection and file picker
    this.showToast('File upload to folder feature coming soon');
  }

  uploadGeneralFiles() {
    // In a real app, this would open file picker
    this.showToast('General file upload feature coming soon');
  }

  onFileClick(file: ContractFile) {
    console.log('File clicked:', file);
    this.showToast(`Opening ${file.name}`);
  }

  onActivityClick(activity: Activity) {
    console.log('Activity clicked:', activity);
    this.showToast(`Activity: ${activity.title}`);
  }

  async showToast(message: string) {
    const toast = await this.toastController.create({
      message: message,
      duration: 2000,
      position: 'bottom'
    });
    toast.present();
  }

  getStatusColor(status: string): string {
    switch (status) {
      case 'Active': return 'success';
      case 'Review': return 'warning';
      case 'Pending': return 'medium';
      case 'Suspended': return 'danger';
      case 'Overdue': return 'danger';
      default: return 'medium';
    }
  }

  getActivityIcon(type: string): string {
    switch (type) {
      case 'contract': return 'document-text';
      case 'payment': return 'card';
      case 'meeting': return 'people';
      case 'document': return 'folder';
      case 'communication': return 'chatbubble';
      default: return 'checkmark-circle';
    }
  }
}
