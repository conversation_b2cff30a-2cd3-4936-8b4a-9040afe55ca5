{"version": 3, "file": "node_modules_ionic_core_dist_esm_ion-app_8_entry_js.js", "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AAC8J;AACnF;AAChB;AACgC;AAC0D;AACtG;AACiC;AAC+B;AAC7B;AACR;AACoB;AACpB;AACpB;AACzB;AACG;AACC;AAEjC,MAAM8C,MAAM,GAAG,uSAAuS;AACtT,MAAMC,YAAY,GAAGD,MAAM;AAE3B,MAAME,GAAG,GAAG,MAAM;EACdC,WAAWA,CAACC,OAAO,EAAE;IACjBjD,qDAAgB,CAAC,IAAI,EAAEiD,OAAO,CAAC;EACnC;EACAC,gBAAgBA,CAAA,EAAG;IAAA,IAAAC,KAAA;IACf;MACIC,GAAG,cAAAC,6KAAA,CAAC,aAAY;QACZ,MAAMC,QAAQ,GAAGhC,4DAAU,CAACiC,MAAM,EAAE,QAAQ,CAAC;QAC7C,IAAI,CAACnC,wDAAM,CAACoC,UAAU,CAAC,UAAU,CAAC,EAAE;UAChC,gLAA6B,CAACC,IAAI,CAAEC,MAAM,IAAKA,MAAM,CAACC,aAAa,CAACvC,wDAAM,CAAC,CAAC;QAChF;QACA,IAAIA,wDAAM,CAACoC,UAAU,CAAC,WAAW,EAAEF,QAAQ,CAAC,EAAE;UAC1C,0LAAkC,CAACG,IAAI,CAAEC,MAAM,IAAKA,MAAM,CAACE,cAAc,CAAC,CAAC,CAAC;QAChF;QACA,IAAIxC,wDAAM,CAACoC,UAAU,CAAC,YAAY,EAAEK,cAAc,CAAC,CAAC,CAAC,EAAE;UACnD;AACpB;AACA;AACA;UACoB,MAAMC,QAAQ,GAAGxC,4DAAU,CAACiC,MAAM,EAAE,KAAK,CAAC,GAAG,KAAK,GAAG,SAAS;UAC9D,4LAAmC,CAACE,IAAI,CAAEC,MAAM,IAAKA,MAAM,CAACK,eAAe,CAAC3C,wDAAM,EAAE0C,QAAQ,CAAC,CAAC;QAClG;QACA,MAAME,wBAAwB,SAAS,mIAA4C;QACnF,MAAMC,gCAAgC,GAAGX,QAAQ,IAAIxC,wFAAqB,CAAC,CAAC;QAC5E,IAAIM,wDAAM,CAACoC,UAAU,CAAC,oBAAoB,EAAES,gCAAgC,CAAC,EAAE;UAC3ED,wBAAwB,CAACE,uBAAuB,CAAC,CAAC;QACtD,CAAC,MACI;UACD;AACpB;AACA;AACA;UACoB,IAAIpD,wFAAqB,CAAC,CAAC,EAAE;YACzBE,qDAAe,CAAC,iKAAiK,CAAC;UACtL;UACAgD,wBAAwB,CAACG,uBAAuB,CAAC,CAAC;QACtD;QACA,IAAI,OAAOZ,MAAM,KAAK,WAAW,EAAE;UAC/B,uIAAgC,CAACE,IAAI,CAAEC,MAAM,IAAKA,MAAM,CAACU,mBAAmB,CAACb,MAAM,CAAC,CAAC;QACzF;QACA,4IAAqC,CAACE,IAAI,CAAEC,MAAM,IAAMP,KAAI,CAACkB,YAAY,GAAGX,MAAM,CAACY,iBAAiB,CAAC,CAAE,CAAC;MAC5G,CAAC,EAAC;IACN;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACUC,QAAQA,CAACC,QAAQ,EAAE;IAAA,IAAAC,MAAA;IAAA,OAAApB,6KAAA;MACrB,IAAIoB,MAAI,CAACJ,YAAY,EAAE;QACnBI,MAAI,CAACJ,YAAY,CAACE,QAAQ,CAACC,QAAQ,CAAC;MACxC;IAAC;EACL;EACAE,MAAMA,CAAA,EAAG;IACL,MAAMC,IAAI,GAAGzD,4DAAU,CAAC,IAAI,CAAC;IAC7B,OAAQjB,qDAAC,CAACE,iDAAI,EAAE;MAAEyE,GAAG,EAAE,0CAA0C;MAAEC,KAAK,EAAE;QAClE,CAACF,IAAI,GAAG,IAAI;QACZ,UAAU,EAAE,IAAI;QAChB,yBAAyB,EAAEvD,wDAAM,CAACoC,UAAU,CAAC,wBAAwB;MACzE;IAAE,CAAC,CAAC;EACZ;EACA,IAAIsB,EAAEA,CAAA,EAAG;IAAE,OAAOzE,qDAAU,CAAC,IAAI,CAAC;EAAE;AACxC,CAAC;AACD,MAAMwD,cAAc,GAAGA,CAAA,KAAM;EACzB;AACJ;AACA;EACI,MAAMkB,aAAa,GAAGzD,4DAAU,CAACiC,MAAM,EAAE,KAAK,CAAC,IAAIjC,4DAAU,CAACiC,MAAM,EAAE,QAAQ,CAAC;EAC/E,IAAIwB,aAAa,EAAE;IACf,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;EACI,MAAMC,kBAAkB,GAAG1D,4DAAU,CAACiC,MAAM,EAAE,SAAS,CAAC,IAAIjC,4DAAU,CAACiC,MAAM,EAAE,WAAW,CAAC;EAC3F,IAAIyB,kBAAkB,EAAE;IACpB,OAAO,IAAI;EACf;EACA,OAAO,KAAK;AAChB,CAAC;AACD,MAAM5B,GAAG,GAAI6B,QAAQ,IAAK;EACtB,IAAI,qBAAqB,IAAI1B,MAAM,EAAE;IACjCA,MAAM,CAAC2B,mBAAmB,CAACD,QAAQ,CAAC;EACxC,CAAC,MACI;IACDE,UAAU,CAACF,QAAQ,EAAE,EAAE,CAAC;EAC5B;AACJ,CAAC;AACDlC,GAAG,CAACqC,KAAK,GAAGtC,YAAY;AAExB,MAAMuC,aAAa,GAAG,8pFAA8pF;AACprF,MAAMC,mBAAmB,GAAGD,aAAa;AAEzC,MAAME,YAAY,GAAG,y9FAAy9F;AAC9+F,MAAMC,kBAAkB,GAAGD,YAAY;AAEvC,MAAME,OAAO,GAAG,MAAM;EAClBzC,WAAWA,CAACC,OAAO,EAAE;IACjBjD,qDAAgB,CAAC,IAAI,EAAEiD,OAAO,CAAC;IAC/B,IAAI,CAACyC,QAAQ,GAAG,KAAK;EACzB;EACAhB,MAAMA,CAAA,EAAG;IACL,MAAMC,IAAI,GAAGzD,4DAAU,CAAC,IAAI,CAAC;IAC7B,OAAQjB,qDAAC,CAACE,iDAAI,EAAE;MAAEyE,GAAG,EAAE,0CAA0C;MAAEC,KAAK,EAAE;QAClE,CAACF,IAAI,GAAG,IAAI;QACZ,CAAC,kBAAkB,GAAG,IAAI,CAACe;MAC/B;IAAE,CAAC,CAAC;EACZ;AACJ,CAAC;AACDD,OAAO,CAACL,KAAK,GAAG;EACZO,GAAG,EAAEL,mBAAmB;EACxBM,EAAE,EAAEJ;AACR,CAAC;AAED,MAAMK,UAAU,GAAG,6qFAA6qF;AAChsF,MAAMC,gBAAgB,GAAGD,UAAU;AAEnC,MAAME,OAAO,GAAG,MAAM;EAClB/C,WAAWA,CAACC,OAAO,EAAE;IACjBjD,qDAAgB,CAAC,IAAI,EAAEiD,OAAO,CAAC;IAC/B,IAAI,CAAC+C,cAAc,GAAGzF,qDAAW,CAAC,IAAI,EAAE,gBAAgB,EAAE,CAAC,CAAC;IAC5D,IAAI,CAAC0F,SAAS,GAAG1F,qDAAW,CAAC,IAAI,EAAE,WAAW,EAAE,CAAC,CAAC;IAClD,IAAI,CAAC2F,YAAY,GAAG3F,qDAAW,CAAC,IAAI,EAAE,cAAc,EAAE,CAAC,CAAC;IACxD,IAAI,CAAC4F,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACC,WAAW,GAAG,KAAK;IACxB,IAAI,CAACC,UAAU,GAAG,CAAC;IACnB,IAAI,CAACC,MAAM,GAAG,KAAK;IACnB,IAAI,CAACC,IAAI,GAAG,CAAC,CAAC;IACd,IAAI,CAACC,OAAO,GAAG,CAAC,CAAC;IACjB,IAAI,CAACC,aAAa,GAAG,IAAI;IACzB,IAAI,CAACC,aAAa,GAAG,IAAI;IACzB,IAAI,CAACC,WAAW,GAAG,IAAI;IACvB;IACA;IACA;IACA,IAAI,CAACC,MAAM,GAAG;MACVC,SAAS,EAAE,CAAC;MACZC,UAAU,EAAE,CAAC;MACbC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAEC,SAAS;MAChBC,MAAM,EAAE,CAAC;MACTC,MAAM,EAAE,CAAC;MACTC,SAAS,EAAE,CAAC;MACZC,QAAQ,EAAE,CAAC;MACXC,QAAQ,EAAE,CAAC;MACXC,SAAS,EAAE,CAAC;MACZC,SAAS,EAAE,CAAC;MACZC,MAAM,EAAE,CAAC;MACTC,MAAM,EAAE,CAAC;MACTC,WAAW,EAAE,CAAC;MACdC,IAAI,EAAEX,SAAS;MACfb,WAAW,EAAE;IACjB,CAAC;IACD,IAAI,CAACyB,KAAK,GAAGZ,SAAS;IACtB,IAAI,CAACa,UAAU,GAAG,KAAK;IACvB,IAAI,CAACC,eAAe,GAAGd,SAAS;IAChC,IAAI,CAACe,OAAO,GAAG,KAAK;IACpB,IAAI,CAACC,OAAO,GAAG,IAAI;IACnB,IAAI,CAACC,YAAY,GAAG,KAAK;EAC7B;EACAC,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAAC1B,aAAa,GAAG,IAAI,CAAC3B,EAAE,CAACsD,OAAO,CAAC,kCAAkC,CAAC,KAAK,IAAI;IACjF;AACR;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI5G,uDAAY,CAAC,IAAI,CAACsD,EAAE,CAAC,EAAE;MACvB;AACZ;AACA;AACA;AACA;AACA;MACY,MAAMuD,WAAW,GAAI,IAAI,CAAC1B,WAAW,GAAG,IAAI,CAAC7B,EAAE,CAACsD,OAAO,CAAC,UAAU,CAAE;MACpE,IAAIC,WAAW,KAAK,IAAI,EAAE;QACtB;AAChB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;QACgB,IAAI,CAACC,gBAAgB,GAAG,MAAM,IAAI,CAACC,MAAM,CAAC,CAAC;QAC3CF,WAAW,CAACG,gBAAgB,CAAC,iBAAiB,EAAE,IAAI,CAACF,gBAAgB,CAAC;MAC1E;IACJ;EACJ;EACAG,oBAAoBA,CAAA,EAAG;IACnB,IAAI,CAACC,WAAW,CAAC,CAAC;IAClB,IAAIlH,uDAAY,CAAC,IAAI,CAACsD,EAAE,CAAC,EAAE;MACvB;AACZ;AACA;AACA;AACA;AACA;MACY,MAAM;QAAE6B,WAAW;QAAE2B;MAAiB,CAAC,GAAG,IAAI;MAC9C,IAAI3B,WAAW,KAAK,IAAI,IAAI2B,gBAAgB,KAAKrB,SAAS,EAAE;QACxDN,WAAW,CAACgC,mBAAmB,CAAC,iBAAiB,EAAEL,gBAAgB,CAAC;MACxE;MACA,IAAI,CAAC3B,WAAW,GAAG,IAAI;MACvB,IAAI,CAAC2B,gBAAgB,GAAGrB,SAAS;IACrC;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI2B,QAAQA,CAAA,EAAG;IACP,IAAI,IAAI,CAAClC,aAAa,EAAE;MACpBmC,YAAY,CAAC,IAAI,CAACnC,aAAa,CAAC;MAChC,IAAI,CAACA,aAAa,GAAG,IAAI;IAC7B;IACA,IAAI,CAACA,aAAa,GAAGvB,UAAU,CAAC,MAAM;MAClC;AACZ;AACA;AACA;AACA;AACA;MACY,IAAI,IAAI,CAACL,EAAE,CAACgE,YAAY,KAAK,IAAI,EAAE;QAC/B;MACJ;MACA,IAAI,CAACP,MAAM,CAAC,CAAC;IACjB,CAAC,EAAE,GAAG,CAAC;EACX;EACAQ,qBAAqBA,CAAA,EAAG;IACpB,MAAM;MAAEhB;IAAgB,CAAC,GAAG,IAAI;IAChC,MAAMpD,IAAI,GAAGzD,4DAAU,CAAC,IAAI,CAAC;IAC7B,OAAO6G,eAAe,KAAKd,SAAS,GAAGtC,IAAI,KAAK,KAAK,IAAIrD,4DAAU,CAAC,KAAK,CAAC,GAAGyG,eAAe;EAChG;EACAQ,MAAMA,CAAA,EAAG;IACL;AACR;AACA;AACA;AACA;AACA;AACA;AACA;IACQ;MACI,IAAI,IAAI,CAACT,UAAU,EAAE;QACjBrH,qDAAQ,CAAC,MAAM,IAAI,CAACuI,cAAc,CAAC,CAAC,CAAC;MACzC,CAAC,MACI,IAAI,IAAI,CAACzC,IAAI,KAAK,CAAC,IAAI,IAAI,CAACC,OAAO,KAAK,CAAC,EAAE;QAC5C,IAAI,CAACD,IAAI,GAAG,IAAI,CAACC,OAAO,GAAG,CAAC;QAC5B7F,qDAAW,CAAC,IAAI,CAAC;MACrB;IACJ;EACJ;EACAqI,cAAcA,CAAA,EAAG;IACb,MAAMC,IAAI,GAAGC,cAAc,CAAC,IAAI,CAACpE,EAAE,CAAC;IACpC,MAAMqE,GAAG,GAAGC,IAAI,CAACC,GAAG,CAAC,IAAI,CAACvE,EAAE,CAACwE,SAAS,EAAE,CAAC,CAAC;IAC1C,MAAMC,MAAM,GAAGH,IAAI,CAACC,GAAG,CAACJ,IAAI,CAACO,YAAY,GAAGL,GAAG,GAAG,IAAI,CAACrE,EAAE,CAAC0E,YAAY,EAAE,CAAC,CAAC;IAC1E,MAAMC,KAAK,GAAGN,GAAG,KAAK,IAAI,CAAC5C,IAAI,IAAIgD,MAAM,KAAK,IAAI,CAAC/C,OAAO;IAC1D,IAAIiD,KAAK,EAAE;MACP,IAAI,CAAClD,IAAI,GAAG4C,GAAG;MACf,IAAI,CAAC3C,OAAO,GAAG+C,MAAM;MACrB5I,qDAAW,CAAC,IAAI,CAAC;IACrB;EACJ;EACA+I,QAAQA,CAACC,EAAE,EAAE;IACT,MAAMC,SAAS,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC;IAC5B,MAAMC,WAAW,GAAG,CAAC,IAAI,CAAC3D,WAAW;IACrC,IAAI,CAACC,UAAU,GAAGuD,SAAS;IAC3B,IAAIG,WAAW,EAAE;MACb,IAAI,CAACC,aAAa,CAAC,CAAC;IACxB;IACA,IAAI,CAAC,IAAI,CAAC1D,MAAM,IAAI,IAAI,CAAC4B,YAAY,EAAE;MACnC,IAAI,CAAC5B,MAAM,GAAG,IAAI;MAClB7F,qDAAQ,CAAEwJ,EAAE,IAAK;QACb,IAAI,CAAC3D,MAAM,GAAG,KAAK;QACnB,IAAI,CAACM,MAAM,CAACI,KAAK,GAAG2C,EAAE;QACtBO,kBAAkB,CAAC,IAAI,CAACtD,MAAM,EAAE,IAAI,CAACuD,QAAQ,EAAEF,EAAE,EAAEF,WAAW,CAAC;QAC/D,IAAI,CAAC9D,SAAS,CAACmE,IAAI,CAAC,IAAI,CAACxD,MAAM,CAAC;MACpC,CAAC,CAAC;IACN;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACUvE,gBAAgBA,CAAA,EAAG;IAAA,IAAAgI,MAAA;IAAA,OAAAhH,6KAAA;MACrB;AACR;AACA;AACA;MACQ,IAAI,CAACgH,MAAI,CAACF,QAAQ,EAAE;QAChB,MAAM,IAAIG,OAAO,CAAEC,OAAO,IAAK9I,uDAAgB,CAAC4I,MAAI,CAACvF,EAAE,EAAEyF,OAAO,CAAC,CAAC;MACtE;MACA,OAAOD,OAAO,CAACC,OAAO,CAACF,MAAI,CAACF,QAAQ,CAAC;IAAC;EAC1C;EACA;AACJ;AACA;AACA;EACUK,oBAAoBA,CAAA,EAAG;IAAA,IAAAC,MAAA;IAAA,OAAApH,6KAAA;MACzB,IAAI,CAACoH,MAAI,CAACC,mBAAmB,EAAE;QAC3B,MAAM,IAAIJ,OAAO,CAAEC,OAAO,IAAK9I,uDAAgB,CAACgJ,MAAI,CAAC3F,EAAE,EAAEyF,OAAO,CAAC,CAAC;MACtE;MACA,OAAOD,OAAO,CAACC,OAAO,CAACE,MAAI,CAACC,mBAAmB,CAAC;IAAC;EACrD;EACA;AACJ;AACA;AACA;AACA;EACIC,WAAWA,CAACC,QAAQ,GAAG,CAAC,EAAE;IACtB,OAAO,IAAI,CAACC,aAAa,CAAC5D,SAAS,EAAE,CAAC,EAAE2D,QAAQ,CAAC;EACrD;EACA;AACJ;AACA;AACA;AACA;EACUE,cAAcA,CAAA,EAAe;IAAA,IAAAC,MAAA;IAAA,OAAA1H,6KAAA,YAAduH,QAAQ,GAAG,CAAC;MAC7B,MAAMT,QAAQ,SAASY,MAAI,CAAC1I,gBAAgB,CAAC,CAAC;MAC9C,MAAM2I,CAAC,GAAGb,QAAQ,CAACc,YAAY,GAAGd,QAAQ,CAACe,YAAY;MACvD,OAAOH,MAAI,CAACF,aAAa,CAAC5D,SAAS,EAAE+D,CAAC,EAAEJ,QAAQ,CAAC;IAAC,GAAAO,KAAA,OAAAC,SAAA;EACtD;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACUC,aAAaA,CAACC,CAAC,EAAEN,CAAC,EAAEJ,QAAQ,EAAE;IAAA,IAAAW,MAAA;IAAA,OAAAlI,6KAAA;MAChC,MAAM8G,QAAQ,SAASoB,MAAI,CAAClJ,gBAAgB,CAAC,CAAC;MAC9C,OAAOkJ,MAAI,CAACV,aAAa,CAACS,CAAC,GAAGnB,QAAQ,CAACrD,UAAU,EAAEkE,CAAC,GAAGb,QAAQ,CAACtD,SAAS,EAAE+D,QAAQ,CAAC;IAAC;EACzF;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACUC,aAAaA,CAAAW,EAAA,EAAAC,GAAA,EAAqB;IAAA,IAAAC,MAAA;IAAA,OAAArI,6KAAA,YAApBiI,CAAC,EAAEN,CAAC,EAAEJ,QAAQ,GAAG,CAAC;MAClC,MAAM9F,EAAE,SAAS4G,MAAI,CAACrJ,gBAAgB,CAAC,CAAC;MACxC,IAAIuI,QAAQ,GAAG,EAAE,EAAE;QACf,IAAII,CAAC,IAAI,IAAI,EAAE;UACXlG,EAAE,CAAC+B,SAAS,GAAGmE,CAAC;QACpB;QACA,IAAIM,CAAC,IAAI,IAAI,EAAE;UACXxG,EAAE,CAACgC,UAAU,GAAGwE,CAAC;QACrB;QACA;MACJ;MACA,IAAIf,OAAO;MACX,IAAInD,SAAS,GAAG,CAAC;MACjB,MAAMuE,OAAO,GAAG,IAAIrB,OAAO,CAAEvK,CAAC,IAAMwK,OAAO,GAAGxK,CAAE,CAAC;MACjD,MAAM6L,KAAK,GAAG9G,EAAE,CAAC+B,SAAS;MAC1B,MAAMgF,KAAK,GAAG/G,EAAE,CAACgC,UAAU;MAC3B,MAAMY,MAAM,GAAGsD,CAAC,IAAI,IAAI,GAAGA,CAAC,GAAGY,KAAK,GAAG,CAAC;MACxC,MAAMnE,MAAM,GAAG6D,CAAC,IAAI,IAAI,GAAGA,CAAC,GAAGO,KAAK,GAAG,CAAC;MACxC;MACA,MAAMC,IAAI,GAAIlC,SAAS,IAAK;QACxB,MAAMmC,UAAU,GAAG3C,IAAI,CAAC4C,GAAG,CAAC,CAAC,EAAE,CAACpC,SAAS,GAAGxC,SAAS,IAAIwD,QAAQ,CAAC,GAAG,CAAC;QACtE,MAAMqB,MAAM,GAAG7C,IAAI,CAAC8C,GAAG,CAACH,UAAU,EAAE,CAAC,CAAC,GAAG,CAAC;QAC1C,IAAIrE,MAAM,KAAK,CAAC,EAAE;UACd5C,EAAE,CAAC+B,SAAS,GAAGuC,IAAI,CAAC+C,KAAK,CAACF,MAAM,GAAGvE,MAAM,GAAGkE,KAAK,CAAC;QACtD;QACA,IAAInE,MAAM,KAAK,CAAC,EAAE;UACd3C,EAAE,CAACgC,UAAU,GAAGsC,IAAI,CAAC+C,KAAK,CAACF,MAAM,GAAGxE,MAAM,GAAGoE,KAAK,CAAC;QACvD;QACA,IAAII,MAAM,GAAG,CAAC,EAAE;UACZ;UACA;UACAG,qBAAqB,CAACN,IAAI,CAAC;QAC/B,CAAC,MACI;UACDvB,OAAO,CAAC,CAAC;QACb;MACJ,CAAC;MACD;MACA6B,qBAAqB,CAAEnC,EAAE,IAAK;QAC1B7C,SAAS,GAAG6C,EAAE;QACd6B,IAAI,CAAC7B,EAAE,CAAC;MACZ,CAAC,CAAC;MACF,OAAO0B,OAAO;IAAC,GAAAR,KAAA,OAAAC,SAAA;EACnB;EACApB,aAAaA,CAAA,EAAG;IACZ,IAAI,CAAC5D,WAAW,GAAG,IAAI;IACvB,IAAI,CAACJ,cAAc,CAACoE,IAAI,CAAC;MACrBhE,WAAW,EAAE;IACjB,CAAC,CAAC;IACF,IAAI,IAAI,CAACD,QAAQ,EAAE;MACfkG,aAAa,CAAC,IAAI,CAAClG,QAAQ,CAAC;IAChC;IACA;IACA,IAAI,CAACA,QAAQ,GAAGmG,WAAW,CAAC,MAAM;MAC9B,IAAI,IAAI,CAACjG,UAAU,GAAGwD,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,GAAG,EAAE;QACpC,IAAI,CAACpB,WAAW,CAAC,CAAC;MACtB;IACJ,CAAC,EAAE,GAAG,CAAC;EACX;EACAA,WAAWA,CAAA,EAAG;IACV,IAAI,IAAI,CAACvC,QAAQ,EACbkG,aAAa,CAAC,IAAI,CAAClG,QAAQ,CAAC;IAChC,IAAI,CAACA,QAAQ,GAAG,IAAI;IACpB,IAAI,IAAI,CAACC,WAAW,EAAE;MAClB,IAAI,CAACA,WAAW,GAAG,KAAK;MACxB,IAAI,CAACF,YAAY,CAACkE,IAAI,CAAC;QACnBhE,WAAW,EAAE;MACjB,CAAC,CAAC;IACN;EACJ;EACA1B,MAAMA,CAAA,EAAG;IACL,MAAM;MAAE+B,aAAa;MAAEuB,OAAO;MAAEC,OAAO;MAAEnD;IAAG,CAAC,GAAG,IAAI;IACpD,MAAMyH,GAAG,GAAGxK,mDAAK,CAAC+C,EAAE,CAAC,GAAG,KAAK,GAAG,KAAK;IACrC,MAAMH,IAAI,GAAGzD,4DAAU,CAAC,IAAI,CAAC;IAC7B,MAAM6G,eAAe,GAAG,IAAI,CAACgB,qBAAqB,CAAC,CAAC;IACpD,MAAMyD,gBAAgB,GAAG7H,IAAI,KAAK,KAAK;IACvC,MAAM8H,OAAO,GAAGhG,aAAa,GAAG,MAAM,GAAG,KAAK;IAC9C,IAAI,CAAC8B,MAAM,CAAC,CAAC;IACb,OAAQtI,qDAAC,CAACE,iDAAI,EAAE;MAAEyE,GAAG,EAAE,0CAA0C;MAAEC,KAAK,EAAE7C,qDAAkB,CAAC,IAAI,CAAC6F,KAAK,EAAE;QACjG,CAAClD,IAAI,GAAG,IAAI;QACZ,gBAAgB,EAAE1C,qDAAW,CAAC,aAAa,EAAE,IAAI,CAAC6C,EAAE,CAAC;QACrD4H,UAAU,EAAE3E,eAAe;QAC3B,CAAC,WAAWwE,GAAG,EAAE,GAAG;MACxB,CAAC,CAAC;MAAEnH,KAAK,EAAE;QACP,cAAc,EAAE,GAAG,IAAI,CAACmB,IAAI,IAAI;QAChC,iBAAiB,EAAE,GAAG,IAAI,CAACC,OAAO;MACtC;IAAE,CAAC,EAAEvG,qDAAC,CAAC,KAAK,EAAE;MAAE2E,GAAG,EAAE,0CAA0C;MAAE+H,GAAG,EAAG7H,EAAE,IAAM,IAAI,CAAC4F,mBAAmB,GAAG5F,EAAG;MAAE8H,EAAE,EAAE,oBAAoB;MAAEC,IAAI,EAAE;IAAa,CAAC,CAAC,EAAE5M,qDAAC,CAACwM,OAAO,EAAE;MAAE7H,GAAG,EAAE,0CAA0C;MAAEC,KAAK,EAAE;QACjO,cAAc,EAAE,IAAI;QACpB,UAAU,EAAEmD,OAAO;QACnB,UAAU,EAAEC,OAAO;QACnByE,UAAU,EAAE,CAAC1E,OAAO,IAAIC,OAAO,KAAKF;MACxC,CAAC;MAAE4E,GAAG,EAAGxC,QAAQ,IAAM,IAAI,CAACA,QAAQ,GAAGA,QAAS;MAAET,QAAQ,EAAE,IAAI,CAACxB,YAAY,GAAIyB,EAAE,IAAK,IAAI,CAACD,QAAQ,CAACC,EAAE,CAAC,GAAG1C,SAAS;MAAE4F,IAAI,EAAE;IAAS,CAAC,EAAE5M,qDAAC,CAAC,MAAM,EAAE;MAAE2E,GAAG,EAAE;IAA2C,CAAC,CAAC,CAAC,EAAE4H,gBAAgB,GAAIvM,qDAAC,CAAC,KAAK,EAAE;MAAE4E,KAAK,EAAE;IAAoB,CAAC,EAAE5E,qDAAC,CAAC,KAAK,EAAE;MAAE4E,KAAK,EAAE;IAAmB,CAAC,CAAC,EAAE5E,qDAAC,CAAC,KAAK,EAAE;MAAE4E,KAAK,EAAE;IAAoB,CAAC,CAAC,CAAC,GAAI,IAAI,EAAE5E,qDAAC,CAAC,MAAM,EAAE;MAAE2E,GAAG,EAAE,0CAA0C;MAAEkI,IAAI,EAAE;IAAQ,CAAC,CAAC,CAAC;EACzb;EACA,IAAIhI,EAAEA,CAAA,EAAG;IAAE,OAAOzE,qDAAU,CAAC,IAAI,CAAC;EAAE;AACxC,CAAC;AACD,MAAM0M,gBAAgB,GAAIjI,EAAE,IAAK;EAC7B,IAAIkI,EAAE;EACN,IAAIlI,EAAE,CAACmI,aAAa,EAAE;IAClB;IACA,OAAOnI,EAAE,CAACmI,aAAa;EAC3B;EACA,IAAI,CAACD,EAAE,GAAGlI,EAAE,CAACoI,UAAU,MAAM,IAAI,IAAIF,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACG,IAAI,EAAE;IACnE;IACA,OAAOrI,EAAE,CAACoI,UAAU,CAACC,IAAI;EAC7B;EACA,OAAO,IAAI;AACf,CAAC;AACD,MAAMjE,cAAc,GAAIpE,EAAE,IAAK;EAC3B,MAAMsI,IAAI,GAAGtI,EAAE,CAACsD,OAAO,CAAC,UAAU,CAAC;EACnC,IAAIgF,IAAI,EAAE;IACN,OAAOA,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;EACI,MAAMnE,IAAI,GAAGnE,EAAE,CAACsD,OAAO,CAAC,4DAA4D,CAAC;EACrF,IAAIa,IAAI,EAAE;IACN,OAAOA,IAAI;EACf;EACA,OAAO8D,gBAAgB,CAACjI,EAAE,CAAC;AAC/B,CAAC;AACD;AACA,MAAMoF,kBAAkB,GAAGA,CAACtD,MAAM,EAAE9B,EAAE,EAAEuI,SAAS,EAAEtD,WAAW,KAAK;EAC/D,MAAMuD,KAAK,GAAG1G,MAAM,CAACS,QAAQ;EAC7B,MAAMkG,KAAK,GAAG3G,MAAM,CAACU,QAAQ;EAC7B,MAAMkG,KAAK,GAAG5G,MAAM,CAACe,WAAW;EAChC,MAAMN,QAAQ,GAAGvC,EAAE,CAACgC,UAAU;EAC9B,MAAMQ,QAAQ,GAAGxC,EAAE,CAAC+B,SAAS;EAC7B,MAAM4G,SAAS,GAAGJ,SAAS,GAAGG,KAAK;EACnC,IAAIzD,WAAW,EAAE;IACb;IACAnD,MAAM,CAACQ,SAAS,GAAGiG,SAAS;IAC5BzG,MAAM,CAACM,MAAM,GAAGG,QAAQ;IACxBT,MAAM,CAACO,MAAM,GAAGG,QAAQ;IACxBV,MAAM,CAACW,SAAS,GAAGX,MAAM,CAACY,SAAS,GAAG,CAAC;EAC3C;EACAZ,MAAM,CAACe,WAAW,GAAG0F,SAAS;EAC9BzG,MAAM,CAACS,QAAQ,GAAGT,MAAM,CAACE,UAAU,GAAGO,QAAQ;EAC9CT,MAAM,CAACU,QAAQ,GAAGV,MAAM,CAACC,SAAS,GAAGS,QAAQ;EAC7CV,MAAM,CAACa,MAAM,GAAGJ,QAAQ,GAAGT,MAAM,CAACM,MAAM;EACxCN,MAAM,CAACc,MAAM,GAAGJ,QAAQ,GAAGV,MAAM,CAACO,MAAM;EACxC,IAAIsG,SAAS,GAAG,CAAC,IAAIA,SAAS,GAAG,GAAG,EAAE;IAClC,MAAMlG,SAAS,GAAG,CAACF,QAAQ,GAAGiG,KAAK,IAAIG,SAAS;IAChD,MAAMjG,SAAS,GAAG,CAACF,QAAQ,GAAGiG,KAAK,IAAIE,SAAS;IAChD7G,MAAM,CAACW,SAAS,GAAGA,SAAS,GAAG,GAAG,GAAGX,MAAM,CAACW,SAAS,GAAG,GAAG;IAC3DX,MAAM,CAACY,SAAS,GAAGA,SAAS,GAAG,GAAG,GAAGZ,MAAM,CAACY,SAAS,GAAG,GAAG;EAC/D;AACJ,CAAC;AACDzB,OAAO,CAACX,KAAK,GAAGU,gBAAgB;AAEhC,MAAM4H,gBAAgB,GAAGA,CAACvD,QAAQ,EAAEwD,MAAM,KAAK;EAC3ClN,qDAAQ,CAAC,MAAM;IACX,MAAMoG,SAAS,GAAGsD,QAAQ,CAACtD,SAAS;IACpC,MAAM+G,SAAS,GAAGzD,QAAQ,CAACc,YAAY,GAAGd,QAAQ,CAACe,YAAY;IAC/D;AACR;AACA;AACA;IACQ,MAAM2C,YAAY,GAAG,EAAE;IACvB;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,MAAMC,SAAS,GAAGF,SAAS,GAAGC,YAAY;IAC1C,MAAME,eAAe,GAAGlH,SAAS,GAAGiH,SAAS;IAC7C,MAAME,KAAK,GAAGrM,uDAAK,CAAC,CAAC,EAAE,CAAC,GAAGoM,eAAe,GAAGF,YAAY,EAAE,CAAC,CAAC;IAC7DhN,qDAAS,CAAC,MAAM;MACZ8M,MAAM,CAACvI,KAAK,CAAC6I,WAAW,CAAC,iBAAiB,EAAED,KAAK,CAACE,QAAQ,CAAC,CAAC,CAAC;IACjE,CAAC,CAAC;EACN,CAAC,CAAC;AACN,CAAC;AAED,MAAMC,YAAY,GAAG,kqBAAkqB;AACvrB,MAAMC,kBAAkB,GAAGD,YAAY;AAEvC,MAAME,WAAW,GAAG,yfAAyf;AAC7gB,MAAMC,iBAAiB,GAAGD,WAAW;AAErC,MAAME,MAAM,GAAG,MAAM;EACjBvL,WAAWA,CAACC,OAAO,EAAE;IAAA,IAAAuL,MAAA;IACjBxO,qDAAgB,CAAC,IAAI,EAAEiD,OAAO,CAAC;IAC/B,IAAI,CAACwL,YAAY,GAAG,IAAI;IACxB,IAAI,CAACC,sBAAsB,GAAG,MAAM;MAChC,MAAM/J,IAAI,GAAGzD,4DAAU,CAAC,IAAI,CAAC;MAC7B,IAAIyD,IAAI,KAAK,KAAK,EAAE;QAChB;MACJ;MACA,MAAM;QAAEe;MAAS,CAAC,GAAG,IAAI;MACzB,MAAMiJ,OAAO,GAAGjJ,QAAQ,KAAK,MAAM;MACnC,IAAI,CAACkJ,wBAAwB,CAAC,CAAC;MAC/B,IAAID,OAAO,EAAE;QACT,MAAME,MAAM,GAAG,IAAI,CAAC/J,EAAE,CAACsD,OAAO,CAAC,uCAAuC,CAAC;QACvE,MAAM0G,SAAS,GAAGD,MAAM,GAAG3M,qDAAc,CAAC2M,MAAM,CAAC,GAAG,IAAI;QACxD,IAAI,CAACC,SAAS,EAAE;UACZ3M,qDAAuB,CAAC,IAAI,CAAC2C,EAAE,CAAC;UAChC;QACJ;QACA,IAAI,CAACiK,eAAe,CAACD,SAAS,CAAC;MACnC;IACJ,CAAC;IACD,IAAI,CAACC,eAAe;MAAA,IAAAC,KAAA,GAAA3L,6KAAA,CAAG,WAAOyL,SAAS,EAAK;QACxC,MAAM3E,QAAQ,GAAIqE,MAAI,CAACrE,QAAQ,SAAS9H,qDAAgB,CAACyM,SAAS,CAAE;QACpE;AACZ;AACA;QACYN,MAAI,CAACS,qBAAqB,GAAG,MAAM;UAC/BvB,gBAAgB,CAACvD,QAAQ,EAAEqE,MAAI,CAAC1J,EAAE,CAAC;QACvC,CAAC;QACDqF,QAAQ,CAAC3B,gBAAgB,CAAC,QAAQ,EAAEgG,MAAI,CAACS,qBAAqB,CAAC;QAC/DvB,gBAAgB,CAACvD,QAAQ,EAAEqE,MAAI,CAAC1J,EAAE,CAAC;MACvC,CAAC;MAAA,iBAAAoK,GAAA;QAAA,OAAAF,KAAA,CAAA7D,KAAA,OAAAC,SAAA;MAAA;IAAA;IACD,IAAI,CAAC+D,eAAe,GAAG,KAAK;IAC5B,IAAI,CAACzJ,QAAQ,GAAGuB,SAAS;IACzB,IAAI,CAACmI,WAAW,GAAG,KAAK;EAC5B;EACAlM,gBAAgBA,CAAA,EAAG;IACf,IAAI,CAACwL,sBAAsB,CAAC,CAAC;EACjC;EACAW,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAACX,sBAAsB,CAAC,CAAC;EACjC;EACMvG,iBAAiBA,CAAA,EAAG;IAAA,IAAAmH,MAAA;IAAA,OAAAjM,6KAAA;MACtBiM,MAAI,CAACb,YAAY,SAASnM,mEAAwB;QAAA,IAAAiN,KAAA,GAAAlM,6KAAA,CAAC,WAAOmM,YAAY,EAAEC,aAAa,EAAK;UACtF;AACZ;AACA;AACA;AACA;UACY,IAAID,YAAY,KAAK,KAAK,IAAIC,aAAa,KAAKxI,SAAS,EAAE;YACvD,MAAMwI,aAAa;UACvB;UACAH,MAAI,CAACH,eAAe,GAAGK,YAAY,CAAC,CAAC;QACzC,CAAC;QAAA,iBAAAE,GAAA,EAAAC,GAAA;UAAA,OAAAJ,KAAA,CAAApE,KAAA,OAAAC,SAAA;QAAA;MAAA,IAAC;IAAC;EACP;EACA3C,oBAAoBA,CAAA,EAAG;IACnB,IAAI,IAAI,CAACgG,YAAY,EAAE;MACnB,IAAI,CAACA,YAAY,CAACmB,OAAO,CAAC,CAAC;IAC/B;EACJ;EACAhB,wBAAwBA,CAAA,EAAG;IACvB,IAAI,IAAI,CAACzE,QAAQ,IAAI,IAAI,CAAC8E,qBAAqB,EAAE;MAC7C,IAAI,CAAC9E,QAAQ,CAACxB,mBAAmB,CAAC,QAAQ,EAAE,IAAI,CAACsG,qBAAqB,CAAC;MACvE,IAAI,CAACA,qBAAqB,GAAGhI,SAAS;IAC1C;EACJ;EACAvC,MAAMA,CAAA,EAAG;IACL,MAAM;MAAE0K,WAAW;MAAE1J;IAAS,CAAC,GAAG,IAAI;IACtC,MAAMf,IAAI,GAAGzD,4DAAU,CAAC,IAAI,CAAC;IAC7B,MAAMkM,IAAI,GAAG,IAAI,CAACtI,EAAE,CAACsD,OAAO,CAAC,UAAU,CAAC;IACxC,MAAMyH,MAAM,GAAGzC,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,IAAI,CAAC0C,aAAa,CAAC,sBAAsB,CAAC;IACrG,OAAQ7P,qDAAC,CAACE,iDAAI,EAAE;MAAEyE,GAAG,EAAE,0CAA0C;MAAEmL,IAAI,EAAE,aAAa;MAAElL,KAAK,EAAE;QACvF,CAACF,IAAI,GAAG,IAAI;QACZ;QACA,CAAC,UAAUA,IAAI,EAAE,GAAG,IAAI;QACxB,CAAC,oBAAoB,GAAGyK,WAAW;QACnC,CAAC,sBAAsBzK,IAAI,EAAE,GAAGyK,WAAW;QAC3C,CAAC,wBAAwB,GAAG,CAAC,IAAI,CAACD,eAAe,KAAK,CAACU,MAAM,IAAIA,MAAM,CAACG,IAAI,KAAK,QAAQ,CAAC;QAC1F,CAAC,mBAAmBtK,QAAQ,EAAE,GAAGA,QAAQ,KAAKuB;MAClD;IAAE,CAAC,EAAEtC,IAAI,KAAK,KAAK,IAAIyK,WAAW,IAAInP,qDAAC,CAAC,KAAK,EAAE;MAAE2E,GAAG,EAAE,0CAA0C;MAAEC,KAAK,EAAE;IAAoB,CAAC,CAAC,EAAE5E,qDAAC,CAAC,MAAM,EAAE;MAAE2E,GAAG,EAAE;IAA2C,CAAC,CAAC,CAAC;EACxM;EACA,IAAIE,EAAEA,CAAA,EAAG;IAAE,OAAOzE,qDAAU,CAAC,IAAI,CAAC;EAAE;AACxC,CAAC;AACDkO,MAAM,CAACnJ,KAAK,GAAG;EACXO,GAAG,EAAEyI,kBAAkB;EACvBxI,EAAE,EAAE0I;AACR,CAAC;AAED,MAAM2B,UAAU,GAAG,sBAAsB;AACzC,MAAMC,YAAY,GAAIC,OAAO,IAAK;EAC9B,MAAMC,WAAW,GAAGC,QAAQ,CAACP,aAAa,CAAC,GAAGK,OAAO,qBAAqB,CAAC;EAC3E,IAAIC,WAAW,KAAK,IAAI,EAAE;IACtB,OAAOA,WAAW;EACtB;EACA,MAAME,QAAQ,GAAGD,QAAQ,CAACE,aAAa,CAACJ,OAAO,CAAC;EAChDG,QAAQ,CAACE,SAAS,CAACC,GAAG,CAAC,oBAAoB,CAAC;EAC5CH,QAAQ,CAAClL,KAAK,CAAC6I,WAAW,CAAC,SAAS,EAAE,MAAM,CAAC;EAC7CoC,QAAQ,CAACK,IAAI,CAACC,WAAW,CAACL,QAAQ,CAAC;EACnC,OAAOA,QAAQ;AACnB,CAAC;AACD,MAAMM,iBAAiB,GAAIC,QAAQ,IAAK;EACpC,IAAI,CAACA,QAAQ,EAAE;IACX;EACJ;EACA,MAAMC,QAAQ,GAAGD,QAAQ,CAACE,gBAAgB,CAAC,aAAa,CAAC;EACzD,OAAO;IACHjM,EAAE,EAAE+L,QAAQ;IACZC,QAAQ,EAAEE,KAAK,CAACC,IAAI,CAACH,QAAQ,CAAC,CAACI,GAAG,CAAEC,OAAO,IAAK;MAC5C,MAAMC,UAAU,GAAGD,OAAO,CAACrB,aAAa,CAAC,WAAW,CAAC;MACrD,OAAO;QACHhL,EAAE,EAAEqM,OAAO;QACXE,UAAU,EAAEF,OAAO,CAACG,UAAU,CAACxB,aAAa,CAAC,qBAAqB,CAAC;QACnEsB,UAAU;QACVG,YAAY,EAAEH,UAAU,GAAGA,UAAU,CAACE,UAAU,CAACxB,aAAa,CAAC,gBAAgB,CAAC,GAAG,IAAI;QACvF0B,YAAY,EAAER,KAAK,CAACC,IAAI,CAACE,OAAO,CAACJ,gBAAgB,CAAC,aAAa,CAAC;MACpE,CAAC;IACL,CAAC;EACL,CAAC;AACL,CAAC;AACD,MAAMU,mBAAmB,GAAGA,CAACtH,QAAQ,EAAEuH,iBAAiB,EAAE5C,SAAS,KAAK;EACpErO,qDAAQ,CAAC,MAAM;IACX,MAAMoG,SAAS,GAAGsD,QAAQ,CAACtD,SAAS;IACpC,MAAMmH,KAAK,GAAGrM,uDAAK,CAAC,CAAC,EAAE,CAAC,GAAG,CAACkF,SAAS,GAAG,GAAG,EAAE,GAAG,CAAC;IACjD;IACA,MAAM8K,eAAe,GAAG7C,SAAS,CAACgB,aAAa,CAAC,gCAAgC,CAAC;IACjF,IAAI6B,eAAe,KAAK,IAAI,EAAE;MAC1B9Q,qDAAS,CAAC,MAAM;QACZ+Q,gBAAgB,CAACF,iBAAiB,CAACZ,QAAQ,EAAE9C,KAAK,CAAC;MACvD,CAAC,CAAC;IACN;EACJ,CAAC,CAAC;AACN,CAAC;AACD,MAAM6D,2BAA2B,GAAGA,CAAChB,QAAQ,EAAEiB,OAAO,KAAK;EACvD;AACJ;AACA;AACA;AACA;AACA;EACI,IAAIjB,QAAQ,CAACnL,QAAQ,KAAK,MAAM,EAAE;IAC9B;EACJ;EACA,IAAIoM,OAAO,KAAK7K,SAAS,EAAE;IACvB4J,QAAQ,CAACzL,KAAK,CAAC2M,cAAc,CAAC,iBAAiB,CAAC;EACpD,CAAC,MACI;IACDlB,QAAQ,CAACzL,KAAK,CAAC6I,WAAW,CAAC,iBAAiB,EAAE6D,OAAO,CAAC5D,QAAQ,CAAC,CAAC,CAAC;EACrE;AACJ,CAAC;AACD,MAAM8D,+BAA+B,GAAGA,CAACrI,EAAE,EAAEsI,eAAe,EAAEpL,SAAS,KAAK;EACxE,IAAI,CAAC8C,EAAE,CAAC,CAAC,CAAC,CAACuI,cAAc,EAAE;IACvB;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,MAAMlE,KAAK,GAAGrE,EAAE,CAAC,CAAC,CAAC,CAACwI,iBAAiB,GAAG,GAAG,IAAItL,SAAS,IAAI,CAAC,GAAG,CAAC,GAAI,CAAC,CAAC,GAAG8C,EAAE,CAAC,CAAC,CAAC,CAACwI,iBAAiB,IAAI,GAAG,GAAI,EAAE;EAC9GN,2BAA2B,CAACI,eAAe,CAACnN,EAAE,EAAEkJ,KAAK,KAAK,CAAC,GAAG/G,SAAS,GAAG+G,KAAK,CAAC;AACpF,CAAC;AACD;AACA;AACA;AACA;AACA;AACA,MAAMoE,yBAAyB,GAAGA,CAACzI,EAAE;AAAE;AACvCsI,eAAe,EAAEP,iBAAiB,EAAEvH,QAAQ,KAAK;EAC7CtJ,qDAAS,CAAC,MAAM;IACZ,MAAMgG,SAAS,GAAGsD,QAAQ,CAACtD,SAAS;IACpCmL,+BAA+B,CAACrI,EAAE,EAAEsI,eAAe,EAAEpL,SAAS,CAAC;IAC/D,MAAMG,KAAK,GAAG2C,EAAE,CAAC,CAAC,CAAC;IACnB,MAAM0I,YAAY,GAAGrL,KAAK,CAACsL,gBAAgB;IAC3C,MAAMC,gBAAgB,GAAGF,YAAY,CAACG,KAAK,GAAGH,YAAY,CAACI,MAAM;IACjE,MAAMC,QAAQ,GAAG1L,KAAK,CAAC2L,UAAU,CAACH,KAAK,GAAGxL,KAAK,CAAC2L,UAAU,CAACF,MAAM;IACjE,MAAMG,YAAY,GAAGL,gBAAgB,KAAK,CAAC,IAAIG,QAAQ,KAAK,CAAC;IAC7D,MAAMG,QAAQ,GAAGzJ,IAAI,CAAC0J,GAAG,CAACT,YAAY,CAACU,IAAI,GAAG/L,KAAK,CAACgM,kBAAkB,CAACD,IAAI,CAAC;IAC5E,MAAME,SAAS,GAAG7J,IAAI,CAAC0J,GAAG,CAACT,YAAY,CAACa,KAAK,GAAGlM,KAAK,CAACgM,kBAAkB,CAACE,KAAK,CAAC;IAC/E,MAAMC,mBAAmB,GAAGZ,gBAAgB,GAAG,CAAC,KAAKM,QAAQ,IAAI,CAAC,IAAII,SAAS,IAAI,CAAC,CAAC;IACrF,IAAIL,YAAY,IAAIO,mBAAmB,EAAE;MACrC;IACJ;IACA,IAAInM,KAAK,CAACkL,cAAc,EAAE;MACtBkB,eAAe,CAACnB,eAAe,EAAE,KAAK,CAAC;MACvCmB,eAAe,CAAC1B,iBAAiB,CAAC;IACtC,CAAC,MACI;MACD;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;MACY,MAAM2B,oBAAoB,GAAIhB,YAAY,CAAC/G,CAAC,KAAK,CAAC,IAAI+G,YAAY,CAACrH,CAAC,KAAK,CAAC,IAAMqH,YAAY,CAACG,KAAK,KAAK,CAAC,IAAIH,YAAY,CAACI,MAAM,KAAK,CAAE;MACtI,IAAIY,oBAAoB,IAAIxM,SAAS,GAAG,CAAC,EAAE;QACvCuM,eAAe,CAACnB,eAAe,CAAC;QAChCmB,eAAe,CAAC1B,iBAAiB,EAAE,KAAK,CAAC;QACzCG,2BAA2B,CAACI,eAAe,CAACnN,EAAE,CAAC;MACnD;IACJ;EACJ,CAAC,CAAC;AACN,CAAC;AACD,MAAMsO,eAAe,GAAGA,CAACE,WAAW,EAAEC,MAAM,GAAG,IAAI,KAAK;EACpD,MAAM1C,QAAQ,GAAGyC,WAAW,CAACxO,EAAE;EAC/B,IAAIyO,MAAM,EAAE;IACR1C,QAAQ,CAACL,SAAS,CAACgD,MAAM,CAAC,mCAAmC,CAAC;IAC9D3C,QAAQ,CAAC4C,eAAe,CAAC,aAAa,CAAC;EAC3C,CAAC,MACI;IACD5C,QAAQ,CAACL,SAAS,CAACC,GAAG,CAAC,mCAAmC,CAAC;IAC3DI,QAAQ,CAAC6C,YAAY,CAAC,aAAa,EAAE,MAAM,CAAC;EAChD;AACJ,CAAC;AACD,MAAM9B,gBAAgB,GAAGA,CAACd,QAAQ,GAAG,EAAE,EAAE9C,KAAK,GAAG,CAAC,EAAEpL,UAAU,GAAG,KAAK,KAAK;EACvEkO,QAAQ,CAAC6C,OAAO,CAAExC,OAAO,IAAK;IAC1B,MAAMyC,QAAQ,GAAGzC,OAAO,CAACC,UAAU;IACnC,MAAMyC,QAAQ,GAAG1C,OAAO,CAACI,YAAY;IACrC,IAAI,CAACqC,QAAQ,IAAIA,QAAQ,CAACE,IAAI,KAAK,OAAO,EAAE;MACxC;IACJ;IACAD,QAAQ,CAACzO,KAAK,CAACxC,UAAU,GAAGA,UAAU,GAAGqN,UAAU,GAAG,EAAE;IACxD4D,QAAQ,CAACzO,KAAK,CAAC2O,SAAS,GAAG,WAAW/F,KAAK,KAAKA,KAAK,MAAM;EAC/D,CAAC,CAAC;AACN,CAAC;AACD,MAAMgG,gBAAgB,GAAGA,CAAC7J,QAAQ,EAAEwD,MAAM,EAAEsG,cAAc,KAAK;EAC3DxT,qDAAQ,CAAC,MAAM;IACX,MAAMoG,SAAS,GAAGsD,QAAQ,CAACtD,SAAS;IACpC,MAAMqN,YAAY,GAAGvG,MAAM,CAACzC,YAAY;IACxC,MAAM4C,SAAS,GAAGmG,cAAc,GAAGA,cAAc,CAAC/I,YAAY,GAAG,CAAC;IAClE;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI+I,cAAc,KAAK,IAAI,IAAIpN,SAAS,GAAGiH,SAAS,EAAE;MAClDH,MAAM,CAACvI,KAAK,CAAC6I,WAAW,CAAC,iBAAiB,EAAE,GAAG,CAAC;MAChD9D,QAAQ,CAAC/E,KAAK,CAAC6I,WAAW,CAAC,WAAW,EAAE,SAASiG,YAAY,iBAAiB,CAAC;MAC/E;IACJ;IACA,MAAMnG,eAAe,GAAGlH,SAAS,GAAGiH,SAAS;IAC7C,MAAMD,YAAY,GAAG,EAAE;IACvB,MAAMG,KAAK,GAAGrM,uDAAK,CAAC,CAAC,EAAEoM,eAAe,GAAGF,YAAY,EAAE,CAAC,CAAC;IACzDhN,qDAAS,CAAC,MAAM;MACZsJ,QAAQ,CAAC/E,KAAK,CAAC2M,cAAc,CAAC,WAAW,CAAC;MAC1CpE,MAAM,CAACvI,KAAK,CAAC6I,WAAW,CAAC,iBAAiB,EAAED,KAAK,CAACE,QAAQ,CAAC,CAAC,CAAC;IACjE,CAAC,CAAC;EACN,CAAC,CAAC;AACN,CAAC;AAED,MAAMiG,YAAY,GAAG,+lEAA+lE;AACpnE,MAAMC,kBAAkB,GAAGD,YAAY;AAEvC,MAAME,WAAW,GAAG,sgBAAsgB;AAC1hB,MAAMC,iBAAiB,GAAGD,WAAW;AAErC,MAAME,MAAM,GAAG,MAAM;EACjBvR,WAAWA,CAACC,OAAO,EAAE;IAAA,IAAAuR,MAAA;IACjBxU,qDAAgB,CAAC,IAAI,EAAEiD,OAAO,CAAC;IAC/B,IAAI,CAACwR,mBAAmB,GAAG,CAAC,CAAC;IAC7B,IAAI,CAACC,eAAe;MAAA,IAAAC,KAAA,GAAAtR,6KAAA,CAAG,WAAOyL,SAAS,EAAEmF,cAAc,EAAK;QACxD,MAAM9J,QAAQ,GAAIqK,MAAI,CAACrK,QAAQ,SAAS9H,qDAAgB,CAACyM,SAAS,CAAE;QACpE;AACZ;AACA;QACY0F,MAAI,CAACvF,qBAAqB,GAAG,MAAM;UAC/B+E,gBAAgB,CAACQ,MAAI,CAACrK,QAAQ,EAAEqK,MAAI,CAAC1P,EAAE,EAAEmP,cAAc,CAAC;QAC5D,CAAC;QACD9J,QAAQ,CAAC3B,gBAAgB,CAAC,QAAQ,EAAEgM,MAAI,CAACvF,qBAAqB,CAAC;QAC/D+E,gBAAgB,CAACQ,MAAI,CAACrK,QAAQ,EAAEqK,MAAI,CAAC1P,EAAE,EAAEmP,cAAc,CAAC;MAC5D,CAAC;MAAA,iBAAAW,GAAA,EAAAC,GAAA;QAAA,OAAAF,KAAA,CAAAxJ,KAAA,OAAAC,SAAA;MAAA;IAAA;IACD,IAAI,CAAC1F,QAAQ,GAAGuB,SAAS;IACzB,IAAI,CAACmI,WAAW,GAAG,KAAK;EAC5B;EACA0F,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAACL,mBAAmB,GAAG7S,uDAAqB,CAAC,IAAI,CAACkD,EAAE,CAAC;EAC7D;EACA5B,gBAAgBA,CAAA,EAAG;IACf,IAAI,CAAC6R,sBAAsB,CAAC,CAAC;EACjC;EACA1F,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAAC0F,sBAAsB,CAAC,CAAC;EACjC;EACAtM,oBAAoBA,CAAA,EAAG;IACnB,IAAI,CAACuM,wBAAwB,CAAC,CAAC;EACnC;EACMD,sBAAsBA,CAAA,EAAG;IAAA,IAAAE,MAAA;IAAA,OAAA5R,6KAAA;MAC3B,MAAMsB,IAAI,GAAGzD,4DAAU,CAAC+T,MAAI,CAAC;MAC7B,IAAItQ,IAAI,KAAK,KAAK,EAAE;QAChB;MACJ;MACA,MAAM;QAAEe;MAAS,CAAC,GAAGuP,MAAI;MACzB,MAAMC,WAAW,GAAGxP,QAAQ,KAAK,UAAU;MAC3C,MAAMiJ,OAAO,GAAGjJ,QAAQ,KAAK,MAAM;MACnCuP,MAAI,CAACD,wBAAwB,CAAC,CAAC;MAC/B,IAAIE,WAAW,EAAE;QACb,MAAMrG,MAAM,GAAGoG,MAAI,CAACnQ,EAAE,CAACsD,OAAO,CAAC,uCAAuC,CAAC;QACvE,MAAM0G,SAAS,GAAGD,MAAM,GAAG3M,qDAAc,CAAC2M,MAAM,CAAC,GAAG,IAAI;QACxD;QACAhO,qDAAS,CAAC,MAAM;UACZ,MAAMsU,KAAK,GAAGjF,YAAY,CAAC,WAAW,CAAC;UACvCiF,KAAK,CAACrB,IAAI,GAAG,OAAO;UACpB5D,YAAY,CAAC,iBAAiB,CAAC;QACnC,CAAC,CAAC;QACF,MAAM+E,MAAI,CAACG,mBAAmB,CAACtG,SAAS,EAAED,MAAM,CAAC;MACrD,CAAC,MACI,IAAIF,OAAO,EAAE;QACd,MAAME,MAAM,GAAGoG,MAAI,CAACnQ,EAAE,CAACsD,OAAO,CAAC,uCAAuC,CAAC;QACvE,MAAM0G,SAAS,GAAGD,MAAM,GAAG3M,qDAAc,CAAC2M,MAAM,CAAC,GAAG,IAAI;QACxD,IAAI,CAACC,SAAS,EAAE;UACZ3M,qDAAuB,CAAC8S,MAAI,CAACnQ,EAAE,CAAC;UAChC;QACJ;QACA,MAAMmP,cAAc,GAAGnF,SAAS,CAACgB,aAAa,CAAC,iCAAiC,CAAC;QACjF,MAAMmF,MAAI,CAACP,eAAe,CAAC5F,SAAS,EAAEmF,cAAc,CAAC;MACzD;IAAC;EACL;EACAe,wBAAwBA,CAAA,EAAG;IACvB,IAAI,IAAI,CAACK,oBAAoB,EAAE;MAC3B,IAAI,CAACA,oBAAoB,CAACC,UAAU,CAAC,CAAC;MACtC,IAAI,CAACD,oBAAoB,GAAGpO,SAAS;IACzC;IACA,IAAI,IAAI,CAACkD,QAAQ,IAAI,IAAI,CAAC8E,qBAAqB,EAAE;MAC7C,IAAI,CAAC9E,QAAQ,CAACxB,mBAAmB,CAAC,QAAQ,EAAE,IAAI,CAACsG,qBAAqB,CAAC;MACvE,IAAI,CAACA,qBAAqB,GAAGhI,SAAS;IAC1C;IACA,IAAI,IAAI,CAACsO,qBAAqB,EAAE;MAC5B,IAAI,CAACA,qBAAqB,CAAC/E,SAAS,CAACgD,MAAM,CAAC,sBAAsB,CAAC;MACnE,IAAI,CAAC+B,qBAAqB,GAAGtO,SAAS;IAC1C;EACJ;EACMmO,mBAAmBA,CAACtG,SAAS,EAAED,MAAM,EAAE;IAAA,IAAA2G,OAAA;IAAA,OAAAnS,6KAAA;MACzC,IAAI,CAACyL,SAAS,IAAI,CAACD,MAAM,EAAE;QACvB1M,qDAAuB,CAACqT,OAAI,CAAC1Q,EAAE,CAAC;QAChC;MACJ;MACA,IAAI,OAAO2Q,oBAAoB,KAAK,WAAW,EAAE;QAC7C;MACJ;MACAD,OAAI,CAACrL,QAAQ,SAAS9H,qDAAgB,CAACyM,SAAS,CAAC;MACjD,MAAM4G,OAAO,GAAG7G,MAAM,CAACkC,gBAAgB,CAAC,YAAY,CAAC;MACrDyE,OAAI,CAACD,qBAAqB,GAAGvE,KAAK,CAACC,IAAI,CAACyE,OAAO,CAAC,CAACC,IAAI,CAAEC,MAAM,IAAKA,MAAM,CAAClQ,QAAQ,KAAK,UAAU,CAAC;MACjG,IAAI,CAAC8P,OAAI,CAACD,qBAAqB,EAAE;QAC7B;MACJ;MACA,MAAMtD,eAAe,GAAGrB,iBAAiB,CAAC4E,OAAI,CAACD,qBAAqB,CAAC;MACrE,MAAM7D,iBAAiB,GAAGd,iBAAiB,CAAC4E,OAAI,CAAC1Q,EAAE,CAAC;MACpD,IAAI,CAACmN,eAAe,IAAI,CAACP,iBAAiB,EAAE;QACxC;MACJ;MACA0B,eAAe,CAACnB,eAAe,EAAE,KAAK,CAAC;MACvCJ,2BAA2B,CAACI,eAAe,CAACnN,EAAE,EAAE,CAAC,CAAC;MAClD;AACR;AACA;AACA;AACA;AACA;MACQ,MAAM+Q,mBAAmB,GAAIlM,EAAE,IAAK;QAChCyI,yBAAyB,CAACzI,EAAE,EAAEsI,eAAe,EAAEP,iBAAiB,EAAE8D,OAAI,CAACrL,QAAQ,CAAC;MACpF,CAAC;MACDqL,OAAI,CAACH,oBAAoB,GAAG,IAAII,oBAAoB,CAACI,mBAAmB,EAAE;QACtEC,IAAI,EAAEhH,SAAS;QACfiH,SAAS,EAAE,CAAC,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;MAC1D,CAAC,CAAC;MACFP,OAAI,CAACH,oBAAoB,CAACW,OAAO,CAACtE,iBAAiB,CAACZ,QAAQ,CAACY,iBAAiB,CAACZ,QAAQ,CAACmF,MAAM,GAAG,CAAC,CAAC,CAACnR,EAAE,CAAC;MACvG;AACR;AACA;AACA;AACA;MACQ0Q,OAAI,CAACvG,qBAAqB,GAAG,MAAM;QAC/BwC,mBAAmB,CAAC+D,OAAI,CAACrL,QAAQ,EAAEuH,iBAAiB,EAAE5C,SAAS,CAAC;MACpE,CAAC;MACD0G,OAAI,CAACrL,QAAQ,CAAC3B,gBAAgB,CAAC,QAAQ,EAAEgN,OAAI,CAACvG,qBAAqB,CAAC;MACpEpO,qDAAS,CAAC,MAAM;QACZ,IAAI2U,OAAI,CAACD,qBAAqB,KAAKtO,SAAS,EAAE;UAC1CuO,OAAI,CAACD,qBAAqB,CAAC/E,SAAS,CAACC,GAAG,CAAC,sBAAsB,CAAC;QACpE;MACJ,CAAC,CAAC;IAAC;EACP;EACA/L,MAAMA,CAAA,EAAG;IACL,MAAM;MAAE0K,WAAW;MAAEqF;IAAoB,CAAC,GAAG,IAAI;IACjD,MAAM9P,IAAI,GAAGzD,4DAAU,CAAC,IAAI,CAAC;IAC7B,MAAMwE,QAAQ,GAAG,IAAI,CAACA,QAAQ,IAAI,MAAM;IACxC;IACA,MAAMwQ,QAAQ,GAAGjU,qDAAW,CAAC,UAAU,EAAE,IAAI,CAAC6C,EAAE,CAAC,GAAG,MAAM,GAAG,QAAQ;IACrE,OAAQ7E,qDAAC,CAACE,iDAAI,EAAEgW,MAAM,CAACC,MAAM,CAAC;MAAExR,GAAG,EAAE,0CAA0C;MAAEmL,IAAI,EAAEmG,QAAQ;MAAErR,KAAK,EAAE;QAChG,CAACF,IAAI,GAAG,IAAI;QACZ;QACA,CAAC,UAAUA,IAAI,EAAE,GAAG,IAAI;QACxB,CAAC,oBAAoB,GAAG,IAAI,CAACyK,WAAW;QACxC,CAAC,mBAAmB1J,QAAQ,EAAE,GAAG,IAAI;QACrC,CAAC,sBAAsBf,IAAI,EAAE,GAAG,IAAI,CAACyK;MACzC;IAAE,CAAC,EAAEqF,mBAAmB,CAAC,EAAE9P,IAAI,KAAK,KAAK,IAAIyK,WAAW,IAAInP,qDAAC,CAAC,KAAK,EAAE;MAAE2E,GAAG,EAAE,0CAA0C;MAAEC,KAAK,EAAE;IAAoB,CAAC,CAAC,EAAE5E,qDAAC,CAAC,MAAM,EAAE;MAAE2E,GAAG,EAAE;IAA2C,CAAC,CAAC,CAAC;EAC9N;EACA,IAAIE,EAAEA,CAAA,EAAG;IAAE,OAAOzE,qDAAU,CAAC,IAAI,CAAC;EAAE;AACxC,CAAC;AACDkU,MAAM,CAACnP,KAAK,GAAG;EACXO,GAAG,EAAEyO,kBAAkB;EACvBxO,EAAE,EAAE0O;AACR,CAAC;AAED,MAAM+B,eAAe,GAAG,4FAA4F;AACpH,MAAMC,qBAAqB,GAAGD,eAAe;AAE7C,MAAME,YAAY,GAAG,MAAM;EACvBvT,WAAWA,CAACC,OAAO,EAAE;IACjBjD,qDAAgB,CAAC,IAAI,EAAEiD,OAAO,CAAC;IAC/B,IAAI,CAACuT,cAAc,GAAGjW,qDAAW,CAAC,IAAI,EAAE,gBAAgB,EAAE,CAAC,CAAC;IAC5D,IAAI,CAACkW,gBAAgB,GAAGlW,qDAAW,CAAC,IAAI,EAAE,kBAAkB,EAAE,CAAC,CAAC;IAChE,IAAI,CAACmW,eAAe,GAAGnW,qDAAW,CAAC,IAAI,EAAE,iBAAiB,EAAE,CAAC,CAAC;IAC9D,IAAI,CAACoW,cAAc,GAAGjU,gEAAoB,CAAC,CAAC;IAC5C,IAAI,CAACkU,4BAA4B,GAAG,KAAK;IACzC,IAAI,CAACjS,IAAI,GAAGzD,4DAAU,CAAC,IAAI,CAAC;IAC5B,IAAI,CAAC2V,QAAQ,GAAG5P,SAAS;IACzB,IAAI,CAAC6P,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACC,SAAS,GAAG9P,SAAS;IAC1B,IAAI,CAAC+P,YAAY,GAAG/P,SAAS;EACjC;EACAgQ,mBAAmBA,CAAA,EAAG;IAClB,IAAI,IAAI,CAACC,OAAO,EAAE;MACd,IAAI,CAACA,OAAO,CAACC,MAAM,CAAC,IAAI,CAACH,YAAY,KAAK/P,SAAS,CAAC;IACxD;EACJ;EACMkB,iBAAiBA,CAAA,EAAG;IAAA,IAAAiP,OAAA;IAAA,OAAA/T,6KAAA;MACtB,MAAMgU,OAAO,GAAGA,CAAA,KAAM;QAClBD,OAAI,CAACR,4BAA4B,GAAG,IAAI;QACxC,IAAIQ,OAAI,CAACJ,YAAY,EAAE;UACnBI,OAAI,CAACJ,YAAY,CAACK,OAAO,CAAC,CAAC;QAC/B;MACJ,CAAC;MACDD,OAAI,CAACF,OAAO,GAAG,OAAO,yIAAkC,EAAEI,sBAAsB,CAACF,OAAI,CAACtS,EAAE,EAAE,MAAM,CAACsS,OAAI,CAACR,4BAA4B,IAAI,CAAC,CAACQ,OAAI,CAACJ,YAAY,IAAII,OAAI,CAACJ,YAAY,CAACO,QAAQ,CAAC,CAAC,EAAE,MAAMF,OAAO,CAAC,CAAC,EAAGvL,IAAI,IAAK;QAAE,IAAIkB,EAAE;QAAE,OAAO,CAACA,EAAE,GAAGoK,OAAI,CAACI,GAAG,MAAM,IAAI,IAAIxK,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACyK,YAAY,CAAC3L,IAAI,CAAC;MAAE,CAAC,EAAE,CAAC4L,cAAc,EAAE5L,IAAI,EAAE6L,GAAG,KAAK;QACjV,IAAIP,OAAI,CAACI,GAAG,EAAE;UACVJ,OAAI,CAACI,GAAG,CAACI,QAAQ,CAAC,MAAM;YACpBR,OAAI,CAACR,4BAA4B,GAAG,KAAK;YACzC,IAAIQ,OAAI,CAACJ,YAAY,EAAE;cACnBI,OAAI,CAACJ,YAAY,CAACa,KAAK,CAACH,cAAc,CAAC;YAC3C;UACJ,CAAC,EAAE;YAAEI,eAAe,EAAE;UAAK,CAAC,CAAC;UAC7B;UACA,IAAIC,YAAY,GAAGL,cAAc,GAAG,CAAC,KAAK,GAAG,KAAK;UAClD;AAChB;AACA;AACA;AACA;AACA;AACA;AACA;UACgB,IAAI,CAACA,cAAc,EAAE;YACjBN,OAAI,CAACI,GAAG,CAACQ,MAAM,CAAC,gCAAgC,CAAC;YACjDD,YAAY,IAAIxV,6DAAuB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAEuJ,IAAI,CAAC,CAAC,CAAC,CAAC;UAC1F,CAAC,MACI;YACDiM,YAAY,IAAIxV,6DAAuB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAEuJ,IAAI,CAAC,CAAC,CAAC,CAAC;UAC1F;UACAsL,OAAI,CAACI,GAAG,CAACS,WAAW,CAACP,cAAc,GAAG,CAAC,GAAG,CAAC,EAAEK,YAAY,EAAEJ,GAAG,CAAC;QACnE,CAAC,MACI;UACDP,OAAI,CAACR,4BAA4B,GAAG,KAAK;QAC7C;MACJ,CAAC,CAAC;MACFQ,OAAI,CAACH,mBAAmB,CAAC,CAAC;IAAC;EAC/B;EACAnC,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAAC0B,cAAc,CAACpM,IAAI,CAAC,CAAC;EAC9B;EACA3B,oBAAoBA,CAAA,EAAG;IACnB,IAAI,IAAI,CAACyO,OAAO,EAAE;MACd,IAAI,CAACA,OAAO,CAACtH,OAAO,CAAC,CAAC;MACtB,IAAI,CAACsH,OAAO,GAAGjQ,SAAS;IAC5B;EACJ;EACA;EACMiR,MAAMA,CAACC,UAAU,EAAEC,SAAS,EAAEC,IAAI,EAAE;IAAA,IAAAC,OAAA;IAAA,OAAAjV,6KAAA;MACtC,MAAMkV,MAAM,SAASD,OAAI,CAAC3B,cAAc,CAAC6B,IAAI,CAAC,CAAC;MAC/C,IAAIC,OAAO,GAAG,KAAK;MACnB,IAAI;QACAA,OAAO,SAASH,OAAI,CAAC1V,UAAU,CAACuV,UAAU,EAAEC,SAAS,EAAEC,IAAI,CAAC;MAChE,CAAC,CACD,OAAO7X,CAAC,EAAE;QACNkY,OAAO,CAACC,KAAK,CAACnY,CAAC,CAAC;MACpB;MACA+X,MAAM,CAAC,CAAC;MACR,OAAOE,OAAO;IAAC;EACnB;EACA;EACMG,UAAUA,CAAChM,EAAE,EAAEiM,MAAM,EAAEC,SAAS,EAAE/B,SAAS,EAAE;IAAA,IAAAgC,OAAA;IAAA,OAAA1V,6KAAA;MAC/C,MAAMoV,OAAO,SAASM,OAAI,CAACC,OAAO,CAACpM,EAAE,EAAEiM,MAAM,EAAE;QAC3CjO,QAAQ,EAAEkO,SAAS,KAAK,MAAM,GAAG,CAAC,GAAG7R,SAAS;QAC9C6R,SAAS,EAAEA,SAAS,KAAK,MAAM,GAAG,MAAM,GAAG,SAAS;QACpDG,gBAAgB,EAAElC;MACtB,CAAC,CAAC;MACF,OAAO;QACH0B,OAAO;QACPS,OAAO,EAAEH,OAAI,CAACI;MAClB,CAAC;IAAC;EACN;EACA;EACMC,UAAUA,CAAA,EAAG;IAAA,IAAAC,OAAA;IAAA,OAAAhW,6KAAA;MACf,MAAMkQ,MAAM,GAAG8F,OAAI,CAACF,QAAQ;MAC5B,OAAO5F,MAAM,GACP;QACE3G,EAAE,EAAE2G,MAAM,CAACpD,OAAO;QAClB+I,OAAO,EAAE3F,MAAM;QACfsF,MAAM,EAAEQ,OAAI,CAACC;MACjB,CAAC,GACCrS,SAAS;IAAC;EACpB;EACM+R,OAAOA,CAACO,SAAS,EAAEV,MAAM,EAAER,IAAI,EAAE;IAAA,IAAAmB,OAAA;IAAA,OAAAnW,6KAAA;MACnC,IAAImW,OAAI,CAACC,eAAe,KAAKF,SAAS,IAAIzX,uDAAqB,CAAC+W,MAAM,EAAEW,OAAI,CAACF,YAAY,CAAC,EAAE;QACxF,OAAO,KAAK;MAChB;MACA;MACA,MAAMlB,SAAS,GAAGoB,OAAI,CAACL,QAAQ;MAC/B,MAAMhB,UAAU,SAAS3V,mEAAe,CAACgX,OAAI,CAAC3C,QAAQ,EAAE2C,OAAI,CAAC1U,EAAE,EAAEyU,SAAS,EAAE,CAAC,UAAU,EAAE,oBAAoB,CAAC,EAAEV,MAAM,CAAC;MACvHW,OAAI,CAACC,eAAe,GAAGF,SAAS;MAChCC,OAAI,CAACL,QAAQ,GAAGhB,UAAU;MAC1BqB,OAAI,CAACF,YAAY,GAAGT,MAAM;MAC1B;MACA,MAAMW,OAAI,CAACtB,MAAM,CAACC,UAAU,EAAEC,SAAS,EAAEC,IAAI,CAAC;MAC9C,MAAM5V,mEAAe,CAAC+W,OAAI,CAAC3C,QAAQ,EAAEuB,SAAS,CAAC;MAC/C,OAAO,IAAI;IAAC;EAChB;EACMxV,UAAUA,CAAA8W,GAAA,EAAAC,GAAA,EAAmC;IAAA,IAAAC,OAAA;IAAA,OAAAvW,6KAAA,YAAlC8U,UAAU,EAAEC,SAAS,EAAEC,IAAI,GAAG,CAAC,CAAC;MAC7C,IAAID,SAAS,KAAKD,UAAU,EAAE;QAC1B,OAAO,KAAK;MAChB;MACA;MACAyB,OAAI,CAACnD,gBAAgB,CAACrM,IAAI,CAAC,CAAC;MAC5B,MAAM;QAAEtF,EAAE;QAAEH;MAAK,CAAC,GAAGiV,OAAI;MACzB,MAAM9C,QAAQ,GAAG8C,OAAI,CAAC9C,QAAQ,IAAI1V,wDAAM,CAACoC,UAAU,CAAC,UAAU,EAAE,IAAI,CAAC;MACrE,MAAMyV,gBAAgB,GAAGZ,IAAI,CAACY,gBAAgB,IAAIW,OAAI,CAAC7C,SAAS,IAAI3V,wDAAM,CAACyY,GAAG,CAAC,cAAc,CAAC;MAC9F,MAAMjX,sDAAU,CAACuT,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC;QAAEzR,IAAI;QAC/CmS,QAAQ;QACRqB,UAAU;QACVC,SAAS;QAAEzK,MAAM,EAAE7I,EAAE;QACrB;AACZ;AACA;AACA;AACA;QACYgV,QAAQ,EAAEtY,uDAAY,CAACsD,EAAE,CAAC;QAAEiV,gBAAgB,EAAE1B,IAAI,CAAC2B,iBAAiB,GAC7DxC,GAAG,IAAK;UACP;AACpB;AACA;AACA;AACA;AACA;AACA;UACoB,IAAIA,GAAG,KAAKvQ,SAAS,IAAI,CAAC2S,OAAI,CAAChD,4BAA4B,EAAE;YACzDgD,OAAI,CAAChD,4BAA4B,GAAG,IAAI;YACxCY,GAAG,CAACI,QAAQ,CAAC,MAAM;cACfgC,OAAI,CAAChD,4BAA4B,GAAG,KAAK;cACzC,IAAIgD,OAAI,CAAC5C,YAAY,EAAE;gBACnB4C,OAAI,CAAC5C,YAAY,CAACa,KAAK,CAAC,KAAK,CAAC;cAClC;YACJ,CAAC,EAAE;cAAEC,eAAe,EAAE;YAAK,CAAC,CAAC;YAC7B;AACxB;AACA;AACA;AACA;AACA;YACwBN,GAAG,CAACS,WAAW,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;UAC5B,CAAC,MACI;YACD2B,OAAI,CAACpC,GAAG,GAAGA,GAAG;UAClB;QACJ,CAAC,GACCvQ;MAAU,CAAC,EAAEoR,IAAI,CAAC,EAAE;QAAEY;MAAiB,CAAC,CAAC,CAAC;MACpD;MACAW,OAAI,CAAClD,eAAe,CAACtM,IAAI,CAAC,CAAC;MAC3B,OAAO,IAAI;IAAC,GAAAe,KAAA,OAAAC,SAAA;EAChB;EACA1G,MAAMA,CAAA,EAAG;IACL,OAAOzE,qDAAC,CAAC,MAAM,EAAE;MAAE2E,GAAG,EAAE;IAA2C,CAAC,CAAC;EACzE;EACA,IAAIE,EAAEA,CAAA,EAAG;IAAE,OAAOzE,qDAAU,CAAC,IAAI,CAAC;EAAE;EACpC,WAAW4Z,QAAQA,CAAA,EAAG;IAAE,OAAO;MAC3B,cAAc,EAAE,CAAC,qBAAqB;IAC1C,CAAC;EAAE;AACP,CAAC;AACD1D,YAAY,CAACnR,KAAK,GAAGkR,qBAAqB;AAE1C,MAAM4D,WAAW,GAAG,qxEAAqxE;AACzyE,MAAMC,iBAAiB,GAAGD,WAAW;AAErC,MAAME,UAAU,GAAG,grBAAgrB;AACnsB,MAAMC,gBAAgB,GAAGD,UAAU;AAEnC,MAAME,YAAY,GAAG,MAAM;EACvBtX,WAAWA,CAACC,OAAO,EAAE;IACjBjD,qDAAgB,CAAC,IAAI,EAAEiD,OAAO,CAAC;IAC/B,IAAI,CAACsX,QAAQ,GAAGha,qDAAW,CAAC,IAAI,EAAE,UAAU,EAAE,CAAC,CAAC;IAChD,IAAI,CAACsH,KAAK,GAAGZ,SAAS;IACtB,IAAI,CAAC6M,IAAI,GAAG7M,SAAS;EACzB;EACAuT,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,SAAS,CAAC,CAAC;EACpB;EACAtS,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAACsS,SAAS,CAAC,CAAC;EACpB;EACAA,SAASA,CAAA,EAAG;IACR,MAAM3G,IAAI,GAAG,IAAI,CAAC4G,OAAO,CAAC,CAAC;IAC3B,IAAI,CAACH,QAAQ,CAACnQ,IAAI,CAAC;MACf,CAAC,SAAS0J,IAAI,EAAE,GAAG;IACvB,CAAC,CAAC;EACN;EACA4G,OAAOA,CAAA,EAAG;IACN,OAAO,IAAI,CAAC5G,IAAI,KAAK7M,SAAS,GAAG,IAAI,CAAC6M,IAAI,GAAG,SAAS;EAC1D;EACApP,MAAMA,CAAA,EAAG;IACL,MAAMC,IAAI,GAAGzD,4DAAU,CAAC,IAAI,CAAC;IAC7B,MAAM4S,IAAI,GAAG,IAAI,CAAC4G,OAAO,CAAC,CAAC;IAC3B,OAAQza,qDAAC,CAACE,iDAAI,EAAE;MAAEyE,GAAG,EAAE,0CAA0C;MAAEC,KAAK,EAAE7C,qDAAkB,CAAC,IAAI,CAAC6F,KAAK,EAAE;QACjG,CAAClD,IAAI,GAAG,IAAI;QACZ,CAAC,SAASmP,IAAI,EAAE,GAAG,IAAI;QACvB,WAAW,EAAEzD,QAAQ,CAACsK,GAAG,KAAK;MAClC,CAAC;IAAE,CAAC,EAAE1a,qDAAC,CAAC,KAAK,EAAE;MAAE2E,GAAG,EAAE,0CAA0C;MAAEC,KAAK,EAAE;IAAgB,CAAC,EAAE5E,qDAAC,CAAC,MAAM,EAAE;MAAE2E,GAAG,EAAE;IAA2C,CAAC,CAAC,CAAC,CAAC;EACpK;EACA,IAAIE,EAAEA,CAAA,EAAG;IAAE,OAAOzE,qDAAU,CAAC,IAAI,CAAC;EAAE;EACpC,WAAW4Z,QAAQA,CAAA,EAAG;IAAE,OAAO;MAC3B,MAAM,EAAE,CAAC,aAAa;IAC1B,CAAC;EAAE;AACP,CAAC;AACDK,YAAY,CAAClV,KAAK,GAAG;EACjBO,GAAG,EAAEwU,iBAAiB;EACtBvU,EAAE,EAAEyU;AACR,CAAC;AAED,MAAMO,aAAa,GAAG,+sFAA+sF;AACruF,MAAMC,mBAAmB,GAAGD,aAAa;AAEzC,MAAME,YAAY,GAAG,iyEAAiyE;AACtzE,MAAMC,kBAAkB,GAAGD,YAAY;AAEvC,MAAME,OAAO,GAAG,MAAM;EAClBhY,WAAWA,CAACC,OAAO,EAAE;IACjBjD,qDAAgB,CAAC,IAAI,EAAEiD,OAAO,CAAC;IAC/B,IAAI,CAACgY,cAAc,GAAG,IAAIC,GAAG,CAAC,CAAC;IAC/B,IAAI,CAACrT,KAAK,GAAGZ,SAAS;EAC1B;EACA6N,iBAAiBA,CAAA,EAAG;IAChB,MAAMqG,OAAO,GAAGnK,KAAK,CAACC,IAAI,CAAC,IAAI,CAACnM,EAAE,CAACiM,gBAAgB,CAAC,aAAa,CAAC,CAAC;IACnE,MAAMqK,YAAY,GAAGD,OAAO,CAACxF,IAAI,CAAE0F,MAAM,IAAK;MAC1C,OAAOA,MAAM,CAACrL,IAAI,KAAK,OAAO;IAClC,CAAC,CAAC;IACF,IAAIoL,YAAY,EAAE;MACdA,YAAY,CAAC5K,SAAS,CAACC,GAAG,CAAC,oBAAoB,CAAC;IACpD;IACA,MAAM6K,eAAe,GAAGH,OAAO,CAACI,OAAO,CAAC,CAAC;IACzC,MAAMC,WAAW,GAAGF,eAAe,CAAC3F,IAAI,CAAE0F,MAAM,IAAKA,MAAM,CAACrL,IAAI,KAAK,KAAK,CAAC,IACvEsL,eAAe,CAAC3F,IAAI,CAAE0F,MAAM,IAAKA,MAAM,CAACrL,IAAI,KAAK,SAAS,CAAC,IAC3DsL,eAAe,CAAC3F,IAAI,CAAE0F,MAAM,IAAKA,MAAM,CAACrL,IAAI,KAAK,WAAW,CAAC;IACjE,IAAIwL,WAAW,EAAE;MACbA,WAAW,CAAChL,SAAS,CAACC,GAAG,CAAC,mBAAmB,CAAC;IAClD;EACJ;EACAgL,aAAaA,CAAC9R,EAAE,EAAE;IACdA,EAAE,CAAC+R,eAAe,CAAC,CAAC;IACpB,MAAMvL,OAAO,GAAGxG,EAAE,CAACgS,MAAM,CAACxL,OAAO;IACjC,MAAMyL,aAAa,GAAGjS,EAAE,CAAC/C,MAAM;IAC/B,MAAMiV,SAAS,GAAG,CAAC,CAAC;IACpB,MAAMC,WAAW,GAAG,IAAI,CAACb,cAAc,CAACpB,GAAG,CAAC1J,OAAO,CAAC,IAAI,CAAC,CAAC;IAC1D,IAAI4L,cAAc,GAAG,KAAK;IAC1B5F,MAAM,CAAC6F,IAAI,CAACJ,aAAa,CAAC,CAACjI,OAAO,CAAE/O,GAAG,IAAK;MACxC,MAAMqX,QAAQ,GAAG,WAAWrX,GAAG,EAAE;MACjC,MAAMsX,QAAQ,GAAGN,aAAa,CAAChX,GAAG,CAAC;MACnC,IAAIsX,QAAQ,KAAKJ,WAAW,CAACG,QAAQ,CAAC,EAAE;QACpCF,cAAc,GAAG,IAAI;MACzB;MACA,IAAIG,QAAQ,EAAE;QACVL,SAAS,CAACI,QAAQ,CAAC,GAAG,IAAI;MAC9B;IACJ,CAAC,CAAC;IACF,IAAIF,cAAc,EAAE;MAChB,IAAI,CAACd,cAAc,CAACkB,GAAG,CAAChM,OAAO,EAAE0L,SAAS,CAAC;MAC3Clb,qDAAW,CAAC,IAAI,CAAC;IACrB;EACJ;EACA+D,MAAMA,CAAA,EAAG;IACL,MAAMC,IAAI,GAAGzD,4DAAU,CAAC,IAAI,CAAC;IAC7B,MAAM4a,WAAW,GAAG,CAAC,CAAC;IACtB,IAAI,CAACb,cAAc,CAACtH,OAAO,CAAEyI,KAAK,IAAK;MACnCjG,MAAM,CAACC,MAAM,CAAC0F,WAAW,EAAEM,KAAK,CAAC;IACrC,CAAC,CAAC;IACF,OAAQnc,qDAAC,CAACE,iDAAI,EAAE;MAAEyE,GAAG,EAAE,0CAA0C;MAAEC,KAAK,EAAEsR,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE0F,WAAW,CAAC,EAAE9Z,qDAAkB,CAAC,IAAI,CAAC6F,KAAK,EAAE;QAC/I,CAAClD,IAAI,GAAG,IAAI;QACZ,YAAY,EAAE1C,qDAAW,CAAC,aAAa,EAAE,IAAI,CAAC6C,EAAE;MACpD,CAAC,CAAC;IAAE,CAAC,EAAE7E,qDAAC,CAAC,KAAK,EAAE;MAAE2E,GAAG,EAAE,0CAA0C;MAAEC,KAAK,EAAE;IAAqB,CAAC,CAAC,EAAE5E,qDAAC,CAAC,KAAK,EAAE;MAAE2E,GAAG,EAAE,0CAA0C;MAAEC,KAAK,EAAE;IAAoB,CAAC,EAAE5E,qDAAC,CAAC,MAAM,EAAE;MAAE2E,GAAG,EAAE,0CAA0C;MAAEkI,IAAI,EAAE;IAAQ,CAAC,CAAC,EAAE7M,qDAAC,CAAC,MAAM,EAAE;MAAE2E,GAAG,EAAE,0CAA0C;MAAEkI,IAAI,EAAE;IAAY,CAAC,CAAC,EAAE7M,qDAAC,CAAC,KAAK,EAAE;MAAE2E,GAAG,EAAE,0CAA0C;MAAEC,KAAK,EAAE;IAAkB,CAAC,EAAE5E,qDAAC,CAAC,MAAM,EAAE;MAAE2E,GAAG,EAAE;IAA2C,CAAC,CAAC,CAAC,EAAE3E,qDAAC,CAAC,MAAM,EAAE;MAAE2E,GAAG,EAAE,0CAA0C;MAAEkI,IAAI,EAAE;IAAU,CAAC,CAAC,EAAE7M,qDAAC,CAAC,MAAM,EAAE;MAAE2E,GAAG,EAAE,0CAA0C;MAAEkI,IAAI,EAAE;IAAM,CAAC,CAAC,CAAC,CAAC;EAC9pB;EACA,IAAIhI,EAAEA,CAAA,EAAG;IAAE,OAAOzE,qDAAU,CAAC,IAAI,CAAC;EAAE;AACxC,CAAC;AACD2a,OAAO,CAAC5V,KAAK,GAAG;EACZO,GAAG,EAAEkV,mBAAmB;EACxBjV,EAAE,EAAEmV;AACR,CAAC", "sources": ["./node_modules/@ionic/core/dist/esm/ion-app_8.entry.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, h, H as Host, f as getElement, d as createEvent, e as readTask, i as forceUpdate, w as writeTask } from './index-a1a47f01.js';\nimport { shouldUseCloseWatcher } from './hardware-back-button-6107a37c.js';\nimport { p as printIonWarning } from './index-9b0d46f4.js';\nimport { b as getIonMode, c as config, a as isPlatform } from './ionic-global-94f25d1b.js';\nimport { m as hasLazyBuild, c as componentOnReady, l as clamp, i as inheritAriaAttributes, s as shallowEqualStringMap } from './helpers-be245865.js';\nimport { i as isRTL } from './dir-babeabeb.js';\nimport { c as createColorClasses, h as hostContext } from './theme-01f3f29c.js';\nimport { a as findIonContent, p as printIonContentErrorMsg, g as getScrollElement } from './index-f3946ac1.js';\nimport { c as createKeyboardController } from './keyboard-controller-ec5c2bfa.js';\nimport { g as getTimeGivenProgression } from './cubic-bezier-fe2083dc.js';\nimport { a as attachComponent, d as detachComponent } from './framework-delegate-ed4ba327.js';\nimport { c as createLockController } from './lock-controller-316928be.js';\nimport { t as transition } from './index-fae1515c.js';\nimport './index-a5d50daf.js';\nimport './keyboard-73175e24.js';\nimport './capacitor-59395cbd.js';\n\nconst appCss = \"html.plt-mobile ion-app{-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}html.plt-mobile ion-app [contenteditable]{-webkit-user-select:text;-moz-user-select:text;-ms-user-select:text;user-select:text}ion-app.force-statusbar-padding{--ion-safe-area-top:20px}\";\nconst IonAppStyle0 = appCss;\n\nconst App = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n    }\n    componentDidLoad() {\n        {\n            rIC(async () => {\n                const isHybrid = isPlatform(window, 'hybrid');\n                if (!config.getBoolean('_testing')) {\n                    import('./index-020f5464.js').then((module) => module.startTapClick(config));\n                }\n                if (config.getBoolean('statusTap', isHybrid)) {\n                    import('./status-tap-dfea3607.js').then((module) => module.startStatusTap());\n                }\n                if (config.getBoolean('inputShims', needInputShims())) {\n                    /**\n                     * needInputShims() ensures that only iOS and Android\n                     * platforms proceed into this block.\n                     */\n                    const platform = isPlatform(window, 'ios') ? 'ios' : 'android';\n                    import('./input-shims-a52daa3a.js').then((module) => module.startInputShims(config, platform));\n                }\n                const hardwareBackButtonModule = await import('./hardware-back-button-6107a37c.js');\n                const supportsHardwareBackButtonEvents = isHybrid || shouldUseCloseWatcher();\n                if (config.getBoolean('hardwareBackButton', supportsHardwareBackButtonEvents)) {\n                    hardwareBackButtonModule.startHardwareBackButton();\n                }\n                else {\n                    /**\n                     * If an app sets hardwareBackButton: false and experimentalCloseWatcher: true\n                     * then the close watcher will not be used.\n                     */\n                    if (shouldUseCloseWatcher()) {\n                        printIonWarning('experimentalCloseWatcher was set to `true`, but hardwareBackButton was set to `false`. Both config options must be `true` for the Close Watcher API to be used.');\n                    }\n                    hardwareBackButtonModule.blockHardwareBackButton();\n                }\n                if (typeof window !== 'undefined') {\n                    import('./keyboard-52278bd7.js').then((module) => module.startKeyboardAssist(window));\n                }\n                import('./focus-visible-dd40d69f.js').then((module) => (this.focusVisible = module.startFocusVisible()));\n            });\n        }\n    }\n    /**\n     * @internal\n     * Used to set focus on an element that uses `ion-focusable`.\n     * Do not use this if focusing the element as a result of a keyboard\n     * event as the focus utility should handle this for us. This method\n     * should be used when we want to programmatically focus an element as\n     * a result of another user action. (Ex: We focus the first element\n     * inside of a popover when the user presents it, but the popover is not always\n     * presented as a result of keyboard action.)\n     */\n    async setFocus(elements) {\n        if (this.focusVisible) {\n            this.focusVisible.setFocus(elements);\n        }\n    }\n    render() {\n        const mode = getIonMode(this);\n        return (h(Host, { key: '6d7c57453b4be454690e8f1a0721f1e3da8f92aa', class: {\n                [mode]: true,\n                'ion-page': true,\n                'force-statusbar-padding': config.getBoolean('_forceStatusbarPadding'),\n            } }));\n    }\n    get el() { return getElement(this); }\n};\nconst needInputShims = () => {\n    /**\n     * iOS always needs input shims\n     */\n    const needsShimsIOS = isPlatform(window, 'ios') && isPlatform(window, 'mobile');\n    if (needsShimsIOS) {\n        return true;\n    }\n    /**\n     * Android only needs input shims when running\n     * in the browser and only if the browser is using the\n     * new Chrome 108+ resize behavior: https://developer.chrome.com/blog/viewport-resize-behavior/\n     */\n    const isAndroidMobileWeb = isPlatform(window, 'android') && isPlatform(window, 'mobileweb');\n    if (isAndroidMobileWeb) {\n        return true;\n    }\n    return false;\n};\nconst rIC = (callback) => {\n    if ('requestIdleCallback' in window) {\n        window.requestIdleCallback(callback);\n    }\n    else {\n        setTimeout(callback, 32);\n    }\n};\nApp.style = IonAppStyle0;\n\nconst buttonsIosCss = \".sc-ion-buttons-ios-h{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-webkit-transform:translateZ(0);transform:translateZ(0);z-index:99}.sc-ion-buttons-ios-s ion-button{--padding-top:0;--padding-bottom:0;margin-left:0;margin-right:0;margin-top:0;margin-bottom:0}.sc-ion-buttons-ios-s ion-button{--padding-top:3px;--padding-bottom:3px;--padding-start:5px;--padding-end:5px;-webkit-margin-start:2px;margin-inline-start:2px;-webkit-margin-end:2px;margin-inline-end:2px;min-height:32px}.sc-ion-buttons-ios-s .button-has-icon-only{--padding-top:0;--padding-bottom:0}.sc-ion-buttons-ios-s ion-button:not(.button-round){--border-radius:4px}.sc-ion-buttons-ios-h.ion-color.sc-ion-buttons-ios-s .button,.ion-color .sc-ion-buttons-ios-h.sc-ion-buttons-ios-s .button{--color:initial;--border-color:initial;--background-focused:var(--ion-color-contrast)}.sc-ion-buttons-ios-h.ion-color.sc-ion-buttons-ios-s .button-solid,.ion-color .sc-ion-buttons-ios-h.sc-ion-buttons-ios-s .button-solid{--background:var(--ion-color-contrast);--background-focused:#000;--background-focused-opacity:.12;--background-activated:#000;--background-activated-opacity:.12;--background-hover:var(--ion-color-base);--background-hover-opacity:0.45;--color:var(--ion-color-base);--color-focused:var(--ion-color-base)}.sc-ion-buttons-ios-h.ion-color.sc-ion-buttons-ios-s .button-clear,.ion-color .sc-ion-buttons-ios-h.sc-ion-buttons-ios-s .button-clear{--color-activated:var(--ion-color-contrast);--color-focused:var(--ion-color-contrast)}.sc-ion-buttons-ios-h.ion-color.sc-ion-buttons-ios-s .button-outline,.ion-color .sc-ion-buttons-ios-h.sc-ion-buttons-ios-s .button-outline{--color-activated:var(--ion-color-base);--color-focused:var(--ion-color-contrast);--background-activated:var(--ion-color-contrast)}.sc-ion-buttons-ios-s .button-clear,.sc-ion-buttons-ios-s .button-outline{--background-activated:transparent;--background-focused:currentColor;--background-hover:transparent}.sc-ion-buttons-ios-s .button-solid:not(.ion-color){--background-focused:#000;--background-focused-opacity:.12;--background-activated:#000;--background-activated-opacity:.12}.sc-ion-buttons-ios-s ion-icon[slot=start]{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;-webkit-margin-end:0.3em;margin-inline-end:0.3em;font-size:1.41em;line-height:0.67}.sc-ion-buttons-ios-s ion-icon[slot=end]{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;-webkit-margin-start:0.4em;margin-inline-start:0.4em;font-size:1.41em;line-height:0.67}.sc-ion-buttons-ios-s ion-icon[slot=icon-only]{padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;font-size:1.65em;line-height:0.67}\";\nconst IonButtonsIosStyle0 = buttonsIosCss;\n\nconst buttonsMdCss = \".sc-ion-buttons-md-h{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-webkit-transform:translateZ(0);transform:translateZ(0);z-index:99}.sc-ion-buttons-md-s ion-button{--padding-top:0;--padding-bottom:0;margin-left:0;margin-right:0;margin-top:0;margin-bottom:0}.sc-ion-buttons-md-s ion-button{--padding-top:3px;--padding-bottom:3px;--padding-start:8px;--padding-end:8px;--box-shadow:none;-webkit-margin-start:2px;margin-inline-start:2px;-webkit-margin-end:2px;margin-inline-end:2px;min-height:32px}.sc-ion-buttons-md-s .button-has-icon-only{--padding-top:0;--padding-bottom:0}.sc-ion-buttons-md-s ion-button:not(.button-round){--border-radius:2px}.sc-ion-buttons-md-h.ion-color.sc-ion-buttons-md-s .button,.ion-color .sc-ion-buttons-md-h.sc-ion-buttons-md-s .button{--color:initial;--color-focused:var(--ion-color-contrast);--color-hover:var(--ion-color-contrast);--background-activated:transparent;--background-focused:var(--ion-color-contrast);--background-hover:var(--ion-color-contrast)}.sc-ion-buttons-md-h.ion-color.sc-ion-buttons-md-s .button-solid,.ion-color .sc-ion-buttons-md-h.sc-ion-buttons-md-s .button-solid{--background:var(--ion-color-contrast);--background-activated:transparent;--background-focused:var(--ion-color-shade);--background-hover:var(--ion-color-base);--color:var(--ion-color-base);--color-focused:var(--ion-color-base);--color-hover:var(--ion-color-base)}.sc-ion-buttons-md-h.ion-color.sc-ion-buttons-md-s .button-outline,.ion-color .sc-ion-buttons-md-h.sc-ion-buttons-md-s .button-outline{--border-color:var(--ion-color-contrast)}.sc-ion-buttons-md-s .button-has-icon-only.button-clear{--padding-top:12px;--padding-end:12px;--padding-bottom:12px;--padding-start:12px;--border-radius:50%;margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;width:3rem;height:3rem}.sc-ion-buttons-md-s .button{--background-hover:currentColor}.sc-ion-buttons-md-s .button-solid{--color:var(--ion-toolbar-background, var(--ion-background-color, #fff));--background:var(--ion-toolbar-color, var(--ion-text-color, #424242));--background-activated:transparent;--background-focused:currentColor}.sc-ion-buttons-md-s .button-outline{--color:initial;--background:transparent;--background-activated:transparent;--background-focused:currentColor;--background-hover:currentColor;--border-color:currentColor}.sc-ion-buttons-md-s .button-clear{--color:initial;--background:transparent;--background-activated:transparent;--background-focused:currentColor;--background-hover:currentColor}.sc-ion-buttons-md-s ion-icon[slot=start]{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;-webkit-margin-end:0.3em;margin-inline-end:0.3em;font-size:1.4em}.sc-ion-buttons-md-s ion-icon[slot=end]{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;-webkit-margin-start:0.4em;margin-inline-start:0.4em;font-size:1.4em}.sc-ion-buttons-md-s ion-icon[slot=icon-only]{padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;font-size:1.8em}\";\nconst IonButtonsMdStyle0 = buttonsMdCss;\n\nconst Buttons = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.collapse = false;\n    }\n    render() {\n        const mode = getIonMode(this);\n        return (h(Host, { key: '2929fd8c4469bab2953c23d47f601706acb104f1', class: {\n                [mode]: true,\n                ['buttons-collapse']: this.collapse,\n            } }));\n    }\n};\nButtons.style = {\n    ios: IonButtonsIosStyle0,\n    md: IonButtonsMdStyle0\n};\n\nconst contentCss = \":host{--background:var(--ion-background-color, #fff);--color:var(--ion-text-color, #000);--padding-top:0px;--padding-bottom:0px;--padding-start:0px;--padding-end:0px;--keyboard-offset:0px;--offset-top:0px;--offset-bottom:0px;--overflow:auto;display:block;position:relative;-ms-flex:1;flex:1;width:100%;height:100%;margin:0 !important;padding:0 !important;font-family:var(--ion-font-family, inherit);contain:size style}:host(.ion-color) .inner-scroll{background:var(--ion-color-base);color:var(--ion-color-contrast)}:host(.outer-content){--background:var(--ion-color-step-50, #f2f2f2)}#background-content{left:0px;right:0px;top:calc(var(--offset-top) * -1);bottom:calc(var(--offset-bottom) * -1);position:absolute;background:var(--background)}.inner-scroll{left:0px;right:0px;top:calc(var(--offset-top) * -1);bottom:calc(var(--offset-bottom) * -1);-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:calc(var(--padding-top) + var(--offset-top));padding-bottom:calc(var(--padding-bottom) + var(--keyboard-offset) + var(--offset-bottom));position:absolute;color:var(--color);-webkit-box-sizing:border-box;box-sizing:border-box;overflow:hidden;-ms-touch-action:pan-x pan-y pinch-zoom;touch-action:pan-x pan-y pinch-zoom}.scroll-y,.scroll-x{-webkit-overflow-scrolling:touch;z-index:0;will-change:scroll-position}.scroll-y{overflow-y:var(--overflow);overscroll-behavior-y:contain}.scroll-x{overflow-x:var(--overflow);overscroll-behavior-x:contain}.overscroll::before,.overscroll::after{position:absolute;width:1px;height:1px;content:\\\"\\\"}.overscroll::before{bottom:-1px}.overscroll::after{top:-1px}:host(.content-sizing){display:-ms-flexbox;display:flex;-ms-flex-direction:column;flex-direction:column;min-height:0;contain:none}:host(.content-sizing) .inner-scroll{position:relative;top:0;bottom:0;margin-top:calc(var(--offset-top) * -1);margin-bottom:calc(var(--offset-bottom) * -1)}.transition-effect{display:none;position:absolute;width:100%;height:100vh;opacity:0;pointer-events:none}:host(.content-ltr) .transition-effect{left:-100%;}:host(.content-rtl) .transition-effect{right:-100%;}.transition-cover{position:absolute;right:0;width:100%;height:100%;background:black;opacity:0.1}.transition-shadow{display:block;position:absolute;width:100%;height:100%;-webkit-box-shadow:inset -9px 0 9px 0 rgba(0, 0, 100, 0.03);box-shadow:inset -9px 0 9px 0 rgba(0, 0, 100, 0.03)}:host(.content-ltr) .transition-shadow{right:0;}:host(.content-rtl) .transition-shadow{left:0;-webkit-transform:scaleX(-1);transform:scaleX(-1)}::slotted([slot=fixed]){position:absolute;-webkit-transform:translateZ(0);transform:translateZ(0)}\";\nconst IonContentStyle0 = contentCss;\n\nconst Content = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.ionScrollStart = createEvent(this, \"ionScrollStart\", 7);\n        this.ionScroll = createEvent(this, \"ionScroll\", 7);\n        this.ionScrollEnd = createEvent(this, \"ionScrollEnd\", 7);\n        this.watchDog = null;\n        this.isScrolling = false;\n        this.lastScroll = 0;\n        this.queued = false;\n        this.cTop = -1;\n        this.cBottom = -1;\n        this.isMainContent = true;\n        this.resizeTimeout = null;\n        this.tabsElement = null;\n        // Detail is used in a hot loop in the scroll event, by allocating it here\n        // V8 will be able to inline any read/write to it since it's a monomorphic class.\n        // https://mrale.ph/blog/2015/01/11/whats-up-with-monomorphism.html\n        this.detail = {\n            scrollTop: 0,\n            scrollLeft: 0,\n            type: 'scroll',\n            event: undefined,\n            startX: 0,\n            startY: 0,\n            startTime: 0,\n            currentX: 0,\n            currentY: 0,\n            velocityX: 0,\n            velocityY: 0,\n            deltaX: 0,\n            deltaY: 0,\n            currentTime: 0,\n            data: undefined,\n            isScrolling: true,\n        };\n        this.color = undefined;\n        this.fullscreen = false;\n        this.forceOverscroll = undefined;\n        this.scrollX = false;\n        this.scrollY = true;\n        this.scrollEvents = false;\n    }\n    connectedCallback() {\n        this.isMainContent = this.el.closest('ion-menu, ion-popover, ion-modal') === null;\n        /**\n         * The fullscreen content offsets need to be\n         * computed after the tab bar has loaded. Since\n         * lazy evaluation means components are not hydrated\n         * at the same time, we need to wait for the ionTabBarLoaded\n         * event to fire. This does not impact dist-custom-elements\n         * because there is no hydration there.\n         */\n        if (hasLazyBuild(this.el)) {\n            /**\n             * We need to cache the reference to the tabs.\n             * If just the content is unmounted then we won't\n             * be able to query for the closest tabs on disconnectedCallback\n             * since the content has been removed from the DOM tree.\n             */\n            const closestTabs = (this.tabsElement = this.el.closest('ion-tabs'));\n            if (closestTabs !== null) {\n                /**\n                 * When adding and removing the event listener\n                 * we need to make sure we pass the same function reference\n                 * otherwise the event listener will not be removed properly.\n                 * We can't only pass `this.resize` because \"this\" in the function\n                 * context becomes a reference to IonTabs instead of IonContent.\n                 *\n                 * Additionally, we listen for ionTabBarLoaded on the IonTabs\n                 * instance rather than the IonTabBar instance. It's possible for\n                 * a tab bar to be conditionally rendered/mounted. Since ionTabBarLoaded\n                 * bubbles, we can catch any instances of child tab bars loading by listening\n                 * on IonTabs.\n                 */\n                this.tabsLoadCallback = () => this.resize();\n                closestTabs.addEventListener('ionTabBarLoaded', this.tabsLoadCallback);\n            }\n        }\n    }\n    disconnectedCallback() {\n        this.onScrollEnd();\n        if (hasLazyBuild(this.el)) {\n            /**\n             * The event listener and tabs caches need to\n             * be cleared otherwise this will create a memory\n             * leak where the IonTabs instance can never be\n             * garbage collected.\n             */\n            const { tabsElement, tabsLoadCallback } = this;\n            if (tabsElement !== null && tabsLoadCallback !== undefined) {\n                tabsElement.removeEventListener('ionTabBarLoaded', tabsLoadCallback);\n            }\n            this.tabsElement = null;\n            this.tabsLoadCallback = undefined;\n        }\n    }\n    /**\n     * Rotating certain devices can update\n     * the safe area insets. As a result,\n     * the fullscreen feature on ion-content\n     * needs to be recalculated.\n     *\n     * We listen for \"resize\" because we\n     * do not care what the orientation of\n     * the device is. Other APIs\n     * such as ScreenOrientation or\n     * the deviceorientation event must have\n     * permission from the user first whereas\n     * the \"resize\" event does not.\n     *\n     * We also throttle the callback to minimize\n     * thrashing when quickly resizing a window.\n     */\n    onResize() {\n        if (this.resizeTimeout) {\n            clearTimeout(this.resizeTimeout);\n            this.resizeTimeout = null;\n        }\n        this.resizeTimeout = setTimeout(() => {\n            /**\n             * Resize should only happen\n             * if the content is visible.\n             * When the content is hidden\n             * then offsetParent will be null.\n             */\n            if (this.el.offsetParent === null) {\n                return;\n            }\n            this.resize();\n        }, 100);\n    }\n    shouldForceOverscroll() {\n        const { forceOverscroll } = this;\n        const mode = getIonMode(this);\n        return forceOverscroll === undefined ? mode === 'ios' && isPlatform('ios') : forceOverscroll;\n    }\n    resize() {\n        /**\n         * Only force update if the component is rendered in a browser context.\n         * Using `forceUpdate` in a server context with pre-rendering can lead to an infinite loop.\n         * The `hydrateDocument` function in `@stencil/core` will render the `ion-content`, but\n         * `forceUpdate` will trigger another render, locking up the server.\n         *\n         * TODO: Remove if STENCIL-834 determines Stencil will account for this.\n         */\n        {\n            if (this.fullscreen) {\n                readTask(() => this.readDimensions());\n            }\n            else if (this.cTop !== 0 || this.cBottom !== 0) {\n                this.cTop = this.cBottom = 0;\n                forceUpdate(this);\n            }\n        }\n    }\n    readDimensions() {\n        const page = getPageElement(this.el);\n        const top = Math.max(this.el.offsetTop, 0);\n        const bottom = Math.max(page.offsetHeight - top - this.el.offsetHeight, 0);\n        const dirty = top !== this.cTop || bottom !== this.cBottom;\n        if (dirty) {\n            this.cTop = top;\n            this.cBottom = bottom;\n            forceUpdate(this);\n        }\n    }\n    onScroll(ev) {\n        const timeStamp = Date.now();\n        const shouldStart = !this.isScrolling;\n        this.lastScroll = timeStamp;\n        if (shouldStart) {\n            this.onScrollStart();\n        }\n        if (!this.queued && this.scrollEvents) {\n            this.queued = true;\n            readTask((ts) => {\n                this.queued = false;\n                this.detail.event = ev;\n                updateScrollDetail(this.detail, this.scrollEl, ts, shouldStart);\n                this.ionScroll.emit(this.detail);\n            });\n        }\n    }\n    /**\n     * Get the element where the actual scrolling takes place.\n     * This element can be used to subscribe to `scroll` events or manually modify\n     * `scrollTop`. However, it's recommended to use the API provided by `ion-content`:\n     *\n     * i.e. Using `ionScroll`, `ionScrollStart`, `ionScrollEnd` for scrolling events\n     * and `scrollToPoint()` to scroll the content into a certain point.\n     */\n    async getScrollElement() {\n        /**\n         * If this gets called in certain early lifecycle hooks (ex: Vue onMounted),\n         * scrollEl won't be defined yet with the custom elements build, so wait for it to load in.\n         */\n        if (!this.scrollEl) {\n            await new Promise((resolve) => componentOnReady(this.el, resolve));\n        }\n        return Promise.resolve(this.scrollEl);\n    }\n    /**\n     * Returns the background content element.\n     * @internal\n     */\n    async getBackgroundElement() {\n        if (!this.backgroundContentEl) {\n            await new Promise((resolve) => componentOnReady(this.el, resolve));\n        }\n        return Promise.resolve(this.backgroundContentEl);\n    }\n    /**\n     * Scroll to the top of the component.\n     *\n     * @param duration The amount of time to take scrolling to the top. Defaults to `0`.\n     */\n    scrollToTop(duration = 0) {\n        return this.scrollToPoint(undefined, 0, duration);\n    }\n    /**\n     * Scroll to the bottom of the component.\n     *\n     * @param duration The amount of time to take scrolling to the bottom. Defaults to `0`.\n     */\n    async scrollToBottom(duration = 0) {\n        const scrollEl = await this.getScrollElement();\n        const y = scrollEl.scrollHeight - scrollEl.clientHeight;\n        return this.scrollToPoint(undefined, y, duration);\n    }\n    /**\n     * Scroll by a specified X/Y distance in the component.\n     *\n     * @param x The amount to scroll by on the horizontal axis.\n     * @param y The amount to scroll by on the vertical axis.\n     * @param duration The amount of time to take scrolling by that amount.\n     */\n    async scrollByPoint(x, y, duration) {\n        const scrollEl = await this.getScrollElement();\n        return this.scrollToPoint(x + scrollEl.scrollLeft, y + scrollEl.scrollTop, duration);\n    }\n    /**\n     * Scroll to a specified X/Y location in the component.\n     *\n     * @param x The point to scroll to on the horizontal axis.\n     * @param y The point to scroll to on the vertical axis.\n     * @param duration The amount of time to take scrolling to that point. Defaults to `0`.\n     */\n    async scrollToPoint(x, y, duration = 0) {\n        const el = await this.getScrollElement();\n        if (duration < 32) {\n            if (y != null) {\n                el.scrollTop = y;\n            }\n            if (x != null) {\n                el.scrollLeft = x;\n            }\n            return;\n        }\n        let resolve;\n        let startTime = 0;\n        const promise = new Promise((r) => (resolve = r));\n        const fromY = el.scrollTop;\n        const fromX = el.scrollLeft;\n        const deltaY = y != null ? y - fromY : 0;\n        const deltaX = x != null ? x - fromX : 0;\n        // scroll loop\n        const step = (timeStamp) => {\n            const linearTime = Math.min(1, (timeStamp - startTime) / duration) - 1;\n            const easedT = Math.pow(linearTime, 3) + 1;\n            if (deltaY !== 0) {\n                el.scrollTop = Math.floor(easedT * deltaY + fromY);\n            }\n            if (deltaX !== 0) {\n                el.scrollLeft = Math.floor(easedT * deltaX + fromX);\n            }\n            if (easedT < 1) {\n                // do not use DomController here\n                // must use nativeRaf in order to fire in the next frame\n                requestAnimationFrame(step);\n            }\n            else {\n                resolve();\n            }\n        };\n        // chill out for a frame first\n        requestAnimationFrame((ts) => {\n            startTime = ts;\n            step(ts);\n        });\n        return promise;\n    }\n    onScrollStart() {\n        this.isScrolling = true;\n        this.ionScrollStart.emit({\n            isScrolling: true,\n        });\n        if (this.watchDog) {\n            clearInterval(this.watchDog);\n        }\n        // watchdog\n        this.watchDog = setInterval(() => {\n            if (this.lastScroll < Date.now() - 120) {\n                this.onScrollEnd();\n            }\n        }, 100);\n    }\n    onScrollEnd() {\n        if (this.watchDog)\n            clearInterval(this.watchDog);\n        this.watchDog = null;\n        if (this.isScrolling) {\n            this.isScrolling = false;\n            this.ionScrollEnd.emit({\n                isScrolling: false,\n            });\n        }\n    }\n    render() {\n        const { isMainContent, scrollX, scrollY, el } = this;\n        const rtl = isRTL(el) ? 'rtl' : 'ltr';\n        const mode = getIonMode(this);\n        const forceOverscroll = this.shouldForceOverscroll();\n        const transitionShadow = mode === 'ios';\n        const TagType = isMainContent ? 'main' : 'div';\n        this.resize();\n        return (h(Host, { key: 'e13815c0e6f6095150b112d3a1aaf2f509aa0d0b', class: createColorClasses(this.color, {\n                [mode]: true,\n                'content-sizing': hostContext('ion-popover', this.el),\n                overscroll: forceOverscroll,\n                [`content-${rtl}`]: true,\n            }), style: {\n                '--offset-top': `${this.cTop}px`,\n                '--offset-bottom': `${this.cBottom}px`,\n            } }, h(\"div\", { key: '8006c4a10d8f7dc83c646246961d018a8097236e', ref: (el) => (this.backgroundContentEl = el), id: \"background-content\", part: \"background\" }), h(TagType, { key: '4dd2f58421493f7a4ca42f8f5d7b85cda8e320ea', class: {\n                'inner-scroll': true,\n                'scroll-x': scrollX,\n                'scroll-y': scrollY,\n                overscroll: (scrollX || scrollY) && forceOverscroll,\n            }, ref: (scrollEl) => (this.scrollEl = scrollEl), onScroll: this.scrollEvents ? (ev) => this.onScroll(ev) : undefined, part: \"scroll\" }, h(\"slot\", { key: '37904f8f1d8319156cd901feb21930ef674fe0f7' })), transitionShadow ? (h(\"div\", { class: \"transition-effect\" }, h(\"div\", { class: \"transition-cover\" }), h(\"div\", { class: \"transition-shadow\" }))) : null, h(\"slot\", { key: '8f696583903af0548d064dca1a6bae060e127485', name: \"fixed\" })));\n    }\n    get el() { return getElement(this); }\n};\nconst getParentElement = (el) => {\n    var _a;\n    if (el.parentElement) {\n        // normal element with a parent element\n        return el.parentElement;\n    }\n    if ((_a = el.parentNode) === null || _a === void 0 ? void 0 : _a.host) {\n        // shadow dom's document fragment\n        return el.parentNode.host;\n    }\n    return null;\n};\nconst getPageElement = (el) => {\n    const tabs = el.closest('ion-tabs');\n    if (tabs) {\n        return tabs;\n    }\n    /**\n     * If we're in a popover, we need to use its wrapper so we can account for space\n     * between the popover and the edges of the screen. But if the popover contains\n     * its own page element, we should use that instead.\n     */\n    const page = el.closest('ion-app, ion-page, .ion-page, page-inner, .popover-content');\n    if (page) {\n        return page;\n    }\n    return getParentElement(el);\n};\n// ******** DOM READ ****************\nconst updateScrollDetail = (detail, el, timestamp, shouldStart) => {\n    const prevX = detail.currentX;\n    const prevY = detail.currentY;\n    const prevT = detail.currentTime;\n    const currentX = el.scrollLeft;\n    const currentY = el.scrollTop;\n    const timeDelta = timestamp - prevT;\n    if (shouldStart) {\n        // remember the start positions\n        detail.startTime = timestamp;\n        detail.startX = currentX;\n        detail.startY = currentY;\n        detail.velocityX = detail.velocityY = 0;\n    }\n    detail.currentTime = timestamp;\n    detail.currentX = detail.scrollLeft = currentX;\n    detail.currentY = detail.scrollTop = currentY;\n    detail.deltaX = currentX - detail.startX;\n    detail.deltaY = currentY - detail.startY;\n    if (timeDelta > 0 && timeDelta < 100) {\n        const velocityX = (currentX - prevX) / timeDelta;\n        const velocityY = (currentY - prevY) / timeDelta;\n        detail.velocityX = velocityX * 0.7 + detail.velocityX * 0.3;\n        detail.velocityY = velocityY * 0.7 + detail.velocityY * 0.3;\n    }\n};\nContent.style = IonContentStyle0;\n\nconst handleFooterFade = (scrollEl, baseEl) => {\n    readTask(() => {\n        const scrollTop = scrollEl.scrollTop;\n        const maxScroll = scrollEl.scrollHeight - scrollEl.clientHeight;\n        /**\n         * Toolbar background will fade\n         * out over fadeDuration in pixels.\n         */\n        const fadeDuration = 10;\n        /**\n         * Begin fading out maxScroll - 30px\n         * from the bottom of the content.\n         * Also determine how close we are\n         * to starting the fade. If we are\n         * before the starting point, the\n         * scale value will get clamped to 0.\n         * If we are after the maxScroll (rubber\n         * band scrolling), the scale value will\n         * get clamped to 1.\n         */\n        const fadeStart = maxScroll - fadeDuration;\n        const distanceToStart = scrollTop - fadeStart;\n        const scale = clamp(0, 1 - distanceToStart / fadeDuration, 1);\n        writeTask(() => {\n            baseEl.style.setProperty('--opacity-scale', scale.toString());\n        });\n    });\n};\n\nconst footerIosCss = \"ion-footer{display:block;position:relative;-ms-flex-order:1;order:1;width:100%;z-index:10}ion-footer.footer-toolbar-padding ion-toolbar:last-of-type{padding-bottom:var(--ion-safe-area-bottom, 0)}.footer-ios ion-toolbar:first-of-type{--border-width:0.55px 0 0}@supports ((-webkit-backdrop-filter: blur(0)) or (backdrop-filter: blur(0))){.footer-background{left:0;right:0;top:0;bottom:0;position:absolute;-webkit-backdrop-filter:saturate(180%) blur(20px);backdrop-filter:saturate(180%) blur(20px)}.footer-translucent-ios ion-toolbar{--opacity:.8}}.footer-ios.ion-no-border ion-toolbar:first-of-type{--border-width:0}.footer-collapse-fade ion-toolbar{--opacity-scale:inherit}\";\nconst IonFooterIosStyle0 = footerIosCss;\n\nconst footerMdCss = \"ion-footer{display:block;position:relative;-ms-flex-order:1;order:1;width:100%;z-index:10}ion-footer.footer-toolbar-padding ion-toolbar:last-of-type{padding-bottom:var(--ion-safe-area-bottom, 0)}.footer-md{-webkit-box-shadow:0 2px 4px -1px rgba(0, 0, 0, 0.2), 0 4px 5px 0 rgba(0, 0, 0, 0.14), 0 1px 10px 0 rgba(0, 0, 0, 0.12);box-shadow:0 2px 4px -1px rgba(0, 0, 0, 0.2), 0 4px 5px 0 rgba(0, 0, 0, 0.14), 0 1px 10px 0 rgba(0, 0, 0, 0.12)}.footer-md.ion-no-border{-webkit-box-shadow:none;box-shadow:none}\";\nconst IonFooterMdStyle0 = footerMdCss;\n\nconst Footer = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.keyboardCtrl = null;\n        this.checkCollapsibleFooter = () => {\n            const mode = getIonMode(this);\n            if (mode !== 'ios') {\n                return;\n            }\n            const { collapse } = this;\n            const hasFade = collapse === 'fade';\n            this.destroyCollapsibleFooter();\n            if (hasFade) {\n                const pageEl = this.el.closest('ion-app,ion-page,.ion-page,page-inner');\n                const contentEl = pageEl ? findIonContent(pageEl) : null;\n                if (!contentEl) {\n                    printIonContentErrorMsg(this.el);\n                    return;\n                }\n                this.setupFadeFooter(contentEl);\n            }\n        };\n        this.setupFadeFooter = async (contentEl) => {\n            const scrollEl = (this.scrollEl = await getScrollElement(contentEl));\n            /**\n             * Handle fading of toolbars on scroll\n             */\n            this.contentScrollCallback = () => {\n                handleFooterFade(scrollEl, this.el);\n            };\n            scrollEl.addEventListener('scroll', this.contentScrollCallback);\n            handleFooterFade(scrollEl, this.el);\n        };\n        this.keyboardVisible = false;\n        this.collapse = undefined;\n        this.translucent = false;\n    }\n    componentDidLoad() {\n        this.checkCollapsibleFooter();\n    }\n    componentDidUpdate() {\n        this.checkCollapsibleFooter();\n    }\n    async connectedCallback() {\n        this.keyboardCtrl = await createKeyboardController(async (keyboardOpen, waitForResize) => {\n            /**\n             * If the keyboard is hiding, then we need to wait\n             * for the webview to resize. Otherwise, the footer\n             * will flicker before the webview resizes.\n             */\n            if (keyboardOpen === false && waitForResize !== undefined) {\n                await waitForResize;\n            }\n            this.keyboardVisible = keyboardOpen; // trigger re-render by updating state\n        });\n    }\n    disconnectedCallback() {\n        if (this.keyboardCtrl) {\n            this.keyboardCtrl.destroy();\n        }\n    }\n    destroyCollapsibleFooter() {\n        if (this.scrollEl && this.contentScrollCallback) {\n            this.scrollEl.removeEventListener('scroll', this.contentScrollCallback);\n            this.contentScrollCallback = undefined;\n        }\n    }\n    render() {\n        const { translucent, collapse } = this;\n        const mode = getIonMode(this);\n        const tabs = this.el.closest('ion-tabs');\n        const tabBar = tabs === null || tabs === void 0 ? void 0 : tabs.querySelector(':scope > ion-tab-bar');\n        return (h(Host, { key: 'dd8fa96901e8a09759a9621b6513f0492b3a6197', role: \"contentinfo\", class: {\n                [mode]: true,\n                // Used internally for styling\n                [`footer-${mode}`]: true,\n                [`footer-translucent`]: translucent,\n                [`footer-translucent-${mode}`]: translucent,\n                ['footer-toolbar-padding']: !this.keyboardVisible && (!tabBar || tabBar.slot !== 'bottom'),\n                [`footer-collapse-${collapse}`]: collapse !== undefined,\n            } }, mode === 'ios' && translucent && h(\"div\", { key: '0fbb4ebf8e3951ff399f843dc11aab37fc48f8b7', class: \"footer-background\" }), h(\"slot\", { key: 'ecb14a65e3b6960670446c4428e3095b3231a3b0' })));\n    }\n    get el() { return getElement(this); }\n};\nFooter.style = {\n    ios: IonFooterIosStyle0,\n    md: IonFooterMdStyle0\n};\n\nconst TRANSITION = 'all 0.2s ease-in-out';\nconst cloneElement = (tagName) => {\n    const getCachedEl = document.querySelector(`${tagName}.ion-cloned-element`);\n    if (getCachedEl !== null) {\n        return getCachedEl;\n    }\n    const clonedEl = document.createElement(tagName);\n    clonedEl.classList.add('ion-cloned-element');\n    clonedEl.style.setProperty('display', 'none');\n    document.body.appendChild(clonedEl);\n    return clonedEl;\n};\nconst createHeaderIndex = (headerEl) => {\n    if (!headerEl) {\n        return;\n    }\n    const toolbars = headerEl.querySelectorAll('ion-toolbar');\n    return {\n        el: headerEl,\n        toolbars: Array.from(toolbars).map((toolbar) => {\n            const ionTitleEl = toolbar.querySelector('ion-title');\n            return {\n                el: toolbar,\n                background: toolbar.shadowRoot.querySelector('.toolbar-background'),\n                ionTitleEl,\n                innerTitleEl: ionTitleEl ? ionTitleEl.shadowRoot.querySelector('.toolbar-title') : null,\n                ionButtonsEl: Array.from(toolbar.querySelectorAll('ion-buttons')),\n            };\n        }),\n    };\n};\nconst handleContentScroll = (scrollEl, scrollHeaderIndex, contentEl) => {\n    readTask(() => {\n        const scrollTop = scrollEl.scrollTop;\n        const scale = clamp(1, 1 + -scrollTop / 500, 1.1);\n        // Native refresher should not cause titles to scale\n        const nativeRefresher = contentEl.querySelector('ion-refresher.refresher-native');\n        if (nativeRefresher === null) {\n            writeTask(() => {\n                scaleLargeTitles(scrollHeaderIndex.toolbars, scale);\n            });\n        }\n    });\n};\nconst setToolbarBackgroundOpacity = (headerEl, opacity) => {\n    /**\n     * Fading in the backdrop opacity\n     * should happen after the large title\n     * has collapsed, so it is handled\n     * by handleHeaderFade()\n     */\n    if (headerEl.collapse === 'fade') {\n        return;\n    }\n    if (opacity === undefined) {\n        headerEl.style.removeProperty('--opacity-scale');\n    }\n    else {\n        headerEl.style.setProperty('--opacity-scale', opacity.toString());\n    }\n};\nconst handleToolbarBorderIntersection = (ev, mainHeaderIndex, scrollTop) => {\n    if (!ev[0].isIntersecting) {\n        return;\n    }\n    /**\n     * There is a bug in Safari where overflow scrolling on a non-body element\n     * does not always reset the scrollTop position to 0 when letting go. It will\n     * set to 1 once the rubber band effect has ended. This causes the background to\n     * appear slightly on certain app setups.\n     *\n     * Additionally, we check if user is rubber banding (scrolling is negative)\n     * as this can mean they are using pull to refresh. Once the refresher starts,\n     * the content is transformed which can cause the intersection observer to erroneously\n     * fire here as well.\n     */\n    const scale = ev[0].intersectionRatio > 0.9 || scrollTop <= 0 ? 0 : ((1 - ev[0].intersectionRatio) * 100) / 75;\n    setToolbarBackgroundOpacity(mainHeaderIndex.el, scale === 1 ? undefined : scale);\n};\n/**\n * If toolbars are intersecting, hide the scrollable toolbar content\n * and show the primary toolbar content. If the toolbars are not intersecting,\n * hide the primary toolbar content and show the scrollable toolbar content\n */\nconst handleToolbarIntersection = (ev, // TODO(FW-2832): type (IntersectionObserverEntry[] triggers errors which should be sorted)\nmainHeaderIndex, scrollHeaderIndex, scrollEl) => {\n    writeTask(() => {\n        const scrollTop = scrollEl.scrollTop;\n        handleToolbarBorderIntersection(ev, mainHeaderIndex, scrollTop);\n        const event = ev[0];\n        const intersection = event.intersectionRect;\n        const intersectionArea = intersection.width * intersection.height;\n        const rootArea = event.rootBounds.width * event.rootBounds.height;\n        const isPageHidden = intersectionArea === 0 && rootArea === 0;\n        const leftDiff = Math.abs(intersection.left - event.boundingClientRect.left);\n        const rightDiff = Math.abs(intersection.right - event.boundingClientRect.right);\n        const isPageTransitioning = intersectionArea > 0 && (leftDiff >= 5 || rightDiff >= 5);\n        if (isPageHidden || isPageTransitioning) {\n            return;\n        }\n        if (event.isIntersecting) {\n            setHeaderActive(mainHeaderIndex, false);\n            setHeaderActive(scrollHeaderIndex);\n        }\n        else {\n            /**\n             * There is a bug with IntersectionObserver on Safari\n             * where `event.isIntersecting === false` when cancelling\n             * a swipe to go back gesture. Checking the intersection\n             * x, y, width, and height provides a workaround. This bug\n             * does not happen when using Safari + Web Animations,\n             * only Safari + CSS Animations.\n             */\n            const hasValidIntersection = (intersection.x === 0 && intersection.y === 0) || (intersection.width !== 0 && intersection.height !== 0);\n            if (hasValidIntersection && scrollTop > 0) {\n                setHeaderActive(mainHeaderIndex);\n                setHeaderActive(scrollHeaderIndex, false);\n                setToolbarBackgroundOpacity(mainHeaderIndex.el);\n            }\n        }\n    });\n};\nconst setHeaderActive = (headerIndex, active = true) => {\n    const headerEl = headerIndex.el;\n    if (active) {\n        headerEl.classList.remove('header-collapse-condense-inactive');\n        headerEl.removeAttribute('aria-hidden');\n    }\n    else {\n        headerEl.classList.add('header-collapse-condense-inactive');\n        headerEl.setAttribute('aria-hidden', 'true');\n    }\n};\nconst scaleLargeTitles = (toolbars = [], scale = 1, transition = false) => {\n    toolbars.forEach((toolbar) => {\n        const ionTitle = toolbar.ionTitleEl;\n        const titleDiv = toolbar.innerTitleEl;\n        if (!ionTitle || ionTitle.size !== 'large') {\n            return;\n        }\n        titleDiv.style.transition = transition ? TRANSITION : '';\n        titleDiv.style.transform = `scale3d(${scale}, ${scale}, 1)`;\n    });\n};\nconst handleHeaderFade = (scrollEl, baseEl, condenseHeader) => {\n    readTask(() => {\n        const scrollTop = scrollEl.scrollTop;\n        const baseElHeight = baseEl.clientHeight;\n        const fadeStart = condenseHeader ? condenseHeader.clientHeight : 0;\n        /**\n         * If we are using fade header with a condense\n         * header, then the toolbar backgrounds should\n         * not begin to fade in until the condense\n         * header has fully collapsed.\n         *\n         * Additionally, the main content should not\n         * overflow out of the container until the\n         * condense header has fully collapsed. When\n         * using just the condense header the content\n         * should overflow out of the container.\n         */\n        if (condenseHeader !== null && scrollTop < fadeStart) {\n            baseEl.style.setProperty('--opacity-scale', '0');\n            scrollEl.style.setProperty('clip-path', `inset(${baseElHeight}px 0px 0px 0px)`);\n            return;\n        }\n        const distanceToStart = scrollTop - fadeStart;\n        const fadeDuration = 10;\n        const scale = clamp(0, distanceToStart / fadeDuration, 1);\n        writeTask(() => {\n            scrollEl.style.removeProperty('clip-path');\n            baseEl.style.setProperty('--opacity-scale', scale.toString());\n        });\n    });\n};\n\nconst headerIosCss = \"ion-header{display:block;position:relative;-ms-flex-order:-1;order:-1;width:100%;z-index:10}ion-header ion-toolbar:first-of-type{padding-top:var(--ion-safe-area-top, 0)}.header-ios ion-toolbar:last-of-type{--border-width:0 0 0.55px}@supports ((-webkit-backdrop-filter: blur(0)) or (backdrop-filter: blur(0))){.header-background{left:0;right:0;top:0;bottom:0;position:absolute;-webkit-backdrop-filter:saturate(180%) blur(20px);backdrop-filter:saturate(180%) blur(20px)}.header-translucent-ios ion-toolbar{--opacity:.8}.header-collapse-condense-inactive .header-background{-webkit-backdrop-filter:blur(20px);backdrop-filter:blur(20px)}}.header-ios.ion-no-border ion-toolbar:last-of-type{--border-width:0}.header-collapse-fade ion-toolbar{--opacity-scale:inherit}.header-collapse-condense{z-index:9}.header-collapse-condense ion-toolbar{position:-webkit-sticky;position:sticky;top:0}.header-collapse-condense ion-toolbar:first-of-type{padding-top:0px;z-index:1}.header-collapse-condense ion-toolbar{--background:var(--ion-background-color, #fff);z-index:0}.header-collapse-condense ion-toolbar:last-of-type{--border-width:0px}.header-collapse-condense ion-toolbar ion-searchbar{padding-top:0px;padding-bottom:13px}.header-collapse-main{--opacity-scale:1}.header-collapse-main ion-toolbar{--opacity-scale:inherit}.header-collapse-main ion-toolbar.in-toolbar ion-title,.header-collapse-main ion-toolbar.in-toolbar ion-buttons{-webkit-transition:all 0.2s ease-in-out;transition:all 0.2s ease-in-out}.header-collapse-condense-inactive:not(.header-collapse-condense) ion-toolbar.in-toolbar ion-title,.header-collapse-condense-inactive:not(.header-collapse-condense) ion-toolbar.in-toolbar ion-buttons.buttons-collapse{opacity:0;pointer-events:none}.header-collapse-condense-inactive.header-collapse-condense ion-toolbar.in-toolbar ion-title,.header-collapse-condense-inactive.header-collapse-condense ion-toolbar.in-toolbar ion-buttons.buttons-collapse{visibility:hidden}ion-header.header-ios:not(.header-collapse-main):has(~ion-content ion-header.header-ios[collapse=condense],~ion-content ion-header.header-ios.header-collapse-condense){opacity:0}\";\nconst IonHeaderIosStyle0 = headerIosCss;\n\nconst headerMdCss = \"ion-header{display:block;position:relative;-ms-flex-order:-1;order:-1;width:100%;z-index:10}ion-header ion-toolbar:first-of-type{padding-top:var(--ion-safe-area-top, 0)}.header-md{-webkit-box-shadow:0 2px 4px -1px rgba(0, 0, 0, 0.2), 0 4px 5px 0 rgba(0, 0, 0, 0.14), 0 1px 10px 0 rgba(0, 0, 0, 0.12);box-shadow:0 2px 4px -1px rgba(0, 0, 0, 0.2), 0 4px 5px 0 rgba(0, 0, 0, 0.14), 0 1px 10px 0 rgba(0, 0, 0, 0.12)}.header-collapse-condense{display:none}.header-md.ion-no-border{-webkit-box-shadow:none;box-shadow:none}\";\nconst IonHeaderMdStyle0 = headerMdCss;\n\nconst Header = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.inheritedAttributes = {};\n        this.setupFadeHeader = async (contentEl, condenseHeader) => {\n            const scrollEl = (this.scrollEl = await getScrollElement(contentEl));\n            /**\n             * Handle fading of toolbars on scroll\n             */\n            this.contentScrollCallback = () => {\n                handleHeaderFade(this.scrollEl, this.el, condenseHeader);\n            };\n            scrollEl.addEventListener('scroll', this.contentScrollCallback);\n            handleHeaderFade(this.scrollEl, this.el, condenseHeader);\n        };\n        this.collapse = undefined;\n        this.translucent = false;\n    }\n    componentWillLoad() {\n        this.inheritedAttributes = inheritAriaAttributes(this.el);\n    }\n    componentDidLoad() {\n        this.checkCollapsibleHeader();\n    }\n    componentDidUpdate() {\n        this.checkCollapsibleHeader();\n    }\n    disconnectedCallback() {\n        this.destroyCollapsibleHeader();\n    }\n    async checkCollapsibleHeader() {\n        const mode = getIonMode(this);\n        if (mode !== 'ios') {\n            return;\n        }\n        const { collapse } = this;\n        const hasCondense = collapse === 'condense';\n        const hasFade = collapse === 'fade';\n        this.destroyCollapsibleHeader();\n        if (hasCondense) {\n            const pageEl = this.el.closest('ion-app,ion-page,.ion-page,page-inner');\n            const contentEl = pageEl ? findIonContent(pageEl) : null;\n            // Cloned elements are always needed in iOS transition\n            writeTask(() => {\n                const title = cloneElement('ion-title');\n                title.size = 'large';\n                cloneElement('ion-back-button');\n            });\n            await this.setupCondenseHeader(contentEl, pageEl);\n        }\n        else if (hasFade) {\n            const pageEl = this.el.closest('ion-app,ion-page,.ion-page,page-inner');\n            const contentEl = pageEl ? findIonContent(pageEl) : null;\n            if (!contentEl) {\n                printIonContentErrorMsg(this.el);\n                return;\n            }\n            const condenseHeader = contentEl.querySelector('ion-header[collapse=\"condense\"]');\n            await this.setupFadeHeader(contentEl, condenseHeader);\n        }\n    }\n    destroyCollapsibleHeader() {\n        if (this.intersectionObserver) {\n            this.intersectionObserver.disconnect();\n            this.intersectionObserver = undefined;\n        }\n        if (this.scrollEl && this.contentScrollCallback) {\n            this.scrollEl.removeEventListener('scroll', this.contentScrollCallback);\n            this.contentScrollCallback = undefined;\n        }\n        if (this.collapsibleMainHeader) {\n            this.collapsibleMainHeader.classList.remove('header-collapse-main');\n            this.collapsibleMainHeader = undefined;\n        }\n    }\n    async setupCondenseHeader(contentEl, pageEl) {\n        if (!contentEl || !pageEl) {\n            printIonContentErrorMsg(this.el);\n            return;\n        }\n        if (typeof IntersectionObserver === 'undefined') {\n            return;\n        }\n        this.scrollEl = await getScrollElement(contentEl);\n        const headers = pageEl.querySelectorAll('ion-header');\n        this.collapsibleMainHeader = Array.from(headers).find((header) => header.collapse !== 'condense');\n        if (!this.collapsibleMainHeader) {\n            return;\n        }\n        const mainHeaderIndex = createHeaderIndex(this.collapsibleMainHeader);\n        const scrollHeaderIndex = createHeaderIndex(this.el);\n        if (!mainHeaderIndex || !scrollHeaderIndex) {\n            return;\n        }\n        setHeaderActive(mainHeaderIndex, false);\n        setToolbarBackgroundOpacity(mainHeaderIndex.el, 0);\n        /**\n         * Handle interaction between toolbar collapse and\n         * showing/hiding content in the primary ion-header\n         * as well as progressively showing/hiding the main header\n         * border as the top-most toolbar collapses or expands.\n         */\n        const toolbarIntersection = (ev) => {\n            handleToolbarIntersection(ev, mainHeaderIndex, scrollHeaderIndex, this.scrollEl);\n        };\n        this.intersectionObserver = new IntersectionObserver(toolbarIntersection, {\n            root: contentEl,\n            threshold: [0.25, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9, 1],\n        });\n        this.intersectionObserver.observe(scrollHeaderIndex.toolbars[scrollHeaderIndex.toolbars.length - 1].el);\n        /**\n         * Handle scaling of large iOS titles and\n         * showing/hiding border on last toolbar\n         * in primary header\n         */\n        this.contentScrollCallback = () => {\n            handleContentScroll(this.scrollEl, scrollHeaderIndex, contentEl);\n        };\n        this.scrollEl.addEventListener('scroll', this.contentScrollCallback);\n        writeTask(() => {\n            if (this.collapsibleMainHeader !== undefined) {\n                this.collapsibleMainHeader.classList.add('header-collapse-main');\n            }\n        });\n    }\n    render() {\n        const { translucent, inheritedAttributes } = this;\n        const mode = getIonMode(this);\n        const collapse = this.collapse || 'none';\n        // banner role must be at top level, so remove role if inside a menu\n        const roleType = hostContext('ion-menu', this.el) ? 'none' : 'banner';\n        return (h(Host, Object.assign({ key: '9fa0af97b605f9fe98b13361bc3d1289745c549f', role: roleType, class: {\n                [mode]: true,\n                // Used internally for styling\n                [`header-${mode}`]: true,\n                [`header-translucent`]: this.translucent,\n                [`header-collapse-${collapse}`]: true,\n                [`header-translucent-${mode}`]: this.translucent,\n            } }, inheritedAttributes), mode === 'ios' && translucent && h(\"div\", { key: '1a780d2625302f2465718e304bdd3794c89c9845', class: \"header-background\" }), h(\"slot\", { key: 'b2b8557b44be40c590bfcc362ac4350f9f8b889e' })));\n    }\n    get el() { return getElement(this); }\n};\nHeader.style = {\n    ios: IonHeaderIosStyle0,\n    md: IonHeaderMdStyle0\n};\n\nconst routerOutletCss = \":host{left:0;right:0;top:0;bottom:0;position:absolute;contain:layout size style;z-index:0}\";\nconst IonRouterOutletStyle0 = routerOutletCss;\n\nconst RouterOutlet = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.ionNavWillLoad = createEvent(this, \"ionNavWillLoad\", 7);\n        this.ionNavWillChange = createEvent(this, \"ionNavWillChange\", 3);\n        this.ionNavDidChange = createEvent(this, \"ionNavDidChange\", 3);\n        this.lockController = createLockController();\n        this.gestureOrAnimationInProgress = false;\n        this.mode = getIonMode(this);\n        this.delegate = undefined;\n        this.animated = true;\n        this.animation = undefined;\n        this.swipeHandler = undefined;\n    }\n    swipeHandlerChanged() {\n        if (this.gesture) {\n            this.gesture.enable(this.swipeHandler !== undefined);\n        }\n    }\n    async connectedCallback() {\n        const onStart = () => {\n            this.gestureOrAnimationInProgress = true;\n            if (this.swipeHandler) {\n                this.swipeHandler.onStart();\n            }\n        };\n        this.gesture = (await import('./swipe-back-37a22d34.js')).createSwipeBackGesture(this.el, () => !this.gestureOrAnimationInProgress && !!this.swipeHandler && this.swipeHandler.canStart(), () => onStart(), (step) => { var _a; return (_a = this.ani) === null || _a === void 0 ? void 0 : _a.progressStep(step); }, (shouldComplete, step, dur) => {\n            if (this.ani) {\n                this.ani.onFinish(() => {\n                    this.gestureOrAnimationInProgress = false;\n                    if (this.swipeHandler) {\n                        this.swipeHandler.onEnd(shouldComplete);\n                    }\n                }, { oneTimeCallback: true });\n                // Account for rounding errors in JS\n                let newStepValue = shouldComplete ? -0.001 : 0.001;\n                /**\n                 * Animation will be reversed here, so need to\n                 * reverse the easing curve as well\n                 *\n                 * Additionally, we need to account for the time relative\n                 * to the new easing curve, as `stepValue` is going to be given\n                 * in terms of a linear curve.\n                 */\n                if (!shouldComplete) {\n                    this.ani.easing('cubic-bezier(1, 0, 0.68, 0.28)');\n                    newStepValue += getTimeGivenProgression([0, 0], [1, 0], [0.68, 0.28], [1, 1], step)[0];\n                }\n                else {\n                    newStepValue += getTimeGivenProgression([0, 0], [0.32, 0.72], [0, 1], [1, 1], step)[0];\n                }\n                this.ani.progressEnd(shouldComplete ? 1 : 0, newStepValue, dur);\n            }\n            else {\n                this.gestureOrAnimationInProgress = false;\n            }\n        });\n        this.swipeHandlerChanged();\n    }\n    componentWillLoad() {\n        this.ionNavWillLoad.emit();\n    }\n    disconnectedCallback() {\n        if (this.gesture) {\n            this.gesture.destroy();\n            this.gesture = undefined;\n        }\n    }\n    /** @internal */\n    async commit(enteringEl, leavingEl, opts) {\n        const unlock = await this.lockController.lock();\n        let changed = false;\n        try {\n            changed = await this.transition(enteringEl, leavingEl, opts);\n        }\n        catch (e) {\n            console.error(e);\n        }\n        unlock();\n        return changed;\n    }\n    /** @internal */\n    async setRouteId(id, params, direction, animation) {\n        const changed = await this.setRoot(id, params, {\n            duration: direction === 'root' ? 0 : undefined,\n            direction: direction === 'back' ? 'back' : 'forward',\n            animationBuilder: animation,\n        });\n        return {\n            changed,\n            element: this.activeEl,\n        };\n    }\n    /** @internal */\n    async getRouteId() {\n        const active = this.activeEl;\n        return active\n            ? {\n                id: active.tagName,\n                element: active,\n                params: this.activeParams,\n            }\n            : undefined;\n    }\n    async setRoot(component, params, opts) {\n        if (this.activeComponent === component && shallowEqualStringMap(params, this.activeParams)) {\n            return false;\n        }\n        // attach entering view to DOM\n        const leavingEl = this.activeEl;\n        const enteringEl = await attachComponent(this.delegate, this.el, component, ['ion-page', 'ion-page-invisible'], params);\n        this.activeComponent = component;\n        this.activeEl = enteringEl;\n        this.activeParams = params;\n        // commit animation\n        await this.commit(enteringEl, leavingEl, opts);\n        await detachComponent(this.delegate, leavingEl);\n        return true;\n    }\n    async transition(enteringEl, leavingEl, opts = {}) {\n        if (leavingEl === enteringEl) {\n            return false;\n        }\n        // emit nav will change event\n        this.ionNavWillChange.emit();\n        const { el, mode } = this;\n        const animated = this.animated && config.getBoolean('animated', true);\n        const animationBuilder = opts.animationBuilder || this.animation || config.get('navAnimation');\n        await transition(Object.assign(Object.assign({ mode,\n            animated,\n            enteringEl,\n            leavingEl, baseEl: el,\n            /**\n             * We need to wait for all Stencil components\n             * to be ready only when using the lazy\n             * loaded bundle.\n             */\n            deepWait: hasLazyBuild(el), progressCallback: opts.progressAnimation\n                ? (ani) => {\n                    /**\n                     * Because this progress callback is called asynchronously\n                     * it is possible for the gesture to start and end before\n                     * the animation is ever set. In that scenario, we should\n                     * immediately call progressEnd so that the transition promise\n                     * resolves and the gesture does not get locked up.\n                     */\n                    if (ani !== undefined && !this.gestureOrAnimationInProgress) {\n                        this.gestureOrAnimationInProgress = true;\n                        ani.onFinish(() => {\n                            this.gestureOrAnimationInProgress = false;\n                            if (this.swipeHandler) {\n                                this.swipeHandler.onEnd(false);\n                            }\n                        }, { oneTimeCallback: true });\n                        /**\n                         * Playing animation to beginning\n                         * with a duration of 0 prevents\n                         * any flickering when the animation\n                         * is later cleaned up.\n                         */\n                        ani.progressEnd(0, 0, 0);\n                    }\n                    else {\n                        this.ani = ani;\n                    }\n                }\n                : undefined }, opts), { animationBuilder }));\n        // emit nav changed event\n        this.ionNavDidChange.emit();\n        return true;\n    }\n    render() {\n        return h(\"slot\", { key: '0949db1bcfde67b462abe9cae72c7a7fd70ea678' });\n    }\n    get el() { return getElement(this); }\n    static get watchers() { return {\n        \"swipeHandler\": [\"swipeHandlerChanged\"]\n    }; }\n};\nRouterOutlet.style = IonRouterOutletStyle0;\n\nconst titleIosCss = \":host{--color:initial;display:-ms-flexbox;display:flex;-ms-flex:1;flex:1;-ms-flex-align:center;align-items:center;-webkit-transform:translateZ(0);transform:translateZ(0);color:var(--color)}:host(.ion-color){color:var(--ion-color-base)}.toolbar-title{display:block;width:100%;text-overflow:ellipsis;white-space:nowrap;overflow:hidden;pointer-events:auto}:host(.title-small) .toolbar-title{white-space:normal}:host{top:0;-webkit-padding-start:90px;padding-inline-start:90px;-webkit-padding-end:90px;padding-inline-end:90px;padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);position:absolute;width:100%;height:100%;-webkit-transform:translateZ(0);transform:translateZ(0);font-size:min(1.0625rem, 20.4px);font-weight:600;text-align:center;-webkit-box-sizing:border-box;box-sizing:border-box;pointer-events:none}@supports (inset-inline-start: 0){:host{inset-inline-start:0}}@supports not (inset-inline-start: 0){:host{left:0}:host-context([dir=rtl]){left:unset;right:unset;right:0}@supports selector(:dir(rtl)){:host(:dir(rtl)){left:unset;right:unset;right:0}}}:host(.title-small){-webkit-padding-start:9px;padding-inline-start:9px;-webkit-padding-end:9px;padding-inline-end:9px;padding-top:6px;padding-bottom:16px;position:relative;font-size:min(0.8125rem, 23.4px);font-weight:normal}:host(.title-large){-webkit-padding-start:12px;padding-inline-start:12px;-webkit-padding-end:12px;padding-inline-end:12px;padding-top:2px;padding-bottom:4px;-webkit-transform-origin:left center;transform-origin:left center;position:static;-ms-flex-align:end;align-items:flex-end;min-width:100%;font-size:min(2.125rem, 61.2px);font-weight:700;text-align:start}:host(.title-large.title-rtl){-webkit-transform-origin:right center;transform-origin:right center}:host(.title-large.ion-cloned-element){--color:var(--ion-text-color, #000);font-family:var(--ion-font-family)}:host(.title-large) .toolbar-title{-webkit-transform-origin:inherit;transform-origin:inherit;width:auto}:host-context([dir=rtl]):host(.title-large) .toolbar-title,:host-context([dir=rtl]).title-large .toolbar-title{-webkit-transform-origin:calc(100% - inherit);transform-origin:calc(100% - inherit)}@supports selector(:dir(rtl)){:host(.title-large:dir(rtl)) .toolbar-title{-webkit-transform-origin:calc(100% - inherit);transform-origin:calc(100% - inherit)}}\";\nconst IonTitleIosStyle0 = titleIosCss;\n\nconst titleMdCss = \":host{--color:initial;display:-ms-flexbox;display:flex;-ms-flex:1;flex:1;-ms-flex-align:center;align-items:center;-webkit-transform:translateZ(0);transform:translateZ(0);color:var(--color)}:host(.ion-color){color:var(--ion-color-base)}.toolbar-title{display:block;width:100%;text-overflow:ellipsis;white-space:nowrap;overflow:hidden;pointer-events:auto}:host(.title-small) .toolbar-title{white-space:normal}:host{-webkit-padding-start:20px;padding-inline-start:20px;-webkit-padding-end:20px;padding-inline-end:20px;padding-top:0;padding-bottom:0;font-size:1.25rem;font-weight:500;letter-spacing:0.0125em}:host(.title-small){width:100%;height:100%;font-size:0.9375rem;font-weight:normal}\";\nconst IonTitleMdStyle0 = titleMdCss;\n\nconst ToolbarTitle = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.ionStyle = createEvent(this, \"ionStyle\", 7);\n        this.color = undefined;\n        this.size = undefined;\n    }\n    sizeChanged() {\n        this.emitStyle();\n    }\n    connectedCallback() {\n        this.emitStyle();\n    }\n    emitStyle() {\n        const size = this.getSize();\n        this.ionStyle.emit({\n            [`title-${size}`]: true,\n        });\n    }\n    getSize() {\n        return this.size !== undefined ? this.size : 'default';\n    }\n    render() {\n        const mode = getIonMode(this);\n        const size = this.getSize();\n        return (h(Host, { key: '6f43362b782ef7d340c241bb66f1469663c03cc1', class: createColorClasses(this.color, {\n                [mode]: true,\n                [`title-${size}`]: true,\n                'title-rtl': document.dir === 'rtl',\n            }) }, h(\"div\", { key: '9c3ff1a289e533ee3426b71ab5560fbea3529502', class: \"toolbar-title\" }, h(\"slot\", { key: '50d5cc5a1519ad58f1994d2f8c8f08f62baac1fe' }))));\n    }\n    get el() { return getElement(this); }\n    static get watchers() { return {\n        \"size\": [\"sizeChanged\"]\n    }; }\n};\nToolbarTitle.style = {\n    ios: IonTitleIosStyle0,\n    md: IonTitleMdStyle0\n};\n\nconst toolbarIosCss = \":host{--border-width:0;--border-style:solid;--opacity:1;--opacity-scale:1;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;display:block;position:relative;width:100%;padding-right:var(--ion-safe-area-right);padding-left:var(--ion-safe-area-left);color:var(--color);font-family:var(--ion-font-family, inherit);contain:content;z-index:10;-webkit-box-sizing:border-box;box-sizing:border-box}:host(.ion-color){color:var(--ion-color-contrast)}:host(.ion-color) .toolbar-background{background:var(--ion-color-base)}.toolbar-container{-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);display:-ms-flexbox;display:flex;position:relative;-ms-flex-direction:row;flex-direction:row;-ms-flex-align:center;align-items:center;-ms-flex-pack:justify;justify-content:space-between;width:100%;min-height:var(--min-height);contain:content;overflow:hidden;z-index:10;-webkit-box-sizing:border-box;box-sizing:border-box}.toolbar-background{left:0;right:0;top:0;bottom:0;position:absolute;-webkit-transform:translateZ(0);transform:translateZ(0);border-width:var(--border-width);border-style:var(--border-style);border-color:var(--border-color);background:var(--background);contain:strict;opacity:calc(var(--opacity) * var(--opacity-scale));z-index:-1;pointer-events:none}::slotted(ion-progress-bar){left:0;right:0;bottom:0;position:absolute}:host{--background:var(--ion-toolbar-background, var(--ion-color-step-50, #f7f7f7));--color:var(--ion-toolbar-color, var(--ion-text-color, #000));--border-color:var(--ion-toolbar-border-color, var(--ion-border-color, var(--ion-color-step-150, rgba(0, 0, 0, 0.2))));--padding-top:3px;--padding-bottom:3px;--padding-start:4px;--padding-end:4px;--min-height:44px}.toolbar-content{-ms-flex:1;flex:1;-ms-flex-order:4;order:4;min-width:0}:host(.toolbar-segment) .toolbar-content{display:-ms-inline-flexbox;display:inline-flex}:host(.toolbar-searchbar) .toolbar-container{padding-top:0;padding-bottom:0}:host(.toolbar-searchbar) ::slotted(*){-ms-flex-item-align:start;align-self:start}:host(.toolbar-searchbar) ::slotted(ion-chip){margin-top:3px}::slotted(ion-buttons){min-height:38px}::slotted([slot=start]){-ms-flex-order:2;order:2}::slotted([slot=secondary]){-ms-flex-order:3;order:3}::slotted([slot=primary]){-ms-flex-order:5;order:5;text-align:end}::slotted([slot=end]){-ms-flex-order:6;order:6;text-align:end}:host(.toolbar-title-large) .toolbar-container{-ms-flex-wrap:wrap;flex-wrap:wrap;-ms-flex-align:start;align-items:flex-start}:host(.toolbar-title-large) .toolbar-content ion-title{-ms-flex:1;flex:1;-ms-flex-order:8;order:8;min-width:100%}\";\nconst IonToolbarIosStyle0 = toolbarIosCss;\n\nconst toolbarMdCss = \":host{--border-width:0;--border-style:solid;--opacity:1;--opacity-scale:1;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;display:block;position:relative;width:100%;padding-right:var(--ion-safe-area-right);padding-left:var(--ion-safe-area-left);color:var(--color);font-family:var(--ion-font-family, inherit);contain:content;z-index:10;-webkit-box-sizing:border-box;box-sizing:border-box}:host(.ion-color){color:var(--ion-color-contrast)}:host(.ion-color) .toolbar-background{background:var(--ion-color-base)}.toolbar-container{-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);display:-ms-flexbox;display:flex;position:relative;-ms-flex-direction:row;flex-direction:row;-ms-flex-align:center;align-items:center;-ms-flex-pack:justify;justify-content:space-between;width:100%;min-height:var(--min-height);contain:content;overflow:hidden;z-index:10;-webkit-box-sizing:border-box;box-sizing:border-box}.toolbar-background{left:0;right:0;top:0;bottom:0;position:absolute;-webkit-transform:translateZ(0);transform:translateZ(0);border-width:var(--border-width);border-style:var(--border-style);border-color:var(--border-color);background:var(--background);contain:strict;opacity:calc(var(--opacity) * var(--opacity-scale));z-index:-1;pointer-events:none}::slotted(ion-progress-bar){left:0;right:0;bottom:0;position:absolute}:host{--background:var(--ion-toolbar-background, var(--ion-background-color, #fff));--color:var(--ion-toolbar-color, var(--ion-text-color, #424242));--border-color:var(--ion-toolbar-border-color, var(--ion-border-color, var(--ion-color-step-150, #c1c4cd)));--padding-top:0;--padding-bottom:0;--padding-start:0;--padding-end:0;--min-height:56px}.toolbar-content{-ms-flex:1;flex:1;-ms-flex-order:3;order:3;min-width:0;max-width:100%}::slotted(.buttons-first-slot){-webkit-margin-start:4px;margin-inline-start:4px}::slotted(.buttons-last-slot){-webkit-margin-end:4px;margin-inline-end:4px}::slotted([slot=start]){-ms-flex-order:2;order:2}::slotted([slot=secondary]){-ms-flex-order:4;order:4}::slotted([slot=primary]){-ms-flex-order:5;order:5;text-align:end}::slotted([slot=end]){-ms-flex-order:6;order:6;text-align:end}\";\nconst IonToolbarMdStyle0 = toolbarMdCss;\n\nconst Toolbar = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.childrenStyles = new Map();\n        this.color = undefined;\n    }\n    componentWillLoad() {\n        const buttons = Array.from(this.el.querySelectorAll('ion-buttons'));\n        const firstButtons = buttons.find((button) => {\n            return button.slot === 'start';\n        });\n        if (firstButtons) {\n            firstButtons.classList.add('buttons-first-slot');\n        }\n        const buttonsReversed = buttons.reverse();\n        const lastButtons = buttonsReversed.find((button) => button.slot === 'end') ||\n            buttonsReversed.find((button) => button.slot === 'primary') ||\n            buttonsReversed.find((button) => button.slot === 'secondary');\n        if (lastButtons) {\n            lastButtons.classList.add('buttons-last-slot');\n        }\n    }\n    childrenStyle(ev) {\n        ev.stopPropagation();\n        const tagName = ev.target.tagName;\n        const updatedStyles = ev.detail;\n        const newStyles = {};\n        const childStyles = this.childrenStyles.get(tagName) || {};\n        let hasStyleChange = false;\n        Object.keys(updatedStyles).forEach((key) => {\n            const childKey = `toolbar-${key}`;\n            const newValue = updatedStyles[key];\n            if (newValue !== childStyles[childKey]) {\n                hasStyleChange = true;\n            }\n            if (newValue) {\n                newStyles[childKey] = true;\n            }\n        });\n        if (hasStyleChange) {\n            this.childrenStyles.set(tagName, newStyles);\n            forceUpdate(this);\n        }\n    }\n    render() {\n        const mode = getIonMode(this);\n        const childStyles = {};\n        this.childrenStyles.forEach((value) => {\n            Object.assign(childStyles, value);\n        });\n        return (h(Host, { key: '8907ed75fbb2b1dced55c481bba6363f1dca815b', class: Object.assign(Object.assign({}, childStyles), createColorClasses(this.color, {\n                [mode]: true,\n                'in-toolbar': hostContext('ion-toolbar', this.el),\n            })) }, h(\"div\", { key: '6bfa09b08d6517f0d680f53b739854cecd631bc9', class: \"toolbar-background\" }), h(\"div\", { key: '1531bd6dd9e0a5843309bba854b744c453037ad0', class: \"toolbar-container\" }, h(\"slot\", { key: '881b41697d386eae651b019128573f0fa432cd33', name: \"start\" }), h(\"slot\", { key: '64a284e6eae5311ac3125dfadb4bb32bdba9d089', name: \"secondary\" }), h(\"div\", { key: 'c1f47503563b38084b27d7ba54f17ec478482b94', class: \"toolbar-content\" }, h(\"slot\", { key: '9a85acfba72252705619ae32acae9c14f81aa57d' })), h(\"slot\", { key: '89e08bd761dc6940dbebc5d06f5f080af204aa72', name: \"primary\" }), h(\"slot\", { key: 'a1cb7d95627f8a3d24dd4b9c11718fc164f53674', name: \"end\" }))));\n    }\n    get el() { return getElement(this); }\n};\nToolbar.style = {\n    ios: IonToolbarIosStyle0,\n    md: IonToolbarMdStyle0\n};\n\nexport { App as ion_app, Buttons as ion_buttons, Content as ion_content, Footer as ion_footer, Header as ion_header, RouterOutlet as ion_router_outlet, ToolbarTitle as ion_title, Toolbar as ion_toolbar };\n"], "names": ["r", "registerInstance", "h", "H", "Host", "f", "getElement", "d", "createEvent", "e", "readTask", "i", "forceUpdate", "w", "writeTask", "shouldUseCloseWatcher", "p", "printIonWarning", "b", "getIonMode", "c", "config", "a", "isPlatform", "m", "hasLazyBuild", "componentOnReady", "l", "clamp", "inheritAriaAttributes", "s", "shallowEqualStringMap", "isRTL", "createColorClasses", "hostContext", "find<PERSON><PERSON><PERSON><PERSON>nt", "printIonContentErrorMsg", "g", "getScrollElement", "createKeyboardController", "getTimeGivenProgression", "attachComponent", "detachComponent", "createLockController", "t", "transition", "appCss", "IonAppStyle0", "App", "constructor", "hostRef", "componentDidLoad", "_this", "rIC", "_asyncToGenerator", "isHybrid", "window", "getBoolean", "then", "module", "startTapClick", "startStatusTap", "needInputShims", "platform", "startInputShims", "hardwareBackButtonModule", "supportsHardwareBackButtonEvents", "startHardwareBackButton", "blockHardwareBackButton", "startKeyboardAssist", "focusVisible", "startFocusVisible", "setFocus", "elements", "_this2", "render", "mode", "key", "class", "el", "needsShimsIOS", "isAndroid<PERSON><PERSON><PERSON>eb", "callback", "requestIdleCallback", "setTimeout", "style", "buttonsIosCss", "IonButtonsIosStyle0", "buttonsMdCss", "IonButtonsMdStyle0", "Buttons", "collapse", "ios", "md", "contentCss", "IonContentStyle0", "Content", "ionScrollStart", "ionScroll", "ionScrollEnd", "watchDog", "isScrolling", "lastScroll", "queued", "cTop", "cBottom", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "resizeTimeout", "tabsElement", "detail", "scrollTop", "scrollLeft", "type", "event", "undefined", "startX", "startY", "startTime", "currentX", "currentY", "velocityX", "velocityY", "deltaX", "deltaY", "currentTime", "data", "color", "fullscreen", "forceOverscroll", "scrollX", "scrollY", "scrollEvents", "connectedCallback", "closest", "closestTabs", "tabsLoadCallback", "resize", "addEventListener", "disconnectedCallback", "onScrollEnd", "removeEventListener", "onResize", "clearTimeout", "offsetParent", "shouldForceOverscroll", "readDimensions", "page", "getPageElement", "top", "Math", "max", "offsetTop", "bottom", "offsetHeight", "dirty", "onScroll", "ev", "timeStamp", "Date", "now", "shouldStart", "onScrollStart", "ts", "updateScrollDetail", "scrollEl", "emit", "_this3", "Promise", "resolve", "getBackgroundElement", "_this4", "backgroundContentEl", "scrollToTop", "duration", "scrollToPoint", "scrollToBottom", "_this5", "y", "scrollHeight", "clientHeight", "apply", "arguments", "scrollByPoint", "x", "_this6", "_x", "_x2", "_this7", "promise", "fromY", "fromX", "step", "linearTime", "min", "easedT", "pow", "floor", "requestAnimationFrame", "clearInterval", "setInterval", "rtl", "transitionShadow", "TagType", "overscroll", "ref", "id", "part", "name", "getParentElement", "_a", "parentElement", "parentNode", "host", "tabs", "timestamp", "prevX", "prevY", "prevT", "<PERSON><PERSON><PERSON><PERSON>", "handleFooterFade", "baseEl", "maxScroll", "fadeDuration", "fadeStart", "distanceToStart", "scale", "setProperty", "toString", "footerIosCss", "IonFooterIosStyle0", "footerMdCss", "IonFooterMdStyle0", "Footer", "_this8", "keyboardCtrl", "checkCollap<PERSON><PERSON><PERSON>er", "hasFade", "destroyCollapsibleFooter", "pageEl", "contentEl", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_ref2", "contentScrollCallback", "_x3", "keyboardVisible", "translucent", "componentDidUpdate", "_this9", "_ref3", "keyboardOpen", "waitForResize", "_x4", "_x5", "destroy", "tabBar", "querySelector", "role", "slot", "TRANSITION", "cloneElement", "tagName", "getCachedEl", "document", "clonedEl", "createElement", "classList", "add", "body", "append<PERSON><PERSON><PERSON>", "createHeaderIndex", "headerEl", "toolbars", "querySelectorAll", "Array", "from", "map", "toolbar", "ionTitleEl", "background", "shadowRoot", "innerTitleEl", "ionButtonsEl", "handleContentScroll", "scrollHeaderIndex", "nativeRefresher", "scaleLargeTitles", "setToolbarBackgroundOpacity", "opacity", "removeProperty", "handleToolbarBorderIntersection", "mainHeaderIndex", "isIntersecting", "intersectionRatio", "handleToolbarIntersection", "intersection", "intersectionRect", "intersectionArea", "width", "height", "rootArea", "rootBounds", "isPageHidden", "leftDiff", "abs", "left", "boundingClientRect", "rightDiff", "right", "isPageTransitioning", "setHeaderActive", "hasValidIntersection", "headerIndex", "active", "remove", "removeAttribute", "setAttribute", "for<PERSON>ach", "ionTitle", "titleDiv", "size", "transform", "handleHeaderFade", "condense<PERSON><PERSON>er", "baseElHeight", "headerIosCss", "IonHeaderIosStyle0", "headerMdCss", "IonHeaderMdStyle0", "Header", "_this0", "inheritedAttributes", "setupFadeHeader", "_ref4", "_x6", "_x7", "componentWillLoad", "checkCollapsible<PERSON><PERSON>er", "destroyCollapsibleHeader", "_this1", "hasCondense", "title", "setupCondenseHeader", "intersectionObserver", "disconnect", "collapsibleMainHeader", "_this10", "IntersectionObserver", "headers", "find", "header", "toolbarIntersection", "root", "threshold", "observe", "length", "roleType", "Object", "assign", "routerOutletCss", "IonRouterOutletStyle0", "RouterOutlet", "ionNavWillLoad", "ionNavWillChange", "ionNavDidChange", "lockController", "gestureOrAnimationInProgress", "delegate", "animated", "animation", "swi<PERSON><PERSON><PERSON><PERSON>", "swipe<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gesture", "enable", "_this11", "onStart", "createSwipeBackGesture", "canStart", "ani", "progressStep", "shouldComplete", "dur", "onFinish", "onEnd", "oneTimeCallback", "newStepValue", "easing", "progressEnd", "commit", "enteringEl", "leavingEl", "opts", "_this12", "unlock", "lock", "changed", "console", "error", "setRouteId", "params", "direction", "_this13", "setRoot", "animationBuilder", "element", "activeEl", "getRouteId", "_this14", "activeParams", "component", "_this15", "activeComponent", "_x8", "_x9", "_this16", "get", "deepWait", "progressCallback", "progressAnimation", "watchers", "titleIosCss", "IonTitleIosStyle0", "titleMdCss", "IonTitleMdStyle0", "ToolbarTitle", "ionStyle", "sizeChanged", "emitStyle", "getSize", "dir", "toolbarIosCss", "IonToolbarIosStyle0", "toolbarMdCss", "IonToolbarMdStyle0", "<PERSON><PERSON><PERSON>", "childrenStyles", "Map", "buttons", "firstButtons", "button", "buttonsReversed", "reverse", "lastButtons", "childrenStyle", "stopPropagation", "target", "updatedStyles", "newStyles", "childStyles", "hasStyleChange", "keys", "<PERSON><PERSON><PERSON>", "newValue", "set", "value", "ion_app", "ion_buttons", "ion_content", "ion_footer", "ion_header", "ion_router_outlet", "ion_title", "ion_toolbar"], "sourceRoot": "webpack:///", "x_google_ignoreList": [0]}