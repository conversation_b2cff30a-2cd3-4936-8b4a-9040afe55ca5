.case-files-container {
  padding: 16px;
  max-width: 1200px;
  margin: 0 auto;
}

.header-section {
  text-align: center;
  margin-bottom: 24px;
  
  .page-title {
    font-size: 28px;
    font-weight: 600;
    color: #1a1a1a;
    margin: 0 0 8px 0;
  }
  
  .page-subtitle {
    font-size: 16px;
    color: #666;
    margin: 0;
  }
}

.case-selector {
  margin-bottom: 24px;

  ion-item {
    --background: #f8f9fa;
    --border-radius: 12px;
    --padding-start: 16px;
    --padding-end: 16px;
    margin-bottom: 16px;
  }
}

.upload-progress {
  margin-top: 16px;
  
  .progress-item {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    padding: 12px;
    margin-bottom: 8px;
    
    .progress-info {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 8px;
      
      .file-name {
        font-size: 14px;
        color: white;
        font-weight: 500;
      }
      
      .progress-percent {
        font-size: 12px;
        color: rgba(255, 255, 255, 0.8);
      }
    }
    
    ion-progress-bar {
      --background: rgba(255, 255, 255, 0.2);
      --progress-background: white;
      height: 4px;
      border-radius: 2px;
    }
  }
}

.loading-container {
  text-align: center;
  padding: 48px 16px;
  
  ion-spinner {
    margin-bottom: 16px;
  }
  
  p {
    color: #666;
    font-size: 14px;
  }
}

.folders-grid, .files-grid {
  margin-bottom: 24px;

  .grid-title {
    font-size: 18px;
    font-weight: 600;
    color: #1a1a1a;
    margin: 0 0 16px 0;
  }

  .folder-items, .file-items {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 16px;
  }
}

.folder-card {
  --background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid #e0e0e0;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
    background: #fafafa;
  }

  .folder-icon {
    text-align: center;
    margin-bottom: 12px;

    ion-icon {
      font-size: 48px;
    }
  }

  .folder-info {
    .folder-name {
      font-size: 16px;
      font-weight: 600;
      color: #1a1a1a;
      margin: 0 0 4px 0;
      word-break: break-word;
    }

    .folder-details {
      font-size: 12px;
      color: #666;
      margin: 0 0 4px 0;
    }

    .folder-created {
      font-size: 12px;
      color: #888;
      margin: 0;
    }
  }
}

.file-card {
  --background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid #e0e0e0;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  }

  .file-icon {
    text-align: center;
    margin-bottom: 12px;

    ion-icon {
      font-size: 32px;
    }
  }

  .file-info {
    margin-bottom: 12px;

    .file-name {
      font-size: 16px;
      font-weight: 600;
      color: #1a1a1a;
      margin: 0 0 4px 0;
      word-break: break-word;
    }

    .file-details {
      font-size: 12px;
      color: #666;
      margin: 0 0 4px 0;
    }

    .file-uploader {
      font-size: 12px;
      color: #888;
      margin: 0;
    }
  }

  .file-actions {
    display: flex;
    justify-content: flex-end;
    gap: 8px;

    ion-button {
      --border-radius: 6px;
      height: 32px;
      width: 32px;
    }
  }
}

.empty-state,
.no-case-selected,
.no-cases {
  text-align: center;
  padding: 64px 16px;

  .empty-icon {
    font-size: 64px;
    color: #ccc;
    margin-bottom: 16px;
  }

  h3 {
    font-size: 18px;
    font-weight: 600;
    color: #666;
    margin: 0 0 8px 0;
  }

  p {
    font-size: 14px;
    color: #888;
    margin: 0 0 24px 0;
  }

  .empty-actions {
    display: flex;
    gap: 12px;
    justify-content: center;
    flex-wrap: wrap;
  }

  ion-button {
    --border-radius: 12px;
  }
}

// Create Folder Modal Styles
.create-folder-content {
  .folder-location {
    margin-bottom: 24px;
    padding: 16px;
    background: #f8f9fa;
    border-radius: 12px;

    h3 {
      font-size: 16px;
      font-weight: 600;
      color: #1a1a1a;
      margin: 0 0 8px 0;
    }

    .location-path {
      display: flex;
      align-items: center;
      font-size: 14px;
      color: #666;
      margin: 0;

      ion-icon {
        margin-right: 8px;
        font-size: 16px;
      }
    }
  }

  ion-item {
    --background: transparent;
    --border-radius: 12px;
    --padding-start: 16px;
    --padding-end: 16px;
    margin-bottom: 24px;
    border: 1px solid #e0e0e0;
    border-radius: 12px;
  }

  .modal-actions {
    margin-top: 32px;

    ion-button {
      --border-radius: 12px;
      height: 48px;
    }
  }
}

// Mobile responsiveness
@media (max-width: 768px) {
  .case-files-container {
    padding: 12px;
  }
  
  .files-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }
  
  .upload-area {
    padding: 24px 12px;
    
    .upload-content {
      .upload-icon {
        font-size: 36px;
      }
      
      h3 {
        font-size: 16px;
      }
      
      p {
        font-size: 13px;
      }
    }
  }
  
  .section-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
    
    h2 {
      font-size: 18px;
    }
  }
}

// Demo info styling
.demo-info {
  margin-top: 32px;

  .info-card {
    --background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 16px;
    box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);

    ion-card-header {
      ion-card-title {
        color: white;
        font-size: 20px;
        font-weight: 600;
      }

      ion-card-subtitle {
        color: rgba(255, 255, 255, 0.8);
        font-size: 14px;
      }
    }

    ion-card-content {
      p {
        color: rgba(255, 255, 255, 0.9);
        margin-bottom: 12px;
      }

      ul {
        color: rgba(255, 255, 255, 0.9);
        margin: 16px 0;
        padding-left: 20px;

        li {
          margin-bottom: 8px;
          font-size: 14px;
        }
      }

      .demo-actions {
        margin-top: 24px;

        ion-button {
          --background: rgba(255, 255, 255, 0.2);
          --color: white;
          --border-radius: 12px;
          font-weight: 600;

          &:hover {
            --background: rgba(255, 255, 255, 0.3);
          }
        }
      }
    }
  }
}

// Dark mode support
@media (prefers-color-scheme: dark) {
  .case-files-container {
    .header-section .page-title {
      color: #ffffff;
    }

    .files-grid .file-card {
      --background: #2d2d2d;
      border-color: #404040;

      .file-info .file-name {
        color: #ffffff;
      }
    }

    .section-header h2 {
      color: #ffffff;
    }
  }
}

ion-fab-button {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}