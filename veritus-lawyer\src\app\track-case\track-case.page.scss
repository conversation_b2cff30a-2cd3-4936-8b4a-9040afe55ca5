// Header Styling
.case-progress-toolbar {
  --background: #B88A42;
  --color: #ffffff;
}

.back-icon {
  font-size: 24px;
  color: #ffffff;
}

.case-progress-title {
  font-size: 18px;
  font-weight: 600;
  color: #ffffff;
}

// Content Styling
.case-progress-content {
  --background: #f8f9fa;
}

.case-progress-container {
  padding: 20px;
  min-height: 100vh;
}

// Profile Section
.profile-section {
  text-align: center;
  margin-bottom: 32px;
  background: #ffffff;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);
}

.profile-avatar {
  margin-bottom: 16px;
}

.avatar-placeholder {
  width: 72px;
  height: 72px;
  border-radius: 50%;
  background: linear-gradient(135deg, #0A49FF 0%, #4A90E2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
  box-shadow: 0 4px 20px rgba(10, 73, 255, 0.3);
}

.avatar-icon {
  font-size: 36px;
  color: #ffffff;
}

.case-title {
  font-size: 20px;
  font-weight: 700;
  color: #000000;
  margin: 0 0 8px 0;
}

.case-info {
  font-size: 14px;
  color: #666666;
  margin: 0 0 8px 0;
}

.case-number,
.case-date {
  font-weight: 500;
}

.lawyer-name {
  font-size: 16px;
  color: #0A49FF;
  font-weight: 600;
  margin: 0;
}

// Progress Section
.progress-section {
  margin-bottom: 32px;
  background: #ffffff;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.progress-label {
  font-size: 16px;
  font-weight: 600;
  color: #000000;
}

.progress-percentage {
  font-size: 16px;
  font-weight: 700;
  color: #0A49FF;
}

.progress-bar {
  width: 100%;
  height: 8px;
  background: #e9ecef;
  border-radius: 4px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #0A49FF 0%, #4A90E2 100%);
  border-radius: 4px;
  transition: width 0.3s ease;
}

// Timeline Section
.timeline-section {
  margin-bottom: 32px;
}

.timeline-title {
  font-size: 18px;
  font-weight: 600;
  color: #000000;
  margin: 0 0 20px 0;
}

.timeline {
  position: relative;
}

.timeline-item {
  display: flex;
  gap: 16px;
  margin-bottom: 24px;
  
  &.last {
    margin-bottom: 0;
  }
}

.timeline-marker {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex-shrink: 0;
}

.timeline-dot {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: #e9ecef;
  border: 3px solid #dee2e6;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  z-index: 2;
  
  &.completed {
    background: #0A49FF;
    border-color: #0A49FF;
    
    .timeline-icon {
      color: #ffffff;
    }
  }
}

.timeline-icon {
  font-size: 16px;
  color: #6c757d;
}

.timeline-line {
  width: 2px;
  height: 40px;
  background: #dee2e6;
  margin-top: 8px;
}

.timeline-content {
  flex: 1;
  padding-top: 4px;
}

.timeline-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 4px;
  gap: 12px;
}

.timeline-event-title {
  font-size: 16px;
  font-weight: 600;
  color: #000000;
  margin: 0;
  line-height: 1.3;
}

.timeline-date {
  font-size: 12px;
  color: #6c757d;
  font-weight: 500;
  flex-shrink: 0;
}

.timeline-description {
  font-size: 14px;
  color: #666666;
  margin: 0;
  line-height: 1.4;
}

// Details Section
.details-section {
  margin-bottom: 32px;
}

.details-title {
  font-size: 18px;
  font-weight: 600;
  color: #000000;
  margin: 0 0 16px 0;
}

.details-card {
  background: #ffffff;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
  gap: 16px;
  
  &:last-child {
    border-bottom: none;
    padding-bottom: 0;
  }
  
  &:first-child {
    padding-top: 0;
  }
}

.detail-label {
  font-size: 14px;
  font-weight: 600;
  color: #495057;
  flex-shrink: 0;
  min-width: 80px;
}

.detail-value {
  font-size: 14px;
  color: #000000;
  text-align: right;
  
  &.status {
    padding: 4px 12px;
    border-radius: 12px;
    font-weight: 500;
    font-size: 12px;
    
    &.ongoing {
      background: #d4edda;
      color: #155724;
    }
    
    &.closed {
      background: #d1ecf1;
      color: #0c5460;
    }
    
    &.cancelled {
      background: #f8d7da;
      color: #721c24;
    }
  }
}

// Loading State
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
  
  ion-spinner {
    margin-bottom: 16px;
  }
  
  p {
    font-size: 16px;
    color: #666666;
    margin: 0;
  }
}

// Empty State
.empty-state {
  text-align: center;
  padding: 60px 20px;
}

.empty-icon {
  font-size: 64px;
  color: #dee2e6;
  margin-bottom: 16px;
}

.empty-title {
  font-size: 18px;
  font-weight: 600;
  color: #000000;
  margin: 0 0 8px 0;
}

.empty-description {
  font-size: 14px;
  color: #666666;
  margin: 0 0 20px 0;
}

.back-btn {
  background: #0A49FF;
  color: #ffffff;
  border: none;
  border-radius: 8px;
  padding: 12px 24px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  
  &:hover {
    background: #0841e6;
  }
  
  &:active {
    transform: scale(0.95);
  }
}

// Responsive Design
@media (max-width: 375px) {
  .case-progress-container {
    padding: 16px;
  }
  
  .timeline-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
  
  .detail-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
  
  .detail-value {
    text-align: left;
  }
}

// Timeline Header Section
.timeline-header-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.timeline-actions {
  margin-top: 8px;
  text-align: right;
}


