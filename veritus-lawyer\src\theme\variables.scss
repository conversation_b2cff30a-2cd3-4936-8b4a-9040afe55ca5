// Ionic Variables and Theming. For more info, please see:
// http://ionicframework.com/docs/theming/
/* src/theme/variables.scss */

/*--------------------------------------------------------------
1) Your Veritus design tokens as SCSS variables
--------------------------------------------------------------*/
$veritus-primary:           #1B3A9E !default;
$veritus-primary-shade:     #16346C !default;
$veritus-primary-tint:      #3B5AAD !default;

$veritus-gold:              #C49A56 !default;
$veritus-gold-shade:        #A77C3E !default;
$veritus-gold-tint:         #D8B67E !default;

$veritus-success:           #67EF77 !default;
$veritus-warning:           #FF9500 !default;
$veritus-danger:            #FF3B30 !default;

$veritus-white:             #FFFFFF !default;
$veritus-black:             #000000 !default;
$veritus-gray-light:        #F2F2F7 !default;
$veritus-gray-medium:       #616161 !default;
$veritus-gray-dark:         #48484A !default;

$veritus-on-surface-light:  rgba(0,0,0,0.20) !default;
$veritus-surface-variant:   rgba(213,208,200,0.38) !default;
$veritus-divider:           rgba(0,0,0,0.06) !default;

/*--------------------------------------------------------------
2) Map those tokens into Ionic’s color map
   (see https://ionicframework.com/docs/theming/theming-your-app)
--------------------------------------------------------------*/
$colors: (
  primary:           $veritus-primary,
  primary-shade:     $veritus-primary-shade,
  primary-tint:      $veritus-primary-tint,

  secondary:         $veritus-gold,
  secondary-shade:   $veritus-gold-shade,
  secondary-tint:    $veritus-gold-tint,

  success:           $veritus-success,
  warning:           $veritus-warning,
  danger:            $veritus-danger,

  medium:            $veritus-gray-medium,
  light:             $veritus-on-surface-light,
  dark:              $veritus-black,

  /* Custom aliases if you like */
  surface-variant:   $veritus-surface-variant,
  divider:           $veritus-divider
) !default;

/*--------------------------------------------------------------
3) (Optional) Export CSS variables for runtime theming/fallback
--------------------------------------------------------------*/
:root {
  --veritus-primary:            #{$veritus-primary};
  --veritus-primary-shade:      #{$veritus-primary-shade};
  --veritus-primary-tint:       #{$veritus-primary-tint};

  --veritus-gold:               #{$veritus-gold};
  --veritus-gold-shade:         #{$veritus-gold-shade};
  --veritus-gold-tint:          #{$veritus-gold-tint};

  --veritus-success:            #{$veritus-success};
  --veritus-warning:            #{$veritus-warning};
  --veritus-danger:             #{$veritus-danger};

  --veritus-white:              #{$veritus-white};
  --veritus-black:              #{$veritus-black};
  --veritus-gray-light:         #{$veritus-gray-light};
  --veritus-gray-medium:        #{$veritus-gray-medium};
  --veritus-gray-dark:          #{$veritus-gray-dark};

  --veritus-on-surface-light:   #{$veritus-on-surface-light};
  --veritus-surface-variant:    #{$veritus-surface-variant};
  --veritus-divider:            #{$veritus-divider};
}

/*--------------------------------------------------------------
4) Surface variant for navigation
--------------------------------------------------------------*/
:root {
  --veritus-surface-variant: rgba(213, 208, 200, 0.38);
  --ion-tab-bar-background: var(--veritus-surface-variant);
  --ion-color-primary: #C49A56; // Gold color for selected tabs
  --ion-color-primary-rgb: 196, 154, 86;
}

/*--------------------------------------------------------------
5) Disable dark mode so M3 surfaces stay white
--------------------------------------------------------------*/
@media (prefers-color-scheme: dark) {
  :root {
    --ion-background-color:   var(--veritus-white) !important;
    --ion-text-color:         var(--veritus-black) !important;
    --ion-tab-bar-background: var(--veritus-surface-variant) !important;
  }
}
