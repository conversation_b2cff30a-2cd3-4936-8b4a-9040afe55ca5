rules_version = '2';
service firebase.storage {
  match /b/{bucket}/o {
    
    // Helper functions
    function isAuthenticated() {
      return request.auth != null;
    }
    
    function getUserRole() {
      return request.auth.token.role;
    }
    
    function isOwner(userId) {
      return request.auth.uid == userId;
    }
    
    function isLawyer() {
      return getUserRole() == 'lawyer';
    }
    
    function isSecretary() {
      return getUserRole() == 'secretary';
    }
    
    function isClient() {
      return getUserRole() == 'client';
    }

    // Profile images - users can upload their own profile images
    match /profiles/{userId}/{allPaths=**} {
      allow read: if isAuthenticated();
      allow write: if isAuthenticated() && isOwner(userId);
    }

    // Case files - lawyers and authorized secretaries can access
    match /cases/{lawyerId}/{caseId}/{allPaths=**} {
      allow read: if isAuthenticated() && (
        isOwner(lawyerId) ||
        (isSecretary() && hasFilePermission(lawyerId)) ||
        isClientOfCase(lawyerId, caseId)
      );
      allow write: if isAuthenticated() && (
        isOwner(lawyerId) ||
        (isSecretary() && hasFilePermission(lawyerId))
      );
    }

    // Document templates - lawyers and authorized secretaries can access
    match /templates/{lawyerId}/{allPaths=**} {
      allow read: if isAuthenticated() && (
        isOwner(lawyerId) ||
        (isSecretary() && hasFilePermission(lawyerId))
      );
      allow write: if isAuthenticated() && (
        isOwner(lawyerId) ||
        (isSecretary() && hasFilePermission(lawyerId))
      );
    }

    // Client documents - clients and their lawyers can access
    match /client_documents/{clientId}/{allPaths=**} {
      allow read: if isAuthenticated() && (
        isOwner(clientId) ||
        isLawyer()
      );
      allow write: if isAuthenticated() && (
        isOwner(clientId) ||
        isLawyer() ||
        (isSecretary() && hasFilePermission())
      );
    }

    // Retainer documents - lawyers, secretaries, and clients can access
    match /retainers/{lawyerId}/{clientId}/{allPaths=**} {
      allow read: if isAuthenticated() && (
        isOwner(lawyerId) ||
        isOwner(clientId) ||
        (isSecretary() && hasFilePermission(lawyerId))
      );
      allow write: if isAuthenticated() && (
        isOwner(lawyerId) ||
        (isSecretary() && hasFilePermission(lawyerId))
      );
    }

    // Verification documents - lawyers can upload verification documents
    match /verification/{lawyerId}/{allPaths=**} {
      allow read: if isAuthenticated() && (
        isOwner(lawyerId) ||
        getUserRole() == 'admin'
      );
      allow write: if isAuthenticated() && isOwner(lawyerId) && isLawyer();
    }

    // Temporary uploads - users can upload to their temp folder
    match /temp/{userId}/{allPaths=**} {
      allow read, write: if isAuthenticated() && isOwner(userId);
      // Auto-delete after 24 hours (handled by Cloud Function)
    }

    // Helper function to check if secretary has file permissions for a lawyer
    function hasFilePermission(lawyerId) {
      // This would need to be implemented with a Firestore lookup
      // For now, we'll allow if user is a secretary
      return isSecretary();
    }

    // Helper function to check if user is a client of a specific case
    function isClientOfCase(lawyerId, caseId) {
      // This would need to be implemented with a Firestore lookup
      // For now, we'll allow if user is a client
      return isClient();
    }

    // Default deny rule
    match /{allPaths=**} {
      allow read, write: if false;
    }
  }
}
