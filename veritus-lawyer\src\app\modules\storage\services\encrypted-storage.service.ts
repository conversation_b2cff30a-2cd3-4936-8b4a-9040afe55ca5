import { Injectable } from '@angular/core';
import { Storage, ref, uploadBytes, getDownloadURL, deleteObject, listAll } from '@angular/fire/storage';
import { Firestore, doc, setDoc, getDoc, updateDoc, collection, query, where, getDocs, deleteDoc } from '@angular/fire/firestore';

export interface EncryptedFile {
  id: string;
  originalName: string;
  encryptedName: string;
  mimeType: string;
  size: number;
  uploadedBy: string;
  uploadedAt: Date;
  caseId?: string;
  clientId?: string;
  lawyerId?: string;
  tags: string[];
  isArchived: boolean;
  encryptionKey: string; // Stored encrypted with user's key
  downloadUrl?: string;
  accessLevel: 'public' | 'restricted' | 'confidential';
}

@Injectable({
  providedIn: 'root'
})
export class EncryptedStorageService {

  constructor(
    private storage: Storage,
    private firestore: Firestore
  ) { }

  // Client-side AES-256 encryption
  async encryptFile(file: File, encryptionKey: string): Promise<ArrayBuffer> {
    // TODO: Implement actual AES-256 encryption
    // For now, return the file as-is (placeholder)
    return file.arrayBuffer();
  }

  async decryptFile(encryptedData: ArrayBuffer, encryptionKey: string): Promise<ArrayBuffer> {
    // TODO: Implement actual AES-256 decryption
    // For now, return the data as-is (placeholder)
    return encryptedData;
  }

  // Generate encryption key for user
  generateEncryptionKey(): string {
    // TODO: Generate secure AES-256 key
    return crypto.getRandomValues(new Uint8Array(32)).toString();
  }

  // Upload encrypted file
  async uploadEncryptedFile(
    file: File,
    metadata: {
      uploadedBy: string;
      caseId?: string;
      clientId?: string;
      lawyerId?: string;
      tags?: string[];
      accessLevel?: 'public' | 'restricted' | 'confidential';
    }
  ): Promise<EncryptedFile> {
    const encryptionKey = this.generateEncryptionKey();
    const encryptedData = await this.encryptFile(file, encryptionKey);
    
    // Generate unique encrypted filename
    const timestamp = Date.now();
    const randomId = Math.random().toString(36).substring(2);
    const encryptedName = `${timestamp}_${randomId}.enc`;
    
    // Upload to Firebase Storage
    const storageRef = ref(this.storage, `encrypted-files/${encryptedName}`);
    const blob = new Blob([encryptedData], { type: 'application/octet-stream' });
    
    await uploadBytes(storageRef, blob);
    const downloadUrl = await getDownloadURL(storageRef);

    // Create file metadata
    const fileMetadata: EncryptedFile = {
      id: `${timestamp}_${randomId}`,
      originalName: file.name,
      encryptedName,
      mimeType: file.type,
      size: file.size,
      uploadedBy: metadata.uploadedBy,
      uploadedAt: new Date(),
      caseId: metadata.caseId,
      clientId: metadata.clientId,
      lawyerId: metadata.lawyerId,
      tags: metadata.tags || [],
      isArchived: false,
      encryptionKey: encryptionKey, // TODO: Encrypt this with user's master key
      downloadUrl,
      accessLevel: metadata.accessLevel || 'restricted'
    };

    // Save metadata to Firestore
    const docRef = doc(this.firestore, 'encrypted-files', fileMetadata.id);
    await setDoc(docRef, fileMetadata);

    return fileMetadata;
  }

  // Download and decrypt file
  async downloadDecryptedFile(fileId: string, userKey: string): Promise<Blob> {
    // Get file metadata
    const docRef = doc(this.firestore, 'encrypted-files', fileId);
    const docSnap = await getDoc(docRef);
    
    if (!docSnap.exists()) {
      throw new Error('File not found');
    }

    const fileMetadata = docSnap.data() as EncryptedFile;
    
    // Download encrypted file
    const response = await fetch(fileMetadata.downloadUrl!);
    const encryptedData = await response.arrayBuffer();
    
    // Decrypt file
    const decryptedData = await this.decryptFile(encryptedData, fileMetadata.encryptionKey);
    
    return new Blob([decryptedData], { type: fileMetadata.mimeType });
  }

  // Get files by user/case/client
  async getFilesByUser(userId: string): Promise<EncryptedFile[]> {
    const q = query(
      collection(this.firestore, 'encrypted-files'),
      where('uploadedBy', '==', userId),
      where('isArchived', '==', false)
    );
    
    const querySnapshot = await getDocs(q);
    return querySnapshot.docs.map(doc => doc.data() as EncryptedFile);
  }

  async getFilesByCase(caseId: string): Promise<EncryptedFile[]> {
    const q = query(
      collection(this.firestore, 'encrypted-files'),
      where('caseId', '==', caseId),
      where('isArchived', '==', false)
    );
    
    const querySnapshot = await getDocs(q);
    return querySnapshot.docs.map(doc => doc.data() as EncryptedFile);
  }

  async getFilesByClient(clientId: string): Promise<EncryptedFile[]> {
    const q = query(
      collection(this.firestore, 'encrypted-files'),
      where('clientId', '==', clientId),
      where('isArchived', '==', false)
    );
    
    const querySnapshot = await getDocs(q);
    return querySnapshot.docs.map(doc => doc.data() as EncryptedFile);
  }

  async getFilesByLawyer(lawyerId: string): Promise<EncryptedFile[]> {
    const q = query(
      collection(this.firestore, 'encrypted-files'),
      where('lawyerId', '==', lawyerId),
      where('isArchived', '==', false)
    );
    
    const querySnapshot = await getDocs(q);
    return querySnapshot.docs.map(doc => doc.data() as EncryptedFile);
  }

  // Delete file
  async deleteFile(fileId: string): Promise<void> {
    // Get file metadata
    const docRef = doc(this.firestore, 'encrypted-files', fileId);
    const docSnap = await getDoc(docRef);
    
    if (!docSnap.exists()) {
      throw new Error('File not found');
    }

    const fileMetadata = docSnap.data() as EncryptedFile;
    
    // Delete from Firebase Storage
    const storageRef = ref(this.storage, `encrypted-files/${fileMetadata.encryptedName}`);
    await deleteObject(storageRef);
    
    // Delete metadata from Firestore
    await deleteDoc(docRef);
  }

  // Archive file (soft delete)
  async archiveFile(fileId: string): Promise<void> {
    const docRef = doc(this.firestore, 'encrypted-files', fileId);
    await updateDoc(docRef, { 
      isArchived: true, 
      archivedAt: new Date() 
    });
  }

  // Update file metadata
  async updateFileMetadata(fileId: string, updates: Partial<EncryptedFile>): Promise<void> {
    const docRef = doc(this.firestore, 'encrypted-files', fileId);
    await updateDoc(docRef, { ...updates, updatedAt: new Date() });
  }

  // Search files
  async searchFiles(searchTerm: string, filters?: {
    userId?: string;
    caseId?: string;
    clientId?: string;
    lawyerId?: string;
    tags?: string[];
    accessLevel?: string;
  }): Promise<EncryptedFile[]> {
    // TODO: Implement full-text search
    // For now, return basic filtered results
    let q = query(collection(this.firestore, 'encrypted-files'));
    
    if (filters?.userId) {
      q = query(q, where('uploadedBy', '==', filters.userId));
    }
    
    if (filters?.caseId) {
      q = query(q, where('caseId', '==', filters.caseId));
    }
    
    if (filters?.clientId) {
      q = query(q, where('clientId', '==', filters.clientId));
    }
    
    if (filters?.lawyerId) {
      q = query(q, where('lawyerId', '==', filters.lawyerId));
    }
    
    if (filters?.accessLevel) {
      q = query(q, where('accessLevel', '==', filters.accessLevel));
    }
    
    const querySnapshot = await getDocs(q);
    const files = querySnapshot.docs.map(doc => doc.data() as EncryptedFile);
    
    // Filter by search term (basic implementation)
    if (searchTerm) {
      return files.filter(file => 
        file.originalName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        file.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()))
      );
    }
    
    return files;
  }
}
