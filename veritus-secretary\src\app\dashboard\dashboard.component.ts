import { Component, OnInit } from '@angular/core';

// Interfaces for type safety
interface CalendarDay {
  date: number;
  otherMonth: boolean;
  isToday: boolean;
  hasAppointment: boolean;
}

interface Appointment {
  type: string;
  client: string;
  time: string;
}

interface RetainerClient {
  name: string;
  status: 'active' | 'inactive';
}

interface Document {
  name: string;
  date: string;
}

interface ChartData {
  height: number;
  color: string;
}

interface Transaction {
  amount: string;
  type: string;
  date: string;
}

@Component({
  selector: 'app-dashboard',
  templateUrl: './dashboard.component.html',
  styleUrls: ['./dashboard.component.scss']
})
export class DashboardComponent implements OnInit {
  // Calendar properties
  currentMonth: string = 'July';
  currentYear: number = 2025;
  selectedDateString: string = 'July 1, 2025';
  dayHeaders: string[] = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];

  // Search and filter
  searchTerm: string = '';

  // Calendar data with proper typing
  calendarDays: CalendarDay[] = [
    { date: 30, otherMonth: true, isToday: false, hasAppointment: false },
    { date: 1, otherMonth: false, isToday: true, hasAppointment: true },
    { date: 2, otherMonth: false, isToday: false, hasAppointment: false },
    { date: 3, otherMonth: false, isToday: false, hasAppointment: false },
    { date: 4, otherMonth: false, isToday: false, hasAppointment: false },
    { date: 5, otherMonth: false, isToday: false, hasAppointment: false },
    { date: 6, otherMonth: false, isToday: false, hasAppointment: false },
    { date: 7, otherMonth: false, isToday: false, hasAppointment: false },
    { date: 8, otherMonth: false, isToday: false, hasAppointment: false },
    { date: 9, otherMonth: false, isToday: false, hasAppointment: false },
    { date: 10, otherMonth: false, isToday: false, hasAppointment: false },
    { date: 11, otherMonth: false, isToday: false, hasAppointment: false },
    { date: 12, otherMonth: false, isToday: false, hasAppointment: true },
    { date: 13, otherMonth: false, isToday: false, hasAppointment: false },
    { date: 14, otherMonth: false, isToday: false, hasAppointment: false },
    { date: 15, otherMonth: false, isToday: false, hasAppointment: false },
    { date: 16, otherMonth: false, isToday: false, hasAppointment: false },
    { date: 17, otherMonth: false, isToday: false, hasAppointment: false },
    { date: 18, otherMonth: false, isToday: false, hasAppointment: false },
    { date: 19, otherMonth: false, isToday: false, hasAppointment: false },
    { date: 20, otherMonth: false, isToday: false, hasAppointment: false },
    { date: 21, otherMonth: false, isToday: false, hasAppointment: false },
    { date: 22, otherMonth: false, isToday: false, hasAppointment: false },
    { date: 23, otherMonth: false, isToday: false, hasAppointment: false },
    { date: 24, otherMonth: false, isToday: false, hasAppointment: false },
    { date: 25, otherMonth: false, isToday: false, hasAppointment: false },
    { date: 26, otherMonth: false, isToday: false, hasAppointment: false },
    { date: 27, otherMonth: false, isToday: false, hasAppointment: false },
    { date: 28, otherMonth: false, isToday: false, hasAppointment: false },
    { date: 29, otherMonth: false, isToday: false, hasAppointment: false },
    { date: 30, otherMonth: false, isToday: false, hasAppointment: false },
    { date: 31, otherMonth: false, isToday: false, hasAppointment: false },
    { date: 1, otherMonth: true, isToday: false, hasAppointment: false },
    { date: 2, otherMonth: true, isToday: false, hasAppointment: false },
    { date: 3, otherMonth: true, isToday: false, hasAppointment: false }
  ];

  // Appointments data
  todayAppointments: Appointment[] = [
    { type: 'Consultation', client: 'Kobe Bryant', time: '10:00' },
    { type: 'Consultation', client: 'LeBron James', time: '11:00' }
  ];

  // Retainer clients data
  retainerClients: RetainerClient[] = [
    { name: 'Amado Cruz', status: 'active' },
    { name: 'Clara Mendoza', status: 'inactive' },
    { name: 'Kobe Bryant', status: 'active' }
  ];

  // Documents data
  recentDocuments: Document[] = [
    { name: 'Lopez-Contract.pdf', date: 'Updated today' },
    { name: 'Client-Agreement.pdf', date: '4 days ago' }
  ];

  // Chart data
  chartData: ChartData[] = [
    { height: 60, color: '#e91e63' },
    { height: 40, color: '#e91e63' },
    { height: 80, color: '#e91e63' },
    { height: 30, color: '#e91e63' },
    { height: 70, color: '#e91e63' },
    { height: 50, color: '#e91e63' },
    { height: 90, color: '#e91e63' },
    { height: 45, color: '#e91e63' },
    { height: 85, color: '#e91e63' }
  ];

  chartLabels: string[] = ['1', '2', '3', '4', '5', '6', '7', '8', '9'];

  // Transactions data
  recentTransactions: Transaction[] = [
    { amount: '₱15,000', type: 'Consultation', date: 'June 15, 2025' },
    { amount: '₱5,000', type: 'Retainer', date: 'June 16, 2025' }
  ];

  ngOnInit(): void {
    this.initializeComponent();
  }

  private initializeComponent(): void {
    // Set current date as today
    const today = new Date();
    this.updateCurrentDate(today);
  }

  private updateCurrentDate(date: Date): void {
    const months = [
      'January', 'February', 'March', 'April', 'May', 'June',
      'July', 'August', 'September', 'October', 'November', 'December'
    ];

    this.currentMonth = months[date.getMonth()];
    this.currentYear = date.getFullYear();
    this.selectedDateString = `${this.currentMonth} ${date.getDate()}, ${this.currentYear}`;
  }

  previousMonth(): void {
    // TODO: Implement previous month navigation
    console.log('Previous month clicked');
  }

  nextMonth(): void {
    // TODO: Implement next month navigation
    console.log('Next month clicked');
  }

  selectDate(day: CalendarDay): void {
    if (day.otherMonth) return;

    // Update selected date
    this.selectedDateString = `${this.currentMonth} ${day.date}, ${this.currentYear}`;

    // TODO: Load appointments for selected date
    console.log('Date selected:', this.selectedDateString);
  }
}
