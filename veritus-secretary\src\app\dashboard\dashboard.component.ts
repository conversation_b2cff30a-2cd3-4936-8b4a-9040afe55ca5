import { Component, OnInit } from '@angular/core';

@Component({
  selector: 'app-dashboard',
  template: `
    <div class="dashboard-container">
      <!-- Left Column: Scheduling -->
      <div class="left-column">
        <div class="scheduling-section">
          <h2>Scheduling</h2>

          <!-- Calendar Widget -->
          <div class="calendar-widget">
            <div class="calendar-header">
              <button class="nav-btn" (click)="previousMonth()">‹</button>
              <h3>{{ currentMonth }}</h3>
              <button class="nav-btn" (click)="nextMonth()">›</button>
            </div>

            <div class="calendar-grid">
              <div class="day-header" *ngFor="let day of dayHeaders">{{ day }}</div>
              <div
                class="calendar-day"
                *ngFor="let day of calendarDays"
                [class.other-month]="day.otherMonth"
                [class.today]="day.isToday"
                [class.has-appointment]="day.hasAppointment"
                (click)="selectDate(day)">
                {{ day.date }}
              </div>
            </div>
          </div>

          <!-- Booking Section -->
          <div class="booking-section">
            <h4>Booking on {{ selectedDateString }}</h4>
            <div class="appointment-list">
              <div class="appointment-item" *ngFor="let appointment of todayAppointments">
                <div class="appointment-info">
                  <div class="appointment-type">{{ appointment.type }}</div>
                  <div class="appointment-client">{{ appointment.client }}</div>
                </div>
                <div class="appointment-time">{{ appointment.time }}</div>
              </div>
            </div>
            <button class="new-booking-btn">+ New Booking</button>
          </div>

          <!-- Retainer Clients -->
          <div class="retainer-section">
            <div class="section-header">
              <h4>Retainer Clients</h4>
              <button class="add-client-btn">+ Add Client</button>
            </div>
            <div class="client-list">
              <div class="client-item" *ngFor="let client of retainerClients">
                <div class="client-status" [class]="client.status"></div>
                <span>{{ client.name }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Right Column: Files & Finance -->
      <div class="right-column">
        <!-- Files Section -->
        <div class="files-section">
          <div class="section-header">
            <h2>Files</h2>
            <div class="search-box">
              <input type="text" placeholder="Search a document" [(ngModel)]="searchTerm">
              <span class="search-icon">🔍</span>
            </div>
          </div>

          <div class="file-categories">
            <div class="file-category">
              <div class="folder-icon">📁</div>
              <span>Disclosures</span>
            </div>
            <div class="file-category">
              <div class="folder-icon">📁</div>
              <span>Evidence</span>
            </div>
            <div class="file-category">
              <div class="folder-icon">📁</div>
              <span>Receipts</span>
            </div>
            <div class="file-category">
              <div class="folder-icon">📁</div>
              <span>Contracts</span>
            </div>
          </div>

          <div class="recent-documents">
            <h4>Recent documents</h4>
            <div class="document-item" *ngFor="let doc of recentDocuments">
              <div class="doc-icon">📄</div>
              <div class="doc-info">
                <div class="doc-name">{{ doc.name }}</div>
                <div class="doc-date">{{ doc.date }}</div>
              </div>
              <button class="doc-action">›</button>
            </div>
          </div>
        </div>

        <!-- Finance Section -->
        <div class="finance-section">
          <div class="section-header">
            <h2>Finance</h2>
            <button class="new-transaction-btn">+ New Transaction</button>
          </div>

          <div class="chart-container">
            <div class="chart-bars">
              <div class="bar" *ngFor="let bar of chartData" [style.height.%]="bar.height" [style.background]="bar.color"></div>
            </div>
            <div class="chart-labels">
              <span *ngFor="let label of chartLabels">{{ label }}</span>
            </div>
            <div class="chart-period">This year</div>
          </div>

          <div class="recent-transactions">
            <h4>Recent Transactions</h4>
            <div class="transaction-item" *ngFor="let transaction of recentTransactions">
              <div class="transaction-amount">{{ transaction.amount }}</div>
              <div class="transaction-info">
                <div class="transaction-type">{{ transaction.type }}</div>
                <div class="transaction-date">{{ transaction.date }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .dashboard-container {
      display: flex;
      gap: 24px;
      padding: 24px;
      background: #f8f9fa;
      min-height: 100vh;
    }

    .left-column {
      flex: 1;
      max-width: 400px;
    }

    .right-column {
      flex: 1;
      display: flex;
      flex-direction: column;
      gap: 24px;
    }

    .scheduling-section,
    .files-section,
    .finance-section {
      background: white;
      border-radius: 12px;
      padding: 24px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    h2 {
      font-size: 24px;
      font-weight: 600;
      margin: 0 0 20px 0;
      color: #333;
    }

    h3, h4 {
      font-size: 16px;
      font-weight: 600;
      margin: 0 0 12px 0;
      color: #333;
    }

    .calendar-widget {
      margin-bottom: 24px;
    }

    .calendar-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
    }

    .nav-btn {
      background: none;
      border: none;
      font-size: 20px;
      cursor: pointer;
      padding: 8px;
      border-radius: 4px;
    }

    .nav-btn:hover {
      background: #f0f0f0;
    }

    .calendar-grid {
      display: grid;
      grid-template-columns: repeat(7, 1fr);
      gap: 1px;
      background: #e0e0e0;
      border-radius: 8px;
      overflow: hidden;
    }

    .day-header {
      background: #f5f5f5;
      padding: 8px;
      text-align: center;
      font-size: 12px;
      font-weight: 600;
      color: #666;
    }

    .calendar-day {
      background: white;
      padding: 12px 8px;
      text-align: center;
      cursor: pointer;
      font-size: 14px;
      min-height: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .calendar-day:hover {
      background: #f0f0f0;
    }

    .calendar-day.today {
      background: #1976d2;
      color: white;
    }

    .calendar-day.other-month {
      color: #ccc;
    }

    .calendar-day.has-appointment {
      background: #e3f2fd;
      color: #1976d2;
    }

    .booking-section {
      margin-bottom: 24px;
    }

    .appointment-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 12px 0;
      border-bottom: 1px solid #f0f0f0;
    }

    .appointment-type {
      font-weight: 600;
      color: #333;
    }

    .appointment-client {
      color: #666;
      font-size: 14px;
    }

    .appointment-time {
      font-weight: 600;
      color: #1976d2;
    }

    .new-booking-btn {
      width: 100%;
      background: #1976d2;
      color: white;
      border: none;
      padding: 12px;
      border-radius: 8px;
      font-weight: 600;
      cursor: pointer;
      margin-top: 16px;
    }

    .new-booking-btn:hover {
      background: #1565c0;
    }

    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
    }

    .add-client-btn {
      background: none;
      border: 1px solid #ddd;
      padding: 6px 12px;
      border-radius: 6px;
      font-size: 12px;
      cursor: pointer;
      color: #666;
    }

    .client-list {
      display: flex;
      flex-direction: column;
      gap: 8px;
    }

    .client-item {
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 8px 0;
    }

    .client-status {
      width: 8px;
      height: 8px;
      border-radius: 50%;
      background: #4caf50;
    }

    .client-status.inactive {
      background: #f44336;
    }

    .search-box {
      position: relative;
      width: 250px;
    }

    .search-box input {
      width: 100%;
      padding: 8px 12px 8px 36px;
      border: 1px solid #ddd;
      border-radius: 6px;
      font-size: 14px;
    }

    .search-icon {
      position: absolute;
      left: 12px;
      top: 50%;
      transform: translateY(-50%);
      color: #666;
    }

    .file-categories {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 16px;
      margin-bottom: 24px;
    }

    .file-category {
      display: flex;
      align-items: center;
      gap: 12px;
      padding: 16px;
      border: 1px solid #e0e0e0;
      border-radius: 8px;
      cursor: pointer;
    }

    .file-category:hover {
      background: #f5f5f5;
    }

    .folder-icon {
      font-size: 24px;
    }

    .document-item {
      display: flex;
      align-items: center;
      gap: 12px;
      padding: 12px 0;
      border-bottom: 1px solid #f0f0f0;
    }

    .doc-icon {
      font-size: 20px;
    }

    .doc-info {
      flex: 1;
    }

    .doc-name {
      font-weight: 500;
      color: #333;
    }

    .doc-date {
      font-size: 12px;
      color: #666;
    }

    .doc-action {
      background: none;
      border: none;
      font-size: 16px;
      color: #666;
      cursor: pointer;
    }

    .new-transaction-btn {
      background: #1976d2;
      color: white;
      border: none;
      padding: 8px 16px;
      border-radius: 6px;
      font-size: 14px;
      cursor: pointer;
    }

    .chart-container {
      margin-bottom: 24px;
    }

    .chart-bars {
      display: flex;
      align-items: flex-end;
      gap: 8px;
      height: 120px;
      margin-bottom: 8px;
    }

    .bar {
      flex: 1;
      min-height: 20px;
      border-radius: 4px 4px 0 0;
    }

    .chart-labels {
      display: flex;
      gap: 8px;
      font-size: 12px;
      color: #666;
    }

    .chart-labels span {
      flex: 1;
      text-align: center;
    }

    .chart-period {
      text-align: center;
      font-size: 12px;
      color: #666;
      margin-top: 8px;
    }

    .transaction-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 12px 0;
      border-bottom: 1px solid #f0f0f0;
    }

    .transaction-amount {
      font-weight: 600;
      color: #333;
    }

    .transaction-info {
      text-align: right;
    }

    .transaction-type {
      font-weight: 500;
      color: #333;
    }

    .transaction-date {
      font-size: 12px;
      color: #666;
    }
  `]
})
export class DashboardComponent implements OnInit {
  currentMonth = 'July';
  currentYear = 2025;
  selectedDateString = 'July 1, 2025';
  searchTerm = '';

  dayHeaders = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];

  calendarDays = [
    { date: 30, otherMonth: true, isToday: false, hasAppointment: false },
    { date: 1, otherMonth: false, isToday: true, hasAppointment: true },
    { date: 2, otherMonth: false, isToday: false, hasAppointment: false },
    { date: 3, otherMonth: false, isToday: false, hasAppointment: false },
    { date: 4, otherMonth: false, isToday: false, hasAppointment: false },
    { date: 5, otherMonth: false, isToday: false, hasAppointment: false },
    { date: 6, otherMonth: false, isToday: false, hasAppointment: false },
    { date: 7, otherMonth: false, isToday: false, hasAppointment: false },
    { date: 8, otherMonth: false, isToday: false, hasAppointment: false },
    { date: 9, otherMonth: false, isToday: false, hasAppointment: false },
    { date: 10, otherMonth: false, isToday: false, hasAppointment: false },
    { date: 11, otherMonth: false, isToday: false, hasAppointment: false },
    { date: 12, otherMonth: false, isToday: false, hasAppointment: true },
    { date: 13, otherMonth: false, isToday: false, hasAppointment: false },
    { date: 14, otherMonth: false, isToday: false, hasAppointment: false },
    { date: 15, otherMonth: false, isToday: false, hasAppointment: false },
    { date: 16, otherMonth: false, isToday: false, hasAppointment: false },
    { date: 17, otherMonth: false, isToday: false, hasAppointment: false },
    { date: 18, otherMonth: false, isToday: false, hasAppointment: false },
    { date: 19, otherMonth: false, isToday: false, hasAppointment: false },
    { date: 20, otherMonth: false, isToday: false, hasAppointment: false },
    { date: 21, otherMonth: false, isToday: false, hasAppointment: false },
    { date: 22, otherMonth: false, isToday: false, hasAppointment: false },
    { date: 23, otherMonth: false, isToday: false, hasAppointment: false },
    { date: 24, otherMonth: false, isToday: false, hasAppointment: false },
    { date: 25, otherMonth: false, isToday: false, hasAppointment: false },
    { date: 26, otherMonth: false, isToday: false, hasAppointment: false },
    { date: 27, otherMonth: false, isToday: false, hasAppointment: false },
    { date: 28, otherMonth: false, isToday: false, hasAppointment: false },
    { date: 29, otherMonth: false, isToday: false, hasAppointment: false },
    { date: 30, otherMonth: false, isToday: false, hasAppointment: false },
    { date: 31, otherMonth: false, isToday: false, hasAppointment: false },
    { date: 1, otherMonth: true, isToday: false, hasAppointment: false },
    { date: 2, otherMonth: true, isToday: false, hasAppointment: false },
    { date: 3, otherMonth: true, isToday: false, hasAppointment: false }
  ];

  todayAppointments = [
    { type: 'Consultation', client: 'Kobe Bryant', time: '10:00' },
    { type: 'Consultation', client: 'LeBron James', time: '11:00' }
  ];

  retainerClients = [
    { name: 'Amado Cruz', status: 'active' },
    { name: 'Clara Mendoza', status: 'inactive' },
    { name: 'Kobe Bryant', status: 'active' }
  ];

  recentDocuments = [
    { name: 'Lopez-Contract.pdf', date: 'Updated today' },
    { name: 'Client-Agreement.pdf', date: '4 days ago' }
  ];

  chartData = [
    { height: 60, color: '#e91e63' },
    { height: 40, color: '#e91e63' },
    { height: 80, color: '#e91e63' },
    { height: 30, color: '#e91e63' },
    { height: 70, color: '#e91e63' },
    { height: 50, color: '#e91e63' },
    { height: 90, color: '#e91e63' },
    { height: 45, color: '#e91e63' },
    { height: 85, color: '#e91e63' }
  ];

  chartLabels = ['1', '2', '3', '4', '5', '6', '7', '8', '9'];

  recentTransactions = [
    { amount: '₱15,000', type: 'Consultation', date: 'June 15, 2025' },
    { amount: '₱5,000', type: 'Retainer', date: 'June 16, 2025' }
  ];

  ngOnInit() {
    // Initialize component
  }

  previousMonth() {
    // Implementation for previous month navigation
  }

  nextMonth() {
    // Implementation for next month navigation
  }

  selectDate(day: any) {
    // Implementation for date selection
  }
}
