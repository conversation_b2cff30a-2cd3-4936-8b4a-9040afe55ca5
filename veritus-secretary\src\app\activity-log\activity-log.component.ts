import { Component, OnInit } from '@angular/core';

interface ActivityLogEntry {
  id: string;
  timestamp: Date;
  action: 'create' | 'update' | 'delete' | 'view' | 'download' | 'upload' | 'login' | 'logout';
  category: 'client' | 'file' | 'appointment' | 'transaction' | 'system' | 'security';
  description: string;
  details: string;
  user: string;
  ipAddress?: string;
  userAgent?: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  status: 'success' | 'failed' | 'warning';
}

@Component({
  selector: 'app-activity-log',
  template: `
    <div class="activity-log-container">
      <div class="activity-log-header">
        <h1>Activity Log</h1>
        <div class="header-actions">
          <button mat-raised-button color="primary" (click)="exportLog()">
            <mat-icon>download</mat-icon>
            Export Log
          </button>
          <button mat-icon-button (click)="refreshLog()">
            <mat-icon>refresh</mat-icon>
          </button>
        </div>
      </div>

      <!-- Filters Section -->
      <div class="filters-section">
        <div class="filter-row">
          <mat-form-field appearance="outline" class="filter-field">
            <mat-label>Date Range</mat-label>
            <mat-select [(value)]="selectedDateRange" (selectionChange)="filterActivities()">
              <mat-option value="today">Today</mat-option>
              <mat-option value="week">This Week</mat-option>
              <mat-option value="month">This Month</mat-option>
              <mat-option value="quarter">This Quarter</mat-option>
              <mat-option value="year">This Year</mat-option>
              <mat-option value="all">All Time</mat-option>
            </mat-select>
          </mat-form-field>

          <mat-form-field appearance="outline" class="filter-field">
            <mat-label>Category</mat-label>
            <mat-select [(value)]="selectedCategory" (selectionChange)="filterActivities()">
              <mat-option value="">All Categories</mat-option>
              <mat-option value="client">Client Management</mat-option>
              <mat-option value="file">File Operations</mat-option>
              <mat-option value="appointment">Appointments</mat-option>
              <mat-option value="transaction">Transactions</mat-option>
              <mat-option value="system">System</mat-option>
              <mat-option value="security">Security</mat-option>
            </mat-select>
          </mat-form-field>

          <mat-form-field appearance="outline" class="filter-field">
            <mat-label>Action Type</mat-label>
            <mat-select [(value)]="selectedAction" (selectionChange)="filterActivities()">
              <mat-option value="">All Actions</mat-option>
              <mat-option value="create">Create</mat-option>
              <mat-option value="update">Update</mat-option>
              <mat-option value="delete">Delete</mat-option>
              <mat-option value="view">View</mat-option>
              <mat-option value="download">Download</mat-option>
              <mat-option value="upload">Upload</mat-option>
              <mat-option value="login">Login</mat-option>
              <mat-option value="logout">Logout</mat-option>
            </mat-select>
          </mat-form-field>

          <mat-form-field appearance="outline" class="filter-field">
            <mat-label>Severity</mat-label>
            <mat-select [(value)]="selectedSeverity" (selectionChange)="filterActivities()">
              <mat-option value="">All Levels</mat-option>
              <mat-option value="low">Low</mat-option>
              <mat-option value="medium">Medium</mat-option>
              <mat-option value="high">High</mat-option>
              <mat-option value="critical">Critical</mat-option>
            </mat-select>
          </mat-form-field>
        </div>

        <div class="search-row">
          <mat-form-field appearance="outline" class="search-field">
            <mat-icon matPrefix>search</mat-icon>
            <mat-label>Search activities...</mat-label>
            <input matInput [(ngModel)]="searchTerm" (input)="filterActivities()" placeholder="Search by description, user, or details">
          </mat-form-field>
        </div>
      </div>

      <!-- Statistics Cards -->
      <div class="stats-section">
        <div class="stat-card success">
          <div class="stat-icon">
            <mat-icon>check_circle</mat-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ getActivityCount('success') }}</div>
            <div class="stat-label">Successful Actions</div>
          </div>
        </div>

        <div class="stat-card warning">
          <div class="stat-icon">
            <mat-icon>warning</mat-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ getActivityCount('warning') }}</div>
            <div class="stat-label">Warnings</div>
          </div>
        </div>

        <div class="stat-card error">
          <div class="stat-icon">
            <mat-icon>error</mat-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ getActivityCount('failed') }}</div>
            <div class="stat-label">Failed Actions</div>
          </div>
        </div>

        <div class="stat-card info">
          <div class="stat-icon">
            <mat-icon>info</mat-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ getTotalActivities() }}</div>
            <div class="stat-label">Total Activities</div>
          </div>
        </div>
      </div>

      <!-- Activity Log Table -->
      <div class="log-table-section">
        <div class="table-header">
          <h3>Activity Timeline</h3>
          <div class="table-controls">
            <span class="results-count">{{ filteredActivities.length }} activities found</span>
          </div>
        </div>

        <div class="activity-timeline">
          <div class="activity-entry" *ngFor="let activity of filteredActivities" [class]="activity.severity">
            <div class="activity-indicator" [class]="activity.status">
              <mat-icon>{{ getActivityIcon(activity.action) }}</mat-icon>
            </div>

            <div class="activity-content">
              <div class="activity-header">
                <div class="activity-title">{{ activity.description }}</div>
                <div class="activity-meta">
                  <span class="activity-time">{{ activity.timestamp | date:'MMM dd, yyyy HH:mm:ss' }}</span>
                  <span class="activity-category" [class]="activity.category">{{ activity.category | titlecase }}</span>
                  <span class="activity-severity" [class]="activity.severity">{{ activity.severity | titlecase }}</span>
                </div>
              </div>

              <div class="activity-details">
                <p>{{ activity.details }}</p>
                <div class="activity-user-info">
                  <span class="user-name">
                    <mat-icon>person</mat-icon>
                    {{ activity.user }}
                  </span>
                  <span class="ip-address" *ngIf="activity.ipAddress">
                    <mat-icon>computer</mat-icon>
                    {{ activity.ipAddress }}
                  </span>
                </div>
              </div>
            </div>

            <div class="activity-actions">
              <button mat-icon-button (click)="viewActivityDetails(activity)">
                <mat-icon>visibility</mat-icon>
              </button>
              <button mat-icon-button [matMenuTriggerFor]="activityMenu">
                <mat-icon>more_vert</mat-icon>
              </button>
              <mat-menu #activityMenu="matMenu">
                <button mat-menu-item (click)="exportSingleActivity(activity)">
                  <mat-icon>download</mat-icon>
                  <span>Export Entry</span>
                </button>
                <button mat-menu-item (click)="flagActivity(activity)">
                  <mat-icon>flag</mat-icon>
                  <span>Flag for Review</span>
                </button>
              </mat-menu>
            </div>
          </div>
        </div>

        <!-- Pagination -->
        <div class="pagination-section" *ngIf="filteredActivities.length > pageSize">
          <mat-paginator
            [length]="filteredActivities.length"
            [pageSize]="pageSize"
            [pageSizeOptions]="[10, 25, 50, 100]"
            (page)="onPageChange($event)">
          </mat-paginator>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .activity-log-container {
      padding: 32px;
      background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
      min-height: calc(100vh - 64px);
      position: relative;
    }

    .activity-log-container::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background:
        radial-gradient(circle at 25% 25%, rgba(251, 146, 60, 0.05) 0%, transparent 50%),
        radial-gradient(circle at 75% 75%, rgba(168, 85, 247, 0.03) 0%, transparent 50%);
      pointer-events: none;
    }

    .activity-log-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 40px;
      position: relative;
      z-index: 1;
    }

    .activity-log-header h1 {
      margin: 0;
      font-size: 36px;
      font-weight: 700;
      color: #1e293b;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      position: relative;
    }

    .activity-log-header h1::after {
      content: '';
      position: absolute;
      bottom: -8px;
      left: 0;
      width: 80px;
      height: 4px;
      background: linear-gradient(90deg, #fb923c 0%, #f97316 100%);
      border-radius: 2px;
    }

    .header-actions {
      display: flex;
      gap: 12px;
      align-items: center;
    }

    .filters-section {
      background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
      border-radius: 20px;
      padding: 32px;
      margin-bottom: 32px;
      box-shadow:
        0 20px 60px rgba(0, 0, 0, 0.1),
        0 8px 24px rgba(0, 0, 0, 0.05);
      border: 1px solid rgba(226, 232, 240, 0.8);
      position: relative;
      overflow: hidden;
    }

    .filters-section::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 6px;
      background: linear-gradient(90deg, #fb923c 0%, #f97316 50%, #fb923c 100%);
    }

    .filter-row {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 20px;
      margin-bottom: 20px;
    }

    .search-row {
      display: flex;
      gap: 20px;
    }

    .filter-field,
    .search-field {
      width: 100%;
    }

    .stats-section {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 24px;
      margin-bottom: 32px;
      position: relative;
      z-index: 1;
    }

    .stat-card {
      background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
      border-radius: 20px;
      padding: 28px;
      box-shadow:
        0 20px 60px rgba(0, 0, 0, 0.1),
        0 8px 24px rgba(0, 0, 0, 0.05);
      border: 1px solid rgba(226, 232, 240, 0.8);
      display: flex;
      align-items: center;
      gap: 20px;
      transition: all 0.3s ease;
      position: relative;
      overflow: hidden;
    }

    .stat-card::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 4px;
      transition: transform 0.3s ease;
      transform: scaleX(0);
    }

    .stat-card.success::before {
      background: linear-gradient(90deg, #22c55e 0%, #16a34a 100%);
    }

    .stat-card.warning::before {
      background: linear-gradient(90deg, #f59e0b 0%, #d97706 100%);
    }

    .stat-card.error::before {
      background: linear-gradient(90deg, #ef4444 0%, #dc2626 100%);
    }

    .stat-card.info::before {
      background: linear-gradient(90deg, #3b82f6 0%, #2563eb 100%);
    }

    .stat-card:hover::before {
      transform: scaleX(1);
    }

    .stat-card:hover {
      transform: translateY(-4px);
      box-shadow:
        0 30px 80px rgba(0, 0, 0, 0.15),
        0 12px 32px rgba(0, 0, 0, 0.08);
    }

    .stat-icon {
      width: 60px;
      height: 60px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 28px;
    }

    .stat-card.success .stat-icon {
      background: linear-gradient(135deg, #dcfce7 0%, #bbf7d0 100%);
      color: #16a34a;
    }

    .stat-card.warning .stat-icon {
      background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
      color: #d97706;
    }

    .stat-card.error .stat-icon {
      background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%);
      color: #dc2626;
    }

    .stat-card.info .stat-icon {
      background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
      color: #2563eb;
    }

    .stat-content {
      flex: 1;
    }

    .stat-value {
      font-size: 32px;
      font-weight: 700;
      color: #1e293b;
      margin-bottom: 4px;
    }

    .stat-label {
      font-size: 14px;
      color: #64748b;
      font-weight: 500;
    }

    .log-table-section {
      background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
      border-radius: 20px;
      padding: 32px;
      box-shadow:
        0 20px 60px rgba(0, 0, 0, 0.1),
        0 8px 24px rgba(0, 0, 0, 0.05);
      border: 1px solid rgba(226, 232, 240, 0.8);
      position: relative;
      overflow: hidden;
    }

    .log-table-section::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 6px;
      background: linear-gradient(90deg, #a855f7 0%, #9333ea 50%, #a855f7 100%);
    }

    .table-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 24px;
    }

    .table-header h3 {
      margin: 0;
      font-size: 20px;
      font-weight: 600;
      color: #1e293b;
    }

    .results-count {
      font-size: 14px;
      color: #64748b;
      font-weight: 500;
    }

    .activity-timeline {
      display: flex;
      flex-direction: column;
      gap: 16px;
      margin-bottom: 24px;
    }

    .activity-entry {
      display: flex;
      align-items: flex-start;
      gap: 20px;
      padding: 20px;
      border: 1px solid rgba(226, 232, 240, 0.8);
      border-radius: 16px;
      transition: all 0.3s ease;
      background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
      position: relative;
    }

    .activity-entry:hover {
      border-color: #fb923c;
      box-shadow: 0 8px 24px rgba(251, 146, 60, 0.15);
      transform: translateY(-2px);
    }

    .activity-entry.critical {
      border-left: 4px solid #ef4444;
    }

    .activity-entry.high {
      border-left: 4px solid #f59e0b;
    }

    .activity-entry.medium {
      border-left: 4px solid #3b82f6;
    }

    .activity-entry.low {
      border-left: 4px solid #22c55e;
    }

    .activity-indicator {
      width: 48px;
      height: 48px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-shrink: 0;
    }

    .activity-indicator.success {
      background: linear-gradient(135deg, #dcfce7 0%, #bbf7d0 100%);
      color: #16a34a;
    }

    .activity-indicator.warning {
      background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
      color: #d97706;
    }

    .activity-indicator.failed {
      background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%);
      color: #dc2626;
    }

    .activity-content {
      flex: 1;
    }

    .activity-header {
      margin-bottom: 12px;
    }

    .activity-title {
      font-size: 16px;
      font-weight: 600;
      color: #1e293b;
      margin-bottom: 8px;
    }

    .activity-meta {
      display: flex;
      gap: 16px;
      flex-wrap: wrap;
    }

    .activity-time {
      font-size: 12px;
      color: #64748b;
      font-weight: 500;
    }

    .activity-category,
    .activity-severity {
      font-size: 11px;
      padding: 4px 8px;
      border-radius: 12px;
      font-weight: 600;
      text-transform: uppercase;
    }

    .activity-category.client {
      background: #dbeafe;
      color: #2563eb;
    }

    .activity-category.file {
      background: #f3e8ff;
      color: #9333ea;
    }

    .activity-category.appointment {
      background: #ecfdf5;
      color: #16a34a;
    }

    .activity-category.transaction {
      background: #fef3c7;
      color: #d97706;
    }

    .activity-category.system {
      background: #f1f5f9;
      color: #475569;
    }

    .activity-category.security {
      background: #fee2e2;
      color: #dc2626;
    }

    .activity-severity.low {
      background: #dcfce7;
      color: #16a34a;
    }

    .activity-severity.medium {
      background: #dbeafe;
      color: #2563eb;
    }

    .activity-severity.high {
      background: #fef3c7;
      color: #d97706;
    }

    .activity-severity.critical {
      background: #fee2e2;
      color: #dc2626;
    }

    .activity-details p {
      margin: 0 0 12px 0;
      font-size: 14px;
      color: #475569;
      line-height: 1.5;
    }

    .activity-user-info {
      display: flex;
      gap: 20px;
      align-items: center;
    }

    .user-name,
    .ip-address {
      display: flex;
      align-items: center;
      gap: 6px;
      font-size: 12px;
      color: #64748b;
    }

    .user-name mat-icon,
    .ip-address mat-icon {
      font-size: 16px;
      width: 16px;
      height: 16px;
    }

    .activity-actions {
      display: flex;
      gap: 4px;
      flex-shrink: 0;
    }

    .activity-actions button {
      width: 36px;
      height: 36px;
    }

    .activity-actions mat-icon {
      font-size: 18px;
      width: 18px;
      height: 18px;
    }

    .pagination-section {
      margin-top: 24px;
      display: flex;
      justify-content: center;
    }

    @media (max-width: 768px) {
      .activity-log-header {
        flex-direction: column;
        gap: 16px;
        align-items: stretch;
      }

      .filter-row {
        grid-template-columns: 1fr;
      }

      .stats-section {
        grid-template-columns: 1fr;
      }

      .activity-entry {
        flex-direction: column;
        gap: 16px;
      }

      .activity-meta {
        flex-direction: column;
        gap: 8px;
      }
    }
  `]
})
export class ActivityLogComponent implements OnInit {
  searchTerm = '';
  selectedDateRange = 'month';
  selectedCategory = '';
  selectedAction = '';
  selectedSeverity = '';
  pageSize = 25;
  currentPage = 0;

  activities: ActivityLogEntry[] = [
    {
      id: '1',
      timestamp: new Date(2025, 6, 11, 14, 30, 0), // July 11, 2025 2:30 PM
      action: 'create',
      category: 'client',
      description: 'Created new client profile',
      details: 'Added new retainer client "Maria Santos" with ₱60,000 retainer amount. All required documentation verified.',
      user: 'Manny P. (Secretary)',
      ipAddress: '*************',
      userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
      severity: 'medium',
      status: 'success'
    },
    {
      id: '2',
      timestamp: new Date(2025, 6, 11, 13, 15, 0), // July 11, 2025 1:15 PM
      action: 'upload',
      category: 'file',
      description: 'Uploaded case documents',
      details: 'Uploaded 3 files to Evidence folder: witness-statement.pdf (2.1MB), photo-evidence.zip (15.2MB), expert-report.docx (890KB)',
      user: 'Manny P. (Secretary)',
      ipAddress: '*************',
      severity: 'low',
      status: 'success'
    },
    {
      id: '3',
      timestamp: new Date(2025, 6, 11, 12, 45, 0), // July 11, 2025 12:45 PM
      action: 'update',
      category: 'appointment',
      description: 'Modified appointment schedule',
      details: 'Rescheduled consultation for Kobe Bryant from July 10, 2025 3:00 PM to July 12, 2025 10:00 AM. Client notified via email.',
      user: 'Manny P. (Secretary)',
      ipAddress: '*************',
      severity: 'medium',
      status: 'success'
    },
    {
      id: '4',
      timestamp: new Date(2025, 6, 11, 11, 20, 0), // July 11, 2025 11:20 AM
      action: 'create',
      category: 'transaction',
      description: 'Recorded consultation payment',
      details: 'Added consultation fee payment of ₱15,000 from John Doe. Payment method: Bank Transfer. Reference: TXN-2025-0711-001',
      user: 'Manny P. (Secretary)',
      ipAddress: '*************',
      severity: 'high',
      status: 'success'
    },
    {
      id: '5',
      timestamp: new Date(2025, 6, 11, 10, 30, 0), // July 11, 2025 10:30 AM
      action: 'download',
      category: 'file',
      description: 'Downloaded client contract',
      details: 'Downloaded Lopez-Contract.pdf (2.4MB) for review and client meeting preparation.',
      user: 'Manny P. (Secretary)',
      ipAddress: '*************',
      severity: 'low',
      status: 'success'
    },
    {
      id: '6',
      timestamp: new Date(2025, 6, 11, 9, 15, 0), // July 11, 2025 9:15 AM
      action: 'login',
      category: 'security',
      description: 'User login successful',
      details: 'Secretary Manny P. logged in successfully from IP *************. Session ID: sess_2025071109150001',
      user: 'Manny P. (Secretary)',
      ipAddress: '*************',
      severity: 'low',
      status: 'success'
    },
    {
      id: '7',
      timestamp: new Date(2025, 6, 10, 17, 45, 0), // July 10, 2025 5:45 PM
      action: 'logout',
      category: 'security',
      description: 'User logout',
      details: 'Secretary Manny P. logged out. Session duration: 8 hours 30 minutes. No security incidents detected.',
      user: 'Manny P. (Secretary)',
      ipAddress: '*************',
      severity: 'low',
      status: 'success'
    },
    {
      id: '8',
      timestamp: new Date(2025, 6, 10, 16, 20, 0), // July 10, 2025 4:20 PM
      action: 'update',
      category: 'client',
      description: 'Updated client contact information',
      details: 'Updated phone number for Clara Mendoza from +63 ************ to +63 ************. Email verification sent.',
      user: 'Manny P. (Secretary)',
      ipAddress: '*************',
      severity: 'medium',
      status: 'success'
    },
    {
      id: '9',
      timestamp: new Date(2025, 6, 10, 15, 10, 0), // July 10, 2025 3:10 PM
      action: 'view',
      category: 'file',
      description: 'Accessed confidential documents',
      details: 'Viewed financial disclosure documents for case #2025-0710-001. Access logged for compliance audit.',
      user: 'Manny P. (Secretary)',
      ipAddress: '*************',
      severity: 'high',
      status: 'success'
    },
    {
      id: '10',
      timestamp: new Date(2025, 6, 10, 14, 30, 0), // July 10, 2025 2:30 PM
      action: 'delete',
      category: 'file',
      description: 'Deleted duplicate file',
      details: 'Removed duplicate file "contract-copy-2.pdf" from Contracts folder. Original file retained with proper naming convention.',
      user: 'Manny P. (Secretary)',
      ipAddress: '*************',
      severity: 'medium',
      status: 'success'
    },
    {
      id: '11',
      timestamp: new Date(2025, 6, 9, 16, 45, 0), // July 9, 2025 4:45 PM
      action: 'create',
      category: 'appointment',
      description: 'Scheduled new appointment',
      details: 'Created appointment for Michael Jordan - Case Review on July 12, 2025 at 2:00 PM. Calendar invite sent to all parties.',
      user: 'Manny P. (Secretary)',
      ipAddress: '*************',
      severity: 'medium',
      status: 'success'
    },
    {
      id: '12',
      timestamp: new Date(2025, 6, 9, 11, 20, 0), // July 9, 2025 11:20 AM
      action: 'update',
      category: 'system',
      description: 'System backup completed',
      details: 'Automated daily backup completed successfully. 1.2GB of data backed up to secure cloud storage. Backup verification passed.',
      user: 'System',
      ipAddress: '********',
      severity: 'low',
      status: 'success'
    },
    {
      id: '13',
      timestamp: new Date(2025, 6, 8, 13, 30, 0), // July 8, 2025 1:30 PM
      action: 'view',
      category: 'security',
      description: 'Failed login attempt detected',
      details: 'Multiple failed login attempts detected from IP ************. Account temporarily locked for security. User notified via email.',
      user: 'Security System',
      ipAddress: '************',
      severity: 'critical',
      status: 'warning'
    }
  ];

  filteredActivities: ActivityLogEntry[] = [];

  ngOnInit() {
    this.filteredActivities = [...this.activities];
  }

  filterActivities() {
    let filtered = [...this.activities];

    // Filter by search term
    if (this.searchTerm) {
      const term = this.searchTerm.toLowerCase();
      filtered = filtered.filter(activity =>
        activity.description.toLowerCase().includes(term) ||
        activity.details.toLowerCase().includes(term) ||
        activity.user.toLowerCase().includes(term)
      );
    }

    // Filter by category
    if (this.selectedCategory) {
      filtered = filtered.filter(activity => activity.category === this.selectedCategory);
    }

    // Filter by action
    if (this.selectedAction) {
      filtered = filtered.filter(activity => activity.action === this.selectedAction);
    }

    // Filter by severity
    if (this.selectedSeverity) {
      filtered = filtered.filter(activity => activity.severity === this.selectedSeverity);
    }

    // Filter by date range
    if (this.selectedDateRange !== 'all') {
      const now = new Date();
      let startDate = new Date();

      switch (this.selectedDateRange) {
        case 'today':
          startDate.setHours(0, 0, 0, 0);
          break;
        case 'week':
          startDate.setDate(now.getDate() - 7);
          break;
        case 'month':
          startDate.setMonth(now.getMonth() - 1);
          break;
        case 'quarter':
          startDate.setMonth(now.getMonth() - 3);
          break;
        case 'year':
          startDate.setFullYear(now.getFullYear() - 1);
          break;
      }

      filtered = filtered.filter(activity => activity.timestamp >= startDate);
    }

    this.filteredActivities = filtered;
  }

  getActivityCount(status: string): number {
    return this.filteredActivities.filter(activity => activity.status === status).length;
  }

  getTotalActivities(): number {
    return this.filteredActivities.length;
  }

  getActivityIcon(action: string): string {
    switch (action) {
      case 'create': return 'add_circle';
      case 'update': return 'edit';
      case 'delete': return 'delete';
      case 'view': return 'visibility';
      case 'download': return 'download';
      case 'upload': return 'upload';
      case 'login': return 'login';
      case 'logout': return 'logout';
      default: return 'info';
    }
  }

  exportLog() {
    // Implement log export functionality
    console.log('Exporting activity log...');
  }

  refreshLog() {
    // Implement log refresh functionality
    this.filterActivities();
    console.log('Activity log refreshed');
  }

  viewActivityDetails(activity: ActivityLogEntry) {
    // Implement activity details view
    console.log('Viewing activity details:', activity);
  }

  exportSingleActivity(activity: ActivityLogEntry) {
    // Implement single activity export
    console.log('Exporting activity:', activity);
  }

  flagActivity(activity: ActivityLogEntry) {
    // Implement activity flagging
    console.log('Flagging activity for review:', activity);
  }

  onPageChange(event: any) {
    this.currentPage = event.pageIndex;
    this.pageSize = event.pageSize;
  }
}