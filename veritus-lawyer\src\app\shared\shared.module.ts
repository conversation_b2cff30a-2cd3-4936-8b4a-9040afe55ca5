import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { IonicModule } from '@ionic/angular';

import { VeritusCardComponent } from './components/veritus-card/veritus-card.component';
import { VeritusButtonComponent } from './components/veritus-button/veritus-button.component';
import { VeritusTimelineComponent } from './components/veritus-timeline/veritus-timeline.component';
import { VeritusAvatarComponent } from './components/veritus-avatar/veritus-avatar.component';
import { LawyerCardComponent } from './components/lawyer-card/lawyer-card.component';

@NgModule({
  declarations: [
    VeritusCardComponent,
    VeritusButtonComponent,
    VeritusTimelineComponent,
    VeritusAvatarComponent,
    LawyerCardComponent
  ],
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    IonicModule
  ],
  exports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    IonicModule,
    VeritusCardComponent,
    VeritusButtonComponent,
    VeritusTimelineComponent,
    VeritusAvatarComponent,
    LawyerCardComponent
  ]
})
export class SharedModule { }
