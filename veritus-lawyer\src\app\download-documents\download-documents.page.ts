import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { MockDataService, MockDocument } from '../services/mock-data.service';

@Component({
  selector: 'app-download-documents',
  templateUrl: './download-documents.page.html',
  styleUrls: ['./download-documents.page.scss'],
  standalone: false,
})
export class DownloadDocumentsPage implements OnInit {
  documents: MockDocument[] = [];
  isLoading = true;
  selectedCategory = 'all';

  categories = [
    { id: 'all', name: 'All Documents' },
    { id: 'contracts', name: 'Contracts' },
    { id: 'court', name: 'Court Documents' },
    { id: 'evidence', name: 'Evidence' },
    { id: 'correspondence', name: 'Correspondence' }
  ];

  mockDocuments: MockDocument[] = [
    {
      id: '1',
      name: 'Service Agreement Contract.pdf',
      type: 'PDF',
      size: '2.4 MB',
      uploadDate: new Date('2024-11-15'),
      category: 'contracts',
      caseId: '1'
    },
    {
      id: '2',
      name: 'Court Filing Motion.pdf',
      type: 'PDF',
      size: '1.8 MB',
      uploadDate: new Date('2024-11-20'),
      category: 'court',
      caseId: '1'
    },
    {
      id: '3',
      name: 'Evidence Photos.zip',
      type: 'ZIP',
      size: '15.2 MB',
      uploadDate: new Date('2024-11-25'),
      category: 'evidence',
      caseId: '1'
    },
    {
      id: '4',
      name: 'Client Correspondence.pdf',
      type: 'PDF',
      size: '856 KB',
      uploadDate: new Date('2024-12-01'),
      category: 'correspondence',
      caseId: '1'
    }
  ];

  constructor(
    private router: Router,
    private mockDataService: MockDataService
  ) { }

  ngOnInit() {
    this.loadDocuments();
  }

  loadDocuments() {
    this.isLoading = true;
    // Simulate API call
    setTimeout(() => {
      this.documents = this.mockDocuments;
      this.isLoading = false;
    }, 500);
  }

  get filteredDocuments() {
    if (this.selectedCategory === 'all') {
      return this.documents;
    }
    return this.documents.filter(doc => doc.category === this.selectedCategory);
  }

  onCategoryChange(categoryId: string) {
    this.selectedCategory = categoryId;
  }

  onDownloadDocument(document: MockDocument) {
    console.log('Downloading document:', document.name);
    // TODO: Implement actual download functionality
    // This would typically involve:
    // 1. Get download URL from encrypted storage
    // 2. Decrypt file client-side
    // 3. Trigger browser download
  }

  onBack() {
    this.router.navigate(['/client-tabs/home']);
  }

  getDocumentIcon(type: string): string {
    switch (type.toLowerCase()) {
      case 'pdf': return 'document-text';
      case 'zip': return 'archive';
      case 'doc':
      case 'docx': return 'document';
      case 'jpg':
      case 'jpeg':
      case 'png': return 'image';
      default: return 'document-outline';
    }
  }

  formatDate(date: Date): string {
    return new Intl.DateTimeFormat('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    }).format(new Date(date));
  }
}
