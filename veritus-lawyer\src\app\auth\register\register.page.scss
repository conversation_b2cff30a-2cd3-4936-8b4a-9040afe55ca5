// Modern White & Gold Registration Design
.modern-auth-content {
  --background: #FFFFFF;
  background: #FFFFFF;
  overflow-y: auto;
}

.auth-wrapper {
  position: relative;
  min-height: 100vh;
  display: flex;
  align-items: flex-start;
  justify-content: center;
  padding: 20px;
  overflow-y: auto;
}

// Background Decorative Elements
.background-elements {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  overflow: hidden;
}

.gold-accent-1 {
  position: absolute;
  top: -50px;
  right: -50px;
  width: 200px;
  height: 200px;
  background: linear-gradient(135deg, rgba(196, 154, 86, 0.1) 0%, rgba(196, 154, 86, 0.05) 100%);
  border-radius: 50%;
  filter: blur(40px);
}

.gold-accent-2 {
  position: absolute;
  bottom: -100px;
  left: -100px;
  width: 300px;
  height: 300px;
  background: linear-gradient(45deg, rgba(196, 154, 86, 0.08) 0%, rgba(196, 154, 86, 0.03) 100%);
  border-radius: 50%;
  filter: blur(60px);
}

.gold-accent-3 {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 400px;
  height: 400px;
  background: radial-gradient(circle, rgba(196, 154, 86, 0.02) 0%, transparent 70%);
  border-radius: 50%;
}

// Main Container
.auth-main-container {
  position: relative;
  z-index: 10;
  width: 100%;
  max-width: 420px;
  display: flex;
  flex-direction: column;
  gap: 24px;
  margin: 20px 0 60px 0;
  padding-bottom: 40px;
}

// Logo Section (same as login)
.logo-section {
  text-align: center;
  margin-bottom: 16px;
}

.logo-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
}

.logo-icon {
  width: 72px;
  height: 72px;
  background: linear-gradient(135deg, #C49A56 0%, #B8894A 100%);
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow:
    0 8px 32px rgba(196, 154, 86, 0.3),
    0 4px 16px rgba(196, 154, 86, 0.2);
  position: relative;

  &::before {
    content: '';
    position: absolute;
    inset: 2px;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, transparent 50%);
    border-radius: 18px;
    pointer-events: none;
  }
}

.justice-scale-icon {
  font-size: 36px;
  color: #FFFFFF;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

.brand-title {
  font-size: 36px;
  font-weight: 700;
  background: linear-gradient(135deg, #C49A56 0%, #B8894A 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin: 0;
  letter-spacing: -0.02em;
  text-shadow: 0 2px 4px rgba(196, 154, 86, 0.1);
}

.brand-subtitle {
  font-size: 16px;
  color: #616161;
  margin: 0;
  font-weight: 400;
}

// Form Card
.form-card {
  background: #FFFFFF;
  border-radius: 24px;
  padding: 32px;
  box-shadow:
    0 4px 24px rgba(0, 0, 0, 0.06),
    0 1px 4px rgba(0, 0, 0, 0.04);
  border: 1px solid rgba(196, 154, 86, 0.1);
}

.form-header {
  margin-bottom: 32px;
}

.header-with-back {
  display: flex;
  align-items: flex-start;
  gap: 16px;
}

.back-btn {
  background: rgba(196, 154, 86, 0.1);
  border: none;
  border-radius: 12px;
  width: 44px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  flex-shrink: 0;
  margin-top: 2px;

  ion-icon {
    font-size: 20px;
    color: #C49A56;
  }

  &:hover {
    background: rgba(196, 154, 86, 0.2);
    transform: translateX(-2px);
  }
}

.header-content {
  flex: 1;
  text-align: left;
}

.form-title {
  font-size: 24px;
  font-weight: 700;
  color: #000000;
  margin: 0 0 8px 0;
  letter-spacing: -0.01em;
}

.form-subtitle {
  font-size: 16px;
  color: #616161;
  margin: 0;
  font-weight: 400;
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

.role-badge {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  background: linear-gradient(135deg, rgba(196, 154, 86, 0.1) 0%, rgba(196, 154, 86, 0.05) 100%);
  color: #C49A56;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 600;
  border: 1px solid rgba(196, 154, 86, 0.2);
}

.role-badge-icon {
  font-size: 16px;
}



// Modern Form Styling
.modern-form {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.input-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.input-label {
  font-size: 14px;
  font-weight: 600;
  color: #000000;
  margin: 0;
}

.input-container {
  position: relative;
  display: flex;
  align-items: center;
}

.input-icon {
  position: absolute;
  left: 16px;
  font-size: 20px;
  color: #C49A56;
  z-index: 2;
}

.modern-input {
  width: 100%;
  height: 56px;
  padding: 0 16px 0 48px;
  border: 2px solid rgba(0, 0, 0, 0.08);
  border-radius: 12px;
  font-size: 16px;
  color: #000000;
  background: #FFFFFF;
  transition: all 0.3s ease;
  box-sizing: border-box;

  &::placeholder {
    color: #9E9E9E;
    font-weight: 400;
  }

  &:focus {
    outline: none;
    border-color: #C49A56;
    box-shadow: 0 0 0 4px rgba(196, 154, 86, 0.1);
  }

  &:hover {
    border-color: rgba(196, 154, 86, 0.3);
  }
}

.input-helper {
  font-size: 12px;
  color: #9E9E9E;
  margin: 4px 0 0 0;
  line-height: 1.4;
}

// Password Input with Toggle
.password-input {
  padding-right: 56px !important; // Make room for toggle button
}

.password-toggle-btn {
  position: absolute;
  right: 16px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 2;
  transition: all 0.2s ease;

  &:hover {
    .password-toggle-icon {
      color: #C49A56;
    }
  }

  &:active {
    transform: translateY(-50%) scale(0.95);
  }
}

.password-toggle-icon {
  font-size: 20px;
  color: #9E9E9E;
  transition: color 0.2s ease;
}

// Sign Up Button
.modern-signup-btn {
  width: 100%;
  height: 56px;
  background: linear-gradient(135deg, #C49A56 0%, #B8894A 100%);
  border: none;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 16px rgba(196, 154, 86, 0.3);

  &:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(196, 154, 86, 0.4);
  }

  &:active:not(:disabled) {
    transform: translateY(0);
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
    box-shadow: 0 4px 16px rgba(196, 154, 86, 0.2);
  }
}

.btn-text {
  font-size: 16px;
  font-weight: 600;
  color: #FFFFFF;
}

.btn-icon {
  font-size: 20px;
  color: #FFFFFF;
}

// Divider Section
.divider-section {
  display: flex;
  align-items: center;
  gap: 16px;
  margin: 32px 0;
}

.divider-line {
  flex: 1;
  height: 1px;
  background: rgba(0, 0, 0, 0.08);
}

.divider-text {
  font-size: 14px;
  color: #9E9E9E;
  font-weight: 500;
}

// Google Sign Up Button
.google-signup-btn {
  width: 100%;
  height: 56px;
  background: #FFFFFF;
  border: 1.5px solid #E0E0E0;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    transition: left 0.5s ease;
  }

  &:hover {
    border-color: #C0C0C0;
    box-shadow:
      0 2px 8px rgba(0, 0, 0, 0.1),
      0 1px 4px rgba(0, 0, 0, 0.06);
    transform: translateY(-1px);

    &::before {
      left: 100%;
    }
  }

  &:active {
    transform: translateY(0);
  }

  &:focus {
    outline: none;
    border-color: #4285F4;
    box-shadow:
      0 0 0 3px rgba(66, 133, 244, 0.1),
      0 2px 8px rgba(0, 0, 0, 0.1);
  }
}

.google-icon-container {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
}

.google-logo {
  width: 20px;
  height: 20px;
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
}

.google-text {
  font-size: 16px;
  font-weight: 500;
  color: #3C4043;
  letter-spacing: 0.25px;
}

// Sign In Section
.signin-section {
  text-align: center;
  margin-top: 24px;
  padding-top: 24px;
  border-top: 1px solid rgba(0, 0, 0, 0.06);
}

.signin-text {
  font-size: 14px;
  color: #616161;
  font-weight: 400;
}

.signin-link {
  background: none;
  border: none;
  font-size: 14px;
  color: #C49A56;
  font-weight: 600;
  cursor: pointer;
  padding: 0;
  transition: color 0.2s ease;

  &:hover {
    color: #B8894A;
    text-decoration: underline;
  }
}

// Responsive Design
@media (max-width: 480px) {
  .auth-wrapper {
    padding: 16px;
  }

  .auth-main-container {
    max-width: 100%;
  }

  .form-card {
    padding: 24px;
    border-radius: 20px;
  }

  .logo-icon {
    width: 64px;
    height: 64px;
    border-radius: 16px;

    &::before {
      border-radius: 14px;
    }
  }

  .justice-scale-icon {
    font-size: 32px;
  }

  .brand-title {
    font-size: 30px;
  }

  .form-title {
    font-size: 22px;
  }

  .role-buttons {
    grid-template-columns: 1fr;
    gap: 8px;
  }

  .modern-role-btn {
    flex-direction: row;
    justify-content: flex-start;
    padding: 12px 16px;
    gap: 12px;
  }

  .role-icon {
    font-size: 20px;
  }

  .google-signup-btn {
    gap: 12px;
  }

  .google-text {
    font-size: 15px;
  }
}

// Animation for form elements
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.form-card {
  animation: fadeInUp 0.6s ease-out;
}

.logo-container {
  animation: fadeInUp 0.6s ease-out 0.2s both;
}

.role-selection-section {
  animation: fadeInUp 0.6s ease-out 0.3s both;
}
