{"version": 3, "file": "default-node_modules_ionic_core_dist_esm_form-controller-21dd62b1_js-node_modules_ionic_core_-a176d1.js", "mappings": ";;;;;;;;;;;;;;AAAA;AACA;AACA;AAC2D;;AAE3D;AACA;AACA;AACA;AACA;AACA;AACA,MAAME,0BAA0B,GAAIC,EAAE,IAAK;EACvC,MAAMC,SAAS,GAAGD,EAAE;EACpB,IAAIE,aAAa;EACjB,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;IAC3B,IAAID,aAAa,KAAKE,SAAS,EAAE;MAC7B;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACY,MAAMC,YAAY,GAAGJ,SAAS,CAACK,KAAK,KAAKF,SAAS,IAAIG,YAAY,CAACN,SAAS,CAAC;MAC7E,MAAMO,qBAAqB,GAAGP,SAAS,CAACQ,YAAY,CAAC,YAAY,CAAC;MAC9D;MACCR,SAAS,CAACQ,YAAY,CAAC,iBAAiB,CAAC,IAAIR,SAAS,CAACS,UAAU,KAAK,IAAK;MAChF,MAAMC,eAAe,GAAGb,uDAAa,CAACG,SAAS,CAAC;MAChD;AACZ;AACA;AACA;MACYC,aAAa,GACTD,SAAS,CAACW,MAAM,KAAK,IAAI,IAAK,CAACP,YAAY,IAAI,CAACG,qBAAqB,IAAIG,eAAe,KAAK,IAAK;IAC1G;IACA,OAAOT,aAAa;EACxB,CAAC;EACD,OAAO;IAAEC;EAAiB,CAAC;AAC/B,CAAC;AACD,MAAMI,YAAY,GAAIN,SAAS,IAAK;EAChC;AACJ;AACA;AACA;AACA;EACI,IAAIY,2BAA2B,CAACC,QAAQ,CAACb,SAAS,CAACc,OAAO,CAAC,IAAId,SAAS,CAACe,aAAa,CAAC,gBAAgB,CAAC,KAAK,IAAI,EAAE;IAC/G,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;EACI,IAAIC,6BAA6B,CAACH,QAAQ,CAACb,SAAS,CAACc,OAAO,CAAC,IAAId,SAAS,CAACiB,WAAW,KAAK,EAAE,EAAE;IAC3F,OAAO,IAAI;EACf;EACA,OAAO,KAAK;AAChB,CAAC;AACD,MAAML,2BAA2B,GAAG,CAAC,WAAW,EAAE,cAAc,EAAE,YAAY,EAAE,WAAW,CAAC;AAC5F,MAAMI,6BAA6B,GAAG,CAAC,YAAY,EAAE,cAAc,EAAE,WAAW,CAAC;;;;;;;;;;;;;;;;;;;AC7DjF;AACA;AACA;AAC+C;AACE;AACQ;;AAEzD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMS,4BAA4B,GAAGA,CAAC1B,EAAE,EAAE2B,QAAQ,EAAEC,gBAAgB,KAAK;EACrE,IAAIC,oBAAoB;EACxB,IAAIC,8BAA8B;EAClC,IAAIT,iDAAG,KAAKjB,SAAS,IAAI,uEAAyB,EAAE;IAChD,MAAM2B,KAAK,GAAGC,KAAK,CAACC,OAAO,CAACN,QAAQ,CAAC,GAAGA,QAAQ,GAAG,CAACA,QAAQ,CAAC;IAC7DE,oBAAoB,GAAG,IAAIK,gBAAgB,CAAEC,OAAO,IAAK;MACrD,KAAK,MAAMC,KAAK,IAAID,OAAO,EAAE;QACzB,KAAK,MAAME,IAAI,IAAID,KAAK,CAACE,UAAU,EAAE;UACjC;AACpB;AACA;AACA;UACoB,IAAID,IAAI,CAACE,QAAQ,KAAKC,IAAI,CAACC,YAAY,IAAIV,KAAK,CAACjB,QAAQ,CAACuB,IAAI,CAACK,IAAI,CAAC,EAAE;YAClE;AACxB;AACA;AACA;AACA;YACwBd,gBAAgB,CAAC,CAAC;YAClB;AACxB;AACA;AACA;AACA;AACA;YACwBL,uDAAG,CAAC,MAAMoB,kBAAkB,CAACN,IAAI,CAAC,CAAC;YACnC;UACJ;QACJ;MACJ;IACJ,CAAC,CAAC;IACFR,oBAAoB,CAACe,OAAO,CAAC5C,EAAE,EAAE;MAC7B6C,SAAS,EAAE;IACf,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,MAAMF,kBAAkB,GAAIG,SAAS,IAAK;IACtC,IAAIC,EAAE;IACN,IAAIjB,8BAA8B,EAAE;MAChCA,8BAA8B,CAACkB,UAAU,CAAC,CAAC;MAC3ClB,8BAA8B,GAAG1B,SAAS;IAC9C;IACA0B,8BAA8B,GAAG,IAAII,gBAAgB,CAAEC,OAAO,IAAK;MAC/DP,gBAAgB,CAAC,CAAC;MAClB,KAAK,MAAMQ,KAAK,IAAID,OAAO,EAAE;QACzB,KAAK,MAAME,IAAI,IAAID,KAAK,CAACa,YAAY,EAAE;UACnC;AACpB;AACA;AACA;AACA;UACoB,IAAIZ,IAAI,CAACE,QAAQ,KAAKC,IAAI,CAACC,YAAY,IAAIJ,IAAI,CAACK,IAAI,KAAKf,QAAQ,EAAE;YAC/DuB,6BAA6B,CAAC,CAAC;UACnC;QACJ;MACJ;IACJ,CAAC,CAAC;IACF;AACR;AACA;AACA;AACA;AACA;IACQpB,8BAA8B,CAACc,OAAO,CAAC,CAACG,EAAE,GAAGD,SAAS,CAACK,aAAa,MAAM,IAAI,IAAIJ,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAGD,SAAS,EAAE;MAAEM,OAAO,EAAE,IAAI;MAAEP,SAAS,EAAE;IAAK,CAAC,CAAC;EACzJ,CAAC;EACD,MAAMQ,OAAO,GAAGA,CAAA,KAAM;IAClB,IAAIxB,oBAAoB,EAAE;MACtBA,oBAAoB,CAACmB,UAAU,CAAC,CAAC;MACjCnB,oBAAoB,GAAGzB,SAAS;IACpC;IACA8C,6BAA6B,CAAC,CAAC;EACnC,CAAC;EACD,MAAMA,6BAA6B,GAAGA,CAAA,KAAM;IACxC,IAAIpB,8BAA8B,EAAE;MAChCA,8BAA8B,CAACkB,UAAU,CAAC,CAAC;MAC3ClB,8BAA8B,GAAG1B,SAAS;IAC9C;EACJ,CAAC;EACD,OAAO;IACHiD;EACJ,CAAC;AACL,CAAC;AAED,MAAMC,cAAc,GAAGA,CAACC,KAAK,EAAEC,SAAS,EAAEC,gBAAgB,KAAK;EAC3D,MAAMC,WAAW,GAAGH,KAAK,IAAI,IAAI,GAAG,CAAC,GAAGA,KAAK,CAACI,QAAQ,CAAC,CAAC,CAACC,MAAM;EAC/D,MAAMC,kBAAkB,GAAGC,uBAAuB,CAACJ,WAAW,EAAEF,SAAS,CAAC;EAC1E;AACJ;AACA;AACA;EACI,IAAIC,gBAAgB,KAAKrD,SAAS,EAAE;IAChC,OAAOyD,kBAAkB;EAC7B;EACA;AACJ;AACA;AACA;AACA;EACI,IAAI;IACA,OAAOJ,gBAAgB,CAACC,WAAW,EAAEF,SAAS,CAAC;EACnD,CAAC,CACD,OAAOO,CAAC,EAAE;IACNtC,qDAAa,CAAC,2CAA2C,EAAEsC,CAAC,CAAC;IAC7D,OAAOF,kBAAkB;EAC7B;AACJ,CAAC;AACD,MAAMC,uBAAuB,GAAGA,CAACF,MAAM,EAAEI,SAAS,KAAK;EACnD,OAAO,GAAGJ,MAAM,MAAMI,SAAS,EAAE;AACrC,CAAC;;;;;;;;;;;;;;;;;ACpID;AACA;AACA;AAC+C;AACE;;AAEjD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAME,qBAAqB,GAAGA,CAAClE,EAAE,EAAEmE,gBAAgB,EAAEC,YAAY,KAAK;EAClE,IAAIC,iBAAiB;EACrB,MAAMC,uBAAuB,GAAGA,CAAA,KAAM;IAClC,MAAMC,aAAa,GAAGJ,gBAAgB,CAAC,CAAC;IACxC;IACA;AACR;AACA;AACA;IACQI,aAAa,KAAKnE,SAAS;IACvB;AACZ;AACA;AACA;AACA;IACYJ,EAAE,CAACM,KAAK,KAAKF,SAAS,IACtBgE,YAAY,CAAC,CAAC,KAAK,IAAI,EAAE;MACzB,OAAO,KAAK;IAChB;IACA,OAAO,IAAI;EACf,CAAC;EACD,MAAMI,mBAAmB,GAAGA,CAAA,KAAM;IAC9B,IAAIF,uBAAuB,CAAC,CAAC,EAAE;MAC3B;AACZ;AACA;AACA;AACA;AACA;MACY/C,uDAAG,CAAC,MAAM;QACNkD,aAAa,CAAC,CAAC;MACnB,CAAC,CAAC;IACN;EACJ,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,MAAMA,aAAa,GAAGA,CAAA,KAAM;IACxB,MAAMF,aAAa,GAAGJ,gBAAgB,CAAC,CAAC;IACxC,IAAII,aAAa,KAAKnE,SAAS,EAAE;MAC7B;IACJ;IACA,IAAI,CAACkE,uBAAuB,CAAC,CAAC,EAAE;MAC5BC,aAAa,CAACG,KAAK,CAACC,cAAc,CAAC,OAAO,CAAC;MAC3C;IACJ;IACA,MAAMC,KAAK,GAAGR,YAAY,CAAC,CAAC,CAACS,WAAW;IACxC;IACA;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQD,KAAK,KAAK,CAAC,IACPL,aAAa,CAACO,YAAY,KAAK,IAAI,IACnCzD,iDAAG,KAAKjB,SAAS,IACjB,2EAA6B,EAAE;MAC/B;AACZ;AACA;AACA;AACA;AACA;AACA;MACY,IAAIiE,iBAAiB,KAAKjE,SAAS,EAAE;QACjC;MACJ;MACA,MAAM2E,EAAE,GAAIV,iBAAiB,GAAG,IAAIW,oBAAoB,CAAEC,EAAE,IAAK;QAC7D;AAChB;AACA;AACA;QACgB,IAAIA,EAAE,CAAC,CAAC,CAAC,CAACC,iBAAiB,KAAK,CAAC,EAAE;UAC/BT,aAAa,CAAC,CAAC;UACfM,EAAE,CAAC/B,UAAU,CAAC,CAAC;UACfqB,iBAAiB,GAAGjE,SAAS;QACjC;MACJ,CAAC;MACD;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACY;QAAE+E,SAAS,EAAE,IAAI;QAAEC,IAAI,EAAEpF;MAAG,CAAC,CAAE;MAC/B+E,EAAE,CAACnC,OAAO,CAAC2B,aAAa,CAAC;MACzB;IACJ;IACA;AACR;AACA;AACA;AACA;AACA;AACA;IACQA,aAAa,CAACG,KAAK,CAACW,WAAW,CAAC,OAAO,EAAE,GAAGT,KAAK,GAAG,IAAI,IAAI,CAAC;EACjE,CAAC;EACD,MAAMvB,OAAO,GAAGA,CAAA,KAAM;IAClB,IAAIgB,iBAAiB,EAAE;MACnBA,iBAAiB,CAACrB,UAAU,CAAC,CAAC;MAC9BqB,iBAAiB,GAAGjE,SAAS;IACjC;EACJ,CAAC;EACD,OAAO;IACHoE,mBAAmB;IACnBnB;EACJ,CAAC;AACL,CAAC", "sources": ["./node_modules/@ionic/core/dist/esm/form-controller-21dd62b1.js", "./node_modules/@ionic/core/dist/esm/input.utils-a445f677.js", "./node_modules/@ionic/core/dist/esm/notch-controller-6bd3e0f9.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { h as findItemLabel } from './helpers-be245865.js';\n\n/**\n * Creates a controller that tracks whether a form control is using the legacy or modern syntax. This should be removed when the legacy form control syntax is removed.\n *\n * @internal\n * @prop el: The Ionic form component to reference\n */\nconst createLegacyFormController = (el) => {\n    const controlEl = el;\n    let legacyControl;\n    const hasLegacyControl = () => {\n        if (legacyControl === undefined) {\n            /**\n             * Detect if developers are using the legacy form control syntax\n             * so a deprecation warning is logged. This warning can be disabled\n             * by either using the new `label` property or setting `aria-label`\n             * on the control.\n             * Alternatively, components that use a slot for the label\n             * can check to see if the component has slotted text\n             * in the light DOM.\n             */\n            const hasLabelProp = controlEl.label !== undefined || hasLabelSlot(controlEl);\n            const hasAriaLabelAttribute = controlEl.hasAttribute('aria-label') ||\n                // Shadow DOM form controls cannot use aria-labelledby\n                (controlEl.hasAttribute('aria-labelledby') && controlEl.shadowRoot === null);\n            const legacyItemLabel = findItemLabel(controlEl);\n            /**\n             * Developers can manually opt-out of the modern form markup\n             * by setting `legacy=\"true\"` on components.\n             */\n            legacyControl =\n                controlEl.legacy === true || (!hasLabelProp && !hasAriaLabelAttribute && legacyItemLabel !== null);\n        }\n        return legacyControl;\n    };\n    return { hasLegacyControl };\n};\nconst hasLabelSlot = (controlEl) => {\n    /**\n     * Components that have a named label slot\n     * also have other slots, so we need to query for\n     * anything that is explicitly passed to slot=\"label\"\n     */\n    if (NAMED_LABEL_SLOT_COMPONENTS.includes(controlEl.tagName) && controlEl.querySelector('[slot=\"label\"]') !== null) {\n        return true;\n    }\n    /**\n     * Components that have an unnamed slot for the label\n     * have no other slots, so we can check the textContent\n     * of the element.\n     */\n    if (UNNAMED_LABEL_SLOT_COMPONENTS.includes(controlEl.tagName) && controlEl.textContent !== '') {\n        return true;\n    }\n    return false;\n};\nconst NAMED_LABEL_SLOT_COMPONENTS = ['ION-INPUT', 'ION-TEXTAREA', 'ION-SELECT', 'ION-RANGE'];\nconst UNNAMED_LABEL_SLOT_COMPONENTS = ['ION-TOGGLE', 'ION-CHECKBOX', 'ION-RADIO'];\n\nexport { createLegacyFormController as c };\n", "/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { w as win } from './index-a5d50daf.js';\nimport { r as raf } from './helpers-be245865.js';\nimport { a as printIonError } from './index-9b0d46f4.js';\n\n/**\n * Used to update a scoped component that uses emulated slots. This fires when\n * content is passed into the slot or when the content inside of a slot changes.\n * This is not needed for components using native slots in the Shadow DOM.\n * @internal\n * @param el The host element to observe\n * @param slotName mutationCallback will fire when nodes on these slot(s) change\n * @param mutationCallback The callback to fire whenever the slotted content changes\n */\nconst createSlotMutationController = (el, slotName, mutationCallback) => {\n    let hostMutationObserver;\n    let slottedContentMutationObserver;\n    if (win !== undefined && 'MutationObserver' in win) {\n        const slots = Array.isArray(slotName) ? slotName : [slotName];\n        hostMutationObserver = new MutationObserver((entries) => {\n            for (const entry of entries) {\n                for (const node of entry.addedNodes) {\n                    /**\n                     * Check to see if the added node\n                     *  is our slotted content.\n                     */\n                    if (node.nodeType === Node.ELEMENT_NODE && slots.includes(node.slot)) {\n                        /**\n                         * If so, we want to watch the slotted\n                         * content itself for changes. This lets us\n                         * detect when content inside of the slot changes.\n                         */\n                        mutationCallback();\n                        /**\n                         * Adding the listener in an raf\n                         * waits until Stencil moves the slotted element\n                         * into the correct place in the event that\n                         * slotted content is being added.\n                         */\n                        raf(() => watchForSlotChange(node));\n                        return;\n                    }\n                }\n            }\n        });\n        hostMutationObserver.observe(el, {\n            childList: true,\n        });\n    }\n    /**\n     * Listen for changes inside of the slotted content.\n     * We can listen for subtree changes here to be\n     * informed of text within the slotted content\n     * changing. Doing this on the host is possible\n     * but it is much more expensive to do because\n     * it also listens for changes to the internals\n     * of the component.\n     */\n    const watchForSlotChange = (slottedEl) => {\n        var _a;\n        if (slottedContentMutationObserver) {\n            slottedContentMutationObserver.disconnect();\n            slottedContentMutationObserver = undefined;\n        }\n        slottedContentMutationObserver = new MutationObserver((entries) => {\n            mutationCallback();\n            for (const entry of entries) {\n                for (const node of entry.removedNodes) {\n                    /**\n                     * If the element was removed then we\n                     * need to destroy the MutationObserver\n                     * so the element can be garbage collected.\n                     */\n                    if (node.nodeType === Node.ELEMENT_NODE && node.slot === slotName) {\n                        destroySlottedContentObserver();\n                    }\n                }\n            }\n        });\n        /**\n         * Listen for changes inside of the element\n         * as well as anything deep in the tree.\n         * We listen on the parentElement so that we can\n         * detect when slotted element itself is removed.\n         */\n        slottedContentMutationObserver.observe((_a = slottedEl.parentElement) !== null && _a !== void 0 ? _a : slottedEl, { subtree: true, childList: true });\n    };\n    const destroy = () => {\n        if (hostMutationObserver) {\n            hostMutationObserver.disconnect();\n            hostMutationObserver = undefined;\n        }\n        destroySlottedContentObserver();\n    };\n    const destroySlottedContentObserver = () => {\n        if (slottedContentMutationObserver) {\n            slottedContentMutationObserver.disconnect();\n            slottedContentMutationObserver = undefined;\n        }\n    };\n    return {\n        destroy,\n    };\n};\n\nconst getCounterText = (value, maxLength, counterFormatter) => {\n    const valueLength = value == null ? 0 : value.toString().length;\n    const defaultCounterText = defaultCounterFormatter(valueLength, maxLength);\n    /**\n     * If developers did not pass a custom formatter,\n     * use the default one.\n     */\n    if (counterFormatter === undefined) {\n        return defaultCounterText;\n    }\n    /**\n     * Otherwise, try to use the custom formatter\n     * and fallback to the default formatter if\n     * there was an error.\n     */\n    try {\n        return counterFormatter(valueLength, maxLength);\n    }\n    catch (e) {\n        printIonError('Exception in provided `counterFormatter`.', e);\n        return defaultCounterText;\n    }\n};\nconst defaultCounterFormatter = (length, maxlength) => {\n    return `${length} / ${maxlength}`;\n};\n\nexport { createSlotMutationController as c, getCounterText as g };\n", "/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { w as win } from './index-a5d50daf.js';\nimport { r as raf } from './helpers-be245865.js';\n\n/**\n * A utility to calculate the size of an outline notch\n * width relative to the content passed. This is used in\n * components such as `ion-select` with `fill=\"outline\"`\n * where we need to pass slotted HTML content. This is not\n * needed when rendering plaintext content because we can\n * render the plaintext again hidden with `opacity: 0` inside\n * of the notch. As a result we can rely on the intrinsic size\n * of the element to correctly compute the notch width. We\n * cannot do this with slotted content because we cannot project\n * it into 2 places at once.\n *\n * @internal\n * @param el: The host element\n * @param getNotchSpacerEl: A function that returns a reference to the notch spacer element inside of the component template.\n * @param getLabelSlot: A function that returns a reference to the slotted content.\n */\nconst createNotchController = (el, getNotchSpacerEl, getLabelSlot) => {\n    let notchVisibilityIO;\n    const needsExplicitNotchWidth = () => {\n        const notchSpacerEl = getNotchSpacerEl();\n        if (\n        /**\n         * If the notch is not being used\n         * then we do not need to set the notch width.\n         */\n        notchSpacerEl === undefined ||\n            /**\n             * If either the label property is being\n             * used or the label slot is not defined,\n             * then we do not need to estimate the notch width.\n             */\n            el.label !== undefined ||\n            getLabelSlot() === null) {\n            return false;\n        }\n        return true;\n    };\n    const calculateNotchWidth = () => {\n        if (needsExplicitNotchWidth()) {\n            /**\n             * Run this the frame after\n             * the browser has re-painted the host element.\n             * Otherwise, the label element may have a width\n             * of 0 and the IntersectionObserver will be used.\n             */\n            raf(() => {\n                setNotchWidth();\n            });\n        }\n    };\n    /**\n     * When using a label prop we can render\n     * the label value inside of the notch and\n     * let the browser calculate the size of the notch.\n     * However, we cannot render the label slot in multiple\n     * places so we need to manually calculate the notch dimension\n     * based on the size of the slotted content.\n     *\n     * This function should only be used to set the notch width\n     * on slotted label content. The notch width for label prop\n     * content is automatically calculated based on the\n     * intrinsic size of the label text.\n     */\n    const setNotchWidth = () => {\n        const notchSpacerEl = getNotchSpacerEl();\n        if (notchSpacerEl === undefined) {\n            return;\n        }\n        if (!needsExplicitNotchWidth()) {\n            notchSpacerEl.style.removeProperty('width');\n            return;\n        }\n        const width = getLabelSlot().scrollWidth;\n        if (\n        /**\n         * If the computed width of the label is 0\n         * and notchSpacerEl's offsetParent is null\n         * then that means the element is hidden.\n         * As a result, we need to wait for the element\n         * to become visible before setting the notch width.\n         *\n         * We do not check el.offsetParent because\n         * that can be null if the host element has\n         * position: fixed applied to it.\n         * notchSpacerEl does not have position: fixed.\n         */\n        width === 0 &&\n            notchSpacerEl.offsetParent === null &&\n            win !== undefined &&\n            'IntersectionObserver' in win) {\n            /**\n             * If there is an IO already attached\n             * then that will update the notch\n             * once the element becomes visible.\n             * As a result, there is no need to create\n             * another one.\n             */\n            if (notchVisibilityIO !== undefined) {\n                return;\n            }\n            const io = (notchVisibilityIO = new IntersectionObserver((ev) => {\n                /**\n                 * If the element is visible then we\n                 * can try setting the notch width again.\n                 */\n                if (ev[0].intersectionRatio === 1) {\n                    setNotchWidth();\n                    io.disconnect();\n                    notchVisibilityIO = undefined;\n                }\n            }, \n            /**\n             * Set the root to be the host element\n             * This causes the IO callback\n             * to be fired in WebKit as soon as the element\n             * is visible. If we used the default root value\n             * then WebKit would only fire the IO callback\n             * after any animations (such as a modal transition)\n             * finished, and there would potentially be a flicker.\n             */\n            { threshold: 0.01, root: el }));\n            io.observe(notchSpacerEl);\n            return;\n        }\n        /**\n         * If the element is visible then we can set the notch width.\n         * The notch is only visible when the label is scaled,\n         * which is why we multiply the width by 0.75 as this is\n         * the same amount the label element is scaled by in the host CSS.\n         * (See $form-control-label-stacked-scale in ionic.globals.scss).\n         */\n        notchSpacerEl.style.setProperty('width', `${width * 0.75}px`);\n    };\n    const destroy = () => {\n        if (notchVisibilityIO) {\n            notchVisibilityIO.disconnect();\n            notchVisibilityIO = undefined;\n        }\n    };\n    return {\n        calculateNotchWidth,\n        destroy,\n    };\n};\n\nexport { createNotchController as c };\n"], "names": ["h", "findItemLabel", "createLegacyFormController", "el", "controlEl", "legacyControl", "hasLegacyControl", "undefined", "hasLabelProp", "label", "hasLabelSlot", "hasAriaLabelAttribute", "hasAttribute", "shadowRoot", "legacyItemLabel", "legacy", "NAMED_LABEL_SLOT_COMPONENTS", "includes", "tagName", "querySelector", "UNNAMED_LABEL_SLOT_COMPONENTS", "textContent", "c", "w", "win", "r", "raf", "a", "printIonError", "createSlotMutationController", "slotName", "mutationCallback", "hostMutationObserver", "slottedContentMutationObserver", "slots", "Array", "isArray", "MutationObserver", "entries", "entry", "node", "addedNodes", "nodeType", "Node", "ELEMENT_NODE", "slot", "watchForSlotChange", "observe", "childList", "slottedEl", "_a", "disconnect", "removedNodes", "destroySlottedContentObserver", "parentElement", "subtree", "destroy", "getCounterText", "value", "max<PERSON><PERSON><PERSON>", "counterFormatter", "valueLength", "toString", "length", "defaultCounterText", "defaultCounterFormatter", "e", "maxlength", "g", "createNotchController", "getNotchSpacerEl", "getLabelSlot", "notchVisibilityIO", "needsExplicitNotchWidth", "notchSpacerEl", "calculateNotchWidth", "set<PERSON><PERSON>chWidth", "style", "removeProperty", "width", "scrollWidth", "offsetParent", "io", "IntersectionObserver", "ev", "intersectionRatio", "threshold", "root", "setProperty"], "sourceRoot": "webpack:///", "x_google_ignoreList": [0, 1, 2]}