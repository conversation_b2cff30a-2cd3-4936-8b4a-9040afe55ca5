name: Deploy Veritus Platform

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

env:
  NODE_VERSION: '18'
  FIREBASE_PROJECT_ID: 'veritus-620ca'

jobs:
  test:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        
    - name: Install dependencies
      run: npm ci
      
    - name: Run linting
      run: npm run lint
      
    - name: Run tests
      run: npm run test -- --watch=false --browsers=ChromeHeadless
      
    - name: Build mobile app
      run: npm run build
      
    - name: Build secretary portal
      run: |
        cd ../veritus-secretary
        npm ci
        npm run build

  deploy-staging:
    needs: test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        
    - name: Install dependencies
      run: npm ci
      
    - name: Build mobile app
      run: npm run build
      
    - name: Build secretary portal
      run: |
        cd ../veritus-secretary
        npm ci
        npm run build
        
    - name: Install Firebase CLI
      run: npm install -g firebase-tools
      
    - name: Deploy to Firebase Staging
      run: |
        echo "${{ secrets.FIREBASE_SERVICE_ACCOUNT }}" > firebase-service-account.json
        firebase use staging --token "${{ secrets.FIREBASE_TOKEN }}"
        firebase deploy --only hosting:mobile,hosting:secretary --token "${{ secrets.FIREBASE_TOKEN }}"
      env:
        FIREBASE_TOKEN: ${{ secrets.FIREBASE_TOKEN }}

  deploy-functions:
    needs: test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        
    - name: Install Firebase CLI
      run: npm install -g firebase-tools
      
    - name: Install Functions dependencies
      run: |
        cd functions
        npm ci
        
    - name: Deploy Cloud Functions
      run: |
        echo "${{ secrets.FIREBASE_SERVICE_ACCOUNT }}" > firebase-service-account.json
        firebase use staging --token "${{ secrets.FIREBASE_TOKEN }}"
        firebase deploy --only functions --token "${{ secrets.FIREBASE_TOKEN }}"
      env:
        FIREBASE_TOKEN: ${{ secrets.FIREBASE_TOKEN }}

  deploy-firestore:
    needs: test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Install Firebase CLI
      run: npm install -g firebase-tools
      
    - name: Deploy Firestore Rules and Indexes
      run: |
        echo "${{ secrets.FIREBASE_SERVICE_ACCOUNT }}" > firebase-service-account.json
        firebase use staging --token "${{ secrets.FIREBASE_TOKEN }}"
        firebase deploy --only firestore:rules,firestore:indexes,storage --token "${{ secrets.FIREBASE_TOKEN }}"
      env:
        FIREBASE_TOKEN: ${{ secrets.FIREBASE_TOKEN }}

  deploy-production:
    needs: [deploy-staging, deploy-functions, deploy-firestore]
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    environment: production
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        
    - name: Install dependencies
      run: npm ci
      
    - name: Build mobile app for production
      run: npm run build -- --configuration=production
      
    - name: Build secretary portal for production
      run: |
        cd ../veritus-secretary
        npm ci
        npm run build -- --configuration=production
        
    - name: Install Firebase CLI
      run: npm install -g firebase-tools
      
    - name: Deploy to Production
      run: |
        echo "${{ secrets.FIREBASE_SERVICE_ACCOUNT_PROD }}" > firebase-service-account.json
        firebase use production --token "${{ secrets.FIREBASE_TOKEN_PROD }}"
        firebase deploy --token "${{ secrets.FIREBASE_TOKEN_PROD }}"
      env:
        FIREBASE_TOKEN_PROD: ${{ secrets.FIREBASE_TOKEN_PROD }}

  security-scan:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Run security audit
      run: npm audit --audit-level high
      
    - name: Check for vulnerabilities
      run: |
        npm audit --json > audit-results.json
        if [ $(cat audit-results.json | jq '.metadata.vulnerabilities.high + .metadata.vulnerabilities.critical') -gt 0 ]; then
          echo "High or critical vulnerabilities found!"
          exit 1
        fi
