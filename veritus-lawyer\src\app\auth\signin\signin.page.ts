import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { FirebaseService } from '../../services/firebase.service';
import { LoadingController, AlertController } from '@ionic/angular';

@Component({
  selector: 'app-signin',
  templateUrl: './signin.page.html',
  styleUrls: ['./signin.page.scss'],
  standalone: false,
})
export class SignInPage implements OnInit {
  signinForm: FormGroup;
  showPassword = false;

  constructor(
    private formBuilder: FormBuilder,
    private router: Router,
    private firebaseService: FirebaseService,
    private loadingController: LoadingController,
    private alertController: AlertController
  ) {
    this.signinForm = this.formBuilder.group({
      email: ['', [Validators.required, Validators.email]],
      password: ['', [Validators.required, Validators.minLength(6)]]
    });
  }

  ngOnInit() {}

  async onSignIn() {
    if (this.signinForm.valid) {
      const loading = await this.loadingController.create({
        message: 'Signing in...',
        spinner: 'crescent'
      });
      await loading.present();

      try {
        const email = this.signinForm.get('email')?.value;
        const password = this.signinForm.get('password')?.value;

        // Development mode bypass for testing
        if (email === '<EMAIL>' && password === 'password123') {
          await loading.dismiss();
          // Mock successful lawyer login
          localStorage.setItem('userRole', 'lawyer');
          localStorage.setItem('isAuthenticated', 'true');
          localStorage.setItem('userName', 'Jerome Rodriguez');
          localStorage.setItem('userEmail', email);
          this.router.navigate(['/tabs']);
          return;
        }



        if (email === '<EMAIL>' && password === 'password123') {
          await loading.dismiss();
          // Mock successful client login
          localStorage.setItem('userRole', 'client');
          localStorage.setItem('isAuthenticated', 'true');
          this.router.navigate(['/client-tabs']);
          return;
        }

        const user = await this.firebaseService.signIn(email, password);

        // Determine user role and redirect accordingly
        const lawyerProfile = await this.firebaseService.getLawyerProfile(user.uid);
        const clientProfile = await this.firebaseService.getClientProfile(user.uid);

        await loading.dismiss();

        if (lawyerProfile) {
          this.router.navigate(['/tabs']);
        } else if (clientProfile) {
          this.router.navigate(['/client-tabs']);
        } else {
          // Fallback to tabs if role cannot be determined
          this.router.navigate(['/tabs']);
        }
      } catch (error: any) {
        await loading.dismiss();
        await this.showErrorAlert('Sign In Failed', error.message || 'Please check your credentials and try again.');
      }
    }
  }

  onGoogleSignIn() {
    // Mock Google OAuth for development
    localStorage.setItem('userRole', 'lawyer');
    localStorage.setItem('isAuthenticated', 'true');
    localStorage.setItem('userName', 'Jerome Rodriguez');
    localStorage.setItem('userEmail', '<EMAIL>');
    this.router.navigate(['/tabs']);
  }

  goToRegister() {
    this.router.navigate(['/auth/role-selection']);
  }

  goToForgotPassword() {
    this.router.navigate(['/auth/forgot-password']);
  }

  togglePasswordVisibility() {
    this.showPassword = !this.showPassword;
  }

  private async showErrorAlert(header: string, message: string) {
    const alert = await this.alertController.create({
      header,
      message,
      buttons: ['OK']
    });
    await alert.present();
  }
}
