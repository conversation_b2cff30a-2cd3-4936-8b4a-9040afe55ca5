import { Component, OnInit } from '@angular/core';
import { Router, NavigationEnd } from '@angular/router';
import { filter } from 'rxjs/operators';
import { AdminAuthService } from './services/admin-auth.service';

@Component({
  selector: 'app-root',
  templateUrl: './app.component.html',
  styleUrls: ['./app.component.scss']
})
export class AppComponent implements OnInit {
  title = 'Veritus Admin';
  currentRoute = '';

  constructor(
    private router: Router,
    private adminAuthService: AdminAuthService
  ) {
    this.router.events
      .pipe(filter(event => event instanceof NavigationEnd))
      .subscribe((event) => {
        if (event instanceof NavigationEnd) {
          this.currentRoute = event.url;
        }
      });
  }

  ngOnInit(): void {
    // The auth service will automatically restore session on initialization
    // This ensures the authentication state is available immediately
  }

  isLoginPage(): boolean {
    return this.currentRoute === '/login' || this.currentRoute === '/' || this.currentRoute === '/setup';
  }
}
