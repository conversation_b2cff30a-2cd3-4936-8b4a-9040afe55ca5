.admin-header {
  height: 70px;
  background: white;
  border-bottom: 1px solid #e1e5e9;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 30px;
  position: sticky;
  top: 0;
  z-index: 999;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.header-left {
  display: flex;
  align-items: center;
  
  .mobile-menu-btn {
    display: none;
    background: none;
    border: none;
    cursor: pointer;
    margin-right: 15px;
    
    .hamburger {
      display: block;
      width: 20px;
      height: 2px;
      background: #333;
      position: relative;
      
      &::before,
      &::after {
        content: '';
        position: absolute;
        width: 20px;
        height: 2px;
        background: #333;
        left: 0;
      }
      
      &::before {
        top: -6px;
      }
      
      &::after {
        top: 6px;
      }
    }
  }
  
  .page-title {
    margin: 0;
    font-size: 1.5rem;
    font-weight: 600;
    color: #333;
  }
}

.header-right {
  display: flex;
  align-items: center;
  gap: 20px;
}

.search-container {
  position: relative;
  
  .search-input {
    width: 300px;
    padding: 10px 40px 10px 15px;
    border: 1px solid #e1e5e9;
    border-radius: 25px;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    
    &:focus {
      outline: none;
      border-color: var(--admin-primary);
      box-shadow: 0 0 0 3px rgba(196, 154, 86, 0.1);
    }
    
    &::placeholder {
      color: #999;
    }
  }
  
  .search-icon {
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: #666;
    width: 16px;
    height: 16px;
    pointer-events: none;
  }
}

.notifications {
  position: relative;
  
  .notification-btn {
    background: none;
    border: none;
    cursor: pointer;
    position: relative;
    padding: 8px;
    border-radius: 50%;
    transition: background-color 0.3s ease;
    
    &:hover {
      background: #f8f9fa;
    }
    
    .notification-icon {
      width: 20px;
      height: 20px;
      color: #666;
      transition: color 0.3s ease;
    }
    
    .notification-badge {
      position: absolute;
      top: 0;
      right: 0;
      background: var(--admin-danger);
      color: white;
      border-radius: 50%;
      width: 18px;
      height: 18px;
      font-size: 0.7rem;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: 600;
    }
  }
  
  .notification-dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    width: 350px;
    background: white;
    border: 1px solid #e1e5e9;
    border-radius: 8px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    margin-top: 10px;
    
    .notification-header {
      padding: 15px 20px;
      border-bottom: 1px solid #e1e5e9;
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      h3 {
        margin: 0;
        font-size: 1rem;
        font-weight: 600;
        color: #333;
      }
      
      .mark-all-read {
        background: none;
        border: none;
        color: var(--admin-primary);
        cursor: pointer;
        font-size: 0.85rem;
        
        &:hover {
          text-decoration: underline;
        }
      }
    }
    
    .notification-list {
      max-height: 300px;
      overflow-y: auto;
      
      .notification-item {
        padding: 15px 20px;
        border-bottom: 1px solid #f1f3f4;
        
        &.unread {
          background: #f8f9ff;
          border-left: 3px solid var(--admin-primary);
        }
        
        &:last-child {
          border-bottom: none;
        }
        
        .notification-content {
          .notification-text {
            margin: 0 0 5px 0;
            font-size: 0.9rem;
            color: #333;
            line-height: 1.4;
          }
          
          .notification-time {
            font-size: 0.8rem;
            color: #666;
          }
        }
      }
      
      .no-notifications {
        padding: 30px 20px;
        text-align: center;
        color: #666;
        
        p {
          margin: 0;
          font-size: 0.9rem;
        }
      }
    }
  }
}

.admin-profile {
  position: relative;
  
  .profile-info {
    display: flex;
    align-items: center;
    cursor: pointer;
    padding: 8px 12px;
    border-radius: 8px;
    transition: background-color 0.3s ease;
    
    &:hover {
      background: #f8f9fa;
    }
    
    .avatar {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      overflow: hidden;
      margin-right: 12px;
      position: relative;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        display: block;
      }

      .avatar-placeholder {
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, var(--admin-primary), #d4a574);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: 600;
        font-size: 14px;
        text-transform: uppercase;
      }
    }
    
    .admin-details {
      display: flex;
      flex-direction: column;
      margin-right: 10px;
      
      .admin-name {
        font-size: 0.9rem;
        font-weight: 600;
        color: #333;
        line-height: 1.2;
      }
      
      .admin-role {
        font-size: 0.8rem;
        color: #666;
        line-height: 1.2;
      }
    }
    
    .dropdown-arrow {
      font-size: 0.7rem;
      color: #666;
      transition: transform 0.3s ease;
    }
    
    &.open .dropdown-arrow {
      transform: rotate(180deg);
    }
  }
  
  .profile-dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    width: 200px;
    background: white;
    border: 1px solid #e1e5e9;
    border-radius: 8px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    margin-top: 10px;
    
    .profile-menu {
      list-style: none;
      margin: 0;
      padding: 8px 0;
      
      li {
        &.divider {
          height: 1px;
          background: #e1e5e9;
          margin: 8px 0;
        }
        
        a, button {
          display: block;
          width: 100%;
          padding: 10px 20px;
          text-decoration: none;
          color: #333;
          font-size: 0.9rem;
          border: none;
          background: none;
          text-align: left;
          cursor: pointer;
          transition: background-color 0.3s ease;
          
          &:hover {
            background: #f8f9fa;
          }
        }
      }
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .admin-header {
    padding: 0 15px;
    
    .header-left {
      .mobile-menu-btn {
        display: block;
      }
      
      .page-title {
        font-size: 1.2rem;
      }
    }
    
    .search-container {
      display: none;
    }
    
    .admin-profile .profile-info .admin-details {
      display: none;
    }
  }
}

@media (max-width: 480px) {
  .admin-header {
    .header-right {
      gap: 10px;
    }
    
    .notifications .notification-dropdown {
      width: 280px;
      right: -50px;
    }
    
    .admin-profile .profile-dropdown {
      right: -50px;
    }
  }
}
