import { Component, OnInit } from '@angular/core';
import { PlatformControlService } from '../services/platform-control.service';

interface Template {
  id: string;
  name: string;
  type: string;
  description: string;
  content: string;
  lastUpdated: Date;
  usageCount: number;
  isActive: boolean;
}

interface Announcement {
  id: string;
  title: string;
  content: string;
  priority: 'high' | 'medium' | 'low';
  isActive: boolean;
  createdAt: Date;
  expiresAt?: Date;
}

interface SystemSettings {
  platformName: string;
  maintenanceMode: boolean;
  allowRegistration: boolean;
  autoApproveVerified: boolean;
  ibpIntegration: boolean;
  documentRetentionDays: number;
  emailNotifications: boolean;
  smsNotifications: boolean;
}

interface UserAccount {
  id: string;
  name: string;
  email: string;
  type: 'lawyer' | 'client' | 'secretary';
  status: 'active' | 'inactive' | 'flagged';
  avatar?: string;
  lastActive: Date;
  createdAt: Date;
}

@Component({
  selector: 'app-platform-control',
  templateUrl: './platform-control.component.html',
  styleUrls: ['./platform-control.component.scss']
})
export class PlatformControlComponent implements OnInit {
  activeTab = 'templates';
  
  templates: Template[] = [];
  announcements: Announcement[] = [];
  accounts: UserAccount[] = [];
  filteredAccounts: UserAccount[] = [];
  
  accountFilter = '';
  accountSearchQuery = '';
  
  systemSettings: SystemSettings = {
    platformName: 'Veritus',
    maintenanceMode: false,
    allowRegistration: true,
    autoApproveVerified: false,
    ibpIntegration: true,
    documentRetentionDays: 365,
    emailNotifications: true,
    smsNotifications: false
  };

  constructor(private platformControlService: PlatformControlService) { }

  ngOnInit(): void {
    this.loadData();
  }

  loadData(): void {
    this.loadTemplates();
    this.loadAnnouncements();
    this.loadAccounts();
    this.loadSystemSettings();
  }

  setActiveTab(tab: string): void {
    this.activeTab = tab;
  }

  // Template Management
  loadTemplates(): void {
    this.templates = [
      {
        id: '1',
        name: 'Contract.DOCX',
        type: 'contract',
        description: 'Standard legal contract template',
        content: '',
        lastUpdated: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),
        usageCount: 45,
        isActive: true
      },
      {
        id: '2',
        name: 'Agreement.PDF',
        type: 'agreement',
        description: 'General agreement template',
        content: '',
        lastUpdated: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000),
        usageCount: 32,
        isActive: true
      },
      {
        id: '3',
        name: 'LandingPage.TXT',
        type: 'document',
        description: 'Landing page content template',
        content: '',
        lastUpdated: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000),
        usageCount: 18,
        isActive: true
      }
    ];
  }

  getTemplateIcon(type: string): string {
    const icons = {
      contract: '📄',
      agreement: '📋',
      document: '📝',
      form: '📊'
    };
    return icons[type as keyof typeof icons] || '📄';
  }

  openTemplateModal(): void {
    console.log('Opening template modal...');
    // Implement template creation modal
  }

  editTemplate(template: Template): void {
    console.log('Editing template:', template.name);
    // Implement template editing
  }

  downloadTemplate(template: Template): void {
    console.log('Downloading template:', template.name);
    // Implement template download
  }

  async deleteTemplate(template: Template): Promise<void> {
    if (confirm(`Are you sure you want to delete "${template.name}"?`)) {
      try {
        const success = await this.platformControlService.deleteTemplate(template.id);
        if (success) {
          this.templates = this.templates.filter(t => t.id !== template.id);
        }
      } catch (error) {
        console.error('Error deleting template:', error);
      }
    }
  }

  // Announcement Management
  loadAnnouncements(): void {
    this.announcements = [
      {
        id: '1',
        title: 'System Maintenance Scheduled',
        content: 'The platform will undergo maintenance on Sunday, 2:00 AM - 4:00 AM.',
        priority: 'high',
        isActive: true,
        createdAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000)
      },
      {
        id: '2',
        title: 'New Template Available',
        content: 'A new contract template has been added to the platform.',
        priority: 'medium',
        isActive: true,
        createdAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000)
      },
      {
        id: '3',
        title: 'Holiday Schedule',
        content: 'Please note the holiday schedule for the upcoming week.',
        priority: 'low',
        isActive: false,
        createdAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
      }
    ];
  }

  openAnnouncementModal(): void {
    console.log('Opening announcement modal...');
    // Implement announcement creation modal
  }

  editAnnouncement(announcement: Announcement): void {
    console.log('Editing announcement:', announcement.title);
    // Implement announcement editing
  }

  async toggleAnnouncement(announcement: Announcement): Promise<void> {
    try {
      const success = await this.platformControlService.toggleAnnouncement(
        announcement.id, 
        !announcement.isActive
      );
      if (success) {
        announcement.isActive = !announcement.isActive;
      }
    } catch (error) {
      console.error('Error toggling announcement:', error);
    }
  }

  async deleteAnnouncement(announcement: Announcement): Promise<void> {
    if (confirm(`Are you sure you want to delete "${announcement.title}"?`)) {
      try {
        const success = await this.platformControlService.deleteAnnouncement(announcement.id);
        if (success) {
          this.announcements = this.announcements.filter(a => a.id !== announcement.id);
        }
      } catch (error) {
        console.error('Error deleting announcement:', error);
      }
    }
  }

  // System Settings
  loadSystemSettings(): void {
    // Load from service - using mock data for now
    console.log('Loading system settings...');
  }

  async saveSystemSettings(): Promise<void> {
    try {
      const success = await this.platformControlService.updateSystemSettings(this.systemSettings);
      if (success) {
        console.log('System settings saved successfully');
        // Show success message
      }
    } catch (error) {
      console.error('Error saving system settings:', error);
    }
  }

  resetSystemSettings(): void {
    if (confirm('Are you sure you want to reset all settings to default?')) {
      this.systemSettings = {
        platformName: 'Veritus',
        maintenanceMode: false,
        allowRegistration: true,
        autoApproveVerified: false,
        ibpIntegration: true,
        documentRetentionDays: 365,
        emailNotifications: true,
        smsNotifications: false
      };
    }
  }

  // Account Management
  loadAccounts(): void {
    this.accounts = [
      {
        id: '1',
        name: 'John Doe',
        email: '<EMAIL>',
        type: 'lawyer',
        status: 'active',
        lastActive: new Date(Date.now() - 2 * 60 * 60 * 1000),
        createdAt: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
      },
      {
        id: '2',
        name: 'Jane Smith',
        email: '<EMAIL>',
        type: 'client',
        status: 'active',
        lastActive: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000),
        createdAt: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000)
      },
      {
        id: '3',
        name: 'Mike Johnson',
        email: '<EMAIL>',
        type: 'secretary',
        status: 'inactive',
        lastActive: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
        createdAt: new Date(Date.now() - 60 * 24 * 60 * 60 * 1000)
      }
    ];
    this.filterAccounts();
  }

  filterAccounts(): void {
    let filtered = this.accounts;

    // Filter by status
    if (this.accountFilter) {
      filtered = filtered.filter(account => account.status === this.accountFilter);
    }

    // Filter by search query
    if (this.accountSearchQuery.trim()) {
      const query = this.accountSearchQuery.toLowerCase();
      filtered = filtered.filter(account => 
        account.name.toLowerCase().includes(query) ||
        account.email.toLowerCase().includes(query)
      );
    }

    this.filteredAccounts = filtered;
  }

  viewAccount(account: UserAccount): void {
    console.log('Viewing account:', account.name);
    // Implement account details view
  }

  async toggleAccountStatus(account: UserAccount): Promise<void> {
    const newStatus = account.status === 'active' ? 'inactive' : 'active';
    try {
      const success = await this.platformControlService.updateAccountStatus(account.id, newStatus);
      if (success) {
        account.status = newStatus;
      }
    } catch (error) {
      console.error('Error updating account status:', error);
    }
  }

  async deleteAccount(account: UserAccount): Promise<void> {
    if (confirm(`Are you sure you want to delete the account for "${account.name}"?`)) {
      try {
        const success = await this.platformControlService.deleteAccount(account.id);
        if (success) {
          this.accounts = this.accounts.filter(a => a.id !== account.id);
          this.filterAccounts();
        }
      } catch (error) {
        console.error('Error deleting account:', error);
      }
    }
  }
}
