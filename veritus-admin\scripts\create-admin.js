/**
 * <PERSON><PERSON><PERSON> to create an admin account for Veritus Admin Panel
 * Run this script to set up the initial admin user
 */

const admin = require('firebase-admin');
const readline = require('readline');

// Initialize Firebase Admin SDK
const serviceAccount = {
  // You'll need to replace this with your actual Firebase service account key
  // Download it from Firebase Console > Project Settings > Service Accounts
  "type": "service_account",
  "project_id": "veritus-lawyer",
  // Add your service account credentials here
};

// Initialize Firebase Admin (uncomment when you have service account)
// admin.initializeApp({
//   credential: admin.credential.cert(serviceAccount),
//   databaseURL: "https://veritus-lawyer-default-rtdb.firebaseio.com"
// });

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

function askQuestion(question) {
  return new Promise((resolve) => {
    rl.question(question, (answer) => {
      resolve(answer);
    });
  });
}

async function createAdminAccount() {
  console.log('🔧 Veritus Admin Account Creator');
  console.log('================================\n');

  try {
    // Get admin details
    const email = await askQuestion('Enter admin email: ');
    const password = await askQuestion('Enter admin password (min 6 characters): ');
    const displayName = await askQuestion('Enter admin display name: ');
    const role = await askQuestion('Enter admin role (super_admin/admin/moderator): ') || 'admin';

    console.log('\n📝 Creating admin account...');

    // Create user in Firebase Auth
    const userRecord = await admin.auth().createUser({
      email: email,
      password: password,
      displayName: displayName,
      emailVerified: true
    });

    console.log('✅ User created in Firebase Auth:', userRecord.uid);

    // Create admin document in Firestore
    const adminData = {
      uid: userRecord.uid,
      email: email,
      displayName: displayName,
      role: role,
      permissions: getPermissionsByRole(role),
      isActive: true,
      createdAt: admin.firestore.FieldValue.serverTimestamp(),
      lastLogin: null
    };

    await admin.firestore().collection('admins').doc(userRecord.uid).set(adminData);
    console.log('✅ Admin document created in Firestore');

    // Set custom claims for role-based access
    await admin.auth().setCustomUserClaims(userRecord.uid, {
      admin: true,
      role: role
    });
    console.log('✅ Custom claims set for user');

    console.log('\n🎉 Admin account created successfully!');
    console.log('📧 Email:', email);
    console.log('👤 Name:', displayName);
    console.log('🔑 Role:', role);
    console.log('🆔 UID:', userRecord.uid);
    console.log('\n🌐 You can now login at: http://localhost:64878');

  } catch (error) {
    console.error('❌ Error creating admin account:', error.message);
  } finally {
    rl.close();
  }
}

function getPermissionsByRole(role) {
  const permissions = {
    super_admin: [
      'manage_admins',
      'manage_lawyers',
      'manage_users',
      'manage_templates',
      'manage_settings',
      'view_analytics',
      'manage_announcements',
      'system_control'
    ],
    admin: [
      'manage_lawyers',
      'manage_users',
      'manage_templates',
      'view_analytics',
      'manage_announcements'
    ],
    moderator: [
      'manage_lawyers',
      'view_analytics'
    ]
  };

  return permissions[role] || permissions.admin;
}

// Run the script
if (require.main === module) {
  console.log('⚠️  Note: You need to configure Firebase Admin SDK first!');
  console.log('1. Go to Firebase Console > Project Settings > Service Accounts');
  console.log('2. Generate a new private key');
  console.log('3. Replace the serviceAccount object in this file');
  console.log('4. Uncomment the admin.initializeApp() line');
  console.log('5. Run: node scripts/create-admin.js\n');
  
  // Uncomment this line when Firebase Admin is configured
  // createAdminAccount();
}

module.exports = { createAdminAccount, getPermissionsByRole };
