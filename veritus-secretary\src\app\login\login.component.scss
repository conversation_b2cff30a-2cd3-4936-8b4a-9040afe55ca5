// Reset
html, body {
  height: 100%;
  margin: 0;
  padding: 0;
  overflow: hidden;
  font-family: 'Segoe UI', Tahoma, sans-serif;
}

:host {
  display: block;
  height: 100%;
  width: 100%;
  overflow: hidden;
}

// Modern White & Gold Authentication Design
.modern-auth-content {
  background: #FFFFFF;
  min-height: 100vh;
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
  overflow-x: hidden;
  padding: 24px 16px;
  box-sizing: border-box;
  position: relative;
  z-index: 1;
}

.auth-wrapper {
  flex-grow: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0;
  position: relative;
  box-sizing: border-box;
}

// Background Decorative Elements
.background-elements {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 0;
  overflow: hidden;
}

.gold-accent-1 {
  position: absolute;
  top: -50px;
  right: -50px;
  width: 200px;
  height: 200px;
  background: linear-gradient(135deg, rgba(196, 154, 86, 0.1), rgba(196, 154, 86, 0.05));
  border-radius: 50%;
  filter: blur(40px);
}

.gold-accent-2 {
  position: absolute;
  bottom: -100px;
  left: -100px;
  width: 300px;
  height: 300px;
  background: linear-gradient(45deg, rgba(196, 154, 86, 0.08), rgba(196, 154, 86, 0.03));
  border-radius: 50%;
  filter: blur(60px);
}

.gold-accent-3 {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 400px;
  height: 400px;
  background: radial-gradient(circle, rgba(196, 154, 86, 0.02), transparent 70%);
  border-radius: 50%;
}

// Main Container
.auth-main-container {
  position: relative;
  z-index: 10;
  width: 100%;
  max-width: 400px;
  display: flex;
  flex-direction: column;
  gap: 32px;
}

// Logo Section
.logo-section {
  text-align: center;
  margin-bottom: 20px;
}

.logo-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
}

.logo-icon {
  width: 72px;
  height: 72px;
  background: linear-gradient(135deg, #C49A56 0%, #B8894A 100%);
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8px 32px rgba(196, 154, 86, 0.3), 0 4px 16px rgba(196, 154, 86, 0.2);
  position: relative;
}

.secretary-icon {
  font-size: 32px;
  color: white;
}

.brand-title {
  font-size: 32px;
  font-weight: 700;
  color: #1a1a1a;
  margin: 0;
  letter-spacing: -0.5px;
}

.brand-subtitle {
  font-size: 16px;
  color: #666666;
  margin: 0;
  font-weight: 500;
}

// Form Card
.form-card {
  background: #FFFFFF;
  border-radius: 24px;
  padding: 32px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.08), 0 8px 24px rgba(0, 0, 0, 0.04);
  border: 1px solid rgba(0, 0, 0, 0.04);
}

.form-header {
  text-align: center;
  margin-bottom: 32px;
}

.form-title {
  font-size: 28px;
  font-weight: 700;
  color: #1a1a1a;
  margin-bottom: 8px;
}

.form-subtitle {
  font-size: 16px;
  color: #666666;
  font-weight: 400;
}

// Modern Form Styles
.modern-form {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.input-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.input-label {
  font-size: 14px;
  font-weight: 600;
  color: #333333;
  margin: 0;
}

.input-container {
  position: relative;
  display: flex;
  align-items: center;
}

.input-icon {
  position: absolute;
  left: 16px;
  font-size: 20px;
  color: #999999;
  z-index: 2;
}

.modern-input {
  width: 100%;
  height: 56px;
  padding: 0 16px 0 48px;
  border: 2px solid #E5E5E5;
  border-radius: 16px;
  font-size: 16px;
  color: #333333;
  background: #FFFFFF;
  transition: all 0.3s ease;
  box-sizing: border-box;
}

.modern-input:focus {
  outline: none;
  border-color: #C49A56;
  box-shadow: 0 0 0 4px rgba(196, 154, 86, 0.1);
}

.password-input {
  padding-right: 56px;
}

.password-toggle-btn {
  position: absolute;
  right: 16px;
  background: none;
  border: none;
  padding: 8px;
  cursor: pointer;
  border-radius: 8px;
  transition: background-color 0.2s ease;
}

.password-toggle-btn:hover {
  background-color: rgba(0, 0, 0, 0.04);
}

.password-toggle-icon {
  font-size: 20px;
  color: #999999;
}

// Error Message
.error-message {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  background: linear-gradient(135deg, #fef2f2, #fee2e2);
  border: 1px solid #fecaca;
  border-radius: 12px;
  color: #dc2626;
  font-size: 14px;
  font-weight: 500;
}

// Forgot Password
.forgot-password-section {
  text-align: right;
  margin: -8px 0 8px 0;
}

.forgot-link {
  background: none;
  border: none;
  color: #C49A56;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  padding: 4px 0;
  text-decoration: none;
  transition: color 0.2s ease;
}

.forgot-link:hover {
  color: #B8894A;
  text-decoration: underline;
}

// Sign In Button
.modern-signin-btn {
  width: 100%;
  height: 56px;
  background: linear-gradient(135deg, #C49A56 0%, #B8894A 100%);
  border: none;
  border-radius: 16px;
  color: white;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  transition: all 0.3s ease;
  box-shadow: 0 4px 16px rgba(196, 154, 86, 0.3);
}

.modern-signin-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(196, 154, 86, 0.4);
}

.modern-signin-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.loading-icon {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

// Divider
.divider-section {
  display: flex;
  align-items: center;
  gap: 16px;
  margin: 24px 0;
}

.divider-line {
  flex: 1;
  height: 1px;
  background: #E5E5E5;
}

.divider-text {
  font-size: 14px;
  color: #999999;
  font-weight: 500;
}

// Google Sign In
.google-signin-btn {
  width: 100%;
  height: 56px;
  background: #FFFFFF;
  border: 2px solid #E5E5E5;
  border-radius: 16px;
  color: #666666;
  font-size: 16px;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  transition: all 0.3s ease;
}

.google-signin-btn:hover:not(:disabled) {
  border-color: #dadce0;
  background: #f8f9fa;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.google-signin-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.google-icon-container {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
}

.google-logo {
  width: 20px;
  height: 20px;
}

.google-text {
  font-size: 16px;
  font-weight: 600;
  color: #3c4043;
}

// Register Section
.register-section {
  text-align: center;
  margin-top: 24px;
}

.register-text {
  font-size: 14px;
  color: #666666;
}

.register-link {
  background: none;
  border: none;
  color: #C49A56;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  text-decoration: none;
  transition: color 0.2s ease;
}

.register-link:hover {
  color: #B8894A;
  text-decoration: underline;
}

// Responsive Design
@media (max-width: 480px) {
  .auth-wrapper {
    padding: 16px;
  }

  .form-card {
    padding: 24px;
  }

  .auth-main-container {
    gap: 24px;
  }

  .logo-icon {
    width: 64px;
    height: 64px;
  }

  .secretary-icon {
    font-size: 28px;
  }

  .brand-title {
    font-size: 28px;
  }

  .form-title {
    font-size: 24px;
  }
}
