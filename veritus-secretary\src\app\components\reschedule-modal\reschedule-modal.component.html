<ion-header>
  <ion-toolbar class="reschedule-toolbar">
    <ion-title>Reschedule Appointment</ion-title>
    <ion-buttons slot="end">
      <ion-button fill="clear" (click)="cancel()">
        <ion-icon name="close"></ion-icon>
      </ion-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>

<ion-content class="reschedule-content">
  <div class="reschedule-container">
    
    <!-- Current Appointment Info -->
    <div class="current-appointment-section">
      <h3>Current Appointment</h3>
      <div class="appointment-card">
        <div class="appointment-header">
          <div class="client-info">
            <h4>{{ appointment.clientName }}</h4>
            <p class="appointment-type">{{ appointment.type }}</p>
          </div>
          <div class="lawyer-info">
            <p class="lawyer-name">{{ appointment.lawyerName }}</p>
          </div>
        </div>
        
        <div class="appointment-details">
          <div class="detail-item">
            <ion-icon name="calendar-outline"></ion-icon>
            <span>{{ formatDate(appointment.date) }}</span>
          </div>
          <div class="detail-item">
            <ion-icon name="time-outline"></ion-icon>
            <span>{{ formatTime(appointment.time) }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Reschedule Form -->
    <div class="reschedule-form-section">
      <h3>Propose New Time</h3>
      
      <!-- Quick Date Selection -->
      <div class="quick-date-selection">
        <h4>Quick Select</h4>
        <div class="quick-buttons">
          <ion-button 
            fill="outline" 
            size="small" 
            (click)="selectTomorrow()">
            Tomorrow
          </ion-button>
          <ion-button 
            fill="outline" 
            size="small" 
            (click)="selectNextWeek()">
            Next Week
          </ion-button>
          <ion-button 
            fill="outline" 
            size="small" 
            (click)="selectNextMonth()">
            Next Month
          </ion-button>
        </div>
      </div>

      <!-- Date Selection -->
      <div class="form-field">
        <ion-label position="stacked">New Date *</ion-label>
        <ion-datetime
          presentation="date"
          [(ngModel)]="proposedDate"
          (ionChange)="onDateChange()"
          [min]="minDate"
          class="date-picker">
        </ion-datetime>
        
        <div *ngIf="isDateInPast" class="validation-error">
          <ion-icon name="alert-circle"></ion-icon>
          <span>Date cannot be in the past</span>
        </div>
      </div>

      <!-- Time Selection -->
      <div class="form-field">
        <ion-label position="stacked">New Time *</ion-label>
        
        <!-- Loading State -->
        <div *ngIf="isLoadingSlots" class="loading-slots">
          <ion-spinner name="crescent"></ion-spinner>
          <span>Loading available time slots...</span>
        </div>

        <!-- Available Time Slots -->
        <div *ngIf="!isLoadingSlots && hasAvailableSlots" class="time-slots-grid">
          <ion-button
            *ngFor="let timeSlot of availableTimeSlots"
            [fill]="proposedTime === timeSlot ? 'solid' : 'outline'"
            [color]="proposedTime === timeSlot ? 'primary' : 'medium'"
            size="small"
            class="time-slot-button"
            (click)="proposedTime = timeSlot; onTimeChange()">
            {{ formatTime(timeSlot) }}
          </ion-button>
        </div>

        <!-- No Slots Available -->
        <div *ngIf="!isLoadingSlots && !hasAvailableSlots" class="no-slots-message">
          <ion-icon name="information-circle-outline"></ion-icon>
          <span>{{ noSlotsMessage }}</span>
        </div>

        <div *ngIf="isSameDateTime" class="validation-error">
          <ion-icon name="alert-circle"></ion-icon>
          <span>New date and time must be different from the original</span>
        </div>
      </div>

      <!-- Reason -->
      <div class="form-field">
        <ion-label position="stacked">Reason for Reschedule *</ion-label>
        <ion-textarea
          [(ngModel)]="reason"
          (ionInput)="onReasonChange()"
          placeholder="Please provide a detailed reason for rescheduling this appointment..."
          rows="4"
          maxlength="500"
          class="reason-textarea">
        </ion-textarea>
        
        <div class="character-count">
          {{ reason.length }}/500 characters
        </div>
        
        <div *ngIf="reasonTooShort" class="validation-error">
          <ion-icon name="alert-circle"></ion-icon>
          <span>Reason must be at least 10 characters long</span>
        </div>
      </div>

      <!-- Form Validation Summary -->
      <div *ngIf="!isFormValid" class="validation-summary">
        <h4>Please complete the following:</h4>
        <ul>
          <li *ngIf="!proposedDate">Select a new date</li>
          <li *ngIf="!proposedTime">Select a new time</li>
          <li *ngIf="reasonTooShort || !reason.trim()">Provide a detailed reason (minimum 10 characters)</li>
          <li *ngIf="isSameDateTime">Choose a different date or time from the original</li>
        </ul>
      </div>
    </div>

    <!-- Action Buttons -->
    <div class="action-buttons">
      <ion-button 
        fill="clear" 
        color="medium" 
        (click)="cancel()"
        [disabled]="isSubmitting">
        Cancel
      </ion-button>
      
      <ion-button 
        fill="solid" 
        color="primary" 
        (click)="submitRescheduleRequest()"
        [disabled]="!isFormValid || isSubmitting">
        <ion-spinner *ngIf="isSubmitting" name="crescent"></ion-spinner>
        <span *ngIf="!isSubmitting">Send Reschedule Request</span>
        <span *ngIf="isSubmitting">Sending...</span>
      </ion-button>
    </div>

    <!-- Info Note -->
    <div class="info-note">
      <ion-icon name="information-circle-outline"></ion-icon>
      <div class="note-content">
        <p><strong>Note:</strong> This will send a reschedule request to both the client and lawyer. The appointment will only be rescheduled once both parties approve the new time.</p>
      </div>
    </div>

  </div>
</ion-content>
