.availability-container {
    padding: 16px;
    display: flex;
    flex-direction: column;
    gap: 20px;
  }
  
  
  .month-navigation {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 12px;
  }
  
  .month-navigation ion-button {
    --padding-start: 0;
    --padding-end: 0;
    --icon-size: 20px;
    color: #444;
  }
  
  .month-title {
    font-size: 20px;
    font-weight: 600;
    color: #333;
  }
  
  .day-header {
    font-weight: 600;
    color: #888;
    font-size: 14px;
    text-align: center;
  }
  
  .day-headers {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    text-align: center;
    font-weight: 600;
    font-size: 14px;
    color: #888;
  }
  
  .calendar-grid {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 8px;
    text-align: center;
  }
  
  .calendar-day {
    padding: 12px;
    border-radius: 50%;
    font-weight: 500;
    background-color: #f3f3f3;
    color: #333;
    cursor: pointer;
    transition: background-color 0.2s ease;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  }
  
  .calendar-day:hover {
    background-color: #ddd;
  }
  
  .calendar-day.available {
    background-color: #3498db;
    color: white;
  }
  
  .calendar-day.other-month {
    opacity: 0.2;
    pointer-events: none;
  }
  
  .time-picker-card {
    border-radius: 16px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
    padding: 16px;
    margin-top: 12px;
  }
  
  
  ion-datetime {
    --padding-start: 8px;
    --padding-end: 8px;
    text-align: center;
    font-size: 16px;
  }  
  
  ion-card-title {
    font-size: 20px;
    font-weight: 600;
    margin-bottom: 8px;
    color: #444;
  }

  ion-item {
    --padding-start: 0;
    --inner-padding-end: 0;
    font-size: 14px;
  }
  
  