import { Component, OnInit, Input, Output, EventEmitter } from '@angular/core';
import { FirebaseService, LawyerProfile } from '../../services/firebase.service';
import { LawyerCalendarSummary, CalendarView, EnhancedAppointment } from '../../models/scheduling.models';
import { ToastController } from '@ionic/angular';

@Component({
  selector: 'app-multi-lawyer-calendar',
  templateUrl: './multi-lawyer-calendar.component.html',
  styleUrls: ['./multi-lawyer-calendar.component.scss']
})
export class MultiLawyerCalendarComponent implements OnInit {
  @Input() linkedLawyers: LawyerProfile[] = [];
  @Output() lawyerSelected = new EventEmitter<LawyerProfile>();
  @Output() dateSelected = new EventEmitter<string>();

  selectedLawyer: LawyerProfile | null = null;
  calendarView: CalendarView = {
    type: 'week',
    currentDate: new Date(),
    selectedDate: new Date()
  };

  lawyerSummaries: LawyerCalendarSummary[] = [];
  isLoading = false;
  showAllLawyers = false;

  // Calendar navigation
  currentWeekStart: Date = new Date();
  currentWeekEnd: Date = new Date();
  weekDays: Date[] = [];

  constructor(
    private firebaseService: FirebaseService,
    private toastController: ToastController
  ) { }

  ngOnInit() {
    this.initializeCalendar();
    this.loadLawyerSummaries();
  }

  ngOnChanges() {
    if (this.linkedLawyers.length > 0) {
      this.loadLawyerSummaries();
    }
  }

  initializeCalendar() {
    this.setCurrentWeek(new Date());
  }

  setCurrentWeek(date: Date) {
    const startOfWeek = new Date(date);
    const day = startOfWeek.getDay();
    const diff = startOfWeek.getDate() - day + (day === 0 ? -6 : 1); // Adjust when day is Sunday
    startOfWeek.setDate(diff);

    this.currentWeekStart = new Date(startOfWeek);
    this.currentWeekEnd = new Date(startOfWeek);
    this.currentWeekEnd.setDate(this.currentWeekEnd.getDate() + 6);

    this.generateWeekDays();
    this.calendarView.currentDate = new Date(date);
  }

  generateWeekDays() {
    this.weekDays = [];
    for (let i = 0; i < 7; i++) {
      const day = new Date(this.currentWeekStart);
      day.setDate(this.currentWeekStart.getDate() + i);
      this.weekDays.push(day);
    }
  }

  async loadLawyerSummaries() {
    if (this.linkedLawyers.length === 0) return;

    this.isLoading = true;
    try {
      this.lawyerSummaries = [];

      for (const lawyer of this.linkedLawyers) {
        try {
          const summary = await this.firebaseService.getLawyerCalendarSummary(
            lawyer.uid,
            this.formatDate(this.calendarView.currentDate)
          );
          this.lawyerSummaries.push(summary);
        } catch (error) {
          console.error('Error loading summary for lawyer:', lawyer.name, error);
          // Add mock summary for testing
          this.lawyerSummaries.push({
            lawyerId: lawyer.uid,
            lawyerName: lawyer.name,
            totalAppointments: Math.floor(Math.random() * 5) + 1,
            confirmedAppointments: Math.floor(Math.random() * 3) + 1,
            pendingAppointments: Math.floor(Math.random() * 2),
            availableSlots: Math.floor(Math.random() * 8) + 2
          });
        }
      }
    } catch (error) {
      console.error('Error loading lawyer summaries:', error);
      this.showToast('Error loading lawyer summaries', 'danger');
    } finally {
      this.isLoading = false;
    }
  }

  selectLawyer(lawyer: LawyerProfile) {
    this.selectedLawyer = lawyer;
    this.showAllLawyers = false;
    this.lawyerSelected.emit(lawyer);
  }

  selectDate(date: Date) {
    this.calendarView.selectedDate = date;
    this.dateSelected.emit(this.formatDate(date));
  }

  toggleAllLawyers() {
    this.showAllLawyers = !this.showAllLawyers;
    if (this.showAllLawyers) {
      this.selectedLawyer = null;
      this.lawyerSelected.emit(null as any);
    }
  }

  // Calendar navigation methods
  previousWeek() {
    const newDate = new Date(this.currentWeekStart);
    newDate.setDate(newDate.getDate() - 7);
    this.setCurrentWeek(newDate);
    this.loadLawyerSummaries();
  }

  nextWeek() {
    const newDate = new Date(this.currentWeekStart);
    newDate.setDate(newDate.getDate() + 7);
    this.setCurrentWeek(newDate);
    this.loadLawyerSummaries();
  }

  goToToday() {
    this.setCurrentWeek(new Date());
    this.selectDate(new Date());
    this.loadLawyerSummaries();
  }

  // View type switching
  onViewTypeChange(event: any) {
    const type = event.detail.value;
    if (type === 'day' || type === 'week' || type === 'month') {
      this.calendarView.type = type;
      if (type === 'day') {
        this.selectDate(this.calendarView.currentDate);
      }
    }
  }

  setViewType(type: 'day' | 'week' | 'month') {
    this.calendarView.type = type;
    if (type === 'day') {
      this.selectDate(this.calendarView.currentDate);
    }
  }

  // Utility methods
  formatDate(date: Date): string {
    return date.toISOString().split('T')[0];
  }

  formatDateDisplay(date: Date): string {
    return date.toLocaleDateString('en-US', {
      weekday: 'short',
      month: 'short',
      day: 'numeric'
    });
  }

  formatWeekRange(): string {
    const startMonth = this.currentWeekStart.toLocaleDateString('en-US', { month: 'short' });
    const startDay = this.currentWeekStart.getDate();
    const endMonth = this.currentWeekEnd.toLocaleDateString('en-US', { month: 'short' });
    const endDay = this.currentWeekEnd.getDate();
    const year = this.currentWeekStart.getFullYear();

    if (startMonth === endMonth) {
      return `${startMonth} ${startDay}-${endDay}, ${year}`;
    } else {
      return `${startMonth} ${startDay} - ${endMonth} ${endDay}, ${year}`;
    }
  }

  isToday(date: Date): boolean {
    const today = new Date();
    return date.toDateString() === today.toDateString();
  }

  isSelected(date: Date): boolean {
    return this.calendarView.selectedDate?.toDateString() === date.toDateString();
  }

  isWeekend(date: Date): boolean {
    const day = date.getDay();
    return day === 0 || day === 6; // Sunday or Saturday
  }

  // Lawyer summary helpers
  getLawyerSummary(lawyerId: string): LawyerCalendarSummary | undefined {
    return this.lawyerSummaries.find(summary => summary.lawyerId === lawyerId);
  }

  getTotalAppointmentsForDay(date: Date): number {
    if (this.showAllLawyers) {
      return this.lawyerSummaries.reduce((total, summary) => total + summary.totalAppointments, 0);
    } else if (this.selectedLawyer) {
      const summary = this.getLawyerSummary(this.selectedLawyer.uid);
      return summary?.totalAppointments || 0;
    }
    return 0;
  }

  getAvailableSlotsForDay(date: Date): number {
    if (this.showAllLawyers) {
      return this.lawyerSummaries.reduce((total, summary) => total + summary.availableSlots, 0);
    } else if (this.selectedLawyer) {
      const summary = this.getLawyerSummary(this.selectedLawyer.uid);
      return summary?.availableSlots || 0;
    }
    return 0;
  }

  // Status indicators
  getLawyerStatusColor(lawyer: LawyerProfile): string {
    const summary = this.getLawyerSummary(lawyer.uid);
    if (!summary) return 'medium';
    
    if (summary.pendingAppointments > 0) return 'warning';
    if (summary.confirmedAppointments > 0) return 'success';
    if (summary.availableSlots > 0) return 'primary';
    return 'medium';
  }

  getLawyerStatusText(lawyer: LawyerProfile): string {
    const summary = this.getLawyerSummary(lawyer.uid);
    if (!summary) return 'No data';
    
    if (summary.pendingAppointments > 0) return `${summary.pendingAppointments} pending`;
    if (summary.confirmedAppointments > 0) return `${summary.confirmedAppointments} confirmed`;
    if (summary.availableSlots > 0) return `${summary.availableSlots} available`;
    return 'No appointments';
  }

  private async showToast(message: string, color: string) {
    const toast = await this.toastController.create({
      message,
      duration: 3000,
      color,
      position: 'top'
    });
    await toast.present();
  }

  // Quick actions
  async refreshData() {
    await this.loadLawyerSummaries();
    this.showToast('Calendar data refreshed', 'success');
  }

  get hasLinkedLawyers(): boolean {
    return this.linkedLawyers.length > 0;
  }

  get selectedLawyerName(): string {
    return this.selectedLawyer?.name || 'All Lawyers';
  }

  get totalLinkedLawyers(): number {
    return this.linkedLawyers.length;
  }

  get totalAppointmentsToday(): number {
    const today = new Date();
    return this.getTotalAppointmentsForDay(today);
  }

  get totalAvailableSlotsToday(): number {
    const today = new Date();
    return this.getAvailableSlotsForDay(today);
  }
}
