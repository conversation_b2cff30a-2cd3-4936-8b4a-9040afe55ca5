<div class="login-container">
  <div class="login-card">
    <div class="login-header">
      <div class="logo">
        <h1>Veritus</h1>
        <p>Admin Portal</p>
        <div class="logo-subtitle">Legal Excellence Management</div>
      </div>
    </div>
    
    <form [formGroup]="loginForm" (ngSubmit)="onLogin()" class="login-form">
      <div class="form-group">
        <label for="email">Email Address</label>
        <input 
          type="email" 
          id="email" 
          formControlName="email" 
          class="form-control"
          [class.error]="loginForm.get('email')?.invalid && loginForm.get('email')?.touched"
          placeholder="Enter your admin email">
        <div class="error-message" *ngIf="loginForm.get('email')?.invalid && loginForm.get('email')?.touched">
          <span *ngIf="loginForm.get('email')?.errors?.['required']">Email is required</span>
          <span *ngIf="loginForm.get('email')?.errors?.['email']">Please enter a valid email</span>
        </div>
      </div>

      <div class="form-group">
        <label for="password">Password</label>
        <input 
          type="password" 
          id="password" 
          formControlName="password" 
          class="form-control"
          [class.error]="loginForm.get('password')?.invalid && loginForm.get('password')?.touched"
          placeholder="Enter your password">
        <div class="error-message" *ngIf="loginForm.get('password')?.invalid && loginForm.get('password')?.touched">
          <span *ngIf="loginForm.get('password')?.errors?.['required']">Password is required</span>
          <span *ngIf="loginForm.get('password')?.errors?.['minlength']">Password must be at least 6 characters</span>
        </div>
      </div>

      <div class="form-group">
        <label class="checkbox-container">
          <input type="checkbox" formControlName="rememberMe">
          <span class="checkmark"></span>
          Remember me
        </label>
      </div>

      <button 
        type="submit" 
        class="login-btn" 
        [disabled]="loginForm.invalid || isLoading">
        <span *ngIf="isLoading" class="spinner"></span>
        {{ isLoading ? 'Signing in...' : 'Sign In' }}
      </button>

      <div class="error-message" *ngIf="errorMessage">
        {{ errorMessage }}
      </div>
    </form>

    <div class="login-footer">
      <div class="setup-link">
        <a routerLink="/setup" class="setup-btn">🔧 First time? Create Admin Account</a>
      </div>
      <p>&copy; 2024 Veritus. All rights reserved.</p>
    </div>
  </div>
</div>
