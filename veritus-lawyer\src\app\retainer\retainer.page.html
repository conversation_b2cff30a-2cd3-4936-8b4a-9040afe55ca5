<ion-content class="retainer-content">
  <!-- Status Bar Spacer -->
  <div class="status-bar-spacer"></div>

  <div class="retainer-container">
    <!-- Header Section -->
    <div class="page-header">
      <h1 class="page-title">Retainer</h1>
      <div class="header-actions">
        <button class="search-button" (click)="toggleSearch()">
          <ion-icon name="search" class="search-icon"></ion-icon>
        </button>
        <button class="add-button" (click)="onAddRetainer()">
          <span class="add-text">Add</span>
        </button>
      </div>
    </div>

    <!-- Search Bar -->
    <div class="search-container" *ngIf="showSearchBar">
      <ion-searchbar
        [(ngModel)]="searchTerm"
        placeholder="Search clients, emails, or case types..."
        debounce="300"
        class="custom-searchbar">
      </ion-searchbar>
    </div>

    <!-- Tab Navigation -->
    <div class="tab-navigation">
      <button
        class="tab-button"
        [class.tab-active]="selectedTab === 'active'"
        (click)="selectTab('active')">
        <span class="tab-text">Active</span>
      </button>
      <button
        class="tab-button"
        [class.tab-active]="selectedTab === 'inactive'"
        (click)="selectTab('inactive')">
        <span class="tab-text">Inactive</span>
      </button>
      <button
        class="tab-button"
        [class.tab-active]="selectedTab === 'overdue'"
        (click)="selectTab('overdue')">
        <span class="tab-text">Overdue</span>
      </button>
    </div>

    <!-- Client List -->
    <div class="client-list">
      <div class="client-item" *ngFor="let client of getFilteredClients()" (click)="onClientClick(client)">
        <div class="client-content">
          <div class="client-avatar">
            <img [src]="client.avatar" [alt]="client.name" class="avatar-image">
            <div class="status-indicator" [class]="'status-' + getStatusColor(client.status)"></div>
          </div>
          <div class="client-info">
            <h3 class="client-name">{{ client.name }}</h3>
            <p class="client-status" [class]="'status-text-' + getStatusColor(client.status)">{{ client.status }}</p>
            <p class="client-case-type">{{ client.caseType }}</p>
            <p class="client-retainer">${{ client.retainerAmount.toLocaleString() }}</p>
          </div>
          <div class="client-meta">
            <p class="last-activity">{{ client.lastActivity }}</p>
            <ion-icon name="chevron-forward" class="chevron-icon"></ion-icon>
          </div>
        </div>
      </div>

      <!-- Empty State -->
      <div class="empty-state" *ngIf="getFilteredClients().length === 0">
        <ion-icon name="people-outline" class="empty-icon"></ion-icon>
        <h3 class="empty-title">No clients found</h3>
        <p class="empty-message">
          <span *ngIf="searchTerm">No clients match your search criteria.</span>
          <span *ngIf="!searchTerm && selectedTab === 'active'">No active clients yet.</span>
          <span *ngIf="!searchTerm && selectedTab === 'inactive'">No inactive clients.</span>
          <span *ngIf="!searchTerm && selectedTab === 'overdue'">No overdue clients.</span>
        </p>
        <button class="empty-action-button" (click)="onAddRetainer()" *ngIf="!searchTerm">
          Add First Client
        </button>
      </div>
    </div>

    <!-- Contract & Files Section -->
    <div class="contracts-section">
      <div class="section-header">
        <h2 class="section-title">Contract & Files</h2>
        <button class="upload-button" (click)="onUploadFiles()">
          <ion-icon name="cloud-upload" class="upload-icon"></ion-icon>
          <span class="upload-text">Upload</span>
        </button>
      </div>

      <!-- Folders List -->
      <div class="folders-list" *ngIf="fileFolders.length > 0">
        <h3 class="subsection-title">Folders</h3>
        <div class="folder-item" *ngFor="let folder of fileFolders">
          <div class="folder-content">
            <div class="folder-icon-container">
              <ion-icon name="folder" class="folder-icon"></ion-icon>
            </div>
            <div class="folder-info">
              <h4 class="folder-name">{{ folder.name }}</h4>
              <p class="folder-meta">{{ folder.fileCount }} files • Created {{ folder.createdDate }}</p>
            </div>
            <div class="folder-action">
              <ion-icon name="chevron-forward" class="chevron-icon"></ion-icon>
            </div>
          </div>
        </div>
      </div>

      <!-- Files List -->
      <div class="files-list">
        <h3 class="subsection-title">Recent Files</h3>
        <div class="file-item" *ngFor="let file of contractFiles" (click)="onFileClick(file)">
          <div class="file-content">
            <div class="file-icon-container">
              <ion-icon [name]="file.icon" class="file-icon"></ion-icon>
            </div>
            <div class="file-info">
              <h4 class="file-name">{{ file.name }}</h4>
              <p class="file-type">{{ file.type }}</p>
              <p class="file-meta-info">{{ file.size }} • {{ file.uploadDate }}</p>
            </div>
            <div class="file-meta">
              <span class="file-count">{{ file.count }}</span>
              <ion-icon name="chevron-forward" class="chevron-icon"></ion-icon>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Activity Section -->
    <div class="activity-section">
      <div class="section-header">
        <h2 class="section-title">Recent Activity</h2>
        <button class="view-all-button">
          <span class="view-all-text">View All</span>
        </button>
      </div>

      <div class="activity-list">
        <div class="activity-item" *ngFor="let activity of recentActivities" (click)="onActivityClick(activity)">
          <div class="activity-content">
            <div class="activity-icon-container">
              <ion-icon [name]="getActivityIcon(activity.type)" class="activity-icon" [class]="'activity-' + activity.type"></ion-icon>
            </div>
            <div class="activity-info">
              <h4 class="activity-title">{{ activity.title }}</h4>
              <p class="activity-description">{{ activity.description }}</p>
              <p class="activity-client">{{ activity.clientName }}</p>
            </div>
            <div class="activity-meta">
              <p class="activity-time">{{ activity.time }}</p>
              <p class="activity-date">{{ activity.date }}</p>
              <ion-icon name="chevron-forward" class="chevron-icon"></ion-icon>
            </div>
          </div>
        </div>

        <!-- Empty Activity State -->
        <div class="empty-activity" *ngIf="recentActivities.length === 0">
          <ion-icon name="time-outline" class="empty-activity-icon"></ion-icon>
          <p class="empty-activity-text">No recent activity</p>
        </div>
      </div>
    </div>
  </div>
</ion-content>
