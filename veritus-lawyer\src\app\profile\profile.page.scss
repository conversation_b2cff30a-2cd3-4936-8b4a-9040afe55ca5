.profile-content {
  --background: #F8F9FA;
}

.profile-container {
  padding: 24px 20px;
  min-height: 100vh;
}

.profile-header {
  margin-bottom: 40px;
  text-align: left;
}

.page-title {
  margin: 0;
  color: #000000;
  font-size: 28px;
  font-weight: 600;
}

// Avatar Section
.avatar-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 48px;
}

.profile-avatar {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  margin-bottom: 20px;
  overflow: hidden;
  background: linear-gradient(135deg, #C49A56, #E6C78A);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 16px rgba(196, 154, 86, 0.3);
  position: relative;
  cursor: pointer;
  transition: all 0.3s ease;
  z-index: 1; // Ensure it's above other elements

  &:hover {
    transform: scale(1.05);
    box-shadow: 0 6px 20px rgba(196, 154, 86, 0.4);

    .upload-overlay {
      opacity: 1;
      pointer-events: auto;
    }
  }

  &.uploading {
    pointer-events: none;

    .upload-overlay {
      opacity: 1 !important;
      background: rgba(0, 0, 0, 0.8);
      pointer-events: auto;
    }
  }

}

.click-area {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 10; // Above everything else
  cursor: pointer;
  border-radius: 50%;
}

.avatar-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 50%;
  position: absolute;
  top: 0;
  left: 0;
}

.upload-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 50%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: all 0.3s ease;
  color: #FFFFFF;
  z-index: 2;
  pointer-events: none; // Don't block clicks when invisible

  &.visible {
    opacity: 1;
    pointer-events: auto; // Allow clicks when visible
  }
}

.upload-icon {
  font-size: 24px;
  margin-bottom: 4px;
}

.upload-text {
  font-size: 12px;
  font-weight: 500;
  text-align: center;
  margin-top: 4px;
}

.uploading-icon {
  animation: pulse 1.5s infinite;
}

.upload-progress {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 3;
}

.upload-spinner {
  --color: #FFFFFF;
  width: 32px;
  height: 32px;
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.7;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

.verified-badge {
  display: flex;
  align-items: center;
  gap: 6px;
  background: #007AFF;
  color: #FFFFFF;
  border: none;
  border-radius: 20px;
  padding: 6px 16px;
  font-size: 14px;
  font-weight: 500;
  box-shadow: 0 2px 8px rgba(0, 122, 255, 0.3);
}

.verified-icon {
  font-size: 16px;
}

.verified-text {
  margin: 0;
}

// Menu Section
.menu-section {
  margin-top: 20px;
}

.menu-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: #FFFFFF;
  border-radius: 12px;
  margin-bottom: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border: 1px solid #F0F0F0;

  &:hover {
    background: #F8F9FA;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  &:active {
    transform: translateY(0);
  }

  &.logout-item {
    &:hover {
      background: #FFF5F5;
      border-color: #FED7D7;
    }
  }
}

.menu-item-content {
  display: flex;
  align-items: center;
  gap: 16px;
}

.menu-icon {
  font-size: 22px;
  color: #666666;
  width: 24px;
  display: flex;
  justify-content: center;
}

.logout-icon {
  color: #E53E3E;
}

.menu-text {
  margin: 0;
  font-size: 16px;
  font-weight: 400;
  color: #333333;
}

.logout-text {
  color: #E53E3E;
}

.chevron-icon {
  font-size: 18px;
  color: #CCCCCC;
}
