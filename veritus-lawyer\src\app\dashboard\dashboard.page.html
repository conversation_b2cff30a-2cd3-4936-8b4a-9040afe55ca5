<ion-content class="dashboard-content">
  <!-- Status Bar Spacer -->
  <div class="status-bar-spacer"></div>

  <div class="dashboard-container">
    <!-- Header -->
    <div class="dashboard-header">
      <div class="greeting-section">
        <h1 class="greeting">
          Good day, {{ lawyerName || 'Atty.' }}!
        </h1>
        <button class="notification-btn" (click)="onNotificationClick()" title="Notifications">
          <ion-icon name="notifications-outline" class="notification-icon"></ion-icon>
          <span class="notification-badge" *ngIf="notificationCount > 0">{{ notificationCount }}</span>
        </button>
      </div>
    </div>

    <!-- Summary Cards -->
    <div class="summary-section">
      <div class="summary-grid">
        <div class="summary-card" *ngFor="let stat of stats">
          <div class="summary-content">
            <div class="summary-icon-container">
              <ion-icon name="folder-outline" class="summary-icon"></ion-icon>
            </div>
            <div class="summary-info">
              <p class="summary-title">{{ stat.title }}</p>
              <h3 class="summary-value">{{ stat.value }} Cases</h3>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Appointments Section -->
    <div class="appointments-section">
      <div class="section-header">
        <h2 class="section-title">Appointments</h2>
        <button class="see-all-btn" (click)="onSeeAllAppointments()">
          See All
        </button>
      </div>

      <div class="appointments-list">
        <div
          class="appointment-card"
          *ngFor="let appointment of appointments"
          (click)="onAppointmentClick(appointment)">
          <div class="appointment-content">
            <div class="appointment-icon">
              <ion-icon name="clipboard-outline" class="clip-icon"></ion-icon>
            </div>
            <div class="appointment-details">
              <h3 class="appointment-type">{{ appointment.type }}</h3>
              <p class="client-name">Client Name: {{ appointment.clientName }}</p>
              <div class="case-status-row">
                <span class="case-status-label">Case Status:</span>
                <span class="status-badge">{{ getStatusText(appointment.status) }}</span>
              </div>
              <div class="appointment-meta">
                <span class="appointment-date">{{ appointment.date }}</span>
                <span class="appointment-time">{{ appointment.time }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Case Files Section -->
    <div class="case-files-section">
      <div class="section-header">
        <h2 class="section-title">Case Files</h2>
        <button class="see-all-btn" (click)="onCreateFolderClick()">Create Folder</button>
      </div>

      <div class="case-files-list">
        <div
          class="case-file-item"
          *ngFor="let caseFile of caseFiles"
          (click)="onCaseFileClick(caseFile)">
          <div class="case-file-content">
            <div class="case-file-icon-container">
              <ion-icon name="folder-outline" class="case-file-icon"></ion-icon>
            </div>
            <div class="case-file-info">
              <h3 class="case-file-title">{{ caseFile.title }}</h3>
              <p class="case-file-count">{{ caseFile.fileCount }} files</p>
            </div>
            <div class="case-file-actions">
              <ion-button fill="clear" color="danger" size="small" (click)="onDeleteFolder(caseFile, $event)"></ion-button>
                <ion-icon name="trash-outline" color="danger" slot="icon-only"></ion-icon>
              <ion-icon name="chevron-forward" class="arrow-icon"></ion-icon>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div> <!-- End of .dashboard-container -->
</ion-content>

<!-- Create Folder Modal -->
<ion-modal [isOpen]="showCreateFolderModal" (didDismiss)="closeCreateFolderModal()">
  <ng-template>
    <ion-header>
      <ion-toolbar>
        <ion-title>Create Folder</ion-title>
        <ion-buttons slot="end">
          <ion-button (click)="closeCreateFolderModal()">
            <ion-icon name="close" slot="icon-only"></ion-icon>
          </ion-button>
        </ion-buttons>
      </ion-toolbar>
    </ion-header>
    <ion-content class="ion-padding">
      <ion-item>
        <ion-label position="stacked">Folder Name</ion-label>
        <ion-input [(ngModel)]="newFolderName" placeholder="Enter folder name"></ion-input>
      </ion-item>
      <ion-button expand="block" (click)="createDashboardFolder()">Create Folder</ion-button>
    </ion-content>
  </ng-template>
</ion-modal>
