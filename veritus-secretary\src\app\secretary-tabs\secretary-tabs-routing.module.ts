import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';

import { SecretaryTabsPage } from './secretary-tabs.page';

const routes: Routes = [
  {
    path: '',
    component: SecretaryTabsPage,
    children: [
      {
        path: 'dashboard',
        loadChildren: () => import('../secretary-dashboard/secretary-dashboard.module').then(m => m.SecretaryDashboardPageModule)
      },
      {
        path: 'calendar',
        loadChildren: () => import('../secretary-calendar/secretary-calendar.module').then(m => m.SecretaryCalendarPageModule)
      },
      {
        path: 'cases',
        loadChildren: () => import('../secretary-cases/secretary-cases.module').then(m => m.SecretaryCasesPageModule)
      },
      {
        path: 'files',
        loadChildren: () => import('../secretary-files/secretary-files.module').then(m => m.SecretaryFilesPageModule)
      },
      {
        path: 'profile',
        loadChildren: () => import('../secretary-profile/secretary-profile.module').then(m => m.SecretaryProfilePageModule)
      },
      {
        path: '',
        redirectTo: '/secretary-tabs/dashboard',
        pathMatch: 'full'
      }
    ]
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class SecretaryTabsPageRoutingModule {}
