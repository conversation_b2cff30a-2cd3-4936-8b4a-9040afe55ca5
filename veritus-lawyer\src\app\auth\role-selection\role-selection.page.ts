import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';

@Component({
  selector: 'app-role-selection',
  templateUrl: './role-selection.page.html',
  styleUrls: ['./role-selection.page.scss'],
  standalone: false,
})
export class RoleSelectionPage implements OnInit {
  selectedRole: 'client' | 'lawyer' | null = null;

  constructor(private router: Router) {}

  ngOnInit() {
    // Reset selection when page loads to ensure clean state
    this.selectedRole = null;
  }

  ionViewWillEnter() {
    // Reset selection every time user enters this page
    this.selectedRole = null;
  }

  selectRole(role: 'client' | 'lawyer') {
    this.selectedRole = role;
  }

  proceedToRegistration() {
    if (this.selectedRole) {
      // Navigate to registration page with selected role
      this.router.navigate(['/auth/register'], { 
        queryParams: { role: this.selectedRole } 
      });
    }
  }

  goToSignIn() {
    this.router.navigate(['/auth/signin']);
  }
}
