<ion-content class="modern-auth-content">
  <div class="auth-wrapper">
    <!-- Background Elements -->
    <div class="background-elements">
      <div class="gold-accent-1"></div>
      <div class="gold-accent-2"></div>
      <div class="gold-accent-3"></div>
    </div>

    <!-- Main Content -->
    <div class="auth-main-container">
      <!-- Logo Section -->
      <div class="logo-section">
        <div class="logo-container">
          <div class="logo-icon">
            <ion-icon name="scale-outline" class="justice-scale-icon"></ion-icon>
          </div>
          <h1 class="brand-title">Veritus</h1>
          <p class="brand-subtitle">Legal Excellence Platform</p>
        </div>
      </div>

      <!-- Role Selection Card -->
      <div class="form-card">
        <div class="form-header">
          <h2 class="form-title">Create Account</h2>
          <p class="form-subtitle">Choose your role and get started</p>
        </div>

        <!-- Role Selection -->
        <div class="role-selection-section">
          <label class="section-label">I am a:</label>
          <div class="role-buttons">
            <button
              type="button"
              class="modern-role-btn"
              [class.role-active]="selectedRole === 'client'"
              (click)="selectRole('client')">
              <ion-icon name="person-outline" class="role-icon"></ion-icon>
              <span class="role-text">Client</span>
            </button>
            <button
              type="button"
              class="modern-role-btn"
              [class.role-active]="selectedRole === 'lawyer'"
              (click)="selectRole('lawyer')">
              <ion-icon name="briefcase-outline" class="role-icon"></ion-icon>
              <span class="role-text">Lawyer</span>
            </button>

          </div>
        </div>

        <!-- Continue Button -->
        <button 
          type="button" 
          class="modern-submit-btn"
          [disabled]="!selectedRole"
          (click)="proceedToRegistration()">
          <span class="btn-text">Continue</span>
          <ion-icon name="arrow-forward-outline" class="btn-icon"></ion-icon>
        </button>

        <!-- Sign In Link -->
        <div class="signin-section">
          <span class="signin-text">Already have an account? </span>
          <button type="button" class="signin-link" (click)="goToSignIn()">
            Sign In
          </button>
        </div>
      </div>
    </div>
  </div>
</ion-content>
