.dashboard-content {
  --background: #f8f9fa;
  background: #f8f9fa;
}

.dashboard-container {
  padding: 24px;
  background: #f8f9fa;
  min-height: 100vh;
}

// Profile Header Section
.profile-header-section {
  margin-bottom: 24px;
}

.profile-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.profile-main {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 20px;
}

.profile-avatar {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f0f0f0;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  ion-icon {
    font-size: 40px;
    color: #666;
  }
}

.profile-info {
  flex: 1;
}

.profile-name {
  font-size: 24px;
  font-weight: 600;
  margin: 0 0 4px 0;
  color: #333;
}

.profile-title {
  font-size: 14px;
  color: #d4af37;
  font-weight: 500;
  margin: 0 0 8px 0;
}

.profile-email, .profile-phone {
  font-size: 14px;
  color: #666;
  margin: 0 0 4px 0;
}

.profile-actions {
  .edit-btn {
    background: none;
    border: none;
    padding: 8px;
    border-radius: 4px;
    cursor: pointer;

    &:hover {
      background: #f0f0f0;
    }

    ion-icon {
      font-size: 20px;
      color: #666;
    }
  }
}

.profile-stats {
  display: flex;
  gap: 24px;
  padding-top: 20px;
  border-top: 1px solid #f0f0f0;
}

.stat-item {
  text-align: center;

  .stat-number {
    display: block;
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin-bottom: 4px;
  }

  .stat-label {
    font-size: 12px;
    color: #666;
  }
}

// Dashboard Layout
.dashboard-layout {
  display: flex;
  gap: 24px;
}

.left-column {
  flex: 1;
  max-width: 400px;
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.right-column {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 24px;
}

// Section Styles
.scheduling-section,
.lawyers-section,
.files-section,
.finance-section {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

h2 {
  font-size: 24px;
  font-weight: 600;
  margin: 0 0 20px 0;
  color: #333;
}

h3, h4 {
  font-size: 16px;
  font-weight: 600;
  margin: 0 0 12px 0;
  color: #333;
}

// Calendar Widget
.calendar-widget {
  margin-bottom: 24px;
}

.calendar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.nav-btn {
  background: none;
  border: none;
  font-size: 20px;
  cursor: pointer;
  padding: 8px;
  border-radius: 4px;

  &:hover {
    background: #f0f0f0;
  }
}

.calendar-grid {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 1px;
  background: #e0e0e0;
  border-radius: 8px;
  overflow: hidden;
}

.day-header {
  background: #f8f9fa;
  padding: 8px 4px;
  text-align: center;
  font-size: 12px;
  font-weight: 600;
  color: #666;
}

.calendar-day {
  background: white;
  padding: 12px 4px;
  text-align: center;
  cursor: pointer;
  font-size: 14px;
  min-height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;

  &:hover {
    background: #f8f9fa;
  }

  &.other-month {
    color: #ccc;
  }

  &.today {
    background: #d4af37;
    color: white;
    font-weight: 600;
  }

  &.has-appointment {
    position: relative;

    &::after {
      content: '';
      position: absolute;
      bottom: 4px;
      left: 50%;
      transform: translateX(-50%);
      width: 4px;
      height: 4px;
      background: #e91e63;
      border-radius: 50%;
    }
  }
}

// Booking Section
.booking-section {
  margin-bottom: 24px;
}

.booking-list {
  margin-bottom: 16px;
}

.booking-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;

  &:last-child {
    border-bottom: none;
  }
}

.booking-info {
  .booking-type {
    font-weight: 500;
    color: #333;
    margin-bottom: 2px;
  }

  .booking-client {
    font-size: 14px;
    color: #666;
  }
}

.booking-time {
  font-weight: 600;
  color: #d4af37;
}

.new-booking-btn {
  background: #d4af37;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;

  &:hover {
    background: #c49a56;
  }
}

// Lawyers Section
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.add-btn {
  background: #d4af37;
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;

  &:hover {
    background: #c49a56;
  }
}

.lawyers-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.lawyer-card {
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  padding: 16px;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border-color: #d4af37;
  }
}

.lawyer-main {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
}

.lawyer-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f0f0f0;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  ion-icon {
    font-size: 24px;
    color: #666;
  }
}

.lawyer-details {
  flex: 1;
}

.lawyer-name {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin: 0 0 4px 0;
}

.lawyer-email {
  font-size: 12px;
  color: #666;
  margin: 0 0 4px 0;
}

.lawyer-credentials {
  display: flex;
  gap: 8px;
  font-size: 11px;
  color: #999;

  span {
    background: #f8f9fa;
    padding: 2px 6px;
    border-radius: 4px;
  }
}

.lawyer-status {
  .status-badge {
    background: #e8f5e8;
    color: #4caf50;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 500;

    &.active {
      background: #e8f5e8;
      color: #4caf50;
    }
  }
}

.lawyer-stats {
  display: flex;
  gap: 16px;
  margin-bottom: 12px;
}

.stat {
  text-align: center;

  .stat-number {
    display: block;
    font-size: 14px;
    font-weight: 600;
    color: #333;
  }

  .stat-label {
    font-size: 10px;
    color: #666;
  }
}

.lawyer-permissions {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.permission-tag {
  background: #f0f8ff;
  color: #2196f3;
  padding: 2px 6px;
  border-radius: 8px;
  font-size: 10px;
  font-weight: 500;
}

// Files Section
.search-box {
  position: relative;
  display: flex;
  align-items: center;

  input {
    border: 1px solid #e0e0e0;
    border-radius: 6px;
    padding: 8px 12px 8px 36px;
    font-size: 14px;
    width: 200px;

    &:focus {
      outline: none;
      border-color: #d4af37;
    }
  }

  .search-icon {
    position: absolute;
    left: 12px;
    color: #666;
    font-size: 16px;
  }
}

.file-categories {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
  margin-bottom: 24px;
}

.file-category {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background: #f8f9fa;
    border-color: #d4af37;
  }

  .folder-icon {
    font-size: 20px;
  }

  span {
    font-size: 14px;
    color: #333;
  }
}

.recent-documents {
  h4 {
    margin-bottom: 12px;
  }
}

.document-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;

  &:hover {
    background: #f8f9fa;
  }

  &:last-child {
    border-bottom: none;
  }

  .document-icon {
    font-size: 16px;
  }

  .document-info {
    flex: 1;

    .document-name {
      font-size: 14px;
      color: #333;
      margin-bottom: 2px;
    }

    .document-date {
      font-size: 12px;
      color: #666;
    }
  }

  ion-icon {
    color: #ccc;
  }
}

// Finance Section
.new-transaction-btn {
  background: #d4af37;
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;

  &:hover {
    background: #c49a56;
  }
}

.finance-chart {
  margin-bottom: 24px;
}

.chart-container {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 16px;
}

.chart-bars {
  display: flex;
  align-items: end;
  gap: 4px;
  height: 100px;
  margin-bottom: 8px;
}

.bar {
  flex: 1;
  min-height: 20px;
  border-radius: 4px 4px 0 0;
}

.chart-labels {
  display: flex;
  gap: 8px;
  font-size: 12px;
  color: #666;

  span {
    flex: 1;
    text-align: center;
  }
}

.chart-period {
  text-align: center;
  font-size: 12px;
  color: #666;
  margin-top: 8px;
}

.recent-transactions {
  h4 {
    margin-bottom: 12px;
  }
}

.transaction-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;

  &:last-child {
    border-bottom: none;
  }

  .transaction-amount {
    font-weight: 600;
    color: #333;
  }

  .transaction-info {
    text-align: right;

    .transaction-type {
      font-weight: 500;
      color: #333;
      margin-bottom: 2px;
    }

    .transaction-date {
      font-size: 12px;
      color: #666;
    }
  }
}

// Responsive Design
@media (max-width: 768px) {
  .dashboard-layout {
    flex-direction: column;
  }

  .left-column {
    max-width: none;
  }

  .profile-main {
    flex-direction: column;
    text-align: center;
  }

  .profile-stats {
    justify-content: center;
  }

  .file-categories {
    grid-template-columns: 1fr;
  }
}






