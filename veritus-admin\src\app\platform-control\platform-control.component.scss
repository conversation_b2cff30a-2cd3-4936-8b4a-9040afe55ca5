.platform-control-container {
  padding: 0;
  margin-left: 250px; // Account for sidebar
}

.control-header {
  margin-bottom: 30px;
  
  h1 {
    margin: 0 0 8px 0;
    font-size: 2rem;
    font-weight: 700;
    color: #333;
  }
  
  p {
    margin: 0;
    color: #666;
    font-size: 1rem;
  }
}

.control-tabs {
  display: flex;
  gap: 5px;
  margin-bottom: 30px;
  border-bottom: 2px solid #e1e5e9;
  
  .tab-btn {
    padding: 12px 20px;
    border: none;
    background: transparent;
    color: #666;
    font-size: 0.95rem;
    font-weight: 500;
    cursor: pointer;
    border-bottom: 3px solid transparent;
    transition: all 0.3s ease;
    
    &:hover {
      color: var(--admin-primary);
      background: rgba(196, 154, 86, 0.05);
    }
    
    &.active {
      color: var(--admin-primary);
      border-bottom-color: var(--admin-primary);
      background: rgba(196, 154, 86, 0.1);
    }
  }
}

.tab-content {
  min-height: 500px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25px;
  
  h2 {
    margin: 0;
    font-size: 1.5rem;
    font-weight: 600;
    color: #333;
  }
  
  .add-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 10px 20px;
    background: var(--admin-primary);
    color: white;
    border: none;
    border-radius: 6px;
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    
    &:hover {
      background: var(--admin-primary-dark);
      transform: translateY(-1px);
    }
    
    .btn-icon {
      font-size: 14px;
    }
  }
  
  .account-filters {
    display: flex;
    gap: 15px;
    
    .filter-select, .search-input {
      padding: 8px 12px;
      border: 1px solid #e1e5e9;
      border-radius: 6px;
      font-size: 0.9rem;
      
      &:focus {
        outline: none;
        border-color: var(--admin-primary);
      }
    }
    
    .search-input {
      width: 250px;
    }
  }
}

// Templates Grid
.templates-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 20px;
  
  .template-card {
    background: white;
    border: 1px solid #e1e5e9;
    border-radius: 12px;
    padding: 20px;
    transition: all 0.3s ease;
    
    &:hover {
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
      transform: translateY(-2px);
    }
    
    .template-icon {
      text-align: center;
      margin-bottom: 15px;
      
      .file-icon {
        font-size: 3rem;
        color: var(--admin-primary);
      }
    }
    
    .template-info {
      text-align: center;
      margin-bottom: 20px;
      
      h3 {
        margin: 0 0 8px 0;
        font-size: 1.2rem;
        font-weight: 600;
        color: #333;
      }
      
      .template-type {
        margin: 0 0 10px 0;
        color: var(--admin-primary);
        font-weight: 500;
        font-size: 0.9rem;
      }
      
      .template-description {
        margin: 0 0 15px 0;
        color: #666;
        font-size: 0.9rem;
        line-height: 1.4;
      }
      
      .template-meta {
        display: flex;
        justify-content: space-between;
        font-size: 0.8rem;
        color: #999;
        
        .last-updated, .usage-count {
          display: block;
        }
      }
    }
    
    .template-actions {
      display: flex;
      gap: 8px;
      justify-content: center;
      
      .action-btn {
        display: flex;
        align-items: center;
        gap: 5px;
        padding: 8px 12px;
        border: none;
        border-radius: 4px;
        font-size: 0.8rem;
        cursor: pointer;
        transition: all 0.3s ease;
        
        .btn-icon {
          font-size: 12px;
        }
        
        &.edit {
          background: #17a2b8;
          color: white;
          
          &:hover {
            background: #138496;
          }
        }
        
        &.download {
          background: #6c757d;
          color: white;
          
          &:hover {
            background: #5a6268;
          }
        }
        
        &.delete {
          background: #dc3545;
          color: white;
          
          &:hover {
            background: #c82333;
          }
        }
      }
    }
  }
}

// Announcements List
.announcements-list {
  .announcement-card {
    background: white;
    border: 1px solid #e1e5e9;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 15px;
    
    .announcement-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: 15px;
      
      h3 {
        margin: 0;
        font-size: 1.2rem;
        font-weight: 600;
        color: #333;
        flex: 1;
      }
      
      .announcement-meta {
        display: flex;
        flex-direction: column;
        align-items: flex-end;
        gap: 5px;
        
        .priority {
          padding: 4px 8px;
          border-radius: 12px;
          font-size: 0.75rem;
          font-weight: 600;
          
          &.high {
            background: rgba(220, 53, 69, 0.1);
            color: #dc3545;
          }
          
          &.medium {
            background: rgba(255, 193, 7, 0.1);
            color: #ffc107;
          }
          
          &.low {
            background: rgba(40, 167, 69, 0.1);
            color: #28a745;
          }
        }
        
        .date {
          font-size: 0.8rem;
          color: #666;
        }
      }
    }
    
    .announcement-content {
      margin-bottom: 15px;
      
      p {
        margin: 0;
        color: #666;
        line-height: 1.5;
      }
    }
    
    .announcement-actions {
      display: flex;
      gap: 10px;
      
      .action-btn {
        padding: 6px 12px;
        border: none;
        border-radius: 4px;
        font-size: 0.85rem;
        cursor: pointer;
        transition: all 0.3s ease;
        
        &.edit {
          background: #17a2b8;
          color: white;
          
          &:hover {
            background: #138496;
          }
        }
        
        &.activate {
          background: #28a745;
          color: white;
          
          &:hover {
            background: #218838;
          }
        }
        
        &.deactivate {
          background: #ffc107;
          color: #212529;
          
          &:hover {
            background: #e0a800;
          }
        }
        
        &.delete {
          background: #dc3545;
          color: white;
          
          &:hover {
            background: #c82333;
          }
        }
      }
    }
  }
}

// Settings Sections
.settings-sections {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
  
  .settings-card {
    background: white;
    border: 1px solid #e1e5e9;
    border-radius: 8px;
    padding: 25px;
    
    h3 {
      margin: 0 0 20px 0;
      font-size: 1.2rem;
      font-weight: 600;
      color: #333;
      border-bottom: 1px solid #e1e5e9;
      padding-bottom: 10px;
    }
    
    .setting-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
      
      &:last-child {
        margin-bottom: 0;
      }
      
      label {
        font-weight: 500;
        color: #333;
        flex: 1;
      }
      
      .setting-input {
        width: 120px;
        padding: 6px 10px;
        border: 1px solid #e1e5e9;
        border-radius: 4px;
        font-size: 0.9rem;
        
        &:focus {
          outline: none;
          border-color: var(--admin-primary);
        }
      }
      
      .toggle-switch {
        position: relative;
        display: inline-block;
        width: 50px;
        height: 24px;
        
        input {
          opacity: 0;
          width: 0;
          height: 0;
        }
        
        .toggle-slider {
          position: absolute;
          cursor: pointer;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background-color: #ccc;
          transition: 0.3s;
          border-radius: 24px;
          
          &:before {
            position: absolute;
            content: "";
            height: 18px;
            width: 18px;
            left: 3px;
            bottom: 3px;
            background-color: white;
            transition: 0.3s;
            border-radius: 50%;
          }
        }
        
        input:checked + .toggle-slider {
          background-color: var(--admin-primary);
        }
        
        input:checked + .toggle-slider:before {
          transform: translateX(26px);
        }
      }
    }
  }
}

.settings-actions {
  display: flex;
  gap: 15px;
  justify-content: center;
  
  .save-btn, .reset-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 24px;
    border: none;
    border-radius: 6px;
    font-size: 0.95rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    
    .btn-icon {
      font-size: 16px;
    }
  }
  
  .save-btn {
    background: #28a745;
    color: white;
    
    &:hover {
      background: #218838;
    }
  }
  
  .reset-btn {
    background: #6c757d;
    color: white;
    
    &:hover {
      background: #5a6268;
    }
  }
}

// Accounts Table
.accounts-table {
  background: white;
  border: 1px solid #e1e5e9;
  border-radius: 8px;
  overflow: hidden;

  table {
    width: 100%;
    border-collapse: collapse;

    thead {
      background: #f8f9fa;

      th {
        padding: 15px;
        text-align: left;
        font-weight: 600;
        color: #333;
        border-bottom: 1px solid #e1e5e9;
      }
    }

    tbody {
      tr {
        border-bottom: 1px solid #f1f3f4;

        &:hover {
          background: #f8f9fa;
        }

        &:last-child {
          border-bottom: none;
        }

        td {
          padding: 15px;
          vertical-align: middle;

          .user-info {
            display: flex;
            align-items: center;
            gap: 12px;

            .user-avatar {
              width: 40px;
              height: 40px;
              border-radius: 50%;
              object-fit: cover;
            }

            .user-name {
              font-weight: 500;
              color: #333;
              margin-bottom: 2px;
            }

            .user-email {
              font-size: 0.85rem;
              color: #666;
            }
          }

          .account-type {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.75rem;
            font-weight: 600;

            &.lawyer {
              background: rgba(196, 154, 86, 0.1);
              color: var(--admin-primary);
            }

            &.client {
              background: rgba(23, 162, 184, 0.1);
              color: #17a2b8;
            }

            &.secretary {
              background: rgba(108, 117, 125, 0.1);
              color: #6c757d;
            }
          }

          .account-status {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.75rem;
            font-weight: 600;

            &.active {
              background: rgba(40, 167, 69, 0.1);
              color: #28a745;
            }

            &.inactive {
              background: rgba(108, 117, 125, 0.1);
              color: #6c757d;
            }

            &.flagged {
              background: rgba(220, 53, 69, 0.1);
              color: #dc3545;
            }
          }

          .account-actions {
            display: flex;
            gap: 8px;

            .action-btn {
              padding: 4px 8px;
              border: none;
              border-radius: 4px;
              font-size: 0.75rem;
              cursor: pointer;
              transition: all 0.3s ease;

              &.small {
                font-size: 0.7rem;
                padding: 3px 6px;
              }

              &.view {
                background: #17a2b8;
                color: white;

                &:hover {
                  background: #138496;
                }
              }

              &.activate {
                background: #28a745;
                color: white;

                &:hover {
                  background: #218838;
                }
              }

              &.deactivate {
                background: #ffc107;
                color: #212529;

                &:hover {
                  background: #e0a800;
                }
              }

              &.delete {
                background: #dc3545;
                color: white;

                &:hover {
                  background: #c82333;
                }
              }
            }
          }
        }
      }
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .platform-control-container {
    margin-left: 0;
    padding: 15px;
  }

  .control-tabs {
    flex-wrap: wrap;
    gap: 5px;

    .tab-btn {
      padding: 10px 15px;
      font-size: 0.85rem;
    }
  }

  .section-header {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;

    .account-filters {
      flex-direction: column;
      gap: 10px;

      .search-input {
        width: 100%;
      }
    }
  }

  .templates-grid {
    grid-template-columns: 1fr;
  }

  .settings-sections {
    grid-template-columns: 1fr;
  }

  .settings-actions {
    flex-direction: column;

    .save-btn, .reset-btn {
      justify-content: center;
    }
  }

  .accounts-table {
    overflow-x: auto;

    table {
      min-width: 600px;
    }
  }
}
