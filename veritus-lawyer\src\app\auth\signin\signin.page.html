<ion-content class="modern-auth-content">
  <div class="auth-wrapper">
    <!-- Background Elements -->
    <div class="background-elements">
      <div class="gold-accent-1"></div>
      <div class="gold-accent-2"></div>
      <div class="gold-accent-3"></div>
    </div>

    <!-- Main Content -->
    <div class="auth-main-container">
      <!-- Logo Section -->
      <div class="logo-section">
        <div class="logo-container">
          <div class="logo-icon">
            <ion-icon name="scale-outline" class="justice-scale-icon"></ion-icon>
          </div>
          <h1 class="brand-title">Veritus</h1>
          <p class="brand-subtitle">Legal Excellence Platform</p>
        </div>
      </div>

      <!-- Form Card -->
      <div class="form-card">
        <div class="form-header">
          <h2 class="form-title">Welcome Back</h2>
          <p class="form-subtitle">Sign in to your account</p>
        </div>

        <form [formGroup]="signinForm" (ngSubmit)="onSignIn()" class="modern-form">
          <!-- Email Field -->
          <div class="input-group">
            <label class="input-label">Email Address</label>
            <div class="input-container">
              <ion-icon name="mail-outline" class="input-icon"></ion-icon>
              <input
                type="email"
                formControlName="email"
                placeholder="Enter your email"
                class="modern-input">
            </div>
          </div>

          <!-- Password Field -->
          <div class="input-group">
            <label class="input-label">Password</label>
            <div class="input-container">
              <ion-icon name="lock-closed-outline" class="input-icon"></ion-icon>
              <input
                [type]="showPassword ? 'text' : 'password'"
                formControlName="password"
                placeholder="Enter your password"
                class="modern-input password-input">
              <button
                type="button"
                class="password-toggle-btn"
                (click)="togglePasswordVisibility()">
                <ion-icon
                  [name]="showPassword ? 'eye-off-outline' : 'eye-outline'"
                  class="password-toggle-icon">
                </ion-icon>
              </button>
            </div>
          </div>

          <!-- Forgot Password Link -->
          <div class="forgot-password-section">
            <button type="button" class="forgot-link" (click)="goToForgotPassword()">
              Forgot Password?
            </button>
          </div>

          <!-- Sign In Button -->
          <button
            type="submit"
            class="modern-signin-btn"
            [disabled]="!signinForm.valid">
            <span class="btn-text">Sign In</span>
            <ion-icon name="arrow-forward" class="btn-icon"></ion-icon>
          </button>
        </form>

        <!-- Divider -->
        <div class="divider-section">
          <div class="divider-line"></div>
          <span class="divider-text">OR</span>
          <div class="divider-line"></div>
        </div>

        <!-- Google Sign In -->
        <button type="button" class="google-signin-btn" (click)="onGoogleSignIn()">
          <div class="google-icon-container">
            <svg class="google-logo" viewBox="0 0 24 24" width="20" height="20">
              <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
              <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
              <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
              <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
            </svg>
          </div>
          <span class="google-text">Continue with Google</span>
        </button>

        <!-- Register Link -->
        <div class="register-section">
          <span class="register-text">Don't have an account? </span>
          <button type="button" class="register-link" (click)="goToRegister()">
            Sign Up
          </button>
        </div>
      </div>
    </div>
  </div>
</ion-content>
