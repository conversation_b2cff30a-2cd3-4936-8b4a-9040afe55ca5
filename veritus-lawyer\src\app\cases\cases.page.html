<ion-content class="retainer-content">
  <div class="status-bar-spacer"></div>

  <div class="retainer-container">

    <div class="page-header">
      <h1 class="page-title">Track Cases</h1>
    </div>

    <div class="tab-navigation">
      <button 
        class="tab-button" 
        [class.tab-active]="selectedTab === 'ongoing'"
        (click)="selectTab('ongoing')">
        <span class="tab-text">On going</span>
      </button>
    
      <button 
        class="tab-button" 
        [class.tab-active]="selectedTab === 'closed'"
        (click)="selectTab('closed')">
        <span class="tab-text">Closed</span>
      </button>
    
      <button 
        class="tab-button" 
        [class.tab-active]="selectedTab === 'cancelled'"
        (click)="selectTab('cancelled')">
        <span class="tab-text">Cancelled</span>
      </button>
    </div>

    <div class="cases-list">
      <div class="case-item" *ngFor="let case of filteredCases">
        <ion-card class="case-card">
          <ion-item lines="none">
            <ion-avatar slot="start">
              <img [src]="case.avatar" />
            </ion-avatar>
            <ion-label>
              <p><strong>Client Name:</strong> {{ case.clientName }}</p>
              <p><strong>Case Status:</strong> <span class="case-status-text">{{ case.status | titlecase }}</span></p>
            </ion-label>
            <ion-button fill="clear" size="small" color="primary" slot="end">Archive</ion-button>
          </ion-item>
          <div class="case-progress">
            <a class="progress-link" (click)="onTrackCaseProgress(case)">Case Progress &gt;</a>
          </div>
        </ion-card>
      </div>
    </div>

  </div> <!-- ✅ closes the outer .cases-container -->
</ion-content>
