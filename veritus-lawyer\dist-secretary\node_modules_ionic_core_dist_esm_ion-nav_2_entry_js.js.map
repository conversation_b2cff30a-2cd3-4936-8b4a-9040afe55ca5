{"version": 3, "file": "node_modules_ionic_core_dist_esm_ion-nav_2_entry_js.js", "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AAC6G;AACnC;AACM;AAC2F;AACjG;AACF;AAExE,MAAM6B,cAAc,GAAG,CAAC;AACxB,MAAMC,mBAAmB,GAAG,CAAC;AAC7B,MAAMC,oBAAoB,GAAG,CAAC;AAC9B;AACA,MAAMC,cAAc,CAAC;EACjBC,WAAWA,CAACC,SAAS,EAAEC,MAAM,EAAE;IAC3B,IAAI,CAACD,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,KAAK,GAAGP,cAAc;EAC/B;EACMQ,IAAIA,CAACC,SAAS,EAAE;IAAA,IAAAC,KAAA;IAAA,OAAAC,6KAAA;MAClBD,KAAI,CAACH,KAAK,GAAGN,mBAAmB;MAChC,IAAI,CAACS,KAAI,CAACE,OAAO,EAAE;QACf,MAAMP,SAAS,GAAGK,KAAI,CAACL,SAAS;QAChCK,KAAI,CAACE,OAAO,SAASb,kEAAe,CAACW,KAAI,CAACG,QAAQ,EAAEJ,SAAS,EAAEJ,SAAS,EAAE,CAAC,UAAU,EAAE,oBAAoB,CAAC,EAAEK,KAAI,CAACJ,MAAM,CAAC;MAC9H;IAAC;EACL;EACA;AACJ;AACA;EACIQ,QAAQA,CAAA,EAAG;IACP/B,uDAAM,CAAC,IAAI,CAACwB,KAAK,KAAKL,oBAAoB,EAAE,6BAA6B,CAAC;IAC1E,MAAMU,OAAO,GAAG,IAAI,CAACA,OAAO;IAC5B,IAAIA,OAAO,EAAE;MACT,IAAI,IAAI,CAACC,QAAQ,EAAE;QACf,IAAI,CAACA,QAAQ,CAACE,iBAAiB,CAACH,OAAO,CAACI,aAAa,EAAEJ,OAAO,CAAC;MACnE,CAAC,MACI;QACDA,OAAO,CAACK,MAAM,CAAC,CAAC;MACpB;IACJ;IACA,IAAI,CAACC,GAAG,GAAGC,SAAS;IACpB,IAAI,CAACZ,KAAK,GAAGL,oBAAoB;EACrC;AACJ;AACA,MAAMkB,OAAO,GAAGA,CAACC,IAAI,EAAEC,EAAE,EAAEhB,MAAM,KAAK;EAClC,IAAI,CAACe,IAAI,EAAE;IACP,OAAO,KAAK;EAChB;EACA,IAAIA,IAAI,CAAChB,SAAS,KAAKiB,EAAE,EAAE;IACvB,OAAO,KAAK;EAChB;EACA,OAAOrC,uDAAqB,CAACoC,IAAI,CAACf,MAAM,EAAEA,MAAM,CAAC;AACrD,CAAC;AACD,MAAMiB,aAAa,GAAGA,CAACC,IAAI,EAAElB,MAAM,KAAK;EACpC,IAAI,CAACkB,IAAI,EAAE;IACP,OAAO,IAAI;EACf;EACA,IAAIA,IAAI,YAAYrB,cAAc,EAAE;IAChC,OAAOqB,IAAI;EACf;EACA,OAAO,IAAIrB,cAAc,CAACqB,IAAI,EAAElB,MAAM,CAAC;AAC3C,CAAC;AACD,MAAMmB,cAAc,GAAIC,KAAK,IAAK;EAC9B,OAAOA,KAAK,CACPC,GAAG,CAAEH,IAAI,IAAK;IACf,IAAIA,IAAI,YAAYrB,cAAc,EAAE;MAChC,OAAOqB,IAAI;IACf;IACA,IAAI,WAAW,IAAIA,IAAI,EAAE;MACrB,OAAOD,aAAa,CAACC,IAAI,CAACnB,SAAS,EAAEmB,IAAI,CAACI,cAAc,KAAK,IAAI,GAAGT,SAAS,GAAGK,IAAI,CAACI,cAAc,CAAC;IACxG;IACA,OAAOL,aAAa,CAACC,IAAI,EAAEL,SAAS,CAAC;EACzC,CAAC,CAAC,CACGU,MAAM,CAAEC,CAAC,IAAKA,CAAC,KAAK,IAAI,CAAC;AAClC,CAAC;AAED,MAAMC,MAAM,GAAG,4FAA4F;AAC3G,MAAMC,YAAY,GAAGD,MAAM;AAE3B,MAAME,GAAG,GAAG,MAAM;EACd7B,WAAWA,CAAC8B,OAAO,EAAE;IACjB9D,qDAAgB,CAAC,IAAI,EAAE8D,OAAO,CAAC;IAC/B,IAAI,CAACC,cAAc,GAAG7D,qDAAW,CAAC,IAAI,EAAE,gBAAgB,EAAE,CAAC,CAAC;IAC5D,IAAI,CAAC8D,gBAAgB,GAAG9D,qDAAW,CAAC,IAAI,EAAE,kBAAkB,EAAE,CAAC,CAAC;IAChE,IAAI,CAAC+D,eAAe,GAAG/D,qDAAW,CAAC,IAAI,EAAE,iBAAiB,EAAE,CAAC,CAAC;IAC9D,IAAI,CAACgE,UAAU,GAAG,EAAE;IACpB,IAAI,CAACC,4BAA4B,GAAG,KAAK;IACzC,IAAI,CAACC,SAAS,GAAG,KAAK;IACtB,IAAI,CAACC,eAAe,GAAG,KAAK;IAC5B,IAAI,CAACC,SAAS,GAAG,KAAK;IACtB,IAAI,CAACC,KAAK,GAAG,EAAE;IACf,IAAI,CAACC,OAAO,GAAG,KAAK;IACpB,IAAI,CAAC/B,QAAQ,GAAGM,SAAS;IACzB,IAAI,CAAC0B,YAAY,GAAG1B,SAAS;IAC7B,IAAI,CAAC2B,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACC,SAAS,GAAG5B,SAAS;IAC1B,IAAI,CAAC6B,UAAU,GAAG7B,SAAS;IAC3B,IAAI,CAAC8B,IAAI,GAAG9B,SAAS;EACzB;EACA+B,mBAAmBA,CAAA,EAAG;IAClB,IAAI,IAAI,CAACC,OAAO,EAAE;MACd,IAAI,CAACA,OAAO,CAACC,MAAM,CAAC,IAAI,CAACP,YAAY,KAAK,IAAI,CAAC;IACnD;EACJ;EACAQ,WAAWA,CAAA,EAAG;IACV,IAAI,IAAI,CAACJ,IAAI,KAAK9B,SAAS,EAAE;MACzB;IACJ;IACA,IAAI,IAAI,CAACyB,OAAO,KAAK,KAAK,EAAE;MACxB;AACZ;AACA;AACA;MACY;IACJ;IACA,IAAI,CAAC,IAAI,CAACJ,SAAS,EAAE;MACjB,IAAI,IAAI,CAACS,IAAI,KAAK9B,SAAS,EAAE;QACzB,IAAI,CAACmC,OAAO,CAAC,IAAI,CAACL,IAAI,EAAE,IAAI,CAACD,UAAU,CAAC;MAC5C;IACJ;EACJ;EACAO,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAACf,SAAS,GAAGgB,QAAQ,CAACC,aAAa,CAAC,YAAY,CAAC,KAAK,IAAI,IAAI,IAAI,CAACC,EAAE,CAACC,OAAO,CAAC,aAAa,CAAC,KAAK,IAAI;IACzG,IAAI,IAAI,CAACd,YAAY,KAAK1B,SAAS,EAAE;MACjC,MAAMyC,IAAI,GAAGhE,4DAAU,CAAC,IAAI,CAAC;MAC7B,IAAI,CAACiD,YAAY,GAAGhD,wDAAM,CAACgE,UAAU,CAAC,kBAAkB,EAAED,IAAI,KAAK,KAAK,CAAC;IAC7E;IACA,IAAI,CAACzB,cAAc,CAAC2B,IAAI,CAAC,CAAC;EAC9B;EACMC,gBAAgBA,CAAA,EAAG;IAAA,IAAAC,MAAA;IAAA,OAAArD,6KAAA;MACrB;MACAqD,MAAI,CAACpB,OAAO,GAAG,IAAI;MACnBoB,MAAI,CAACX,WAAW,CAAC,CAAC;MAClBW,MAAI,CAACb,OAAO,GAAG,OAAO,yIAAkC,EAAEc,sBAAsB,CAACD,MAAI,CAACN,EAAE,EAAEM,MAAI,CAACE,QAAQ,CAACC,IAAI,CAACH,MAAI,CAAC,EAAEA,MAAI,CAACI,OAAO,CAACD,IAAI,CAACH,MAAI,CAAC,EAAEA,MAAI,CAACK,MAAM,CAACF,IAAI,CAACH,MAAI,CAAC,EAAEA,MAAI,CAACM,KAAK,CAACH,IAAI,CAACH,MAAI,CAAC,CAAC;MAC3LA,MAAI,CAACd,mBAAmB,CAAC,CAAC;IAAC;EAC/B;EACAqB,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAAC7B,SAAS,GAAG,KAAK;EAC1B;EACA8B,oBAAoBA,CAAA,EAAG;IACnB,KAAK,MAAMnD,IAAI,IAAI,IAAI,CAACsB,KAAK,EAAE;MAC3BxD,qDAAS,CAACkC,IAAI,CAACT,OAAO,EAAErB,iDAAqB,CAAC;MAC9C8B,IAAI,CAACP,QAAQ,CAAC,CAAC;IACnB;IACA;IACA,IAAI,IAAI,CAACqC,OAAO,EAAE;MACd,IAAI,CAACA,OAAO,CAACsB,OAAO,CAAC,CAAC;MACtB,IAAI,CAACtB,OAAO,GAAGhC,SAAS;IAC5B;IACA,IAAI,CAACmB,UAAU,CAACoC,MAAM,GAAG,CAAC;IAC1B,IAAI,CAAC/B,KAAK,CAAC+B,MAAM,GAAG,CAAC;IACrB,IAAI,CAAChC,SAAS,GAAG,IAAI;EACzB;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIiC,IAAIA,CAACtE,SAAS,EAAEuB,cAAc,EAAEgD,IAAI,EAAEC,IAAI,EAAE;IACxC,OAAO,IAAI,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEzE,SAAS,EAAEuB,cAAc,EAAEgD,IAAI,EAAEC,IAAI,CAAC;EACjE;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIC,MAAMA,CAACC,WAAW,EAAE1E,SAAS,EAAEuB,cAAc,EAAEgD,IAAI,EAAEC,IAAI,EAAE;IACvD,OAAO,IAAI,CAACG,WAAW,CAACD,WAAW,EAAE,CAAC;MAAE1E,SAAS;MAAEuB;IAAe,CAAC,CAAC,EAAEgD,IAAI,EAAEC,IAAI,CAAC;EACrF;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIG,WAAWA,CAACD,WAAW,EAAEE,gBAAgB,EAAEL,IAAI,EAAEC,IAAI,EAAE;IACnD,OAAO,IAAI,CAACK,SAAS,CAAC;MAClBC,WAAW,EAAEJ,WAAW;MACxBK,WAAW,EAAEH,gBAAgB;MAC7BL;IACJ,CAAC,EAAEC,IAAI,CAAC;EACZ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIQ,GAAGA,CAACT,IAAI,EAAEC,IAAI,EAAE;IACZ,OAAO,IAAI,CAACS,WAAW,CAAC,CAAC,CAAC,EAAE,CAAC,EAAEV,IAAI,EAAEC,IAAI,CAAC;EAC9C;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIU,KAAKA,CAACC,eAAe,EAAEZ,IAAI,EAAEC,IAAI,EAAE;IAC/B,MAAMY,EAAE,GAAG;MACPC,WAAW,EAAE,CAAC,CAAC;MACfC,WAAW,EAAE,CAAC,CAAC;MACff;IACJ,CAAC;IACD,IAAI,OAAOY,eAAe,KAAK,QAAQ,IAAIA,eAAe,CAACnF,SAAS,EAAE;MAClEoF,EAAE,CAACG,UAAU,GAAGJ,eAAe;MAC/BC,EAAE,CAACC,WAAW,GAAG,CAAC;IACtB,CAAC,MACI,IAAI,OAAOF,eAAe,KAAK,QAAQ,EAAE;MAC1CC,EAAE,CAACC,WAAW,GAAGF,eAAe,GAAG,CAAC;IACxC;IACA,OAAO,IAAI,CAACN,SAAS,CAACO,EAAE,EAAEZ,IAAI,CAAC;EACnC;EACA;AACJ;AACA;AACA;AACA;AACA;EACIgB,SAASA,CAACjB,IAAI,EAAEC,IAAI,EAAE;IAClB,OAAO,IAAI,CAACS,WAAW,CAAC,CAAC,EAAE,CAAC,CAAC,EAAEV,IAAI,EAAEC,IAAI,CAAC;EAC9C;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIS,WAAWA,CAACQ,UAAU,EAAEH,WAAW,GAAG,CAAC,EAAEf,IAAI,EAAEC,IAAI,EAAE;IACjD,OAAO,IAAI,CAACK,SAAS,CAAC;MAClBQ,WAAW,EAAEI,UAAU;MACvBH,WAAW;MACXf;IACJ,CAAC,EAAEC,IAAI,CAAC;EACZ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIvB,OAAOA,CAACjD,SAAS,EAAEuB,cAAc,EAAEgD,IAAI,EAAEC,IAAI,EAAE;IAC3C,OAAO,IAAI,CAACkB,QAAQ,CAAC,CAAC;MAAE1F,SAAS;MAAEuB;IAAe,CAAC,CAAC,EAAEgD,IAAI,EAAEC,IAAI,CAAC;EACrE;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIkB,QAAQA,CAACpD,KAAK,EAAEiC,IAAI,EAAEC,IAAI,EAAE;IACxBD,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAK,KAAK,CAAC,GAAGA,IAAI,GAAIA,IAAI,GAAG,CAAC,CAAE;IACrD;IACA,IAAIA,IAAI,CAAC9B,QAAQ,KAAK,IAAI,EAAE;MACxB8B,IAAI,CAAC9B,QAAQ,GAAG,KAAK;IACzB;IACA,OAAO,IAAI,CAACoC,SAAS,CAAC;MAClBC,WAAW,EAAE,CAAC;MACdC,WAAW,EAAEzC,KAAK;MAClB+C,WAAW,EAAE,CAAC;MACdC,WAAW,EAAE,CAAC,CAAC;MACff;IACJ,CAAC,EAAEC,IAAI,CAAC;EACZ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACImB,UAAUA,CAAC1E,EAAE,EAAEhB,MAAM,EAAE2F,SAAS,EAAElD,SAAS,EAAE;IACzC,MAAMmD,MAAM,GAAG,IAAI,CAACC,aAAa,CAAC,CAAC;IACnC,IAAI/E,OAAO,CAAC8E,MAAM,EAAE5E,EAAE,EAAEhB,MAAM,CAAC,EAAE;MAC7B,OAAO8F,OAAO,CAACC,OAAO,CAAC;QACnBC,OAAO,EAAE,KAAK;QACd1F,OAAO,EAAEsF,MAAM,CAACtF;MACpB,CAAC,CAAC;IACN;IACA,IAAIyF,OAAO;IACX,MAAME,OAAO,GAAG,IAAIH,OAAO,CAAEjI,CAAC,IAAMkI,OAAO,GAAGlI,CAAE,CAAC;IACjD,IAAIqI,MAAM;IACV,MAAMC,UAAU,GAAG;MACfC,SAAS,EAAE,KAAK;MAChBC,WAAW,EAAGC,UAAU,IAAK;QACzB,IAAIC,IAAI;QACR,MAAMC,CAAC,GAAG,IAAIV,OAAO,CAAEjI,CAAC,IAAM0I,IAAI,GAAG1I,CAAE,CAAC;QACxCkI,OAAO,CAAC;UACJC,OAAO,EAAE,IAAI;UACb1F,OAAO,EAAEgG,UAAU;UACnBG,WAAW;YAAA,IAAAC,IAAA,GAAArG,6KAAA,CAAE,aAAY;cACrBkG,IAAI,CAAC,CAAC;cACN,MAAML,MAAM;YAChB,CAAC;YAAA,gBAHDO,WAAWA,CAAA;cAAA,OAAAC,IAAA,CAAAC,KAAA,OAAAC,SAAA;YAAA;UAAA;QAIf,CAAC,CAAC;QACF,OAAOJ,CAAC;MACZ;IACJ,CAAC;IACD,IAAIb,SAAS,KAAK,MAAM,EAAE;MACtBO,MAAM,GAAG,IAAI,CAAClD,OAAO,CAAChC,EAAE,EAAEhB,MAAM,EAAEmG,UAAU,CAAC;IACjD,CAAC,MACI;MACD;MACA,MAAMU,cAAc,GAAG,IAAI,CAACxE,KAAK,CAACyE,IAAI,CAAEtF,CAAC,IAAKV,OAAO,CAACU,CAAC,EAAER,EAAE,EAAEhB,MAAM,CAAC,CAAC;MACrE,IAAI6G,cAAc,EAAE;QAChBX,MAAM,GAAG,IAAI,CAACjB,KAAK,CAAC4B,cAAc,EAAEE,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEb,UAAU,CAAC,EAAE;UAAER,SAAS,EAAE,MAAM;UAAEsB,gBAAgB,EAAExE;QAAU,CAAC,CAAC,CAAC;MACzI,CAAC,MACI,IAAIkD,SAAS,KAAK,SAAS,EAAE;QAC9BO,MAAM,GAAG,IAAI,CAAC7B,IAAI,CAACrD,EAAE,EAAEhB,MAAM,EAAE+G,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEb,UAAU,CAAC,EAAE;UAAEc,gBAAgB,EAAExE;QAAU,CAAC,CAAC,CAAC;MACjH,CAAC,MACI,IAAIkD,SAAS,KAAK,MAAM,EAAE;QAC3BO,MAAM,GAAG,IAAI,CAAClD,OAAO,CAAChC,EAAE,EAAEhB,MAAM,EAAE+G,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEb,UAAU,CAAC,EAAE;UAAER,SAAS,EAAE,MAAM;UAAEnD,QAAQ,EAAE,IAAI;UAAEyE,gBAAgB,EAAExE;QAAU,CAAC,CAAC,CAAC;MACvJ;IACJ;IACA,OAAOwD,OAAO;EAClB;EACA;AACJ;AACA;AACA;AACA;EACUiB,UAAUA,CAAA,EAAG;IAAA,IAAAC,MAAA;IAAA,OAAA9G,6KAAA;MACf,MAAMuF,MAAM,GAAGuB,MAAI,CAACtB,aAAa,CAAC,CAAC;MACnC,IAAID,MAAM,EAAE;QACR,OAAO;UACH5E,EAAE,EAAE4E,MAAM,CAACtF,OAAO,CAAC8G,OAAO;UAC1BpH,MAAM,EAAE4F,MAAM,CAAC5F,MAAM;UACrBM,OAAO,EAAEsF,MAAM,CAACtF;QACpB,CAAC;MACL;MACA,OAAOO,SAAS;IAAC;EACrB;EACA;AACJ;AACA;EACUwG,SAASA,CAAA,EAAG;IAAA,IAAAC,MAAA;IAAA,OAAAjH,6KAAA;MACd,OAAOiH,MAAI,CAACzB,aAAa,CAAC,CAAC;IAAC;EAChC;EACA;AACJ;AACA;AACA;AACA;EACU0B,UAAUA,CAACC,KAAK,EAAE;IAAA,IAAAC,MAAA;IAAA,OAAApH,6KAAA;MACpB,OAAOoH,MAAI,CAACpF,KAAK,CAACmF,KAAK,CAAC;IAAC;EAC7B;EACA;AACJ;AACA;AACA;AACA;EACUE,SAASA,CAAC3G,IAAI,EAAE;IAAA,IAAA4G,MAAA;IAAA,OAAAtH,6KAAA;MAClB,OAAOsH,MAAI,CAACC,aAAa,CAAC7G,IAAI,CAAC;IAAC;EACpC;EACA;AACJ;AACA;AACA;AACA;EACU8G,WAAWA,CAAC9G,IAAI,EAAE;IAAA,IAAA+G,MAAA;IAAA,OAAAzH,6KAAA;MACpB,OAAOyH,MAAI,CAACC,eAAe,CAAChH,IAAI,CAAC;IAAC;EACtC;EACAiH,SAASA,CAAA,EAAG;IACR,OAAO,IAAI,CAAC3F,KAAK,CAAC+B,MAAM;EAC5B;EACAyB,aAAaA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACxD,KAAK,CAAC,IAAI,CAACA,KAAK,CAAC+B,MAAM,GAAG,CAAC,CAAC;EAC5C;EACAwD,aAAaA,CAAC7G,IAAI,GAAG,IAAI,CAAC8E,aAAa,CAAC,CAAC,EAAE;IACvC,OAAO,CAAC,EAAE9E,IAAI,IAAI,IAAI,CAACgH,eAAe,CAAChH,IAAI,CAAC,CAAC;EACjD;EACAgH,eAAeA,CAAChH,IAAI,GAAG,IAAI,CAAC8E,aAAa,CAAC,CAAC,EAAE;IACzC,IAAI,CAAC9E,IAAI,EAAE;MACP,OAAOF,SAAS;IACpB;IACA,MAAMwB,KAAK,GAAG,IAAI,CAACA,KAAK;IACxB,MAAMmF,KAAK,GAAGnF,KAAK,CAAC4F,OAAO,CAAClH,IAAI,CAAC;IACjC,OAAOyG,KAAK,GAAG,CAAC,GAAGnF,KAAK,CAACmF,KAAK,GAAG,CAAC,CAAC,GAAG3G,SAAS;EACnD;EACA;AACJ;AACA;AACA;AACA;EACU+D,SAASA,CAACO,EAAE,EAAEZ,IAAI,EAAE;IAAA,IAAA2D,MAAA;IAAA,OAAA7H,6KAAA;MACtB,IAAI8H,EAAE,EAAEC,EAAE;MACV,IAAIF,MAAI,CAAC/F,eAAe,KAAK,CAACgG,EAAE,GAAGhD,EAAE,CAACb,IAAI,MAAM,IAAI,IAAI6D,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACE,UAAU,CAAC,EAAE;QAC7F,OAAO,KAAK;MAChB;MACA,MAAMpC,OAAO,GAAG,IAAIH,OAAO,CAAC,CAACC,OAAO,EAAEuC,MAAM,KAAK;QAC7CnD,EAAE,CAACY,OAAO,GAAGA,OAAO;QACpBZ,EAAE,CAACmD,MAAM,GAAGA,MAAM;MACtB,CAAC,CAAC;MACFnD,EAAE,CAACZ,IAAI,GAAGA,IAAI;MACd;AACR;AACA;AACA;AACA;AACA;MACQ,IAAIY,EAAE,CAACb,IAAI,IAAIa,EAAE,CAACb,IAAI,CAAC8B,SAAS,KAAK,KAAK,IAAI8B,MAAI,CAAChG,SAAS,EAAE;QAC1D,MAAMqG,MAAM,GAAGrF,QAAQ,CAACC,aAAa,CAAC,YAAY,CAAC;QACnD,IAAIoF,MAAM,EAAE;UACR,MAAMC,aAAa,SAASD,MAAM,CAACC,aAAa,CAAC,CAAC;UAClD,IAAIA,aAAa,KAAK,KAAK,EAAE;YACzB,OAAO,KAAK;UAChB;UACA,IAAI,OAAOA,aAAa,KAAK,QAAQ,EAAE;YACnCD,MAAM,CAAClE,IAAI,CAACmE,aAAa,EAAErD,EAAE,CAACb,IAAI,CAACqB,SAAS,IAAI,MAAM,CAAC;YACvD,OAAO,KAAK;UAChB;QACJ;MACJ;MACA;MACA,IAAI,CAAC,CAACyC,EAAE,GAAGjD,EAAE,CAACL,WAAW,MAAM,IAAI,IAAIsD,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAChE,MAAM,MAAM,CAAC,EAAE;QAC9Ee,EAAE,CAACL,WAAW,GAAGjE,SAAS;MAC9B;MACA;MACAqH,MAAI,CAAClG,UAAU,CAACqC,IAAI,CAACc,EAAE,CAAC;MACxB;MACA;MACA+C,MAAI,CAACO,QAAQ,CAAC,CAAC;MACf,OAAOxC,OAAO;IAAC;EACnB;EACAyC,OAAOA,CAACC,MAAM,EAAExD,EAAE,EAAE;IAChB,IAAI,IAAI,CAAC/C,SAAS,EAAE;MAChB,IAAI,CAACwG,SAAS,CAAC,8BAA8B,EAAEzD,EAAE,CAAC;MAClD;IACJ;IACA,IAAIA,EAAE,CAACZ,IAAI,EAAE;MACTY,EAAE,CAACZ,IAAI,CAACoE,MAAM,CAACE,YAAY,EAAEF,MAAM,CAACG,kBAAkB,EAAEH,MAAM,CAACI,YAAY,EAAEJ,MAAM,CAACK,WAAW,EAAEL,MAAM,CAAChD,SAAS,CAAC;IACtH;IACAR,EAAE,CAACY,OAAO,CAAC4C,MAAM,CAACE,YAAY,CAAC;IAC/B,IAAI1D,EAAE,CAACb,IAAI,CAAC8B,SAAS,KAAK,KAAK,IAAI,IAAI,CAAClE,SAAS,EAAE;MAC/C,MAAMqG,MAAM,GAAGrF,QAAQ,CAACC,aAAa,CAAC,YAAY,CAAC;MACnD,IAAIoF,MAAM,EAAE;QACR,MAAM5C,SAAS,GAAGgD,MAAM,CAAChD,SAAS,KAAK,MAAM,GAAG,MAAM,GAAG,SAAS;QAClE4C,MAAM,CAACU,UAAU,CAACtD,SAAS,CAAC;MAChC;IACJ;EACJ;EACAuD,MAAMA,CAACC,YAAY,EAAEhE,EAAE,EAAE;IACrB,IAAI,IAAI,CAAC/C,SAAS,EAAE;MAChB,IAAI,CAACwG,SAAS,CAAC,8BAA8B,EAAEzD,EAAE,CAAC;MAClD;IACJ;IACA,IAAI,CAACnD,UAAU,CAACoC,MAAM,GAAG,CAAC;IAC1B,IAAI,CAACwE,SAAS,CAACO,YAAY,EAAEhE,EAAE,CAAC;EACpC;EACAyD,SAASA,CAACO,YAAY,EAAEhE,EAAE,EAAE;IACxB,IAAIA,EAAE,CAACZ,IAAI,EAAE;MACTY,EAAE,CAACZ,IAAI,CAAC,KAAK,EAAE,KAAK,EAAE4E,YAAY,CAAC;IACvC;IACA,IAAIhE,EAAE,CAACmD,MAAM,IAAI,CAAC,IAAI,CAAClG,SAAS,EAAE;MAC9B+C,EAAE,CAACmD,MAAM,CAACa,YAAY,CAAC;IAC3B,CAAC,MACI;MACDhE,EAAE,CAACY,OAAO,CAAC,KAAK,CAAC;IACrB;EACJ;EACA;AACJ;AACA;AACA;AACA;EACI0C,QAAQA,CAAA,EAAG;IACP;IACA;IACA,IAAI,IAAI,CAACtG,eAAe,EAAE;MACtB,OAAO,KAAK;IAChB;IACA;IACA,MAAMgD,EAAE,GAAG,IAAI,CAACnD,UAAU,CAACoH,KAAK,CAAC,CAAC;IAClC,IAAI,CAACjE,EAAE,EAAE;MACL,OAAO,KAAK;IAChB;IACA,IAAI,CAACkE,aAAa,CAAClE,EAAE,CAAC;IACtB,OAAO,IAAI;EACf;EACA;EACMkE,aAAaA,CAAClE,EAAE,EAAE;IAAA,IAAAmE,MAAA;IAAA,OAAAjJ,6KAAA;MACpB,IAAI;QACA;QACAiJ,MAAI,CAACxH,gBAAgB,CAAC0B,IAAI,CAAC,CAAC;QAC5B8F,MAAI,CAACnH,eAAe,GAAG,IAAI;QAC3BmH,MAAI,CAACC,SAAS,CAACpE,EAAE,CAAC;QAClB,MAAM6D,WAAW,GAAGM,MAAI,CAACzD,aAAa,CAAC,CAAC;QACxC,MAAMkD,YAAY,GAAGO,MAAI,CAACE,eAAe,CAACrE,EAAE,EAAE6D,WAAW,CAAC;QAC1D,IAAI,CAACA,WAAW,IAAI,CAACD,YAAY,EAAE;UAC/B,MAAM,IAAIU,KAAK,CAAC,qCAAqC,CAAC;QAC1D;QACA,IAAIV,YAAY,IAAIA,YAAY,CAAC9I,KAAK,KAAKP,cAAc,EAAE;UACvD,MAAMqJ,YAAY,CAAC7I,IAAI,CAACoJ,MAAI,CAAClG,EAAE,CAAC;QACpC;QACAkG,MAAI,CAACI,YAAY,CAACX,YAAY,EAAEC,WAAW,EAAE7D,EAAE,CAAC;QAChD;QACA,MAAM2D,kBAAkB,GAAG,CAAC3D,EAAE,CAACwE,0BAA0B,IAAIxE,EAAE,CAACyE,yBAAyB,KAAKb,YAAY,KAAKC,WAAW;QAC1H,IAAIF,kBAAkB,IAAI3D,EAAE,CAACb,IAAI,IAAI0E,WAAW,EAAE;UAC9C,MAAMa,eAAe,GAAG1E,EAAE,CAACb,IAAI,CAACqB,SAAS,KAAK,MAAM;UACpD;AAChB;AACA;AACA;UACgB,IAAIkE,eAAe,EAAE;YACjB1E,EAAE,CAACb,IAAI,CAAC2C,gBAAgB,GAAG9B,EAAE,CAACb,IAAI,CAAC2C,gBAAgB,KAAK8B,YAAY,KAAK,IAAI,IAAIA,YAAY,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,YAAY,CAAC9B,gBAAgB,CAAC;UACtJ;UACA+B,WAAW,CAAC/B,gBAAgB,GAAG9B,EAAE,CAACb,IAAI,CAAC2C,gBAAgB;QAC3D;QACA,IAAI0B,MAAM;QACV,IAAIG,kBAAkB,EAAE;UACpBH,MAAM,SAASW,MAAI,CAACvK,UAAU,CAACgK,YAAY,EAAEC,WAAW,EAAE7D,EAAE,CAAC;QACjE,CAAC,MACI;UACD;UACA;UACA;UACA;UACAwD,MAAM,GAAG;YACLE,YAAY,EAAE,IAAI;YAClBC,kBAAkB,EAAE;UACxB,CAAC;QACL;QACAQ,MAAI,CAACZ,OAAO,CAACC,MAAM,EAAExD,EAAE,CAAC;QACxBmE,MAAI,CAACvH,eAAe,CAACyB,IAAI,CAAC,CAAC;MAC/B,CAAC,CACD,OAAO2F,YAAY,EAAE;QACjBG,MAAI,CAACJ,MAAM,CAACC,YAAY,EAAEhE,EAAE,CAAC;MACjC;MACAmE,MAAI,CAACnH,eAAe,GAAG,KAAK;MAC5BmH,MAAI,CAACb,QAAQ,CAAC,CAAC;IAAC;EACpB;EACAc,SAASA,CAACpE,EAAE,EAAE;IACV,IAAIgD,EAAE,EAAEC,EAAE;IACV,IAAI0B,EAAE;IACN,MAAMC,WAAW,GAAG,IAAI,CAAC1H,KAAK,CAAC+B,MAAM;IACrC,CAAC+D,EAAE,GAAGhD,EAAE,CAACb,IAAI,MAAM,IAAI,IAAI6D,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAIhD,EAAE,CAACb,IAAI,GAAG,CAAC,CAAE;IAC9D,CAAC8D,EAAE,GAAG,CAAC0B,EAAE,GAAG3E,EAAE,CAACb,IAAI,EAAE/D,QAAQ,MAAM,IAAI,IAAI6H,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAI0B,EAAE,CAACvJ,QAAQ,GAAG,IAAI,CAACA,QAAS;IAC7F,IAAI4E,EAAE,CAACG,UAAU,KAAKzE,SAAS,EAAE;MAC7BpC,uDAAM,CAAC0G,EAAE,CAACC,WAAW,KAAKvE,SAAS,EAAE,8BAA8B,CAAC;MACpEpC,uDAAM,CAAC0G,EAAE,CAACE,WAAW,KAAKxE,SAAS,EAAE,8BAA8B,CAAC;MACpE,MAAM2G,KAAK,GAAG,IAAI,CAACnF,KAAK,CAAC4F,OAAO,CAAC9C,EAAE,CAACG,UAAU,CAAC;MAC/C,IAAIkC,KAAK,GAAG,CAAC,EAAE;QACX,MAAM,IAAIiC,KAAK,CAAC,0BAA0B,CAAC;MAC/C;MACAtE,EAAE,CAACC,WAAW,IAAIoC,KAAK;IAC3B;IACA,IAAIrC,EAAE,CAACC,WAAW,KAAKvE,SAAS,EAAE;MAC9B,IAAIsE,EAAE,CAACC,WAAW,GAAG,CAAC,EAAE;QACpBD,EAAE,CAACC,WAAW,GAAG2E,WAAW,GAAG,CAAC;MACpC;MACA,IAAI5E,EAAE,CAACE,WAAW,GAAG,CAAC,EAAE;QACpBF,EAAE,CAACE,WAAW,GAAG0E,WAAW,GAAG5E,EAAE,CAACC,WAAW;MACjD;MACAD,EAAE,CAACyE,yBAAyB,GAAGzE,EAAE,CAACE,WAAW,GAAG,CAAC,IAAIF,EAAE,CAACC,WAAW,GAAGD,EAAE,CAACE,WAAW,KAAK0E,WAAW;IACxG;IACA,IAAI5E,EAAE,CAACL,WAAW,EAAE;MAChB;MACA;MACA,IAAIK,EAAE,CAACN,WAAW,GAAG,CAAC,IAAIM,EAAE,CAACN,WAAW,GAAGkF,WAAW,EAAE;QACpD5E,EAAE,CAACN,WAAW,GAAGkF,WAAW;MAChC;MACA5E,EAAE,CAACwE,0BAA0B,GAAGxE,EAAE,CAACN,WAAW,KAAKkF,WAAW;IAClE;IACA,MAAMjF,WAAW,GAAGK,EAAE,CAACL,WAAW;IAClC,IAAI,CAACA,WAAW,EAAE;MACd;IACJ;IACArG,uDAAM,CAACqG,WAAW,CAACV,MAAM,GAAG,CAAC,EAAE,wBAAwB,CAAC;IACxD,MAAM4F,eAAe,GAAG7I,cAAc,CAAC2D,WAAW,CAAC;IACnD,IAAIkF,eAAe,CAAC5F,MAAM,KAAK,CAAC,EAAE;MAC9B,MAAM,IAAIqF,KAAK,CAAC,yBAAyB,CAAC;IAC9C;IACA;IACA,KAAK,MAAM1I,IAAI,IAAIiJ,eAAe,EAAE;MAChCjJ,IAAI,CAACR,QAAQ,GAAG4E,EAAE,CAACb,IAAI,CAAC/D,QAAQ;MAChC,MAAMK,GAAG,GAAGG,IAAI,CAACH,GAAG;MACpB,IAAIA,GAAG,IAAIA,GAAG,KAAK,IAAI,EAAE;QACrB,MAAM,IAAI6I,KAAK,CAAC,oCAAoC,CAAC;MACzD;MACA,IAAI1I,IAAI,CAACd,KAAK,KAAKL,oBAAoB,EAAE;QACrC,MAAM,IAAI6J,KAAK,CAAC,qCAAqC,CAAC;MAC1D;IACJ;IACAtE,EAAE,CAACL,WAAW,GAAGkF,eAAe;EACpC;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIR,eAAeA,CAACrE,EAAE,EAAE6D,WAAW,EAAE;IAC7B;IACA,MAAMlE,WAAW,GAAGK,EAAE,CAACL,WAAW;IAClC,IAAIA,WAAW,KAAKjE,SAAS,EAAE;MAC3B,OAAOiE,WAAW,CAACA,WAAW,CAACV,MAAM,GAAG,CAAC,CAAC;IAC9C;IACA;IACA,MAAMgB,WAAW,GAAGD,EAAE,CAACC,WAAW;IAClC,IAAIA,WAAW,KAAKvE,SAAS,EAAE;MAC3B,MAAMwB,KAAK,GAAG,IAAI,CAACA,KAAK;MACxB,MAAM4H,SAAS,GAAG7E,WAAW,GAAGD,EAAE,CAACE,WAAW;MAC9C,KAAK,IAAI6E,CAAC,GAAG7H,KAAK,CAAC+B,MAAM,GAAG,CAAC,EAAE8F,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;QACxC,MAAMnJ,IAAI,GAAGsB,KAAK,CAAC6H,CAAC,CAAC;QACrB,IAAI,CAACA,CAAC,GAAG9E,WAAW,IAAI8E,CAAC,IAAID,SAAS,KAAKlJ,IAAI,KAAKiI,WAAW,EAAE;UAC7D,OAAOjI,IAAI;QACf;MACJ;IACJ;IACA,OAAOF,SAAS;EACpB;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACI6I,YAAYA,CAACX,YAAY,EAAEC,WAAW,EAAE7D,EAAE,EAAE;IACxC,IAAIgD,EAAE,EAAEC,EAAE,EAAE0B,EAAE;IACdrL,uDAAM,CAACuK,WAAW,IAAID,YAAY,EAAE,4CAA4C,CAAC;IACjFtK,uDAAM,CAAC0G,EAAE,CAACY,OAAO,EAAE,uBAAuB,CAAC;IAC3CtH,uDAAM,CAAC0G,EAAE,CAACmD,MAAM,EAAE,sBAAsB,CAAC;IACzC;IACA,MAAMhE,IAAI,GAAGa,EAAE,CAACb,IAAI;IACpB,MAAM;MAAEQ,WAAW;MAAEM,WAAW;MAAEC;IAAY,CAAC,GAAGF,EAAE;IACpD;IACA,IAAIgF,YAAY;IAChB;IACA,IAAI/E,WAAW,KAAKvE,SAAS,IAAIwE,WAAW,KAAKxE,SAAS,EAAE;MACxDpC,uDAAM,CAAC2G,WAAW,IAAI,CAAC,EAAE,iCAAiC,CAAC;MAC3D3G,uDAAM,CAAC4G,WAAW,IAAI,CAAC,EAAE,iCAAiC,CAAC;MAC3D8E,YAAY,GAAG,EAAE;MACjB,KAAK,IAAID,CAAC,GAAG9E,WAAW,EAAE8E,CAAC,GAAG9E,WAAW,GAAGC,WAAW,EAAE6E,CAAC,EAAE,EAAE;QAC1D,MAAMnJ,IAAI,GAAG,IAAI,CAACsB,KAAK,CAAC6H,CAAC,CAAC;QAC1B,IAAInJ,IAAI,KAAKF,SAAS,IAAIE,IAAI,KAAKgI,YAAY,IAAIhI,IAAI,KAAKiI,WAAW,EAAE;UACrEmB,YAAY,CAAC9F,IAAI,CAACtD,IAAI,CAAC;QAC3B;MACJ;MACA;MACA,CAACoH,EAAE,GAAG7D,IAAI,CAACqB,SAAS,MAAM,IAAI,IAAIwC,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAI7D,IAAI,CAACqB,SAAS,GAAG,MAAO;IACpF;IACA,MAAMyE,aAAa,GAAG,IAAI,CAAC/H,KAAK,CAAC+B,MAAM,IAAI,CAACgE,EAAE,GAAGtD,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,WAAW,CAACV,MAAM,MAAM,IAAI,IAAIgE,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,CAAC,CAAC,IAAI/C,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,KAAK,CAAC,GAAGA,WAAW,GAAG,CAAC,CAAC;IACvO5G,uDAAM,CAAC2L,aAAa,IAAI,CAAC,EAAE,mCAAmC,CAAC;IAC/D,IAAIA,aAAa,KAAK,CAAC,EAAE;MACrBC,OAAO,CAACC,IAAI,CAAC,sGAAsG,EAAE,IAAI,EAAE,IAAI,CAAClH,EAAE,CAAC;MACnI,MAAM,IAAIqG,KAAK,CAAC,+CAA+C,CAAC;IACpE;IACA;IACA;IACA,IAAI3E,WAAW,EAAE;MACb;MACA,IAAIL,WAAW,GAAGU,EAAE,CAACN,WAAW;MAChC,KAAK,MAAM9D,IAAI,IAAI+D,WAAW,EAAE;QAC5B,IAAI,CAACyF,YAAY,CAACxJ,IAAI,EAAE0D,WAAW,CAAC;QACpCA,WAAW,EAAE;MACjB;MACA,IAAIU,EAAE,CAACwE,0BAA0B,EAAE;QAC/B;QACA,CAACG,EAAE,GAAGxF,IAAI,CAACqB,SAAS,MAAM,IAAI,IAAImE,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAIxF,IAAI,CAACqB,SAAS,GAAG,SAAU;MACvF;IACJ;IACA;IACA;IACA;IACA;IACA;IACA,IAAIwE,YAAY,IAAIA,YAAY,CAAC/F,MAAM,GAAG,CAAC,EAAE;MACzC,KAAK,MAAMrD,IAAI,IAAIoJ,YAAY,EAAE;QAC7BtL,qDAAS,CAACkC,IAAI,CAACT,OAAO,EAAEnB,iDAAoB,CAAC;QAC7CN,qDAAS,CAACkC,IAAI,CAACT,OAAO,EAAEjB,iDAAmB,CAAC;QAC5CR,qDAAS,CAACkC,IAAI,CAACT,OAAO,EAAErB,iDAAqB,CAAC;MAClD;MACA;MACA,KAAK,MAAM8B,IAAI,IAAIoJ,YAAY,EAAE;QAC7B,IAAI,CAACK,WAAW,CAACzJ,IAAI,CAAC;MAC1B;IACJ;EACJ;EACMhC,UAAUA,CAACgK,YAAY,EAAEC,WAAW,EAAE7D,EAAE,EAAE;IAAA,IAAAsF,MAAA;IAAA,OAAApK,6KAAA;MAC5C;MACA;MACA,MAAMiE,IAAI,GAAGa,EAAE,CAACb,IAAI;MACpB,MAAMoG,gBAAgB,GAAGpG,IAAI,CAACqG,iBAAiB,GACxCC,GAAG,IAAK;QACP;AAChB;AACA;AACA;AACA;AACA;AACA;QACgB,IAAIA,GAAG,KAAK/J,SAAS,IAAI,CAAC4J,MAAI,CAACxI,4BAA4B,EAAE;UACzDwI,MAAI,CAACxI,4BAA4B,GAAG,IAAI;UACxC2I,GAAG,CAACC,QAAQ,CAAC,MAAM;YACfJ,MAAI,CAACxI,4BAA4B,GAAG,KAAK;UAC7C,CAAC,EAAE;YAAE6I,eAAe,EAAE;UAAK,CAAC,CAAC;UAC7B;AACpB;AACA;AACA;AACA;AACA;UACoBF,GAAG,CAACG,WAAW,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QAC5B,CAAC,MACI;UACDN,MAAI,CAACO,KAAK,GAAGJ,GAAG;QACpB;MACJ,CAAC,GACC/J,SAAS;MACf,MAAMyC,IAAI,GAAGhE,4DAAU,CAACmL,MAAI,CAAC;MAC7B,MAAMnE,UAAU,GAAGyC,YAAY,CAACzI,OAAO;MACvC;MACA,MAAM2K,SAAS,GAAGjC,WAAW,IAAIA,WAAW,CAAC1I,OAAO;MACpD,MAAM4K,aAAa,GAAGnE,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC;QAAE1D,IAAI;QAAE6H,UAAU,EAAEV,MAAI,CAAC7C,aAAa,CAACmB,YAAY,CAAC;QAAEqC,MAAM,EAAEX,MAAI,CAACrH,EAAE;QAAEsH,gBAAgB;QAAElI,QAAQ,EAAEiI,MAAI,CAACjI,QAAQ,IAAIjD,wDAAM,CAACgE,UAAU,CAAC,UAAU,EAAE,IAAI,CAAC;QAAE+C,UAAU;QACjN2E;MAAU,CAAC,EAAE3G,IAAI,CAAC,EAAE;QAAE2C,gBAAgB,EAAE3C,IAAI,CAAC2C,gBAAgB,IAAIwD,MAAI,CAAChI,SAAS,IAAIlD,wDAAM,CAAC8L,GAAG,CAAC,cAAc;MAAE,CAAC,CAAC;MACpH,MAAM;QAAExC;MAAa,CAAC,SAAS9J,qDAAU,CAACmM,aAAa,CAAC;MACxD,OAAOT,MAAI,CAACa,gBAAgB,CAACzC,YAAY,EAAEE,YAAY,EAAEC,WAAW,EAAE1E,IAAI,CAAC;IAAC;EAChF;EACAgH,gBAAgBA,CAACzC,YAAY,EAAEE,YAAY,EAAEC,WAAW,EAAE1E,IAAI,EAAE;IAC5D;AACR;AACA;AACA;IACQ,MAAMiH,UAAU,GAAG1C,YAAY,GAAGE,YAAY,GAAGC,WAAW;IAC5D,IAAIuC,UAAU,EAAE;MACZ,IAAI,CAACC,oBAAoB,CAACD,UAAU,CAAC;IACzC;IACA,OAAO;MACH1C,YAAY;MACZC,kBAAkB,EAAE,IAAI;MACxBC,YAAY;MACZC,WAAW;MACXrD,SAAS,EAAErB,IAAI,CAACqB;IACpB,CAAC;EACL;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACI4E,YAAYA,CAACxJ,IAAI,EAAEyG,KAAK,EAAE;IACtB,MAAMnF,KAAK,GAAG,IAAI,CAACA,KAAK;IACxB,MAAMoJ,aAAa,GAAGpJ,KAAK,CAAC4F,OAAO,CAAClH,IAAI,CAAC;IACzC,IAAI0K,aAAa,GAAG,CAAC,CAAC,EAAE;MACpBhN,uDAAM,CAACsC,IAAI,CAACH,GAAG,KAAK,IAAI,EAAE,6BAA6B,CAAC;MACxD;MACAyB,KAAK,CAACqJ,MAAM,CAACD,aAAa,EAAE,CAAC,CAAC;MAC9B;MACApJ,KAAK,CAACqJ,MAAM,CAAClE,KAAK,EAAE,CAAC,EAAEzG,IAAI,CAAC;IAChC,CAAC,MACI;MACDtC,uDAAM,CAAC,CAACsC,IAAI,CAACH,GAAG,EAAE,aAAa,CAAC;MAChC;MACA;MACAG,IAAI,CAACH,GAAG,GAAG,IAAI;MACfyB,KAAK,CAACqJ,MAAM,CAAClE,KAAK,EAAE,CAAC,EAAEzG,IAAI,CAAC;IAChC;EACJ;EACA;AACJ;AACA;AACA;AACA;EACIuE,UAAUA,CAACvE,IAAI,EAAE;IACbtC,uDAAM,CAACsC,IAAI,CAACd,KAAK,KAAKN,mBAAmB,IAAIoB,IAAI,CAACd,KAAK,KAAKL,oBAAoB,EAAE,0CAA0C,CAAC;IAC7H,MAAMyC,KAAK,GAAG,IAAI,CAACA,KAAK;IACxB,MAAMmF,KAAK,GAAGnF,KAAK,CAAC4F,OAAO,CAAClH,IAAI,CAAC;IACjCtC,uDAAM,CAAC+I,KAAK,GAAG,CAAC,CAAC,EAAE,gCAAgC,CAAC;IACpD,IAAIA,KAAK,IAAI,CAAC,EAAE;MACZnF,KAAK,CAACqJ,MAAM,CAAClE,KAAK,EAAE,CAAC,CAAC;IAC1B;EACJ;EACAgD,WAAWA,CAACzJ,IAAI,EAAE;IACdA,IAAI,CAACP,QAAQ,CAAC,CAAC;IACf,IAAI,CAAC8E,UAAU,CAACvE,IAAI,CAAC;EACzB;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIyK,oBAAoBA,CAACD,UAAU,EAAE;IAC7B;IACA;IACA;IACA,IAAI,IAAI,CAACnJ,SAAS,EAAE;MAChB;IACJ;IACA,MAAMC,KAAK,GAAG,IAAI,CAACA,KAAK;IACxB,MAAMsJ,eAAe,GAAGtJ,KAAK,CAAC4F,OAAO,CAACsD,UAAU,CAAC;IACjD,KAAK,IAAIrB,CAAC,GAAG7H,KAAK,CAAC+B,MAAM,GAAG,CAAC,EAAE8F,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;MACxC,MAAMnJ,IAAI,GAAGsB,KAAK,CAAC6H,CAAC,CAAC;MACrB;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;MACY,MAAM5J,OAAO,GAAGS,IAAI,CAACT,OAAO;MAC5B,IAAIA,OAAO,EAAE;QACT,IAAI4J,CAAC,GAAGyB,eAAe,EAAE;UACrB;UACA;UACA9M,qDAAS,CAACyB,OAAO,EAAErB,iDAAqB,CAAC;UACzC,IAAI,CAACuL,WAAW,CAACzJ,IAAI,CAAC;QAC1B,CAAC,MACI,IAAImJ,CAAC,GAAGyB,eAAe,EAAE;UAC1B;UACA;UACA3M,qDAAa,CAACsB,OAAO,EAAE,IAAI,CAAC;QAChC;MACJ;IACJ;EACJ;EACAsD,QAAQA,CAAA,EAAG;IACP,OAAQ,CAAC,IAAI,CAAC3B,4BAA4B,IACtC,CAAC,CAAC,IAAI,CAACM,YAAY,IACnB,CAAC,IAAI,CAACJ,eAAe,IACrB,IAAI,CAACH,UAAU,CAACoC,MAAM,KAAK,CAAC,IAC5B,IAAI,CAACwD,aAAa,CAAC,CAAC;EAC5B;EACA9D,OAAOA,CAAA,EAAG;IACN,IAAI,CAAC7B,4BAA4B,GAAG,IAAI;IACxC,IAAI,CAAC8C,GAAG,CAAC;MAAEY,SAAS,EAAE,MAAM;MAAEgF,iBAAiB,EAAE;IAAK,CAAC,CAAC;EAC5D;EACA5G,MAAMA,CAAC6H,SAAS,EAAE;IACd,IAAI,IAAI,CAACZ,KAAK,EAAE;MACZ,IAAI,CAACA,KAAK,CAACa,YAAY,CAACD,SAAS,CAAC;IACtC;EACJ;EACA5H,KAAKA,CAAC8H,cAAc,EAAEF,SAAS,EAAEG,GAAG,EAAE;IAClC,IAAI,IAAI,CAACf,KAAK,EAAE;MACZ,IAAI,CAACA,KAAK,CAACH,QAAQ,CAAC,MAAM;QACtB,IAAI,CAAC5I,4BAA4B,GAAG,KAAK;MAC7C,CAAC,EAAE;QAAE6I,eAAe,EAAE;MAAK,CAAC,CAAC;MAC7B;MACA,IAAIkB,YAAY,GAAGF,cAAc,GAAG,CAAC,KAAK,GAAG,KAAK;MAClD;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;MACY,IAAI,CAACA,cAAc,EAAE;QACjB,IAAI,CAACd,KAAK,CAACiB,MAAM,CAAC,gCAAgC,CAAC;QACnDD,YAAY,IAAIzN,4DAAuB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAEqN,SAAS,CAAC,CAAC,CAAC,CAAC;MAC/F,CAAC,MACI;QACDI,YAAY,IAAIzN,4DAAuB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAEqN,SAAS,CAAC,CAAC,CAAC,CAAC;MAC/F;MACA,IAAI,CAACZ,KAAK,CAACD,WAAW,CAACe,cAAc,GAAG,CAAC,GAAG,CAAC,EAAEE,YAAY,EAAED,GAAG,CAAC;IACrE,CAAC,MACI;MACD,IAAI,CAAC9J,4BAA4B,GAAG,KAAK;IAC7C;EACJ;EACAiK,MAAMA,CAAA,EAAG;IACL,OAAOjO,qDAAC,CAAC,MAAM,EAAE;MAAEkO,GAAG,EAAE;IAA2C,CAAC,CAAC;EACzE;EACA,IAAI/I,EAAEA,CAAA,EAAG;IAAE,OAAOjF,qDAAU,CAAC,IAAI,CAAC;EAAE;EACpC,WAAWiO,QAAQA,CAAA,EAAG;IAAE,OAAO;MAC3B,cAAc,EAAE,CAAC,qBAAqB,CAAC;MACvC,MAAM,EAAE,CAAC,aAAa;IAC1B,CAAC;EAAE;AACP,CAAC;AACDzK,GAAG,CAAC0K,KAAK,GAAG3K,YAAY;AAExB,MAAM4K,OAAO,GAAGA,CAAClJ,EAAE,EAAEmJ,eAAe,EAAExM,SAAS,EAAEuB,cAAc,EAAEkL,eAAe,KAAK;EACjF,MAAM5L,GAAG,GAAGwC,EAAE,CAACC,OAAO,CAAC,SAAS,CAAC;EACjC,IAAIzC,GAAG,EAAE;IACL,IAAI2L,eAAe,KAAK,SAAS,EAAE;MAC/B,IAAIxM,SAAS,KAAKc,SAAS,EAAE;QACzB,OAAOD,GAAG,CAACyD,IAAI,CAACtE,SAAS,EAAEuB,cAAc,EAAE;UAAE+G,UAAU,EAAE,IAAI;UAAEpB,gBAAgB,EAAEuF;QAAgB,CAAC,CAAC;MACvG;IACJ,CAAC,MACI,IAAID,eAAe,KAAK,MAAM,EAAE;MACjC,IAAIxM,SAAS,KAAKc,SAAS,EAAE;QACzB,OAAOD,GAAG,CAACoC,OAAO,CAACjD,SAAS,EAAEuB,cAAc,EAAE;UAAE+G,UAAU,EAAE,IAAI;UAAEpB,gBAAgB,EAAEuF;QAAgB,CAAC,CAAC;MAC1G;IACJ,CAAC,MACI,IAAID,eAAe,KAAK,MAAM,EAAE;MACjC,OAAO3L,GAAG,CAACmE,GAAG,CAAC;QAAEsD,UAAU,EAAE,IAAI;QAAEpB,gBAAgB,EAAEuF;MAAgB,CAAC,CAAC;IAC3E;EACJ;EACA,OAAO1G,OAAO,CAACC,OAAO,CAAC,KAAK,CAAC;AACjC,CAAC;AAED,MAAM0G,OAAO,GAAG,MAAM;EAClB3M,WAAWA,CAAC8B,OAAO,EAAE;IACjB9D,qDAAgB,CAAC,IAAI,EAAE8D,OAAO,CAAC;IAC/B,IAAI,CAAC8K,OAAO,GAAG,MAAM;MACjB,OAAOJ,OAAO,CAAC,IAAI,CAAClJ,EAAE,EAAE,IAAI,CAACmJ,eAAe,EAAE,IAAI,CAACxM,SAAS,EAAE,IAAI,CAACuB,cAAc,EAAE,IAAI,CAACkL,eAAe,CAAC;IAC5G,CAAC;IACD,IAAI,CAACzM,SAAS,GAAGc,SAAS;IAC1B,IAAI,CAACS,cAAc,GAAGT,SAAS;IAC/B,IAAI,CAAC0L,eAAe,GAAG,SAAS;IAChC,IAAI,CAACC,eAAe,GAAG3L,SAAS;EACpC;EACAqL,MAAMA,CAAA,EAAG;IACL,OAAOjO,qDAAC,CAACI,iDAAI,EAAE;MAAE8N,GAAG,EAAE,0CAA0C;MAAEO,OAAO,EAAE,IAAI,CAACA;IAAQ,CAAC,CAAC;EAC9F;EACA,IAAItJ,EAAEA,CAAA,EAAG;IAAE,OAAOjF,qDAAU,CAAC,IAAI,CAAC;EAAE;AACxC,CAAC", "sources": ["./node_modules/@ionic/core/dist/esm/ion-nav_2.entry.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, d as createEvent, h, f as getElement, H as Host } from './index-a1a47f01.js';\nimport { g as getTimeGivenProgression } from './cubic-bezier-fe2083dc.js';\nimport { o as assert, s as shallowEqualStringMap } from './helpers-be245865.js';\nimport { l as lifecycle, t as transition, s as set<PERSON>age<PERSON><PERSON><PERSON>, d as LIFECYCLE_WILL_UNLOAD, b as LIFECYCLE_WILL_LEAVE, c as LIFECYCLE_DID_LEAVE } from './index-fae1515c.js';\nimport { b as getIonMode, c as config } from './ionic-global-94f25d1b.js';\nimport { a as attachComponent } from './framework-delegate-ed4ba327.js';\n\nconst VIEW_STATE_NEW = 1;\nconst VIEW_STATE_ATTACHED = 2;\nconst VIEW_STATE_DESTROYED = 3;\n// TODO(FW-2832): types\nclass ViewController {\n    constructor(component, params) {\n        this.component = component;\n        this.params = params;\n        this.state = VIEW_STATE_NEW;\n    }\n    async init(container) {\n        this.state = VIEW_STATE_ATTACHED;\n        if (!this.element) {\n            const component = this.component;\n            this.element = await attachComponent(this.delegate, container, component, ['ion-page', 'ion-page-invisible'], this.params);\n        }\n    }\n    /**\n     * DOM WRITE\n     */\n    _destroy() {\n        assert(this.state !== VIEW_STATE_DESTROYED, 'view state must be ATTACHED');\n        const element = this.element;\n        if (element) {\n            if (this.delegate) {\n                this.delegate.removeViewFromDom(element.parentElement, element);\n            }\n            else {\n                element.remove();\n            }\n        }\n        this.nav = undefined;\n        this.state = VIEW_STATE_DESTROYED;\n    }\n}\nconst matches = (view, id, params) => {\n    if (!view) {\n        return false;\n    }\n    if (view.component !== id) {\n        return false;\n    }\n    return shallowEqualStringMap(view.params, params);\n};\nconst convertToView = (page, params) => {\n    if (!page) {\n        return null;\n    }\n    if (page instanceof ViewController) {\n        return page;\n    }\n    return new ViewController(page, params);\n};\nconst convertToViews = (pages) => {\n    return pages\n        .map((page) => {\n        if (page instanceof ViewController) {\n            return page;\n        }\n        if ('component' in page) {\n            return convertToView(page.component, page.componentProps === null ? undefined : page.componentProps);\n        }\n        return convertToView(page, undefined);\n    })\n        .filter((v) => v !== null);\n};\n\nconst navCss = \":host{left:0;right:0;top:0;bottom:0;position:absolute;contain:layout size style;z-index:0}\";\nconst IonNavStyle0 = navCss;\n\nconst Nav = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.ionNavWillLoad = createEvent(this, \"ionNavWillLoad\", 7);\n        this.ionNavWillChange = createEvent(this, \"ionNavWillChange\", 3);\n        this.ionNavDidChange = createEvent(this, \"ionNavDidChange\", 3);\n        this.transInstr = [];\n        this.gestureOrAnimationInProgress = false;\n        this.useRouter = false;\n        this.isTransitioning = false;\n        this.destroyed = false;\n        this.views = [];\n        this.didLoad = false;\n        this.delegate = undefined;\n        this.swipeGesture = undefined;\n        this.animated = true;\n        this.animation = undefined;\n        this.rootParams = undefined;\n        this.root = undefined;\n    }\n    swipeGestureChanged() {\n        if (this.gesture) {\n            this.gesture.enable(this.swipeGesture === true);\n        }\n    }\n    rootChanged() {\n        if (this.root === undefined) {\n            return;\n        }\n        if (this.didLoad === false) {\n            /**\n             * If the component has not loaded yet, we can skip setting up the root component.\n             * It will be called when `componentDidLoad` fires.\n             */\n            return;\n        }\n        if (!this.useRouter) {\n            if (this.root !== undefined) {\n                this.setRoot(this.root, this.rootParams);\n            }\n        }\n    }\n    componentWillLoad() {\n        this.useRouter = document.querySelector('ion-router') !== null && this.el.closest('[no-router]') === null;\n        if (this.swipeGesture === undefined) {\n            const mode = getIonMode(this);\n            this.swipeGesture = config.getBoolean('swipeBackEnabled', mode === 'ios');\n        }\n        this.ionNavWillLoad.emit();\n    }\n    async componentDidLoad() {\n        // We want to set this flag before any watch callbacks are manually called\n        this.didLoad = true;\n        this.rootChanged();\n        this.gesture = (await import('./swipe-back-37a22d34.js')).createSwipeBackGesture(this.el, this.canStart.bind(this), this.onStart.bind(this), this.onMove.bind(this), this.onEnd.bind(this));\n        this.swipeGestureChanged();\n    }\n    connectedCallback() {\n        this.destroyed = false;\n    }\n    disconnectedCallback() {\n        for (const view of this.views) {\n            lifecycle(view.element, LIFECYCLE_WILL_UNLOAD);\n            view._destroy();\n        }\n        // Release swipe back gesture and transition.\n        if (this.gesture) {\n            this.gesture.destroy();\n            this.gesture = undefined;\n        }\n        this.transInstr.length = 0;\n        this.views.length = 0;\n        this.destroyed = true;\n    }\n    /**\n     * Push a new component onto the current navigation stack. Pass any additional\n     * information along as an object. This additional information is accessible\n     * through NavParams.\n     *\n     * @param component The component to push onto the navigation stack.\n     * @param componentProps Any properties of the component.\n     * @param opts The navigation options.\n     * @param done The transition complete function.\n     */\n    push(component, componentProps, opts, done) {\n        return this.insert(-1, component, componentProps, opts, done);\n    }\n    /**\n     * Inserts a component into the navigation stack at the specified index.\n     * This is useful to add a component at any point in the navigation stack.\n     *\n     * @param insertIndex The index to insert the component at in the stack.\n     * @param component The component to insert into the navigation stack.\n     * @param componentProps Any properties of the component.\n     * @param opts The navigation options.\n     * @param done The transition complete function.\n     */\n    insert(insertIndex, component, componentProps, opts, done) {\n        return this.insertPages(insertIndex, [{ component, componentProps }], opts, done);\n    }\n    /**\n     * Inserts an array of components into the navigation stack at the specified index.\n     * The last component in the array will become instantiated as a view, and animate\n     * in to become the active view.\n     *\n     * @param insertIndex The index to insert the components at in the stack.\n     * @param insertComponents The components to insert into the navigation stack.\n     * @param opts The navigation options.\n     * @param done The transition complete function.\n     */\n    insertPages(insertIndex, insertComponents, opts, done) {\n        return this.queueTrns({\n            insertStart: insertIndex,\n            insertViews: insertComponents,\n            opts,\n        }, done);\n    }\n    /**\n     * Pop a component off of the navigation stack. Navigates back from the current\n     * component.\n     *\n     * @param opts The navigation options.\n     * @param done The transition complete function.\n     */\n    pop(opts, done) {\n        return this.removeIndex(-1, 1, opts, done);\n    }\n    /**\n     * Pop to a specific index in the navigation stack.\n     *\n     * @param indexOrViewCtrl The index or view controller to pop to.\n     * @param opts The navigation options.\n     * @param done The transition complete function.\n     */\n    popTo(indexOrViewCtrl, opts, done) {\n        const ti = {\n            removeStart: -1,\n            removeCount: -1,\n            opts,\n        };\n        if (typeof indexOrViewCtrl === 'object' && indexOrViewCtrl.component) {\n            ti.removeView = indexOrViewCtrl;\n            ti.removeStart = 1;\n        }\n        else if (typeof indexOrViewCtrl === 'number') {\n            ti.removeStart = indexOrViewCtrl + 1;\n        }\n        return this.queueTrns(ti, done);\n    }\n    /**\n     * Navigate back to the root of the stack, no matter how far back that is.\n     *\n     * @param opts The navigation options.\n     * @param done The transition complete function.\n     */\n    popToRoot(opts, done) {\n        return this.removeIndex(1, -1, opts, done);\n    }\n    /**\n     * Removes a component from the navigation stack at the specified index.\n     *\n     * @param startIndex The number to begin removal at.\n     * @param removeCount The number of components to remove.\n     * @param opts The navigation options.\n     * @param done The transition complete function.\n     */\n    removeIndex(startIndex, removeCount = 1, opts, done) {\n        return this.queueTrns({\n            removeStart: startIndex,\n            removeCount,\n            opts,\n        }, done);\n    }\n    /**\n     * Set the root for the current navigation stack to a component.\n     *\n     * @param component The component to set as the root of the navigation stack.\n     * @param componentProps Any properties of the component.\n     * @param opts The navigation options.\n     * @param done The transition complete function.\n     */\n    setRoot(component, componentProps, opts, done) {\n        return this.setPages([{ component, componentProps }], opts, done);\n    }\n    /**\n     * Set the views of the current navigation stack and navigate to the last view.\n     * By default animations are disabled, but they can be enabled by passing options\n     * to the navigation controller. Navigation parameters can also be passed to the\n     * individual pages in the array.\n     *\n     * @param views The list of views to set as the navigation stack.\n     * @param opts The navigation options.\n     * @param done The transition complete function.\n     */\n    setPages(views, opts, done) {\n        opts !== null && opts !== void 0 ? opts : (opts = {});\n        // if animation wasn't set to true then default it to NOT animate\n        if (opts.animated !== true) {\n            opts.animated = false;\n        }\n        return this.queueTrns({\n            insertStart: 0,\n            insertViews: views,\n            removeStart: 0,\n            removeCount: -1,\n            opts,\n        }, done);\n    }\n    /**\n     * Called by the router to update the view.\n     *\n     * @param id The component tag.\n     * @param params The component params.\n     * @param direction A direction hint.\n     * @param animation an AnimationBuilder.\n     *\n     * @return the status.\n     * @internal\n     */\n    setRouteId(id, params, direction, animation) {\n        const active = this.getActiveSync();\n        if (matches(active, id, params)) {\n            return Promise.resolve({\n                changed: false,\n                element: active.element,\n            });\n        }\n        let resolve;\n        const promise = new Promise((r) => (resolve = r));\n        let finish;\n        const commonOpts = {\n            updateURL: false,\n            viewIsReady: (enteringEl) => {\n                let mark;\n                const p = new Promise((r) => (mark = r));\n                resolve({\n                    changed: true,\n                    element: enteringEl,\n                    markVisible: async () => {\n                        mark();\n                        await finish;\n                    },\n                });\n                return p;\n            },\n        };\n        if (direction === 'root') {\n            finish = this.setRoot(id, params, commonOpts);\n        }\n        else {\n            // Look for a view matching the target in the view stack.\n            const viewController = this.views.find((v) => matches(v, id, params));\n            if (viewController) {\n                finish = this.popTo(viewController, Object.assign(Object.assign({}, commonOpts), { direction: 'back', animationBuilder: animation }));\n            }\n            else if (direction === 'forward') {\n                finish = this.push(id, params, Object.assign(Object.assign({}, commonOpts), { animationBuilder: animation }));\n            }\n            else if (direction === 'back') {\n                finish = this.setRoot(id, params, Object.assign(Object.assign({}, commonOpts), { direction: 'back', animated: true, animationBuilder: animation }));\n            }\n        }\n        return promise;\n    }\n    /**\n     * Called by <ion-router> to retrieve the current component.\n     *\n     * @internal\n     */\n    async getRouteId() {\n        const active = this.getActiveSync();\n        if (active) {\n            return {\n                id: active.element.tagName,\n                params: active.params,\n                element: active.element,\n            };\n        }\n        return undefined;\n    }\n    /**\n     * Get the active view.\n     */\n    async getActive() {\n        return this.getActiveSync();\n    }\n    /**\n     * Get the view at the specified index.\n     *\n     * @param index The index of the view.\n     */\n    async getByIndex(index) {\n        return this.views[index];\n    }\n    /**\n     * Returns `true` if the current view can go back.\n     *\n     * @param view The view to check.\n     */\n    async canGoBack(view) {\n        return this.canGoBackSync(view);\n    }\n    /**\n     * Get the previous view.\n     *\n     * @param view The view to get.\n     */\n    async getPrevious(view) {\n        return this.getPreviousSync(view);\n    }\n    getLength() {\n        return this.views.length;\n    }\n    getActiveSync() {\n        return this.views[this.views.length - 1];\n    }\n    canGoBackSync(view = this.getActiveSync()) {\n        return !!(view && this.getPreviousSync(view));\n    }\n    getPreviousSync(view = this.getActiveSync()) {\n        if (!view) {\n            return undefined;\n        }\n        const views = this.views;\n        const index = views.indexOf(view);\n        return index > 0 ? views[index - 1] : undefined;\n    }\n    /**\n     * Adds a navigation stack change to the queue and schedules it to run.\n     *\n     * @returns Whether the transition succeeds.\n     */\n    async queueTrns(ti, done) {\n        var _a, _b;\n        if (this.isTransitioning && ((_a = ti.opts) === null || _a === void 0 ? void 0 : _a.skipIfBusy)) {\n            return false;\n        }\n        const promise = new Promise((resolve, reject) => {\n            ti.resolve = resolve;\n            ti.reject = reject;\n        });\n        ti.done = done;\n        /**\n         * If using router, check to see if navigation hooks\n         * will allow us to perform this transition. This\n         * is required in order for hooks to work with\n         * the ion-back-button or swipe to go back.\n         */\n        if (ti.opts && ti.opts.updateURL !== false && this.useRouter) {\n            const router = document.querySelector('ion-router');\n            if (router) {\n                const canTransition = await router.canTransition();\n                if (canTransition === false) {\n                    return false;\n                }\n                if (typeof canTransition === 'string') {\n                    router.push(canTransition, ti.opts.direction || 'back');\n                    return false;\n                }\n            }\n        }\n        // Normalize empty\n        if (((_b = ti.insertViews) === null || _b === void 0 ? void 0 : _b.length) === 0) {\n            ti.insertViews = undefined;\n        }\n        // Enqueue transition instruction\n        this.transInstr.push(ti);\n        // if there isn't a transition already happening\n        // then this will kick off this transition\n        this.nextTrns();\n        return promise;\n    }\n    success(result, ti) {\n        if (this.destroyed) {\n            this.fireError('nav controller was destroyed', ti);\n            return;\n        }\n        if (ti.done) {\n            ti.done(result.hasCompleted, result.requiresTransition, result.enteringView, result.leavingView, result.direction);\n        }\n        ti.resolve(result.hasCompleted);\n        if (ti.opts.updateURL !== false && this.useRouter) {\n            const router = document.querySelector('ion-router');\n            if (router) {\n                const direction = result.direction === 'back' ? 'back' : 'forward';\n                router.navChanged(direction);\n            }\n        }\n    }\n    failed(rejectReason, ti) {\n        if (this.destroyed) {\n            this.fireError('nav controller was destroyed', ti);\n            return;\n        }\n        this.transInstr.length = 0;\n        this.fireError(rejectReason, ti);\n    }\n    fireError(rejectReason, ti) {\n        if (ti.done) {\n            ti.done(false, false, rejectReason);\n        }\n        if (ti.reject && !this.destroyed) {\n            ti.reject(rejectReason);\n        }\n        else {\n            ti.resolve(false);\n        }\n    }\n    /**\n     * Consumes the next transition in the queue.\n     *\n     * @returns whether the transition is executed.\n     */\n    nextTrns() {\n        // this is the framework's bread 'n butta function\n        // only one transition is allowed at any given time\n        if (this.isTransitioning) {\n            return false;\n        }\n        // there is no transition happening right now, executes the next instructions.\n        const ti = this.transInstr.shift();\n        if (!ti) {\n            return false;\n        }\n        this.runTransition(ti);\n        return true;\n    }\n    /** Executes all the transition instruction from the queue. */\n    async runTransition(ti) {\n        try {\n            // set that this nav is actively transitioning\n            this.ionNavWillChange.emit();\n            this.isTransitioning = true;\n            this.prepareTI(ti);\n            const leavingView = this.getActiveSync();\n            const enteringView = this.getEnteringView(ti, leavingView);\n            if (!leavingView && !enteringView) {\n                throw new Error('no views in the stack to be removed');\n            }\n            if (enteringView && enteringView.state === VIEW_STATE_NEW) {\n                await enteringView.init(this.el);\n            }\n            this.postViewInit(enteringView, leavingView, ti);\n            // Needs transition?\n            const requiresTransition = (ti.enteringRequiresTransition || ti.leavingRequiresTransition) && enteringView !== leavingView;\n            if (requiresTransition && ti.opts && leavingView) {\n                const isBackDirection = ti.opts.direction === 'back';\n                /**\n                 * If heading back, use the entering page's animation\n                 * unless otherwise specified by the developer.\n                 */\n                if (isBackDirection) {\n                    ti.opts.animationBuilder = ti.opts.animationBuilder || (enteringView === null || enteringView === void 0 ? void 0 : enteringView.animationBuilder);\n                }\n                leavingView.animationBuilder = ti.opts.animationBuilder;\n            }\n            let result;\n            if (requiresTransition) {\n                result = await this.transition(enteringView, leavingView, ti);\n            }\n            else {\n                // transition is not required, so we are already done!\n                // they're inserting/removing the views somewhere in the middle or\n                // beginning, so visually nothing needs to animate/transition\n                // resolve immediately because there's no animation that's happening\n                result = {\n                    hasCompleted: true,\n                    requiresTransition: false,\n                };\n            }\n            this.success(result, ti);\n            this.ionNavDidChange.emit();\n        }\n        catch (rejectReason) {\n            this.failed(rejectReason, ti);\n        }\n        this.isTransitioning = false;\n        this.nextTrns();\n    }\n    prepareTI(ti) {\n        var _a, _b;\n        var _c;\n        const viewsLength = this.views.length;\n        (_a = ti.opts) !== null && _a !== void 0 ? _a : (ti.opts = {});\n        (_b = (_c = ti.opts).delegate) !== null && _b !== void 0 ? _b : (_c.delegate = this.delegate);\n        if (ti.removeView !== undefined) {\n            assert(ti.removeStart !== undefined, 'removeView needs removeStart');\n            assert(ti.removeCount !== undefined, 'removeView needs removeCount');\n            const index = this.views.indexOf(ti.removeView);\n            if (index < 0) {\n                throw new Error('removeView was not found');\n            }\n            ti.removeStart += index;\n        }\n        if (ti.removeStart !== undefined) {\n            if (ti.removeStart < 0) {\n                ti.removeStart = viewsLength - 1;\n            }\n            if (ti.removeCount < 0) {\n                ti.removeCount = viewsLength - ti.removeStart;\n            }\n            ti.leavingRequiresTransition = ti.removeCount > 0 && ti.removeStart + ti.removeCount === viewsLength;\n        }\n        if (ti.insertViews) {\n            // allow -1 to be passed in to auto push it on the end\n            // and clean up the index if it's larger then the size of the stack\n            if (ti.insertStart < 0 || ti.insertStart > viewsLength) {\n                ti.insertStart = viewsLength;\n            }\n            ti.enteringRequiresTransition = ti.insertStart === viewsLength;\n        }\n        const insertViews = ti.insertViews;\n        if (!insertViews) {\n            return;\n        }\n        assert(insertViews.length > 0, 'length can not be zero');\n        const viewControllers = convertToViews(insertViews);\n        if (viewControllers.length === 0) {\n            throw new Error('invalid views to insert');\n        }\n        // Check all the inserted view are correct\n        for (const view of viewControllers) {\n            view.delegate = ti.opts.delegate;\n            const nav = view.nav;\n            if (nav && nav !== this) {\n                throw new Error('inserted view was already inserted');\n            }\n            if (view.state === VIEW_STATE_DESTROYED) {\n                throw new Error('inserted view was already destroyed');\n            }\n        }\n        ti.insertViews = viewControllers;\n    }\n    /**\n     * Returns the view that will be entered considering the transition instructions.\n     *\n     * @param ti The instructions.\n     * @param leavingView The view being left or undefined if none.\n     *\n     * @returns The view that will be entered, undefined if none.\n     */\n    getEnteringView(ti, leavingView) {\n        // The last inserted view will be entered when view are inserted.\n        const insertViews = ti.insertViews;\n        if (insertViews !== undefined) {\n            return insertViews[insertViews.length - 1];\n        }\n        // When views are deleted, we will enter the last view that is not removed and not the view being left.\n        const removeStart = ti.removeStart;\n        if (removeStart !== undefined) {\n            const views = this.views;\n            const removeEnd = removeStart + ti.removeCount;\n            for (let i = views.length - 1; i >= 0; i--) {\n                const view = views[i];\n                if ((i < removeStart || i >= removeEnd) && view !== leavingView) {\n                    return view;\n                }\n            }\n        }\n        return undefined;\n    }\n    /**\n     * Adds and Removes the views from the navigation stack.\n     *\n     * @param enteringView The view being entered.\n     * @param leavingView The view being left.\n     * @param ti The instructions.\n     */\n    postViewInit(enteringView, leavingView, ti) {\n        var _a, _b, _c;\n        assert(leavingView || enteringView, 'Both leavingView and enteringView are null');\n        assert(ti.resolve, 'resolve must be valid');\n        assert(ti.reject, 'reject must be valid');\n        // Compute the views to remove.\n        const opts = ti.opts;\n        const { insertViews, removeStart, removeCount } = ti;\n        /** Records the view to destroy */\n        let destroyQueue;\n        // there are views to remove\n        if (removeStart !== undefined && removeCount !== undefined) {\n            assert(removeStart >= 0, 'removeStart can not be negative');\n            assert(removeCount >= 0, 'removeCount can not be negative');\n            destroyQueue = [];\n            for (let i = removeStart; i < removeStart + removeCount; i++) {\n                const view = this.views[i];\n                if (view !== undefined && view !== enteringView && view !== leavingView) {\n                    destroyQueue.push(view);\n                }\n            }\n            // default the direction to \"back\"\n            (_a = opts.direction) !== null && _a !== void 0 ? _a : (opts.direction = 'back');\n        }\n        const finalNumViews = this.views.length + ((_b = insertViews === null || insertViews === void 0 ? void 0 : insertViews.length) !== null && _b !== void 0 ? _b : 0) - (removeCount !== null && removeCount !== void 0 ? removeCount : 0);\n        assert(finalNumViews >= 0, 'final balance can not be negative');\n        if (finalNumViews === 0) {\n            console.warn(`You can't remove all the pages in the navigation stack. nav.pop() is probably called too many times.`, this, this.el);\n            throw new Error('navigation stack needs at least one root page');\n        }\n        // At this point the transition can not be rejected, any throw should be an error\n        // Insert the new views in the stack.\n        if (insertViews) {\n            // add the views to the\n            let insertIndex = ti.insertStart;\n            for (const view of insertViews) {\n                this.insertViewAt(view, insertIndex);\n                insertIndex++;\n            }\n            if (ti.enteringRequiresTransition) {\n                // default to forward if not already set\n                (_c = opts.direction) !== null && _c !== void 0 ? _c : (opts.direction = 'forward');\n            }\n        }\n        // if the views to be removed are in the beginning or middle\n        // and there is not a view that needs to visually transition out\n        // then just destroy them and don't transition anything\n        // batch all of lifecycles together\n        // let's make sure, callbacks are zoned\n        if (destroyQueue && destroyQueue.length > 0) {\n            for (const view of destroyQueue) {\n                lifecycle(view.element, LIFECYCLE_WILL_LEAVE);\n                lifecycle(view.element, LIFECYCLE_DID_LEAVE);\n                lifecycle(view.element, LIFECYCLE_WILL_UNLOAD);\n            }\n            // once all lifecycle events has been delivered, we can safely detroy the views\n            for (const view of destroyQueue) {\n                this.destroyView(view);\n            }\n        }\n    }\n    async transition(enteringView, leavingView, ti) {\n        // we should animate (duration > 0) if the pushed page is not the first one (startup)\n        // or if it is a portal (modal, actionsheet, etc.)\n        const opts = ti.opts;\n        const progressCallback = opts.progressAnimation\n            ? (ani) => {\n                /**\n                 * Because this progress callback is called asynchronously\n                 * it is possible for the gesture to start and end before\n                 * the animation is ever set. In that scenario, we should\n                 * immediately call progressEnd so that the transition promise\n                 * resolves and the gesture does not get locked up.\n                 */\n                if (ani !== undefined && !this.gestureOrAnimationInProgress) {\n                    this.gestureOrAnimationInProgress = true;\n                    ani.onFinish(() => {\n                        this.gestureOrAnimationInProgress = false;\n                    }, { oneTimeCallback: true });\n                    /**\n                     * Playing animation to beginning\n                     * with a duration of 0 prevents\n                     * any flickering when the animation\n                     * is later cleaned up.\n                     */\n                    ani.progressEnd(0, 0, 0);\n                }\n                else {\n                    this.sbAni = ani;\n                }\n            }\n            : undefined;\n        const mode = getIonMode(this);\n        const enteringEl = enteringView.element;\n        // eslint-disable-next-line @typescript-eslint/prefer-optional-chain\n        const leavingEl = leavingView && leavingView.element;\n        const animationOpts = Object.assign(Object.assign({ mode, showGoBack: this.canGoBackSync(enteringView), baseEl: this.el, progressCallback, animated: this.animated && config.getBoolean('animated', true), enteringEl,\n            leavingEl }, opts), { animationBuilder: opts.animationBuilder || this.animation || config.get('navAnimation') });\n        const { hasCompleted } = await transition(animationOpts);\n        return this.transitionFinish(hasCompleted, enteringView, leavingView, opts);\n    }\n    transitionFinish(hasCompleted, enteringView, leavingView, opts) {\n        /**\n         * If the transition did not complete, the leavingView will still be the active\n         * view on the stack. Otherwise unmount all the views after the enteringView.\n         */\n        const activeView = hasCompleted ? enteringView : leavingView;\n        if (activeView) {\n            this.unmountInactiveViews(activeView);\n        }\n        return {\n            hasCompleted,\n            requiresTransition: true,\n            enteringView,\n            leavingView,\n            direction: opts.direction,\n        };\n    }\n    /**\n     * Inserts a view at the specified index.\n     *\n     * When the view already is in the stack it will be moved to the new position.\n     *\n     * @param view The view to insert.\n     * @param index The index where to insert the view.\n     */\n    insertViewAt(view, index) {\n        const views = this.views;\n        const existingIndex = views.indexOf(view);\n        if (existingIndex > -1) {\n            assert(view.nav === this, 'view is not part of the nav');\n            // The view already in the stack, removes it.\n            views.splice(existingIndex, 1);\n            // and add it back at the requested index.\n            views.splice(index, 0, view);\n        }\n        else {\n            assert(!view.nav, 'nav is used');\n            // this is a new view to add to the stack\n            // create the new entering view\n            view.nav = this;\n            views.splice(index, 0, view);\n        }\n    }\n    /**\n     * Removes a view from the stack.\n     *\n     * @param view The view to remove.\n     */\n    removeView(view) {\n        assert(view.state === VIEW_STATE_ATTACHED || view.state === VIEW_STATE_DESTROYED, 'view state should be loaded or destroyed');\n        const views = this.views;\n        const index = views.indexOf(view);\n        assert(index > -1, 'view must be part of the stack');\n        if (index >= 0) {\n            views.splice(index, 1);\n        }\n    }\n    destroyView(view) {\n        view._destroy();\n        this.removeView(view);\n    }\n    /**\n     * Unmounts all inactive views after the specified active view.\n     *\n     * DOM WRITE\n     *\n     * @param activeView The view that is actively visible in the stack. Used to calculate which views to unmount.\n     */\n    unmountInactiveViews(activeView) {\n        // ok, cleanup time!! Destroy all of the views that are\n        // INACTIVE and come after the active view\n        // only do this if the views exist, though\n        if (this.destroyed) {\n            return;\n        }\n        const views = this.views;\n        const activeViewIndex = views.indexOf(activeView);\n        for (let i = views.length - 1; i >= 0; i--) {\n            const view = views[i];\n            /**\n             * When inserting multiple views via insertPages\n             * the last page will be transitioned to, but the\n             * others will not be. As a result, a DOM element\n             * will only be created for the last page inserted.\n             * As a result, it is possible to have views in the\n             * stack that do not have `view.element` yet.\n             */\n            const element = view.element;\n            if (element) {\n                if (i > activeViewIndex) {\n                    // this view comes after the active view\n                    // let's unload it\n                    lifecycle(element, LIFECYCLE_WILL_UNLOAD);\n                    this.destroyView(view);\n                }\n                else if (i < activeViewIndex) {\n                    // this view comes before the active view\n                    // and it is not a portal then ensure it is hidden\n                    setPageHidden(element, true);\n                }\n            }\n        }\n    }\n    canStart() {\n        return (!this.gestureOrAnimationInProgress &&\n            !!this.swipeGesture &&\n            !this.isTransitioning &&\n            this.transInstr.length === 0 &&\n            this.canGoBackSync());\n    }\n    onStart() {\n        this.gestureOrAnimationInProgress = true;\n        this.pop({ direction: 'back', progressAnimation: true });\n    }\n    onMove(stepValue) {\n        if (this.sbAni) {\n            this.sbAni.progressStep(stepValue);\n        }\n    }\n    onEnd(shouldComplete, stepValue, dur) {\n        if (this.sbAni) {\n            this.sbAni.onFinish(() => {\n                this.gestureOrAnimationInProgress = false;\n            }, { oneTimeCallback: true });\n            // Account for rounding errors in JS\n            let newStepValue = shouldComplete ? -0.001 : 0.001;\n            /**\n             * Animation will be reversed here, so need to\n             * reverse the easing curve as well\n             *\n             * Additionally, we need to account for the time relative\n             * to the new easing curve, as `stepValue` is going to be given\n             * in terms of a linear curve.\n             */\n            if (!shouldComplete) {\n                this.sbAni.easing('cubic-bezier(1, 0, 0.68, 0.28)');\n                newStepValue += getTimeGivenProgression([0, 0], [1, 0], [0.68, 0.28], [1, 1], stepValue)[0];\n            }\n            else {\n                newStepValue += getTimeGivenProgression([0, 0], [0.32, 0.72], [0, 1], [1, 1], stepValue)[0];\n            }\n            this.sbAni.progressEnd(shouldComplete ? 1 : 0, newStepValue, dur);\n        }\n        else {\n            this.gestureOrAnimationInProgress = false;\n        }\n    }\n    render() {\n        return h(\"slot\", { key: '6894eccc60e446294b01261477691ea1e88348ab' });\n    }\n    get el() { return getElement(this); }\n    static get watchers() { return {\n        \"swipeGesture\": [\"swipeGestureChanged\"],\n        \"root\": [\"rootChanged\"]\n    }; }\n};\nNav.style = IonNavStyle0;\n\nconst navLink = (el, routerDirection, component, componentProps, routerAnimation) => {\n    const nav = el.closest('ion-nav');\n    if (nav) {\n        if (routerDirection === 'forward') {\n            if (component !== undefined) {\n                return nav.push(component, componentProps, { skipIfBusy: true, animationBuilder: routerAnimation });\n            }\n        }\n        else if (routerDirection === 'root') {\n            if (component !== undefined) {\n                return nav.setRoot(component, componentProps, { skipIfBusy: true, animationBuilder: routerAnimation });\n            }\n        }\n        else if (routerDirection === 'back') {\n            return nav.pop({ skipIfBusy: true, animationBuilder: routerAnimation });\n        }\n    }\n    return Promise.resolve(false);\n};\n\nconst NavLink = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.onClick = () => {\n            return navLink(this.el, this.routerDirection, this.component, this.componentProps, this.routerAnimation);\n        };\n        this.component = undefined;\n        this.componentProps = undefined;\n        this.routerDirection = 'forward';\n        this.routerAnimation = undefined;\n    }\n    render() {\n        return h(Host, { key: 'dab6e8a908395d99c87452c5e5aa4e61d9e72435', onClick: this.onClick });\n    }\n    get el() { return getElement(this); }\n};\n\nexport { Nav as ion_nav, NavLink as ion_nav_link };\n"], "names": ["r", "registerInstance", "d", "createEvent", "h", "f", "getElement", "H", "Host", "g", "getTimeGivenProgression", "o", "assert", "s", "shallowEqualStringMap", "l", "lifecycle", "t", "transition", "setPageHidden", "LIFECYCLE_WILL_UNLOAD", "b", "LIFECYCLE_WILL_LEAVE", "c", "LIFECYCLE_DID_LEAVE", "getIonMode", "config", "a", "attachComponent", "VIEW_STATE_NEW", "VIEW_STATE_ATTACHED", "VIEW_STATE_DESTROYED", "ViewController", "constructor", "component", "params", "state", "init", "container", "_this", "_asyncToGenerator", "element", "delegate", "_destroy", "removeViewFromDom", "parentElement", "remove", "nav", "undefined", "matches", "view", "id", "convertToView", "page", "convertToViews", "pages", "map", "componentProps", "filter", "v", "navCss", "IonNavStyle0", "Nav", "hostRef", "ionNavWillLoad", "ionNavWillChange", "ionNavDidChange", "transInstr", "gestureOrAnimationInProgress", "useRouter", "isTransitioning", "destroyed", "views", "didLoad", "swipeGesture", "animated", "animation", "rootParams", "root", "swipeGestureChanged", "gesture", "enable", "rootChanged", "setRoot", "componentWillLoad", "document", "querySelector", "el", "closest", "mode", "getBoolean", "emit", "componentDidLoad", "_this2", "createSwipeBackGesture", "canStart", "bind", "onStart", "onMove", "onEnd", "connectedCallback", "disconnectedCallback", "destroy", "length", "push", "opts", "done", "insert", "insertIndex", "insertPages", "insertComponents", "queueTrns", "insertStart", "insertViews", "pop", "removeIndex", "popTo", "indexOrViewCtrl", "ti", "removeStart", "removeCount", "<PERSON><PERSON><PERSON><PERSON>", "popToRoot", "startIndex", "setPages", "setRouteId", "direction", "active", "getActiveSync", "Promise", "resolve", "changed", "promise", "finish", "commonOpts", "updateURL", "viewIsReady", "enteringEl", "mark", "p", "markVisible", "_ref", "apply", "arguments", "viewController", "find", "Object", "assign", "animationBuilder", "getRouteId", "_this3", "tagName", "getActive", "_this4", "getByIndex", "index", "_this5", "canGoBack", "_this6", "canGoBackSync", "getPrevious", "_this7", "getPreviousSync", "<PERSON><PERSON><PERSON><PERSON>", "indexOf", "_this8", "_a", "_b", "skipIfBusy", "reject", "router", "canTransition", "nextTrns", "success", "result", "fireError", "hasCompleted", "requiresTransition", "enteringView", "leavingView", "navChanged", "failed", "rejectReason", "shift", "runTransition", "_this9", "prepareTI", "getEnteringView", "Error", "postViewInit", "enteringRequiresTransition", "leavingRequiresTransition", "isBackDirection", "_c", "viewsLength", "viewControllers", "removeEnd", "i", "destroyQueue", "finalNumViews", "console", "warn", "insertViewAt", "destroyView", "_this0", "progressCallback", "progressAnimation", "ani", "onFinish", "oneTimeCallback", "progressEnd", "sbAni", "leavingEl", "animationOpts", "showGoBack", "baseEl", "get", "transitionFinish", "activeView", "unmountInactiveViews", "existingIndex", "splice", "activeViewIndex", "<PERSON><PERSON><PERSON><PERSON>", "progressStep", "shouldComplete", "dur", "newStepValue", "easing", "render", "key", "watchers", "style", "navLink", "routerDirection", "routerAnimation", "NavLink", "onClick", "ion_nav", "ion_nav_link"], "sourceRoot": "webpack:///", "x_google_ignoreList": [0]}