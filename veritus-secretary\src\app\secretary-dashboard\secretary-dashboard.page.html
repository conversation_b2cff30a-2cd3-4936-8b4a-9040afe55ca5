<ion-content class="dashboard-content">
  <div class="dashboard-container">

    <!-- Secretary Profile Header -->
    <div class="profile-header-section">
      <div class="profile-card">
        <div class="profile-main">
          <div class="profile-avatar">
            <ion-icon name="person-circle" *ngIf="!secretaryProfile?.avatar"></ion-icon>
            <img [src]="secretaryProfile?.avatar" *ngIf="secretaryProfile?.avatar" alt="Secretary Avatar" />
          </div>
          <div class="profile-info">
            <h1 class="profile-name">{{ secretaryProfile?.name || '<PERSON>' }}</h1>
            <p class="profile-title">Secretary</p>
            <p class="profile-email">{{ secretaryProfile?.email || '<EMAIL>' }}</p>
            <p class="profile-phone">{{ secretaryProfile?.phone || '+****************' }}</p>
          </div>
          <div class="profile-actions">
            <button class="edit-btn" (click)="onEditProfile()">
              <ion-icon name="create-outline"></ion-icon>
            </button>
          </div>
        </div>
        <div class="profile-stats">
          <div class="stat-item">
            <span class="stat-number">{{ stats.linkedLawyers }}</span>
            <span class="stat-label">Linked Lawyers</span>
          </div>
          <div class="stat-item">
            <span class="stat-number">1 year ago</span>
            <span class="stat-label">Member Since</span>
          </div>
          <div class="stat-item">
            <span class="stat-number">{{ getPermissionCount() }}/6</span>
            <span class="stat-label">Active Permissions</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Main Dashboard Layout -->
    <div class="dashboard-layout">

      <!-- Left Column: Scheduling & Lawyers -->
      <div class="left-column">

        <!-- Scheduling Section -->
        <div class="scheduling-section">
          <h2>Scheduling</h2>

          <!-- Calendar Widget -->
          <div class="calendar-widget">
            <div class="calendar-header">
              <button class="nav-btn" (click)="previousMonth()">‹</button>
              <h3>{{ currentMonth }}</h3>
              <button class="nav-btn" (click)="nextMonth()">›</button>
            </div>

            <div class="calendar-grid">
              <div class="day-header" *ngFor="let day of dayHeaders">{{ day }}</div>
              <div
                class="calendar-day"
                *ngFor="let day of calendarDays"
                [class.other-month]="day.otherMonth"
                [class.today]="day.isToday"
                [class.has-appointment]="day.hasAppointment"
                (click)="selectDate(day)">
                {{ day.date }}
              </div>
            </div>
          </div>

          <!-- Booking Section -->
          <div class="booking-section">
            <h4>Booking on {{ selectedDateString }}</h4>
            <div class="booking-list">
              <div class="booking-item">
                <div class="booking-info">
                  <div class="booking-type">Consultation</div>
                  <div class="booking-client">John Bryant</div>
                </div>
                <div class="booking-time">10:00</div>
              </div>
              <div class="booking-item">
                <div class="booking-info">
                  <div class="booking-type">Consultation</div>
                  <div class="booking-client">LeBron James</div>
                </div>
                <div class="booking-time">11:00</div>
              </div>
            </div>
            <button class="new-booking-btn">+ New Booking</button>
          </div>
        </div>

        <!-- Linked Lawyers Section -->
        <div class="lawyers-section">
          <div class="section-header">
            <h2>Linked Lawyers ({{ linkedLawyers.length }})</h2>
            <button class="add-btn" (click)="onLinkNewLawyer()">+ Link New</button>
          </div>
          <div class="lawyers-list">
            <div class="lawyer-card" *ngFor="let lawyer of linkedLawyers" (click)="onViewLawyer(lawyer.id)">
              <div class="lawyer-main">
                <div class="lawyer-avatar">
                  <ion-icon name="person-circle" *ngIf="!lawyer.avatar"></ion-icon>
                  <img [src]="lawyer.avatar" *ngIf="lawyer.avatar" [alt]="lawyer.name" />
                </div>
                <div class="lawyer-details">
                  <h3 class="lawyer-name">{{ lawyer.name }}</h3>
                  <p class="lawyer-email">{{ lawyer.email }}</p>
                  <div class="lawyer-credentials">
                    <span>Roll: {{ lawyer.rollNumber }}</span>
                    <span>Bar: {{ lawyer.barId }}</span>
                  </div>
                </div>
                <div class="lawyer-status">
                  <span class="status-badge active">{{ lawyer.status | titlecase }}</span>
                </div>
              </div>
              <div class="lawyer-stats">
                <div class="stat">
                  <span class="stat-number">{{ lawyer.caseCount }}</span>
                  <span class="stat-label">Cases</span>
                </div>
                <div class="stat">
                  <span class="stat-number">{{ lawyer.appointmentCount }}</span>
                  <span class="stat-label">Appointments</span>
                </div>
                <div class="stat">
                  <span class="stat-number">{{ formatDate(lawyer.lastActivity) }}</span>
                  <span class="stat-label">Last Active</span>
                </div>
              </div>
              <div class="lawyer-permissions">
                <span class="permission-tag" *ngIf="lawyer.permissions?.canManageCalendar">Calendar</span>
                <span class="permission-tag" *ngIf="lawyer.permissions?.canManageFiles">Files</span>
                <span class="permission-tag" *ngIf="lawyer.permissions?.canManageCases">Cases</span>
                <span class="permission-tag" *ngIf="lawyer.permissions?.canManageRetainers">Retainers</span>
                <span class="permission-tag" *ngIf="lawyer.permissions?.canViewFinances">View Finances</span>
              </div>
            </div>
          </div>
        </div>

      </div>

      <!-- Right Column: Files & Finance -->
      <div class="right-column">

        <!-- Files Section -->
        <div class="files-section">
          <div class="section-header">
            <h2>Files</h2>
            <div class="search-box">
              <input type="text" placeholder="Search a document" [(ngModel)]="searchTerm">
              <ion-icon name="search-outline" class="search-icon"></ion-icon>
            </div>
          </div>

          <div class="file-categories">
            <div class="file-category">
              <div class="folder-icon">📁</div>
              <span>Disclosures</span>
            </div>
            <div class="file-category">
              <div class="folder-icon">📁</div>
              <span>Evidence</span>
            </div>
            <div class="file-category">
              <div class="folder-icon">📁</div>
              <span>Receipts</span>
            </div>
            <div class="file-category">
              <div class="folder-icon">📁</div>
              <span>Contracts</span>
            </div>
          </div>

          <div class="recent-documents">
            <h4>Recent documents</h4>
            <div class="document-item" *ngFor="let doc of recentDocuments">
              <div class="document-icon">📄</div>
              <div class="document-info">
                <div class="document-name">{{ doc.name }}</div>
                <div class="document-date">{{ doc.date }}</div>
              </div>
              <ion-icon name="chevron-forward-outline"></ion-icon>
            </div>
          </div>
        </div>

        <!-- Finance Section -->
        <div class="finance-section">
          <div class="section-header">
            <h2>Finance</h2>
            <button class="new-transaction-btn">+ New Transaction</button>
          </div>

          <div class="finance-chart">
            <div class="chart-container">
              <div class="chart-bars">
                <div class="bar" *ngFor="let data of chartData" [style.height.%]="data.height" [style.background-color]="data.color"></div>
              </div>
              <div class="chart-labels">
                <span *ngFor="let label of chartLabels">{{ label }}</span>
              </div>
              <div class="chart-period">June 2025</div>
            </div>
          </div>

          <div class="recent-transactions">
            <h4>Recent Transactions</h4>
            <div class="transaction-item" *ngFor="let transaction of recentTransactions">
              <div class="transaction-amount">{{ transaction.amount }}</div>
              <div class="transaction-info">
                <div class="transaction-type">{{ transaction.type }}</div>
                <div class="transaction-date">{{ transaction.date }}</div>
              </div>
            </div>
          </div>
        </div>

      </div>
    </div>

  </div>
</ion-content>
