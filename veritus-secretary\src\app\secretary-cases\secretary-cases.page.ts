import { Component, OnInit } from '@angular/core';
import { FirebaseService, LawyerProfile } from '../services/firebase.service';

interface Case {
  id?: string;
  title: string;
  clientName: string;
  description: string;
  lawyerId: string;
  status: 'ongoing' | 'closed' | 'pending';
  fileCount: number;
  createdAt: Date;
  updatedAt: Date;
}

interface CaseWithLawyer extends Case {
  lawyerName: string;
}

@Component({
  selector: 'app-secretary-cases',
  templateUrl: './secretary-cases.page.html',
  styleUrls: ['./secretary-cases.page.scss'],
})
export class SecretaryCasesPage implements OnInit {
  linkedLawyers: LawyerProfile[] = [];
  selectedLawyer: string = 'all';
  cases: CaseWithLawyer[] = [];
  filteredCases: CaseWithLawyer[] = [];
  searchTerm: string = '';
  selectedStatus: string = 'all';

  constructor(
    private firebaseService: FirebaseService
  ) { }

  ngOnInit() {
    this.loadLinkedLawyers();
    this.loadCases();
  }

  async loadLinkedLawyers() {
    const currentUser = this.firebaseService.getCurrentUser();
    if (currentUser) {
      this.linkedLawyers = await this.firebaseService.getSecretaryLinkedLawyers(currentUser.uid);
    }
  }

  async loadCases() {
    // Mock cases for now since we don't have the full case service
    const mockCases: CaseWithLawyer[] = [
      {
        id: '1',
        title: 'Contract Dispute - ABC Corp',
        clientName: 'ABC Corporation',
        description: 'Contract dispute regarding service delivery terms',
        lawyerId: 'lawyer1',
        lawyerName: 'Atty. Smith',
        status: 'ongoing',
        fileCount: 5,
        createdAt: new Date('2024-01-15'),
        updatedAt: new Date('2024-01-20')
      },
      {
        id: '2',
        title: 'Employment Case - John Doe',
        clientName: 'John Doe',
        description: 'Wrongful termination case',
        lawyerId: 'lawyer2',
        lawyerName: 'Atty. Johnson',
        status: 'pending',
        fileCount: 3,
        createdAt: new Date('2024-01-10'),
        updatedAt: new Date('2024-01-18')
      },
      {
        id: '3',
        title: 'Property Settlement',
        clientName: 'Jane Smith',
        description: 'Divorce property settlement case',
        lawyerId: 'lawyer1',
        lawyerName: 'Atty. Smith',
        status: 'closed',
        fileCount: 8,
        createdAt: new Date('2023-12-01'),
        updatedAt: new Date('2024-01-05')
      }
    ];
    
    this.cases = mockCases.sort((a, b) => 
      new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime()
    );
    
    this.filterCases();
  }

  filterCases() {
    this.filteredCases = this.cases.filter(caseItem => {
      const matchesLawyer = this.selectedLawyer === 'all' || caseItem.lawyerId === this.selectedLawyer;
      const matchesStatus = this.selectedStatus === 'all' || caseItem.status === this.selectedStatus;
      const matchesSearch = !this.searchTerm || 
        caseItem.title.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
        caseItem.clientName.toLowerCase().includes(this.searchTerm.toLowerCase());
      
      return matchesLawyer && matchesStatus && matchesSearch;
    });
  }

  onLawyerChange() {
    this.filterCases();
  }

  onStatusChange() {
    this.filterCases();
  }

  onSearchChange() {
    this.filterCases();
  }

  async onCreateCase() {
    if (this.linkedLawyers.length === 0) {
      alert('You need to be linked with at least one lawyer to create cases.');
      return;
    }

    const title = prompt('Enter case title:');
    const clientName = prompt('Enter client name:');
    const description = prompt('Enter case description:');
    const lawyerName = prompt('Enter lawyer name:', this.linkedLawyers[0]?.name || '');
    
    if (title && clientName && description && lawyerName) {
      const lawyer = this.linkedLawyers.find(l => l.name.toLowerCase().includes(lawyerName.toLowerCase()));
      
      if (!lawyer) {
        alert('Lawyer not found. Please enter a valid lawyer name.');
        return;
      }

      const newCase: CaseWithLawyer = {
        id: Date.now().toString(),
        title,
        clientName,
        description,
        lawyerId: lawyer.uid,
        lawyerName: lawyer.name,
        status: 'ongoing',
        fileCount: 0,
        createdAt: new Date(),
        updatedAt: new Date()
      };

      this.cases.unshift(newCase);
      this.filterCases();
      alert('Case created successfully!');
    }
  }

  async onUpdateCaseProgress(caseItem: CaseWithLawyer) {
    const status = prompt('Enter status (ongoing/closed/pending):', caseItem.status);
    const description = prompt('Enter updated description:', caseItem.description);
    const progressNote = prompt('Enter progress note:');
    
    if (status && description) {
      const index = this.cases.findIndex(c => c.id === caseItem.id);
      if (index !== -1) {
        this.cases[index] = {
          ...caseItem,
          status: status as any,
          description,
          updatedAt: new Date()
        };
        this.filterCases();
        alert('Case progress updated successfully!');
      }
    }
  }

  getStatusColor(status: string): string {
    switch (status) {
      case 'ongoing': return 'primary';
      case 'closed': return 'success';
      case 'pending': return 'warning';
      default: return 'medium';
    }
  }

  getStatusIcon(status: string): string {
    switch (status) {
      case 'ongoing': return 'play-circle';
      case 'closed': return 'checkmark-circle';
      case 'pending': return 'time';
      default: return 'help-circle';
    }
  }

  getOngoingCount(): number {
    return this.cases.filter(c => c.status === 'ongoing').length;
  }

  getPendingCount(): number {
    return this.cases.filter(c => c.status === 'pending').length;
  }

  getClosedCount(): number {
    return this.cases.filter(c => c.status === 'closed').length;
  }

  onViewCase(caseItem: CaseWithLawyer) {
    // TODO: Navigate to case details page
    console.log('View case:', caseItem);
    alert(`Viewing case: ${caseItem.title}\nClient: ${caseItem.clientName}\nStatus: ${caseItem.status}`);
  }
}
