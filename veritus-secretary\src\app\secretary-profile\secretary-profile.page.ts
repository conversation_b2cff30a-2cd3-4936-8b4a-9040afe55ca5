import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { FirebaseService, SecretaryProfile } from '../services/firebase.service';

@Component({
  selector: 'app-secretary-profile',
  templateUrl: './secretary-profile.page.html',
  styleUrls: ['./secretary-profile.page.scss'],
})
export class SecretaryProfilePage implements OnInit {
  secretary: SecretaryPro<PERSON>le | null = null;
  isEditing = false;
  editForm = {
    name: '',
    phone: '',
    avatar: ''
  };

  constructor(
    private router: Router,
    private firebaseService: FirebaseService
  ) { }

  ngOnInit() {
    this.loadSecretaryProfile();
  }

  async loadSecretaryProfile() {
    const currentUser = this.firebaseService.getCurrentUser();
    if (currentUser) {
      this.secretary = await this.firebaseService.getSecretaryProfile(currentUser.uid);
      if (this.secretary) {
        this.editForm = {
          name: this.secretary.name,
          phone: this.secretary.phone || '',
          avatar: this.secretary.avatar || ''
        };
      }
    }
  }

  onEditProfile() {
    this.isEditing = true;
  }

  onCancelEdit() {
    this.isEditing = false;
    if (this.secretary) {
      this.editForm = {
        name: this.secretary.name,
        phone: this.secretary.phone || '',
        avatar: this.secretary.avatar || ''
      };
    }
  }

  async onSaveProfile() {
    if (!this.secretary) return;

    try {
      await this.firebaseService.updateSecretaryProfile(this.secretary.uid, {
        name: this.editForm.name,
        phone: this.editForm.phone,
        avatar: this.editForm.avatar
      });

      // Update local secretary object
      this.secretary = {
        ...this.secretary,
        name: this.editForm.name,
        phone: this.editForm.phone,
        avatar: this.editForm.avatar
      };

      this.isEditing = false;
      alert('Profile updated successfully!');
    } catch (error) {
      console.error('Error updating profile:', error);
      alert('Error updating profile. Please try again.');
    }
  }

  async onLogout() {
    const confirmed = confirm('Are you sure you want to logout?');
    if (confirmed) {
      try {
        await this.firebaseService.signOut();
        this.router.navigate(['/auth/signin']);
      } catch (error) {
        console.error('Error signing out:', error);
        alert('Error signing out. Please try again.');
      }
    }
  }

  onManageLinkedLawyers() {
    this.router.navigate(['/link-lawyer']);
  }

  onViewAuditLog() {
    this.router.navigate(['/audit-log']);
  }

  getPermissionCount(): number {
    if (!this.secretary) return 0;
    
    const permissions = this.secretary.permissions;
    return Object.values(permissions).filter(Boolean).length;
  }

  getLinkedLawyersCount(): number {
    return this.secretary?.linkedLawyers?.length || 0;
  }
}
