import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

// Services
import { EncryptedStorageService } from './services/encrypted-storage.service';
import { FileManagerService } from './services/file-manager.service';
import { ArchiveService } from './services/archive.service';
import { AccessControlService } from './services/access-control.service';

// Components
import { FileUploadComponent } from './components/file-upload/file-upload.component';
import { FileListComponent } from './components/file-list/file-list.component';
import { FileViewerComponent } from './components/file-viewer/file-viewer.component';

@NgModule({
  declarations: [
    FileUploadComponent,
    FileListComponent,
    FileViewerComponent
  ],
  imports: [
    CommonModule
  ],
  providers: [
    EncryptedStorageService,
    FileManagerService,
    ArchiveService,
    AccessControlService
  ],
  exports: [
    FileUploadComponent,
    FileListComponent,
    FileViewerComponent
  ]
})
export class StorageModule { }
