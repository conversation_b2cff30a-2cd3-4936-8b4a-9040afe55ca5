import { Component, OnInit } from '@angular/core';
import { AdminAuthService, AdminUser } from '../services/admin-auth.service';

interface DashboardStats {
  pendingVerifications: number;
  verifiedLawyers: number;
  totalUsers: number;
  activeTemplates: number;
  newVerificationsToday: number;
  verifiedToday: number;
  newUsersToday: number;
  templatesUpdated: number;
}

interface VerificationRequest {
  id: string;
  name: string;
  rollNumber: string;
  firm: string;
  avatar?: string;
  status: 'pending' | 'approved' | 'rejected';
  submittedAt: Date;
}

interface SystemActivity {
  id: string;
  type: 'verification' | 'user' | 'system' | 'template';
  description: string;
  timestamp: Date;
}

interface PlatformHealth {
  serverStatus: 'online' | 'offline' | 'maintenance';
  databaseStatus: 'connected' | 'disconnected';
  storageUsage: number;
  apiResponseTime: number;
}

@Component({
  selector: 'app-dashboard',
  templateUrl: './dashboard.component.html',
  styleUrls: ['./dashboard.component.scss']
})
export class DashboardComponent implements OnInit {
  currentAdmin: AdminUser | null = null;
  activityFilter = 'today';

  stats: DashboardStats = {
    pendingVerifications: 12,
    verifiedLawyers: 1247,
    totalUsers: 3456,
    activeTemplates: 23,
    newVerificationsToday: 5,
    verifiedToday: 8,
    newUsersToday: 15,
    templatesUpdated: 2
  };

  recentVerifications: VerificationRequest[] = [
    {
      id: '1',
      name: 'John Doe',
      rollNumber: '123456',
      firm: 'ABC Legal Services',
      status: 'pending',
      submittedAt: new Date(Date.now() - 2 * 60 * 60 * 1000) // 2 hours ago
    },
    {
      id: '2',
      name: 'Jane Cruz',
      rollNumber: '783012',
      firm: 'XYZ Law Group',
      status: 'pending',
      submittedAt: new Date(Date.now() - 4 * 60 * 60 * 1000) // 4 hours ago
    },
    {
      id: '3',
      name: 'Michael Santos',
      rollNumber: '456789',
      firm: 'Santos & Associates',
      status: 'approved',
      submittedAt: new Date(Date.now() - 6 * 60 * 60 * 1000) // 6 hours ago
    },
    {
      id: '4',
      name: 'Maria Garcia',
      rollNumber: '987654',
      firm: 'Garcia Law Firm',
      status: 'pending',
      submittedAt: new Date(Date.now() - 8 * 60 * 60 * 1000) // 8 hours ago
    }
  ];

  systemActivities: SystemActivity[] = [
    {
      id: '1',
      type: 'verification',
      description: 'Lawyer verification approved for John Smith',
      timestamp: new Date(Date.now() - 30 * 60 * 1000) // 30 minutes ago
    },
    {
      id: '2',
      type: 'user',
      description: 'New user registration: <EMAIL>',
      timestamp: new Date(Date.now() - 1 * 60 * 60 * 1000) // 1 hour ago
    },
    {
      id: '3',
      type: 'template',
      description: 'Contract template updated by Admin',
      timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000) // 2 hours ago
    },
    {
      id: '4',
      type: 'system',
      description: 'System backup completed successfully',
      timestamp: new Date(Date.now() - 3 * 60 * 60 * 1000) // 3 hours ago
    },
    {
      id: '5',
      type: 'verification',
      description: 'Lawyer verification rejected for invalid documents',
      timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000) // 4 hours ago
    }
  ];

  platformHealth: PlatformHealth = {
    serverStatus: 'online',
    databaseStatus: 'connected',
    storageUsage: 75,
    apiResponseTime: 245
  };

  constructor(private adminAuthService: AdminAuthService) { }

  ngOnInit(): void {
    this.adminAuthService.currentAdmin$.subscribe(admin => {
      this.currentAdmin = admin;
    });

    this.loadDashboardData();
  }

  filterActivity(): void {
    // Filter activities based on selected time period
    const now = new Date();
    let filterDate: Date;

    switch (this.activityFilter) {
      case 'today':
        filterDate = new Date(now.getFullYear(), now.getMonth(), now.getDate());
        break;
      case 'week':
        filterDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        break;
      case 'month':
        filterDate = new Date(now.getFullYear(), now.getMonth(), 1);
        break;
      default:
        filterDate = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    }

    // In a real application, you would fetch filtered data from the backend
    console.log('Filtering activities from:', filterDate);
  }

  getActivityIcon(type: string): string {
    const icons = {
      verification: '✓',
      user: '👤',
      system: '⚙',
      template: '📄'
    };
    return icons[type as keyof typeof icons] || '•';
  }



  getInitials(name: string): string {
    if (!name) return '?';
    return name
      .split(' ')
      .map(word => word.charAt(0).toUpperCase())
      .slice(0, 2)
      .join('');
  }

  onImageError(event: any, verification: any): void {
    // Hide the broken image and show placeholder instead
    event.target.style.display = 'none';
    verification.avatar = undefined;
  }

  private loadDashboardData(): void {
    // In a real application, you would fetch this data from backend services
    // For now, we're using mock data initialized above

    // Example of how you might fetch real data:
    // this.dashboardService.getStats().subscribe(stats => this.stats = stats);
    // this.dashboardService.getRecentVerifications().subscribe(verifications => this.recentVerifications = verifications);
    // this.dashboardService.getSystemActivities().subscribe(activities => this.systemActivities = activities);
    // this.dashboardService.getPlatformHealth().subscribe(health => this.platformHealth = health);
  }
}
