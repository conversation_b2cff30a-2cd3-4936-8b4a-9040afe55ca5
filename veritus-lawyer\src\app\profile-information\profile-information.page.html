<ion-header>
  <ion-toolbar class="custom-header">
    <ion-buttons slot="start">
      <ion-back-button defaultHref="/" (click)="onBack()"></ion-back-button>
    </ion-buttons>
    <ion-title>Profile Information</ion-title>
  </ion-toolbar>
</ion-header>

<ion-content class="profile-page">

  <div class="avatar-container">
    <div class="avatar-circle">
      <!-- User uploaded image from Firebase -->
      <img *ngIf="profileImageUrl"
           [src]="profileImageUrl"
           alt="Profile"
           class="avatar-image">

      <!-- Default SVG avatar when no image is uploaded -->
      <svg *ngIf="!profileImageUrl" width="110" height="110" viewBox="0 0 110 110" fill="none" xmlns="http://www.w3.org/2000/svg">
        <!-- Professional Lawyer Character - Simple Design -->

        <!-- Green Background -->
        <circle cx="55" cy="55" r="55" fill="#4CAF50"/>

        <!-- Face -->
        <circle cx="55" cy="40" r="20" fill="#FFCCB3"/>

        <!-- Eyes -->
        <circle cx="48" cy="36" r="2.5" fill="#333333"/>
        <circle cx="62" cy="36" r="2.5" fill="#333333"/>

        <!-- Smile -->
        <path d="M48 46 Q55 52 62 46" stroke="#333333" stroke-width="2" fill="none"/>

        <!-- Suit -->
        <rect x="35" y="60" width="40" height="40" fill="#333333"/>

        <!-- Shirt -->
        <rect x="45" y="60" width="20" height="30" fill="#FFFFFF"/>

        <!-- Tie -->
        <polygon points="55,60 60,60 57,75 53,75 50,60" fill="#FF0000"/>

        <!-- Briefcase (Left) -->
        <rect x="25" y="75" width="10" height="15" fill="#8B4513"/>

        <!-- Scales (Right) -->
        <rect x="75" y="75" width="10" height="15" fill="#FFD700"/>
      </svg>
    </div>
    <div class="verified-badge">
      <ion-icon name="checkmark-circle-outline" color="success"></ion-icon>
      <ion-badge color="warning" class="verified-text">Verified</ion-badge>
    </div>
  </div>

  <div class="form-container">
    <div class="form-field">
      <div class="field-label">Name:</div>
      <ion-input [(ngModel)]="profileData.name" readonly="true" class="field-input readonly-field"></ion-input>
    </div>

    <div class="form-field">
      <div class="field-label">Law Firm:</div>
      <div class="input-with-icon">
        <ion-input [(ngModel)]="profileData.lawFirm" placeholder="Enter your law firm name" class="field-input"></ion-input>
        <ion-icon name="location-outline" class="location-icon" (click)="onLocationClick()"></ion-icon>
      </div>
    </div>

    <div class="form-row">
      <div class="form-field half-width">
        <div class="field-label">Years of Experience:</div>
        <ion-input type="number" [(ngModel)]="profileData.yearsOfExperience" placeholder="Enter years of experience" class="field-input"></ion-input>
      </div>

      <div class="form-field half-width">
        <div class="field-label">Bar Roll Number:</div>
        <ion-input [(ngModel)]="profileData.barRollNumber" placeholder="#123145" class="field-input"></ion-input>
      </div>
    </div>
  </div>

  <ion-button expand="block" class="save-button" color="warning" (click)="onSave()">SAVE</ion-button>

</ion-content>
