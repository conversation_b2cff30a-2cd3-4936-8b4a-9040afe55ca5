// White and Gold Color Palette
:root {
  --md-primary-label: #000000;
  --md-icon-default: #000000;
  --md-background-primary: #FFFFFF;
  --md-on-surface-disabled: rgba(0, 0, 0, 0.2);
  --md-secondary-text: #616161;
  --md-divider: rgba(0, 0, 0, 0.06);
  --md-surface-variant: rgba(213, 208, 200, 0.38);
  --md-accent-gold: #C49A56;
  --md-accent-blue: #1B3A9E;
  --md-status-green: #67EF77;
}

.dashboard-content {
  --background: #FFFFFF !important;
  background: #FFFFFF !important;
  background-color: #FFFFFF !important;
  padding: 0;
}

// Force white background on all elements
ion-content {
  --background: #FFFFFF !important;
  background: #FFFFFF !important;
  background-color: #FFFFFF !important;
}

// Ensure body and html are white
body, html {
  background: #FFFFFF !important;
  background-color: #FFFFFF !important;
}

.status-bar-spacer {
  height: 44px;
  background: #FFFFFF;
}

.dashboard-container {
  padding: 16px;
  background: #FFFFFF;
  min-height: calc(100vh - 44px);
}

.dashboard-header {
  margin-bottom: 24px;
}

.greeting-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.greeting {
  margin: 0;
  flex: 1;
  font-size: 24px;
  font-weight: 700;
  color: var(--md-primary-label);
  line-height: 1.2;
}

.notification-btn {
  width: 44px;
  height: 44px;
  border-radius: 50%;
  background-color: #FFFFFF;
  border: 1px solid #E0E0E0;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  position: relative;

  &:hover {
    background-color: #F5F5F5;
    border-color: #C49A56;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);

    .notification-icon {
      color: #C49A56;
    }
  }

  &:active {
    transform: translateY(0);
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  }
}

.notification-icon {
  font-size: 22px;
  color: #666666;
  font-weight: normal;
  transition: color 0.3s ease;
}

.notification-badge {
  position: absolute;
  top: -2px;
  right: -2px;
  background: linear-gradient(135deg, #FF4757, #FF3742);
  color: #FFFFFF;
  border-radius: 50%;
  min-width: 20px;
  height: 20px;
  font-size: 11px;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid #FFFFFF;
  box-shadow: 0 2px 6px rgba(255, 71, 87, 0.4);
  animation: pulse 2s infinite;
  padding: 0 4px;
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}

// Summary Cards Section
.summary-section {
  margin-bottom: 32px;
}

.summary-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
}

.summary-card {
  background: #FFFFFF;
  border-radius: 12px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(196, 154, 86, 0.2);
}

.summary-content {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 8px;
}

.summary-icon-container {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.summary-icon {
  font-size: 24px;
  color: var(--md-accent-gold);
}

.summary-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.summary-title {
  font-size: 16px;
  color: var(--md-secondary-text);
  margin: 0;
  line-height: 1.2;
}

.summary-value {
  font-size: 16px;
  font-weight: 700;
  color: var(--md-primary-label);
  margin: 0;
  line-height: 1.2;
}

// Appointments Section
.appointments-section {
  margin-bottom: 32px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.section-title {
  font-size: 24px;
  font-weight: 700;
  color: var(--md-primary-label);
  margin: 0;
  line-height: 1.2;
}

.see-all-btn {
  background: none;
  border: none;
  padding: 8px 0;
  cursor: pointer;
  font-size: 16px;
  font-weight: 500;
  color: var(--md-accent-gold);
}

.appointments-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.appointment-card {
  background: #FFFFFF;
  border-radius: 12px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(196, 154, 86, 0.2);
  cursor: pointer;
}

.appointment-content {
  display: flex;
  align-items: flex-start;
  gap: 12px;
}

.appointment-icon {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.clip-icon {
  font-size: 24px;
  color: var(--md-accent-gold);
}

.appointment-details {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.appointment-type {
  font-size: 16px;
  font-weight: 700;
  color: var(--md-primary-label);
  margin: 0;
  line-height: 1.2;
}

.client-name {
  font-size: 16px;
  color: var(--md-secondary-text);
  margin: 0;
  line-height: 1.2;
}

.case-status-row {
  display: flex;
  align-items: center;
  gap: 8px;
}

.case-status-label {
  font-size: 16px;
  color: var(--md-secondary-text);
}

.status-badge {
  background: var(--md-status-green);
  color: var(--md-background-primary);
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 14px;
  font-weight: 500;
}

.appointment-meta {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.appointment-date,
.appointment-time {
  font-size: 16px;
  color: var(--md-secondary-text);
  margin: 0;
  line-height: 1.2;
}

// Case Files Section
.case-file-actions {
  display: flex;
  align-items: center;
  gap: 6px;
  margin-left: auto;
}

.case-file-actions ion-icon.arrow-icon {
  font-size: 20px;
  color: var(--ion-color-medium);
  margin-right: 4px;
}

.case-files-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.case-file-item {
  background: #FFFFFF;
  border-radius: 12px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(196, 154, 86, 0.2);
  cursor: pointer;
}

.case-file-content {
  display: flex;
  align-items: center;
  gap: 16px;
}

.case-file-icon-container {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.case-file-icon {
  font-size: 24px;
  color: var(--md-accent-gold);
}

.case-file-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.case-file-title {
  font-size: 16px;
  font-weight: 700;
  color: var(--md-primary-label);
  margin: 0;
  line-height: 1.2;
}

.case-file-count {
  font-size: 16px;
  color: var(--md-secondary-text);
  margin: 0;
  line-height: 1.2;
}

.case-file-arrow {
  flex-shrink: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.arrow-icon {
  font-size: 20px;
  color: var(--md-secondary-text);
}
