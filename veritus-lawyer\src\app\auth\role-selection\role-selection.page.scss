/* Modern White & Gold Role Selection Design */
.modern-auth-content {
  --background: #FFFFFF;
  background: #FFFFFF;
  overflow: hidden;
}

.auth-wrapper {
  position: relative;
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

// Background Decorative Elements
.background-elements {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  overflow: hidden;
}

.gold-accent-1 {
  position: absolute;
  top: -50px;
  right: -50px;
  width: 200px;
  height: 200px;
  background: linear-gradient(135deg, rgba(196, 154, 86, 0.1) 0%, rgba(196, 154, 86, 0.05) 100%);
  border-radius: 50%;
  filter: blur(40px);
}

.gold-accent-2 {
  position: absolute;
  bottom: -100px;
  left: -100px;
  width: 300px;
  height: 300px;
  background: linear-gradient(45deg, rgba(196, 154, 86, 0.08) 0%, rgba(196, 154, 86, 0.03) 100%);
  border-radius: 50%;
  filter: blur(60px);
}

.gold-accent-3 {
  position: absolute;
  top: 50%;
  left: -150px;
  width: 250px;
  height: 250px;
  background: linear-gradient(90deg, rgba(196, 154, 86, 0.06) 0%, rgba(196, 154, 86, 0.02) 100%);
  border-radius: 50%;
  filter: blur(50px);
}

// Main Container
.auth-main-container {
  position: relative;
  z-index: 10;
  width: 100%;
  max-width: 420px;
  margin: 0 auto;
}

// Logo Section
.logo-section {
  text-align: center;
  margin-bottom: 40px;
}

.logo-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
}

.logo-icon {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, #C49A56 0%, #D4AF6A 100%);
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8px 32px rgba(196, 154, 86, 0.3);
}

.justice-scale-icon {
  font-size: 36px;
  color: #FFFFFF;
}

.brand-title {
  font-size: 32px;
  font-weight: 700;
  color: #C49A56;
  margin: 0;
  letter-spacing: -0.5px;
}

.brand-subtitle {
  font-size: 16px;
  color: #666666;
  margin: 0;
  font-weight: 400;
}

// Form Card
.form-card {
  background: #FFFFFF;
  border-radius: 24px;
  padding: 40px 32px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(196, 154, 86, 0.1);
}

.form-header {
  text-align: left;
  margin-bottom: 32px;
}

.form-title {
  font-size: 24px;
  font-weight: 700;
  color: #000000;
  margin: 0 0 8px 0;
  letter-spacing: -0.3px;
}

.form-subtitle {
  font-size: 16px;
  color: #666666;
  margin: 0;
  font-weight: 400;
}

// Role Selection
.role-selection-section {
  margin-bottom: 32px;
}

.section-label {
  display: block;
  font-size: 16px;
  font-weight: 600;
  color: #000000;
  margin-bottom: 16px;
}

.role-buttons {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.modern-role-btn {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 20px 24px;
  background: #FFFFFF;
  border: 2px solid #E5E5E5;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  font-size: 16px;
  font-weight: 500;
  color: #333333;
  width: 100%;
  text-align: left;

  &:hover:not(.role-active) {
    border-color: #C49A56;
    background: rgba(196, 154, 86, 0.02);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(196, 154, 86, 0.15);
  }

  &:focus {
    outline: none;
    border-color: #C49A56;
  }

  &:focus:not(.role-active) {
    background: rgba(196, 154, 86, 0.02);
  }

  &.role-active {
    border-color: #C49A56;
    background: linear-gradient(135deg, rgba(196, 154, 86, 0.08) 0%, rgba(196, 154, 86, 0.04) 100%);
    color: #C49A56;
    box-shadow: 0 4px 20px rgba(196, 154, 86, 0.2);
  }

  // Ensure clean state when not active
  &:not(.role-active) {
    border-color: #E5E5E5;
    background: #FFFFFF;
    color: #333333;
    box-shadow: none;
    transform: none;
  }
}

.role-icon {
  font-size: 24px;
  color: inherit;
}

.role-text {
  font-size: 16px;
  font-weight: 500;
  color: inherit;
}

// Continue Button
.modern-submit-btn {
  width: 100%;
  height: 56px;
  background: linear-gradient(135deg, #C49A56 0%, #D4AF6A 100%);
  border: none;
  border-radius: 12px;
  color: #FFFFFF;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  margin-bottom: 24px;
  box-shadow: 0 4px 20px rgba(196, 154, 86, 0.3);

  &:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 8px 30px rgba(196, 154, 86, 0.4);
  }

  &:active:not(:disabled) {
    transform: translateY(0);
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
    box-shadow: 0 4px 20px rgba(196, 154, 86, 0.2);
  }
}

.btn-text {
  font-size: 16px;
  font-weight: 600;
}

.btn-icon {
  font-size: 20px;
}

// Sign In Section
.signin-section {
  text-align: center;
  margin-top: 24px;
}

.signin-text {
  font-size: 14px;
  color: #666666;
}

.signin-link {
  background: none;
  border: none;
  color: #C49A56;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  text-decoration: none;
  transition: color 0.3s ease;

  &:hover {
    color: #B8935A;
    text-decoration: underline;
  }
}

// Responsive Design
@media (max-width: 480px) {
  .auth-wrapper {
    padding: 16px;
  }

  .form-card {
    padding: 32px 24px;
  }

  .logo-icon {
    width: 70px;
    height: 70px;
  }

  .justice-scale-icon {
    font-size: 32px;
  }

  .brand-title {
    font-size: 28px;
  }

  .form-title {
    font-size: 22px;
  }
}
