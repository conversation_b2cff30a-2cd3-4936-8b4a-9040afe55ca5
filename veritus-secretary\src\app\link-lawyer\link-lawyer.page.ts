import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { 
  FirebaseService, 
  AssistantCode, 
  LawyerSecretaryLink 
} from '../services/firebase.service';

@Component({
  selector: 'app-link-lawyer',
  templateUrl: './link-lawyer.page.html',
  styleUrls: ['./link-lawyer.page.scss'],
})
export class LinkLawyerPage implements OnInit {
  linkedLawyers: LawyerSecretaryLink[] = [];
  assistantCode = '';
  codePreview: AssistantCode | null = null;
  isLinking = false;
  isLoading = false;

  constructor(
    private router: Router,
    private firebaseService: FirebaseService
  ) { }

  ngOnInit() {
    this.loadLinkedLawyers();
  }

  async loadLinkedLawyers() {
    this.isLoading = true;
    const currentUser = this.firebaseService.getCurrentUser();
    
    if (currentUser) {
      try {
        this.linkedLawyers = await this.firebaseService.getLinkedLawyers(currentUser.uid);
      } catch (error) {
        console.error('Error loading linked lawyers:', error);
        this.showToast('Error loading linked lawyers', 'danger');
      }
    }
    
    this.isLoading = false;
  }

  async refreshData() {
    await this.loadLinkedLawyers();
    this.showToast('Data refreshed', 'success');
  }

  onCodeInput(event: any) {
    const value = event.target.value.toUpperCase();
    this.assistantCode = value;
    
    // Clear preview if code is changed
    if (this.codePreview) {
      this.codePreview = null;
    }
  }

  isValidCode(): boolean {
    return this.assistantCode.length === 8;
  }

  async linkToLawyer() {
    if (!this.isValidCode()) {
      this.showToast('Please enter a valid 8-character code', 'warning');
      return;
    }

    const currentUser = this.firebaseService.getCurrentUser();
    if (!currentUser) {
      this.showToast('Please sign in to link to lawyers', 'warning');
      return;
    }

    this.isLinking = true;

    try {
      // Validate the code first
      const codeData = await this.firebaseService.validateAssistantCode(this.assistantCode);
      
      if (!codeData) {
        this.showToast('Invalid or expired code', 'danger');
        this.isLinking = false;
        return;
      }

      // Check if already linked to this lawyer
      const alreadyLinked = this.linkedLawyers.some(link => 
        link.lawyerId === codeData.lawyerId
      );

      if (alreadyLinked) {
        this.showToast('You are already linked to this lawyer', 'warning');
        this.isLinking = false;
        return;
      }

      // Show preview
      this.codePreview = codeData;
      this.isLinking = false;

    } catch (error) {
      console.error('Error validating code:', error);
      this.showToast('Error validating code', 'danger');
      this.isLinking = false;
    }
  }

  async confirmLink() {
    if (!this.codePreview) return;

    const currentUser = this.firebaseService.getCurrentUser();
    if (!currentUser) {
      this.showToast('Please sign in to link to lawyers', 'warning');
      return;
    }

    this.isLinking = true;

    try {
      const link = await this.firebaseService.useAssistantCode(
        this.assistantCode,
        currentUser.uid
      );

      // Add to local list
      this.linkedLawyers.unshift(link);
      
      // Clear form
      this.assistantCode = '';
      this.codePreview = null;
      
      this.showToast(`Successfully linked to ${link.lawyerName}`, 'success');

      // Show success alert
      await this.showLinkSuccessAlert(link);

    } catch (error) {
      console.error('Error linking to lawyer:', error);
      this.showToast('Failed to link to lawyer', 'danger');
    } finally {
      this.isLinking = false;
    }
  }

  async showLinkSuccessAlert(link: LawyerSecretaryLink) {
    const confirmed = confirm(`Successfully linked to ${link.lawyerName}! Would you like to start working with them now?`);
    
    if (confirmed) {
      this.switchToLawyer(link);
    }
  }

  cancelPreview() {
    this.codePreview = null;
    this.assistantCode = '';
  }

  async switchToLawyer(lawyer: LawyerSecretaryLink) {
    // Store the selected lawyer context
    localStorage.setItem('selectedLawyerContext', JSON.stringify({
      lawyerId: lawyer.lawyerId,
      lawyerName: lawyer.lawyerName,
      permissions: lawyer.permissions
    }));

    // Navigate to lawyer's dashboard or workspace
    this.router.navigate(['/dashboard'], {
      queryParams: { context: 'lawyer', lawyerId: lawyer.lawyerId }
    });
  }

  formatDate(date: any): string {
    return new Date(date).toLocaleDateString();
  }

  async showToast(message: string, color: 'success' | 'warning' | 'danger' = 'success') {
    console.log(`${color.toUpperCase()}: ${message}`);
    // For now, just log. In a full implementation, you'd use ToastController
  }
}
