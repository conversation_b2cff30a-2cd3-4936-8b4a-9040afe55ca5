import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { FirebaseService, SecretaryP<PERSON><PERSON>le, LawyerProfile, SecretaryPermissions } from '../services/firebase.service';

interface DashboardStats {
  linkedLawyers: number;
  totalCases: number;
  totalAppointments: number;
}

interface EnhancedLawyerCard {
  id: string;
  name: string;
  email: string;
  phone?: string;
  avatar?: string;
  rollNumber?: string;
  barId?: string;
  caseCount: number;
  appointmentCount: number;
  lastActivity: string;
  permissions?: SecretaryPermissions;
  status: 'active' | 'inactive';
}

@Component({
  selector: 'app-secretary-dashboard',
  templateUrl: './secretary-dashboard.page.html',
  styleUrls: ['./secretary-dashboard.page.scss'],
})
export class SecretaryDashboardPage implements OnInit {
  secretaryName = 'Secretary';
  secretaryProfile: SecretaryProfile | null = null;
  stats: DashboardStats = {
    linkedLawyers: 0,
    totalCases: 0,
    totalAppointments: 0
  };

  linkedLawyers: EnhancedLawyerCard[] = [];
  recentActivities: any[] = [];
  pendingTasks: any[] = [];

  // Calendar properties
  currentMonth = 'July';
  currentYear = 2025;
  selectedDateString = 'July 1, 2025';
  searchTerm = '';

  dayHeaders = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
  calendarDays = [
    { date: 30, otherMonth: true, isToday: false, hasAppointment: false },
    { date: 1, otherMonth: false, isToday: true, hasAppointment: true },
    { date: 2, otherMonth: false, isToday: false, hasAppointment: false },
    { date: 3, otherMonth: false, isToday: false, hasAppointment: false },
    { date: 4, otherMonth: false, isToday: false, hasAppointment: false },
    { date: 5, otherMonth: false, isToday: false, hasAppointment: false },
    { date: 6, otherMonth: false, isToday: false, hasAppointment: false },
    { date: 7, otherMonth: false, isToday: false, hasAppointment: false },
    { date: 8, otherMonth: false, isToday: false, hasAppointment: false },
    { date: 9, otherMonth: false, isToday: false, hasAppointment: false },
    { date: 10, otherMonth: false, isToday: false, hasAppointment: false },
    { date: 11, otherMonth: false, isToday: false, hasAppointment: false },
    { date: 12, otherMonth: false, isToday: false, hasAppointment: false },
    { date: 13, otherMonth: false, isToday: false, hasAppointment: false },
    { date: 14, otherMonth: false, isToday: false, hasAppointment: false },
    { date: 15, otherMonth: false, isToday: false, hasAppointment: false },
    { date: 16, otherMonth: false, isToday: false, hasAppointment: false },
    { date: 17, otherMonth: false, isToday: false, hasAppointment: false },
    { date: 18, otherMonth: false, isToday: false, hasAppointment: false },
    { date: 19, otherMonth: false, isToday: false, hasAppointment: false },
    { date: 20, otherMonth: false, isToday: false, hasAppointment: false },
    { date: 21, otherMonth: false, isToday: false, hasAppointment: false },
    { date: 22, otherMonth: false, isToday: false, hasAppointment: false },
    { date: 23, otherMonth: false, isToday: false, hasAppointment: false },
    { date: 24, otherMonth: false, isToday: false, hasAppointment: false },
    { date: 25, otherMonth: false, isToday: false, hasAppointment: false },
    { date: 26, otherMonth: false, isToday: false, hasAppointment: false },
    { date: 27, otherMonth: false, isToday: false, hasAppointment: false },
    { date: 28, otherMonth: false, isToday: false, hasAppointment: false },
    { date: 29, otherMonth: false, isToday: false, hasAppointment: false },
    { date: 30, otherMonth: false, isToday: false, hasAppointment: false },
    { date: 31, otherMonth: false, isToday: false, hasAppointment: false },
    { date: 1, otherMonth: true, isToday: false, hasAppointment: false },
    { date: 2, otherMonth: true, isToday: false, hasAppointment: false },
    { date: 3, otherMonth: true, isToday: false, hasAppointment: false }
  ];

  recentDocuments = [
    { name: 'Lopez-Contract.pdf', date: 'Updated today' },
    { name: 'Client-Agreement.pdf', date: '4 days ago' }
  ];

  chartData = [
    { height: 60, color: '#d4af37' },
    { height: 40, color: '#d4af37' },
    { height: 80, color: '#d4af37' },
    { height: 30, color: '#d4af37' },
    { height: 70, color: '#d4af37' },
    { height: 50, color: '#d4af37' },
    { height: 90, color: '#d4af37' },
    { height: 45, color: '#d4af37' },
    { height: 85, color: '#d4af37' }
  ];

  chartLabels = ['1', '2', '3', '4', '5', '6', '7', '8', '9'];

  recentTransactions = [
    { amount: '₱15,000', type: 'Consultation', date: 'June 15, 2025' },
    { amount: '₱5,000', type: 'Retainer', date: 'June 16, 2025' }
  ];

  constructor(
    private router: Router,
    private firebaseService: FirebaseService
  ) { }

  ngOnInit() {
    this.loadSecretaryProfile();
    this.loadDashboardData();
  }

  async loadSecretaryProfile() {
    // Always load mock data for demonstration purposes
    this.secretaryProfile = {
      uid: 'mock-secretary-id',
      email: '<EMAIL>',
      name: 'Sarah Johnson',
      phone: '+****************',
      avatar: '',
      role: 'secretary',
      linkedLawyers: ['lawyer1', 'lawyer2'],
      permissions: {
        canManageCalendar: true,
        canManageFiles: true,
        canManageCases: true,
        canManageRetainers: false,
        canViewFinances: true,
        canManageFinances: false
      },
      createdAt: new Date('2024-01-15'),
      updatedAt: new Date()
    };
    this.secretaryName = this.secretaryProfile.name;

    // Try to load real data if user is authenticated
    const currentUser = this.firebaseService.getCurrentUser();
    if (currentUser) {
      try {
        const realProfile = await this.firebaseService.getSecretaryProfile(currentUser.uid);
        if (realProfile) {
          this.secretaryProfile = realProfile;
          this.secretaryName = realProfile.name;
        }
      } catch (error) {
        console.log('Using mock data for demonstration');
      }
    }
  }

  async loadDashboardData() {
    // Always load mock data for demonstration
    this.linkedLawyers = [
      {
        id: 'lawyer1',
        name: 'Attorney John Smith',
        email: '<EMAIL>',
        phone: '+****************',
        avatar: '',
        rollNumber: 'BAR12345',
        barId: 'NY-67890',
        caseCount: 12,
        appointmentCount: 8,
        lastActivity: '2 hours ago',
        permissions: {
          canManageCalendar: true,
          canManageFiles: true,
          canManageCases: true,
          canManageRetainers: false,
          canViewFinances: true,
          canManageFinances: false
        },
        status: 'active' as const
      },
      {
        id: 'lawyer2',
        name: 'Attorney Maria Rodriguez',
        email: '<EMAIL>',
        phone: '+****************',
        avatar: '',
        rollNumber: 'BAR54321',
        barId: 'CA-12345',
        caseCount: 8,
        appointmentCount: 5,
        lastActivity: '1 day ago',
        permissions: {
          canManageCalendar: true,
          canManageFiles: false,
          canManageCases: true,
          canManageRetainers: true,
          canViewFinances: false,
          canManageFinances: false
        },
        status: 'active' as const
      }
    ];
    this.stats.linkedLawyers = this.linkedLawyers.length;

    // Try to load real data if user is authenticated
    const currentUser = this.firebaseService.getCurrentUser();
    if (currentUser) {
      try {
        // Load linked lawyers with enhanced information
        const lawyers = await this.firebaseService.getSecretaryLinkedLawyers(currentUser.uid);
        const linkedLawyerLinks = await this.firebaseService.getLinkedLawyers(currentUser.uid);

        if (lawyers.length > 0) {
          this.stats.linkedLawyers = lawyers.length;

          this.linkedLawyers = lawyers.map((lawyer) => {
            // Find the link to get permissions
            const link = linkedLawyerLinks.find(l => l.lawyerId === lawyer.uid);

            return {
              id: lawyer.uid,
              name: lawyer.name,
              email: lawyer.email,
              phone: lawyer.phone,
              avatar: lawyer.avatar,
              rollNumber: lawyer.rollNumber,
              barId: lawyer.barId,
              caseCount: 0, // Will be loaded separately
              appointmentCount: 0, // Will be loaded separately
              lastActivity: 'Today',
              permissions: link?.permissions || this.secretaryProfile?.permissions,
              status: 'active' as const
            };
          });
        }

        // Load actual statistics
        try {
          const dashboardStats = await this.firebaseService.getSecretaryDashboardStats(currentUser.uid);
          this.stats = dashboardStats;
        } catch (error) {
          console.error('Error loading dashboard stats:', error);
          // Use mock stats for demonstration
          this.stats = {
            linkedLawyers: this.linkedLawyers.length,
            totalCases: this.linkedLawyers.reduce((sum, lawyer) => sum + lawyer.caseCount, 0),
            totalAppointments: this.linkedLawyers.reduce((sum, lawyer) => sum + lawyer.appointmentCount, 0)
          };
        }

        // Load recent activities (mock for now)
        this.recentActivities = [
          {
            type: 'appointment',
            description: 'Scheduled appointment for John Doe',
            time: '2 hours ago',
            lawyer: lawyers[0]?.name || 'Lawyer'
          },
          {
            type: 'case',
            description: 'Updated case progress for ABC Corp',
            time: '4 hours ago',
            lawyer: lawyers[1]?.name || lawyers[0]?.name || 'Lawyer'
          }
        ];

        // Load pending tasks (mock for now)
        this.pendingTasks = [
          {
            type: 'approval',
            description: 'Client information update pending approval',
            priority: 'high',
            lawyer: lawyers[0]?.name || 'Lawyer'
          },
          {
            type: 'reminder',
            description: 'Send appointment reminder to client',
            priority: 'medium',
            dueTime: '30 minutes'
          }
        ];

      } catch (error) {
        console.error('Error loading dashboard data:', error);
      }
    }
  }

  onViewLawyer(lawyerId: string) {
    this.router.navigate(['/secretary/lawyer', lawyerId]);
  }

  onManageCalendar() {
    this.router.navigate(['/secretary-calendar']);
  }

  onManageFiles() {
    this.router.navigate(['/secretary-files']);
  }

  onManageCases() {
    this.router.navigate(['/secretary-cases']);
  }

  onViewFinances() {
    this.router.navigate(['/finance']);
  }

  onViewAuditLog() {
    this.router.navigate(['/secretary/audit']);
  }

  onLinkNewLawyer() {
    this.router.navigate(['/secretary/link-lawyer']);
  }

  getGreeting(): string {
    const hour = new Date().getHours();
    if (hour < 12) return 'Good morning';
    if (hour < 17) return 'Good afternoon';
    return 'Good evening';
  }

  getPermissionCount(): number {
    if (!this.secretaryProfile) return 0;

    const permissions = this.secretaryProfile.permissions;
    return Object.values(permissions).filter(Boolean).length;
  }

  getActivePermissions(): string {
    if (!this.secretaryProfile) return '0';

    const permissions = this.secretaryProfile.permissions;
    const activeCount = Object.values(permissions).filter(Boolean).length;
    const totalCount = Object.keys(permissions).length;

    return `${activeCount}/${totalCount}`;
  }

  formatDate(date: Date | string): string {
    if (!date) return 'Unknown';

    const dateObj = typeof date === 'string' ? new Date(date) : date;
    const now = new Date();
    const diffTime = Math.abs(now.getTime() - dateObj.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays < 30) {
      return `${diffDays} days ago`;
    } else if (diffDays < 365) {
      const months = Math.floor(diffDays / 30);
      return `${months} month${months > 1 ? 's' : ''} ago`;
    } else {
      const years = Math.floor(diffDays / 365);
      return `${years} year${years > 1 ? 's' : ''} ago`;
    }
  }

  onEditProfile() {
    this.router.navigate(['/secretary/profile']);
  }

  // Calendar methods
  previousMonth() {
    // Implementation for previous month navigation
    console.log('Previous month clicked');
  }

  nextMonth() {
    // Implementation for next month navigation
    console.log('Next month clicked');
  }

  selectDate(day: any) {
    // Implementation for date selection
    this.selectedDateString = `${this.currentMonth} ${day.date}, ${this.currentYear}`;
    console.log('Date selected:', this.selectedDateString);
  }
}
