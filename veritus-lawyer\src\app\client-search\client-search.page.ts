import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';

interface Lawyer {
  id: string;
  name: string;
  firm: string;
  location: string;
  avatar: string;
  bio: string;
  specialties: string[];
  rating: number;
  experience: string;
  isAvailable: boolean;
}

@Component({
  selector: 'app-client-search',
  templateUrl: './client-search.page.html',
  styleUrls: ['./client-search.page.scss'],
  standalone: false,
})
export class ClientSearchPage implements OnInit {
  searchTerm = '';
  lawyers: Lawyer[] = [
    {
      id: '1',
      name: 'Atty. Sarah Johnson',
      firm: 'Johnson & Associates',
      location: 'Manila, Philippines',
      avatar: 'assets/avatars/lawyer1.jpg',
      bio: 'Experienced corporate lawyer specializing in business law and contracts.',
      specialties: ['Corporate Law', 'Business Contracts', 'Mergers & Acquisitions'],
      rating: 4.8,
      experience: '12 years',
      isAvailable: true
    },
    {
      id: '2',
      name: '<PERSON><PERSON>. <PERSON>',
      firm: 'Chen Legal Services',
      location: 'Makati, Philippines',
      avatar: 'assets/avatars/lawyer2.jpg',
      bio: 'Criminal defense attorney with extensive trial experience.',
      specialties: ['Criminal Defense', 'Trial Advocacy', 'Appeals'],
      rating: 4.9,
      experience: '15 years',
      isAvailable: true
    },
    {
      id: '3',
      name: 'Atty. Maria Santos',
      firm: 'Santos Family Law',
      location: 'Quezon City, Philippines',
      avatar: 'assets/avatars/lawyer3.jpg',
      bio: 'Family law specialist focusing on divorce, custody, and adoption cases.',
      specialties: ['Family Law', 'Divorce', 'Child Custody', 'Adoption'],
      rating: 4.7,
      experience: '10 years',
      isAvailable: false
    },
    {
      id: '4',
      name: 'Atty. Robert Kim',
      firm: 'Kim & Partners',
      location: 'BGC, Taguig',
      avatar: 'assets/avatars/lawyer4.jpg',
      bio: 'Real estate and property law expert with commercial focus.',
      specialties: ['Real Estate', 'Property Law', 'Commercial Leasing'],
      rating: 4.6,
      experience: '8 years',
      isAvailable: true
    }
  ];

  filteredLawyers: Lawyer[] = [];

  constructor(private router: Router) { }

  ngOnInit() {
    this.filteredLawyers = [...this.lawyers];
  }

  onSearchChange() {
    if (!this.searchTerm.trim()) {
      this.filteredLawyers = [...this.lawyers];
      return;
    }

    const term = this.searchTerm.toLowerCase();
    this.filteredLawyers = this.lawyers.filter(lawyer => 
      lawyer.name.toLowerCase().includes(term) ||
      lawyer.firm.toLowerCase().includes(term) ||
      lawyer.location.toLowerCase().includes(term) ||
      lawyer.specialties.some(specialty => specialty.toLowerCase().includes(term))
    );
  }

  onViewLawyer(lawyer: Lawyer) {
    this.router.navigate(['/lawyer-detail', lawyer.id]);
  }

  onRequestRetainer(lawyer: Lawyer) {
    this.router.navigate(['/request-retainer', lawyer.id]);
  }

  getStarArray(rating: number): boolean[] {
    const stars = [];
    for (let i = 1; i <= 5; i++) {
      stars.push(i <= Math.floor(rating));
    }
    return stars;
  }
}
