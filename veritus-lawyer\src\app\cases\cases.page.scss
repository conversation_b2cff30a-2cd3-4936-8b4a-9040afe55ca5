.retainer-content {
  --background: #FFFFFF;
  background: #FFFFFF;
}

.retainer-container {
  padding: 16px;
  background: #FFFFFF;
  min-height: calc(100vh - 44px);
}

.status-bar-spacer {
  height: 44px;
  background: #FFFFFF;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32px;
  padding: 8px 4px 0;
  background: #FFFFFF;
}

.page-title {
  font-size: 32px;
  font-weight: 700;
  color: #000000;
  margin: 0;
  letter-spacing: -0.02em;
}

// Tab Navigation
.tab-navigation {
  display: flex;
  justify-content: center;
  gap: 8px;
  margin-bottom: 24px;
  padding: 0 16px;
}

.tab-button {
  background: #F5F5F5;
  border: 1px solid #E0E0E0;
  border-radius: 8px;
  padding: 12px 16px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  align-items: center;
  justify-content: center;
  flex: 1;
  max-width: 140px;
  min-width: 120px;

  &.tab-active {
    background: #C49A56;
    border-color: #C49A56;

    .tab-text {
      color: #FFFFFF;
      font-weight: 600;
    }
  }

  &:hover:not(.tab-active) {
    background: #EEEEEE;
    border-color: #D0D0D0;

    .tab-text {
      color: #757575;
    }
  }
}

.tab-text {
  font-size: 14px;
  font-weight: 500;
  color: #666666;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  letter-spacing: -0.01em;
  text-align: center;
}

// Cases List
.cases-list {
  padding: 0 16px;
  margin-bottom: 32px;
}

.case-card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  margin-bottom: 12px;
  border-radius: 12px;
  border: 1px solid rgba(0, 0, 0, 0.04);
  transition: all 0.2s ease;

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  }
}

ion-label p {
  margin: 0;
  font-size: 14px;
  line-height: 1.4;
}

.case-status-text {
  color: #67EF77 !important;
  font-weight: 600;
}

.case-progress {
  text-align: right;
  padding: 0 16px 16px 16px;
}

.progress-link {
  font-size: 14px;
  color: #C49A56;
  font-weight: 500;
  text-decoration: none;
  transition: color 0.2s ease;

  &:hover {
    color: #B8894A;
  }
}

// Responsive Design
@media (max-width: 480px) {
  .retainer-container {
    padding: 12px;
  }

  .page-header {
    margin-bottom: 20px;
  }

  .page-title {
    font-size: 28px;
  }

  .tab-navigation {
    gap: 6px;
    padding: 0 12px;
  }

  .tab-button {
    padding: 10px 14px;
    max-width: 115px;
    min-width: 95px;
  }

  .tab-text {
    font-size: 13px;
  }

  .cases-list {
    padding: 0 12px;
  }
}
