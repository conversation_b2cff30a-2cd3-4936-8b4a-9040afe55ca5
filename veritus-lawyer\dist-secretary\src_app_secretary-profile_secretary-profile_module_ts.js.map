{"version": 3, "file": "src_app_secretary-profile_secretary-profile_module_ts.js", "mappings": ";;;;;;;;;;;;;;;;AACuD;AAES;;;AAEhE,MAAME,MAAM,GAAW,CACrB;EACEC,IAAI,EAAE,EAAE;EACRC,SAAS,EAAEH,yEAAoBA;CAChC,CACF;AAMK,MAAOI,iCAAiC;;;uBAAjCA,iCAAiC;IAAA;EAAA;;;YAAjCA;IAAiC;EAAA;;;gBAHlCL,yDAAY,CAACM,QAAQ,CAACJ,MAAM,CAAC,EAC7BF,yDAAY;IAAA;EAAA;;;sHAEXK,iCAAiC;IAAAE,OAAA,GAAAC,yDAAA;IAAAC,OAAA,GAFlCT,yDAAY;EAAA;AAAA,K;;;;;;;;;;;;;;;;;;;;ACbuB;AACF;AACA;AAE0C;AACvB;;AAW1D,MAAOa,0BAA0B;;;uBAA1BA,0BAA0B;IAAA;EAAA;;;YAA1BA;IAA0B;EAAA;;;gBAPnCH,yDAAY,EACZC,uDAAW,EACXC,uDAAW,EACXP,gGAAiC;IAAA;EAAA;;;sHAIxBQ,0BAA0B;IAAAC,YAAA,GAFtBb,yEAAoB;IAAAM,OAAA,GALjCG,yDAAY,EACZC,uDAAW,EACXC,uDAAW,EACXP,gGAAiC;EAAA;AAAA,K;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IEA3BU,uDAAA,cAA0F;;;;IAA5DA,wDAAA,QAAAG,MAAA,CAAAC,SAAA,CAAAC,MAAA,EAAAL,2DAAA,CAAwB;;;;;IACtDA,uDAAA,mBAAiF;;;;;IAFnFA,4DAFJ,cAA8C,cAChB,cACC;IAEzBA,wDADA,IAAAS,yCAAA,kBAA0F,IAAAC,8CAAA,uBACpB;IACxEV,0DAAA,EAAM;IACNA,4DAAA,aAA8E;IAC5EA,oDAAA,GACF;IAAAA,0DAAA,EAAK;IACLA,4DAAA,YAA2D;IAAAA,oDAAA,GAAqB;IAEpFA,0DAFoF,EAAI,EAChF,EACF;;;;IARMA,uDAAA,GAAsB;IAAtBA,wDAAA,SAAAG,MAAA,CAAAC,SAAA,CAAAC,MAAA,CAAsB;IACjBL,uDAAA,EAAuB;IAAvBA,wDAAA,UAAAG,MAAA,CAAAC,SAAA,CAAAC,MAAA,CAAuB;IAGlCL,uDAAA,GACF;IADEA,gEAAA,MAAAG,MAAA,CAAAC,SAAA,CAAAW,IAAA,MACF;IAC2Df,uDAAA,GAAqB;IAArBA,+DAAA,CAAAG,MAAA,CAAAC,SAAA,CAAAa,KAAA,CAAqB;;;;;IAOhFjB,4DAFJ,cAA6C,cACpB,cACyD;IAC5EA,oDAAA,GACF;IAAAA,0DAAA,EAAM;IACNA,4DAAA,cAA0D;IAAAA,oDAAA,qBAAc;IAC1EA,0DAD0E,EAAM,EAC1E;IAGJA,4DADF,cAAuB,cACyD;IAC5EA,oDAAA,GACF;IAAAA,0DAAA,EAAM;IACNA,4DAAA,cAA0D;IAAAA,oDAAA,mBAAW;IACvEA,0DADuE,EAAM,EACvE;IAGJA,4DADF,eAAuB,eACyD;IAC5EA,oDAAA,gBACF;IAAAA,0DAAA,EAAM;IACNA,4DAAA,eAA0D;IAAAA,oDAAA,cAAM;IAEpEA,0DAFoE,EAAM,EAClE,EACF;;;;IAlBAA,uDAAA,GACF;IADEA,gEAAA,MAAAG,MAAA,CAAAe,qBAAA,QACF;IAMElB,uDAAA,GACF;IADEA,gEAAA,MAAAG,MAAA,CAAAgB,kBAAA,QACF;;;;;;IAoBEnB,4DAFJ,cAA0C,cACjB,gBACuC;IAAAA,oDAAA,gBAAS;IAAAA,0DAAA,EAAQ;IAC7EA,4DAAA,YAA2D;IAAAA,oDAAA,GAAoB;IACjFA,0DADiF,EAAI,EAC/E;IAGJA,4DADF,cAAuB,gBACuC;IAAAA,oDAAA,YAAK;IAAAA,0DAAA,EAAQ;IACzEA,4DAAA,YAA2D;IAAAA,oDAAA,IAAqB;IAClFA,0DADkF,EAAI,EAChF;IAGJA,4DADF,eAAuB,iBACuC;IAAAA,oDAAA,aAAK;IAAAA,0DAAA,EAAQ;IACzEA,4DAAA,aAA2D;IAAAA,oDAAA,IAAuC;IACpGA,0DADoG,EAAI,EAClG;IAGJA,4DADF,eAAuB,iBACuC;IAAAA,oDAAA,YAAI;IAAAA,0DAAA,EAAQ;IACxEA,4DAAA,aAA2D;IAAAA,oDAAA,iBAAS;IACtEA,0DADsE,EAAI,EACpE;IAENA,4DAAA,kBAAyE;IAA1BA,wDAAA,mBAAAqB,mEAAA;MAAArB,2DAAA,CAAAuB,GAAA;MAAA,MAAApB,MAAA,GAAAH,2DAAA;MAAA,OAAAA,yDAAA,CAASG,MAAA,CAAAuB,aAAA,EAAe;IAAA,EAAC;IACtE1B,uDAAA,oBAA4D;IAC5DA,oDAAA,sBACF;IACFA,0DADE,EAAS,EACL;;;;IAtByDA,uDAAA,GAAoB;IAApBA,+DAAA,CAAAG,MAAA,CAAAC,SAAA,CAAAW,IAAA,CAAoB;IAKpBf,uDAAA,GAAqB;IAArBA,+DAAA,CAAAG,MAAA,CAAAC,SAAA,CAAAa,KAAA,CAAqB;IAKrBjB,uDAAA,GAAuC;IAAvCA,+DAAA,CAAAG,MAAA,CAAAC,SAAA,CAAAuB,KAAA,mBAAuC;;;;;;IAiBlG3B,4DAFJ,cAAyC,cAChB,oBAC4C;IAAAA,oDAAA,gBAAS;IAAAA,0DAAA,EAAY;IACtFA,4DAAA,oBAGqB;IAFnBA,8DAAA,2BAAA6B,6EAAAC,MAAA;MAAA9B,2DAAA,CAAA+B,GAAA;MAAA,MAAA5B,MAAA,GAAAH,2DAAA;MAAAA,gEAAA,CAAAG,MAAA,CAAA8B,QAAA,CAAAlB,IAAA,EAAAe,MAAA,MAAA3B,MAAA,CAAA8B,QAAA,CAAAlB,IAAA,GAAAe,MAAA;MAAA,OAAA9B,yDAAA,CAAA8B,MAAA;IAAA,EAA2B;IAI/B9B,0DADE,EAAY,EACR;IAGJA,4DADF,cAAuB,oBAC4C;IAAAA,oDAAA,YAAK;IAAAA,0DAAA,EAAY;IAClFA,4DAAA,oBAGqB;IAFnBA,8DAAA,2BAAAkC,6EAAAJ,MAAA;MAAA9B,2DAAA,CAAA+B,GAAA;MAAA,MAAA5B,MAAA,GAAAH,2DAAA;MAAAA,gEAAA,CAAAG,MAAA,CAAA8B,QAAA,CAAAN,KAAA,EAAAG,MAAA,MAAA3B,MAAA,CAAA8B,QAAA,CAAAN,KAAA,GAAAG,MAAA;MAAA,OAAA9B,yDAAA,CAAA8B,MAAA;IAAA,EAA4B;IAIhC9B,0DADE,EAAY,EACR;IAGJA,4DADF,cAAuB,qBAC4C;IAAAA,oDAAA,kBAAU;IAAAA,0DAAA,EAAY;IACvFA,4DAAA,qBAGqB;IAFnBA,8DAAA,2BAAAmC,8EAAAL,MAAA;MAAA9B,2DAAA,CAAA+B,GAAA;MAAA,MAAA5B,MAAA,GAAAH,2DAAA;MAAAA,gEAAA,CAAAG,MAAA,CAAA8B,QAAA,CAAA5B,MAAA,EAAAyB,MAAA,MAAA3B,MAAA,CAAA8B,QAAA,CAAA5B,MAAA,GAAAyB,MAAA;MAAA,OAAA9B,yDAAA,CAAA8B,MAAA;IAAA,EAA6B;IAIjC9B,0DADE,EAAY,EACR;IAGJA,4DADF,eAA0B,kBAC+C;IAA1BA,wDAAA,mBAAAoC,mEAAA;MAAApC,2DAAA,CAAA+B,GAAA;MAAA,MAAA5B,MAAA,GAAAH,2DAAA;MAAA,OAAAA,yDAAA,CAASG,MAAA,CAAAkC,aAAA,EAAe;IAAA,EAAC;IACpErC,uDAAA,oBAAuD;IACvDA,oDAAA,sBACF;IAAAA,0DAAA,EAAS;IACTA,4DAAA,kBAA0E;IAAzBA,wDAAA,mBAAAsC,mEAAA;MAAAtC,2DAAA,CAAA+B,GAAA;MAAA,MAAA5B,MAAA,GAAAH,2DAAA;MAAA,OAAAA,yDAAA,CAASG,MAAA,CAAAoC,YAAA,EAAc;IAAA,EAAC;IACvEvC,uDAAA,oBAAmD;IACnDA,oDAAA,gBACF;IAEJA,0DAFI,EAAS,EACL,EACF;;;;IAlCAA,uDAAA,GAA2B;IAA3BA,8DAAA,YAAAG,MAAA,CAAA8B,QAAA,CAAAlB,IAAA,CAA2B;IAS3Bf,uDAAA,GAA4B;IAA5BA,8DAAA,YAAAG,MAAA,CAAA8B,QAAA,CAAAN,KAAA,CAA4B;IAS5B3B,uDAAA,GAA6B;IAA7BA,8DAAA,YAAAG,MAAA,CAAA8B,QAAA,CAAA5B,MAAA,CAA6B;;;;;IAtDnCL,4DADF,cAA4C,YACyC;IACjFA,oDAAA,4BACF;IAAAA,0DAAA,EAAK;IA8BLA,wDA5BA,IAAAyC,yCAAA,mBAA0C,IAAAC,yCAAA,mBA4BD;IAuC3C1C,0DAAA,EAAM;;;;IAnEoBA,uDAAA,GAAgB;IAAhBA,wDAAA,UAAAG,MAAA,CAAAwC,SAAA,CAAgB;IA4BhB3C,uDAAA,EAAe;IAAfA,wDAAA,SAAAG,MAAA,CAAAwC,SAAA,CAAe;;;ADxEvC,MAAOzD,oBAAoB;EAS/B0D,YACUC,MAAc,EACdC,eAAgC;IADhC,KAAAD,MAAM,GAANA,MAAM;IACN,KAAAC,eAAe,GAAfA,eAAe;IAVzB,KAAA1C,SAAS,GAA4B,IAAI;IACzC,KAAAuC,SAAS,GAAG,KAAK;IACjB,KAAAV,QAAQ,GAAG;MACTlB,IAAI,EAAE,EAAE;MACRY,KAAK,EAAE,EAAE;MACTtB,MAAM,EAAE;KACT;EAKG;EAEJ0C,QAAQA,CAAA;IACN,IAAI,CAACC,oBAAoB,EAAE;EAC7B;EAEMA,oBAAoBA,CAAA;IAAA,IAAAC,KAAA;IAAA,OAAAC,6KAAA;MACxB,MAAMC,WAAW,GAAGF,KAAI,CAACH,eAAe,CAACM,cAAc,EAAE;MACzD,IAAID,WAAW,EAAE;QACfF,KAAI,CAAC7C,SAAS,SAAS6C,KAAI,CAACH,eAAe,CAACO,mBAAmB,CAACF,WAAW,CAACG,GAAG,CAAC;QAChF,IAAIL,KAAI,CAAC7C,SAAS,EAAE;UAClB6C,KAAI,CAAChB,QAAQ,GAAG;YACdlB,IAAI,EAAEkC,KAAI,CAAC7C,SAAS,CAACW,IAAI;YACzBY,KAAK,EAAEsB,KAAI,CAAC7C,SAAS,CAACuB,KAAK,IAAI,EAAE;YACjCtB,MAAM,EAAE4C,KAAI,CAAC7C,SAAS,CAACC,MAAM,IAAI;WAClC;;;IAEJ;EACH;EAEAqB,aAAaA,CAAA;IACX,IAAI,CAACiB,SAAS,GAAG,IAAI;EACvB;EAEAJ,YAAYA,CAAA;IACV,IAAI,CAACI,SAAS,GAAG,KAAK;IACtB,IAAI,IAAI,CAACvC,SAAS,EAAE;MAClB,IAAI,CAAC6B,QAAQ,GAAG;QACdlB,IAAI,EAAE,IAAI,CAACX,SAAS,CAACW,IAAI;QACzBY,KAAK,EAAE,IAAI,CAACvB,SAAS,CAACuB,KAAK,IAAI,EAAE;QACjCtB,MAAM,EAAE,IAAI,CAACD,SAAS,CAACC,MAAM,IAAI;OAClC;;EAEL;EAEMgC,aAAaA,CAAA;IAAA,IAAAkB,MAAA;IAAA,OAAAL,6KAAA;MACjB,IAAI,CAACK,MAAI,CAACnD,SAAS,EAAE;MAErB,IAAI;QACF,MAAMmD,MAAI,CAACT,eAAe,CAACU,sBAAsB,CAACD,MAAI,CAACnD,SAAS,CAACkD,GAAG,EAAE;UACpEvC,IAAI,EAAEwC,MAAI,CAACtB,QAAQ,CAAClB,IAAI;UACxBY,KAAK,EAAE4B,MAAI,CAACtB,QAAQ,CAACN,KAAK;UAC1BtB,MAAM,EAAEkD,MAAI,CAACtB,QAAQ,CAAC5B;SACvB,CAAC;QAEF;QACAkD,MAAI,CAACnD,SAAS,GAAG;UACf,GAAGmD,MAAI,CAACnD,SAAS;UACjBW,IAAI,EAAEwC,MAAI,CAACtB,QAAQ,CAAClB,IAAI;UACxBY,KAAK,EAAE4B,MAAI,CAACtB,QAAQ,CAACN,KAAK;UAC1BtB,MAAM,EAAEkD,MAAI,CAACtB,QAAQ,CAAC5B;SACvB;QAEDkD,MAAI,CAACZ,SAAS,GAAG,KAAK;QACtBc,KAAK,CAAC,+BAA+B,CAAC;OACvC,CAAC,OAAOC,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;QAC/CD,KAAK,CAAC,2CAA2C,CAAC;;IACnD;EACH;EAEMG,QAAQA,CAAA;IAAA,IAAAC,MAAA;IAAA,OAAAX,6KAAA;MACZ,MAAMY,SAAS,GAAGC,OAAO,CAAC,kCAAkC,CAAC;MAC7D,IAAID,SAAS,EAAE;QACb,IAAI;UACF,MAAMD,MAAI,CAACf,eAAe,CAACkB,OAAO,EAAE;UACpCH,MAAI,CAAChB,MAAM,CAACoB,QAAQ,CAAC,CAAC,cAAc,CAAC,CAAC;SACvC,CAAC,OAAOP,KAAK,EAAE;UACdC,OAAO,CAACD,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;UAC1CD,KAAK,CAAC,sCAAsC,CAAC;;;IAEhD;EACH;EAEAS,qBAAqBA,CAAA;IACnB,IAAI,CAACrB,MAAM,CAACoB,QAAQ,CAAC,CAAC,cAAc,CAAC,CAAC;EACxC;EAEAE,cAAcA,CAAA;IACZ,IAAI,CAACtB,MAAM,CAACoB,QAAQ,CAAC,CAAC,YAAY,CAAC,CAAC;EACtC;EAEA9C,kBAAkBA,CAAA;IAChB,IAAI,CAAC,IAAI,CAACf,SAAS,EAAE,OAAO,CAAC;IAE7B,MAAMgE,WAAW,GAAG,IAAI,CAAChE,SAAS,CAACgE,WAAW;IAC9C,OAAOC,MAAM,CAACC,MAAM,CAACF,WAAW,CAAC,CAACG,MAAM,CAACC,OAAO,CAAC,CAACC,MAAM;EAC1D;EAEAvD,qBAAqBA,CAAA;IACnB,OAAO,IAAI,CAACd,SAAS,EAAEsE,aAAa,EAAED,MAAM,IAAI,CAAC;EACnD;;;uBAvGWvF,oBAAoB,EAAAc,+DAAA,CAAAP,mDAAA,GAAAO,+DAAA,CAAA6E,uEAAA;IAAA;EAAA;;;YAApB3F,oBAAoB;MAAA6F,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,8BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCP7BrF,4DAFJ,iBAAY,qBAC2B,mBACyB;UAAAA,oDAAA,cAAO;UAEvEA,0DAFuE,EAAY,EACnE,EACH;UAGXA,4DADF,qBAAyD,aACF;UAyCnDA,wDAtCA,IAAAuF,mCAAA,iBAA8C,IAAAC,mCAAA,kBAcD,IAAAC,mCAAA,iBAwBD;UA4E1CzF,4DADF,aAA6B,aACwD;UACjFA,oDAAA,uBACF;UAAAA,0DAAA,EAAK;UAGHA,4DADF,cAA0B,kBACsC;UAAlCA,wDAAA,mBAAA0F,uDAAA;YAAA,OAASJ,GAAA,CAAApB,qBAAA,EAAuB;UAAA,EAAC;UAC3DlE,uDAAA,oBAAuD;UAErDA,4DADF,eAA4B,cAC0D;UAClFA,oDAAA,wBACF;UAAAA,0DAAA,EAAK;UACLA,4DAAA,aAAgE;UAC9DA,oDAAA,wCACF;UACFA,0DADE,EAAI,EACA;UACNA,uDAAA,oBAAiE;UACnEA,0DAAA,EAAS;UAETA,4DAAA,kBAAuD;UAA3BA,wDAAA,mBAAA2F,uDAAA;YAAA,OAASL,GAAA,CAAAnB,cAAA,EAAgB;UAAA,EAAC;UACpDnE,uDAAA,oBAAqD;UAEnDA,4DADF,eAA4B,cAC0D;UAClFA,oDAAA,sBACF;UAAAA,0DAAA,EAAK;UACLA,4DAAA,aAAgE;UAC9DA,oDAAA,oCACF;UACFA,0DADE,EAAI,EACA;UACNA,uDAAA,oBAAiE;UAGvEA,0DAFI,EAAS,EACL,EACF;UAIJA,4DADF,eAA4B,kBACsB;UAArBA,wDAAA,mBAAA4F,uDAAA;YAAA,OAASN,GAAA,CAAA1B,QAAA,EAAU;UAAA,EAAC;UAC7C5D,uDAAA,oBAAgE;UAChEA,4DAAA,gBAA4C;UAAAA,oDAAA,cAAM;UAK1DA,0DAL0D,EAAO,EAClD,EACL,EAEF,EACM;;;UA5JmBA,uDAAA,GAAe;UAAfA,wDAAA,SAAAsF,GAAA,CAAAlF,SAAA,CAAe;UAchBJ,uDAAA,EAAe;UAAfA,wDAAA,SAAAsF,GAAA,CAAAlF,SAAA,CAAe;UAwBhBJ,uDAAA,EAAe;UAAfA,wDAAA,SAAAsF,GAAA,CAAAlF,SAAA,CAAe", "sources": ["./src/app/secretary-profile/secretary-profile-routing.module.ts", "./src/app/secretary-profile/secretary-profile.module.ts", "./src/app/secretary-profile/secretary-profile.page.ts", "./src/app/secretary-profile/secretary-profile.page.html"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { Routes, RouterModule } from '@angular/router';\r\n\r\nimport { SecretaryProfilePage } from './secretary-profile.page';\r\n\r\nconst routes: Routes = [\r\n  {\r\n    path: '',\r\n    component: SecretaryProfilePage\r\n  }\r\n];\r\n\r\n@NgModule({\r\n  imports: [RouterModule.forChild(routes)],\r\n  exports: [RouterModule],\r\n})\r\nexport class SecretaryProfilePageRoutingModule {}\r\n", "import { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { FormsModule } from '@angular/forms';\r\nimport { IonicModule } from '@ionic/angular';\r\n\r\nimport { SecretaryProfilePageRoutingModule } from './secretary-profile-routing.module';\r\nimport { SecretaryProfilePage } from './secretary-profile.page';\r\n\r\n@NgModule({\r\n  imports: [\r\n    CommonModule,\r\n    FormsModule,\r\n    IonicModule,\r\n    SecretaryProfilePageRoutingModule\r\n  ],\r\n  declarations: [SecretaryProfilePage]\r\n})\r\nexport class SecretaryProfilePageModule {}\r\n", "import { Component, OnInit } from '@angular/core';\r\nimport { Router } from '@angular/router';\r\nimport { FirebaseService, SecretaryProfile } from '../services/firebase.service';\r\n\r\n@Component({\r\n  selector: 'app-secretary-profile',\r\n  templateUrl: './secretary-profile.page.html',\r\n  styleUrls: ['./secretary-profile.page.scss'],\r\n})\r\nexport class SecretaryProfilePage implements OnInit {\r\n  secretary: SecretaryPro<PERSON>le | null = null;\r\n  isEditing = false;\r\n  editForm = {\r\n    name: '',\r\n    phone: '',\r\n    avatar: ''\r\n  };\r\n\r\n  constructor(\r\n    private router: Router,\r\n    private firebaseService: FirebaseService\r\n  ) { }\r\n\r\n  ngOnInit() {\r\n    this.loadSecretaryProfile();\r\n  }\r\n\r\n  async loadSecretaryProfile() {\r\n    const currentUser = this.firebaseService.getCurrentUser();\r\n    if (currentUser) {\r\n      this.secretary = await this.firebaseService.getSecretaryProfile(currentUser.uid);\r\n      if (this.secretary) {\r\n        this.editForm = {\r\n          name: this.secretary.name,\r\n          phone: this.secretary.phone || '',\r\n          avatar: this.secretary.avatar || ''\r\n        };\r\n      }\r\n    }\r\n  }\r\n\r\n  onEditProfile() {\r\n    this.isEditing = true;\r\n  }\r\n\r\n  onCancelEdit() {\r\n    this.isEditing = false;\r\n    if (this.secretary) {\r\n      this.editForm = {\r\n        name: this.secretary.name,\r\n        phone: this.secretary.phone || '',\r\n        avatar: this.secretary.avatar || ''\r\n      };\r\n    }\r\n  }\r\n\r\n  async onSaveProfile() {\r\n    if (!this.secretary) return;\r\n\r\n    try {\r\n      await this.firebaseService.updateSecretaryProfile(this.secretary.uid, {\r\n        name: this.editForm.name,\r\n        phone: this.editForm.phone,\r\n        avatar: this.editForm.avatar\r\n      });\r\n\r\n      // Update local secretary object\r\n      this.secretary = {\r\n        ...this.secretary,\r\n        name: this.editForm.name,\r\n        phone: this.editForm.phone,\r\n        avatar: this.editForm.avatar\r\n      };\r\n\r\n      this.isEditing = false;\r\n      alert('Profile updated successfully!');\r\n    } catch (error) {\r\n      console.error('Error updating profile:', error);\r\n      alert('Error updating profile. Please try again.');\r\n    }\r\n  }\r\n\r\n  async onLogout() {\r\n    const confirmed = confirm('Are you sure you want to logout?');\r\n    if (confirmed) {\r\n      try {\r\n        await this.firebaseService.signOut();\r\n        this.router.navigate(['/auth/signin']);\r\n      } catch (error) {\r\n        console.error('Error signing out:', error);\r\n        alert('Error signing out. Please try again.');\r\n      }\r\n    }\r\n  }\r\n\r\n  onManageLinkedLawyers() {\r\n    this.router.navigate(['/link-lawyer']);\r\n  }\r\n\r\n  onViewAuditLog() {\r\n    this.router.navigate(['/audit-log']);\r\n  }\r\n\r\n  getPermissionCount(): number {\r\n    if (!this.secretary) return 0;\r\n    \r\n    const permissions = this.secretary.permissions;\r\n    return Object.values(permissions).filter(Boolean).length;\r\n  }\r\n\r\n  getLinkedLawyersCount(): number {\r\n    return this.secretary?.linkedLawyers?.length || 0;\r\n  }\r\n}\r\n", "<ion-header>\r\n  <ion-toolbar class=\"veritus-toolbar\">\r\n    <ion-title class=\"veritus-text-white veritus-font-semibold\">Profile</ion-title>\r\n  </ion-toolbar>\r\n</ion-header>\r\n\r\n<ion-content class=\"profile-content veritus-gradient-bg\">\r\n  <div class=\"profile-container veritus-safe-area-top\">\r\n    \r\n    <!-- Profile Header -->\r\n    <div class=\"profile-header\" *ngIf=\"secretary\">\r\n      <div class=\"avatar-section\">\r\n        <div class=\"avatar-circle\">\r\n          <img *ngIf=\"secretary.avatar\" [src]=\"secretary.avatar\" alt=\"Profile\" class=\"avatar-image\">\r\n          <ion-icon *ngIf=\"!secretary.avatar\" name=\"person\" class=\"avatar-icon\"></ion-icon>\r\n        </div>\r\n        <h2 class=\"profile-name veritus-text-xl veritus-font-bold veritus-text-white\">\r\n          {{ secretary.name }}\r\n        </h2>\r\n        <p class=\"profile-email veritus-text-sm veritus-text-gray\">{{ secretary.email }}</p>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Profile Stats -->\r\n    <div class=\"stats-section\" *ngIf=\"secretary\">\r\n      <div class=\"stat-card\">\r\n        <div class=\"stat-number veritus-text-lg veritus-font-bold veritus-text-white\">\r\n          {{ getLinkedLawyersCount() }}\r\n        </div>\r\n        <div class=\"stat-label veritus-text-xs veritus-text-gray\">Linked Lawyers</div>\r\n      </div>\r\n      \r\n      <div class=\"stat-card\">\r\n        <div class=\"stat-number veritus-text-lg veritus-font-bold veritus-text-white\">\r\n          {{ getPermissionCount() }}\r\n        </div>\r\n        <div class=\"stat-label veritus-text-xs veritus-text-gray\">Permissions</div>\r\n      </div>\r\n      \r\n      <div class=\"stat-card\">\r\n        <div class=\"stat-number veritus-text-lg veritus-font-bold veritus-text-white\">\r\n          Active\r\n        </div>\r\n        <div class=\"stat-label veritus-text-xs veritus-text-gray\">Status</div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Profile Information -->\r\n    <div class=\"info-section\" *ngIf=\"secretary\">\r\n      <h3 class=\"section-title veritus-text-lg veritus-font-semibold veritus-text-white\">\r\n        Profile Information\r\n      </h3>\r\n      \r\n      <div class=\"info-card\" *ngIf=\"!isEditing\">\r\n        <div class=\"info-item\">\r\n          <label class=\"info-label veritus-text-sm veritus-text-gray\">Full Name</label>\r\n          <p class=\"info-value veritus-text-base veritus-text-white\">{{ secretary.name }}</p>\r\n        </div>\r\n        \r\n        <div class=\"info-item\">\r\n          <label class=\"info-label veritus-text-sm veritus-text-gray\">Email</label>\r\n          <p class=\"info-value veritus-text-base veritus-text-white\">{{ secretary.email }}</p>\r\n        </div>\r\n        \r\n        <div class=\"info-item\">\r\n          <label class=\"info-label veritus-text-sm veritus-text-gray\">Phone</label>\r\n          <p class=\"info-value veritus-text-base veritus-text-white\">{{ secretary.phone || 'Not provided' }}</p>\r\n        </div>\r\n        \r\n        <div class=\"info-item\">\r\n          <label class=\"info-label veritus-text-sm veritus-text-gray\">Role</label>\r\n          <p class=\"info-value veritus-text-base veritus-text-white\">Secretary</p>\r\n        </div>\r\n        \r\n        <button class=\"veritus-btn-secondary edit-btn\" (click)=\"onEditProfile()\">\r\n          <ion-icon name=\"create-outline\" class=\"btn-icon\"></ion-icon>\r\n          Edit Profile\r\n        </button>\r\n      </div>\r\n      \r\n      <!-- Edit Form -->\r\n      <div class=\"edit-form\" *ngIf=\"isEditing\">\r\n        <div class=\"form-item\">\r\n          <ion-label class=\"form-label veritus-text-sm veritus-text-white\">Full Name</ion-label>\r\n          <ion-input\r\n            [(ngModel)]=\"editForm.name\"\r\n            placeholder=\"Enter your full name\"\r\n            class=\"form-input\">\r\n          </ion-input>\r\n        </div>\r\n        \r\n        <div class=\"form-item\">\r\n          <ion-label class=\"form-label veritus-text-sm veritus-text-white\">Phone</ion-label>\r\n          <ion-input\r\n            [(ngModel)]=\"editForm.phone\"\r\n            placeholder=\"Enter your phone number\"\r\n            class=\"form-input\">\r\n          </ion-input>\r\n        </div>\r\n        \r\n        <div class=\"form-item\">\r\n          <ion-label class=\"form-label veritus-text-sm veritus-text-white\">Avatar URL</ion-label>\r\n          <ion-input\r\n            [(ngModel)]=\"editForm.avatar\"\r\n            placeholder=\"Enter avatar image URL\"\r\n            class=\"form-input\">\r\n          </ion-input>\r\n        </div>\r\n        \r\n        <div class=\"form-actions\">\r\n          <button class=\"veritus-btn-primary save-btn\" (click)=\"onSaveProfile()\">\r\n            <ion-icon name=\"checkmark\" class=\"btn-icon\"></ion-icon>\r\n            Save Changes\r\n          </button>\r\n          <button class=\"veritus-btn-secondary cancel-btn\" (click)=\"onCancelEdit()\">\r\n            <ion-icon name=\"close\" class=\"btn-icon\"></ion-icon>\r\n            Cancel\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Quick Actions -->\r\n    <div class=\"actions-section\">\r\n      <h3 class=\"section-title veritus-text-lg veritus-font-semibold veritus-text-white\">\r\n        Quick Actions\r\n      </h3>\r\n      \r\n      <div class=\"action-cards\">\r\n        <button class=\"action-card\" (click)=\"onManageLinkedLawyers()\">\r\n          <ion-icon name=\"people\" class=\"action-icon\"></ion-icon>\r\n          <div class=\"action-content\">\r\n            <h4 class=\"action-title veritus-text-base veritus-font-semibold veritus-text-white\">\r\n              Manage Lawyers\r\n            </h4>\r\n            <p class=\"action-description veritus-text-sm veritus-text-gray\">\r\n              View and manage linked lawyers\r\n            </p>\r\n          </div>\r\n          <ion-icon name=\"chevron-forward\" class=\"chevron-icon\"></ion-icon>\r\n        </button>\r\n        \r\n        <button class=\"action-card\" (click)=\"onViewAuditLog()\">\r\n          <ion-icon name=\"list\" class=\"action-icon\"></ion-icon>\r\n          <div class=\"action-content\">\r\n            <h4 class=\"action-title veritus-text-base veritus-font-semibold veritus-text-white\">\r\n              Activity Log\r\n            </h4>\r\n            <p class=\"action-description veritus-text-sm veritus-text-gray\">\r\n              View your activity history\r\n            </p>\r\n          </div>\r\n          <ion-icon name=\"chevron-forward\" class=\"chevron-icon\"></ion-icon>\r\n        </button>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Logout Section -->\r\n    <div class=\"logout-section\">\r\n      <button class=\"logout-btn\" (click)=\"onLogout()\">\r\n        <ion-icon name=\"log-out-outline\" class=\"logout-icon\"></ion-icon>\r\n        <span class=\"logout-text veritus-text-base\">Logout</span>\r\n      </button>\r\n    </div>\r\n\r\n  </div>\r\n</ion-content>\r\n"], "names": ["RouterModule", "SecretaryProfilePage", "routes", "path", "component", "SecretaryProfilePageRoutingModule", "<PERSON><PERSON><PERSON><PERSON>", "imports", "i1", "exports", "CommonModule", "FormsModule", "IonicModule", "SecretaryProfilePageModule", "declarations", "i0", "ɵɵelement", "ɵɵproperty", "ctx_r0", "secretary", "avatar", "ɵɵsanitizeUrl", "ɵɵelementStart", "ɵɵtemplate", "SecretaryProfilePage_div_6_img_3_Template", "SecretaryProfilePage_div_6_ion_icon_4_Template", "ɵɵelementEnd", "ɵɵtext", "ɵɵadvance", "ɵɵtextInterpolate1", "name", "ɵɵtextInterpolate", "email", "getLinkedLawyersCount", "getPermissionCount", "ɵɵlistener", "SecretaryProfilePage_div_8_div_3_Template_button_click_21_listener", "ɵɵrestoreView", "_r2", "ɵɵnextContext", "ɵɵresetView", "onEditProfile", "phone", "ɵɵtwoWayListener", "SecretaryProfilePage_div_8_div_4_Template_ion_input_ngModelChange_4_listener", "$event", "_r3", "ɵɵtwoWayBindingSet", "editForm", "SecretaryProfilePage_div_8_div_4_Template_ion_input_ngModelChange_8_listener", "SecretaryProfilePage_div_8_div_4_Template_ion_input_ngModelChange_12_listener", "SecretaryProfilePage_div_8_div_4_Template_button_click_14_listener", "onSaveProfile", "SecretaryProfilePage_div_8_div_4_Template_button_click_17_listener", "onCancelEdit", "ɵɵtwoWayProperty", "SecretaryProfilePage_div_8_div_3_Template", "SecretaryProfilePage_div_8_div_4_Template", "isEditing", "constructor", "router", "firebaseService", "ngOnInit", "loadSecretaryProfile", "_this", "_asyncToGenerator", "currentUser", "getCurrentUser", "getSecretaryProfile", "uid", "_this2", "updateSecretaryProfile", "alert", "error", "console", "onLogout", "_this3", "confirmed", "confirm", "signOut", "navigate", "onManageLinkedLawyers", "onViewAuditLog", "permissions", "Object", "values", "filter", "Boolean", "length", "linkedLawyers", "ɵɵdirectiveInject", "Router", "i2", "FirebaseService", "selectors", "decls", "vars", "consts", "template", "SecretaryProfilePage_Template", "rf", "ctx", "SecretaryProfilePage_div_6_Template", "SecretaryProfilePage_div_7_Template", "SecretaryProfilePage_div_8_Template", "SecretaryProfilePage_Template_button_click_13_listener", "SecretaryProfilePage_Template_button_click_21_listener", "SecretaryProfilePage_Template_button_click_30_listener"], "sourceRoot": "webpack:///", "x_google_ignoreList": []}