// Material 3 Design Tokens for Tabs
:root {
  --md-primary-label: #000000;
  --md-icon-default: #000000;
  --md-background-primary: #FFFFFF;
  --md-secondary-text: #616161;
  --md-divider: rgba(0, 0, 0, 0.06);
  --md-accent-blue: #1B3A9E;
  --md-surface-variant: rgba(213, 208, 200, 0.38);
}

.material-tab-bar {
  --background: var(--md-surface-variant);
  background: rgba(213, 208, 200, 0.38) !important;
  border-top: 1px solid var(--md-divider);
  height: 80px;
  padding-bottom: env(safe-area-inset-bottom);
}

.material-tab-button {
  --color: var(--md-secondary-text);
  --color-selected: #C49A56; // Gold color for selected state
  --ripple-color: transparent;
  position: relative;

  // Circular background highlight for selected state
  &::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 48px;
    height: 48px;
    background: linear-gradient(135deg, rgba(196, 154, 86, 0.15) 0%, rgba(196, 154, 86, 0.08) 100%);
    border-radius: 50%;
    transform: translate(-50%, -50%) scale(0);
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    z-index: 0;
  }

  .tab-icon {
    font-size: 24px;
    margin-bottom: 0;
    color: var(--md-secondary-text);
    transition: color 0.3s ease;
    position: relative;
    z-index: 1;
  }

  .tab-icon-selected {
    display: none;
    font-size: 24px;
    margin-bottom: 0;
    color: #C49A56; // Gold color for selected icon
    position: relative;
    z-index: 1;
  }

  &.tab-selected {
    // Show circular background
    &::before {
      transform: translate(-50%, -50%) scale(1);
    }

    .tab-icon {
      display: none;
    }

    .tab-icon-selected {
      display: block;
      color: #C49A56; // Gold color for selected state
    }
  }

  // Add hover effect
  &:hover:not(.tab-selected) {
    .tab-icon {
      color: rgba(196, 154, 86, 0.7); // Semi-transparent gold on hover
    }

    // Subtle circle on hover
    &::before {
      transform: translate(-50%, -50%) scale(0.6);
      background: linear-gradient(135deg, rgba(196, 154, 86, 0.08) 0%, rgba(196, 154, 86, 0.04) 100%);
    }
  }
}
