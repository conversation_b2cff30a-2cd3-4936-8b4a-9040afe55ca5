import { Injectable } from '@angular/core';
import { AngularFireAuth } from '@angular/fire/compat/auth';
import { AngularFirestore } from '@angular/fire/compat/firestore';
import { BehaviorSubject, Observable } from 'rxjs';
import { map } from 'rxjs/operators';

export interface AdminUser {
  uid: string;
  email: string;
  displayName: string;
  role: 'admin';
  permissions: string[];
  lastLogin: Date;
  isActive: boolean;
  avatar?: string;
}

export interface LoginResult {
  success: boolean;
  user?: AdminUser;
  error?: string;
}

@Injectable({
  providedIn: 'root'
})
export class AdminAuthService {
  private currentAdminSubject = new BehaviorSubject<AdminUser | null>(null);
  public currentAdmin$ = this.currentAdminSubject.asObservable();
  private readonly ADMIN_SESSION_KEY = 'veritus_admin_session';
  private readonly ADMIN_DATA_KEY = 'veritus_admin_data';

  constructor(
    private afAuth: AngularFireAuth,
    private firestore: AngularFirestore
  ) {
    this.initializeAuthState();
  }

  private async initializeAuthState(): Promise<void> {
    // First, try to restore session from localStorage
    this.restoreSessionFromStorage();

    // Then listen to Firebase auth state changes
    this.afAuth.authState.subscribe(async (user) => {
      if (user) {
        const adminData = await this.getAdminData(user.uid);
        if (adminData && adminData.isActive) {
          this.setCurrentAdmin(adminData);
          this.saveSessionToStorage(adminData);
        } else {
          this.clearSession();
          await this.afAuth.signOut();
        }
      } else {
        // Check if we have a stored session but Firebase auth is not ready
        const storedSession = this.getStoredSession();
        if (storedSession && this.isSessionValid()) {
          // Keep the stored session active until Firebase auth is restored
          this.setCurrentAdmin(storedSession);
        } else {
          this.clearSession();
        }
      }
    });
  }

  async login(email: string, password: string): Promise<LoginResult> {
    try {
      // First check if the email is in the admin collection
      const adminQuery = await this.firestore
        .collection('admins', ref => ref.where('email', '==', email))
        .get()
        .toPromise();

      if (!adminQuery || adminQuery.empty) {
        return {
          success: false,
          error: 'Access denied. Admin credentials required.'
        };
      }

      const adminDoc = adminQuery.docs[0];
      const adminData = adminDoc.data() as AdminUser;

      if (!adminData.isActive) {
        return {
          success: false,
          error: 'Account is deactivated. Contact system administrator.'
        };
      }

      // Authenticate with Firebase Auth
      const credential = await this.afAuth.signInWithEmailAndPassword(email, password);
      
      if (credential.user) {
        // Update last login
        await this.firestore.collection('admins').doc(adminDoc.id).update({
          lastLogin: new Date()
        });

        const updatedAdminData = { ...adminData, lastLogin: new Date() };
        this.setCurrentAdmin(updatedAdminData);
        this.saveSessionToStorage(updatedAdminData);

        return {
          success: true,
          user: updatedAdminData
        };
      }

      return {
        success: false,
        error: 'Authentication failed.'
      };
    } catch (error: any) {
      let errorMessage = 'Login failed. Please try again.';
      
      switch (error.code) {
        case 'auth/user-not-found':
        case 'auth/wrong-password':
          errorMessage = 'Invalid email or password.';
          break;
        case 'auth/too-many-requests':
          errorMessage = 'Too many failed attempts. Please try again later.';
          break;
        case 'auth/user-disabled':
          errorMessage = 'Account has been disabled.';
          break;
      }

      return {
        success: false,
        error: errorMessage
      };
    }
  }

  async logout(): Promise<void> {
    await this.afAuth.signOut();
    this.clearSession();
  }

  isLoggedIn(): boolean {
    return this.currentAdminSubject.value !== null;
  }

  getCurrentAdmin(): AdminUser | null {
    return this.currentAdminSubject.value;
  }

  hasPermission(permission: string): boolean {
    const admin = this.getCurrentAdmin();
    return admin ? admin.permissions.includes(permission) : false;
  }

  hasRole(role: string): boolean {
    const admin = this.getCurrentAdmin();
    return admin ? admin.role === role : false;
  }

  private async getAdminData(uid: string): Promise<AdminUser | null> {
    try {
      const adminDoc = await this.firestore.collection('admins').doc(uid).get().toPromise();
      if (adminDoc && adminDoc.exists) {
        return adminDoc.data() as AdminUser;
      }
      return null;
    } catch (error) {
      console.error('Error fetching admin data:', error);
      return null;
    }
  }

  // Admin management methods
  async createAdmin(adminData: Partial<AdminUser>): Promise<boolean> {
    try {
      const currentAdmin = this.getCurrentAdmin();
      if (!currentAdmin || currentAdmin.role !== 'admin') {
        throw new Error('Insufficient permissions');
      }

      await this.firestore.collection('admins').add({
        ...adminData,
        createdAt: new Date(),
        createdBy: currentAdmin.uid
      });

      return true;
    } catch (error) {
      console.error('Error creating admin:', error);
      return false;
    }
  }

  async updateAdminStatus(adminId: string, isActive: boolean): Promise<boolean> {
    try {
      const currentAdmin = this.getCurrentAdmin();
      if (!currentAdmin || currentAdmin.role !== 'admin') {
        throw new Error('Insufficient permissions');
      }

      await this.firestore.collection('admins').doc(adminId).update({
        isActive,
        updatedAt: new Date(),
        updatedBy: currentAdmin.uid
      });

      return true;
    } catch (error) {
      console.error('Error updating admin status:', error);
      return false;
    }
  }

  // Session management methods
  private saveSessionToStorage(adminData: AdminUser): void {
    try {
      localStorage.setItem(this.ADMIN_SESSION_KEY, 'true');
      const dataToStore = {
        ...adminData,
        lastLogin: adminData.lastLogin instanceof Date
          ? adminData.lastLogin.toISOString()
          : new Date().toISOString()
      };
      localStorage.setItem(this.ADMIN_DATA_KEY, JSON.stringify(dataToStore));
    } catch (error) {
      console.error('Error saving session to storage:', error);
    }
  }

  private restoreSessionFromStorage(): void {
    try {
      const hasSession = localStorage.getItem(this.ADMIN_SESSION_KEY) === 'true';
      const adminDataStr = localStorage.getItem(this.ADMIN_DATA_KEY);

      if (hasSession && adminDataStr) {
        const adminData = JSON.parse(adminDataStr);
        // Convert lastLogin back to Date object
        if (adminData.lastLogin) {
          adminData.lastLogin = new Date(adminData.lastLogin);
        }
        this.setCurrentAdmin(adminData);
      }
    } catch (error) {
      console.error('Error restoring session from storage:', error);
      this.clearSession();
    }
  }

  private getStoredSession(): AdminUser | null {
    try {
      const hasSession = localStorage.getItem(this.ADMIN_SESSION_KEY) === 'true';
      const adminDataStr = localStorage.getItem(this.ADMIN_DATA_KEY);

      if (hasSession && adminDataStr) {
        const adminData = JSON.parse(adminDataStr);
        if (adminData.lastLogin) {
          adminData.lastLogin = new Date(adminData.lastLogin);
        }
        return adminData;
      }
      return null;
    } catch (error) {
      console.error('Error getting stored session:', error);
      return null;
    }
  }

  private setCurrentAdmin(adminData: AdminUser): void {
    this.currentAdminSubject.next(adminData);
  }

  private clearSession(): void {
    this.currentAdminSubject.next(null);
    localStorage.removeItem(this.ADMIN_SESSION_KEY);
    localStorage.removeItem(this.ADMIN_DATA_KEY);
  }

  // Public method to check if session is valid
  isSessionValid(): boolean {
    const storedSession = this.getStoredSession();
    if (!storedSession) return false;

    // Check if session is not too old (optional: add session expiry)
    const lastLogin = storedSession.lastLogin;
    if (lastLogin) {
      const sessionAge = Date.now() - lastLogin.getTime();
      const maxSessionAge = 7 * 24 * 60 * 60 * 1000; // 7 days
      return sessionAge < maxSessionAge;
    }

    return true;
  }
}
