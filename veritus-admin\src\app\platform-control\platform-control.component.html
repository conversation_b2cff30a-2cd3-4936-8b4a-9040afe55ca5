<div class="platform-control-container">
  <div class="control-header">
    <h1>Platform Control</h1>
    <p>Manage templates, system settings, and platform configurations</p>
  </div>

  <div class="control-tabs">
    <button class="tab-btn" 
            [class.active]="activeTab === 'templates'"
            (click)="setActiveTab('templates')">
      📄 Templates
    </button>
    <button class="tab-btn" 
            [class.active]="activeTab === 'announcements'"
            (click)="setActiveTab('announcements')">
      📢 Announcements
    </button>
    <button class="tab-btn" 
            [class.active]="activeTab === 'system'"
            (click)="setActiveTab('system')">
      ⚙️ System Settings
    </button>
    <button class="tab-btn" 
            [class.active]="activeTab === 'accounts'"
            (click)="setActiveTab('accounts')">
      👥 Account Management
    </button>
  </div>

  <!-- Templates Tab -->
  <div class="tab-content" *ngIf="activeTab === 'templates'">
    <div class="section-header">
      <h2>Legal Templates</h2>
      <button class="add-btn" (click)="openTemplateModal()">
        <i class="btn-icon">➕</i>
        New Template
      </button>
    </div>

    <div class="templates-grid">
      <div class="template-card" *ngFor="let template of templates">
        <div class="template-icon">
          <i class="file-icon">{{ getTemplateIcon(template.type) }}</i>
        </div>
        <div class="template-info">
          <h3>{{ template.name }}</h3>
          <p class="template-type">{{ template.type | titlecase }}</p>
          <p class="template-description">{{ template.description }}</p>
          <div class="template-meta">
            <span class="last-updated">Updated: {{ template.lastUpdated | date:'short' }}</span>
            <span class="usage-count">Used {{ template.usageCount }} times</span>
          </div>
        </div>
        <div class="template-actions">
          <button class="action-btn edit" (click)="editTemplate(template)">
            <i class="btn-icon">✏️</i>
            Edit
          </button>
          <button class="action-btn download" (click)="downloadTemplate(template)">
            <i class="btn-icon">⬇️</i>
            Download
          </button>
          <button class="action-btn delete" (click)="deleteTemplate(template)">
            <i class="btn-icon">🗑️</i>
            Delete
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Announcements Tab -->
  <div class="tab-content" *ngIf="activeTab === 'announcements'">
    <div class="section-header">
      <h2>System Announcements</h2>
      <button class="add-btn" (click)="openAnnouncementModal()">
        <i class="btn-icon">➕</i>
        New Announcement
      </button>
    </div>

    <div class="announcements-list">
      <div class="announcement-card" *ngFor="let announcement of announcements">
        <div class="announcement-header">
          <h3>{{ announcement.title }}</h3>
          <div class="announcement-meta">
            <span class="priority" [class]="announcement.priority">{{ announcement.priority | titlecase }}</span>
            <span class="date">{{ announcement.createdAt | date:'medium' }}</span>
          </div>
        </div>
        <div class="announcement-content">
          <p>{{ announcement.content }}</p>
        </div>
        <div class="announcement-actions">
          <button class="action-btn edit" (click)="editAnnouncement(announcement)">Edit</button>
          <button class="action-btn" 
                  [class]="announcement.isActive ? 'deactivate' : 'activate'"
                  (click)="toggleAnnouncement(announcement)">
            {{ announcement.isActive ? 'Deactivate' : 'Activate' }}
          </button>
          <button class="action-btn delete" (click)="deleteAnnouncement(announcement)">Delete</button>
        </div>
      </div>
    </div>
  </div>

  <!-- System Settings Tab -->
  <div class="tab-content" *ngIf="activeTab === 'system'">
    <div class="section-header">
      <h2>System Configuration</h2>
    </div>

    <div class="settings-sections">
      <div class="settings-card">
        <h3>Platform Settings</h3>
        <div class="setting-item">
          <label>Platform Name</label>
          <input type="text" [(ngModel)]="systemSettings.platformName" class="setting-input">
        </div>
        <div class="setting-item">
          <label>Maintenance Mode</label>
          <label class="toggle-switch">
            <input type="checkbox" [(ngModel)]="systemSettings.maintenanceMode">
            <span class="toggle-slider"></span>
          </label>
        </div>
        <div class="setting-item">
          <label>User Registration</label>
          <label class="toggle-switch">
            <input type="checkbox" [(ngModel)]="systemSettings.allowRegistration">
            <span class="toggle-slider"></span>
          </label>
        </div>
      </div>

      <div class="settings-card">
        <h3>Verification Settings</h3>
        <div class="setting-item">
          <label>Auto-approve verified lawyers</label>
          <label class="toggle-switch">
            <input type="checkbox" [(ngModel)]="systemSettings.autoApproveVerified">
            <span class="toggle-slider"></span>
          </label>
        </div>
        <div class="setting-item">
          <label>IBP Integration</label>
          <label class="toggle-switch">
            <input type="checkbox" [(ngModel)]="systemSettings.ibpIntegration">
            <span class="toggle-slider"></span>
          </label>
        </div>
        <div class="setting-item">
          <label>Document Retention (days)</label>
          <input type="number" [(ngModel)]="systemSettings.documentRetentionDays" class="setting-input">
        </div>
      </div>

      <div class="settings-card">
        <h3>Notification Settings</h3>
        <div class="setting-item">
          <label>Email Notifications</label>
          <label class="toggle-switch">
            <input type="checkbox" [(ngModel)]="systemSettings.emailNotifications">
            <span class="toggle-slider"></span>
          </label>
        </div>
        <div class="setting-item">
          <label>SMS Notifications</label>
          <label class="toggle-switch">
            <input type="checkbox" [(ngModel)]="systemSettings.smsNotifications">
            <span class="toggle-slider"></span>
          </label>
        </div>
      </div>
    </div>

    <div class="settings-actions">
      <button class="save-btn" (click)="saveSystemSettings()">
        <i class="btn-icon">💾</i>
        Save Settings
      </button>
      <button class="reset-btn" (click)="resetSystemSettings()">
        <i class="btn-icon">🔄</i>
        Reset to Default
      </button>
    </div>
  </div>

  <!-- Account Management Tab -->
  <div class="tab-content" *ngIf="activeTab === 'accounts'">
    <div class="section-header">
      <h2>Account Management</h2>
      <div class="account-filters">
        <select [(ngModel)]="accountFilter" (change)="filterAccounts()" class="filter-select">
          <option value="">All Accounts</option>
          <option value="active">Active</option>
          <option value="inactive">Inactive</option>
          <option value="flagged">Flagged</option>
        </select>
        <input type="text" 
               placeholder="Search accounts..." 
               [(ngModel)]="accountSearchQuery"
               (input)="filterAccounts()"
               class="search-input">
      </div>
    </div>

    <div class="accounts-table">
      <table>
        <thead>
          <tr>
            <th>User</th>
            <th>Type</th>
            <th>Status</th>
            <th>Last Active</th>
            <th>Actions</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let account of filteredAccounts">
            <td>
              <div class="user-info">
                <img [src]="account.avatar || '/assets/default-avatar.png'" 
                     [alt]="account.name" 
                     class="user-avatar">
                <div>
                  <div class="user-name">{{ account.name }}</div>
                  <div class="user-email">{{ account.email }}</div>
                </div>
              </div>
            </td>
            <td>
              <span class="account-type" [class]="account.type">{{ account.type | titlecase }}</span>
            </td>
            <td>
              <span class="account-status" [class]="account.status">{{ account.status | titlecase }}</span>
            </td>
            <td>{{ account.lastActive | date:'short' }}</td>
            <td>
              <div class="account-actions">
                <button class="action-btn small view" (click)="viewAccount(account)">View</button>
                <button class="action-btn small" 
                        [class]="account.status === 'active' ? 'deactivate' : 'activate'"
                        (click)="toggleAccountStatus(account)">
                  {{ account.status === 'active' ? 'Deactivate' : 'Activate' }}
                </button>
                <button class="action-btn small delete" (click)="deleteAccount(account)">Delete</button>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</div>
