rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    
    // Helper functions
    function isAuthenticated() {
      return request.auth != null;
    }
    
    function getUserRole() {
      return request.auth.token.role;
    }
    
    function isOwner(userId) {
      return request.auth.uid == userId;
    }
    
    function isLawyer() {
      return getUserRole() == 'lawyer';
    }
    
    function isSecretary() {
      return getUserRole() == 'secretary';
    }
    
    function isClient() {
      return getUserRole() == 'client';
    }
    
    function isAdmin() {
      return getUserRole() == 'admin';
    }

    // Admin collection - for admin panel authentication and management
    match /admins/{adminId} {
  allow get: if true;
  allow list: if request.auth == null || isAdmin(); 
  allow create: if true; 
  allow update: if isAuthenticated() && (isOwner(adminId) || isAdmin());
  allow delete: if isAuthenticated() && isAdmin();
}

    // User profiles - users can only access their own profile
    match /users/{userId} {
      allow read, write: if isAuthenticated() && isOwner(userId);
    }

    // Lawyer profiles
    match /lawyers/{lawyerId} {
      allow read: if isAuthenticated();
      allow write: if isAuthenticated() && isOwner(lawyerId) && isLawyer();
      allow create: if isAuthenticated() && isOwner(lawyerId) && isLawyer();
    }

    // Secretary profiles
    match /secretaries/{secretaryId} {
      allow read: if isAuthenticated() && (
        isOwner(secretaryId) || 
        isLinkedLawyer(secretaryId) ||
        isAdmin()
      );
      allow write: if isAuthenticated() && isOwner(secretaryId) && isSecretary();
      allow create: if isAuthenticated() && isOwner(secretaryId) && isSecretary();
    }

    // Client profiles
    match /clients/{clientId} {
      allow read: if isAuthenticated() && (
        isOwner(clientId) || 
        isLawyer() ||
        isAdmin()
      );
      allow write: if isAuthenticated() && isOwner(clientId) && isClient();
      allow create: if isAuthenticated() && isOwner(clientId) && isClient();
    }

    // Assistant codes - only lawyers can create/manage their own codes
    match /assistant_codes/{codeId} {
      allow read: if isAuthenticated() && (
        isLawyer() && resource.data.lawyerId == request.auth.uid ||
        isAdmin()
      );
      allow create: if false; // Only Cloud Functions can create codes
      allow update: if false; // Only Cloud Functions can update codes
      allow delete: if false; // Only Cloud Functions can delete codes
    }

    // Lawyer-Secretary links
    match /lawyer_secretary_links/{linkId} {
      allow read: if isAuthenticated() && (
        resource.data.lawyerId == request.auth.uid ||
        resource.data.secretaryId == request.auth.uid ||
        isAdmin()
      );
      allow create: if false; // Only Cloud Functions can create links
      allow update: if isAuthenticated() && (
        (isLawyer() && resource.data.lawyerId == request.auth.uid) ||
        isAdmin()
      );
      allow delete: if false; // Only Cloud Functions can delete links
    }

    // Cases - lawyers and linked secretaries can access
    match /cases/{caseId} {
      allow read: if isAuthenticated() && (
        resource.data.lawyerId == request.auth.uid ||
        isLinkedSecretary(resource.data.lawyerId) ||
        resource.data.clientId == request.auth.uid ||
        isAdmin()
      );
      allow write: if isAuthenticated() && (
        (isLawyer() && resource.data.lawyerId == request.auth.uid) ||
        (isSecretary() && isLinkedSecretary(resource.data.lawyerId) && hasPermission('canManageCases')) ||
        isAdmin()
      );
      allow create: if isAuthenticated() && (
        isLawyer() ||
        (isSecretary() && hasPermission('canManageCases')) ||
        isAdmin()
      );
    }

    // Appointments - lawyers, secretaries with permission, and clients can access
    match /appointments/{appointmentId} {
      allow read: if isAuthenticated() && (
        resource.data.lawyerId == request.auth.uid ||
        resource.data.clientId == request.auth.uid ||
        (isSecretary() && isLinkedSecretary(resource.data.lawyerId)) ||
        isAdmin()
      );
      allow write: if isAuthenticated() && (
        (isLawyer() && resource.data.lawyerId == request.auth.uid) ||
        (isSecretary() && isLinkedSecretary(resource.data.lawyerId) && hasPermission('canManageCalendar')) ||
        isAdmin()
      );
      allow create: if isAuthenticated() && (
        isLawyer() ||
        isClient() ||
        (isSecretary() && hasPermission('canManageCalendar')) ||
        isAdmin()
      );
    }

    // Files - lawyers and secretaries with permission can access
    match /files/{fileId} {
      allow read: if isAuthenticated() && (
        resource.data.lawyerId == request.auth.uid ||
        (isSecretary() && isLinkedSecretary(resource.data.lawyerId) && hasPermission('canManageFiles')) ||
        resource.data.clientId == request.auth.uid ||
        isAdmin()
      );
      allow write: if isAuthenticated() && (
        (isLawyer() && resource.data.lawyerId == request.auth.uid) ||
        (isSecretary() && isLinkedSecretary(resource.data.lawyerId) && hasPermission('canManageFiles')) ||
        isAdmin()
      );
      allow create: if isAuthenticated() && (
        isLawyer() ||
        (isSecretary() && hasPermission('canManageFiles')) ||
        isAdmin()
      );
    }

    // Retainers - lawyers and secretaries with permission can access
    match /retainers/{retainerId} {
      allow read: if isAuthenticated() && (
        resource.data.lawyerId == request.auth.uid ||
        (isSecretary() && isLinkedSecretary(resource.data.lawyerId) && hasPermission('canManageRetainers')) ||
        resource.data.clientId == request.auth.uid ||
        isAdmin()
      );
      allow write: if isAuthenticated() && (
        (isLawyer() && resource.data.lawyerId == request.auth.uid) ||
        (isSecretary() && isLinkedSecretary(resource.data.lawyerId) && hasPermission('canManageRetainers')) ||
        isAdmin()
      );
      allow create: if isAuthenticated() && (
        isLawyer() ||
        (isSecretary() && hasPermission('canManageRetainers')) ||
        isAdmin()
      );
    }

    // Financial records - lawyers and secretaries with permission can access
    match /financial_records/{recordId} {
      allow read: if isAuthenticated() && (
        resource.data.lawyerId == request.auth.uid ||
        (isSecretary() && isLinkedSecretary(resource.data.lawyerId) && hasPermission('canViewFinances')) ||
        isAdmin()
      );
      allow write: if isAuthenticated() && (
        (isLawyer() && resource.data.lawyerId == request.auth.uid) ||
        (isSecretary() && isLinkedSecretary(resource.data.lawyerId) && hasPermission('canManageFinances')) ||
        isAdmin()
      );
      allow create: if isAuthenticated() && (
        isLawyer() ||
        (isSecretary() && hasPermission('canManageFinances')) ||
        isAdmin()
      );
    }

    // Audit logs - read-only for involved parties, admin can read all
    match /audit_logs/{logId} {
      allow read: if isAuthenticated() && (
        resource.data.userId == request.auth.uid ||
        isAdmin()
      );
      allow write: if false; // Only Cloud Functions can write audit logs
    }

    // Helper function to check if secretary is linked to a lawyer
    function isLinkedSecretary(lawyerId) {
      return isSecretary() && 
        exists(/databases/$(database)/documents/lawyer_secretary_links/$(getLinkId(lawyerId, request.auth.uid)));
    }

    // Helper function to get link document ID (simplified - in practice you'd query)
    function getLinkId(lawyerId, secretaryId) {
      return lawyerId + '_' + secretaryId;
    }

    // Helper function to check secretary permissions
    function hasPermission(permission) {
      return isSecretary() && 
        get(/databases/$(database)/documents/secretaries/$(request.auth.uid)).data.permissions[permission] == true;
    }

    // Helper function to check if user is linked lawyer for a secretary
    function isLinkedLawyer(secretaryId) {
      return isLawyer() && 
        request.auth.uid in get(/databases/$(database)/documents/secretaries/$(secretaryId)).data.linkedLawyers;
    }

    // Default deny rule
    match /{document=**} {
      allow read, write: if false;
    }
  }
}
