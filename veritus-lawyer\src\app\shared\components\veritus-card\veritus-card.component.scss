.veritus-card {
  background: var(--veritus-white);
  border-radius: var(--veritus-border-radius);
  border: 1px solid var(--veritus-gray-light);
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
  }
}

.veritus-card-hero {
  background: var(--veritus-gradient-primary);
  color: var(--veritus-white);
  border: none;
}

.veritus-card-compact {
  border-radius: var(--veritus-border-radius-sm);
}

.veritus-shadow-sm {
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.veritus-shadow-md {
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.veritus-shadow-lg {
  box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1);
}

.veritus-p-sm {
  padding: var(--veritus-spacing-sm);
}

.veritus-p-md {
  padding: var(--veritus-spacing-md);
}

.veritus-p-lg {
  padding: var(--veritus-spacing-lg);
}
