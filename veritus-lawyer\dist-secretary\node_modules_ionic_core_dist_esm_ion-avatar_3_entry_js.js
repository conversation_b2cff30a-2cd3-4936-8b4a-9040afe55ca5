"use strict";
(self["webpackChunkveritus_secretary"] = self["webpackChunkveritus_secretary"] || []).push([["node_modules_ionic_core_dist_esm_ion-avatar_3_entry_js"],{

/***/ 9642:
/*!*****************************************************************!*\
  !*** ./node_modules/@ionic/core/dist/esm/ion-avatar_3.entry.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ion_avatar: () => (/* binding */ Avatar),
/* harmony export */   ion_badge: () => (/* binding */ Badge),
/* harmony export */   ion_thumbnail: () => (/* binding */ Thumbnail)
/* harmony export */ });
/* harmony import */ var _index_a1a47f01_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./index-a1a47f01.js */ 2856);
/* harmony import */ var _ionic_global_94f25d1b_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./ionic-global-94f25d1b.js */ 3502);
/* harmony import */ var _theme_01f3f29c_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./theme-01f3f29c.js */ 1882);
/*!
 * (C) Ionic http://ionicframework.com - MIT License
 */



const avatarIosCss = ":host{border-radius:var(--border-radius);display:block}::slotted(ion-img),::slotted(img){border-radius:var(--border-radius);width:100%;height:100%;-o-object-fit:cover;object-fit:cover;overflow:hidden}:host{--border-radius:50%;width:48px;height:48px}";
const IonAvatarIosStyle0 = avatarIosCss;
const avatarMdCss = ":host{border-radius:var(--border-radius);display:block}::slotted(ion-img),::slotted(img){border-radius:var(--border-radius);width:100%;height:100%;-o-object-fit:cover;object-fit:cover;overflow:hidden}:host{--border-radius:50%;width:64px;height:64px}";
const IonAvatarMdStyle0 = avatarMdCss;
const Avatar = class {
  constructor(hostRef) {
    (0,_index_a1a47f01_js__WEBPACK_IMPORTED_MODULE_0__.r)(this, hostRef);
  }
  render() {
    return (0,_index_a1a47f01_js__WEBPACK_IMPORTED_MODULE_0__.h)(_index_a1a47f01_js__WEBPACK_IMPORTED_MODULE_0__.H, {
      key: 'f6014b524497bb18ae919ba6f6928407310d6870',
      class: (0,_ionic_global_94f25d1b_js__WEBPACK_IMPORTED_MODULE_1__.b)(this)
    }, (0,_index_a1a47f01_js__WEBPACK_IMPORTED_MODULE_0__.h)("slot", {
      key: '192ff4a8e10c0b0a4a2ed795ff2675afa8b23449'
    }));
  }
};
Avatar.style = {
  ios: IonAvatarIosStyle0,
  md: IonAvatarMdStyle0
};
const badgeIosCss = ":host{--background:var(--ion-color-primary, #3880ff);--color:var(--ion-color-primary-contrast, #fff);--padding-top:3px;--padding-end:8px;--padding-bottom:3px;--padding-start:8px;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);display:inline-block;min-width:10px;background:var(--background);color:var(--color);font-family:var(--ion-font-family, inherit);font-size:0.8125rem;font-weight:bold;line-height:1;text-align:center;white-space:nowrap;contain:content;vertical-align:baseline}:host(.ion-color){background:var(--ion-color-base);color:var(--ion-color-contrast)}:host(:empty){display:none}:host{border-radius:10px;font-size:max(13px, 0.8125rem)}";
const IonBadgeIosStyle0 = badgeIosCss;
const badgeMdCss = ":host{--background:var(--ion-color-primary, #3880ff);--color:var(--ion-color-primary-contrast, #fff);--padding-top:3px;--padding-end:8px;--padding-bottom:3px;--padding-start:8px;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);display:inline-block;min-width:10px;background:var(--background);color:var(--color);font-family:var(--ion-font-family, inherit);font-size:0.8125rem;font-weight:bold;line-height:1;text-align:center;white-space:nowrap;contain:content;vertical-align:baseline}:host(.ion-color){background:var(--ion-color-base);color:var(--ion-color-contrast)}:host(:empty){display:none}:host{--padding-top:3px;--padding-end:4px;--padding-bottom:4px;--padding-start:4px;border-radius:4px}";
const IonBadgeMdStyle0 = badgeMdCss;
const Badge = class {
  constructor(hostRef) {
    (0,_index_a1a47f01_js__WEBPACK_IMPORTED_MODULE_0__.r)(this, hostRef);
    this.color = undefined;
  }
  render() {
    const mode = (0,_ionic_global_94f25d1b_js__WEBPACK_IMPORTED_MODULE_1__.b)(this);
    return (0,_index_a1a47f01_js__WEBPACK_IMPORTED_MODULE_0__.h)(_index_a1a47f01_js__WEBPACK_IMPORTED_MODULE_0__.H, {
      key: '22d41ceefb76f40dfbf739fd71483f1272a45858',
      class: (0,_theme_01f3f29c_js__WEBPACK_IMPORTED_MODULE_2__.c)(this.color, {
        [mode]: true
      })
    }, (0,_index_a1a47f01_js__WEBPACK_IMPORTED_MODULE_0__.h)("slot", {
      key: 'e7e65463bac5903971a8f9f6be55515f42b81a83'
    }));
  }
};
Badge.style = {
  ios: IonBadgeIosStyle0,
  md: IonBadgeMdStyle0
};
const thumbnailCss = ":host{--size:48px;--border-radius:0;border-radius:var(--border-radius);display:block;width:var(--size);height:var(--size)}::slotted(ion-img),::slotted(img){border-radius:var(--border-radius);width:100%;height:100%;-o-object-fit:cover;object-fit:cover;overflow:hidden}";
const IonThumbnailStyle0 = thumbnailCss;
const Thumbnail = class {
  constructor(hostRef) {
    (0,_index_a1a47f01_js__WEBPACK_IMPORTED_MODULE_0__.r)(this, hostRef);
  }
  render() {
    return (0,_index_a1a47f01_js__WEBPACK_IMPORTED_MODULE_0__.h)(_index_a1a47f01_js__WEBPACK_IMPORTED_MODULE_0__.H, {
      key: 'd2667635930e4c0896805f452357e7dc9086bc72',
      class: (0,_ionic_global_94f25d1b_js__WEBPACK_IMPORTED_MODULE_1__.b)(this)
    }, (0,_index_a1a47f01_js__WEBPACK_IMPORTED_MODULE_0__.h)("slot", {
      key: '66eb1487f3da4da2ef71b812a8d0f0fe884c7d81'
    }));
  }
};
Thumbnail.style = IonThumbnailStyle0;


/***/ })

}]);
//# sourceMappingURL=node_modules_ionic_core_dist_esm_ion-avatar_3_entry_js.js.map