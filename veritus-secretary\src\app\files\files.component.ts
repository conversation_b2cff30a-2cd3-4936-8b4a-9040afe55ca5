import { Component, OnInit } from '@angular/core';

interface Document {
  id: string;
  name: string;
  category: 'disclosures' | 'evidence' | 'receipts' | 'contracts';
  uploadDate: Date;
  size: string;
  type: string;
}

@Component({
  selector: 'app-files',
  template: `
    <div class="files-container">
      <div class="files-header">
        <h1>Files</h1>
        <div class="search-container">
          <mat-form-field appearance="outline" class="search-field">
            <mat-icon matPrefix>search</mat-icon>
            <input matInput placeholder="Search a document" [(ngModel)]="searchTerm" (input)="filterDocuments()">
          </mat-form-field>
        </div>
      </div>

      <div class="files-content">
        <!-- Document Categories -->
        <div class="document-categories">
          <div class="category-grid">
            <div class="category-card" (click)="selectCategory('disclosures')">
              <div class="category-icon">
                <mat-icon>folder</mat-icon>
              </div>
              <div class="category-name">Disclosures</div>
              <div class="category-count">{{ getCategoryCount('disclosures') }} files</div>
            </div>

            <div class="category-card" (click)="selectCategory('evidence')">
              <div class="category-icon">
                <mat-icon>folder</mat-icon>
              </div>
              <div class="category-name">Evidence</div>
              <div class="category-count">{{ getCategoryCount('evidence') }} files</div>
            </div>

            <div class="category-card" (click)="selectCategory('receipts')">
              <div class="category-icon">
                <mat-icon>folder</mat-icon>
              </div>
              <div class="category-name">Receipts</div>
              <div class="category-count">{{ getCategoryCount('receipts') }} files</div>
            </div>

            <div class="category-card" (click)="selectCategory('contracts')">
              <div class="category-icon">
                <mat-icon>folder</mat-icon>
              </div>
              <div class="category-name">Contracts</div>
              <div class="category-count">{{ getCategoryCount('contracts') }} files</div>
            </div>
          </div>
        </div>

        <!-- Recent Documents -->
        <div class="recent-documents">
          <div class="section-header">
            <h3>Recent documents</h3>
            <button mat-raised-button color="primary">
              <mat-icon>upload</mat-icon>
              Upload Document
            </button>
          </div>

          <div class="documents-list">
            <div class="document-item" *ngFor="let doc of filteredDocuments">
              <div class="document-icon">
                <mat-icon>{{ getDocumentIcon(doc.type) }}</mat-icon>
              </div>
              <div class="document-info">
                <div class="document-name">{{ doc.name }}</div>
                <div class="document-meta">
                  <span class="document-size">{{ doc.size }}</span>
                  <span class="document-date">{{ getTimeAgo(doc.uploadDate) }}</span>
                </div>
              </div>
              <div class="document-actions">
                <button mat-icon-button>
                  <mat-icon>download</mat-icon>
                </button>
                <button mat-icon-button>
                  <mat-icon>more_vert</mat-icon>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .files-container {
      padding: 32px;
      background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
      min-height: calc(100vh - 64px);
      position: relative;
    }

    .files-container::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background:
        radial-gradient(circle at 30% 30%, rgba(168, 85, 247, 0.05) 0%, transparent 50%),
        radial-gradient(circle at 70% 70%, rgba(34, 197, 94, 0.03) 0%, transparent 50%);
      pointer-events: none;
    }

    .files-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 40px;
      position: relative;
      z-index: 1;
    }

    .files-header h1 {
      margin: 0;
      font-size: 36px;
      font-weight: 700;
      color: #1e293b;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      position: relative;
    }

    .files-header h1::after {
      content: '';
      position: absolute;
      bottom: -8px;
      left: 0;
      width: 80px;
      height: 4px;
      background: linear-gradient(90deg, #a855f7 0%, #9333ea 100%);
      border-radius: 2px;
    }

    .search-container {
      width: 300px;
    }

    .search-field {
      width: 100%;
    }

    .files-content {
      display: flex;
      flex-direction: column;
      gap: 32px;
    }

    .document-categories {
      background: white;
      border-radius: 12px;
      padding: 24px;
      box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }

    .category-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 16px;
    }

    .category-card {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 24px;
      border: 2px solid #e0e0e0;
      border-radius: 12px;
      cursor: pointer;
      transition: all 0.3s ease;
      background: #fafafa;
    }

    .category-card:hover {
      border-color: #C49A56;
      background: #fff;
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    }

    .category-icon {
      width: 60px;
      height: 60px;
      border-radius: 50%;
      background: linear-gradient(135deg, #C49A56 0%, #B8935A 100%);
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 16px;
    }

    .category-icon mat-icon {
      color: white;
      font-size: 28px;
      width: 28px;
      height: 28px;
    }

    .category-name {
      font-size: 16px;
      font-weight: 600;
      color: #333;
      margin-bottom: 4px;
    }

    .category-count {
      font-size: 12px;
      color: #666;
    }

    .recent-documents {
      background: white;
      border-radius: 12px;
      padding: 24px;
      box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }

    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 24px;
    }

    .section-header h3 {
      margin: 0;
      font-size: 18px;
      font-weight: 600;
      color: #333;
    }

    .documents-list {
      display: flex;
      flex-direction: column;
      gap: 12px;
    }

    .document-item {
      display: flex;
      align-items: center;
      gap: 16px;
      padding: 16px;
      border: 1px solid #e0e0e0;
      border-radius: 8px;
      transition: all 0.2s ease;
    }

    .document-item:hover {
      background: #f8f9fa;
      border-color: #C49A56;
    }

    .document-icon {
      width: 40px;
      height: 40px;
      border-radius: 8px;
      background: #f0f0f0;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .document-icon mat-icon {
      color: #666;
      font-size: 20px;
      width: 20px;
      height: 20px;
    }

    .document-info {
      flex: 1;
    }

    .document-name {
      font-size: 14px;
      font-weight: 500;
      color: #333;
      margin-bottom: 4px;
    }

    .document-meta {
      display: flex;
      gap: 16px;
      font-size: 12px;
      color: #666;
    }

    .document-actions {
      display: flex;
      gap: 4px;
    }

    .document-actions button {
      width: 32px;
      height: 32px;
    }

    .document-actions mat-icon {
      font-size: 16px;
      width: 16px;
      height: 16px;
    }

    @media (max-width: 768px) {
      .files-header {
        flex-direction: column;
        gap: 16px;
        align-items: stretch;
      }

      .search-container {
        width: 100%;
      }

      .category-grid {
        grid-template-columns: repeat(2, 1fr);
      }

      .section-header {
        flex-direction: column;
        gap: 16px;
        align-items: stretch;
      }
    }
  `]
})
export class FilesComponent implements OnInit {
  searchTerm = '';
  selectedCategory: string | null = null;
  
  documents: Document[] = [
    {
      id: '1',
      name: 'Lopez-Contract.pdf',
      category: 'contracts',
      uploadDate: new Date(),
      size: '2.4 MB',
      type: 'pdf'
    },
    {
      id: '2',
      name: 'Client-Agreement.pdf',
      category: 'contracts',
      uploadDate: new Date(Date.now() - 4 * 24 * 60 * 60 * 1000), // 4 days ago
      size: '1.8 MB',
      type: 'pdf'
    },
    {
      id: '3',
      name: 'Evidence-Photos.zip',
      category: 'evidence',
      uploadDate: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000), // 2 days ago
      size: '15.2 MB',
      type: 'zip'
    },
    {
      id: '4',
      name: 'Financial-Disclosure.docx',
      category: 'disclosures',
      uploadDate: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000), // 1 day ago
      size: '890 KB',
      type: 'docx'
    },
    {
      id: '5',
      name: 'Receipt-001.jpg',
      category: 'receipts',
      uploadDate: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000), // 3 days ago
      size: '245 KB',
      type: 'jpg'
    }
  ];

  filteredDocuments: Document[] = [];

  ngOnInit() {
    this.filteredDocuments = [...this.documents];
  }

  getCategoryCount(category: string): number {
    return this.documents.filter(doc => doc.category === category).length;
  }

  selectCategory(category: string) {
    this.selectedCategory = category;
    this.filterDocuments();
  }

  filterDocuments() {
    let filtered = [...this.documents];

    // Filter by category
    if (this.selectedCategory) {
      filtered = filtered.filter(doc => doc.category === this.selectedCategory);
    }

    // Filter by search term
    if (this.searchTerm) {
      filtered = filtered.filter(doc => 
        doc.name.toLowerCase().includes(this.searchTerm.toLowerCase())
      );
    }

    this.filteredDocuments = filtered;
  }

  getDocumentIcon(type: string): string {
    switch (type) {
      case 'pdf': return 'picture_as_pdf';
      case 'docx': return 'description';
      case 'zip': return 'archive';
      case 'jpg': 
      case 'png': return 'image';
      default: return 'insert_drive_file';
    }
  }

  getTimeAgo(date: Date): string {
    const now = new Date();
    const diffTime = Math.abs(now.getTime() - date.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    if (diffDays === 1) {
      return 'Updated today';
    } else {
      return `${diffDays} days ago`;
    }
  }
}
