<ion-header>
  <ion-toolbar class="client-toolbar">
    <ion-title class="client-title">Find a Lawyer</ion-title>
  </ion-toolbar>
</ion-header>

<ion-content class="client-search-content">
  <div class="search-container">
    
    <!-- Search Bar -->
    <div class="search-section">
      <ion-searchbar
        [(ngModel)]="searchTerm"
        (ionInput)="onSearchChange()"
        placeholder="Search lawyers, firms, or specialties..."
        class="client-searchbar">
      </ion-searchbar>
    </div>

    <!-- Lawyers List -->
    <div class="lawyers-section">
      <div class="section-header" *ngIf="filteredLawyers.length > 0">
        <h3 class="section-title">Available Lawyers ({{ filteredLawyers.length }})</h3>
      </div>
      
      <div class="lawyers-list">
        <div class="lawyer-card" *ngFor="let lawyer of filteredLawyers">
          <div class="lawyer-avatar">
            <div class="avatar-placeholder">
              <ion-icon name="person" class="avatar-icon"></ion-icon>
            </div>
            <div class="availability-indicator" [class.available]="lawyer.isAvailable"></div>
          </div>
          
          <div class="lawyer-content">
            <div class="lawyer-header">
              <h4 class="lawyer-name">{{ lawyer.name }}</h4>
              <div class="lawyer-rating">
                <div class="stars">
                  <ion-icon 
                    *ngFor="let star of getStarArray(lawyer.rating)" 
                    [name]="star ? 'star' : 'star-outline'"
                    [class.filled]="star">
                  </ion-icon>
                </div>
                <span class="rating-text">{{ lawyer.rating }}</span>
              </div>
            </div>
            
            <div class="lawyer-details">
              <p class="lawyer-firm">{{ lawyer.firm }}</p>
              <p class="lawyer-location">
                <ion-icon name="location-outline"></ion-icon>
                {{ lawyer.location }}
              </p>
              <p class="lawyer-experience">
                <ion-icon name="briefcase-outline"></ion-icon>
                {{ lawyer.experience }} experience
              </p>
            </div>
            
            <p class="lawyer-bio">{{ lawyer.bio }}</p>
            
            <div class="lawyer-specialties">
              <span class="specialty-tag" *ngFor="let specialty of lawyer.specialties.slice(0, 2)">
                {{ specialty }}
              </span>
              <span class="more-specialties" *ngIf="lawyer.specialties.length > 2">
                +{{ lawyer.specialties.length - 2 }} more
              </span>
            </div>
          </div>
          
          <div class="lawyer-actions">
            <button 
              class="action-btn view-btn" 
              (click)="onViewLawyer(lawyer)">
              View
            </button>
            <button 
              class="action-btn request-btn" 
              (click)="onRequestRetainer(lawyer)"
              [disabled]="!lawyer.isAvailable">
              {{ lawyer.isAvailable ? 'Request' : 'Unavailable' }}
            </button>
          </div>
        </div>
      </div>
      
      <!-- Empty State -->
      <div class="empty-state" *ngIf="filteredLawyers.length === 0">
        <ion-icon name="search-outline" class="empty-icon"></ion-icon>
        <h3 class="empty-title">No lawyers found</h3>
        <p class="empty-description">
          Try adjusting your search terms or browse all available lawyers.
        </p>
        <button class="clear-search-btn" (click)="searchTerm = ''; onSearchChange()">
          Clear Search
        </button>
      </div>
    </div>

  </div>
</ion-content>
