.timeline-container {
  padding: var(--veritus-spacing-md);
}

.timeline-item {
  display: flex;
  margin-bottom: var(--veritus-spacing-lg);
  position: relative;

  &:last-child {
    margin-bottom: 0;
  }
}

.timeline-marker {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-right: var(--veritus-spacing-md);
  position: relative;
}

.timeline-dot {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: var(--veritus-gray-light);
  border: 3px solid var(--veritus-white);
  box-shadow: 0 0 0 2px var(--veritus-gray-light);
  transition: all 0.3s ease;
  z-index: 2;

  &.timeline-dot-completed {
    background: var(--veritus-primary);
    box-shadow: 0 0 0 2px var(--veritus-primary);
  }
}

.timeline-line {
  width: 2px;
  height: 40px;
  background: var(--veritus-gray-light);
  margin-top: 4px;
  transition: all 0.3s ease;

  &.timeline-line-completed {
    background: var(--veritus-primary);
  }
}

.timeline-content {
  flex: 1;
  padding-top: 2px;
}

.timeline-title {
  margin: 0 0 var(--veritus-spacing-xs) 0;
  color: var(--veritus-gray-dark);
}

.timeline-description {
  margin: 0 0 var(--veritus-spacing-xs) 0;
  line-height: 1.5;
}

.timeline-date {
  color: var(--veritus-gray-medium);
  font-style: italic;
}
