import { Injectable } from '@angular/core';
import { AngularFirestore } from '@angular/fire/compat/firestore';
import { AngularFireStorage } from '@angular/fire/compat/storage';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import firebase from 'firebase/compat/app';

export interface Template {
  id: string;
  name: string;
  type: string;
  description: string;
  content: string;
  lastUpdated: Date;
  usageCount: number;
  isActive: boolean;
  createdBy: string;
}

export interface Announcement {
  id: string;
  title: string;
  content: string;
  priority: 'high' | 'medium' | 'low';
  isActive: boolean;
  createdAt: Date;
  expiresAt?: Date;
  createdBy: string;
}

export interface SystemSettings {
  platformName: string;
  maintenanceMode: boolean;
  allowRegistration: boolean;
  autoApproveVerified: boolean;
  ibpIntegration: boolean;
  documentRetentionDays: number;
  emailNotifications: boolean;
  smsNotifications: boolean;
  lastUpdated: Date;
  updatedBy: string;
}

@Injectable({
  providedIn: 'root'
})
export class PlatformControlService {

  constructor(
    private firestore: AngularFirestore,
    private storage: AngularFireStorage
  ) { }

  // Template Management
  getTemplates(): Observable<Template[]> {
    return this.firestore
      .collection('templates', ref => ref.orderBy('lastUpdated', 'desc'))
      .snapshotChanges()
      .pipe(
        map(actions => actions.map(a => {
          const data = a.payload.doc.data() as Template;
          const id = a.payload.doc.id;
          return { ...data, id };
        }))
      );
  }

  async createTemplate(template: Partial<Template>): Promise<boolean> {
    try {
      const templateData = {
        ...template,
        createdAt: new Date(),
        lastUpdated: new Date(),
        usageCount: 0,
        isActive: true,
        createdBy: 'current-admin-id' // Replace with actual admin ID
      };

      await this.firestore.collection('templates').add(templateData);
      return true;
    } catch (error) {
      console.error('Error creating template:', error);
      return false;
    }
  }

  async updateTemplate(templateId: string, updates: Partial<Template>): Promise<boolean> {
    try {
      const updateData = {
        ...updates,
        lastUpdated: new Date(),
        updatedBy: 'current-admin-id' // Replace with actual admin ID
      };

      await this.firestore.collection('templates').doc(templateId).update(updateData);
      return true;
    } catch (error) {
      console.error('Error updating template:', error);
      return false;
    }
  }

  async deleteTemplate(templateId: string): Promise<boolean> {
    try {
      await this.firestore.collection('templates').doc(templateId).delete();
      return true;
    } catch (error) {
      console.error('Error deleting template:', error);
      return false;
    }
  }

  async uploadTemplate(file: File): Promise<string> {
    try {
      const filePath = `templates/${Date.now()}_${file.name}`;
      const fileRef = this.storage.ref(filePath);
      const uploadTask = await fileRef.put(file);
      return await uploadTask.ref.getDownloadURL();
    } catch (error) {
      console.error('Error uploading template:', error);
      throw error;
    }
  }

  async incrementTemplateUsage(templateId: string): Promise<boolean> {
    try {
      const templateRef = this.firestore.collection('templates').doc(templateId);
      await templateRef.update({
        usageCount: firebase.firestore.FieldValue.increment(1),
        lastUsed: new Date()
      });
      return true;
    } catch (error) {
      console.error('Error incrementing template usage:', error);
      return false;
    }
  }

  // Announcement Management
  getAnnouncements(): Observable<Announcement[]> {
    return this.firestore
      .collection('announcements', ref => ref.orderBy('createdAt', 'desc'))
      .snapshotChanges()
      .pipe(
        map(actions => actions.map(a => {
          const data = a.payload.doc.data() as Announcement;
          const id = a.payload.doc.id;
          return { ...data, id };
        }))
      );
  }

  getActiveAnnouncements(): Observable<Announcement[]> {
    return this.firestore
      .collection('announcements', ref => 
        ref.where('isActive', '==', true)
           .where('expiresAt', '>', new Date())
           .orderBy('expiresAt')
           .orderBy('priority')
      )
      .snapshotChanges()
      .pipe(
        map(actions => actions.map(a => {
          const data = a.payload.doc.data() as Announcement;
          const id = a.payload.doc.id;
          return { ...data, id };
        }))
      );
  }

  async createAnnouncement(announcement: Partial<Announcement>): Promise<boolean> {
    try {
      const announcementData = {
        ...announcement,
        createdAt: new Date(),
        isActive: true,
        createdBy: 'current-admin-id' // Replace with actual admin ID
      };

      await this.firestore.collection('announcements').add(announcementData);
      return true;
    } catch (error) {
      console.error('Error creating announcement:', error);
      return false;
    }
  }

  async updateAnnouncement(announcementId: string, updates: Partial<Announcement>): Promise<boolean> {
    try {
      const updateData = {
        ...updates,
        updatedAt: new Date(),
        updatedBy: 'current-admin-id' // Replace with actual admin ID
      };

      await this.firestore.collection('announcements').doc(announcementId).update(updateData);
      return true;
    } catch (error) {
      console.error('Error updating announcement:', error);
      return false;
    }
  }

  async toggleAnnouncement(announcementId: string, isActive: boolean): Promise<boolean> {
    try {
      await this.firestore.collection('announcements').doc(announcementId).update({
        isActive,
        updatedAt: new Date(),
        updatedBy: 'current-admin-id' // Replace with actual admin ID
      });
      return true;
    } catch (error) {
      console.error('Error toggling announcement:', error);
      return false;
    }
  }

  async deleteAnnouncement(announcementId: string): Promise<boolean> {
    try {
      await this.firestore.collection('announcements').doc(announcementId).delete();
      return true;
    } catch (error) {
      console.error('Error deleting announcement:', error);
      return false;
    }
  }

  // System Settings Management
  getSystemSettings(): Observable<SystemSettings | null> {
    return this.firestore
      .collection('system_settings')
      .doc('main')
      .snapshotChanges()
      .pipe(
        map(action => {
          if (action.payload.exists) {
            const data = action.payload.data() as SystemSettings;
            return { id: action.payload.id, ...data };
          }
          return null;
        })
      );
  }

  async updateSystemSettings(settings: Partial<SystemSettings>): Promise<boolean> {
    try {
      const updateData = {
        ...settings,
        lastUpdated: new Date(),
        updatedBy: 'current-admin-id' // Replace with actual admin ID
      };

      await this.firestore.collection('system_settings').doc('main').set(updateData, { merge: true });
      return true;
    } catch (error) {
      console.error('Error updating system settings:', error);
      return false;
    }
  }

  async resetSystemSettings(): Promise<boolean> {
    try {
      const defaultSettings: SystemSettings = {
        platformName: 'Veritus',
        maintenanceMode: false,
        allowRegistration: true,
        autoApproveVerified: false,
        ibpIntegration: true,
        documentRetentionDays: 365,
        emailNotifications: true,
        smsNotifications: false,
        lastUpdated: new Date(),
        updatedBy: 'current-admin-id' // Replace with actual admin ID
      };

      await this.firestore.collection('system_settings').doc('main').set(defaultSettings);
      return true;
    } catch (error) {
      console.error('Error resetting system settings:', error);
      return false;
    }
  }

  // Account Management
  async updateAccountStatus(accountId: string, status: string): Promise<boolean> {
    try {
      await this.firestore.collection('users').doc(accountId).update({
        status,
        statusUpdatedAt: new Date(),
        statusUpdatedBy: 'current-admin-id' // Replace with actual admin ID
      });
      return true;
    } catch (error) {
      console.error('Error updating account status:', error);
      return false;
    }
  }

  async deleteAccount(accountId: string): Promise<boolean> {
    try {
      // Instead of deleting, mark as deleted for audit purposes
      await this.firestore.collection('users').doc(accountId).update({
        isDeleted: true,
        deletedAt: new Date(),
        deletedBy: 'current-admin-id' // Replace with actual admin ID
      });
      return true;
    } catch (error) {
      console.error('Error deleting account:', error);
      return false;
    }
  }

  async flagAccount(accountId: string, reason: string): Promise<boolean> {
    try {
      await this.firestore.collection('users').doc(accountId).update({
        status: 'flagged',
        flagReason: reason,
        flaggedAt: new Date(),
        flaggedBy: 'current-admin-id' // Replace with actual admin ID
      });
      return true;
    } catch (error) {
      console.error('Error flagging account:', error);
      return false;
    }
  }

  // Analytics and Reports
  async getPlatformStats(): Promise<any> {
    try {
      const [users, templates, announcements] = await Promise.all([
        this.firestore.collection('users').get().toPromise(),
        this.firestore.collection('templates').get().toPromise(),
        this.firestore.collection('announcements').get().toPromise()
      ]);

      return {
        totalUsers: users?.size || 0,
        totalTemplates: templates?.size || 0,
        totalAnnouncements: announcements?.size || 0,
        generatedAt: new Date()
      };
    } catch (error) {
      console.error('Error getting platform stats:', error);
      return null;
    }
  }

  async generateSystemReport(): Promise<any> {
    try {
      const stats = await this.getPlatformStats();
      const settings = await this.getSystemSettings().pipe(map(s => s)).toPromise();
      
      return {
        stats,
        settings,
        generatedAt: new Date(),
        generatedBy: 'current-admin-id' // Replace with actual admin ID
      };
    } catch (error) {
      console.error('Error generating system report:', error);
      return null;
    }
  }
}
