import { Injectable } from '@angular/core';
import { initializeApp } from 'firebase/app';
import { 
  getAuth, 
  Auth, 
  signInWithEmailAndPassword, 
  createUserWithEmailAndPassword,
  signOut,
  sendPasswordResetEmail,
  User
} from 'firebase/auth';
import { 
  getFirestore, 
  Firestore, 
  doc, 
  setDoc, 
  getDoc, 
  updateDoc, 
  deleteDoc,
  collection, 
  addDoc, 
  query, 
  where, 
  orderBy, 
  getDocs,
  limit
} from 'firebase/firestore';
import { getFunctions, httpsCallable } from 'firebase/functions';
import { environment } from '../../environments/environment';
import {
  LawyerAvailability,
  EnhancedAppointment,
  RescheduleRequest,
  AppointmentFilter,
  AppointmentReminder,
  LawyerCalendarSummary
} from '../models/scheduling.models';

// Interfaces
export interface SecretaryProfile {
  uid: string;
  email: string;
  name: string;
  phone?: string;
  avatar?: string;
  role: 'secretary';
  linkedLawyers: string[]; // Array of lawyer UIDs
  permissions: SecretaryPermissions;
  createdAt: Date;
  updatedAt: Date;
}

export interface SecretaryPermissions {
  canManageCalendar: boolean;
  canManageFiles: boolean;
  canManageCases: boolean;
  canManageRetainers: boolean;
  canViewFinances: boolean;
  canManageFinances: boolean;
}

export interface AssistantCode {
  id?: string;
  code: string;
  lawyerId: string;
  lawyerName: string;
  permissions: SecretaryPermissions;
  isUsed: boolean;
  usedBy?: string; // Secretary UID who used the code
  expiresAt: Date;
  createdAt: Date;
  usedAt?: Date;
}

export interface LawyerSecretaryLink {
  id?: string;
  lawyerId: string;
  secretaryId: string;
  lawyerName: string;
  secretaryName: string;
  status: 'pending' | 'approved' | 'rejected';
  permissions: SecretaryPermissions;
  requestedAt: Date;
  respondedAt?: Date;
  createdAt: Date;
}

export interface LawyerProfile {
  uid: string;
  email: string;
  name: string;
  rollNumber: string;
  barId: string;
  phone?: string;
  avatar?: string;
  role: 'lawyer';
  secretaryCode?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface AuditLog {
  id?: string;
  userId: string;
  userRole: 'lawyer' | 'secretary' | 'client';
  userName: string;
  action: string;
  entityType: string;
  entityId: string;
  changes?: any;
  metadata?: any;
  timestamp: Date;
}

@Injectable({
  providedIn: 'root'
})
export class FirebaseService {
  private app = initializeApp(environment.firebase);
  private auth: Auth = getAuth(this.app);
  private firestore: Firestore = getFirestore(this.app);
  private functions = getFunctions(this.app);

  constructor() { }

  // Authentication Methods
  async signIn(email: string, password: string): Promise<User> {
    const userCredential = await signInWithEmailAndPassword(this.auth, email, password);
    return userCredential.user;
  }

  async signUp(email: string, password: string, userData: any): Promise<User> {
    const userCredential = await createUserWithEmailAndPassword(this.auth, email, password);
    return userCredential.user;
  }

  async signOut(): Promise<void> {
    await signOut(this.auth);
  }

  async resetPassword(email: string): Promise<void> {
    await sendPasswordResetEmail(this.auth, email);
  }

  getCurrentUser(): User | null {
    return this.auth.currentUser;
  }

  // Secretary Profile Methods
  async createSecretaryProfile(profile: SecretaryProfile): Promise<void> {
    const docRef = doc(this.firestore, 'secretaries', profile.uid);
    await setDoc(docRef, profile);
    await this.logActivity(profile.uid, 'secretary', profile.name, 'CREATE', 'secretary_profile', profile.uid);
  }

  async getSecretaryProfile(uid: string): Promise<SecretaryProfile | null> {
    const docRef = doc(this.firestore, 'secretaries', uid);
    const docSnap = await getDoc(docRef);

    if (docSnap.exists()) {
      return docSnap.data() as SecretaryProfile;
    }
    return null;
  }

  async updateSecretaryProfile(uid: string, updates: Partial<SecretaryProfile>): Promise<void> {
    const docRef = doc(this.firestore, 'secretaries', uid);
    await updateDoc(docRef, { ...updates, updatedAt: new Date() });

    const secretary = await this.getSecretaryProfile(uid);
    if (secretary) {
      await this.logActivity(uid, 'secretary', secretary.name, 'UPDATE', 'secretary_profile', uid, updates);
    }
  }

  // Lawyer Profile Methods (for secretary access)
  async getLawyerProfile(uid: string): Promise<LawyerProfile | null> {
    const docRef = doc(this.firestore, 'lawyers', uid);
    const docSnap = await getDoc(docRef);

    if (docSnap.exists()) {
      return docSnap.data() as LawyerProfile;
    }
    return null;
  }

  async getSecretaryLinkedLawyers(secretaryId: string): Promise<LawyerProfile[]> {
    const secretary = await this.getSecretaryProfile(secretaryId);
    if (!secretary || secretary.linkedLawyers.length === 0) {
      return [];
    }

    const lawyers: LawyerProfile[] = [];
    for (const lawyerId of secretary.linkedLawyers) {
      const lawyer = await this.getLawyerProfile(lawyerId);
      if (lawyer) {
        lawyers.push(lawyer);
      }
    }
    return lawyers;
  }

  // Assistant Code Management Methods
  async validateAssistantCode(code: string): Promise<AssistantCode | null> {
    const q = query(
      collection(this.firestore, 'assistant_codes'),
      where('code', '==', code),
      where('isUsed', '==', false)
    );

    const querySnapshot = await getDocs(q);

    if (querySnapshot.empty) {
      return null;
    }

    const doc = querySnapshot.docs[0];
    const assistantCode = { id: doc.id, ...doc.data() } as AssistantCode;

    // Check if code has expired
    const expiryDate = assistantCode.expiresAt instanceof Date
      ? assistantCode.expiresAt
      : (assistantCode.expiresAt as any).toDate();

    if (new Date() > expiryDate) {
      return null;
    }

    return assistantCode;
  }

  async useAssistantCode(
    code: string,
    secretaryId: string
  ): Promise<LawyerSecretaryLink> {
    try {
      const linkSecretary = httpsCallable(this.functions, 'linkSecretary');
      const result = await linkSecretary({ code });

      if (result.data && (result.data as any).success) {
        return (result.data as any).link;
      } else {
        throw new Error('Failed to link secretary');
      }
    } catch (error) {
      console.error('Error calling linkSecretary function:', error);
      throw error;
    }
  }

  async getLinkedLawyers(secretaryId: string): Promise<LawyerSecretaryLink[]> {
    const q = query(
      collection(this.firestore, 'lawyer_secretary_links'),
      where('secretaryId', '==', secretaryId),
      where('status', '==', 'approved')
    );

    const querySnapshot = await getDocs(q);
    return querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    } as LawyerSecretaryLink));
  }

  // Secretary Dashboard Statistics
  async getSecretaryDashboardStats(secretaryId: string): Promise<any> {
    const secretary = await this.getSecretaryProfile(secretaryId);
    if (!secretary) return { linkedLawyers: 0, totalCases: 0, totalAppointments: 0 };

    let totalCases = 0;
    let totalAppointments = 0;

    // For now, return mock data since we don't have case/appointment collections set up
    // In a real implementation, you would query the cases and appointments collections
    for (const lawyerId of secretary.linkedLawyers) {
      // Mock data - replace with actual queries
      totalCases += Math.floor(Math.random() * 10) + 1;
      totalAppointments += Math.floor(Math.random() * 5) + 1;
    }

    return {
      linkedLawyers: secretary.linkedLawyers.length,
      totalCases,
      totalAppointments
    };
  }

  async checkSecretaryPermission(
    secretaryId: string,
    lawyerId: string,
    permission: keyof SecretaryPermissions
  ): Promise<boolean> {
    const q = query(
      collection(this.firestore, 'lawyer_secretary_links'),
      where('secretaryId', '==', secretaryId),
      where('lawyerId', '==', lawyerId),
      where('status', '==', 'approved')
    );

    const querySnapshot = await getDocs(q);

    if (querySnapshot.empty) {
      return false;
    }

    const link = querySnapshot.docs[0].data() as LawyerSecretaryLink;
    return link.permissions[permission] || false;
  }

  async getAuditLogs(
    entityType?: string,
    entityId?: string,
    userId?: string,
    limitCount: number = 50
  ): Promise<AuditLog[]> {
    let q = query(
      collection(this.firestore, 'audit_logs'),
      orderBy('timestamp', 'desc')
    );

    if (entityType) {
      q = query(q, where('entityType', '==', entityType));
    }

    if (entityId) {
      q = query(q, where('entityId', '==', entityId));
    }

    if (userId) {
      q = query(q, where('userId', '==', userId));
    }

    const querySnapshot = await getDocs(q);
    return querySnapshot.docs.slice(0, limitCount).map(doc => ({
      id: doc.id,
      ...doc.data()
    } as AuditLog));
  }

  // Lawyer Availability Management Methods
  async getLawyerAvailability(lawyerId: string, date?: string): Promise<LawyerAvailability[]> {
    let q = query(
      collection(this.firestore, 'availability'),
      where('lawyerId', '==', lawyerId)
    );

    if (date) {
      q = query(q, where('date', '==', date));
    }

    q = query(q, orderBy('date', 'asc'));

    const querySnapshot = await getDocs(q);
    return querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    } as LawyerAvailability));
  }

  async createLawyerAvailability(availability: Omit<LawyerAvailability, 'id'>): Promise<string> {
    const docRef = await addDoc(collection(this.firestore, 'availability'), {
      ...availability,
      createdAt: new Date(),
      updatedAt: new Date()
    });

    // Log activity
    const secretary = await this.getSecretaryProfile(availability.createdBy);
    if (secretary) {
      await this.logActivity(
        availability.createdBy,
        'secretary',
        secretary.name,
        'CREATE',
        'availability',
        docRef.id,
        { lawyerId: availability.lawyerId, date: availability.date, timeSlots: availability.timeSlots }
      );
    }

    return docRef.id;
  }

  async updateLawyerAvailability(
    availabilityId: string,
    updates: Partial<LawyerAvailability>,
    updatedBy: string
  ): Promise<void> {
    const docRef = doc(this.firestore, 'availability', availabilityId);
    await updateDoc(docRef, { ...updates, updatedAt: new Date() });

    // Log activity
    const secretary = await this.getSecretaryProfile(updatedBy);
    if (secretary) {
      await this.logActivity(
        updatedBy,
        'secretary',
        secretary.name,
        'UPDATE',
        'availability',
        availabilityId,
        updates
      );
    }
  }

  async deleteLawyerAvailability(availabilityId: string, deletedBy: string): Promise<void> {
    const docRef = doc(this.firestore, 'availability', availabilityId);
    await deleteDoc(docRef);

    // Log activity
    const secretary = await this.getSecretaryProfile(deletedBy);
    if (secretary) {
      await this.logActivity(
        deletedBy,
        'secretary',
        secretary.name,
        'DELETE',
        'availability',
        availabilityId
      );
    }
  }

  // Enhanced Appointment Management Methods
  async createAppointment(appointmentData: Omit<EnhancedAppointment, 'id'>): Promise<string> {
    const docRef = await addDoc(collection(this.firestore, 'appointments'), {
      ...appointmentData,
      createdAt: new Date(),
      updatedAt: new Date()
    });

    // Log the activity
    await this.logActivity(
      appointmentData.managedBy || appointmentData.lawyerId,
      appointmentData.createdBy,
      appointmentData.clientName,
      'CREATE',
      'appointment',
      docRef.id,
      {
        lawyerName: appointmentData.lawyerName,
        date: appointmentData.date,
        time: appointmentData.time,
        type: appointmentData.type
      }
    );

    return docRef.id;
  }

  async updateAppointment(appointmentId: string, updates: Partial<EnhancedAppointment>): Promise<void> {
    const docRef = doc(this.firestore, 'appointments', appointmentId);
    await updateDoc(docRef, {
      ...updates,
      updatedAt: new Date()
    });

    // Log the activity
    if (updates.lastModifiedBy) {
      await this.logActivity(
        updates.lastModifiedBy,
        updates.lastModifiedByRole || 'secretary',
        updates.clientName || 'Unknown',
        'UPDATE',
        'appointment',
        appointmentId,
        updates
      );
    }
  }

  async deleteAppointment(appointmentId: string, deletedBy: string, deletedByRole: 'lawyer' | 'secretary' | 'client'): Promise<void> {
    const docRef = doc(this.firestore, 'appointments', appointmentId);

    // Get appointment details for logging
    const docSnap = await getDoc(docRef);
    const appointment = docSnap.data() as EnhancedAppointment;

    await deleteDoc(docRef);

    // Log the activity
    await this.logActivity(
      deletedBy,
      deletedByRole,
      appointment.clientName,
      'DELETE',
      'appointment',
      appointmentId,
      {
        lawyerName: appointment.lawyerName,
        date: appointment.date,
        time: appointment.time,
        type: appointment.type
      }
    );
  }

  async getAppointmentsForSecretary(
    secretaryId: string,
    filter?: AppointmentFilter
  ): Promise<EnhancedAppointment[]> {
    const secretary = await this.getSecretaryProfile(secretaryId);
    if (!secretary || secretary.linkedLawyers.length === 0) {
      return [];
    }

    let q = query(
      collection(this.firestore, 'appointments'),
      where('lawyerId', 'in', secretary.linkedLawyers)
    );

    if (filter?.status && filter.status.length > 0) {
      q = query(q, where('status', 'in', filter.status));
    }

    if (filter?.dateFrom) {
      q = query(q, where('date', '>=', filter.dateFrom));
    }

    if (filter?.dateTo) {
      q = query(q, where('date', '<=', filter.dateTo));
    }

    q = query(q, orderBy('date', 'asc'), orderBy('time', 'asc'));

    const querySnapshot = await getDocs(q);
    let appointments = querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    } as EnhancedAppointment));

    // Apply additional filters that can't be done in Firestore query
    if (filter?.type && filter.type.length > 0) {
      appointments = appointments.filter(apt => filter.type!.includes(apt.type));
    }

    if (filter?.isUrgent !== undefined) {
      appointments = appointments.filter(apt => apt.isUrgent === filter.isUrgent);
    }

    if (filter?.createdBy && filter.createdBy.length > 0) {
      appointments = appointments.filter(apt => filter.createdBy!.includes(apt.createdBy));
    }

    return appointments;
  }

  async getAppointmentsByLawyer(lawyerId: string, dateFrom?: string, dateTo?: string): Promise<EnhancedAppointment[]> {
    let q = query(
      collection(this.firestore, 'appointments'),
      where('lawyerId', '==', lawyerId)
    );

    if (dateFrom) {
      q = query(q, where('date', '>=', dateFrom));
    }

    if (dateTo) {
      q = query(q, where('date', '<=', dateTo));
    }

    q = query(q, orderBy('date', 'asc'), orderBy('time', 'asc'));

    const querySnapshot = await getDocs(q);
    return querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    } as EnhancedAppointment));
  }

  async updateAppointmentStatus(
    appointmentId: string,
    status: string,
    updatedBy: string,
    reason?: string
  ): Promise<void> {
    const updates: any = {
      status,
      lastModifiedBy: updatedBy,
      lastModifiedByRole: 'secretary',
      updatedAt: new Date()
    };

    if (reason) {
      updates.remarks = reason;
    }

    const docRef = doc(this.firestore, 'appointments', appointmentId);
    await updateDoc(docRef, updates);

    // Log activity
    const secretary = await this.getSecretaryProfile(updatedBy);
    if (secretary) {
      await this.logActivity(
        updatedBy,
        'secretary',
        secretary.name,
        'UPDATE',
        'appointment',
        appointmentId,
        { status, reason }
      );
    }
  }

  // Reschedule Request Management Methods
  async createRescheduleRequest(request: Omit<RescheduleRequest, 'id'>): Promise<string> {
    const docRef = await addDoc(collection(this.firestore, 'reschedule_requests'), {
      ...request,
      createdAt: new Date(),
      expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) // Expires in 7 days
    });

    // Log activity
    await this.logActivity(
      request.requestedBy,
      'secretary',
      request.requestedByName,
      'CREATE',
      'reschedule_request',
      docRef.id,
      {
        appointmentId: request.appointmentId,
        originalDate: request.originalDate,
        proposedDate: request.proposedDate,
        reason: request.reason
      }
    );

    return docRef.id;
  }

  async getRescheduleRequests(appointmentId?: string): Promise<RescheduleRequest[]> {
    let q = query(collection(this.firestore, 'reschedule_requests'));

    if (appointmentId) {
      q = query(q, where('appointmentId', '==', appointmentId));
    }

    q = query(q, orderBy('createdAt', 'desc'));

    const querySnapshot = await getDocs(q);
    return querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    } as RescheduleRequest));
  }

  async updateRescheduleRequestStatus(
    requestId: string,
    status: 'approved' | 'rejected',
    respondedBy: string,
    response?: string
  ): Promise<void> {
    const updates: any = {
      status,
      respondedAt: new Date()
    };

    if (response) {
      updates.clientResponse = response;
    }

    const docRef = doc(this.firestore, 'reschedule_requests', requestId);
    await updateDoc(docRef, updates);

    // If approved, update the original appointment
    if (status === 'approved') {
      const request = await this.getRescheduleRequest(requestId);
      if (request) {
        await this.updateAppointmentFromReschedule(request);
      }
    }

    // Log activity
    await this.logActivity(
      respondedBy,
      'secretary',
      'Secretary',
      'UPDATE',
      'reschedule_request',
      requestId,
      { status, response }
    );
  }

  private async getRescheduleRequest(requestId: string): Promise<RescheduleRequest | null> {
    const docRef = doc(this.firestore, 'reschedule_requests', requestId);
    const docSnap = await getDoc(docRef);

    if (docSnap.exists()) {
      return { id: docSnap.id, ...docSnap.data() } as RescheduleRequest;
    }
    return null;
  }

  private async updateAppointmentFromReschedule(request: RescheduleRequest): Promise<void> {
    const updates = {
      originalDate: request.originalDate,
      originalTime: request.originalTime,
      date: request.proposedDate,
      time: request.proposedTime,
      status: 'rescheduled',
      rescheduleReason: request.reason,
      rescheduleRequestedBy: request.requestedBy,
      rescheduleRequestedAt: request.createdAt,
      updatedAt: new Date()
    };

    const docRef = doc(this.firestore, 'appointments', request.appointmentId);
    await updateDoc(docRef, updates);
  }

  // Calendar Summary Methods
  async getLawyerCalendarSummary(lawyerId: string, date: string): Promise<LawyerCalendarSummary> {
    const lawyer = await this.getLawyerProfile(lawyerId);
    const appointments = await this.getAppointmentsByLawyer(lawyerId, date, date);
    const availability = await this.getLawyerAvailability(lawyerId, date);

    const totalAppointments = appointments.length;
    const confirmedAppointments = appointments.filter(apt => apt.status === 'confirmed').length;
    const pendingAppointments = appointments.filter(apt => apt.status === 'pending').length;

    // Calculate available slots
    let availableSlots = 0;
    availability.forEach(avail => {
      const bookedSlots = appointments.filter(apt =>
        apt.date === avail.date && avail.timeSlots.includes(apt.time)
      ).length;
      availableSlots += Math.max(0, avail.timeSlots.length - bookedSlots);
    });

    // Find next appointment
    const futureAppointments = appointments.filter(apt =>
      new Date(apt.date + ' ' + apt.time) > new Date()
    ).sort((a, b) =>
      new Date(a.date + ' ' + a.time).getTime() - new Date(b.date + ' ' + b.time).getTime()
    );

    const nextAppointment = futureAppointments.length > 0 ? {
      date: futureAppointments[0].date,
      time: futureAppointments[0].time,
      clientName: futureAppointments[0].clientName
    } : undefined;

    return {
      lawyerId,
      lawyerName: lawyer?.name || 'Unknown',
      totalAppointments,
      confirmedAppointments,
      pendingAppointments,
      availableSlots,
      nextAppointment
    };
  }

  // Reminder Management Methods
  async createAppointmentReminder(reminder: Omit<AppointmentReminder, 'id'>): Promise<string> {
    const docRef = await addDoc(collection(this.firestore, 'appointment_reminders'), {
      ...reminder,
      createdAt: new Date()
    });
    return docRef.id;
  }

  async getPendingReminders(): Promise<AppointmentReminder[]> {
    const q = query(
      collection(this.firestore, 'appointment_reminders'),
      where('status', '==', 'pending'),
      where('scheduledFor', '<=', new Date())
    );

    const querySnapshot = await getDocs(q);
    return querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    } as AppointmentReminder));
  }

  async markReminderAsSent(reminderId: string): Promise<void> {
    const docRef = doc(this.firestore, 'appointment_reminders', reminderId);
    await updateDoc(docRef, {
      status: 'sent',
      sentAt: new Date()
    });
  }

  // Audit Logging Methods
  async logActivity(
    userId: string,
    userRole: 'lawyer' | 'secretary' | 'client',
    userName: string,
    action: string,
    entityType: string,
    entityId: string,
    changes?: any,
    metadata?: any
  ): Promise<void> {
    const auditLog: AuditLog = {
      userId,
      userRole,
      userName,
      action,
      entityType,
      entityId,
      changes,
      metadata,
      timestamp: new Date()
    };

    await addDoc(collection(this.firestore, 'audit_logs'), auditLog);
  }
}
