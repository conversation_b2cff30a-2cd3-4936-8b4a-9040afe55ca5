{"name": "veritus-secretary", "version": "0.0.1", "author": "Veritus Team", "homepage": "https://secretary.veritus.app/", "scripts": {"ng": "ng", "start": "ng serve --port 4201", "build": "ng build --output-path ../veritus-lawyer/dist-secretary", "watch": "ng build --watch --configuration development", "test": "ng test", "lint": "ng lint", "e2e": "ng e2e"}, "private": true, "dependencies": {"@angular/animations": "^17.0.0", "@angular/common": "^17.0.0", "@angular/compiler": "^17.0.0", "@angular/core": "^17.0.0", "@angular/fire": "^17.0.0", "@angular/forms": "^17.0.0", "@angular/platform-browser": "^17.0.0", "@angular/platform-browser-dynamic": "^17.0.0", "@angular/router": "^17.0.0", "@angular/material": "^17.0.0", "@angular/cdk": "^17.0.0", "@ionic/angular": "^7.6.0", "@ionic/core": "^7.6.0", "firebase": "^10.7.0", "ionicons": "^7.2.0", "rxjs": "~7.8.0", "tslib": "^2.3.0", "zone.js": "~0.14.0"}, "devDependencies": {"@angular-devkit/build-angular": "^17.0.0", "@angular/cli": "^17.0.0", "@angular/compiler-cli": "^17.0.0", "@types/jasmine": "~5.1.0", "@types/node": "^18.18.0", "jasmine-core": "~5.1.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "typescript": "~5.2.0"}}