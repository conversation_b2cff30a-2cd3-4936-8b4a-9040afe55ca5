import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { FirebaseService, LawyerProfile } from '../services/firebase.service';

interface StatCard {
  title: string;
  value: number;
  icon: string;
  color: string;
}

interface Appointment {
  id: string;
  type: string;
  clientName: string;
  status: 'new' | 'confirmed' | 'completed' | 'cancelled';
  date: string;
  time: string;
}

interface CaseFile {
  id: string;
  title: string;
  fileCount: number;
  icon: string;
}

@Component({
  selector: 'app-dashboard',
  templateUrl: './dashboard.page.html',
  styleUrls: ['./dashboard.page.scss'],
  standalone: false,
})
export class DashboardPage implements OnInit {
  lawyerName = '';
  lawyerProfile: LawyerProfile | null = null;
  notificationCount = 0;
  showCreateFolderModal = false;
  newFolderName = '';

  stats: StatCard[] = [
    { title: 'On going Cases', value: 0, icon: 'briefcase', color: 'primary' },
    { title: 'Completed Cases', value: 0, icon: 'checkmark-circle', color: 'success' }
  ];

  appointments: Appointment[] = [];
  caseFiles: CaseFile[] = [];

  constructor(
    private router: Router,
    private firebaseService: FirebaseService
  ) {}

  async ngOnInit() {
    const user = this.firebaseService.getCurrentUser();
    if (!user) return;

    await this.loadLawyerProfile(user.uid);
    await this.loadDashboardData(user.uid);
  }

  async ionViewWillEnter() {
    const user = this.firebaseService.getCurrentUser();
    if (!user) return;

    await this.loadDashboardData(user.uid); 
  }

  async loadLawyerProfile(uid: string) {
    try {
      const profile = await this.firebaseService.getLawyerProfile(uid);
      this.lawyerProfile = profile;

      if (profile?.name) {
        this.lawyerName = profile.name.startsWith('Atty.')
          ? profile.name
          : `Atty. ${profile.name}`;
      } else {
        const currentUser = this.firebaseService.getCurrentUser();
        this.lawyerName = currentUser?.email
          ? `Atty. ${currentUser.email.split('@')[0]}`
          : 'Atty. (Unnamed)';
      }
    } catch (error) {
      console.error('Failed to load lawyer profile:', error);
    }
  }

  async loadDashboardData(uid: string) {
    try {
      const stats = await this.firebaseService.getDashboardStats(uid);
      this.stats[0].value = stats.ongoingCases;
      this.stats[1].value = stats.completedCases;

      const appointments = await this.firebaseService.getAppointments(uid);
      this.appointments = appointments.slice(0, 2).map(a => ({
        id: a.id || 'unknown',
        type: a.type,
        clientName: a.clientName,
        status: a.status,
        date: a.date,
        time: a.time
      }));

      const cases = await this.firebaseService.getCases(uid);
      this.caseFiles = cases.map(c => ({
        id: c.id!,
        title: c.title,
        fileCount: c.fileCount || 0,
        icon: 'folder'
      }));
    } catch (error) {
      console.error('Error loading dashboard data:', error);
    }
  }

  onNotificationClick() {}

  onSeeAllAppointments() {}

  onAppointmentClick(appointment: Appointment) {}

  onCaseFileClick(caseFile: CaseFile) {
    this.router.navigate(['/case-files'], { queryParams: { caseId: caseFile.id } });
  }

  onCreateFolderClick() {
    this.newFolderName = '';
    this.showCreateFolderModal = true;
  }

  closeCreateFolderModal() {
    this.showCreateFolderModal = false;
  }

  async createDashboardFolder() {
    if (!this.newFolderName.trim()) return;

    const currentUser = this.firebaseService.getCurrentUser();
    if (!currentUser) return;

    const newCase = {
      lawyerId: currentUser.uid,
      clientName: '',
      title: this.newFolderName.trim(),
      status: 'ongoing' as const,
      description: '',
      fileCount: 0,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    try {
      await this.firebaseService.createCase(newCase);
      await this.loadDashboardData(currentUser.uid); 
      this.closeCreateFolderModal();
    } catch (error) {
      console.error('Error creating case folder:', error);
    }
  }

  async onDeleteFolder(folder: CaseFile, event: Event) {
    event.stopPropagation();

    const confirm = window.confirm(`Are you sure you want to delete "${folder.title}"?`);
    if (!confirm) return;

    const currentUser = this.firebaseService.getCurrentUser();
    if (!currentUser) return;

    try {
      await this.firebaseService.deleteCase(folder.id);
      await this.loadDashboardData(currentUser.uid);
    } catch (error) {
      console.error('Failed to delete case:', error);
    }
  }

  getStatusBadgeClass(status: string): string {
    switch (status) {
      case 'new': return 'badge-new';
      case 'confirmed': return 'badge-active';
      case 'completed': return 'badge-closed';
      default: return 'badge-new';
    }
  }

  getStatusText(status: string): string {
    switch (status) {
      case 'new': return 'New';
      case 'confirmed': return 'Confirmed';
      case 'completed': return 'Completed';
      default: return status;
    }
  }
}
