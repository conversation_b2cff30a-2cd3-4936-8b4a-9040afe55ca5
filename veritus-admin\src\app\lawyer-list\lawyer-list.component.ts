import { <PERSON>mpo<PERSON>, <PERSON>Ini<PERSON>, <PERSON><PERSON><PERSON><PERSON> } from '@angular/core';
import { Subscription } from 'rxjs';
import { FirebaseService, LawyerProfile } from '../services/firebase.service';

@Component({
  selector: 'app-lawyer-list',
  templateUrl: './lawyer-list.component.html',
  styleUrls: ['./lawyer-list.component.scss']
})
export class LawyerListComponent implements OnInit, OnDestroy {
  lawyers: LawyerProfile[] = [];
  filteredLawyers: LawyerProfile[] = [];
  searchQuery = '';
  statusFilter = '';
  loading = true;
  error: string | null = null;
  private subscriptions: Subscription[] = [];

  constructor(private firebaseService: FirebaseService) { }

  ngOnInit(): void {
    this.loadLawyers();
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach(sub => sub.unsubscribe());
  }

  loadLawyers(): void {
    this.loading = true;
    this.error = null;

    const lawyersSubscription = this.firebaseService.getAllLawyers().subscribe({
      next: (lawyers) => {
        this.lawyers = lawyers;
        this.filterLawyers();
        this.loading = false;
      },
      error: (error) => {
        console.error('Error loading lawyers:', error);
        this.error = 'Failed to load lawyers. Please try again.';
        this.loading = false;
      }
    });

    this.subscriptions.push(lawyersSubscription);
  }

  filterLawyers(): void {
    let filtered = this.lawyers;

    if (this.searchQuery.trim()) {
      const query = this.searchQuery.toLowerCase();
      filtered = filtered.filter(lawyer =>
        (lawyer.name || '').toLowerCase().includes(query) ||
        (lawyer.rollNumber || '').toLowerCase().includes(query) ||
        (lawyer.firm || '').toLowerCase().includes(query) ||
        (lawyer.email || '').toLowerCase().includes(query)
      );
    }

    if (this.statusFilter) {
      filtered = filtered.filter(lawyer => lawyer.status === this.statusFilter);
    }

    this.filteredLawyers = filtered;
  }

  viewLawyer(lawyer: LawyerProfile): void {
    console.log('Viewing lawyer:', lawyer.name);
    // TODO: Navigate to lawyer detail view
  }

  editLawyer(lawyer: LawyerProfile): void {
    console.log('Editing lawyer:', lawyer.name);
    // TODO: Navigate to lawyer edit form
  }

  async toggleLawyerStatus(lawyer: LawyerProfile): Promise<void> {
    const newStatus = lawyer.status === 'verified' ? 'suspended' : 'verified';

    try {
      await this.firebaseService.updateLawyerStatus(lawyer.uid, newStatus);

      // Log the admin action
      await this.firebaseService.logAdminAction(
        'STATUS_CHANGE',
        'lawyer',
        lawyer.uid,
        {
          oldStatus: lawyer.status,
          newStatus: newStatus,
          lawyerName: lawyer.name,
          lawyerEmail: lawyer.email
        }
      );

      console.log(`Lawyer ${lawyer.name} status changed to ${newStatus}`);
    } catch (error) {
      console.error('Error updating lawyer status:', error);
      this.error = 'Failed to update lawyer status. Please try again.';
    }
  }

  getInitials(name: string): string {
    if (!name) return '?';
    return name
      .split(' ')
      .map(word => word.charAt(0).toUpperCase())
      .slice(0, 2)
      .join('');
  }

  onImageError(event: any, lawyer: any): void {
    // Hide the broken image and show placeholder instead
    event.target.style.display = 'none';
    lawyer.avatar = undefined;
  }
}
