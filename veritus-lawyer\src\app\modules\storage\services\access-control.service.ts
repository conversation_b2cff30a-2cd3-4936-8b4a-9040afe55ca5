import { Injectable } from '@angular/core';
import { Firestore, doc, getDoc } from '@angular/fire/firestore';

export interface AccessPermissions {
  canRead: boolean;
  canWrite: boolean;
  canDelete: boolean;
  canShare: boolean;
  canArchive: boolean;
}

@Injectable({
  providedIn: 'root'
})
export class AccessControlService {

  constructor(private firestore: Firestore) { }

  // Get user role
  async getUserRole(userId: string): Promise<'client' | 'lawyer' | 'secretary' | null> {
    const userDoc = await getDoc(doc(this.firestore, 'users', userId));
    if (userDoc.exists()) {
      return userDoc.data()['role'] || null;
    }
    return null;
  }

  // Get linked lawyers for secretary
  async getLinkedLawyers(secretaryId: string): Promise<string[]> {
    const secretaryDoc = await getDoc(doc(this.firestore, 'secretaries', secretaryId));
    if (secretaryDoc.exists()) {
      return secretaryDoc.data()['linkedLawyers'] || [];
    }
    return [];
  }

  // Check if user can upload file
  async canUploadFile(
    userId: string,
    caseId?: string,
    clientId?: string,
    lawyerId?: string
  ): Promise<boolean> {
    const userRole = await this.getUserRole(userId);
    
    switch (userRole) {
      case 'client':
        // Clients can only upload to their own cases
        return clientId === userId;
        
      case 'lawyer':
        // Lawyers can upload to their own cases
        return lawyerId === userId;
        
      case 'secretary':
        // Secretaries can upload if they have permission and are linked to the lawyer
        if (lawyerId) {
          const linkedLawyers = await this.getLinkedLawyers(userId);
          const isLinked = linkedLawyers.includes(lawyerId);
          const hasPermission = await this.getSecretaryPermission(userId, 'canManageFiles');
          return isLinked && hasPermission;
        }
        return false;
        
      default:
        return false;
    }
  }

  // Check if user can download file
  async canDownloadFile(userId: string, fileId: string): Promise<boolean> {
    const fileDoc = await getDoc(doc(this.firestore, 'encrypted-files', fileId));
    if (!fileDoc.exists()) {
      return false;
    }

    const file = fileDoc.data();
    const userRole = await this.getUserRole(userId);

    switch (userRole) {
      case 'client':
        // Clients can download their own files or files shared with them
        return file['clientId'] === userId || file['uploadedBy'] === userId;
        
      case 'lawyer':
        // Lawyers can download files from their cases
        return file['lawyerId'] === userId || file['uploadedBy'] === userId;
        
      case 'secretary':
        // Secretaries can download if they have permission and are linked to the lawyer
        if (file['lawyerId']) {
          const linkedLawyers = await this.getLinkedLawyers(userId);
          const isLinked = linkedLawyers.includes(file['lawyerId']);
          const hasPermission = await this.getSecretaryPermission(userId, 'canManageFiles');
          return isLinked && hasPermission;
        }
        return false;
        
      default:
        return false;
    }
  }

  // Check if user can delete file
  async canDeleteFile(userId: string, fileId: string): Promise<boolean> {
    const fileDoc = await getDoc(doc(this.firestore, 'encrypted-files', fileId));
    if (!fileDoc.exists()) {
      return false;
    }

    const file = fileDoc.data();
    const userRole = await this.getUserRole(userId);

    switch (userRole) {
      case 'client':
        // Clients can only delete files they uploaded
        return file['uploadedBy'] === userId;
        
      case 'lawyer':
        // Lawyers can delete files from their cases or files they uploaded
        return file['lawyerId'] === userId || file['uploadedBy'] === userId;
        
      case 'secretary':
        // Secretaries can delete if they have permission and are linked to the lawyer
        if (file['lawyerId']) {
          const linkedLawyers = await this.getLinkedLawyers(userId);
          const isLinked = linkedLawyers.includes(file['lawyerId']);
          const hasPermission = await this.getSecretaryPermission(userId, 'canManageFiles');
          return isLinked && hasPermission;
        }
        return false;
        
      default:
        return false;
    }
  }

  // Check if user can move file
  async canMoveFile(
    userId: string,
    fileId: string,
    newLocation: {
      caseId?: string;
      clientId?: string;
      lawyerId?: string;
    }
  ): Promise<boolean> {
    // First check if user can delete from current location
    const canDelete = await this.canDeleteFile(userId, fileId);
    if (!canDelete) {
      return false;
    }

    // Then check if user can upload to new location
    return this.canUploadFile(
      userId,
      newLocation.caseId,
      newLocation.clientId,
      newLocation.lawyerId
    );
  }

  // Check if user can share file
  async canShareFile(userId: string, fileId: string): Promise<boolean> {
    const fileDoc = await getDoc(doc(this.firestore, 'encrypted-files', fileId));
    if (!fileDoc.exists()) {
      return false;
    }

    const file = fileDoc.data();
    const userRole = await this.getUserRole(userId);

    switch (userRole) {
      case 'client':
        // Clients can share files they uploaded
        return file['uploadedBy'] === userId;
        
      case 'lawyer':
        // Lawyers can share files from their cases
        return file['lawyerId'] === userId || file['uploadedBy'] === userId;
        
      case 'secretary':
        // Secretaries can share if they have permission
        if (file['lawyerId']) {
          const linkedLawyers = await this.getLinkedLawyers(userId);
          const isLinked = linkedLawyers.includes(file['lawyerId']);
          const hasPermission = await this.getSecretaryPermission(userId, 'canManageFiles');
          return isLinked && hasPermission;
        }
        return false;
        
      default:
        return false;
    }
  }

  // Check if user can archive case
  async canArchiveCase(userId: string, caseId: string): Promise<boolean> {
    const userRole = await this.getUserRole(userId);
    
    switch (userRole) {
      case 'lawyer':
        // Lawyers can archive their own cases
        const caseDoc = await getDoc(doc(this.firestore, 'cases', caseId));
        if (caseDoc.exists()) {
          return caseDoc.data()['lawyerId'] === userId;
        }
        return false;
        
      case 'secretary':
        // Secretaries can archive if they have permission and are linked to the lawyer
        const caseDoc2 = await getDoc(doc(this.firestore, 'cases', caseId));
        if (caseDoc2.exists()) {
          const lawyerId = caseDoc2.data()['lawyerId'];
          const linkedLawyers = await this.getLinkedLawyers(userId);
          const isLinked = linkedLawyers.includes(lawyerId);
          const hasPermission = await this.getSecretaryPermission(userId, 'canManageCases');
          return isLinked && hasPermission;
        }
        return false;
        
      default:
        return false;
    }
  }

  // Get secretary permission
  async getSecretaryPermission(secretaryId: string, permission: string): Promise<boolean> {
    const secretaryDoc = await getDoc(doc(this.firestore, 'secretaries', secretaryId));
    if (secretaryDoc.exists()) {
      const permissions = secretaryDoc.data()['permissions'] || {};
      return permissions[permission] || false;
    }
    return false;
  }

  // Get user permissions for a specific resource
  async getUserPermissions(
    userId: string,
    resourceType: 'file' | 'case' | 'client',
    resourceId: string
  ): Promise<AccessPermissions> {
    
    const defaultPermissions: AccessPermissions = {
      canRead: false,
      canWrite: false,
      canDelete: false,
      canShare: false,
      canArchive: false
    };

    const userRole = await this.getUserRole(userId);
    if (!userRole) {
      return defaultPermissions;
    }

    switch (resourceType) {
      case 'file':
        return {
          canRead: await this.canDownloadFile(userId, resourceId),
          canWrite: await this.canUploadFile(userId), // Simplified check
          canDelete: await this.canDeleteFile(userId, resourceId),
          canShare: await this.canShareFile(userId, resourceId),
          canArchive: false // Files are archived as part of cases
        };
        
      case 'case':
        return {
          canRead: await this.canViewCase(userId, resourceId),
          canWrite: await this.canEditCase(userId, resourceId),
          canDelete: await this.canDeleteCase(userId, resourceId),
          canShare: await this.canShareCase(userId, resourceId),
          canArchive: await this.canArchiveCase(userId, resourceId)
        };
        
      default:
        return defaultPermissions;
    }
  }

  // Additional permission checks for cases
  private async canViewCase(userId: string, caseId: string): Promise<boolean> {
    const caseDoc = await getDoc(doc(this.firestore, 'cases', caseId));
    if (!caseDoc.exists()) {
      return false;
    }

    const caseData = caseDoc.data();
    const userRole = await this.getUserRole(userId);

    switch (userRole) {
      case 'client':
        return caseData['clientId'] === userId;
      case 'lawyer':
        return caseData['lawyerId'] === userId;
      case 'secretary':
        const linkedLawyers = await this.getLinkedLawyers(userId);
        return linkedLawyers.includes(caseData['lawyerId']);
      default:
        return false;
    }
  }

  private async canEditCase(userId: string, caseId: string): Promise<boolean> {
    const caseDoc = await getDoc(doc(this.firestore, 'cases', caseId));
    if (!caseDoc.exists()) {
      return false;
    }

    const caseData = caseDoc.data();
    const userRole = await this.getUserRole(userId);

    switch (userRole) {
      case 'lawyer':
        return caseData['lawyerId'] === userId;
      case 'secretary':
        const linkedLawyers = await this.getLinkedLawyers(userId);
        const isLinked = linkedLawyers.includes(caseData['lawyerId']);
        const hasPermission = await this.getSecretaryPermission(userId, 'canManageCases');
        return isLinked && hasPermission;
      default:
        return false;
    }
  }

  private async canDeleteCase(userId: string, caseId: string): Promise<boolean> {
    const caseDoc = await getDoc(doc(this.firestore, 'cases', caseId));
    if (!caseDoc.exists()) {
      return false;
    }

    const caseData = caseDoc.data();
    const userRole = await this.getUserRole(userId);

    // Only lawyers can delete their own cases
    return userRole === 'lawyer' && caseData['lawyerId'] === userId;
  }

  private async canShareCase(userId: string, caseId: string): Promise<boolean> {
    // Similar to edit permissions
    return this.canEditCase(userId, caseId);
  }
}
