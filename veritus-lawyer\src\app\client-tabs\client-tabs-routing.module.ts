import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';

import { ClientTabsPage } from './client-tabs.page';

const routes: Routes = [
  {
    path: '',
    component: ClientTabsPage,
    children: [
      {
        path: 'home',
        loadChildren: () => import('../client-home/client-home.module').then(m => m.ClientHomePageModule)
      },
      {
        path: 'search',
        loadChildren: () => import('../client-search/client-search.module').then(m => m.ClientSearchPageModule)
      },
      {
        path: 'documents',
        loadChildren: () => import('../client-documents/client-documents.module').then(m => m.ClientDocumentsPageModule)
      },
      {
        path: 'appointments',
        loadChildren: () => import('../client-appointments/client-appointments.module').then(m => m.ClientAppointmentsPageModule)
      },
      {
        path: 'profile',
        loadChildren: () => import('../client-profile/client-profile.module').then(m => m.ClientProfilePageModule)
      },
      {
        path: '',
        redirectTo: '/client-tabs/home',
        pathMatch: 'full'
      }
    ]
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class ClientTabsPageRoutingModule {}
