import { Component, OnInit } from '@angular/core';

interface Client {
  id: string;
  name: string;
  email: string;
  phone: string;
  status: 'active' | 'inactive' | 'pending';
  retainerAmount: number;
  joinDate: Date;
  lastContact: Date;
  caseCount: number;
}

@Component({
  selector: 'app-clients',
  template: `
    <div class="clients-container">
      <div class="clients-header">
        <h1>Retainer Clients</h1>
        <button mat-raised-button color="primary" (click)="openAddClientDialog()">
          <mat-icon>add</mat-icon>
          Add Client
        </button>
      </div>

      <div class="clients-content">
        <!-- Client Statistics -->
        <div class="stats-section">
          <div class="stat-card">
            <div class="stat-icon active">
              <mat-icon>person</mat-icon>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ getClientCount('active') }}</div>
              <div class="stat-label">Active Clients</div>
            </div>
          </div>

          <div class="stat-card">
            <div class="stat-icon pending">
              <mat-icon>schedule</mat-icon>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ getClientCount('pending') }}</div>
              <div class="stat-label">Pending Clients</div>
            </div>
          </div>

          <div class="stat-card">
            <div class="stat-icon revenue">
              <mat-icon>account_balance_wallet</mat-icon>
            </div>
            <div class="stat-content">
              <div class="stat-value">₱{{ getTotalRetainer() | number }}</div>
              <div class="stat-label">Total Retainer</div>
            </div>
          </div>
        </div>

        <!-- Clients List -->
        <div class="clients-list-section">
          <div class="list-header">
            <h3>Client List</h3>
            <div class="list-controls">
              <mat-form-field appearance="outline" class="search-field">
                <mat-icon matPrefix>search</mat-icon>
                <input matInput placeholder="Search clients" [(ngModel)]="searchTerm" (input)="filterClients()">
              </mat-form-field>
              <mat-form-field appearance="outline" class="filter-field">
                <mat-select [(value)]="statusFilter" (selectionChange)="filterClients()">
                  <mat-option value="">All Status</mat-option>
                  <mat-option value="active">Active</mat-option>
                  <mat-option value="pending">Pending</mat-option>
                  <mat-option value="inactive">Inactive</mat-option>
                </mat-select>
              </mat-form-field>
            </div>
          </div>

          <div class="clients-grid">
            <div class="client-card" *ngFor="let client of filteredClients">
              <div class="client-header">
                <div class="client-avatar">
                  <mat-icon>person</mat-icon>
                </div>
                <div class="client-status" [class]="client.status">
                  <div class="status-dot"></div>
                  <span>{{ client.status | titlecase }}</span>
                </div>
              </div>

              <div class="client-info">
                <h4 class="client-name">{{ client.name }}</h4>
                <div class="client-contact">
                  <div class="contact-item">
                    <mat-icon>email</mat-icon>
                    <span>{{ client.email }}</span>
                  </div>
                  <div class="contact-item">
                    <mat-icon>phone</mat-icon>
                    <span>{{ client.phone }}</span>
                  </div>
                </div>
              </div>

              <div class="client-details">
                <div class="detail-row">
                  <span class="detail-label">Retainer:</span>
                  <span class="detail-value">₱{{ client.retainerAmount | number }}</span>
                </div>
                <div class="detail-row">
                  <span class="detail-label">Cases:</span>
                  <span class="detail-value">{{ client.caseCount }}</span>
                </div>
                <div class="detail-row">
                  <span class="detail-label">Joined:</span>
                  <span class="detail-value">{{ client.joinDate | date:'MMM yyyy' }}</span>
                </div>
                <div class="detail-row">
                  <span class="detail-label">Last Contact:</span>
                  <span class="detail-value">{{ getTimeAgo(client.lastContact) }}</span>
                </div>
              </div>

              <div class="client-actions">
                <button mat-icon-button (click)="viewClient(client)">
                  <mat-icon>visibility</mat-icon>
                </button>
                <button mat-icon-button (click)="editClient(client)">
                  <mat-icon>edit</mat-icon>
                </button>
                <button mat-icon-button (click)="contactClient(client)">
                  <mat-icon>message</mat-icon>
                </button>
                <button mat-icon-button [matMenuTriggerFor]="clientMenu">
                  <mat-icon>more_vert</mat-icon>
                </button>
                <mat-menu #clientMenu="matMenu">
                  <button mat-menu-item (click)="viewCases(client)">
                    <mat-icon>folder</mat-icon>
                    <span>View Cases</span>
                  </button>
                  <button mat-menu-item (click)="viewTransactions(client)">
                    <mat-icon>receipt</mat-icon>
                    <span>View Transactions</span>
                  </button>
                  <button mat-menu-item (click)="deactivateClient(client)">
                    <mat-icon>block</mat-icon>
                    <span>Deactivate</span>
                  </button>
                </mat-menu>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .clients-container {
      padding: 32px;
      background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
      min-height: calc(100vh - 64px);
      position: relative;
    }

    .clients-container::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background:
        radial-gradient(circle at 20% 20%, rgba(59, 130, 246, 0.05) 0%, transparent 50%),
        radial-gradient(circle at 80% 80%, rgba(196, 154, 86, 0.03) 0%, transparent 50%);
      pointer-events: none;
    }

    .clients-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 40px;
      position: relative;
      z-index: 1;
    }

    .clients-header h1 {
      margin: 0;
      font-size: 36px;
      font-weight: 700;
      color: #1e293b;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      position: relative;
    }

    .clients-header h1::after {
      content: '';
      position: absolute;
      bottom: -8px;
      left: 0;
      width: 80px;
      height: 4px;
      background: linear-gradient(90deg, #3b82f6 0%, #2563eb 100%);
      border-radius: 2px;
    }

    .clients-content {
      display: flex;
      flex-direction: column;
      gap: 24px;
    }

    .stats-section {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 20px;
    }

    .stat-card {
      background: white;
      border-radius: 12px;
      padding: 24px;
      box-shadow: 0 2px 8px rgba(0,0,0,0.1);
      display: flex;
      align-items: center;
      gap: 16px;
    }

    .stat-icon {
      width: 60px;
      height: 60px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .stat-icon.active {
      background: #e8f5e8;
      color: #2e7d32;
    }

    .stat-icon.pending {
      background: #fff3e0;
      color: #f57c00;
    }

    .stat-icon.revenue {
      background: #e3f2fd;
      color: #1976d2;
    }

    .stat-icon mat-icon {
      font-size: 28px;
      width: 28px;
      height: 28px;
    }

    .stat-content {
      flex: 1;
    }

    .stat-value {
      font-size: 24px;
      font-weight: 600;
      color: #333;
      margin-bottom: 4px;
    }

    .stat-label {
      font-size: 14px;
      color: #666;
    }

    .clients-list-section {
      background: white;
      border-radius: 12px;
      padding: 24px;
      box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }

    .list-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 24px;
    }

    .list-header h3 {
      margin: 0;
      font-size: 18px;
      font-weight: 600;
    }

    .list-controls {
      display: flex;
      gap: 16px;
    }

    .search-field,
    .filter-field {
      width: 200px;
    }

    .clients-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
      gap: 20px;
    }

    .client-card {
      border: 1px solid rgba(226, 232, 240, 0.8);
      border-radius: 20px;
      padding: 28px;
      transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
      background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
      position: relative;
      overflow: hidden;
    }

    .client-card::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 4px;
      background: linear-gradient(90deg, #3b82f6 0%, #2563eb 100%);
      transform: scaleX(0);
      transition: transform 0.3s ease;
    }

    .client-card:hover::before {
      transform: scaleX(1);
    }

    .client-card:hover {
      border-color: #3b82f6;
      box-shadow:
        0 20px 60px rgba(59, 130, 246, 0.15),
        0 8px 24px rgba(59, 130, 246, 0.08);
      background: linear-gradient(135deg, #ffffff 0%, #f0f9ff 100%);
      transform: translateY(-8px);
    }

    .client-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
    }

    .client-avatar {
      width: 50px;
      height: 50px;
      border-radius: 50%;
      background: linear-gradient(135deg, #C49A56 0%, #B8935A 100%);
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
    }

    .client-status {
      display: flex;
      align-items: center;
      gap: 6px;
      padding: 4px 8px;
      border-radius: 12px;
      font-size: 11px;
      font-weight: 500;
      text-transform: uppercase;
    }

    .client-status.active {
      background: #e8f5e8;
      color: #2e7d32;
    }

    .client-status.pending {
      background: #fff3e0;
      color: #f57c00;
    }

    .client-status.inactive {
      background: #ffebee;
      color: #d32f2f;
    }

    .status-dot {
      width: 6px;
      height: 6px;
      border-radius: 50%;
      background: currentColor;
    }

    .client-info {
      margin-bottom: 16px;
    }

    .client-name {
      margin: 0 0 8px 0;
      font-size: 16px;
      font-weight: 600;
      color: #333;
    }

    .client-contact {
      display: flex;
      flex-direction: column;
      gap: 4px;
    }

    .contact-item {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 12px;
      color: #666;
    }

    .contact-item mat-icon {
      font-size: 14px;
      width: 14px;
      height: 14px;
    }

    .client-details {
      margin-bottom: 16px;
      padding-top: 16px;
      border-top: 1px solid #e0e0e0;
    }

    .detail-row {
      display: flex;
      justify-content: space-between;
      margin-bottom: 8px;
      font-size: 12px;
    }

    .detail-label {
      color: #666;
    }

    .detail-value {
      font-weight: 500;
      color: #333;
    }

    .client-actions {
      display: flex;
      justify-content: flex-end;
      gap: 4px;
      padding-top: 16px;
      border-top: 1px solid #e0e0e0;
    }

    .client-actions button {
      width: 32px;
      height: 32px;
    }

    .client-actions mat-icon {
      font-size: 16px;
      width: 16px;
      height: 16px;
    }

    @media (max-width: 768px) {
      .clients-header {
        flex-direction: column;
        gap: 16px;
        align-items: stretch;
      }

      .list-header {
        flex-direction: column;
        gap: 16px;
        align-items: stretch;
      }

      .list-controls {
        flex-direction: column;
      }

      .search-field,
      .filter-field {
        width: 100%;
      }

      .clients-grid {
        grid-template-columns: 1fr;
      }
    }
  `]
})
export class ClientsComponent implements OnInit {
  searchTerm = '';
  statusFilter = '';
  
  clients: Client[] = [
    {
      id: '1',
      name: 'Amado Cruz',
      email: '<EMAIL>',
      phone: '+63 ************',
      status: 'active',
      retainerAmount: 50000,
      joinDate: new Date(2024, 8, 15), // September 15, 2024
      lastContact: new Date(2025, 6, 10), // July 10, 2025
      caseCount: 3
    },
    {
      id: '2',
      name: 'Clara Mendoza',
      email: '<EMAIL>',
      phone: '+63 ************',
      status: 'active',
      retainerAmount: 75000,
      joinDate: new Date(2024, 10, 20), // November 20, 2024
      lastContact: new Date(2025, 6, 8), // July 8, 2025
      caseCount: 2
    },
    {
      id: '3',
      name: 'Kobe Bryant',
      email: '<EMAIL>',
      phone: '+63 ************',
      status: 'active',
      retainerAmount: 100000,
      joinDate: new Date(2024, 11, 5), // December 5, 2024
      lastContact: new Date(2025, 6, 12), // July 12, 2025
      caseCount: 1
    },
    {
      id: '4',
      name: 'Maria Santos',
      email: '<EMAIL>',
      phone: '+63 ************',
      status: 'pending',
      retainerAmount: 60000,
      joinDate: new Date(2025, 6, 1), // July 1, 2025
      lastContact: new Date(2025, 6, 1), // July 1, 2025
      caseCount: 0
    }
  ];

  filteredClients: Client[] = [];

  ngOnInit() {
    this.filteredClients = [...this.clients];
  }

  getClientCount(status: string): number {
    return this.clients.filter(client => client.status === status).length;
  }

  getTotalRetainer(): number {
    return this.clients
      .filter(client => client.status === 'active')
      .reduce((total, client) => total + client.retainerAmount, 0);
  }

  filterClients() {
    let filtered = [...this.clients];

    // Filter by search term
    if (this.searchTerm) {
      filtered = filtered.filter(client => 
        client.name.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
        client.email.toLowerCase().includes(this.searchTerm.toLowerCase())
      );
    }

    // Filter by status
    if (this.statusFilter) {
      filtered = filtered.filter(client => client.status === this.statusFilter);
    }

    this.filteredClients = filtered;
  }

  getTimeAgo(date: Date): string {
    const now = new Date();
    const diffTime = Math.abs(now.getTime() - date.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    if (diffDays === 1) {
      return 'Today';
    } else if (diffDays <= 7) {
      return `${diffDays} days ago`;
    } else {
      return date.toLocaleDateString();
    }
  }

  openAddClientDialog() {
    // Implement add client dialog
  }

  viewClient(client: Client) {
    // Implement view client details
  }

  editClient(client: Client) {
    // Implement edit client
  }

  contactClient(client: Client) {
    // Implement contact client
  }

  viewCases(client: Client) {
    // Implement view client cases
  }

  viewTransactions(client: Client) {
    // Implement view client transactions
  }

  deactivateClient(client: Client) {
    // Implement deactivate client
  }
}
