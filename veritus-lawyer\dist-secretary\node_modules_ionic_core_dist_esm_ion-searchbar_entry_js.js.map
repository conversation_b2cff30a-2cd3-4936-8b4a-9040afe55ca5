{"version": 3, "file": "node_modules_ionic_core_dist_esm_ion-searchbar_entry_js.js", "mappings": ";;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AAC+H;AACX;AACrE;AACe;AACqE;AACzD;AAE1E,MAAM+B,eAAe,GAAG,mzTAAmzT;AAC30T,MAAMC,qBAAqB,GAAGD,eAAe;AAE7C,MAAME,cAAc,GAAG,63QAA63Q;AACp5Q,MAAMC,oBAAoB,GAAGD,cAAc;AAE3C,MAAME,SAAS,GAAG,MAAM;EACpBC,WAAWA,CAACC,OAAO,EAAE;IAAA,IAAAC,KAAA;IACjBrC,qDAAgB,CAAC,IAAI,EAAEoC,OAAO,CAAC;IAC/B,IAAI,CAACE,QAAQ,GAAGpC,qDAAW,CAAC,IAAI,EAAE,UAAU,EAAE,CAAC,CAAC;IAChD,IAAI,CAACqC,SAAS,GAAGrC,qDAAW,CAAC,IAAI,EAAE,WAAW,EAAE,CAAC,CAAC;IAClD,IAAI,CAACsC,SAAS,GAAGtC,qDAAW,CAAC,IAAI,EAAE,WAAW,EAAE,CAAC,CAAC;IAClD,IAAI,CAACuC,QAAQ,GAAGvC,qDAAW,CAAC,IAAI,EAAE,UAAU,EAAE,CAAC,CAAC;IAChD,IAAI,CAACwC,OAAO,GAAGxC,qDAAW,CAAC,IAAI,EAAE,SAAS,EAAE,CAAC,CAAC;IAC9C,IAAI,CAACyC,QAAQ,GAAGzC,qDAAW,CAAC,IAAI,EAAE,UAAU,EAAE,CAAC,CAAC;IAChD,IAAI,CAAC0C,QAAQ,GAAG1C,qDAAW,CAAC,IAAI,EAAE,UAAU,EAAE,CAAC,CAAC;IAChD,IAAI,CAAC2C,eAAe,GAAG,KAAK;IAC5B,IAAI,CAACC,eAAe,GAAG,IAAI;IAC3B,IAAI,CAACC,OAAO,GAAG,iBAAiBC,YAAY,EAAE,EAAE;IAChD,IAAI,CAACC,mBAAmB,GAAG,CAAC,CAAC;IAC7B;AACR;AACA;IACQ,IAAI,CAACC,YAAY;MAAA,IAAAC,IAAA,GAAAC,6KAAA,CAAG,WAAOC,WAAW,EAAK;QACvChB,KAAI,CAACI,QAAQ,CAACa,IAAI,CAAC,CAAC;QACpB,OAAO,IAAIC,OAAO,CAAEC,OAAO,IAAK;UAC5B;UACA;UACAC,UAAU,CAAC,MAAM;YACb,MAAMC,KAAK,GAAGrB,KAAI,CAACsB,QAAQ,CAAC,CAAC;YAC7B,IAAID,KAAK,KAAK,EAAE,EAAE;cACdrB,KAAI,CAACqB,KAAK,GAAG,EAAE;cACfrB,KAAI,CAACuB,eAAe,CAAC,CAAC;cACtB;AACxB;AACA;AACA;AACA;AACA;cACwB,IAAIP,WAAW,IAAI,CAAChB,KAAI,CAACwB,OAAO,EAAE;gBAC9BxB,KAAI,CAACyB,QAAQ,CAAC,CAAC;gBACf;AAC5B;AACA;AACA;AACA;AACA;gBAC4BzB,KAAI,CAAC0B,YAAY,GAAGL,KAAK;cAC7B;YACJ;YACAF,OAAO,CAAC,CAAC;UACb,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC;QACd,CAAC,CAAC;MACN,CAAC;MAAA,iBAAAQ,EAAA;QAAA,OAAAb,IAAA,CAAAc,KAAA,OAAAC,SAAA;MAAA;IAAA;IACD;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACC,iBAAiB;MAAA,IAAAC,KAAA,GAAAhB,6KAAA,CAAG,WAAOiB,EAAE,EAAK;QACnC,IAAIA,EAAE,EAAE;UACJA,EAAE,CAACC,cAAc,CAAC,CAAC;UACnBD,EAAE,CAACE,eAAe,CAAC,CAAC;QACxB;QACAlC,KAAI,CAACG,SAAS,CAACc,IAAI,CAAC,CAAC;QACrB;QACA,MAAMI,KAAK,GAAGrB,KAAI,CAACsB,QAAQ,CAAC,CAAC;QAC7B,MAAME,OAAO,GAAGxB,KAAI,CAACwB,OAAO;QAC5B,MAAMxB,KAAI,CAACa,YAAY,CAAC,CAAC;QACzB;AACZ;AACA;AACA;AACA;QACY,IAAIQ,KAAK,IAAI,CAACG,OAAO,EAAE;UACnBxB,KAAI,CAACmC,eAAe,CAACH,EAAE,CAAC;QAC5B;QACA,IAAIhC,KAAI,CAACoC,WAAW,EAAE;UAClBpC,KAAI,CAACoC,WAAW,CAACC,IAAI,CAAC,CAAC;QAC3B;MACJ,CAAC;MAAA,iBAAAC,GAAA;QAAA,OAAAP,KAAA,CAAAH,KAAA,OAAAC,SAAA;MAAA;IAAA;IACD;AACR;AACA;IACQ,IAAI,CAACU,OAAO,GAAIP,EAAE,IAAK;MACnB,MAAMQ,KAAK,GAAGR,EAAE,CAACS,MAAM;MACvB,IAAID,KAAK,EAAE;QACP,IAAI,CAACnB,KAAK,GAAGmB,KAAK,CAACnB,KAAK;MAC5B;MACA,IAAI,CAACE,eAAe,CAACS,EAAE,CAAC;IAC5B,CAAC;IACD,IAAI,CAACU,QAAQ,GAAIV,EAAE,IAAK;MACpB,IAAI,CAACG,eAAe,CAACH,EAAE,CAAC;IAC5B,CAAC;IACD;AACR;AACA;AACA;IACQ,IAAI,CAACW,MAAM,GAAIX,EAAE,IAAK;MAClB,IAAI,CAACR,OAAO,GAAG,KAAK;MACpB,IAAI,CAACnB,OAAO,CAACY,IAAI,CAAC,CAAC;MACnB,IAAI,CAAC2B,gBAAgB,CAAC,CAAC;MACvB,IAAI,IAAI,CAAClB,YAAY,KAAK,IAAI,CAACL,KAAK,EAAE;QAClC,IAAI,CAACc,eAAe,CAACH,EAAE,CAAC;MAC5B;MACA,IAAI,CAACN,YAAY,GAAGmB,SAAS;IACjC,CAAC;IACD;AACR;AACA;IACQ,IAAI,CAACC,OAAO,GAAG,MAAM;MACjB,IAAI,CAACtB,OAAO,GAAG,IAAI;MACnB,IAAI,CAACE,YAAY,GAAG,IAAI,CAACL,KAAK;MAC9B,IAAI,CAACf,QAAQ,CAACW,IAAI,CAAC,CAAC;MACpB,IAAI,CAAC2B,gBAAgB,CAAC,CAAC;IAC3B,CAAC;IACD,IAAI,CAACpB,OAAO,GAAG,KAAK;IACpB,IAAI,CAACuB,SAAS,GAAG,IAAI;IACrB,IAAI,CAACC,KAAK,GAAGH,SAAS;IACtB,IAAI,CAACI,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACC,cAAc,GAAG,SAAS;IAC/B,IAAI,CAACC,YAAY,GAAG,KAAK;IACzB,IAAI,CAACC,WAAW,GAAG,KAAK;IACxB,IAAI,CAACC,gBAAgB,GAAG9D,wDAAM,CAAC+D,GAAG,CAAC,gBAAgB,EAAEvE,iDAAc,CAAC;IACpE,IAAI,CAACwE,gBAAgB,GAAG,QAAQ;IAChC,IAAI,CAACC,SAAS,GAAGX,SAAS;IAC1B,IAAI,CAACY,QAAQ,GAAGZ,SAAS;IACzB,IAAI,CAACa,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACC,SAAS,GAAGd,SAAS;IAC1B,IAAI,CAACe,YAAY,GAAGf,SAAS;IAC7B,IAAI,CAACgB,SAAS,GAAGhB,SAAS;IAC1B,IAAI,CAACiB,SAAS,GAAGjB,SAAS;IAC1B,IAAI,CAACkB,IAAI,GAAG,IAAI,CAACrD,OAAO;IACxB,IAAI,CAACsD,WAAW,GAAG,QAAQ;IAC3B,IAAI,CAACC,UAAU,GAAGpB,SAAS;IAC3B,IAAI,CAACqB,gBAAgB,GAAG,OAAO;IAC/B,IAAI,CAACC,eAAe,GAAG,QAAQ;IAC/B,IAAI,CAACC,UAAU,GAAG,KAAK;IACvB,IAAI,CAACC,IAAI,GAAG,QAAQ;IACpB,IAAI,CAAChD,KAAK,GAAG,EAAE;EACnB;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIiD,aAAaA,CAACC,QAAQ,EAAE;IACpB,IAAI,CAAC3D,mBAAmB,GAAG4D,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC7D,mBAAmB,CAAC,EAAE;MAAE8D,IAAI,EAAEH;IAAS,CAAC,CAAC;IACzGxG,qDAAW,CAAC,IAAI,CAAC;EACrB;EACA4G,YAAYA,CAACJ,QAAQ,EAAE;IACnB,IAAI,CAAC3D,mBAAmB,GAAG4D,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC7D,mBAAmB,CAAC,EAAE;MAAEgE,GAAG,EAAEL;IAAS,CAAC,CAAC;IACxGxG,qDAAW,CAAC,IAAI,CAAC;EACrB;EACA8G,eAAeA,CAAA,EAAG;IACd,MAAM;MAAE5E,QAAQ;MAAEwD,QAAQ;MAAEqB;IAAiB,CAAC,GAAG,IAAI;IACrD;AACR;AACA;AACA;IACQ,IAAI,CAAC7E,QAAQ,GAAGwD,QAAQ,KAAKZ,SAAS,GAAGiC,gBAAgB,KAAK,IAAI,IAAIA,gBAAgB,KAAK,KAAK,CAAC,GAAGA,gBAAgB,GAAG7E,QAAQ,GAAG3B,uDAAa,CAAC2B,QAAQ,EAAEwD,QAAQ,CAAC;EACvK;EACAsB,YAAYA,CAAA,EAAG;IACX,MAAMC,OAAO,GAAG,IAAI,CAAC5C,WAAW;IAChC,MAAMf,KAAK,GAAG,IAAI,CAACC,QAAQ,CAAC,CAAC;IAC7B,IAAI0D,OAAO,IAAIA,OAAO,CAAC3D,KAAK,KAAKA,KAAK,EAAE;MACpC2D,OAAO,CAAC3D,KAAK,GAAGA,KAAK;IACzB;EACJ;EACA4D,uBAAuBA,CAAA,EAAG;IACtBC,qBAAqB,CAAC,MAAM;MACxB,IAAI,CAACtC,gBAAgB,CAAC,CAAC;MACvB7E,qDAAW,CAAC,IAAI,CAAC;IACrB,CAAC,CAAC;EACN;EACAoH,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAACC,SAAS,CAAC,CAAC;EACpB;EACAC,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAACzE,mBAAmB,GAAG4D,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEjG,uDAAiB,CAAC,IAAI,CAAC8G,EAAE,EAAE,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC,CAAC;EAC7F;EACAC,gBAAgBA,CAAA,EAAG;IACf,IAAI,CAACT,gBAAgB,GAAG,IAAI,CAAC7E,QAAQ;IACrC,IAAI,CAAC2C,gBAAgB,CAAC,CAAC;IACvB,IAAI,CAACiC,eAAe,CAAC,CAAC;IACtBzD,UAAU,CAAC,MAAM;MACb,IAAI,CAAC2B,SAAS,GAAG,KAAK;IAC1B,CAAC,EAAE,GAAG,CAAC;EACX;EACAqC,SAASA,CAAA,EAAG;IACR,IAAI,CAAC7E,QAAQ,CAACU,IAAI,CAAC;MACfuE,SAAS,EAAE;IACf,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACU/D,QAAQA,CAAA,EAAG;IAAA,IAAAgE,MAAA;IAAA,OAAA1E,6KAAA;MACb,IAAI0E,MAAI,CAACrD,WAAW,EAAE;QAClBqD,MAAI,CAACrD,WAAW,CAACsD,KAAK,CAAC,CAAC;MAC5B;IAAC;EACL;EACA;AACJ;AACA;EACUC,eAAeA,CAAA,EAAG;IAAA,IAAAC,MAAA;IAAA,OAAA7E,6KAAA;MACpB;AACR;AACA;AACA;MACQ,IAAI,CAAC6E,MAAI,CAACxD,WAAW,EAAE;QACnB,MAAM,IAAIlB,OAAO,CAAEC,OAAO,IAAKzC,uDAAgB,CAACkH,MAAI,CAACN,EAAE,EAAEnE,OAAO,CAAC,CAAC;MACtE;MACA,OAAOD,OAAO,CAACC,OAAO,CAACyE,MAAI,CAACxD,WAAW,CAAC;IAAC;EAC7C;EACA;AACJ;AACA;AACA;AACA;AACA;EACID,eAAeA,CAAC0D,KAAK,EAAE;IACnB,MAAM;MAAExE;IAAM,CAAC,GAAG,IAAI;IACtB;IACA,MAAMkD,QAAQ,GAAGlD,KAAK,IAAI,IAAI,GAAGA,KAAK,GAAGA,KAAK,CAACyE,QAAQ,CAAC,CAAC;IACzD;IACA,IAAI,CAACpE,YAAY,GAAG6C,QAAQ;IAC5B,IAAI,CAACrE,SAAS,CAACe,IAAI,CAAC;MAAEI,KAAK,EAAEkD,QAAQ;MAAEsB;IAAM,CAAC,CAAC;EACnD;EACA;AACJ;AACA;EACItE,eAAeA,CAACsE,KAAK,EAAE;IACnB,MAAM;MAAExE;IAAM,CAAC,GAAG,IAAI;IACtB,IAAI,CAACpB,QAAQ,CAACgB,IAAI,CAAC;MAAEI,KAAK;MAAEwE;IAAM,CAAC,CAAC;EACxC;EACA;AACJ;AACA;AACA;EACIjD,gBAAgBA,CAAA,EAAG;IACf,MAAMvB,KAAK,GAAG,IAAI,CAACC,QAAQ,CAAC,CAAC;IAC7B,MAAMyE,aAAa,GAAG,IAAI,CAACtF,eAAe;IAC1C,MAAMuF,IAAI,GAAGxG,4DAAU,CAAC,IAAI,CAAC;IAC7B,MAAMiB,eAAe,GAAG,CAAC,IAAI,CAACwC,QAAQ,IAAI5B,KAAK,CAAC4E,IAAI,CAAC,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC,IAAI,CAACzE,OAAO;IAC/E,IAAI,CAACf,eAAe,GAAGA,eAAe;IACtC,IAAIuF,IAAI,KAAK,KAAK,EAAE;MAChB;IACJ;IACA,IAAID,aAAa,KAAKtF,eAAe,EAAE;MACnC,IAAI,CAACyF,mBAAmB,CAAC,CAAC;IAC9B;IACA,IAAI,IAAI,CAACjD,QAAQ,EAAE;MACf,IAAI,CAACkD,oBAAoB,CAAC,CAAC;IAC/B;EACJ;EACA;AACJ;AACA;EACID,mBAAmBA,CAAA,EAAG;IAClB,MAAMlB,OAAO,GAAG,IAAI,CAAC5C,WAAW;IAChC,IAAI,CAAC4C,OAAO,EAAE;MACV;IACJ;IACA,MAAMoB,GAAG,GAAGxH,mDAAK,CAAC,IAAI,CAAC0G,EAAE,CAAC;IAC1B,MAAMe,MAAM,GAAG,CAAC,IAAI,CAACf,EAAE,CAACgB,UAAU,IAAI,IAAI,CAAChB,EAAE,EAAEiB,aAAa,CAAC,wBAAwB,CAAC;IACtF,IAAI,IAAI,CAAC9F,eAAe,EAAE;MACtBuE,OAAO,CAACwB,eAAe,CAAC,OAAO,CAAC;MAChCH,MAAM,CAACG,eAAe,CAAC,OAAO,CAAC;IACnC,CAAC,MACI;MACD;MACA,MAAMC,GAAG,GAAGC,QAAQ;MACpB,MAAMC,QAAQ,GAAGF,GAAG,CAACG,aAAa,CAAC,MAAM,CAAC;MAC1CD,QAAQ,CAACE,SAAS,GAAG,IAAI,CAAC7C,WAAW,IAAI,EAAE;MAC3CyC,GAAG,CAACK,IAAI,CAACC,WAAW,CAACJ,QAAQ,CAAC;MAC9B;MACAhI,uDAAG,CAAC,MAAM;QACN,MAAMqI,SAAS,GAAGL,QAAQ,CAACM,WAAW;QACtCN,QAAQ,CAACO,MAAM,CAAC,CAAC;QACjB;QACA,MAAMC,SAAS,GAAG,aAAa,GAAGH,SAAS,GAAG,CAAC,GAAG,KAAK;QACvD;QACA;AAChB;AACA;AACA;AACA;AACA;QACgB,MAAMI,QAAQ,GAAG,aAAa,IAAIJ,SAAS,GAAG,CAAC,GAAGX,MAAM,CAACgB,WAAW,GAAG,CAAC,CAAC,GAAG,KAAK;QACjF;QACA,IAAIjB,GAAG,EAAE;UACLpB,OAAO,CAACsC,KAAK,CAACC,YAAY,GAAGJ,SAAS;UACtCd,MAAM,CAACiB,KAAK,CAACE,WAAW,GAAGJ,QAAQ;QACvC,CAAC,MACI;UACDpC,OAAO,CAACsC,KAAK,CAACG,WAAW,GAAGN,SAAS;UACrCd,MAAM,CAACiB,KAAK,CAACI,UAAU,GAAGN,QAAQ;QACtC;MACJ,CAAC,CAAC;IACN;EACJ;EACA;AACJ;AACA;EACIjB,oBAAoBA,CAAA,EAAG;IACnB,MAAMC,GAAG,GAAGxH,mDAAK,CAAC,IAAI,CAAC0G,EAAE,CAAC;IAC1B,MAAMqC,YAAY,GAAG,CAAC,IAAI,CAACrC,EAAE,CAACgB,UAAU,IAAI,IAAI,CAAChB,EAAE,EAAEiB,aAAa,CAAC,0BAA0B,CAAC;IAC9F,MAAMqB,gBAAgB,GAAG,IAAI,CAACC,sBAAsB,CAAC,CAAC;IACtD,IAAIF,YAAY,KAAK,IAAI,IAAIC,gBAAgB,KAAK,IAAI,CAACpH,eAAe,EAAE;MACpE,MAAMsH,WAAW,GAAGH,YAAY,CAACL,KAAK;MACtC,IAAI,CAAC9G,eAAe,GAAGoH,gBAAgB;MACvC,IAAIA,gBAAgB,EAAE;QAClB,IAAIxB,GAAG,EAAE;UACL0B,WAAW,CAACJ,UAAU,GAAG,GAAG;QAChC,CAAC,MACI;UACDI,WAAW,CAACN,WAAW,GAAG,GAAG;QACjC;MACJ,CAAC,MACI;QACD,MAAMO,MAAM,GAAGJ,YAAY,CAACV,WAAW;QACvC,IAAIc,MAAM,GAAG,CAAC,EAAE;UACZ,IAAI3B,GAAG,EAAE;YACL0B,WAAW,CAACJ,UAAU,GAAG,CAACK,MAAM,GAAG,IAAI;UAC3C,CAAC,MACI;YACDD,WAAW,CAACN,WAAW,GAAG,CAACO,MAAM,GAAG,IAAI;UAC5C;QACJ;MACJ;IACJ;EACJ;EACAzG,QAAQA,CAAA,EAAG;IACP,OAAO,IAAI,CAACD,KAAK,IAAI,EAAE;EAC3B;EACA2G,QAAQA,CAAA,EAAG;IACP,OAAO,IAAI,CAAC1G,QAAQ,CAAC,CAAC,KAAK,EAAE;EACjC;EACA;AACJ;AACA;AACA;AACA;AACA;EACIuG,sBAAsBA,CAAA,EAAG;IACrB,IAAI,IAAI,CAAC3D,gBAAgB,KAAK,OAAO,IAAK,IAAI,CAACA,gBAAgB,KAAK,OAAO,IAAI,CAAC,IAAI,CAAC1C,OAAQ,EAAE;MAC3F,OAAO,KAAK;IAChB;IACA,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;AACA;EACIyG,qBAAqBA,CAAA,EAAG;IACpB,IAAI,IAAI,CAAC9D,eAAe,KAAK,OAAO,IAAK,IAAI,CAACA,eAAe,KAAK,OAAO,IAAI,CAAC,IAAI,CAAC3C,OAAQ,EAAE;MACzF,OAAO,KAAK;IAChB;IACA,OAAO,IAAI;EACf;EACA0G,MAAMA,CAAA,EAAG;IACL,MAAM;MAAE3E,gBAAgB;MAAEL;IAAe,CAAC,GAAG,IAAI;IACjD,MAAMD,QAAQ,GAAG,IAAI,CAACA,QAAQ,IAAI1D,wDAAM,CAAC4I,UAAU,CAAC,UAAU,EAAE,IAAI,CAAC;IACrE,MAAMnC,IAAI,GAAGxG,4DAAU,CAAC,IAAI,CAAC;IAC7B,MAAMgE,SAAS,GAAG,IAAI,CAACA,SAAS,KAAKwC,IAAI,KAAK,KAAK,GAAG/G,iDAAW,GAAGC,iDAAU,CAAC;IAC/E,MAAM+E,UAAU,GAAG,IAAI,CAACA,UAAU,KAAK+B,IAAI,KAAK,KAAK,GAAG5G,iDAAa,GAAGE,iDAAW,CAAC;IACpF,MAAMuI,sBAAsB,GAAG,IAAI,CAACA,sBAAsB,CAAC,CAAC;IAC5D,MAAMF,YAAY,GAAG,IAAI,CAACzD,gBAAgB,KAAK,OAAO,IAAKlG,qDAAC,CAAC,QAAQ,EAAE;MAAEoK,GAAG,EAAE,0CAA0C;MAAE,YAAY,EAAE7E,gBAAgB;MAAE,aAAa,EAAEsE,sBAAsB,GAAGhF,SAAS,GAAG,MAAM;MAAEwB,IAAI,EAAE,QAAQ;MAAEgE,QAAQ,EAAErC,IAAI,KAAK,KAAK,IAAI,CAAC6B,sBAAsB,GAAG,CAAC,CAAC,GAAGhF,SAAS;MAAEyF,WAAW,EAAE,IAAI,CAACxG,iBAAiB;MAAEyG,YAAY,EAAE,IAAI,CAACzG,iBAAiB;MAAE0G,KAAK,EAAE;IAA0B,CAAC,EAAExK,qDAAC,CAAC,KAAK,EAAE;MAAEoK,GAAG,EAAE,0CAA0C;MAAE,aAAa,EAAE;IAAO,CAAC,EAAEpC,IAAI,KAAK,IAAI,GAAIhI,qDAAC,CAAC,UAAU,EAAE;MAAE,aAAa,EAAE,MAAM;MAAEgI,IAAI,EAAEA,IAAI;MAAEyC,IAAI,EAAE,IAAI,CAACpF,gBAAgB;MAAEqF,IAAI,EAAE;IAAM,CAAC,CAAC,GAAKnF,gBAAiB,CAAC,CAAE;IACxnB,OAAQvF,qDAAC,CAACE,iDAAI,EAAE;MAAEkK,GAAG,EAAE,0CAA0C;MAAEO,IAAI,EAAE,QAAQ;MAAE,eAAe,EAAE,IAAI,CAACjF,QAAQ,GAAG,MAAM,GAAG,IAAI;MAAE8E,KAAK,EAAE3J,qDAAkB,CAAC,IAAI,CAACmE,KAAK,EAAE;QACjK,CAACgD,IAAI,GAAG,IAAI;QACZ,oBAAoB,EAAE/C,QAAQ;QAC9B,oBAAoB,EAAE,IAAI,CAACS,QAAQ;QACnC,sBAAsB,EAAET,QAAQ,IAAI,IAAI,CAACF,SAAS;QAClD,qBAAqB,EAAE,IAAI,CAACiF,QAAQ,CAAC,CAAC;QACtC,wBAAwB,EAAE,IAAI,CAACvH,eAAe;QAC9C,qBAAqB,EAAE,IAAI,CAACe,OAAO;QACnC,6BAA6B,EAAE,IAAI,CAACyG,qBAAqB,CAAC,CAAC;QAC3D,8BAA8B,EAAE,IAAI,CAACJ,sBAAsB,CAAC;MAChE,CAAC;IAAE,CAAC,EAAE7J,qDAAC,CAAC,KAAK,EAAE;MAAEoK,GAAG,EAAE,0CAA0C;MAAEI,KAAK,EAAE;IAA4B,CAAC,EAAExK,qDAAC,CAAC,OAAO,EAAEwG,MAAM,CAACC,MAAM,CAAC;MAAE2D,GAAG,EAAE,0CAA0C;MAAE,YAAY,EAAE,aAAa;MAAE1E,QAAQ,EAAE,IAAI,CAACA,QAAQ;MAAEkF,GAAG,EAAGtD,EAAE,IAAM,IAAI,CAAClD,WAAW,GAAGkD,EAAG;MAAEkD,KAAK,EAAE,iBAAiB;MAAEK,SAAS,EAAE,IAAI,CAAClF,SAAS;MAAEmF,YAAY,EAAE,IAAI,CAAClF,YAAY;MAAEG,IAAI,EAAE,IAAI,CAACA,IAAI;MAAExB,OAAO,EAAE,IAAI,CAACA,OAAO;MAAEG,QAAQ,EAAE,IAAI,CAACA,QAAQ;MAAEC,MAAM,EAAE,IAAI,CAACA,MAAM;MAAEG,OAAO,EAAE,IAAI,CAACA,OAAO;MAAEiG,SAAS,EAAE,IAAI,CAACjF,SAAS;MAAEkF,SAAS,EAAE,IAAI,CAACnF,SAAS;MAAEG,WAAW,EAAE,IAAI,CAACA,WAAW;MAAEK,IAAI,EAAE,IAAI,CAACA,IAAI;MAAEhD,KAAK,EAAE,IAAI,CAACC,QAAQ,CAAC,CAAC;MAAE2H,cAAc,EAAE/F,cAAc,KAAK,SAAS,GAAGL,SAAS,GAAGK,cAAc;MAAEgG,YAAY,EAAE,IAAI,CAAC/F,YAAY;MAAEgG,WAAW,EAAE,IAAI,CAAC/F,WAAW;MAAEgB,UAAU,EAAE,IAAI,CAACA;IAAW,CAAC,EAAE,IAAI,CAACxD,mBAAmB,CAAC,CAAC,EAAEoF,IAAI,KAAK,IAAI,IAAI2B,YAAY,EAAE3J,qDAAC,CAAC,UAAU,EAAE;MAAEoK,GAAG,EAAE,0CAA0C;MAAE,aAAa,EAAE,MAAM;MAAEpC,IAAI,EAAEA,IAAI;MAAEyC,IAAI,EAAExE,UAAU;MAAEyE,IAAI,EAAE,KAAK;MAAEF,KAAK,EAAE;IAAwB,CAAC,CAAC,EAAExK,qDAAC,CAAC,QAAQ,EAAE;MAAEoK,GAAG,EAAE,0CAA0C;MAAE,YAAY,EAAE,OAAO;MAAE/D,IAAI,EAAE,QAAQ;MAAE,SAAS,EAAE,IAAI;MAAEmE,KAAK,EAAE,wBAAwB;MAAEY,aAAa,EAAGpH,EAAE,IAAK;QACvoC;AAChB;AACA;AACA;AACA;QACgBA,EAAE,CAACC,cAAc,CAAC,CAAC;MACvB,CAAC;MAAEoH,OAAO,EAAEA,CAAA,KAAM,IAAI,CAACxI,YAAY,CAAC,IAAI;IAAE,CAAC,EAAE7C,qDAAC,CAAC,UAAU,EAAE;MAAEoK,GAAG,EAAE,0CAA0C;MAAE,aAAa,EAAE,MAAM;MAAEpC,IAAI,EAAEA,IAAI;MAAEyC,IAAI,EAAEjF,SAAS;MAAEkF,IAAI,EAAE,KAAK;MAAEF,KAAK,EAAE;IAAuB,CAAC,CAAC,CAAC,CAAC,EAAExC,IAAI,KAAK,KAAK,IAAI2B,YAAY,CAAC;EAC1P;EACA,IAAIrC,EAAEA,CAAA,EAAG;IAAE,OAAOlH,qDAAU,CAAC,IAAI,CAAC;EAAE;EACpC,WAAWkL,QAAQA,CAAA,EAAG;IAAE,OAAO;MAC3B,MAAM,EAAE,CAAC,eAAe,CAAC;MACzB,KAAK,EAAE,CAAC,cAAc,CAAC;MACvB,UAAU,EAAE,CAAC,iBAAiB,CAAC;MAC/B,OAAO,EAAE,CAAC,cAAc,CAAC;MACzB,kBAAkB,EAAE,CAAC,yBAAyB;IAClD,CAAC;EAAE;AACP,CAAC;AACD,IAAI3I,YAAY,GAAG,CAAC;AACpBd,SAAS,CAACyH,KAAK,GAAG;EACdiC,GAAG,EAAE7J,qBAAqB;EAC1B8J,EAAE,EAAE5J;AACR,CAAC", "sources": ["./node_modules/@ionic/core/dist/esm/ion-searchbar.entry.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, d as createEvent, i as forceUpdate, h, H as Host, f as getElement } from './index-a1a47f01.js';\nimport { j as debounceEvent, k as inheritAttributes, c as componentOnReady, r as raf } from './helpers-be245865.js';\nimport { i as isRTL } from './dir-babeabeb.js';\nimport { c as createColorClasses } from './theme-01f3f29c.js';\nimport { a as arrowBackSharp, b as closeCircle, d as closeSharp, s as searchOutline, e as searchSharp } from './index-f7dc70ba.js';\nimport { c as config, b as getIonMode } from './ionic-global-94f25d1b.js';\n\nconst searchbarIosCss = \".sc-ion-searchbar-ios-h{--placeholder-color:initial;--placeholder-font-style:initial;--placeholder-font-weight:initial;--placeholder-opacity:0.6;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;display:-ms-flexbox;display:flex;position:relative;-ms-flex-align:center;align-items:center;width:100%;color:var(--color);font-family:var(--ion-font-family, inherit);-webkit-box-sizing:border-box;box-sizing:border-box}.ion-color.sc-ion-searchbar-ios-h{color:var(--ion-color-contrast)}.ion-color.sc-ion-searchbar-ios-h .searchbar-input.sc-ion-searchbar-ios{background:var(--ion-color-base)}.ion-color.sc-ion-searchbar-ios-h .searchbar-clear-button.sc-ion-searchbar-ios,.ion-color.sc-ion-searchbar-ios-h .searchbar-cancel-button.sc-ion-searchbar-ios,.ion-color.sc-ion-searchbar-ios-h .searchbar-search-icon.sc-ion-searchbar-ios{color:inherit}.searchbar-search-icon.sc-ion-searchbar-ios{color:var(--icon-color);pointer-events:none}.searchbar-input-container.sc-ion-searchbar-ios{display:block;position:relative;-ms-flex-negative:1;flex-shrink:1;width:100%}.searchbar-input.sc-ion-searchbar-ios{font-family:inherit;font-size:inherit;font-style:inherit;font-weight:inherit;letter-spacing:inherit;text-decoration:inherit;text-indent:inherit;text-overflow:inherit;text-transform:inherit;text-align:inherit;white-space:inherit;color:inherit;border-radius:var(--border-radius);display:block;width:100%;min-height:inherit;border:0;outline:none;background:var(--background);font-family:inherit;-webkit-box-shadow:var(--box-shadow);box-shadow:var(--box-shadow);-webkit-box-sizing:border-box;box-sizing:border-box;-webkit-appearance:none;-moz-appearance:none;appearance:none}.searchbar-input.sc-ion-searchbar-ios::-webkit-input-placeholder{color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.searchbar-input.sc-ion-searchbar-ios::-moz-placeholder{color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.searchbar-input.sc-ion-searchbar-ios:-ms-input-placeholder{color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.searchbar-input.sc-ion-searchbar-ios::-ms-input-placeholder{color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.searchbar-input.sc-ion-searchbar-ios::placeholder{color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.searchbar-input.sc-ion-searchbar-ios::-webkit-search-cancel-button,.searchbar-input.sc-ion-searchbar-ios::-ms-clear{display:none}.searchbar-cancel-button.sc-ion-searchbar-ios{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;display:none;height:100%;border:0;outline:none;color:var(--cancel-button-color);cursor:pointer;-webkit-appearance:none;-moz-appearance:none;appearance:none}.searchbar-cancel-button.sc-ion-searchbar-ios>div.sc-ion-searchbar-ios{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;width:100%;height:100%}.searchbar-clear-button.sc-ion-searchbar-ios{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;display:none;min-height:0;outline:none;color:var(--clear-button-color);-webkit-appearance:none;-moz-appearance:none;appearance:none}.searchbar-clear-button.sc-ion-searchbar-ios:focus{opacity:0.5}.searchbar-has-value.searchbar-should-show-clear.sc-ion-searchbar-ios-h .searchbar-clear-button.sc-ion-searchbar-ios{display:block}.searchbar-disabled.sc-ion-searchbar-ios-h{cursor:default;opacity:0.4;pointer-events:none}.sc-ion-searchbar-ios-h{--background:rgba(var(--ion-text-color-rgb, 0, 0, 0), 0.07);--border-radius:10px;--box-shadow:none;--cancel-button-color:var(--ion-color-primary, #3880ff);--clear-button-color:var(--ion-color-step-600, #666666);--color:var(--ion-text-color, #000);--icon-color:var(--ion-color-step-600, #666666);-webkit-padding-start:12px;padding-inline-start:12px;-webkit-padding-end:12px;padding-inline-end:12px;padding-top:12px;padding-bottom:12px;min-height:60px;contain:content}.searchbar-input-container.sc-ion-searchbar-ios{min-height:36px}.searchbar-search-icon.sc-ion-searchbar-ios{-webkit-margin-start:calc(50% - 60px);margin-inline-start:calc(50% - 60px);top:0;position:absolute;width:1.375rem;height:100%;contain:strict}@supports (inset-inline-start: 0){.searchbar-search-icon.sc-ion-searchbar-ios{inset-inline-start:5px}}@supports not (inset-inline-start: 0){.searchbar-search-icon.sc-ion-searchbar-ios{left:5px}[dir=rtl].sc-ion-searchbar-ios-h .searchbar-search-icon.sc-ion-searchbar-ios,[dir=rtl] .sc-ion-searchbar-ios-h .searchbar-search-icon.sc-ion-searchbar-ios{left:unset;right:unset;right:5px}[dir=rtl].sc-ion-searchbar-ios .searchbar-search-icon.sc-ion-searchbar-ios{left:unset;right:unset;right:5px}@supports selector(:dir(rtl)){.searchbar-search-icon.sc-ion-searchbar-ios:dir(rtl){left:unset;right:unset;right:5px}}}.searchbar-input.sc-ion-searchbar-ios{-webkit-padding-start:0px;padding-inline-start:0px;-webkit-padding-end:0px;padding-inline-end:0px;padding-top:6px;padding-bottom:6px;height:100%;font-size:1.0625rem;font-weight:400;contain:strict}.searchbar-has-value.searchbar-should-show-clear.sc-ion-searchbar-ios-h .searchbar-input.sc-ion-searchbar-ios{-webkit-padding-start:1.75rem;padding-inline-start:1.75rem;-webkit-padding-end:1.75rem;padding-inline-end:1.75rem}.searchbar-clear-button.sc-ion-searchbar-ios{top:0;background-position:center;position:absolute;width:1.875rem;height:100%;border:0;background-color:transparent}@supports (inset-inline-start: 0){.searchbar-clear-button.sc-ion-searchbar-ios{inset-inline-end:0}}@supports not (inset-inline-start: 0){.searchbar-clear-button.sc-ion-searchbar-ios{right:0}[dir=rtl].sc-ion-searchbar-ios-h .searchbar-clear-button.sc-ion-searchbar-ios,[dir=rtl] .sc-ion-searchbar-ios-h .searchbar-clear-button.sc-ion-searchbar-ios{left:unset;right:unset;left:0}[dir=rtl].sc-ion-searchbar-ios .searchbar-clear-button.sc-ion-searchbar-ios{left:unset;right:unset;left:0}@supports selector(:dir(rtl)){.searchbar-clear-button.sc-ion-searchbar-ios:dir(rtl){left:unset;right:unset;left:0}}}.searchbar-clear-icon.sc-ion-searchbar-ios{width:1.125rem;height:100%}.searchbar-cancel-button.sc-ion-searchbar-ios{-webkit-padding-start:8px;padding-inline-start:8px;-webkit-padding-end:0;padding-inline-end:0;padding-top:0;padding-bottom:0;-ms-flex-negative:0;flex-shrink:0;background-color:transparent;font-size:16px}.searchbar-left-aligned.sc-ion-searchbar-ios-h .searchbar-search-icon.sc-ion-searchbar-ios{-webkit-margin-start:0;margin-inline-start:0}.searchbar-left-aligned.sc-ion-searchbar-ios-h .searchbar-input.sc-ion-searchbar-ios{-webkit-padding-start:1.875rem;padding-inline-start:1.875rem}.searchbar-has-focus.sc-ion-searchbar-ios-h .searchbar-cancel-button.sc-ion-searchbar-ios,.searchbar-should-show-cancel.sc-ion-searchbar-ios-h .searchbar-cancel-button.sc-ion-searchbar-ios,.searchbar-animated.sc-ion-searchbar-ios-h .searchbar-cancel-button.sc-ion-searchbar-ios{display:block}.searchbar-animated.sc-ion-searchbar-ios-h .searchbar-search-icon.sc-ion-searchbar-ios,.searchbar-animated.sc-ion-searchbar-ios-h .searchbar-input.sc-ion-searchbar-ios{-webkit-transition:all 300ms ease;transition:all 300ms ease}.searchbar-animated.searchbar-has-focus.sc-ion-searchbar-ios-h .searchbar-cancel-button.sc-ion-searchbar-ios,.searchbar-animated.searchbar-should-show-cancel.sc-ion-searchbar-ios-h .searchbar-cancel-button.sc-ion-searchbar-ios{opacity:1;pointer-events:auto}.searchbar-animated.sc-ion-searchbar-ios-h .searchbar-cancel-button.sc-ion-searchbar-ios{-webkit-margin-end:-100%;margin-inline-end:-100%;-webkit-transform:translate3d(0,  0,  0);transform:translate3d(0,  0,  0);-webkit-transition:all 300ms ease;transition:all 300ms ease;opacity:0;pointer-events:none}.searchbar-no-animate.sc-ion-searchbar-ios-h .searchbar-search-icon.sc-ion-searchbar-ios,.searchbar-no-animate.sc-ion-searchbar-ios-h .searchbar-input.sc-ion-searchbar-ios,.searchbar-no-animate.sc-ion-searchbar-ios-h .searchbar-cancel-button.sc-ion-searchbar-ios{-webkit-transition-duration:0ms;transition-duration:0ms}.ion-color.sc-ion-searchbar-ios-h .searchbar-cancel-button.sc-ion-searchbar-ios{color:var(--ion-color-base)}@media (any-hover: hover){.ion-color.sc-ion-searchbar-ios-h .searchbar-cancel-button.sc-ion-searchbar-ios:hover{color:var(--ion-color-tint)}}ion-toolbar.sc-ion-searchbar-ios-h,ion-toolbar .sc-ion-searchbar-ios-h{padding-top:1px;padding-bottom:15px;min-height:52px}ion-toolbar.ion-color.sc-ion-searchbar-ios-h:not(.ion-color),ion-toolbar.ion-color .sc-ion-searchbar-ios-h:not(.ion-color){color:inherit}ion-toolbar.ion-color.sc-ion-searchbar-ios-h:not(.ion-color) .searchbar-cancel-button.sc-ion-searchbar-ios,ion-toolbar.ion-color .sc-ion-searchbar-ios-h:not(.ion-color) .searchbar-cancel-button.sc-ion-searchbar-ios{color:currentColor}ion-toolbar.ion-color.sc-ion-searchbar-ios-h .searchbar-search-icon.sc-ion-searchbar-ios,ion-toolbar.ion-color .sc-ion-searchbar-ios-h .searchbar-search-icon.sc-ion-searchbar-ios{color:currentColor;opacity:0.5}ion-toolbar.ion-color.sc-ion-searchbar-ios-h:not(.ion-color) .searchbar-input.sc-ion-searchbar-ios,ion-toolbar.ion-color .sc-ion-searchbar-ios-h:not(.ion-color) .searchbar-input.sc-ion-searchbar-ios{background:rgba(var(--ion-color-contrast-rgb), 0.07);color:currentColor}ion-toolbar.ion-color.sc-ion-searchbar-ios-h:not(.ion-color) .searchbar-clear-button.sc-ion-searchbar-ios,ion-toolbar.ion-color .sc-ion-searchbar-ios-h:not(.ion-color) .searchbar-clear-button.sc-ion-searchbar-ios{color:currentColor;opacity:0.5}\";\nconst IonSearchbarIosStyle0 = searchbarIosCss;\n\nconst searchbarMdCss = \".sc-ion-searchbar-md-h{--placeholder-color:initial;--placeholder-font-style:initial;--placeholder-font-weight:initial;--placeholder-opacity:0.6;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;display:-ms-flexbox;display:flex;position:relative;-ms-flex-align:center;align-items:center;width:100%;color:var(--color);font-family:var(--ion-font-family, inherit);-webkit-box-sizing:border-box;box-sizing:border-box}.ion-color.sc-ion-searchbar-md-h{color:var(--ion-color-contrast)}.ion-color.sc-ion-searchbar-md-h .searchbar-input.sc-ion-searchbar-md{background:var(--ion-color-base)}.ion-color.sc-ion-searchbar-md-h .searchbar-clear-button.sc-ion-searchbar-md,.ion-color.sc-ion-searchbar-md-h .searchbar-cancel-button.sc-ion-searchbar-md,.ion-color.sc-ion-searchbar-md-h .searchbar-search-icon.sc-ion-searchbar-md{color:inherit}.searchbar-search-icon.sc-ion-searchbar-md{color:var(--icon-color);pointer-events:none}.searchbar-input-container.sc-ion-searchbar-md{display:block;position:relative;-ms-flex-negative:1;flex-shrink:1;width:100%}.searchbar-input.sc-ion-searchbar-md{font-family:inherit;font-size:inherit;font-style:inherit;font-weight:inherit;letter-spacing:inherit;text-decoration:inherit;text-indent:inherit;text-overflow:inherit;text-transform:inherit;text-align:inherit;white-space:inherit;color:inherit;border-radius:var(--border-radius);display:block;width:100%;min-height:inherit;border:0;outline:none;background:var(--background);font-family:inherit;-webkit-box-shadow:var(--box-shadow);box-shadow:var(--box-shadow);-webkit-box-sizing:border-box;box-sizing:border-box;-webkit-appearance:none;-moz-appearance:none;appearance:none}.searchbar-input.sc-ion-searchbar-md::-webkit-input-placeholder{color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.searchbar-input.sc-ion-searchbar-md::-moz-placeholder{color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.searchbar-input.sc-ion-searchbar-md:-ms-input-placeholder{color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.searchbar-input.sc-ion-searchbar-md::-ms-input-placeholder{color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.searchbar-input.sc-ion-searchbar-md::placeholder{color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.searchbar-input.sc-ion-searchbar-md::-webkit-search-cancel-button,.searchbar-input.sc-ion-searchbar-md::-ms-clear{display:none}.searchbar-cancel-button.sc-ion-searchbar-md{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;display:none;height:100%;border:0;outline:none;color:var(--cancel-button-color);cursor:pointer;-webkit-appearance:none;-moz-appearance:none;appearance:none}.searchbar-cancel-button.sc-ion-searchbar-md>div.sc-ion-searchbar-md{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;width:100%;height:100%}.searchbar-clear-button.sc-ion-searchbar-md{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;display:none;min-height:0;outline:none;color:var(--clear-button-color);-webkit-appearance:none;-moz-appearance:none;appearance:none}.searchbar-clear-button.sc-ion-searchbar-md:focus{opacity:0.5}.searchbar-has-value.searchbar-should-show-clear.sc-ion-searchbar-md-h .searchbar-clear-button.sc-ion-searchbar-md{display:block}.searchbar-disabled.sc-ion-searchbar-md-h{cursor:default;opacity:0.4;pointer-events:none}.sc-ion-searchbar-md-h{--background:var(--ion-background-color, #fff);--border-radius:2px;--box-shadow:0 2px 2px 0 rgba(0, 0, 0, 0.14), 0 3px 1px -2px rgba(0, 0, 0, 0.2), 0 1px 5px 0 rgba(0, 0, 0, 0.12);--cancel-button-color:var(--ion-color-step-900, #1a1a1a);--clear-button-color:initial;--color:var(--ion-color-step-850, #262626);--icon-color:var(--ion-color-step-600, #666666);-webkit-padding-start:8px;padding-inline-start:8px;-webkit-padding-end:8px;padding-inline-end:8px;padding-top:8px;padding-bottom:8px;background:inherit}.searchbar-search-icon.sc-ion-searchbar-md{top:11px;width:1.3125rem;height:1.3125rem}@supports (inset-inline-start: 0){.searchbar-search-icon.sc-ion-searchbar-md{inset-inline-start:16px}}@supports not (inset-inline-start: 0){.searchbar-search-icon.sc-ion-searchbar-md{left:16px}[dir=rtl].sc-ion-searchbar-md-h .searchbar-search-icon.sc-ion-searchbar-md,[dir=rtl] .sc-ion-searchbar-md-h .searchbar-search-icon.sc-ion-searchbar-md{left:unset;right:unset;right:16px}[dir=rtl].sc-ion-searchbar-md .searchbar-search-icon.sc-ion-searchbar-md{left:unset;right:unset;right:16px}@supports selector(:dir(rtl)){.searchbar-search-icon.sc-ion-searchbar-md:dir(rtl){left:unset;right:unset;right:16px}}}.searchbar-cancel-button.sc-ion-searchbar-md{top:0;background-color:transparent;font-size:1.5em}@supports (inset-inline-start: 0){.searchbar-cancel-button.sc-ion-searchbar-md{inset-inline-start:9px}}@supports not (inset-inline-start: 0){.searchbar-cancel-button.sc-ion-searchbar-md{left:9px}[dir=rtl].sc-ion-searchbar-md-h .searchbar-cancel-button.sc-ion-searchbar-md,[dir=rtl] .sc-ion-searchbar-md-h .searchbar-cancel-button.sc-ion-searchbar-md{left:unset;right:unset;right:9px}[dir=rtl].sc-ion-searchbar-md .searchbar-cancel-button.sc-ion-searchbar-md{left:unset;right:unset;right:9px}@supports selector(:dir(rtl)){.searchbar-cancel-button.sc-ion-searchbar-md:dir(rtl){left:unset;right:unset;right:9px}}}.searchbar-search-icon.sc-ion-searchbar-md,.searchbar-cancel-button.sc-ion-searchbar-md{position:absolute}.searchbar-search-icon.ion-activated.sc-ion-searchbar-md,.searchbar-cancel-button.ion-activated.sc-ion-searchbar-md{background-color:transparent}.searchbar-input.sc-ion-searchbar-md{-webkit-padding-start:3.4375rem;padding-inline-start:3.4375rem;-webkit-padding-end:3.4375rem;padding-inline-end:3.4375rem;padding-top:0.375rem;padding-bottom:0.375rem;background-position:left 8px center;height:auto;font-size:1rem;font-weight:400;line-height:30px}[dir=rtl].sc-ion-searchbar-md-h .searchbar-input.sc-ion-searchbar-md,[dir=rtl] .sc-ion-searchbar-md-h .searchbar-input.sc-ion-searchbar-md{background-position:right 8px center}[dir=rtl].sc-ion-searchbar-md .searchbar-input.sc-ion-searchbar-md{background-position:right 8px center}@supports selector(:dir(rtl)){.searchbar-input.sc-ion-searchbar-md:dir(rtl){background-position:right 8px center}}.searchbar-clear-button.sc-ion-searchbar-md{top:0;padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;position:absolute;height:100%;border:0;background-color:transparent}@supports (inset-inline-start: 0){.searchbar-clear-button.sc-ion-searchbar-md{inset-inline-end:13px}}@supports not (inset-inline-start: 0){.searchbar-clear-button.sc-ion-searchbar-md{right:13px}[dir=rtl].sc-ion-searchbar-md-h .searchbar-clear-button.sc-ion-searchbar-md,[dir=rtl] .sc-ion-searchbar-md-h .searchbar-clear-button.sc-ion-searchbar-md{left:unset;right:unset;left:13px}[dir=rtl].sc-ion-searchbar-md .searchbar-clear-button.sc-ion-searchbar-md{left:unset;right:unset;left:13px}@supports selector(:dir(rtl)){.searchbar-clear-button.sc-ion-searchbar-md:dir(rtl){left:unset;right:unset;left:13px}}}.searchbar-clear-button.ion-activated.sc-ion-searchbar-md{background-color:transparent}.searchbar-clear-icon.sc-ion-searchbar-md{width:1.375rem;height:100%}.searchbar-has-focus.sc-ion-searchbar-md-h .searchbar-search-icon.sc-ion-searchbar-md{display:block}.searchbar-has-focus.sc-ion-searchbar-md-h .searchbar-cancel-button.sc-ion-searchbar-md,.searchbar-should-show-cancel.sc-ion-searchbar-md-h .searchbar-cancel-button.sc-ion-searchbar-md{display:block}.searchbar-has-focus.sc-ion-searchbar-md-h .searchbar-cancel-button.sc-ion-searchbar-md+.searchbar-search-icon.sc-ion-searchbar-md,.searchbar-should-show-cancel.sc-ion-searchbar-md-h .searchbar-cancel-button.sc-ion-searchbar-md+.searchbar-search-icon.sc-ion-searchbar-md{display:none}ion-toolbar.sc-ion-searchbar-md-h,ion-toolbar .sc-ion-searchbar-md-h{-webkit-padding-start:7px;padding-inline-start:7px;-webkit-padding-end:7px;padding-inline-end:7px;padding-top:3px;padding-bottom:3px}\";\nconst IonSearchbarMdStyle0 = searchbarMdCss;\n\nconst Searchbar = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.ionInput = createEvent(this, \"ionInput\", 7);\n        this.ionChange = createEvent(this, \"ionChange\", 7);\n        this.ionCancel = createEvent(this, \"ionCancel\", 7);\n        this.ionClear = createEvent(this, \"ionClear\", 7);\n        this.ionBlur = createEvent(this, \"ionBlur\", 7);\n        this.ionFocus = createEvent(this, \"ionFocus\", 7);\n        this.ionStyle = createEvent(this, \"ionStyle\", 7);\n        this.isCancelVisible = false;\n        this.shouldAlignLeft = true;\n        this.inputId = `ion-searchbar-${searchbarIds++}`;\n        this.inheritedAttributes = {};\n        /**\n         * Clears the input field and triggers the control change.\n         */\n        this.onClearInput = async (shouldFocus) => {\n            this.ionClear.emit();\n            return new Promise((resolve) => {\n                // setTimeout() fixes https://github.com/ionic-team/ionic/issues/7527\n                // wait for 4 frames\n                setTimeout(() => {\n                    const value = this.getValue();\n                    if (value !== '') {\n                        this.value = '';\n                        this.emitInputChange();\n                        /**\n                         * When tapping clear button\n                         * ensure input is focused after\n                         * clearing input so users\n                         * can quickly start typing.\n                         */\n                        if (shouldFocus && !this.focused) {\n                            this.setFocus();\n                            /**\n                             * The setFocus call above will clear focusedValue,\n                             * but ionChange will never have gotten a chance to\n                             * fire. Manually revert focusedValue so onBlur can\n                             * compare against what was in the box before the clear.\n                             */\n                            this.focusedValue = value;\n                        }\n                    }\n                    resolve();\n                }, 16 * 4);\n            });\n        };\n        /**\n         * Clears the input field and tells the input to blur since\n         * the clearInput function doesn't want the input to blur\n         * then calls the custom cancel function if the user passed one in.\n         */\n        this.onCancelSearchbar = async (ev) => {\n            if (ev) {\n                ev.preventDefault();\n                ev.stopPropagation();\n            }\n            this.ionCancel.emit();\n            // get cached values before clearing the input\n            const value = this.getValue();\n            const focused = this.focused;\n            await this.onClearInput();\n            /**\n             * If there used to be something in the box, and we weren't focused\n             * beforehand (meaning no blur fired that would already handle this),\n             * manually fire ionChange.\n             */\n            if (value && !focused) {\n                this.emitValueChange(ev);\n            }\n            if (this.nativeInput) {\n                this.nativeInput.blur();\n            }\n        };\n        /**\n         * Update the Searchbar input value when the input changes\n         */\n        this.onInput = (ev) => {\n            const input = ev.target;\n            if (input) {\n                this.value = input.value;\n            }\n            this.emitInputChange(ev);\n        };\n        this.onChange = (ev) => {\n            this.emitValueChange(ev);\n        };\n        /**\n         * Sets the Searchbar to not focused and checks if it should align left\n         * based on whether there is a value in the searchbar or not.\n         */\n        this.onBlur = (ev) => {\n            this.focused = false;\n            this.ionBlur.emit();\n            this.positionElements();\n            if (this.focusedValue !== this.value) {\n                this.emitValueChange(ev);\n            }\n            this.focusedValue = undefined;\n        };\n        /**\n         * Sets the Searchbar to focused and active on input focus.\n         */\n        this.onFocus = () => {\n            this.focused = true;\n            this.focusedValue = this.value;\n            this.ionFocus.emit();\n            this.positionElements();\n        };\n        this.focused = false;\n        this.noAnimate = true;\n        this.color = undefined;\n        this.animated = false;\n        this.autocapitalize = 'default';\n        this.autocomplete = 'off';\n        this.autocorrect = 'off';\n        this.cancelButtonIcon = config.get('backButtonIcon', arrowBackSharp);\n        this.cancelButtonText = 'Cancel';\n        this.clearIcon = undefined;\n        this.debounce = undefined;\n        this.disabled = false;\n        this.inputmode = undefined;\n        this.enterkeyhint = undefined;\n        this.maxlength = undefined;\n        this.minlength = undefined;\n        this.name = this.inputId;\n        this.placeholder = 'Search';\n        this.searchIcon = undefined;\n        this.showCancelButton = 'never';\n        this.showClearButton = 'always';\n        this.spellcheck = false;\n        this.type = 'search';\n        this.value = '';\n    }\n    /**\n     * lang and dir are globally enumerated attributes.\n     * As a result, creating these as properties\n     * can have unintended side effects. Instead, we\n     * listen for attribute changes and inherit them\n     * to the inner `<input>` element.\n     */\n    onLangChanged(newValue) {\n        this.inheritedAttributes = Object.assign(Object.assign({}, this.inheritedAttributes), { lang: newValue });\n        forceUpdate(this);\n    }\n    onDirChanged(newValue) {\n        this.inheritedAttributes = Object.assign(Object.assign({}, this.inheritedAttributes), { dir: newValue });\n        forceUpdate(this);\n    }\n    debounceChanged() {\n        const { ionInput, debounce, originalIonInput } = this;\n        /**\n         * If debounce is undefined, we have to manually revert the ionInput emitter in case\n         * debounce used to be set to a number. Otherwise, the event would stay debounced.\n         */\n        this.ionInput = debounce === undefined ? originalIonInput !== null && originalIonInput !== void 0 ? originalIonInput : ionInput : debounceEvent(ionInput, debounce);\n    }\n    valueChanged() {\n        const inputEl = this.nativeInput;\n        const value = this.getValue();\n        if (inputEl && inputEl.value !== value) {\n            inputEl.value = value;\n        }\n    }\n    showCancelButtonChanged() {\n        requestAnimationFrame(() => {\n            this.positionElements();\n            forceUpdate(this);\n        });\n    }\n    connectedCallback() {\n        this.emitStyle();\n    }\n    componentWillLoad() {\n        this.inheritedAttributes = Object.assign({}, inheritAttributes(this.el, ['lang', 'dir']));\n    }\n    componentDidLoad() {\n        this.originalIonInput = this.ionInput;\n        this.positionElements();\n        this.debounceChanged();\n        setTimeout(() => {\n            this.noAnimate = false;\n        }, 300);\n    }\n    emitStyle() {\n        this.ionStyle.emit({\n            searchbar: true,\n        });\n    }\n    /**\n     * Sets focus on the native `input` in `ion-searchbar`. Use this method instead of the global\n     * `input.focus()`.\n     *\n     * Developers who wish to focus an input when a page enters\n     * should call `setFocus()` in the `ionViewDidEnter()` lifecycle method.\n     *\n     * Developers who wish to focus an input when an overlay is presented\n     * should call `setFocus` after `didPresent` has resolved.\n     *\n     * See [managing focus](/docs/developing/managing-focus) for more information.\n     */\n    async setFocus() {\n        if (this.nativeInput) {\n            this.nativeInput.focus();\n        }\n    }\n    /**\n     * Returns the native `<input>` element used under the hood.\n     */\n    async getInputElement() {\n        /**\n         * If this gets called in certain early lifecycle hooks (ex: Vue onMounted),\n         * nativeInput won't be defined yet with the custom elements build, so wait for it to load in.\n         */\n        if (!this.nativeInput) {\n            await new Promise((resolve) => componentOnReady(this.el, resolve));\n        }\n        return Promise.resolve(this.nativeInput);\n    }\n    /**\n     * Emits an `ionChange` event.\n     *\n     * This API should be called for user committed changes.\n     * This API should not be used for external value changes.\n     */\n    emitValueChange(event) {\n        const { value } = this;\n        // Checks for both null and undefined values\n        const newValue = value == null ? value : value.toString();\n        // Emitting a value change should update the internal state for tracking the focused value\n        this.focusedValue = newValue;\n        this.ionChange.emit({ value: newValue, event });\n    }\n    /**\n     * Emits an `ionInput` event.\n     */\n    emitInputChange(event) {\n        const { value } = this;\n        this.ionInput.emit({ value, event });\n    }\n    /**\n     * Positions the input search icon, placeholder, and the cancel button\n     * based on the input value and if it is focused. (ios only)\n     */\n    positionElements() {\n        const value = this.getValue();\n        const prevAlignLeft = this.shouldAlignLeft;\n        const mode = getIonMode(this);\n        const shouldAlignLeft = !this.animated || value.trim() !== '' || !!this.focused;\n        this.shouldAlignLeft = shouldAlignLeft;\n        if (mode !== 'ios') {\n            return;\n        }\n        if (prevAlignLeft !== shouldAlignLeft) {\n            this.positionPlaceholder();\n        }\n        if (this.animated) {\n            this.positionCancelButton();\n        }\n    }\n    /**\n     * Positions the input placeholder\n     */\n    positionPlaceholder() {\n        const inputEl = this.nativeInput;\n        if (!inputEl) {\n            return;\n        }\n        const rtl = isRTL(this.el);\n        const iconEl = (this.el.shadowRoot || this.el).querySelector('.searchbar-search-icon');\n        if (this.shouldAlignLeft) {\n            inputEl.removeAttribute('style');\n            iconEl.removeAttribute('style');\n        }\n        else {\n            // Create a dummy span to get the placeholder width\n            const doc = document;\n            const tempSpan = doc.createElement('span');\n            tempSpan.innerText = this.placeholder || '';\n            doc.body.appendChild(tempSpan);\n            // Get the width of the span then remove it\n            raf(() => {\n                const textWidth = tempSpan.offsetWidth;\n                tempSpan.remove();\n                // Calculate the input padding\n                const inputLeft = 'calc(50% - ' + textWidth / 2 + 'px)';\n                // Calculate the icon margin\n                /**\n                 * We take the icon width to account\n                 * for any text scales applied to the icon\n                 * such as Dynamic Type on iOS as well as 8px\n                 * of padding.\n                 */\n                const iconLeft = 'calc(50% - ' + (textWidth / 2 + iconEl.clientWidth + 8) + 'px)';\n                // Set the input padding start and icon margin start\n                if (rtl) {\n                    inputEl.style.paddingRight = inputLeft;\n                    iconEl.style.marginRight = iconLeft;\n                }\n                else {\n                    inputEl.style.paddingLeft = inputLeft;\n                    iconEl.style.marginLeft = iconLeft;\n                }\n            });\n        }\n    }\n    /**\n     * Show the iOS Cancel button on focus, hide it offscreen otherwise\n     */\n    positionCancelButton() {\n        const rtl = isRTL(this.el);\n        const cancelButton = (this.el.shadowRoot || this.el).querySelector('.searchbar-cancel-button');\n        const shouldShowCancel = this.shouldShowCancelButton();\n        if (cancelButton !== null && shouldShowCancel !== this.isCancelVisible) {\n            const cancelStyle = cancelButton.style;\n            this.isCancelVisible = shouldShowCancel;\n            if (shouldShowCancel) {\n                if (rtl) {\n                    cancelStyle.marginLeft = '0';\n                }\n                else {\n                    cancelStyle.marginRight = '0';\n                }\n            }\n            else {\n                const offset = cancelButton.offsetWidth;\n                if (offset > 0) {\n                    if (rtl) {\n                        cancelStyle.marginLeft = -offset + 'px';\n                    }\n                    else {\n                        cancelStyle.marginRight = -offset + 'px';\n                    }\n                }\n            }\n        }\n    }\n    getValue() {\n        return this.value || '';\n    }\n    hasValue() {\n        return this.getValue() !== '';\n    }\n    /**\n     * Determines whether or not the cancel button should be visible onscreen.\n     * Cancel button should be shown if one of two conditions applies:\n     * 1. `showCancelButton` is set to `always`.\n     * 2. `showCancelButton` is set to `focus`, and the searchbar has been focused.\n     */\n    shouldShowCancelButton() {\n        if (this.showCancelButton === 'never' || (this.showCancelButton === 'focus' && !this.focused)) {\n            return false;\n        }\n        return true;\n    }\n    /**\n     * Determines whether or not the clear button should be visible onscreen.\n     * Clear button should be shown if one of two conditions applies:\n     * 1. `showClearButton` is set to `always`.\n     * 2. `showClearButton` is set to `focus`, and the searchbar has been focused.\n     */\n    shouldShowClearButton() {\n        if (this.showClearButton === 'never' || (this.showClearButton === 'focus' && !this.focused)) {\n            return false;\n        }\n        return true;\n    }\n    render() {\n        const { cancelButtonText, autocapitalize } = this;\n        const animated = this.animated && config.getBoolean('animated', true);\n        const mode = getIonMode(this);\n        const clearIcon = this.clearIcon || (mode === 'ios' ? closeCircle : closeSharp);\n        const searchIcon = this.searchIcon || (mode === 'ios' ? searchOutline : searchSharp);\n        const shouldShowCancelButton = this.shouldShowCancelButton();\n        const cancelButton = this.showCancelButton !== 'never' && (h(\"button\", { key: '9c7b4d2e86d9bcd12e57c9a96723d3da598a3773', \"aria-label\": cancelButtonText, \"aria-hidden\": shouldShowCancelButton ? undefined : 'true', type: \"button\", tabIndex: mode === 'ios' && !shouldShowCancelButton ? -1 : undefined, onMouseDown: this.onCancelSearchbar, onTouchStart: this.onCancelSearchbar, class: \"searchbar-cancel-button\" }, h(\"div\", { key: '1c25268a776134cccd29eb752898cb8ac0eed30f', \"aria-hidden\": \"true\" }, mode === 'md' ? (h(\"ion-icon\", { \"aria-hidden\": \"true\", mode: mode, icon: this.cancelButtonIcon, lazy: false })) : (cancelButtonText))));\n        return (h(Host, { key: 'feef9fc7e405656e134a76dc037aaaa1a4ce36b4', role: \"search\", \"aria-disabled\": this.disabled ? 'true' : null, class: createColorClasses(this.color, {\n                [mode]: true,\n                'searchbar-animated': animated,\n                'searchbar-disabled': this.disabled,\n                'searchbar-no-animate': animated && this.noAnimate,\n                'searchbar-has-value': this.hasValue(),\n                'searchbar-left-aligned': this.shouldAlignLeft,\n                'searchbar-has-focus': this.focused,\n                'searchbar-should-show-clear': this.shouldShowClearButton(),\n                'searchbar-should-show-cancel': this.shouldShowCancelButton(),\n            }) }, h(\"div\", { key: '92e3925dc0de468e5665705902153837105dfa57', class: \"searchbar-input-container\" }, h(\"input\", Object.assign({ key: 'fb74faf81b347a62338ccdac981525df1c52b322', \"aria-label\": \"search text\", disabled: this.disabled, ref: (el) => (this.nativeInput = el), class: \"searchbar-input\", inputMode: this.inputmode, enterKeyHint: this.enterkeyhint, name: this.name, onInput: this.onInput, onChange: this.onChange, onBlur: this.onBlur, onFocus: this.onFocus, minLength: this.minlength, maxLength: this.maxlength, placeholder: this.placeholder, type: this.type, value: this.getValue(), autoCapitalize: autocapitalize === 'default' ? undefined : autocapitalize, autoComplete: this.autocomplete, autoCorrect: this.autocorrect, spellcheck: this.spellcheck }, this.inheritedAttributes)), mode === 'md' && cancelButton, h(\"ion-icon\", { key: 'd58c3636dac1d2e4135989f4c07dc95c51492e60', \"aria-hidden\": \"true\", mode: mode, icon: searchIcon, lazy: false, class: \"searchbar-search-icon\" }), h(\"button\", { key: '1cece7c63ca5ca4b8799e15ee6d2bac100ef0d5e', \"aria-label\": \"reset\", type: \"button\", \"no-blur\": true, class: \"searchbar-clear-button\", onPointerDown: (ev) => {\n                /**\n                 * This prevents mobile browsers from\n                 * blurring the input when the clear\n                 * button is activated.\n                 */\n                ev.preventDefault();\n            }, onClick: () => this.onClearInput(true) }, h(\"ion-icon\", { key: 'fe3c2b9cac29002f69e95a89b554c7504e2df050', \"aria-hidden\": \"true\", mode: mode, icon: clearIcon, lazy: false, class: \"searchbar-clear-icon\" }))), mode === 'ios' && cancelButton));\n    }\n    get el() { return getElement(this); }\n    static get watchers() { return {\n        \"lang\": [\"onLangChanged\"],\n        \"dir\": [\"onDirChanged\"],\n        \"debounce\": [\"debounceChanged\"],\n        \"value\": [\"valueChanged\"],\n        \"showCancelButton\": [\"showCancelButtonChanged\"]\n    }; }\n};\nlet searchbarIds = 0;\nSearchbar.style = {\n    ios: IonSearchbarIosStyle0,\n    md: IonSearchbarMdStyle0\n};\n\nexport { Searchbar as ion_searchbar };\n"], "names": ["r", "registerInstance", "d", "createEvent", "i", "forceUpdate", "h", "H", "Host", "f", "getElement", "j", "debounceEvent", "k", "inheritAttributes", "c", "componentOnReady", "raf", "isRTL", "createColorClasses", "a", "arrowBackSharp", "b", "closeCircle", "closeSharp", "s", "searchOutline", "e", "searchSharp", "config", "getIonMode", "searchbarIosCss", "IonSearchbarIosStyle0", "searchbarMdCss", "IonSearchbarMdStyle0", "Searchbar", "constructor", "hostRef", "_this", "ionInput", "ionChange", "ionCancel", "ionClear", "ionBlur", "ionFocus", "ionStyle", "isCancelVisible", "shouldAlignLeft", "inputId", "searchbarIds", "inheritedAttributes", "onClearInput", "_ref", "_asyncToGenerator", "shouldFocus", "emit", "Promise", "resolve", "setTimeout", "value", "getValue", "emitInputChange", "focused", "setFocus", "focusedValue", "_x", "apply", "arguments", "onCancelSearchbar", "_ref2", "ev", "preventDefault", "stopPropagation", "emitValueChange", "nativeInput", "blur", "_x2", "onInput", "input", "target", "onChange", "onBlur", "positionElements", "undefined", "onFocus", "noAnimate", "color", "animated", "autocapitalize", "autocomplete", "autocorrect", "cancelButtonIcon", "get", "cancelButtonText", "clearIcon", "debounce", "disabled", "inputmode", "enterkeyhint", "maxlength", "minlength", "name", "placeholder", "searchIcon", "showCancelButton", "showClearButton", "spellcheck", "type", "onLangChanged", "newValue", "Object", "assign", "lang", "onDirChanged", "dir", "debounce<PERSON><PERSON>ed", "originalIonInput", "valueChanged", "inputEl", "showCancelButtonChanged", "requestAnimationFrame", "connectedCallback", "emitStyle", "componentWillLoad", "el", "componentDidLoad", "searchbar", "_this2", "focus", "getInputElement", "_this3", "event", "toString", "prevAlignLeft", "mode", "trim", "positionPlaceholder", "positionCancelButton", "rtl", "iconEl", "shadowRoot", "querySelector", "removeAttribute", "doc", "document", "tempSpan", "createElement", "innerText", "body", "append<PERSON><PERSON><PERSON>", "textWidth", "offsetWidth", "remove", "inputLeft", "iconLeft", "clientWidth", "style", "paddingRight", "marginRight", "paddingLeft", "marginLeft", "cancelButton", "shouldShowCancel", "shouldShowCancelButton", "cancelStyle", "offset", "hasValue", "shouldShowClearButton", "render", "getBoolean", "key", "tabIndex", "onMouseDown", "onTouchStart", "class", "icon", "lazy", "role", "ref", "inputMode", "enterKeyHint", "<PERSON><PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON>", "autoCapitalize", "autoComplete", "autoCorrect", "onPointerDown", "onClick", "watchers", "ios", "md", "ion_searchbar"], "sourceRoot": "webpack:///", "x_google_ignoreList": [0]}