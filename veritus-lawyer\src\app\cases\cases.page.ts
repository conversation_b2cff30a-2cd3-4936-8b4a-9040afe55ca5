import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';

interface Case {
  id: number;
  clientName: string;
  status: 'ongoing' | 'closed' | 'cancelled';
  avatar: string;
}

@Component({
  selector: 'app-cases',
  templateUrl: './cases.page.html',
  styleUrls: ['./cases.page.scss'],
  standalone: false,
})
export class CasesPage implements OnInit {
  selectedTab = 'ongoing';

  cases = [
    { id: 1, clientName: 'Kobe Bryant', status: 'closed', avatar: 'assets/avatars/kobe.png' },
    { id: 2, clientName: 'Lebro<PERSON> James', status: 'closed', avatar: 'assets/avatars/lebron.png' },
    { id: 3, clientName: '<PERSON>', status: 'ongoing', avatar: 'assets/avatars/client1.jpg' }
  ];

  filteredCases: Case[] = [];

  constructor(private router: Router) { }

  ngOnInit() {
    this.filterCases();
  }

  selectTab(tab: string) {
    this.selectedTab = tab;
    this.filterCases();
  }

  filterCases() {
    this.filteredCases = this.cases.filter(c => c.status === this.selectedTab) as Case[];
  }

  onTrackCaseProgress(caseItem: Case) {
    // Navigate to case progress tracker with the case ID
    this.router.navigate(['/track-case', caseItem.id]);
  }
}
