# 🏛️ Veritus Legal Platform - Capstone Project

A comprehensive legal platform ecosystem built with modern web technologies, featuring separate applications for lawyers and secretaries with integrated Firebase backend services.

## 📁 Project Structure

```
Veritus/
├── veritus-lawyer/          # Ionic Angular Mobile App for Lawyers & Clients
├── veritus-secretary/       # Angular Web Portal for Legal Secretaries
├── veritus-workspace.code-workspace  # VSCode Multi-root Workspace
└── README.md               # This file
```

## 🚀 Applications Overview

### 📱 Veritus Lawyer (Mobile App)
**Technology Stack:** Ionic + Angular + Capacitor + Firebase
- **Target Users:** Lawyers and Clients
- **Platform:** iOS, Android, Web
- **Features:**
  - Role-based authentication (Lawyer/Client/Secretary)
  - Lawyer dashboard with case management
  - Client portal with lawyer search and document generation
  - Encrypted file storage with AES-256
  - Real-time notifications
  - Appointment scheduling
  - Case tracking and progress timelines

### 💼 Veritus Secretary (Web Portal)
**Technology Stack:** Angular + Material Design + Firebase
- **Target Users:** Legal Secretaries/Assistants
- **Platform:** Web Browser
- **Features:**
  - Multi-lawyer management system
  - Calendar and appointment scheduling
  - File management with categories
  - Financial tracking and transaction entry
  - Client management interface
  - Activity audit logging
  - Data visualization dashboards

## 🛠️ Development Setup

### Prerequisites
- Node.js 18+
- npm or yarn
- Ionic CLI: `npm install -g @ionic/cli`
- Angular CLI: `npm install -g @angular/cli`
- Firebase CLI: `npm install -g firebase-tools`

### Quick Start

1. **Clone the repository:**
   ```bash
   git clone https://github.com/romee19/Veritus.git
   cd Veritus
   ```

2. **Setup Lawyer App:**
   ```bash
   cd veritus-lawyer
   npm install
   ionic serve
   ```

3. **Setup Secretary Portal:**
   ```bash
   cd veritus-secretary
   npm install
   ng serve
   ```

4. **Open Multi-root Workspace:**
   - Open `veritus-workspace.code-workspace` in VSCode

## 🔧 Build & Deployment

### Lawyer App (Mobile)
```bash
cd veritus-lawyer
ionic build
npx cap sync
npx cap run android  # or ios
```

### Secretary Portal (Web)
```bash
cd veritus-secretary
ng build --prod
```

## 🔐 Security Features

- **AES-256 Encryption** for file storage
- **Role-based Access Control** (RBAC)
- **Firebase Authentication** with custom claims
- **Secure API endpoints** with authentication middleware
- **Encrypted document transmission**

## 📊 Architecture

- **Frontend:** Ionic Angular (Mobile) + Angular (Web)
- **Backend:** Firebase (Firestore, Storage, Functions, Auth)
- **Mobile:** Capacitor for native functionality
- **State Management:** Angular Services + RxJS
- **UI Framework:** Ionic Components + Angular Material

## 🤝 Contributing

This is a capstone project. For development guidelines, see individual project READMEs:
- [Lawyer App Documentation](./veritus-lawyer/README.md)
- [Secretary Portal Documentation](./veritus-secretary/README.md)

## 📄 License

This project is part of an academic capstone project.

---

**Developed by:** Jerome  
**Project:** Legal Platform Ecosystem  
**Year:** 2025
