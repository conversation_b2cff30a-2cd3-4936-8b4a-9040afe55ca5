"use strict";(self.webpackChunkveritus_secretary=self.webpackChunkveritus_secretary||[]).push([[3300],{3300:(C,f,a)=>{a.r(f),a.d(f,{SecretaryProfilePageModule:()=>F});var p=a(177),l=a(9417),c=a(2276),d=a(8498),g=a(467),t=a(4438),u=a(8287);function m(n,s){if(1&n&&t.nrm(0,"img",28),2&n){const e=t.XpG(2);t.Y8G("src",e.secretary.avatar,t.B4B)}}function v(n,s){1&n&&t.nrm(0,"ion-icon",29)}function b(n,s){if(1&n&&(t.j41(0,"div",21)(1,"div",22)(2,"div",23),t.DNE(3,m,1,1,"img",24)(4,v,1,0,"ion-icon",25),t.k0s(),t.j41(5,"h2",26),t.<PERSON><PERSON>(6),t.k0s(),t.j41(7,"p",27),t.EFF(8),t.k0s()()()),2&n){const e=t.XpG();t.R7$(3),t.Y8G("ngIf",e.secretary.avatar),t.R7$(),t.Y8G("ngIf",!e.secretary.avatar),t.R7$(2),t.SpI(" ",e.secretary.name," "),t.R7$(2),t.JRh(e.secretary.email)}}function _(n,s){if(1&n&&(t.j41(0,"div",30)(1,"div",31)(2,"div",32),t.EFF(3),t.k0s(),t.j41(4,"div",33),t.EFF(5,"Linked Lawyers"),t.k0s()(),t.j41(6,"div",31)(7,"div",32),t.EFF(8),t.k0s(),t.j41(9,"div",33),t.EFF(10,"Permissions"),t.k0s()(),t.j41(11,"div",31)(12,"div",32),t.EFF(13," Active "),t.k0s(),t.j41(14,"div",33),t.EFF(15,"Status"),t.k0s()()()),2&n){const e=t.XpG();t.R7$(3),t.SpI(" ",e.getLinkedLawyersCount()," "),t.R7$(5),t.SpI(" ",e.getPermissionCount()," ")}}function x(n,s){if(1&n){const e=t.RV6();t.j41(0,"div",37)(1,"div",38)(2,"label",39),t.EFF(3,"Full Name"),t.k0s(),t.j41(4,"p",40),t.EFF(5),t.k0s()(),t.j41(6,"div",38)(7,"label",39),t.EFF(8,"Email"),t.k0s(),t.j41(9,"p",40),t.EFF(10),t.k0s()(),t.j41(11,"div",38)(12,"label",39),t.EFF(13,"Phone"),t.k0s(),t.j41(14,"p",40),t.EFF(15),t.k0s()(),t.j41(16,"div",38)(17,"label",39),t.EFF(18,"Role"),t.k0s(),t.j41(19,"p",40),t.EFF(20,"Secretary"),t.k0s()(),t.j41(21,"button",41),t.bIt("click",function(){t.eBV(e);const i=t.XpG(2);return t.Njj(i.onEditProfile())}),t.nrm(22,"ion-icon",42),t.EFF(23," Edit Profile "),t.k0s()()}if(2&n){const e=t.XpG(2);t.R7$(5),t.JRh(e.secretary.name),t.R7$(5),t.JRh(e.secretary.email),t.R7$(5),t.JRh(e.secretary.phone||"Not provided")}}function h(n,s){if(1&n){const e=t.RV6();t.j41(0,"div",43)(1,"div",44)(2,"ion-label",45),t.EFF(3,"Full Name"),t.k0s(),t.j41(4,"ion-input",46),t.mxI("ngModelChange",function(i){t.eBV(e);const o=t.XpG(2);return t.DH7(o.editForm.name,i)||(o.editForm.name=i),t.Njj(i)}),t.k0s()(),t.j41(5,"div",44)(6,"ion-label",45),t.EFF(7,"Phone"),t.k0s(),t.j41(8,"ion-input",47),t.mxI("ngModelChange",function(i){t.eBV(e);const o=t.XpG(2);return t.DH7(o.editForm.phone,i)||(o.editForm.phone=i),t.Njj(i)}),t.k0s()(),t.j41(9,"div",44)(10,"ion-label",45),t.EFF(11,"Avatar URL"),t.k0s(),t.j41(12,"ion-input",48),t.mxI("ngModelChange",function(i){t.eBV(e);const o=t.XpG(2);return t.DH7(o.editForm.avatar,i)||(o.editForm.avatar=i),t.Njj(i)}),t.k0s()(),t.j41(13,"div",49)(14,"button",50),t.bIt("click",function(){t.eBV(e);const i=t.XpG(2);return t.Njj(i.onSaveProfile())}),t.nrm(15,"ion-icon",51),t.EFF(16," Save Changes "),t.k0s(),t.j41(17,"button",52),t.bIt("click",function(){t.eBV(e);const i=t.XpG(2);return t.Njj(i.onCancelEdit())}),t.nrm(18,"ion-icon",53),t.EFF(19," Cancel "),t.k0s()()()}if(2&n){const e=t.XpG(2);t.R7$(4),t.R50("ngModel",e.editForm.name),t.R7$(4),t.R50("ngModel",e.editForm.phone),t.R7$(4),t.R50("ngModel",e.editForm.avatar)}}function y(n,s){if(1&n&&(t.j41(0,"div",34)(1,"h3",8),t.EFF(2," Profile Information "),t.k0s(),t.DNE(3,x,24,3,"div",35)(4,h,20,3,"div",36),t.k0s()),2&n){const e=t.XpG();t.R7$(3),t.Y8G("ngIf",!e.isEditing),t.R7$(),t.Y8G("ngIf",e.isEditing)}}const P=[{path:"",component:(()=>{class n{constructor(e,r){this.router=e,this.firebaseService=r,this.secretary=null,this.isEditing=!1,this.editForm={name:"",phone:"",avatar:""}}ngOnInit(){this.loadSecretaryProfile()}loadSecretaryProfile(){var e=this;return(0,g.A)(function*(){const r=e.firebaseService.getCurrentUser();r&&(e.secretary=yield e.firebaseService.getSecretaryProfile(r.uid),e.secretary&&(e.editForm={name:e.secretary.name,phone:e.secretary.phone||"",avatar:e.secretary.avatar||""}))})()}onEditProfile(){this.isEditing=!0}onCancelEdit(){this.isEditing=!1,this.secretary&&(this.editForm={name:this.secretary.name,phone:this.secretary.phone||"",avatar:this.secretary.avatar||""})}onSaveProfile(){var e=this;return(0,g.A)(function*(){if(e.secretary)try{yield e.firebaseService.updateSecretaryProfile(e.secretary.uid,{name:e.editForm.name,phone:e.editForm.phone,avatar:e.editForm.avatar}),e.secretary={...e.secretary,name:e.editForm.name,phone:e.editForm.phone,avatar:e.editForm.avatar},e.isEditing=!1,alert("Profile updated successfully!")}catch(r){console.error("Error updating profile:",r),alert("Error updating profile. Please try again.")}})()}onLogout(){var e=this;return(0,g.A)(function*(){if(confirm("Are you sure you want to logout?"))try{yield e.firebaseService.signOut(),e.router.navigate(["/auth/signin"])}catch(i){console.error("Error signing out:",i),alert("Error signing out. Please try again.")}})()}onManageLinkedLawyers(){this.router.navigate(["/link-lawyer"])}onViewAuditLog(){this.router.navigate(["/audit-log"])}getPermissionCount(){return this.secretary?Object.values(this.secretary.permissions).filter(Boolean).length:0}getLinkedLawyersCount(){return this.secretary?.linkedLawyers?.length||0}static{this.\u0275fac=function(r){return new(r||n)(t.rXU(d.Ix),t.rXU(u.f))}}static{this.\u0275cmp=t.VBU({type:n,selectors:[["app-secretary-profile"]],decls:34,vars:3,consts:[[1,"veritus-toolbar"],[1,"veritus-text-white","veritus-font-semibold"],[1,"profile-content","veritus-gradient-bg"],[1,"profile-container","veritus-safe-area-top"],["class","profile-header",4,"ngIf"],["class","stats-section",4,"ngIf"],["class","info-section",4,"ngIf"],[1,"actions-section"],[1,"section-title","veritus-text-lg","veritus-font-semibold","veritus-text-white"],[1,"action-cards"],[1,"action-card",3,"click"],["name","people",1,"action-icon"],[1,"action-content"],[1,"action-title","veritus-text-base","veritus-font-semibold","veritus-text-white"],[1,"action-description","veritus-text-sm","veritus-text-gray"],["name","chevron-forward",1,"chevron-icon"],["name","list",1,"action-icon"],[1,"logout-section"],[1,"logout-btn",3,"click"],["name","log-out-outline",1,"logout-icon"],[1,"logout-text","veritus-text-base"],[1,"profile-header"],[1,"avatar-section"],[1,"avatar-circle"],["alt","Profile","class","avatar-image",3,"src",4,"ngIf"],["name","person","class","avatar-icon",4,"ngIf"],[1,"profile-name","veritus-text-xl","veritus-font-bold","veritus-text-white"],[1,"profile-email","veritus-text-sm","veritus-text-gray"],["alt","Profile",1,"avatar-image",3,"src"],["name","person",1,"avatar-icon"],[1,"stats-section"],[1,"stat-card"],[1,"stat-number","veritus-text-lg","veritus-font-bold","veritus-text-white"],[1,"stat-label","veritus-text-xs","veritus-text-gray"],[1,"info-section"],["class","info-card",4,"ngIf"],["class","edit-form",4,"ngIf"],[1,"info-card"],[1,"info-item"],[1,"info-label","veritus-text-sm","veritus-text-gray"],[1,"info-value","veritus-text-base","veritus-text-white"],[1,"veritus-btn-secondary","edit-btn",3,"click"],["name","create-outline",1,"btn-icon"],[1,"edit-form"],[1,"form-item"],[1,"form-label","veritus-text-sm","veritus-text-white"],["placeholder","Enter your full name",1,"form-input",3,"ngModelChange","ngModel"],["placeholder","Enter your phone number",1,"form-input",3,"ngModelChange","ngModel"],["placeholder","Enter avatar image URL",1,"form-input",3,"ngModelChange","ngModel"],[1,"form-actions"],[1,"veritus-btn-primary","save-btn",3,"click"],["name","checkmark",1,"btn-icon"],[1,"veritus-btn-secondary","cancel-btn",3,"click"],["name","close",1,"btn-icon"]],template:function(r,i){1&r&&(t.j41(0,"ion-header")(1,"ion-toolbar",0)(2,"ion-title",1),t.EFF(3,"Profile"),t.k0s()()(),t.j41(4,"ion-content",2)(5,"div",3),t.DNE(6,b,9,4,"div",4)(7,_,16,2,"div",5)(8,y,5,2,"div",6),t.j41(9,"div",7)(10,"h3",8),t.EFF(11," Quick Actions "),t.k0s(),t.j41(12,"div",9)(13,"button",10),t.bIt("click",function(){return i.onManageLinkedLawyers()}),t.nrm(14,"ion-icon",11),t.j41(15,"div",12)(16,"h4",13),t.EFF(17," Manage Lawyers "),t.k0s(),t.j41(18,"p",14),t.EFF(19," View and manage linked lawyers "),t.k0s()(),t.nrm(20,"ion-icon",15),t.k0s(),t.j41(21,"button",10),t.bIt("click",function(){return i.onViewAuditLog()}),t.nrm(22,"ion-icon",16),t.j41(23,"div",12)(24,"h4",13),t.EFF(25," Activity Log "),t.k0s(),t.j41(26,"p",14),t.EFF(27," View your activity history "),t.k0s()(),t.nrm(28,"ion-icon",15),t.k0s()()(),t.j41(29,"div",17)(30,"button",18),t.bIt("click",function(){return i.onLogout()}),t.nrm(31,"ion-icon",19),t.j41(32,"span",20),t.EFF(33,"Logout"),t.k0s()()()()()),2&r&&(t.R7$(6),t.Y8G("ngIf",i.secretary),t.R7$(),t.Y8G("ngIf",i.secretary),t.R7$(),t.Y8G("ngIf",i.secretary))},dependencies:[p.bT,l.BC,l.vS,c.W9,c.eU,c.iq,c.$w,c.he,c.BC,c.ai,c.Gw],styles:[".profile-content[_ngcontent-%COMP%]{--background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%)}.profile-container[_ngcontent-%COMP%]{padding:20px;min-height:100vh}.profile-header[_ngcontent-%COMP%]{margin-bottom:30px}.avatar-section[_ngcontent-%COMP%]{text-align:center;padding:20px}.avatar-circle[_ngcontent-%COMP%]{width:80px;height:80px;border-radius:50%;background:#ffffff1a;display:flex;align-items:center;justify-content:center;margin:0 auto 16px;border:2px solid #d4af37}.avatar-image[_ngcontent-%COMP%]{width:100%;height:100%;border-radius:50%;object-fit:cover}.avatar-icon[_ngcontent-%COMP%]{font-size:40px;color:#d4af37}.profile-name[_ngcontent-%COMP%]{margin-bottom:4px}.profile-email[_ngcontent-%COMP%]{opacity:.8}.stats-section[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(3,1fr);gap:15px;margin-bottom:30px}.stat-card[_ngcontent-%COMP%]{background:#ffffff1a;border-radius:12px;padding:16px;text-align:center;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px);border:1px solid rgba(255,255,255,.1)}.stat-number[_ngcontent-%COMP%]{margin-bottom:4px}.stat-label[_ngcontent-%COMP%]{opacity:.8}.info-section[_ngcontent-%COMP%]{margin-bottom:30px}.section-title[_ngcontent-%COMP%]{margin-bottom:15px}.info-card[_ngcontent-%COMP%]{background:#ffffff1a;border-radius:12px;padding:20px;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px);border:1px solid rgba(255,255,255,.1)}.info-item[_ngcontent-%COMP%]{margin-bottom:20px}.info-item[_ngcontent-%COMP%]:last-child{margin-bottom:0}.info-label[_ngcontent-%COMP%]{display:block;margin-bottom:4px;font-weight:500}.info-value[_ngcontent-%COMP%]{margin:0}.edit-btn[_ngcontent-%COMP%]{width:100%;margin-top:20px;display:flex;align-items:center;justify-content:center;gap:8px}.edit-form[_ngcontent-%COMP%]{background:#ffffff1a;border-radius:12px;padding:20px;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px);border:1px solid rgba(255,255,255,.1)}.form-item[_ngcontent-%COMP%]{margin-bottom:20px}.form-label[_ngcontent-%COMP%]{display:block;margin-bottom:8px;font-weight:500}.form-input[_ngcontent-%COMP%]{--background: rgba(255, 255, 255, .1);--color: white;--placeholder-color: rgba(255, 255, 255, .6);--border-radius: 8px;--padding-start: 12px;--padding-end: 12px;border:1px solid rgba(255,255,255,.2);border-radius:8px}.form-actions[_ngcontent-%COMP%]{display:grid;grid-template-columns:1fr 1fr;gap:12px;margin-top:20px}.save-btn[_ngcontent-%COMP%], .cancel-btn[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;gap:8px}.btn-icon[_ngcontent-%COMP%]{font-size:16px}.actions-section[_ngcontent-%COMP%]{margin-bottom:30px}.action-cards[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:12px}.action-card[_ngcontent-%COMP%]{background:#ffffff1a;border:none;border-radius:12px;padding:16px;display:grid;grid-template-columns:auto 1fr auto;gap:15px;align-items:center;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px);border:1px solid rgba(255,255,255,.1);transition:all .3s ease;text-align:left}.action-card[_ngcontent-%COMP%]:hover{background:#ffffff26;transform:translate(5px)}.action-icon[_ngcontent-%COMP%]{font-size:24px;color:#d4af37}.action-content[_ngcontent-%COMP%]{flex:1}.action-title[_ngcontent-%COMP%]{margin-bottom:4px}.action-description[_ngcontent-%COMP%]{margin:0}.chevron-icon[_ngcontent-%COMP%]{color:#d4af37;font-size:18px}.logout-section[_ngcontent-%COMP%]{margin-top:40px;padding-top:20px;border-top:1px solid rgba(255,255,255,.1)}.logout-btn[_ngcontent-%COMP%]{width:100%;background:#f4433633;border:1px solid rgba(244,67,54,.3);border-radius:12px;padding:16px;display:flex;align-items:center;justify-content:center;gap:12px;transition:all .3s ease}.logout-btn[_ngcontent-%COMP%]:hover{background:#f443364d;transform:translateY(-2px)}.logout-icon[_ngcontent-%COMP%]{font-size:20px;color:#f44336}.logout-text[_ngcontent-%COMP%]{color:#f44336;font-weight:500}@media (max-width: 768px){.stats-section[_ngcontent-%COMP%], .form-actions[_ngcontent-%COMP%]{grid-template-columns:1fr}.action-card[_ngcontent-%COMP%]{grid-template-columns:1fr;text-align:center;gap:10px}}"]})}}return n})()}];let k=(()=>{class n{static{this.\u0275fac=function(r){return new(r||n)}}static{this.\u0275mod=t.$C({type:n})}static{this.\u0275inj=t.G2t({imports:[d.iI.forChild(P),d.iI]})}}return n})(),F=(()=>{class n{static{this.\u0275fac=function(r){return new(r||n)}}static{this.\u0275mod=t.$C({type:n})}static{this.\u0275inj=t.G2t({imports:[p.MD,l.YN,c.bv,k]})}}return n})()}}]);