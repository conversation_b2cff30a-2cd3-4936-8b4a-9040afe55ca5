import { Component, OnInit, Output, EventEmitter } from '@angular/core';
import { Router, NavigationEnd } from '@angular/router';
import { filter } from 'rxjs/operators';
import { AdminAuthService, AdminUser } from '../../services/admin-auth.service';

interface Notification {
  id: string;
  message: string;
  timestamp: Date;
  read: boolean;
  type: 'info' | 'warning' | 'success' | 'error';
}

@Component({
  selector: 'app-header',
  templateUrl: './header.component.html',
  styleUrls: ['./header.component.scss']
})
export class HeaderComponent implements OnInit {
  @Output() mobileMenuToggle = new EventEmitter<void>();

  currentAdmin: AdminUser | null = null;
  searchQuery = '';
  showSearch = true;
  showNotifications = false;
  showProfileMenu = false;
  notificationCount = 0;
  notifications: Notification[] = [];
  currentRoute = '';

  private pageTitles: { [key: string]: string } = {
    '/admin/dashboard': 'Dashboard',
    '/admin/lawyer-verification': '',
    '/admin/lawyer-list': 'Lawyer List',
    '/admin/platform-control': 'Platform Control',
    '/admin/templates': 'Templates',
    '/admin/users': 'Users',
    '/admin/settings': 'Settings'
  };

  constructor(
    private adminAuthService: AdminAuthService,
    private router: Router
  ) {
    this.router.events
      .pipe(filter(event => event instanceof NavigationEnd))
      .subscribe((event) => {
        if (event instanceof NavigationEnd) {
          this.currentRoute = event.url;
          this.updateSearchVisibility();
        }
      });
  }

  ngOnInit(): void {
    this.adminAuthService.currentAdmin$.subscribe(admin => {
      this.currentAdmin = admin;
    });

    this.loadNotifications();
    
    // Close dropdowns when clicking outside
    document.addEventListener('click', (event) => {
      const target = event.target as HTMLElement;
      if (!target.closest('.notifications')) {
        this.showNotifications = false;
      }
      if (!target.closest('.admin-profile')) {
        this.showProfileMenu = false;
      }
    });
  }

  getPageTitle(): string {
    return this.pageTitles[this.currentRoute] || 'Admin Panel';
  }

  updateSearchVisibility(): void {
    // Show search on pages where it's useful
    const searchablePages = [
      '/admin/lawyer-verification',
      '/admin/lawyer-list',
      '/admin/users',
      '/admin/templates'
    ];
    this.showSearch = searchablePages.includes(this.currentRoute);
  }

  onSearch(event: any): void {
    const query = event.target.value;
    // Emit search event or handle search logic
    console.log('Search query:', query);
  }

  toggleMobileMenu(): void {
    this.mobileMenuToggle.emit();
  }

  toggleNotifications(): void {
    this.showNotifications = !this.showNotifications;
    this.showProfileMenu = false;
  }

  toggleProfileMenu(): void {
    this.showProfileMenu = !this.showProfileMenu;
    this.showNotifications = false;
  }

  markAllAsRead(): void {
    this.notifications.forEach(notification => {
      notification.read = true;
    });
    this.updateNotificationCount();
  }

  async logout(): Promise<void> {
    try {
      await this.adminAuthService.logout();
      this.router.navigate(['/login']);
    } catch (error) {
      console.error('Logout error:', error);
    }
  }

  private loadNotifications(): void {
    // Mock notifications - replace with actual service call
    this.notifications = [
      {
        id: '1',
        message: 'New lawyer registration pending verification',
        timestamp: new Date(Date.now() - 30 * 60 * 1000), // 30 minutes ago
        read: false,
        type: 'info'
      },
      {
        id: '2',
        message: 'System backup completed successfully',
        timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 hours ago
        read: false,
        type: 'success'
      },
      {
        id: '3',
        message: 'Template update requires approval',
        timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000), // 4 hours ago
        read: true,
        type: 'warning'
      }
    ];
    
    this.updateNotificationCount();
  }

  private updateNotificationCount(): void {
    this.notificationCount = this.notifications.filter(n => !n.read).length;
  }

  getInitials(name: string): string {
    if (!name) return 'A';
    return name
      .split(' ')
      .map(word => word.charAt(0).toUpperCase())
      .slice(0, 2)
      .join('');
  }

  onImageError(event: any): void {
    // Hide the broken image and show placeholder instead
    event.target.style.display = 'none';
    if (this.currentAdmin) {
      this.currentAdmin.avatar = undefined;
    }
  }
}
