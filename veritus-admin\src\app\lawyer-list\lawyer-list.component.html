<div class="lawyer-list-container">
  <div class="list-header">
    <h1>Lawyer Directory</h1>
    <div class="header-actions">
      <div class="search-container">
        <input type="text"
               placeholder="Search lawyers..."
               [(ngModel)]="searchQuery"
               (input)="filterLawyers()"
               class="search-input">
        <svg class="search-icon" width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M21 21L16.514 16.506L21 21ZM19 10.5C19 15.194 15.194 19 10.5 19C5.806 19 2 15.194 2 10.5C2 5.806 5.806 2 10.5 2C15.194 2 19 5.806 19 10.5Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
      </div>
      <select [(ngModel)]="statusFilter" (change)="filterLawyers()" class="filter-select">
        <option value="">All Status</option>
        <option value="verified">Verified</option>
        <option value="pending">Pending</option>
        <option value="suspended">Suspended</option>
      </select>
    </div>
  </div>

  <!-- Loading State -->
  <div class="loading-container" *ngIf="loading">
    <div class="loading-spinner"></div>
    <p>Loading lawyers...</p>
  </div>

  <!-- Error State -->
  <div class="error-container" *ngIf="error && !loading">
    <div class="error-message">
      <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M12 9V13M12 17H12.01M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
      </svg>
      <p>{{ error }}</p>
      <button class="retry-btn" (click)="loadLawyers()">Retry</button>
    </div>
  </div>

  <!-- Empty State -->
  <div class="empty-container" *ngIf="!loading && !error && filteredLawyers.length === 0">
    <div class="empty-message">
      <svg width="48" height="48" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M20 21V19C20 16.7909 18.2091 15 16 15H8C5.79086 15 4 16.7909 4 19V21M16 7C16 9.20914 14.2091 11 12 11C9.79086 11 8 9.20914 8 7C8 4.79086 9.79086 3 12 3C14.2091 3 16 4.79086 16 7Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
      </svg>
      <h3>No lawyers found</h3>
      <p>{{ searchQuery || statusFilter ? 'Try adjusting your search criteria.' : 'No lawyers have registered yet.' }}</p>
    </div>
  </div>

  <!-- Lawyers Grid -->
  <div class="lawyers-grid" *ngIf="!loading && !error && filteredLawyers.length > 0">
    <div class="lawyer-card" *ngFor="let lawyer of filteredLawyers">
      <div class="lawyer-header">
        <div class="lawyer-avatar">
          <div class="avatar-placeholder" *ngIf="!lawyer.avatar">
            {{ getInitials(lawyer.name) }}
          </div>
          <img *ngIf="lawyer.avatar"
               [src]="lawyer.avatar"
               [alt]="lawyer.name"
               (error)="onImageError($event, lawyer)">
        </div>
        <div class="lawyer-info">
          <h3>{{ lawyer.name }}</h3>
          <p class="roll-number">Roll No: {{ lawyer.rollNumber || 'Not provided' }}</p>
          <p class="firm">{{ lawyer.firm || 'Independent Practice' }}</p>
        </div>
        <span class="status-badge" [class]="lawyer.status">{{ lawyer.status | titlecase }}</span>
      </div>
      
      <div class="lawyer-details">
        <p><strong>Email:</strong> {{ lawyer.email || 'Not provided' }}</p>
        <p><strong>Phone:</strong> {{ lawyer.phone || 'Not provided' }}</p>
        <p><strong>Specialization:</strong> {{ lawyer.specialization || 'Not specified' }}</p>
        <p><strong>Experience:</strong> {{ lawyer.yearsOfPractice || 0 }} years</p>
        <p><strong>Joined:</strong> {{ lawyer.createdAt | date:'mediumDate' }}</p>
      </div>
      
      <div class="lawyer-actions">
        <button class="action-btn view" (click)="viewLawyer(lawyer)">View Profile</button>
        <button class="action-btn edit" (click)="editLawyer(lawyer)">Edit</button>
        <button class="action-btn" 
                [class]="lawyer.status === 'verified' ? 'suspend' : 'verify'"
                (click)="toggleLawyerStatus(lawyer)">
          {{ lawyer.status === 'verified' ? 'Suspend' : 'Verify' }}
        </button>
      </div>
    </div>
  </div>
</div>
