<div class="dashboard-container">
  <!-- Left Column: Scheduling -->
  <div class="left-column">
    <div class="scheduling-section">
      <h2>Scheduling</h2>

      <!-- Calendar Widget -->
      <div class="calendar-widget">
        <div class="calendar-header">
          <button class="nav-btn" (click)="previousMonth()">‹</button>
          <h3>{{ currentMonth }}</h3>
          <button class="nav-btn" (click)="nextMonth()">›</button>
        </div>

        <div class="calendar-grid">
          <div class="day-header" *ngFor="let day of dayHeaders">{{ day }}</div>
          <div
            class="calendar-day"
            *ngFor="let day of calendarDays"
            [class.other-month]="day.otherMonth"
            [class.today]="day.isToday"
            [class.has-appointment]="day.hasAppointment"
            (click)="selectDate(day)">
            {{ day.date }}
          </div>
        </div>
      </div>

      <!-- Booking Section -->
      <div class="booking-section">
        <h4>Booking on {{ selectedDateString }}</h4>
        <div class="appointment-list">
          <div class="appointment-item" *ngFor="let appointment of todayAppointments">
            <div class="appointment-info">
              <div class="appointment-type">{{ appointment.type }}</div>
              <div class="appointment-client">{{ appointment.client }}</div>
            </div>
            <div class="appointment-time">{{ appointment.time }}</div>
          </div>
        </div>
        <button class="new-booking-btn">+ New Booking</button>
      </div>

      <!-- Retainer Clients -->
      <div class="retainer-section">
        <div class="section-header">
          <h4>Retainer Clients</h4>
          <button class="add-client-btn">+ Add Client</button>
        </div>
        <div class="client-list">
          <div class="client-item" *ngFor="let client of retainerClients">
            <div class="client-status" [class]="client.status"></div>
            <span>{{ client.name }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Right Column: Files & Finance -->
  <div class="right-column">
    <!-- Files Section -->
    <div class="files-section">
      <div class="section-header">
        <h2>Files</h2>
        <div class="search-box">
          <input type="text" placeholder="Search a document" [(ngModel)]="searchTerm">
          <span class="search-icon">🔍</span>
        </div>
      </div>

      <div class="file-categories">
        <div class="file-category">
          <div class="folder-icon">📁</div>
          <span>Disclosures</span>
        </div>
        <div class="file-category">
          <div class="folder-icon">📁</div>
          <span>Evidence</span>
        </div>
        <div class="file-category">
          <div class="folder-icon">📁</div>
          <span>Receipts</span>
        </div>
        <div class="file-category">
          <div class="folder-icon">📁</div>
          <span>Contracts</span>
        </div>
      </div>

      <div class="recent-documents">
        <h4>Recent documents</h4>
        <div class="document-item" *ngFor="let doc of recentDocuments">
          <div class="doc-icon">📄</div>
          <div class="doc-info">
            <div class="doc-name">{{ doc.name }}</div>
            <div class="doc-date">{{ doc.date }}</div>
          </div>
          <button class="doc-action">›</button>
        </div>
      </div>
    </div>

    <!-- Finance Section -->
    <div class="finance-section">
      <div class="section-header">
        <h2>Finance</h2>
        <button class="new-transaction-btn">+ New Transaction</button>
      </div>

      <div class="chart-container">
        <div class="chart-bars">
          <div class="bar" *ngFor="let bar of chartData" [style.height.%]="bar.height" [style.background]="bar.color"></div>
        </div>
        <div class="chart-labels">
          <span *ngFor="let label of chartLabels">{{ label }}</span>
        </div>
        <div class="chart-period">This year</div>
      </div>

      <div class="recent-transactions">
        <h4>Recent Transactions</h4>
        <div class="transaction-item" *ngFor="let transaction of recentTransactions">
          <div class="transaction-amount">{{ transaction.amount }}</div>
          <div class="transaction-info">
            <div class="transaction-type">{{ transaction.type }}</div>
            <div class="transaction-date">{{ transaction.date }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
